"""
Enhanced ensemble model for stock price prediction
"""
import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, RegressorMixin
import joblib
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedEnsembleModel(BaseEstimator, RegressorMixin):
    """
    Enhanced ensemble model that combines multiple base models with dynamic weighting
    """

    def __init__(self, base_models=None, weighting_strategy='equal'):
        """
        Initialize the ensemble model

        Args:
            base_models (list): List of base models
            weighting_strategy (str): Strategy for weighting models ('equal', 'performance', 'adaptive')
        """
        self.base_models = base_models if base_models is not None else []
        self.weighting_strategy = weighting_strategy
        self.weights = None
        self.model_errors = None
        self.model_names = None
        self.performance_history = []

    def fit(self, X, y):
        """
        Fit the ensemble model

        Args:
            X (np.ndarray): Input features
            y (np.ndarray): Target values

        Returns:
            self: Fitted model
        """
        if len(self.base_models) == 0:
            raise ValueError("No base models provided")

        # Prepare data
        X_2d = self._prepare_data(X)

        # Fit each base model
        for i, model in enumerate(self.base_models):
            logger.info(f"Fitting base model {i+1}/{len(self.base_models)}")
            model.fit(X_2d, y)

        # Initialize model errors
        self.model_errors = np.ones(len(self.base_models))

        # Calculate initial weights
        self._update_weights(X, y)

        return self

    def predict(self, X):
        """
        Make predictions with the ensemble model

        Args:
            X (np.ndarray): Input features

        Returns:
            np.ndarray: Predictions
        """
        if len(self.base_models) == 0:
            raise ValueError("No base models provided")

        if self.weights is None:
            # If weights are not set, use equal weighting
            self.weights = np.ones(len(self.base_models)) / len(self.base_models)

        # Prepare data
        X_2d = self._prepare_data(X)

        # Get predictions from each base model
        predictions = []
        for i, model in enumerate(self.base_models):
            try:
                # Check if the model is a dictionary (which indicates a loading error)
                if isinstance(model, dict):
                    logger.warning(f"Model {i} is a dictionary, not a proper model object")

                    # Try to extract the actual model from the dictionary
                    if 'model' in model:
                        logger.info(f"Extracting model from dictionary for model {i}")
                        actual_model = model['model']

                        # Check if it's a Prophet model
                        if self._is_prophet_model(actual_model):
                            pred = actual_model.predict(X=None, future_periods=1)
                        else:
                            pred = actual_model.predict(X_2d)
                    else:
                        # If we can't extract the model, raise an error to trigger the fallback
                        raise ValueError(f"Model {i} is a dictionary without a 'model' key")
                # Handle Prophet models differently
                elif self._is_prophet_model(model):
                    # Prophet models need special handling
                    # For Prophet, we'll use the model's predict method directly
                    # and just take the last prediction point
                    pred = model.predict(X=None, future_periods=1)
                else:
                    # Standard sklearn models
                    pred = model.predict(X_2d)

                predictions.append(pred)
            except Exception as e:
                logger.error(f"Error with model {i}: {str(e)}")
                # Use a default prediction (average of other models or 0)
                if predictions:
                    # Use average of other models
                    pred = np.mean(predictions, axis=0)
                else:
                    # Use zeros
                    pred = np.zeros_like(X_2d[:, 0])
                predictions.append(pred)

        # Convert to numpy array
        predictions = np.array(predictions)

        # Apply weights and sum
        weighted_pred = np.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            weighted_pred += self.weights[i] * pred

        return weighted_pred

    def _prepare_data(self, X):
        """
        Prepare data for model input

        Args:
            X (np.ndarray): Input data

        Returns:
            np.ndarray: Prepared data
        """
        # Check if input is 3D (for LSTM-like models)
        if len(X.shape) == 3:
            # Reshape to 2D for sklearn models
            return X.reshape(X.shape[0], -1)
        else:
            return X

    def _is_prophet_model(self, model):
        """
        Check if a model is a Prophet model

        Args:
            model: Model to check

        Returns:
            bool: True if model is a Prophet model, False otherwise
        """
        return hasattr(model, '__class__') and model.__class__.__name__ == 'ProphetModel'

    def _update_weights(self, X=None, y=None):
        """
        Update model weights based on the selected strategy

        Args:
            X (np.ndarray): Input features
            y (np.ndarray): Target values
        """
        if self.weighting_strategy == 'equal':
            # Equal weighting
            self.weights = np.ones(len(self.base_models)) / len(self.base_models)

        elif self.weighting_strategy == 'performance' and X is not None and y is not None:
            # Performance-based weighting
            X_2d = self._prepare_data(X)
            errors = []

            for model in self.base_models:
                pred = model.predict(X_2d)
                mse = np.mean((pred - y) ** 2)
                errors.append(mse)

            # Convert errors to weights (inverse of error)
            inv_errors = 1.0 / (np.array(errors) + 1e-10)  # Add small constant to avoid division by zero
            self.weights = inv_errors / np.sum(inv_errors)
            self.model_errors = np.array(errors)

        elif self.weighting_strategy == 'adaptive':
            # Adaptive weighting based on historical performance
            if len(self.performance_history) > 0:
                # Calculate exponentially weighted average of past performance
                alpha = 0.7  # Weight for most recent performance
                recent_errors = np.array([perf['errors'] for perf in self.performance_history[-5:]])
                weighted_errors = np.zeros(len(self.base_models))

                for i, errors in enumerate(reversed(recent_errors)):
                    weight = alpha * (1 - alpha) ** i
                    weighted_errors += weight * errors

                # Convert to weights
                inv_errors = 1.0 / (weighted_errors + 1e-10)
                self.weights = inv_errors / np.sum(inv_errors)
                self.model_errors = weighted_errors
            else:
                # Default to equal weighting if no history
                self.weights = np.ones(len(self.base_models)) / len(self.base_models)

        else:
            # Default to equal weighting
            self.weights = np.ones(len(self.base_models)) / len(self.base_models)

        logger.info(f"Updated weights: {self.weights}")

    def update_performance(self, X, y):
        """
        Update performance history with new data

        Args:
            X (np.ndarray): Input features
            y (np.ndarray): Target values
        """
        X_2d = self._prepare_data(X)
        errors = []

        for model in self.base_models:
            pred = model.predict(X_2d)
            mse = np.mean((pred - y) ** 2)
            errors.append(mse)

        # Add to performance history
        self.performance_history.append({
            'timestamp': datetime.now(),
            'errors': np.array(errors)
        })

        # Keep only the last 10 performance records
        if len(self.performance_history) > 10:
            self.performance_history = self.performance_history[-10:]

        # Update weights
        self._update_weights()

    def get_model_performance(self):
        """
        Get performance metrics for each model

        Returns:
            dict: Dictionary with performance metrics
        """
        if self.model_errors is None or self.weights is None:
            return None

        performance = {
            'errors': self.model_errors.tolist(),
            'weights': self.weights.tolist()
        }

        if self.model_names is not None:
            performance['model_names'] = self.model_names

        return performance

    def set_model_names(self, names):
        """
        Set names for the base models

        Args:
            names (list): List of model names
        """
        if len(names) != len(self.base_models):
            raise ValueError(f"Number of names ({len(names)}) does not match number of models ({len(self.base_models)})")

        self.model_names = names

    def save(self, path, filename):
        """
        Save the ensemble model

        Args:
            path (str): Directory path
            filename (str): Filename

        Returns:
            str: Path to the saved model
        """
        if not os.path.exists(path):
            os.makedirs(path)

        file_path = os.path.join(path, filename)
        joblib.dump(self, file_path)
        logger.info(f"Saved ensemble model to {file_path}")

        return file_path

    @classmethod
    def load(cls, path, filename):
        """
        Load an ensemble model

        Args:
            path (str): Directory path
            filename (str): Filename

        Returns:
            EnhancedEnsembleModel: Loaded model
        """
        file_path = os.path.join(path, filename)

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Model file not found: {file_path}")

        model = joblib.load(file_path)
        logger.info(f"Loaded ensemble model from {file_path}")

        return model

def create_enhanced_ensemble(base_models, model_names=None, weighting_strategy='adaptive'):
    """
    Create an enhanced ensemble model

    Args:
        base_models (list): List of base models
        model_names (list): List of model names
        weighting_strategy (str): Strategy for weighting models

    Returns:
        EnhancedEnsembleModel: Ensemble model
    """
    ensemble = EnhancedEnsembleModel(base_models=base_models, weighting_strategy=weighting_strategy)

    if model_names is not None:
        ensemble.set_model_names(model_names)

    return ensemble
