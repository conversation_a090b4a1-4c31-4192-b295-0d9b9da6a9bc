import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
import joblib
import os
import logging

# Try to import XGBoost, but don't fail if it's not available
try:
    from xgboost import XGBRegressor
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

# Import hybrid models
try:
    from models.hybrid_model import ARIMAMLHybrid, EnsembleModel, create_ensemble_model
    HYBRID_MODELS_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("Successfully imported hybrid models")
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"Hybrid models import error: {str(e)}")
    # Create a fallback implementation to avoid errors
    class ARIMAMLHybrid:
        def __init__(self, ml_model='rf', arima_order=(1,1,1)):
            self.ml_model = ml_model
            self.arima_order = arima_order
            self.model_type = 'arima_ml'
            self.ml_model_type = ml_model

        def train(self, X, y, **kwargs):
            from sklearn.ensemble import RandomForestRegressor
            self.model = RandomForestRegressor(n_estimators=100, random_state=42)

            # Get save parameters
            save_path = kwargs.get('save_path', 'saved_models')
            symbol = kwargs.get('symbol', 'stock')
            horizon = kwargs.get('horizon', None)

            # Reshape data if needed
            if len(X.shape) == 3:
                X_2d = X.reshape(X.shape[0], -1)
            else:
                X_2d = X

            # Train the model
            self.model.fit(X_2d, y)

            # Save the model
            if save_path and symbol and horizon is not None:
                self.save(path=save_path, symbol=symbol, horizon=horizon)

            return self, {}, None

        def predict(self, X):
            if len(X.shape) == 3:
                X_2d = X.reshape(X.shape[0], -1)
            else:
                X_2d = X
            return self.model.predict(X_2d)

        def save(self, path='saved_models', symbol='stock', horizon=None):
            import os
            import joblib

            if not os.path.exists(path):
                os.makedirs(path)

            # Set up prediction horizon for model name
            horizon_str = f"_{horizon}min" if horizon else ""

            # Save model
            joblib.dump(self.model, os.path.join(path, f"{symbol}_{self.model_type}_{self.ml_model_type}{horizon_str}.joblib"))

            # Save parameters
            model_params = {
                'arima_order': self.arima_order,
                'y_train_mean': 0.0,
                'last_y_values': None,
                'ml_model_type': self.ml_model_type
            }
            joblib.dump(model_params, os.path.join(path, f"{symbol}_{self.model_type}_params{horizon_str}.joblib"))

        def load(self, path='saved_models', symbol='stock', horizon=None):
            import os
            import joblib

            # Set up prediction horizon for model name
            horizon_str = f"_{horizon}min" if horizon else ""

            # Load ML model
            ml_model_path = os.path.join(path, f"{symbol}_{self.model_type}_{self.ml_model_type}{horizon_str}.joblib")
            params_path = os.path.join(path, f"{symbol}_{self.model_type}_params{horizon_str}.joblib")

            if not os.path.exists(ml_model_path) or not os.path.exists(params_path):
                raise FileNotFoundError(f"Model files not found at {ml_model_path} or {params_path}")

            # Load ML model
            self.model = joblib.load(ml_model_path)

            # Load model parameters
            model_params = joblib.load(params_path)
            self.arima_order = model_params['arima_order']
            self.ml_model_type = model_params['ml_model_type']

            return self

    def create_ensemble_model(model_types=['rf', 'gb', 'lr']):
        from sklearn.ensemble import RandomForestRegressor
        return RandomForestRegressor(n_estimators=100, random_state=42)

    HYBRID_MODELS_AVAILABLE = True  # Set to True since we have fallbacks

# Import Prophet model
try:
    from models.prophet_model import ProphetModel, prepare_prophet_data
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StockPredictionModel:
    """
    Scikit-learn based model for stock price prediction (fallback for when TensorFlow is not available)
    """

    def __init__(self, sequence_length=60, prediction_horizon=1, model_type='rf'):
        """
        Initialize the model

        Args:
            sequence_length (int): Number of time steps to look back
            prediction_horizon (int): Number of time steps to predict ahead
            model_type (str): Type of model ('rf', 'gb', 'lr', 'svr', 'xgb', 'hybrid', 'ensemble')
        """
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.model_type = model_type
        self.model = None
        self.scaler = MinMaxScaler(feature_range=(0, 1))

        # Check if this is a hybrid model or prophet model
        self.is_hybrid = model_type in ['hybrid', 'ensemble', 'arima_ml']
        self.is_prophet = model_type == 'prophet'

        # Add a property to check model type more reliably
        self._model_category = 'sklearn'  # Default to sklearn

    def _create_model(self):
        """
        Create the appropriate scikit-learn model

        Returns:
            sklearn model: Initialized model
        """
        if self.model_type == 'rf':
            return RandomForestRegressor(n_estimators=100, random_state=42)
        elif self.model_type == 'gb':
            # Use XGBoost instead of sklearn GradientBoosting to avoid pickle issues on macOS
            try:
                from xgboost import XGBRegressor
                return XGBRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    verbosity=0,
                    n_jobs=1  # Avoid threading issues
                )
            except ImportError:
                # Fallback to RandomForest if XGBoost not available
                logger.warning("XGBoost not available, using RandomForest as fallback for GB model")
                return RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
        elif self.model_type == 'lr':
            return LinearRegression()
        elif self.model_type == 'svr':
            # Use a more robust SVR configuration with better parameters
            return SVR(kernel='rbf', C=10, gamma='scale', epsilon=0.1, tol=1e-3, cache_size=1000)
        elif self.model_type == 'prophet' and PROPHET_AVAILABLE:
            # Use Prophet for time series forecasting
            return ProphetModel(prediction_horizon=self.prediction_horizon)
        elif self.model_type == 'xgb' and XGBOOST_AVAILABLE:
            logger.info("Using XGBoost for XGB model (optimized for macOS)")
            from xgboost import XGBRegressor
            return XGBRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                verbosity=0,
                n_jobs=1  # Avoid threading issues on macOS
            )
        elif self.model_type == 'xgb' and not XGBOOST_AVAILABLE:
            logger.warning("XGBoost not available for XGB model, using RandomForest as fallback")
            return RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
        elif self.model_type == 'hybrid':
            if HYBRID_MODELS_AVAILABLE:
                # Create ARIMA-ML hybrid model with Random Forest as the ML component
                logger.info("Creating ARIMAMLHybrid model with RF component")
                return ARIMAMLHybrid(ml_model='rf', arima_order=(1,1,1))
            else:
                logger.warning("Hybrid models not available. Falling back to Random Forest.")
                return RandomForestRegressor(n_estimators=100, random_state=42)
        elif self.model_type == 'arima_ml':
            if HYBRID_MODELS_AVAILABLE:
                # Create ARIMA-ML hybrid model with Random Forest as the ML component
                logger.info("Creating ARIMAMLHybrid model with RF component")
                return ARIMAMLHybrid(ml_model='rf', arima_order=(1,1,1))
            else:
                logger.warning("Hybrid models not available. Falling back to Random Forest.")
                return RandomForestRegressor(n_estimators=100, random_state=42)
        elif self.model_type == 'ensemble':
            if HYBRID_MODELS_AVAILABLE:
                # Create ensemble with all available models
                model_types = ['rf', 'gb', 'lr']
                if XGBOOST_AVAILABLE:
                    model_types.append('xgb')
                return create_ensemble_model(model_types=model_types)
            else:
                logging.warning("Ensemble models not available. Falling back to Random Forest.")
                return RandomForestRegressor(n_estimators=100, random_state=42)
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")

    def _prepare_data(self, X):
        """
        Prepare data for scikit-learn models

        Args:
            X (np.ndarray): Input data with shape (samples, sequence_length, features)

        Returns:
            np.ndarray: Reshaped data with shape (samples, sequence_length * features)
        """
        try:
            # Check if X is already 2D
            if len(X.shape) == 2:
                logger.info(f"Input data is already 2D with shape {X.shape}")
                return X

            # Reshape from 3D to 2D for scikit-learn
            if len(X.shape) == 3:
                samples, seq_len, features = X.shape
                logger.info(f"Reshaping 3D input with shape {X.shape} to 2D")
                return X.reshape(samples, seq_len * features)

            # Handle unexpected shapes
            if len(X.shape) == 1:
                logger.warning(f"Input data is 1D with shape {X.shape}, reshaping to 2D")
                return X.reshape(1, -1)

            # Handle other cases
            logger.warning(f"Unexpected input shape: {X.shape}, attempting to flatten")
            return X.reshape(1, -1)

        except Exception as e:
            logger.error(f"Error preparing data: {str(e)}")
            # Last resort: try to convert to numpy array and reshape
            try:
                X_np = np.array(X)
                logger.warning(f"Converted input to numpy array with shape {X_np.shape}")
                if len(X_np.shape) > 1:
                    return X_np.reshape(1, -1)
                else:
                    return X_np.reshape(1, len(X_np))
            except:
                # If all else fails, return the original data
                logger.error("Failed to reshape data, returning original")
                return X

    def train(self, X_train, y_train, epochs=None, batch_size=None, validation_split=None,
              save_path='saved_models', symbol='stock', horizon=None):
        """
        Train the model

        Args:
            X_train (np.ndarray): Training data
            y_train (np.ndarray): Target values
            epochs (int): Not used for scikit-learn models
            batch_size (int): Not used for scikit-learn models
            validation_split (float): Not used for scikit-learn models
            save_path (str): Path to save the model
            symbol (str): Stock symbol
            horizon (int): Prediction horizon (minutes)

        Returns:
            tuple: (model, history) - For scikit-learn models, history is None
        """
        # Create model
        self.model = self._create_model()

        # Train model
        logger.info(f"Training {self.model_type} model for {horizon} minutes horizon")

        # Check the actual model type - some models like ensemble might fall back to RandomForest
        actual_model_type = type(self.model).__name__

        # For hybrid models with the train method, let them handle everything
        if self.is_hybrid and hasattr(self.model, 'train'):
            # Hybrid model has its own train method
            return self.model.train(X_train, y_train, epochs=epochs, batch_size=batch_size,
                                   validation_split=validation_split, save_path=save_path,
                                   symbol=symbol, horizon=horizon)
        elif self.is_prophet:
            # Special handling for Prophet model
            if horizon is not None:
                self.model.prediction_horizon = horizon

            # Prophet requires a DataFrame with 'ds' and 'y' columns
            try:
                # Convert 3D array to DataFrame for Prophet
                import pandas as pd
                from datetime import datetime, timedelta

                # Create a date range for the Prophet model
                end_date = datetime.now()
                start_date = end_date - timedelta(days=len(X_train))
                date_range = pd.date_range(start=start_date, end=end_date, periods=len(X_train))

                # Create the DataFrame with 'ds' and 'y' columns
                prophet_df = pd.DataFrame({
                    'ds': date_range,
                    'y': y_train
                })

                # Add additional regressors if available
                if len(X_train.shape) == 3:
                    # Reshape to 2D for feature extraction
                    X_2d = X_train.reshape(X_train.shape[0], -1)

                    # Add some features as regressors (use first few columns)
                    feature_count = min(4, X_2d.shape[1])
                    for i in range(feature_count):
                        prophet_df[f'feature_{i}'] = X_2d[:, i]

                logger.info(f"Prepared Prophet DataFrame with shape {prophet_df.shape}")

                # Fit the Prophet model with the DataFrame
                self.model.fit(X=None, y=None, df=prophet_df)
                logger.info("Prophet model training completed")
            except Exception as e:
                logger.error(f"Error preparing data for Prophet: {str(e)}")
                raise
        else:
            # Prepare data for standard scikit-learn models
            X_train_2d = self._prepare_data(X_train)

            # Standard scikit-learn models or fallback models
            if self.model_type == 'svr' or 'SVR' in actual_model_type:
                import time

                logger.info("Starting SVR training (this may take some time)...")
                start_time = time.time()

                # Use more robust SVR parameters for better training
                if hasattr(self.model, 'C'):
                    self.model.C = 10.0  # Increase regularization parameter
                if hasattr(self.model, 'gamma'):
                    self.model.gamma = 'scale'  # Use scale for better performance
                if hasattr(self.model, 'epsilon'):
                    self.model.epsilon = 0.1  # Adjust epsilon for better fit

                # For SVR, we'll use a custom training approach with progress indication
                # First, create a smaller subset for initial training
                sample_size = min(1000, len(X_train_2d))
                indices = np.random.choice(len(X_train_2d), sample_size, replace=False)
                X_sample = X_train_2d[indices]
                y_sample = y_train[indices]

                # Train on the sample first
                logger.info(f"Training SVR on sample of {sample_size} examples...")
                self.model.fit(X_sample, y_sample)

                # Then train on the full dataset
                logger.info("Training SVR on full dataset...")
                self.model.fit(X_train_2d, y_train)

                end_time = time.time()
                logger.info(f"SVR training completed in {end_time - start_time:.2f} seconds")
            elif self.model_type == 'lr' or 'LinearRegression' in actual_model_type:
                # Enhance Linear Regression with more robust training
                logger.info("Training enhanced LinearRegression model...")

                # Add regularization by using Ridge or Lasso instead of simple LinearRegression
                from sklearn.linear_model import Ridge
                self.model = Ridge(alpha=1.0, fit_intercept=True, max_iter=1000)

                # Train the model with more iterations
                self.model.fit(X_train_2d, y_train)
                logger.info("Enhanced LinearRegression model training completed")
            else:
                # Standard models (RandomForest, GradientBoosting, XGBoost)
                # Also handles fallback cases when ensemble/hybrid aren't available
                logger.info(f"Training {actual_model_type} model...")
                self.model.fit(X_train_2d, y_train)
                logger.info(f"{actual_model_type} model training completed")

        # Save the model if save_path is provided
        if save_path and symbol and horizon is not None:
            self.save(save_path, symbol, horizon)

        # Return the model and None for history to maintain compatibility with the LSTM API
        return self.model, None

    def predict(self, X):
        """
        Make predictions

        Args:
            X (np.ndarray): Input data

        Returns:
            np.ndarray: Predictions
        """
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        # For hybrid models, delegate to the hybrid model's predict method
        if self.is_hybrid:
            try:
                logger.info(f"Using hybrid model predict method for {self.model_type}")
                # Check if X needs reshaping for the hybrid model
                if hasattr(self.model, '_prepare_data') and callable(getattr(self.model, '_prepare_data')):
                    # Use the hybrid model's own data preparation method
                    return self.model.predict(X)
                else:
                    # Prepare data ourselves before passing to the hybrid model
                    X_2d = self._prepare_data(X)
                    return self.model.predict(X_2d)
            except Exception as e:
                logger.error(f"Error in hybrid model prediction: {str(e)}")
                # Try to fall back to standard prediction
                X_2d = self._prepare_data(X)
                return self.model.predict(X_2d)
        # For Prophet models, use a special approach
        elif self.is_prophet:
            try:
                # Prophet model needs a future dataframe for prediction
                import pandas as pd
                from datetime import datetime, timedelta

                # Create a future dataframe for Prophet
                periods = self.prediction_horizon if hasattr(self, 'prediction_horizon') else 1

                # Create a date range for prediction
                end_date = datetime.now() + timedelta(days=periods)
                start_date = datetime.now()
                future_dates = pd.date_range(start=start_date, end=end_date, periods=periods+1)

                # Create the future dataframe
                future_df = pd.DataFrame({'ds': future_dates})

                # Add features as regressors if they were used during training
                if len(X.shape) == 3:
                    # Reshape to 2D for feature extraction
                    X_2d = X.reshape(X.shape[0], -1)

                    # Add features as regressors (use first few columns)
                    feature_count = min(4, X_2d.shape[1])
                    for i in range(feature_count):
                        if X_2d.shape[0] > 0:  # Make sure we have data
                            # Use the last value for each feature
                            future_df[f'feature_{i}'] = X_2d[-1, i]

                logger.info(f"StockPredictionModel: Calling Prophet predict with future dataframe of shape {future_df.shape}")

                # Make prediction with the Prophet model
                forecast = self.model.predict(df_future=future_df)

                # Get the prediction value (last value in the forecast)
                if isinstance(forecast, np.ndarray) and len(forecast) > 0:
                    result = forecast[-1]
                else:
                    result = forecast

                logger.info(f"StockPredictionModel: Prophet result type: {type(result)}, value: {result}")
                return result
            except Exception as e:
                logger.error(f"Error in Prophet model prediction: {str(e)}")
                # Print the full traceback for debugging
                import traceback
                logger.error(traceback.format_exc())
                # Return a more realistic fallback value
                # Try to get a reasonable price estimate
                try:
                    from app.utils.data_processing import get_original_data
                    # Get the original data for this symbol
                    df = get_original_data(symbol='stock', path='data')
                    if df is not None and 'Close' in df.columns and len(df) > 0:
                        # Use the mean price as a fallback
                        fallback_value = df['Close'].mean()
                        logger.info(f"Using mean price as fallback: {fallback_value}")
                        return np.array([fallback_value])
                except Exception as fallback_error:
                    logger.error(f"Error getting fallback value: {str(fallback_error)}")

                # If all else fails, return a default value
                logger.info("Using default value 100.0 as fallback")
                return np.array([100.0])
        else:
            # Try to handle different input shapes
            try:
                # First try with the original shape
                return self.model.predict(X)
            except Exception as shape_error:
                logger.warning(f"Prediction with original shape failed: {str(shape_error)}. Trying with prepared data.")
                # If that fails, try with the prepared data
                try:
                    X_2d = self._prepare_data(X)
                    return self.model.predict(X_2d)
                except Exception as e:
                    logger.error(f"All prediction attempts failed: {str(e)}")
                    raise

    def save(self, path='saved_models', symbol='stock', horizon=None):
        """
        Save the model

        Args:
            path (str): Path to save the model
            symbol (str): Stock symbol
            horizon (int): Prediction horizon (minutes)
        """
        if self.model is None:
            raise ValueError("Model not trained")

        if not os.path.exists(path):
            os.makedirs(path)

        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Save model (XGBoost for GB models doesn't have pickle issues)
        try:
            joblib.dump(self.model, os.path.join(path, f"{symbol}_{self.model_type}{horizon_str}.joblib"))
            logger.info(f"Successfully saved {self.model_type} model to {symbol}_{self.model_type}{horizon_str}.joblib")
        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")
            raise e

    def load(self, path='saved_models', symbol='stock', horizon=None):
        """
        Load a saved model

        Args:
            path (str): Path where the model is saved
            symbol (str): Stock symbol
            horizon (int): Prediction horizon (minutes)

        Returns:
            sklearn model: Loaded model
        """
        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Try different model file formats
        model_path_joblib = os.path.join(path, f"{symbol}_{self.model_type}{horizon_str}.joblib")
        model_path_pkl = os.path.join(path, f"{symbol}_{self.model_type}{horizon_str}.pkl")

        # Initialize model_path with the default joblib path
        model_path = model_path_joblib

        # Log the model path we're trying to load
        logger.info(f"Attempting to load model from {model_path_joblib} or {model_path_pkl}")

        # Special handling for hybrid models - they use a different naming convention
        if self.model_type == 'hybrid':
            # Try different possible hybrid model paths
            possible_paths = [
                os.path.join(path, f"{symbol}_{self.model_type}_rf{horizon_str}.joblib"),
                os.path.join(path, f"{symbol}_arima_ml_rf{horizon_str}.joblib"),
                os.path.join(path, f"{symbol}_{self.model_type}{horizon_str}.joblib")
            ]

            for hybrid_path in possible_paths:
                if os.path.exists(hybrid_path):
                    logger.info(f"Found hybrid model at {hybrid_path}")
                    model_path = hybrid_path
                    break

        # If the exact model file doesn't exist, try to find a matching model
        if not os.path.exists(model_path):
            logger.warning(f"Model not found at {model_path}, searching for alternatives...")

            # List all potential model files for this symbol and horizon, excluding scalers
            potential_model_files = []
            for file in os.listdir(path):
                if file.endswith('.joblib') and symbol in file and (horizon_str in file or str(horizon) in file) and '_scaler' not in file:
                    potential_model_files.append(file)

            # Special handling for hybrid models - check for arima_ml files as fallback, excluding scalers
            if self.model_type == 'hybrid' and not potential_model_files:
                for file in os.listdir(path):
                    if file.endswith('.joblib') and symbol in file and 'arima_ml' in file and horizon_str in file and '_scaler' not in file:
                        potential_model_files.append(file)
                        logger.info(f"Found arima_ml model as fallback for hybrid: {file}")

            logger.info(f"Found {len(potential_model_files)} potential model files: {potential_model_files}")

            if potential_model_files:
                # Try to find a model file that matches the intended model type
                matching_model_files = [f for f in potential_model_files if f"{symbol}_{self.model_type}" in f]

                if matching_model_files:
                    # Use the first matching model file
                    model_path = os.path.join(path, matching_model_files[0])
                    logger.info(f"Found matching model file: {model_path}")
                else:
                    # If no exact match for the model type, use the first available potential model file
                    model_path = os.path.join(path, potential_model_files[0])
                    logger.warning(f"Using alternative model file: {model_path}")

                    # Update model type to match the file if possible
                    for model_type in ['rf', 'gb', 'lr', 'svr', 'xgb', 'hybrid', 'ensemble']:
                        if f"_{model_type}_" in model_path or f"_{model_type}{horizon_str}" in model_path:
                            self.model_type = model_type
                            logger.info(f"Updated model type to {model_type}")
                            break

                # Load the model (XGBoost for GB models loads without issues)
                logger.info(f"Loading model from {model_path}")
                self.model = joblib.load(model_path)
                logger.info(f"Successfully loaded {self.model_type} model from {model_path}")

                # Check if the loaded object is a model or something else (like a scaler)
                if not hasattr(self.model, 'predict'):
                    error_msg = f"Loaded object from {model_path} does not have a 'predict' method. It might be a scaler or other non-model object."
                    logger.error(error_msg)
                    # Create a simple fallback model
                    from sklearn.ensemble import RandomForestRegressor
                    self.model = RandomForestRegressor(n_estimators=10)
                    logger.warning(f"Created fallback RandomForestRegressor model")
                    # Optionally, raise an error to indicate a potential issue
                    # raise TypeError(error_msg)

            else:
                # Provide a more informative error message
                error_msg = f"No model files found for {symbol} with horizon {horizon}. "
                error_msg += f"Please train a {self.model_type} model for this horizon first. "
                error_msg += f"Available .joblib files in {path}: {[f for f in os.listdir(path) if symbol in f and '.joblib' in f]}"
                logger.error(error_msg)
                raise FileNotFoundError(error_msg)

        # This section is redundant and causes the error when model_path is not properly set
        # The model is already loaded above in the successful code path
        return self.model
