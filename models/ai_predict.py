"""
Function to predict stock prices using external AI services.
This function is called from predict.py when AI services are enabled.
"""
import os
import logging
import configparser
from datetime import datetime
import importlib

# Configure logging
logger = logging.getLogger(__name__)

# Import helper functions
try:
    from app.utils.config_helpers import clean_config_value, get_cleaned_config
except ImportError:
    # Define function in case import fails
    def clean_config_value(value):
        """Helper function to clean config values when imported from app fails"""
        if not isinstance(value, str):
            return value

        # Remove quotes if present
        if value.startswith('"') and value.endswith('"'):
            return value[1:-1]
        elif value.startswith("'") and value.endswith("'"):
            return value[1:-1]

        return value

    def get_cleaned_config(config, section, key, fallback=""):
        """Helper function to get clean config values"""
        value = config.get(section, key, fallback=fallback)
        return clean_config_value(value)

def predict_with_ai_service(df, symbol, horizons, ai_config, live_data=None):
    """
    Get price predictions using an external AI service.

    Args:
        df (pd.DataFrame): Historical price data
        symbol (str): Stock symbol
        horizons (list): List of prediction horizons
        ai_config (dict): AI service configuration
        live_data (pd.DataFrame, optional): Live price data if available

    Returns:
        dict: Predictions for each horizon
    """
    try:
        # Extract provider from config
        provider = ai_config.get("provider", "none")
        # Clean provider value
        provider = clean_config_value(provider)
        if provider == "none":
            logger.warning("No AI provider configured")
            return {h: None for h in horizons}

        # Import AI integration module
        from models.ai_integration import AIServiceIntegration

        # Get config object
        config = ai_config.get("config")

        # Prepare data for the AI model
        data = _prepare_data_for_ai(df, symbol)

        # If live data is available, update the current price
        if live_data is not None and not live_data.empty:
            logger.info(f"Using live data for current price: {live_data['Close'].iloc[-1]}")
            # Update the current price in the data
            data["current_price"] = float(live_data['Close'].iloc[-1])

            # Log the difference between historical and live price
            historical_price = float(df['Close'].iloc[-1])
            live_price = float(live_data['Close'].iloc[-1])
            price_diff_pct = ((live_price - historical_price) / historical_price) * 100
            logger.info(f"Price difference: Historical={historical_price}, Live={live_price}, Diff={price_diff_pct:.2f}%")

        # Call the appropriate service
        if provider == "mock":
            # Use mock predictions (no API key required)
            logger.info("Using mock AI predictions (no API key required)")
            return _mock_predict_with_ai(data, symbol, horizons)

        elif provider in ["openai", "azure", "anthropic", "custom"]:
            api_key = get_cleaned_config(config, provider, "api_key", fallback="")

            if not api_key:
                logger.error(f"API key for {provider} is not configured")
                return {h: None for h in horizons}

            # For Azure and custom, we also need an endpoint
            endpoint = None
            if provider in ["azure", "custom"]:
                endpoint = get_cleaned_config(config, provider, "endpoint", fallback="")
                if not endpoint:
                    logger.error(f"Endpoint for {provider} is not configured")
                    return {h: None for h in horizons}

            # Initialize the service
            service = AIServiceIntegration(
                provider=provider,
                api_key=api_key,
                endpoint=endpoint
            )

            # Get predictions
            return service.predict_with_ai(data, symbol, horizons)

        else:
            logger.error(f"Unsupported AI provider: {provider}")
            return {h: None for h in horizons}

    except Exception as e:
        logger.error(f"Error using AI service for predictions: {str(e)}")
        return {h: None for h in horizons}

def _mock_predict_with_ai(data, symbol, horizons):
    """
    Generate mock AI predictions without requiring an API key.

    This function creates synthetic predictions based on the current price
    and some basic technical analysis principles. It's useful for testing
    the AI integration functionality without needing actual API access.

    Args:
        data (dict): Prepared data for the AI model
        symbol (str): Stock symbol
        horizons (list): List of prediction horizons (in minutes)

    Returns:
        dict: Mock predictions for each horizon
    """
    import random
    import math
    from datetime import datetime, timedelta

    # Get current price
    current_price = data.get("current_price", 100.0)

    # Get historical data if available
    historical_data = data.get("historical_data", [])

    # Calculate a trend factor based on recent price movement if data is available
    trend_factor = 0.0
    if len(historical_data) >= 2:
        # Get the oldest and newest prices in our dataset
        oldest_price = historical_data[0].get("close", current_price)
        newest_price = historical_data[-1].get("close", current_price)

        # Calculate a simple trend (positive or negative)
        if newest_price > oldest_price:
            # Upward trend - very slight upward bias for Egyptian stocks
            trend_factor = 0.002  # Reduced from 0.05
        elif newest_price < oldest_price:
            # Downward trend - very slight downward bias
            trend_factor = -0.001  # Reduced from -0.02

    # Get technical indicators if available
    technical_indicators = data.get("technical_indicators", {})

    # Use technical indicators to adjust predictions if available
    indicator_factor = 0.0
    if technical_indicators:
        # This is a simplified example - in a real system, you would
        # use actual technical analysis principles
        if technical_indicators.get("RSI", 50) > 70:
            # Overbought condition
            indicator_factor -= 0.002  # Reduced from -0.03
        elif technical_indicators.get("RSI", 50) < 30:
            # Oversold condition
            indicator_factor += 0.003  # Reduced from 0.04

        # MACD signal
        if technical_indicators.get("MACD", 0) > 0:
            indicator_factor += 0.001  # Reduced from 0.02
        elif technical_indicators.get("MACD", 0) < 0:
            indicator_factor -= 0.0005  # Reduced from -0.01

    # Generate predictions for each horizon
    predictions = {}
    for horizon in horizons:
        # Convert horizon to hours for more realistic scaling
        horizon_hours = horizon / 60.0

        # Base variation increases with longer horizons, but much more conservatively
        # Square root function ensures that longer horizons have more variation but not excessively
        base_variation = 0.001 * math.sqrt(horizon_hours)  # Reduced from 0.02

        # Random component - smaller random factor
        random_factor = random.uniform(-base_variation, base_variation)

        # Time-based factor - much smaller growth factor for Egyptian stocks
        # Logarithmic scaling to prevent excessive growth for longer horizons
        time_factor = 0.0003 * math.log(1 + horizon_hours)  # Reduced from 0.005 * horizon

        # Calculate the total price change
        price_change = (random_factor + trend_factor + indicator_factor + time_factor)

        # Apply realistic constraints based on horizon
        # For Egyptian stocks, limit changes to more realistic ranges
        max_change_per_hour = 0.005  # 0.5% max change per hour
        max_change = max_change_per_hour * math.sqrt(horizon_hours)  # Square root for diminishing returns

        # Constrain the price change
        if price_change > max_change:
            price_change = max_change
        elif price_change < -max_change:
            price_change = -max_change

        # Apply the change to the current price
        predicted_price = current_price * (1 + price_change)

        # Round to 2 decimal places
        predicted_price = round(predicted_price * 100) / 100

        # Store the prediction
        predictions[horizon] = predicted_price

    # Log the predictions
    logger.info(f"Generated mock predictions for {symbol}: {predictions}")

    return predictions

def _prepare_data_for_ai(historical_data, symbol):
    """
    Prepare data for sending to the AI model.

    Args:
        historical_data (pd.DataFrame): Historical price data
        symbol (str): Stock symbol

    Returns:
        dict: Prepared data for the AI model
    """
    try:
        # Get the most recent data points (last 30 days)
        recent_data = historical_data.tail(30).copy()

        # Format the data for the AI model
        formatted_data = []
        for _, row in recent_data.iterrows():
            formatted_data.append({
                "date": row['Date'].strftime('%Y-%m-%d'),
                "open": float(row['Open']),
                "high": float(row['High']),
                "low": float(row['Low']),
                "close": float(row['Close']),
                "volume": int(row['Volume']) if 'Volume' in row else None
            })

        # Calculate basic statistics
        current_price = recent_data['Close'].iloc[-1]
        avg_price_30d = recent_data['Close'].mean()
        min_price_30d = recent_data['Close'].min()
        max_price_30d = recent_data['Close'].max()
        price_change_30d = (current_price - recent_data['Close'].iloc[0]) / recent_data['Close'].iloc[0] * 100

        # Get technical indicators if available
        technical_indicators = {}
        for col in recent_data.columns:
            if col not in ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Adj Close']:
                technical_indicators[col] = float(recent_data[col].iloc[-1])

        # Prepare the full context
        data = {
            "symbol": symbol,
            "current_date": datetime.now().strftime('%Y-%m-%d'),
            "current_price": float(current_price),
            "statistics": {
                "avg_price_30d": float(avg_price_30d),
                "min_price_30d": float(min_price_30d),
                "max_price_30d": float(max_price_30d),
                "price_change_30d": float(price_change_30d)
            },
            "technical_indicators": technical_indicators,
            "historical_data": formatted_data
        }

        return data

    except Exception as e:
        logger.error(f"Error preparing data for AI: {str(e)}")
        # Return a simplified version as fallback
        return {
            "symbol": symbol,
            "current_price": float(historical_data['Close'].iloc[-1]),
            "historical_data": []
        }

def check_ai_services_health():
    """
    Perform a health check on the AI services functionality.

    This function checks:
    1. If AI services are enabled in the configuration
    2. If the configuration for the selected provider is valid
    3. If a test prediction can be made with the configured service

    Returns:
        dict: A dictionary containing the health check results
    """
    results = {
        "status": "UNKNOWN",
        "enabled": False,
        "provider": "none",
        "config_valid": False,
        "prediction_test": False,
        "errors": [],
        "details": {}
    }

    try:
        # Step 1: Check if AI services are enabled
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                  'config', 'ai_services.conf')

        if not os.path.exists(config_path):
            results["status"] = "FAIL"
            results["errors"].append("Configuration file not found")
            return results

        config = configparser.ConfigParser()
        config.read(config_path)

        # Check if AI services are enabled
        enabled = config.getboolean("ai_services", "enabled", fallback=False)
        results["enabled"] = enabled

        if not enabled:
            results["status"] = "DISABLED"
            results["details"]["message"] = "AI services are disabled in configuration"
            return results

        # Step 2: Get and validate provider configuration
        provider = get_cleaned_config(config, "ai_services", "default_provider", fallback="none")
        results["provider"] = provider

        if provider == "none":
            results["status"] = "FAIL"
            results["errors"].append("No AI provider configured")
            return results

        # Import validation function
        try:
            from app.utils.config_helpers import validate_ai_service_config
        except ImportError:
            # Define a simple validation function if import fails
            def validate_ai_service_config(config, provider):
                provider = clean_config_value(provider)
                if provider == "none":
                    return True, ""

                if provider not in config.sections():
                    return False, f"Provider {provider} not configured"

                # Basic validation for each provider
                if provider == "openai":
                    api_key = get_cleaned_config(config, provider, "api_key", fallback="")
                    if not api_key:
                        return False, "OpenAI API key is not configured"
                elif provider == "azure":
                    api_key = get_cleaned_config(config, provider, "api_key", fallback="")
                    endpoint = get_cleaned_config(config, provider, "endpoint", fallback="")
                    if not api_key:
                        return False, "Azure API key is not configured"
                    if not endpoint:
                        return False, "Azure endpoint is not configured"
                elif provider == "anthropic":
                    api_key = get_cleaned_config(config, provider, "api_key", fallback="")
                    if not api_key:
                        return False, "Anthropic API key is not configured"
                elif provider == "custom":
                    api_key = get_cleaned_config(config, provider, "api_key", fallback="")
                    endpoint = get_cleaned_config(config, provider, "endpoint", fallback="")
                    if not api_key:
                        return False, "Custom API key is not configured"
                    if not endpoint:
                        return False, "Custom endpoint is not configured"
                elif provider == "mock":
                    # Mock provider doesn't require any configuration
                    return True, ""
                else:
                    return False, f"Unknown AI provider: {provider}"

                return True, ""

        # Validate the configuration
        is_valid, error_message = validate_ai_service_config(config, provider)
        results["config_valid"] = is_valid

        if not is_valid:
            results["status"] = "FAIL"
            results["errors"].append(f"Invalid configuration: {error_message}")
            return results

        # Step 3: Check required packages
        required_packages = []
        if provider == "openai":
            required_packages.append("openai")
        elif provider == "anthropic":
            required_packages.append("anthropic")
        elif provider == "mock":
            # Mock provider doesn't require any external packages
            results["details"]["using_mock_provider"] = True
            logger.info("Using mock provider - no external packages required")

        # Skip package check for mock provider
        if provider != "mock" and required_packages:
            missing_packages = []
            for package in required_packages:
                try:
                    importlib.import_module(package)
                    results["details"][f"{package}_installed"] = True
                except ImportError:
                    missing_packages.append(package)
                    results["details"][f"{package}_installed"] = False

            if missing_packages:
                results["status"] = "FAIL"
                results["errors"].append(f"Missing required packages: {', '.join(missing_packages)}")
                results["details"]["missing_packages"] = missing_packages
                return results

        # Step 4: Test prediction
        try:
            # Simple test data
            test_data = {
                "symbol": "TEST",
                "current_price": 100.0,
                "historical_data": [{"date": "2025-05-01", "close": 100.0}]
            }

            # Test horizons - use multiple horizons
            horizons = [4, 30]

            # Handle mock provider separately
            if provider == "mock":
                # Use the mock prediction function directly
                result = _mock_predict_with_ai(test_data, "TEST", horizons)
            else:
                # Import AI integration module
                from models.ai_integration import AIServiceIntegration

                # Handle different providers
                api_key = get_cleaned_config(config, provider, "api_key", fallback="")

                # For Azure and custom, we also need an endpoint
                endpoint = None
                if provider in ["azure", "custom"]:
                    endpoint = get_cleaned_config(config, provider, "endpoint", fallback="")

                # Initialize the service
                service = AIServiceIntegration(
                    provider=provider,
                    api_key=api_key,
                    endpoint=endpoint
                )

                # Get predictions
                result = service.predict_with_ai(test_data, "TEST", horizons)

            # Check result
            if result and 4 in result and result[4] is not None:
                results["prediction_test"] = True
                results["details"]["test_prediction"] = result[4]
                results["status"] = "PASS"
            else:
                results["prediction_test"] = False
                results["details"]["test_result"] = result
                results["status"] = "FAIL"
                results["errors"].append("No valid prediction was returned")

        except Exception as e:
            results["prediction_test"] = False
            results["status"] = "FAIL"
            results["errors"].append(f"Error testing prediction: {str(e)}")

        return results

    except Exception as e:
        results["status"] = "FAIL"
        results["errors"].append(f"Unexpected error: {str(e)}")
        return results
