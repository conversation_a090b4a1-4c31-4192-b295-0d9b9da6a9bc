# LLM Integration for AI Stocks Bot

This directory is where you should place the Capybara-Hermes 2.5 Mistral 7B model file for local LLM integration.

## Model Setup Instructions

1. Download the Capybara-Hermes 2.5 Mistral 7B model from Hugging Face:
   - Model URL: [https://huggingface.co/NousResearch/Capybara-Hermes-2.5-Mistral-7B](https://huggingface.co/NousResearch/Capybara-Hermes-2.5-Mistral-7B)
   - Download the GGUF version (e.g., `capybara-hermes-2.5-mistral-7b.Q4_K_M.gguf`)

2. Place the downloaded `.gguf` file in this directory (`models/llm/`)

3. Rename the file to `capybara-hermes-2.5-mistral-7b.gguf` for automatic detection

4. If you prefer to use a different model or filename, you can specify the custom path in the LLM Insights UI

## System Requirements

- **RAM**: At least 16GB recommended (8GB minimum for 4-bit quantized models)
- **Disk Space**: ~4GB for the quantized model file
- **CPU**: Modern multi-core CPU (at least 4 cores recommended)
- **GPU**: Optional but recommended for faster inference

## Troubleshooting

If you encounter issues with the LLM integration:

1. **Model not found**: Ensure the model file is in the correct location and has the expected name
2. **Out of memory errors**: Try using a more quantized version of the model (e.g., Q4_K_M)
3. **Slow performance**: Adjust the number of threads in the LLM Settings sidebar
4. **Installation issues**: Make sure llama-cpp-python is installed correctly:
   ```
   pip install llama-cpp-python
   ```

## Alternative Models

If you prefer to use a different model, you can use any GGUF format model compatible with llama-cpp-python. Some alternatives include:

- Mistral 7B Instruct
- Llama 2 7B
- Phi-2
- Gemma 7B

Just place the alternative model in this directory and specify the custom path in the LLM Insights UI.

## Privacy Note

All LLM processing happens locally on your machine. No data is sent to external servers, ensuring complete privacy for your financial analysis.
