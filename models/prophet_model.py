"""
Prophet model for time series forecasting in the AI Stocks Bot
"""
import numpy as np
import pandas as pd
from prophet import Prophet
import joblib
import os
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProphetModel:
    """
    Prophet model for time series forecasting
    """
    def __init__(self, prediction_horizon=1):
        """
        Initialize the Prophet model

        Args:
            prediction_horizon (int): Number of time steps to predict ahead
        """
        self.prediction_horizon = prediction_horizon
        self.model = None
        self.last_date = None
        self.frequency = 'min'  # Default to minutes

    def fit(self, X, y=None, df=None):
        """
        Fit the Prophet model

        Args:
            X: Not used for Prophet (included for compatibility)
            y: Not used for Prophet (included for compatibility)
            df (pd.DataFrame): DataFrame with 'ds' (date) and 'y' (target) columns

        Returns:
            self: Fitted model
        """
        if df is None:
            raise ValueError("DataFrame must be provided for Prophet model")

        # Ensure df has the required columns
        if 'ds' not in df.columns or 'y' not in df.columns:
            raise ValueError("DataFrame must have 'ds' and 'y' columns")

        # Store the last date for future predictions
        self.last_date = df['ds'].max()

        # Determine frequency
        if len(df) > 1:
            time_diff = (df['ds'].iloc[1] - df['ds'].iloc[0]).total_seconds()
            if time_diff < 60*60:  # Less than an hour
                self.frequency = 'min'
            elif time_diff < 24*60*60:  # Less than a day
                self.frequency = 'H'
            else:
                self.frequency = 'D'

        logger.info(f"Training Prophet model with {len(df)} data points, frequency: {self.frequency}")

        # Initialize and fit the model
        self.model = Prophet(
            daily_seasonality=True,
            yearly_seasonality=True,
            weekly_seasonality=True,
            changepoint_prior_scale=0.05,
            interval_width=0.95
        )

        # Add additional regressors if available
        if 'Open' in df.columns:
            self.model.add_regressor('Open')
        if 'High' in df.columns:
            self.model.add_regressor('High')
        if 'Low' in df.columns:
            self.model.add_regressor('Low')
        if 'Volume' in df.columns:
            self.model.add_regressor('Volume')

        # Fit the model
        self.model.fit(df)

        return self

    def predict(self, X=None, future_periods=None, df_future=None):
        """
        Make predictions with the Prophet model

        Args:
            X: Not used for Prophet (included for compatibility)
            future_periods (int): Number of periods to predict
            df_future (pd.DataFrame): DataFrame with future dates and regressors

        Returns:
            np.ndarray: Predictions
        """
        if self.model is None:
            raise ValueError("Model not trained")

        try:
            # If df_future is provided, use it
            if df_future is not None:
                future = df_future
            else:
                # Otherwise, create a future dataframe
                periods = future_periods or self.prediction_horizon
                future = self.model.make_future_dataframe(
                    periods=periods,
                    freq=self.frequency
                )

                # Add regressors if they were used during training
                if hasattr(self.model, 'extra_regressors') and self.model.extra_regressors:
                    for regressor in self.model.extra_regressors:
                        try:
                            # Check if regressor is a dictionary or an object
                            if isinstance(regressor, dict) and 'name' in regressor:
                                regressor_name = regressor['name']
                            elif hasattr(regressor, 'name'):
                                regressor_name = regressor.name
                            else:
                                logger.warning(f"Could not determine regressor name: {regressor}")
                                continue

                            # Add the regressor to the future dataframe if it's missing
                            if regressor_name not in future.columns:
                                # Use the last value for each regressor
                                if regressor_name in self.model.history.columns:
                                    future[regressor_name] = self.model.history[regressor_name].iloc[-1]
                                    logger.info(f"Added regressor {regressor_name} to future dataframe")
                                else:
                                    logger.warning(f"Regressor {regressor_name} not found in history")
                        except Exception as e:
                            logger.error(f"Error adding regressor: {str(e)}")
                            # Continue with other regressors

            # Make predictions
            logger.info(f"Making Prophet prediction with future dataframe of shape {future.shape}")
            forecast = self.model.predict(future)
            logger.info(f"Prophet forecast columns: {forecast.columns}")

            # Always return a simple numpy array with the last prediction value
            # This is the most compatible format for the rest of the code
            if 'yhat' in forecast.columns and len(forecast['yhat']) > 0:
                # Get the last prediction value
                prediction_value = forecast['yhat'].values[-1]

                # If the prediction is too close to zero, use the historical mean as a fallback
                if abs(prediction_value) < 0.001:
                    # Use the historical mean as a more realistic fallback
                    if hasattr(self.model, 'history') and 'y' in self.model.history.columns:
                        prediction_value = self.model.history['y'].mean()
                        logger.info(f"Using historical mean as fallback: {prediction_value}")
                    else:
                        # If no history is available, use 100 as a default value
                        prediction_value = 100.0
                        logger.info(f"Using default value as fallback: {prediction_value}")

                logger.info(f"Prophet prediction value: {prediction_value}")
                return np.array([prediction_value])
            else:
                logger.error("Prophet forecast does not contain 'yhat' column or is empty")
                # Use a more realistic fallback value
                if hasattr(self.model, 'history') and 'y' in self.model.history.columns:
                    fallback_value = self.model.history['y'].mean()
                    logger.info(f"Using historical mean as fallback: {fallback_value}")
                    return np.array([fallback_value])
                else:
                    # If no history is available, use 100 as a default value
                    logger.info("Using default value 100.0 as fallback")
                    return np.array([100.0])

        except Exception as e:
            logger.error(f"Error in Prophet prediction: {str(e)}")
            # Print the full traceback for debugging
            import traceback
            logger.error(traceback.format_exc())
            # Use a more realistic fallback value
            if hasattr(self.model, 'history') and 'y' in self.model.history.columns:
                fallback_value = self.model.history['y'].mean()
                logger.info(f"Using historical mean as fallback: {fallback_value}")
                return np.array([fallback_value])
            else:
                # If no history is available, use 100 as a default value
                logger.info("Using default value 100.0 as fallback")
                return np.array([100.0])

    def save(self, path='saved_models', symbol='stock', horizon=None):
        """
        Save the Prophet model

        Args:
            path (str): Path to save the model
            symbol (str): Stock symbol
            horizon (int): Prediction horizon
        """
        if self.model is None:
            raise ValueError("Model not trained")

        if not os.path.exists(path):
            os.makedirs(path)

        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Save model components
        model_data = {
            'model': self.model,
            'last_date': self.last_date,
            'frequency': self.frequency,
            'prediction_horizon': self.prediction_horizon
        }

        # Save model
        joblib.dump(model_data, os.path.join(path, f"{symbol}_prophet{horizon_str}.joblib"))

    def load(self, path='saved_models', symbol='stock', horizon=None):
        """
        Load a saved Prophet model

        Args:
            path (str): Path where the model is saved
            symbol (str): Stock symbol
            horizon (int): Prediction horizon

        Returns:
            Prophet model: Loaded model
        """
        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Load model
        model_path = os.path.join(path, f"{symbol}_prophet{horizon_str}.joblib")

        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model not found at {model_path}")

        # Load model components
        model_data = joblib.load(model_path)

        self.model = model_data['model']
        self.last_date = model_data['last_date']
        self.frequency = model_data['frequency']
        self.prediction_horizon = model_data['prediction_horizon']

        return self.model

def prepare_prophet_data(df, target_col='Close'):
    """
    Prepare data for Prophet model

    Args:
        df (pd.DataFrame): DataFrame with stock data
        target_col (str): Column to use as target

    Returns:
        pd.DataFrame: DataFrame ready for Prophet
    """
    # Create a copy to avoid modifying the original
    prophet_df = df.copy()

    # Rename columns for Prophet
    prophet_df['ds'] = prophet_df['Date']
    prophet_df['y'] = prophet_df[target_col]

    # Keep only necessary columns
    keep_cols = ['ds', 'y']

    # Add regressors if available
    for col in ['Open', 'High', 'Low', 'Volume']:
        if col in prophet_df.columns:
            keep_cols.append(col)

    return prophet_df[keep_cols]
