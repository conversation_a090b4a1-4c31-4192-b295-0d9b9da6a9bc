"""
Robust Ensemble Model

This module provides a robust implementation of ensemble models that avoids
NumPy random number generation issues.
"""

import os
import logging
import joblib
import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, RegressorMixin
from sklearn.linear_model import Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RobustEnsembleModel(BaseEstimator, RegressorMixin):
    """
    A robust ensemble model that combines multiple base models
    while avoiding NumPy random number generation issues.
    """
    
    def __init__(self, base_models=None, weights=None, meta_model=None):
        """
        Initialize the robust ensemble model.
        
        Args:
            base_models (list): List of base models
            weights (list): List of weights for each model
            meta_model: Meta-model for stacking
        """
        self.base_models = base_models if base_models is not None else []
        self.weights = weights
        self.meta_model = meta_model
        self.is_fitted = False
        
    def fit(self, X, y):
        """
        Fit the ensemble model.
        
        Args:
            X (np.ndarray): Input features
            y (np.ndarray): Target values
            
        Returns:
            self: Fitted model
        """
        if len(self.base_models) == 0:
            raise ValueError("No base models provided")
        
        # Prepare data
        X_2d = self._prepare_data(X)
        
        # Fit each base model
        for i, model in enumerate(self.base_models):
            logger.info(f"Fitting base model {i+1}/{len(self.base_models)}")
            try:
                model.fit(X_2d, y)
            except Exception as e:
                logger.error(f"Error fitting base model {i+1}: {str(e)}")
                # Replace with a simple model
                from sklearn.linear_model import LinearRegression
                self.base_models[i] = LinearRegression()
                self.base_models[i].fit(X_2d, y)
        
        # Initialize weights if not provided
        if self.weights is None:
            self.weights = [1.0 / len(self.base_models)] * len(self.base_models)
        
        # Fit meta-model if provided
        if self.meta_model is not None:
            # Generate predictions from base models
            meta_features = np.column_stack([
                model.predict(X_2d) for model in self.base_models
            ])
            
            try:
                self.meta_model.fit(meta_features, y)
            except Exception as e:
                logger.error(f"Error fitting meta-model: {str(e)}")
                # Fall back to Ridge regression
                self.meta_model = Ridge(alpha=1.0)
                self.meta_model.fit(meta_features, y)
        
        self.is_fitted = True
        return self
    
    def predict(self, X):
        """
        Make predictions with the ensemble model.
        
        Args:
            X (np.ndarray): Input features
            
        Returns:
            np.ndarray: Predictions
        """
        if not self.is_fitted:
            raise ValueError("Model has not been fitted yet")
        
        if len(self.base_models) == 0:
            raise ValueError("No base models available")
        
        # Prepare data
        X_2d = self._prepare_data(X)
        
        # Get predictions from each base model
        base_predictions = []
        for i, model in enumerate(self.base_models):
            try:
                pred = model.predict(X_2d)
                base_predictions.append(pred)
            except Exception as e:
                logger.error(f"Error with model {i}: {str(e)}")
                # Use a default prediction (average of other models or 0)
                if base_predictions:
                    # Use average of other models
                    pred = np.mean(base_predictions, axis=0)
                else:
                    # Use zeros
                    pred = np.zeros(X_2d.shape[0])
                base_predictions.append(pred)
        
        # Use meta-model if available
        if self.meta_model is not None:
            try:
                meta_features = np.column_stack(base_predictions)
                return self.meta_model.predict(meta_features)
            except Exception as e:
                logger.error(f"Error using meta-model: {str(e)}")
                # Fall back to weighted average
        
        # Use weighted average
        base_predictions = np.array(base_predictions)
        weighted_pred = np.zeros(base_predictions.shape[1])
        
        for i, pred in enumerate(base_predictions):
            weighted_pred += self.weights[i] * pred
        
        return weighted_pred
    
    def _prepare_data(self, X):
        """
        Prepare data for model input.
        
        Args:
            X (np.ndarray): Input data
            
        Returns:
            np.ndarray: Prepared data
        """
        # Check if input is 3D (for LSTM-like models)
        if len(X.shape) == 3:
            # Reshape to 2D for sklearn models
            return X.reshape(X.shape[0], -1)
        else:
            return X
    
    def save(self, path, symbol, horizon):
        """
        Save the ensemble model.
        
        Args:
            path (str): Directory path
            symbol (str): Stock symbol
            horizon (int): Prediction horizon
            
        Returns:
            str: Path to the saved model
        """
        if not os.path.exists(path):
            os.makedirs(path)
        
        # Create filename
        filename = f"{symbol}_ensemble_{horizon}min.joblib"
        file_path = os.path.join(path, filename)
        
        # Save model
        joblib.dump(self, file_path)
        logger.info(f"Saved ensemble model to {file_path}")
        
        return file_path
    
    @classmethod
    def load(cls, path, symbol, horizon):
        """
        Load an ensemble model.
        
        Args:
            path (str): Directory path
            symbol (str): Stock symbol
            horizon (int): Prediction horizon
            
        Returns:
            RobustEnsembleModel: Loaded model
        """
        # Create filename
        filename = f"{symbol}_ensemble_{horizon}min.joblib"
        file_path = os.path.join(path, filename)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Model file not found: {file_path}")
        
        # Load model
        model = joblib.load(file_path)
        logger.info(f"Loaded ensemble model from {file_path}")
        
        return model

def create_robust_ensemble(base_models, weights=None, meta_model_type=None):
    """
    Create a robust ensemble model.
    
    Args:
        base_models (list): List of base models
        weights (list): List of weights for each model
        meta_model_type (str): Type of meta-model to use
        
    Returns:
        RobustEnsembleModel: Ensemble model
    """
    # Create meta-model if requested
    meta_model = None
    if meta_model_type is not None:
        if meta_model_type.lower() == 'ridge':
            meta_model = Ridge(alpha=1.0)
        elif meta_model_type.lower() == 'lasso':
            meta_model = Lasso(alpha=0.1)
        elif meta_model_type.lower() == 'rf':
            meta_model = RandomForestRegressor(n_estimators=10, random_state=42)
    
    # Create ensemble model
    return RobustEnsembleModel(
        base_models=base_models,
        weights=weights,
        meta_model=meta_model
    )
