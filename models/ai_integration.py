"""
This module integrates external AI services into the AI Stocks Bot application
for enhanced stock price predictions.

It supports integration with various AI services via APIs:
- OpenAI (GPT-4)
- Azure OpenAI
- Anthropic (Claude)
- Custom API endpoints supporting the Model Context Protocol (MCP)
"""

import os
import json
import logging
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

# Configure logging
logger = logging.getLogger(__name__)

# Import helper functions
try:
    from app.utils.config_helpers import clean_config_value, get_cleaned_config
except ImportError:
    # Define function in case import fails
    def clean_config_value(value):
        """Helper function to clean config values when imported from app fails"""
        if not isinstance(value, str):
            return value

        # Remove quotes if present
        if value.startswith('"') and value.endswith('"'):
            return value[1:-1]
        elif value.startswith("'") and value.endswith("'"):
            return value[1:-1]

        return value

    def get_cleaned_config(config, section, key, fallback=""):
        """Helper function to get clean config values"""
        value = config.get(section, key, fallback=fallback)
        return clean_config_value(value)

class AIServiceIntegration:
    """
    Handles integration with external AI services for enhanced stock price predictions.
    Supports various service providers and the Model Context Protocol (MCP).
    """
    def __init__(self, provider="openai", api_key=None, endpoint=None):
        """
        Initialize the AI service integration.

        Args:
            provider (str): The AI service provider ("openai", "azure", "anthropic", "custom")
            api_key (str): API key for the service
            endpoint (str): Custom endpoint URL for Azure or custom providers
        """        # Normalize provider name and handle quoted values
        self.provider = clean_config_value(provider).lower()
        self.api_key = clean_config_value(api_key) or os.environ.get(f"{self.provider.upper()}_API_KEY", "")
        self.endpoint = clean_config_value(endpoint)

        # Validate configuration
        if self.provider == "mock":
            # Mock provider doesn't need API key or endpoint
            logger.info("Using mock AI provider (no API key required)")
        elif not self.api_key:
            logger.warning(f"No API key provided for {self.provider}. Some features may not work.")

        if self.provider in ["azure", "custom"] and not self.endpoint:
            logger.warning(f"No endpoint URL provided for {self.provider}. Service will not work.")

    def predict_with_ai(self, historical_data, symbol, horizons, additional_context=None):
        """
        Get price predictions using an external AI service.

        Args:
            historical_data (pd.DataFrame or dict): Historical price data
            symbol (str): Stock symbol
            horizons (list): List of prediction horizons
            additional_context (dict): Additional context for the AI model

        Returns:
            dict: Predictions for each horizon
        """
        # Prepare data for the AI model
        # Check if historical_data is already a dict (for testing)
        if isinstance(historical_data, dict):
            data_for_ai = historical_data
        else:
            data_for_ai = self._prepare_data_for_ai(historical_data, symbol, additional_context)

        # Call the appropriate service
        if self.provider == "openai":
            return self._call_openai(data_for_ai, horizons)
        elif self.provider == "azure":
            return self._call_azure_openai(data_for_ai, horizons)
        elif self.provider == "anthropic":
            return self._call_anthropic(data_for_ai, horizons)
        elif self.provider == "custom":
            return self._call_custom_endpoint(data_for_ai, horizons)
        elif self.provider == "mock":
            # Import the mock prediction function
            try:
                from models.ai_predict import _mock_predict_with_ai
                return _mock_predict_with_ai(data_for_ai, data_for_ai.get("symbol", "UNKNOWN"), horizons)
            except ImportError:
                logger.error("Could not import mock prediction function")
                return {h: None for h in horizons}
        else:
            logger.error(f"Unsupported AI provider: {self.provider}")
            return {h: None for h in horizons}

    def _prepare_data_for_ai(self, historical_data, symbol, additional_context=None):
        """
        Prepare data for sending to the AI model.

        Args:
            historical_data (pd.DataFrame): Historical price data
            symbol (str): Stock symbol
            additional_context (dict): Additional context for the AI model

        Returns:
            dict: Prepared data for the AI model
        """
        # Get the most recent data points (last 30 days)
        recent_data = historical_data.tail(30).copy()

        # Format the data for the AI model
        formatted_data = []
        for _, row in recent_data.iterrows():
            formatted_data.append({
                "date": row['Date'].strftime('%Y-%m-%d'),
                "open": float(row['Open']),
                "high": float(row['High']),
                "low": float(row['Low']),
                "close": float(row['Close']),
                "volume": int(row['Volume']) if 'Volume' in row else None
            })

        # Calculate basic statistics
        current_price = recent_data['Close'].iloc[-1]
        avg_price_30d = recent_data['Close'].mean()
        min_price_30d = recent_data['Close'].min()
        max_price_30d = recent_data['Close'].max()
        price_change_30d = (current_price - recent_data['Close'].iloc[0]) / recent_data['Close'].iloc[0] * 100

        # Get technical indicators if available
        technical_indicators = {}
        for col in recent_data.columns:
            if col not in ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Adj Close']:
                technical_indicators[col] = float(recent_data[col].iloc[-1])

        # Prepare the full context
        data = {
            "symbol": symbol,
            "current_date": datetime.now().strftime('%Y-%m-%d'),
            "current_price": float(current_price),
            "statistics": {
                "avg_price_30d": float(avg_price_30d),
                "min_price_30d": float(min_price_30d),
                "max_price_30d": float(max_price_30d),
                "price_change_30d": float(price_change_30d)            },
            "technical_indicators": technical_indicators,
            "historical_data": formatted_data
        }

        # Add additional context if provided
        if additional_context:
            data["additional_context"] = additional_context

        return data

    def _call_openai(self, data, horizons):
        """Call OpenAI API for predictions"""
        try:
            # Import OpenAI client - using the newer SDK
            try:
                from openai import OpenAI
                using_new_sdk = True
            except ImportError:
                # Fall back to older SDK if necessary
                import openai
                using_new_sdk = False

            # Prepare the message for GPT
            system_message = """You are a financial analyst AI specialized in stock price prediction.
            Analyze the provided historical data and technical indicators to predict future stock prices.
            Return your predictions as a JSON object with the specified time horizons as keys and predicted prices as values.
            Base your predictions on patterns in the data, technical indicators, and your knowledge of market behavior."""

            # Construct the user message with data and requested horizons
            user_message = f"""
            Please analyze the following stock data for {data.get('symbol', 'TEST')} and predict the price for these time horizons: {horizons}.
            Current price: {data.get('current_price', 100.0)}

            Recent historical data and technical indicators are provided below.

            {json.dumps(data, indent=2)}

            Return ONLY a JSON object with your predictions, with the time horizons as keys and predicted prices as values.
            Format: {{"horizon1": price1, "horizon2": price2, ...}}
            """

            # Call the OpenAI API using the appropriate SDK version
            if using_new_sdk:
                # New SDK approach
                client = OpenAI(api_key=self.api_key)
                response = client.chat.completions.create(
                    model="gpt-4",  # or another suitable model
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=0.3,  # Lower temperature for more deterministic outputs
                    max_tokens=500
                )
                # Extract the prediction from the new SDK response format
                prediction_text = response.choices[0].message.content.strip()
            else:
                # Legacy SDK approach
                openai.api_key = self.api_key
                response = openai.ChatCompletion.create(
                    model="gpt-4",  # or another suitable model
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=0.3,  # Lower temperature for more deterministic outputs
                    max_tokens=500
                )
                # Extract the prediction from the old SDK response format
                prediction_text = response.choices[0].message.content.strip()

            # If the response contains markdown code blocks, extract just the JSON part
            if "```json" in prediction_text:
                prediction_text = prediction_text.split("```json")[1].split("```")[0].strip()
            elif "```" in prediction_text:
                prediction_text = prediction_text.split("```")[1].split("```")[0].strip()

            # Try to parse the JSON response with error handling
            try:
                predictions = json.loads(prediction_text)
            except json.JSONDecodeError:
                # If we can't parse the response as JSON, try to extract just a JSON-like part
                import re
                json_pattern = r'\{.*?\}'
                match = re.search(json_pattern, prediction_text, re.DOTALL)
                if match:
                    predictions = json.loads(match.group(0))
                else:
                    # If all else fails, create a simple prediction
                    predictions = {str(h): data.get('current_price', 100.0) * 1.01 for h in horizons}
                    logger.warning("Could not parse OpenAI response as JSON. Using fallback predictions.")
              # Convert predictions to the expected format
            formatted_predictions = {}
            for horizon in horizons:
                horizon_str = str(horizon)
                if horizon_str in predictions:
                    try:
                        formatted_predictions[horizon] = float(predictions[horizon_str])
                    except (ValueError, TypeError):
                        # Handle non-numeric values
                        logger.warning(f"Non-numeric prediction for horizon {horizon}: {predictions[horizon_str]}")
                        formatted_predictions[horizon] = None
                else:
                    logger.warning(f"Horizon {horizon} not found in AI predictions")
                    formatted_predictions[horizon] = None

            logger.info(f"AI predictions retrieved successfully: {formatted_predictions}")
            return formatted_predictions

        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            # Return None for all horizons in case of error
            return {h: None for h in horizons}

    def _call_azure_openai(self, data, horizons):
        """Call Azure OpenAI API for predictions"""
        if not self.endpoint:
            logger.error("Azure OpenAI endpoint not configured")
            return {h: None for h in horizons}

        try:
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "api-key": self.api_key
            }

            # Prepare the system and user messages (similar to OpenAI)
            system_message = """You are a financial analyst AI specialized in stock price prediction.
            Analyze the provided historical data and technical indicators to predict future stock prices.
            Return your predictions as a JSON object with the specified time horizons as keys and predicted prices as values.
            Base your predictions on patterns in the data, technical indicators, and your knowledge of market behavior."""

            user_message = f"""
            Please analyze the following stock data for {data['symbol']} and predict the price for these time horizons: {horizons}.
            Current price: {data['current_price']}

            Recent historical data and technical indicators are provided below.

            {json.dumps(data, indent=2)}

            Return ONLY a JSON object with your predictions, with the time horizons as keys and predicted prices as values.
            Format: {{"horizon1": price1, "horizon2": price2, ...}}
            """

            # Prepare the request payload
            payload = {
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "temperature": 0.3,
                "max_tokens": 500
            }

            # Call the Azure OpenAI API
            response = requests.post(self.endpoint, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                response_data = response.json()
                prediction_text = response_data["choices"][0]["message"]["content"].strip()

                # If the response contains markdown code blocks, extract just the JSON part
                if "```json" in prediction_text:
                    prediction_text = prediction_text.split("```json")[1].split("```")[0].strip()
                elif "```" in prediction_text:
                    prediction_text = prediction_text.split("```")[1].split("```")[0].strip()

                # Parse the JSON response
                predictions = json.loads(prediction_text)

                # Convert to the expected format {horizon: price}
                formatted_predictions = {}
                for horizon in horizons:
                    horizon_str = str(horizon)
                    if horizon_str in predictions:
                        formatted_predictions[horizon] = float(predictions[horizon_str])
                    else:
                        logger.warning(f"Horizon {horizon} not found in AI predictions")
                        formatted_predictions[horizon] = None

                logger.info(f"Azure AI predictions retrieved successfully: {formatted_predictions}")
                return formatted_predictions
            else:
                logger.error(f"Azure OpenAI API error: {response.status_code} - {response.text}")
                return {h: None for h in horizons}

        except Exception as e:
            logger.error(f"Error calling Azure OpenAI API: {str(e)}")
            return {h: None for h in horizons}

    def _call_anthropic(self, data, horizons):
        """Call Anthropic API for predictions"""
        try:
            import anthropic

            # Initialize the Anthropic client
            client = anthropic.Anthropic(api_key=self.api_key)

            # Prepare the prompt for Claude
            prompt = f"""
            You are a financial analyst AI specialized in stock price prediction.
            Please analyze the following stock data for {data['symbol']} and predict the price for these time horizons: {horizons}.
            Current price: {data['current_price']}

            Recent historical data and technical indicators are provided below.

            {json.dumps(data, indent=2)}

            Based on the data, predict the future stock prices. Return ONLY a JSON object with your predictions,
            with the time horizons as keys and predicted prices as values.

            Format: {{"horizon1": price1, "horizon2": price2, ...}}
            """

            # Call the Anthropic API
            response = client.messages.create(
                model="claude-3-sonnet-20240229",  # or another suitable Claude model
                max_tokens=500,
                temperature=0.3,
                system="You are a financial analyst AI that provides accurate stock price predictions based on data analysis.",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )

            # Extract and parse the prediction
            prediction_text = response.content[0].text

            # If the response contains markdown code blocks, extract just the JSON part
            if "```json" in prediction_text:
                prediction_text = prediction_text.split("```json")[1].split("```")[0].strip()
            elif "```" in prediction_text:
                prediction_text = prediction_text.split("```")[1].split("```")[0].strip()

            # Parse the JSON response
            predictions = json.loads(prediction_text)

            # Convert to the expected format {horizon: price}
            formatted_predictions = {}
            for horizon in horizons:
                horizon_str = str(horizon)
                if horizon_str in predictions:
                    formatted_predictions[horizon] = float(predictions[horizon_str])
                else:
                    logger.warning(f"Horizon {horizon} not found in AI predictions")
                    formatted_predictions[horizon] = None

            logger.info(f"Anthropic AI predictions retrieved successfully: {formatted_predictions}")
            return formatted_predictions

        except Exception as e:
            logger.error(f"Error calling Anthropic API: {str(e)}")
            return {h: None for h in horizons}

    def _call_custom_endpoint(self, data, horizons):
        """
        Call a custom API endpoint that supports the Model Context Protocol (MCP)
        or another custom API format
        """
        if not self.endpoint:
            logger.error("Custom endpoint not configured")
            return {h: None for h in horizons}

        try:
            # Prepare headers
            headers = {
                "Content-Type": "application/json"
            }

            # Add API key to headers if provided
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"

            # Prepare the payload for MCP format
            payload = {
                "context": {
                    "task": "stock_price_prediction",
                    "symbol": data["symbol"],
                    "current_price": data["current_price"],
                    "history": data["historical_data"],
                    "technical_indicators": data.get("technical_indicators", {}),
                    "statistics": data.get("statistics", {}),
                    "horizons": horizons
                },
                "examples": [
                    {
                        "input": "Predict stock prices",
                        "output": {"4": 45.2, "30": 46.8, "69": 48.3}
                    }
                ],
                "query": f"Predict future prices for {data['symbol']} at horizons {horizons}"
            }

            # Call the custom API endpoint
            response = requests.post(self.endpoint, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                response_data = response.json()

                # Extract predictions from the response
                # The exact format will depend on your custom API's response structure
                if "predictions" in response_data:
                    predictions = response_data["predictions"]
                elif "result" in response_data:
                    predictions = response_data["result"]
                elif "response" in response_data:
                    predictions = response_data["response"]
                else:
                    predictions = response_data  # Assume the response itself is the predictions

                # Convert to the expected format {horizon: price}
                formatted_predictions = {}
                for horizon in horizons:
                    horizon_str = str(horizon)
                    if horizon_str in predictions:                    formatted_predictions[horizon] = float(predictions[horizon_str])
                    else:
                        logger.warning(f"Horizon {horizon} not found in custom API predictions")
                        formatted_predictions[horizon] = None

                logger.info(f"Custom API predictions retrieved successfully: {formatted_predictions}")
                return formatted_predictions
            else:
                logger.error(f"Custom API error: {response.status_code} - {response.text}")
                return {h: None for h in horizons}

        except Exception as e:
            logger.error(f"Error calling custom API endpoint: {str(e)}")
            return {h: None for h in horizons}


# Function to ensemble predictions from multiple sources
def ensemble_predictions(predictions_list, weights=None):
    """
    Create an ensemble of predictions from multiple sources.

    Args:
        predictions_list (list): List of prediction dictionaries
        weights (list): Optional list of weights for each prediction source

    Returns:
        dict: Ensembled predictions
    """
    if not predictions_list:
        return {}

    # If no weights provided, use equal weights
    if weights is None:
        weights = [1.0] * len(predictions_list)

    # Normalize weights
    total_weight = sum(weights)
    if total_weight == 0:
        return {}
    weights = [w / total_weight for w in weights]

    # Get all horizons from all predictions
    all_horizons = set()
    for preds in predictions_list:
        all_horizons.update(preds.keys())

    # Calculate weighted average for each horizon
    ensemble_result = {}
    for horizon in all_horizons:
        valid_predictions = []
        valid_weights = []

        # Collect valid predictions and their weights
        for i, preds in enumerate(predictions_list):
            if horizon in preds and preds[horizon] is not None:
                valid_predictions.append(preds[horizon])
                valid_weights.append(weights[i])

        # Calculate weighted average if we have valid predictions
        if valid_predictions:
            # Normalize valid weights
            total_valid_weight = sum(valid_weights)
            if total_valid_weight > 0:
                normalized_weights = [w / total_valid_weight for w in valid_weights]
                weighted_sum = sum(p * w for p, w in zip(valid_predictions, normalized_weights))
                ensemble_result[horizon] = weighted_sum

    return ensemble_result
