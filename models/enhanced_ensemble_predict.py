"""
Enhanced ensemble prediction module for the AI Stocks Bot app
"""
import numpy as np
import pandas as pd
import logging
import os
import joblib
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_enhanced_ensemble_predictions(historical_data, live_data, symbol, horizons,
                                     sequence_length=60, models_path='saved_models',
                                     max_price_change_pct=10.0, weighting_strategy='adaptive'):
    """
    Get detailed predictions from enhanced ensemble model including individual model predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical stock data
        live_data (pd.DataFrame): DataFrame with live stock data (can be None)
        symbol (str): Stock symbol
        horizons (list): List of prediction horizons
        sequence_length (int): Number of time steps to look back
        models_path (str): Path where models are saved
        max_price_change_pct (float): Maximum allowed price change as a percentage
        weighting_strategy (str): Strategy for weighting models

    Returns:
        dict: Dictionary with ensemble and individual model predictions for each horizon
    """
    try:
        from app.utils.data_processing import load_scaler
        from app.utils.feature_engineering import prepare_features, prepare_prediction_data
        from models.sklearn_model import StockPredictionModel
        from models.ensemble_model import EnhancedEnsembleModel, create_enhanced_ensemble

        # Start timing
        start_time = datetime.now()
        logger.info(f"Starting enhanced ensemble predictions for {symbol} with {len(horizons)} horizons")

        # Combine historical and live data if available
        if live_data is not None and not live_data.empty:
            df = pd.concat([historical_data, live_data], ignore_index=True)
        else:
            df = historical_data.copy()

        # Apply feature engineering
        df_features = prepare_features(df)

        # Get the current price to use as a reference
        current_price = None
        if 'Close' in df.columns:
            current_price = df['Close'].iloc[-1]
        elif 'close' in df.columns:
            current_price = df['close'].iloc[-1]

        logger.info(f"Current price for {symbol}: {current_price}")

        # Dictionary to store results
        results = {}

        # Base model names for the ensemble - will be populated dynamically when loading models

        # Make predictions for each horizon
        for horizon in horizons:
            logger.info(f"Making enhanced ensemble predictions for {horizon} horizon")

            # Load scaler
            scaler = load_scaler(symbol, horizon=horizon, model_type='ensemble', path=models_path)

            # Get the expected number of features from the scaler
            expected_features = None
            if hasattr(scaler, 'n_features_in_'):
                expected_features = scaler.n_features_in_
                logger.info(f"Scaler expects {expected_features} features")

            # Define core feature names based on the expected count
            # This ensures we select the right features for the model
            if expected_features == 7:
                # Standard 7-feature set
                core_feature_names = ['Open', 'High', 'Low', 'Close', 'Volume', 'SMA20', 'RSI']
            elif expected_features == 5:
                # Minimal 5-feature set
                core_feature_names = ['Open', 'High', 'Low', 'Close', 'Volume']
            else:
                # Default to None, which will use all available features
                core_feature_names = None

            # Prepare data for prediction with consistent feature selection
            features = prepare_prediction_data(df_features, sequence_length, feature_subset=core_feature_names)

            # Log the feature shape for debugging
            logger.info(f"Feature shape after selection: {features.shape}")

            # Normalize data - handle feature dimension mismatch
            try:
                features_scaled = scaler.transform(features)
            except ValueError as e:
                logger.warning(f"Feature dimension mismatch: {str(e)}")

                # Get the expected number of features from the error message if not already known
                if expected_features is None:
                    import re
                    match = re.search(r'expecting (\d+) features as input', str(e))
                    if match:
                        expected_features = int(match.group(1))
                        logger.info(f"Scaler expects {expected_features} features, but got {features.shape[1]}")

                # Handle the mismatch
                if expected_features is not None:
                    # If we have more features than expected, select only the first expected_features
                    if features.shape[1] > expected_features:
                        logger.info(f"Selecting first {expected_features} features from {features.shape[1]} total features")
                        features_subset = features[:, :expected_features]
                        features_scaled = scaler.transform(features_subset)
                    else:
                        # If we have fewer features than expected, pad with zeros
                        logger.info(f"Padding features from {features.shape[1]} to {expected_features}")
                        features_padded = np.zeros((features.shape[0], expected_features))
                        features_padded[:, :features.shape[1]] = features
                        features_scaled = scaler.transform(features_padded)
                else:
                    # If we can't determine the expected features, try to create a new scaler
                    logger.warning("Could not determine expected features, creating new scaler")
                    from sklearn.preprocessing import MinMaxScaler
                    new_scaler = MinMaxScaler()
                    features_scaled = new_scaler.fit_transform(features)
                    logger.info(f"Created new scaler for {features.shape[1]} features")

            # Reshape for model input [samples, time steps, features]
            X_pred = np.array([features_scaled])

            # Try to load existing enhanced ensemble model
            enhanced_model_filename = f"{symbol}_enhanced_ensemble_{horizon}.pkl"
            enhanced_model_path = os.path.join(models_path, enhanced_model_filename)

            if os.path.exists(enhanced_model_path):
                # Load existing enhanced ensemble model
                logger.info(f"Loading existing enhanced ensemble model from {enhanced_model_path}")
                try:
                    # EnhancedEnsembleModel is already imported at the top of the file
                    enhanced_model = joblib.load(enhanced_model_path)
                    logger.info(f"Successfully loaded enhanced ensemble model from {enhanced_model_path}")

                    # Add a check to ensure the loaded object is a valid EnhancedEnsembleModel instance
                    if not isinstance(enhanced_model, EnhancedEnsembleModel):
                        logger.error(f"Loaded object from {enhanced_model_path} is not an EnhancedEnsembleModel instance. Type: {type(enhanced_model)}")
                        enhanced_model = None # Treat as if loading failed
                    elif not hasattr(enhanced_model, 'base_models'):
                         logger.warning("Loaded enhanced ensemble model does not have 'base_models' attribute.")

                except Exception as e:
                    logger.error(f"Error loading enhanced ensemble model from {enhanced_model_path}: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
                    # Fall back to creating a new model
                    enhanced_model = None
            else:
                logger.info(f"Enhanced ensemble model file not found at {enhanced_model_path}. Will attempt to create a new one.")
                enhanced_model = None

            if enhanced_model is None:
                logger.info(f"Attempting to create a new enhanced ensemble model for {symbol} horizon {horizon}")

                # Load base models
                base_models = []
                base_model_names = []

                # First try to load scikit-learn models
                sklearn_model_types = ['rf', 'gb', 'lr', 'svr']

                # Check if XGBoost is available
                try:
                    __import__("xgboost")
                    sklearn_model_types.append('xgb')
                except ImportError:
                    logger.warning("XGBoost not available")

                # Check if Prophet is available
                try:
                    __import__("prophet")
                    sklearn_model_types.append('prophet')
                except ImportError:
                    logger.warning("Prophet not available")

                # Load scikit-learn models
                for model_type in sklearn_model_types:
                    try:
                        model = StockPredictionModel(
                            sequence_length=sequence_length,
                            prediction_horizon=horizon,
                            model_type=model_type
                        )
                        model.load(path=models_path, symbol=symbol, horizon=horizon)
                        base_models.append(model.model)
                        base_model_names.append(model_type.capitalize())
                        logger.info(f"Loaded {model_type} model for horizon {horizon}")
                    except Exception as e:
                        logger.warning(f"Could not load {model_type} model: {str(e)}")

                # Try to load TensorFlow models if available
                try:
                    # Check if TensorFlow is available
                    from models.train import USING_TENSORFLOW
                    if USING_TENSORFLOW:
                        # Try to load LSTM model
                        try:
                            from models.lstm_model import StockLSTMModel
                            lstm_model = StockLSTMModel(
                                sequence_length=sequence_length,
                                prediction_horizon=horizon
                            )
                            lstm_model.load(path=models_path, symbol=symbol, horizon=horizon)
                            base_models.append(lstm_model.model)
                            base_model_names.append('LSTM')
                            logger.info(f"Loaded LSTM model for horizon {horizon}")
                        except Exception as e:
                            logger.warning(f"Could not load LSTM model: {str(e)}")
                except ImportError:
                    logger.warning("TensorFlow not available, skipping deep learning models")

                if len(base_models) == 0:
                    logger.error(f"No base models could be loaded for {symbol} horizon {horizon}")
                    results[horizon] = {
                        'ensemble': 0.0,
                        'individual': {name: 0.0 for name in base_model_names},
                        'error': "No base models could be loaded"
                    }
                    continue

                # Create enhanced ensemble model
                enhanced_model = create_enhanced_ensemble(
                    base_models=base_models,
                    model_names=base_model_names[:len(base_models)],
                    weighting_strategy=weighting_strategy
                )

                # Save the enhanced ensemble model
                try:
                    enhanced_model.save(models_path, enhanced_model_filename)
                    logger.info(f"Saved enhanced ensemble model to {enhanced_model_path}")
                except Exception as e:
                    logger.error(f"Error saving enhanced ensemble model: {str(e)}")

            # Make predictions with the enhanced ensemble model
            try:
                # Get predictions from each base model
                X_2d = enhanced_model._prepare_data(X_pred)

                individual_preds = {}
                predictions = []

                # Get model names
                model_names = enhanced_model.model_names if enhanced_model.model_names else base_model_names[:len(enhanced_model.base_models)]

                # Get predictions from each base model
                for i, base_model in enumerate(enhanced_model.base_models):
                    try:
                        model_name = model_names[i] if i < len(model_names) else f"Model {i+1}"

                        # Check if the base model is a dictionary (which indicates a loading error)
                        if isinstance(base_model, dict):
                            logger.warning(f"Base model {i} is a dictionary, not a proper model object")

                            # Try to extract the actual model from the dictionary
                            if 'model' in base_model:
                                logger.info(f"Extracting model from dictionary for base model {i}")
                                actual_model = base_model['model']
                                pred = actual_model.predict(X_2d)
                            else:
                                # If we can't extract the model, try to rebuild it
                                logger.warning(f"Attempting to rebuild model {i} (type: {model_names[i] if i < len(model_names) else 'unknown'})")

                                # Try to determine the model type from the name
                                model_type = 'rf'  # Default to Random Forest
                                if i < len(model_names):
                                    name = model_names[i].lower()
                                    if 'forest' in name:
                                        model_type = 'rf'
                                    elif 'boost' in name:
                                        model_type = 'gb'
                                    elif 'linear' in name or 'regression' in name:
                                        model_type = 'lr'
                                    elif 'xgboost' in name:
                                        model_type = 'xgb'
                                    elif 'prophet' in name:
                                        model_type = 'prophet'

                                # Try to rebuild the model
                                try:
                                    from models.sklearn_model import StockPredictionModel
                                    rebuilt_model = StockPredictionModel(
                                        sequence_length=sequence_length,
                                        prediction_horizon=horizon,
                                        model_type=model_type
                                    )
                                    # Try to load the model
                                    rebuilt_model.load(path=models_path, symbol=symbol, horizon=horizon)

                                    # Replace the dictionary with the actual model
                                    enhanced_model.base_models[i] = rebuilt_model.model

                                    # Make prediction with the rebuilt model
                                    pred = rebuilt_model.model.predict(X_2d)
                                    logger.info(f"Successfully rebuilt and used model {i}")
                                except Exception as rebuild_error:
                                    logger.error(f"Failed to rebuild model {i}: {str(rebuild_error)}")
                                    # If rebuilding fails, raise an error to trigger the fallback
                                    raise ValueError(f"Base model {i} is a dictionary without a 'model' key and could not be rebuilt")
                        else:
                            # Normal case - model is a proper object
                            pred = base_model.predict(X_2d)

                        predictions.append(pred)
                        individual_preds[model_name] = pred
                    except Exception as e:
                        logger.error(f"Error with base model {i}: {str(e)}")
                        # Use a default prediction (average of other models or 0)
                        if predictions:
                            # Use average of other models
                            pred = np.mean(predictions, axis=0)
                        else:
                            # Use zeros if no other predictions available
                            pred = np.zeros_like(X_2d[:, 0])
                        predictions.append(pred)
                        individual_preds[f"Model {i+1} (Error)"] = pred

                # Get ensemble prediction with error handling
                try:
                    ensemble_pred = enhanced_model.predict(X_pred)

                    # Get model weights
                    weights = enhanced_model.weights

                    # Handle scikit-learn model output (flat array)
                    ensemble_scaled = ensemble_pred[0]
                except Exception as e:
                    logger.error(f"Error in ensemble prediction: {str(e)}")

                    # Calculate a fallback ensemble prediction based on individual predictions
                    if predictions:
                        # Use the mean of available individual predictions
                        ensemble_pred = np.mean(predictions, axis=0)
                        ensemble_scaled = ensemble_pred[0]
                        logger.info(f"Using mean of individual predictions as fallback: {ensemble_scaled}")

                        # Use equal weights as fallback
                        num_models = len(enhanced_model.base_models)
                        weights = np.ones(num_models) / num_models
                        logger.info(f"Using equal weights as fallback: {weights}")
                    else:
                        # If no individual predictions, use a default value
                        logger.error("No individual predictions available for fallback")
                        raise ValueError("Cannot generate ensemble prediction")

                # Create a dummy array to inverse transform
                try:
                    # Get the number of features expected by the scaler
                    n_features = scaler.n_features_in_ if hasattr(scaler, 'n_features_in_') else features.shape[1]

                    # Create a dummy array with the right number of features
                    dummy = np.zeros((1, n_features))

                    # Close price is typically at index 3, but ensure it's within bounds
                    close_idx = min(3, n_features - 1)
                    dummy[0, close_idx] = ensemble_scaled

                    # Inverse transform to get the actual ensemble price
                    inverse_transformed = scaler.inverse_transform(dummy)
                    ensemble_price = inverse_transformed[0, close_idx]
                except Exception as e:
                    logger.error(f"Error in inverse transform: {str(e)}")
                    # Fallback: use the scaled value directly with a reasonable multiplier
                    ensemble_price = ensemble_scaled * 100  # Arbitrary multiplier

                # Process individual model predictions
                individual_prices = {}
                for model_name, pred in individual_preds.items():
                    # Get the prediction value
                    pred_scaled = pred[0]

                    try:
                        # Get the number of features expected by the scaler
                        n_features = scaler.n_features_in_ if hasattr(scaler, 'n_features_in_') else features.shape[1]

                        # Create a dummy array with the right number of features
                        dummy = np.zeros((1, n_features))

                        # Close price is typically at index 3, but ensure it's within bounds
                        close_idx = min(3, n_features - 1)
                        dummy[0, close_idx] = pred_scaled

                        # Inverse transform to get the actual price
                        inverse_transformed = scaler.inverse_transform(dummy)
                        pred_price = inverse_transformed[0, close_idx]
                    except Exception as e:
                        logger.error(f"Error in inverse transform for {model_name}: {str(e)}")
                        # Fallback: use the scaled value directly with a reasonable multiplier
                        pred_price = pred_scaled * 100  # Arbitrary multiplier

                    individual_prices[model_name] = pred_price

                # Apply sanity check to predictions if we have a current price
                if current_price is not None:
                    # Calculate the maximum allowed change based on the parameter
                    max_change = current_price * (max_price_change_pct / 100.0)

                    # Ensure ensemble prediction is within reasonable bounds
                    if abs(ensemble_price - current_price) > max_change:
                        logger.warning(f"Ensemble prediction {ensemble_price} is too far from current price {current_price}. Adjusting.")
                        # Adjust the prediction to be within the allowed range
                        if ensemble_price > current_price:
                            ensemble_price = current_price + max_change
                        else:
                            ensemble_price = current_price - max_change

                    # Also apply sanity check to individual model predictions
                    for model_name in individual_prices.keys():
                        pred_price = individual_prices[model_name]
                        if abs(pred_price - current_price) > max_change:
                            logger.warning(f"{model_name} prediction {pred_price} is too far from current price {current_price}. Adjusting.")
                            # Adjust the prediction to be within the allowed range
                            if pred_price > current_price:
                                individual_prices[model_name] = current_price + max_change
                            else:
                                individual_prices[model_name] = current_price - max_change

                # Store results for this horizon
                results[horizon] = {
                    'ensemble': ensemble_price,
                    'individual': individual_prices,
                    'weights': weights.tolist() if weights is not None else None
                }

                logger.info(f"Enhanced ensemble prediction for {horizon} horizon: {ensemble_price}")

            except Exception as e:
                logger.error(f"Error making enhanced ensemble predictions for horizon {horizon}: {str(e)}")

                # Use current price as fallback if available
                fallback_price = 100.0  # Default fallback
                if current_price is not None:
                    fallback_price = current_price

                # Create fallback individual predictions with small variations
                import random
                individual_prices = {}
                for model_name in base_model_names:
                    # Add a small random variation (-2% to +3%)
                    variation = (random.random() * 0.05) - 0.02
                    individual_prices[model_name] = fallback_price * (1 + variation)

                # For longer horizons, add a slightly larger trend
                # Convert minutes to days for scaling (assuming trading day is ~8 hours)
                days_equivalent = horizon / (8 * 60)
                # Cap at 5 days for scaling purposes
                days_equivalent = min(days_equivalent, 5)
                # Add a small trend based on the horizon (0.5% per day, could be positive or negative)
                trend = (random.random() * 0.02 - 0.01) * days_equivalent
                ensemble_price = fallback_price * (1 + trend)

                results[horizon] = {
                    'ensemble': ensemble_price,
                    'individual': individual_prices,
                    'error': str(e)
                }

        # Log total time
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"Enhanced ensemble predictions completed in {duration:.2f} seconds")

        return results

    except Exception as e:
        logger.error(f"Error in enhanced ensemble predictions: {str(e)}")
        # Return a default result with error information
        error_results = {}

        # Try to get the current price to use as a baseline for fallback predictions
        fallback_price = 100.0  # Default fallback if we can't get the current price
        if 'current_price' in locals() and current_price is not None:
            fallback_price = current_price

        for horizon in horizons:
            # Create fallback individual predictions with small variations
            import random
            individual_preds = {}
            for model_name in ['Random Forest', 'Gradient Boosting', 'Linear Regression', 'XGBoost', 'Prophet']:
                # Add a small random variation (-2% to +3%)
                variation = (random.random() * 0.05) - 0.02
                individual_preds[model_name] = fallback_price * (1 + variation)

            # For longer horizons, add a slightly larger trend
            # Convert minutes to days for scaling (assuming trading day is ~8 hours)
            days_equivalent = horizon / (8 * 60)
            # Cap at 5 days for scaling purposes
            days_equivalent = min(days_equivalent, 5)
            # Add a small trend based on the horizon (0.5% per day, could be positive or negative)
            trend = (random.random() * 0.02 - 0.01) * days_equivalent
            ensemble_price = fallback_price * (1 + trend)

            error_results[horizon] = {
                'ensemble': ensemble_price,
                'individual': individual_preds,
                'error': str(e)
            }

        logger.info(f"Using fallback predictions based on price: {fallback_price}")
        return error_results
