import tensorflow as tf  # type: ignore
from tensorflow.keras.models import Sequential, load_model  # type: ignore
from tensorflow.keras.layers import LSTM, Dense, Dropout, Bidirectional  # type: ignore
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint  # type: ignore
import numpy as np
import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StockPredictionModel:
    """
    LSTM model for stock price prediction
    """

    def __init__(self, sequence_length=60, prediction_horizon=1, model_type='lstm'):
        """
        Initialize the model

        Args:
            sequence_length (int): Number of time steps to look back
            prediction_horizon (int): Number of time steps to predict ahead
            model_type (str): Type of model ('lstm', 'bilstm')
        """
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.model_type = model_type
        self.model = None

    def build_lstm_model(self, input_shape, output_units=1):
        """
        Build LSTM model

        Args:
            input_shape (tuple): Shape of input data (sequence_length, features)
            output_units (int): Number of output units

        Returns:
            tf.keras.Model: Compiled LSTM model
        """
        model = Sequential()

        # LSTM layers
        model.add(LSTM(units=50, return_sequences=True, input_shape=input_shape))
        model.add(Dropout(0.2))

        model.add(LSTM(units=50, return_sequences=True))
        model.add(Dropout(0.2))

        model.add(LSTM(units=50))
        model.add(Dropout(0.2))

        # Output layer
        model.add(Dense(units=output_units))

        # Compile model
        model.compile(optimizer='adam', loss='mean_squared_error')

        return model

    def build_bilstm_model(self, input_shape, output_units=1):
        """
        Build Bidirectional LSTM model

        Args:
            input_shape (tuple): Shape of input data (sequence_length, features)
            output_units (int): Number of output units

        Returns:
            tf.keras.Model: Compiled Bidirectional LSTM model
        """
        model = Sequential()

        # Bidirectional LSTM layers
        model.add(Bidirectional(LSTM(units=50, return_sequences=True), input_shape=input_shape))
        model.add(Dropout(0.2))

        model.add(Bidirectional(LSTM(units=50, return_sequences=True)))
        model.add(Dropout(0.2))

        model.add(Bidirectional(LSTM(units=50)))
        model.add(Dropout(0.2))

        # Output layer
        model.add(Dense(units=output_units))

        # Compile model
        model.compile(optimizer='adam', loss='mean_squared_error')

        return model

    def train(self, X_train, y_train, epochs=100, batch_size=32, validation_split=0.2,
              save_path='saved_models', symbol='stock', horizon=None):
        """
        Train the model

        Args:
            X_train (np.ndarray): Training data
            y_train (np.ndarray): Target values
            epochs (int): Number of epochs
            batch_size (int): Batch size
            validation_split (float): Validation split ratio
            save_path (str): Path to save the model
            symbol (str): Stock symbol
            horizon (int): Prediction horizon (minutes)

        Returns:
            tf.keras.Model: Trained model
        """
        # Get input shape
        _, timesteps, features = X_train.shape
        input_shape = (timesteps, features)

        # Build model
        if self.model_type == 'lstm':
            self.model = self.build_lstm_model(input_shape)
        elif self.model_type == 'bilstm':
            self.model = self.build_bilstm_model(input_shape)
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")

        # Create save directory if it doesn't exist
        if not os.path.exists(save_path):
            os.makedirs(save_path)

        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Set up callbacks
        callbacks = [
            EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True),
            ModelCheckpoint(
                filepath=os.path.join(save_path, f"{symbol}_{self.model_type}{horizon_str}.keras"),
                monitor='val_loss',
                save_best_only=True
            )
        ]

        # Train model
        history = self.model.fit(
            X_train, y_train,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            callbacks=callbacks,
            verbose=1
        )

        return self.model, history

    def predict(self, X):
        """
        Make predictions

        Args:
            X (np.ndarray): Input data

        Returns:
            np.ndarray: Predictions
        """
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        return self.model.predict(X)

    def save(self, path='saved_models', symbol='stock', horizon=None):
        """
        Save the model

        Args:
            path (str): Path to save the model
            symbol (str): Stock symbol
            horizon (int): Prediction horizon (minutes)
        """
        if self.model is None:
            raise ValueError("Model not trained")

        if not os.path.exists(path):
            os.makedirs(path)

        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Use the .keras extension instead of .h5
        model_path = os.path.join(path, f"{symbol}_{self.model_type}{horizon_str}.keras")
        logger.info(f"Saving model to {model_path} using native Keras format")
        self.model.save(model_path)

    def load(self, path='saved_models', symbol='stock', horizon=None):
        """
        Load a saved model

        Args:
            path (str): Path where the model is saved
            symbol (str): Stock symbol
            horizon (int): Prediction horizon (minutes)

        Returns:
            tf.keras.Model: Loaded model
        """
        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Try the new .keras format first
        keras_path = os.path.join(path, f"{symbol}_{self.model_type}{horizon_str}.keras")
        h5_path = os.path.join(path, f"{symbol}_{self.model_type}{horizon_str}.h5")

        # Check which format exists
        if os.path.exists(keras_path):
            logger.info(f"Loading model from native Keras format: {keras_path}")
            model_path = keras_path
        elif os.path.exists(h5_path):
            logger.info(f"Loading model from legacy HDF5 format: {h5_path}")
            model_path = h5_path
        else:
            # Neither format exists
            error_msg = f"Model not found at {keras_path} or {h5_path}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        self.model = load_model(model_path)
        return self.model
