import os
import pandas as pd
import numpy as np
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Try to import TensorFlow-based models, fall back to scikit-learn model if not available
try:
    # Check if we should disable TensorFlow (via config marker file)
    no_tensorflow_cfg = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'no_tensorflow.cfg')
    force_sklearn = os.path.exists(no_tensorflow_cfg)

    if force_sklearn:
        logger.info("TensorFlow disabled by configuration. Using scikit-learn models only.")
        raise ImportError("TensorFlow disabled by configuration")

    # Try the actual import and verification
    import tensorflow as tf

    # Check TensorFlow version
    tf_version = tf.__version__
    logger.info(f"TensorFlow version: {tf_version}")

    # Verify TensorFlow is working by performing a simple operation
    try:
        test_tensor = tf.constant([[1.0, 2.0], [3.0, 4.0]])
        test_result = tf.reduce_mean(test_tensor)
        _ = test_result.numpy()  # This will raise an exception if TensorFlow is not working
        logger.info(f"TensorFlow test successful")
    except (TypeError, AttributeError) as e:
        logger.warning(f"TensorFlow test failed: {str(e)}")
        raise ImportError("TensorFlow not working correctly")

    # Import TensorFlow-based models
    from models.lstm_model import StockPredictionModel as LSTMModel

    # Try to import Transformer model
    try:
        from models.transformer_model import StockTransformerModel
        TRANSFORMER_AVAILABLE = True
        logger.info("Transformer model is available")
    except ImportError as e:
        TRANSFORMER_AVAILABLE = False
        logger.warning(f"Transformer model not available: {str(e)}")

    # Default to LSTM model
    StockPredictionModel = LSTMModel
    USING_TENSORFLOW = True
    logger.info("Using TensorFlow-based models")
except (ImportError, AttributeError, ModuleNotFoundError) as e:
    logger.warning(f"TensorFlow import error: {str(e)}")
    from models.sklearn_model import StockPredictionModel
    TRANSFORMER_AVAILABLE = False
    USING_TENSORFLOW = False
    logger.info("TensorFlow not available. Using scikit-learn based model instead")
except Exception as e:
    logger.warning(f"TensorFlow test failed: {str(e)}")
    from models.sklearn_model import StockPredictionModel
    TRANSFORMER_AVAILABLE = False
    USING_TENSORFLOW = False
    logger.info("TensorFlow available but not working correctly. Using scikit-learn based model instead")

from app.utils.data_processing import preprocess_data, save_scaler
from app.utils.feature_engineering import prepare_features

def train_model(df, symbol, horizons=[5, 15, 30, 60], sequence_length=60,
                model_type='lstm', epochs=100, batch_size=32, save_path='saved_models'):
    """
    Train models for different prediction horizons

    Args:
        df (pd.DataFrame): DataFrame with stock data
        symbol (str): Stock symbol
        horizons (list): List of prediction horizons in minutes
        sequence_length (int): Number of time steps to look back
        model_type (str): Type of model ('lstm', 'bilstm', 'rf', 'gb', 'lr', 'svr', 'prophet')
        epochs (int): Number of epochs
        batch_size (int): Batch size
        save_path (str): Path to save the models

    Returns:
        dict: Dictionary with trained models
    """
    # Log the training parameters
    logger.info(f"Training models for symbol: {symbol}")
    logger.info(f"Model type: {model_type}")
    logger.info(f"Horizons: {horizons}")
    logger.info(f"Sequence length: {sequence_length}")
    logger.info(f"Epochs: {epochs}")
    logger.info(f"Batch size: {batch_size}")
    logger.info(f"Data shape: {df.shape}")

    # Prepare features
    logger.info("Preparing features...")
    df_features = prepare_features(df)
    logger.info(f"Features prepared. Shape: {df_features.shape}")

    # Log available columns after feature engineering
    logger.info(f"Available columns after feature engineering: {df_features.columns.tolist()}")

    # Create directory for saving models if it doesn't exist
    if not os.path.exists(save_path):
        os.makedirs(save_path)
        logger.info(f"Created directory: {save_path}")

    # Dictionary to store trained models
    trained_models = {}

    # Train models for each horizon
    for horizon in horizons:
        logger.info(f"Training model for {horizon} minutes horizon")
        logger.info(f"Data shape before processing: {df_features.shape}")

        try:
            # Check if we have enough data for this horizon
            # For minute-based data, we need enough rows to cover the horizon
            # For daily data, we need enough days to cover the horizon in minutes

            # Estimate how many rows we need based on data frequency
            data_frequency = "daily"  # Default assumption

            # Try to detect if we have intraday data by checking time differences
            if len(df_features) > 1:
                df_features['Date'] = pd.to_datetime(df_features['Date'])
                time_diffs = df_features['Date'].diff().dropna()
                if time_diffs.min().total_seconds() < 24*60*60:  # Less than a day
                    data_frequency = "intraday"

            # Calculate required rows based on frequency
            if data_frequency == "intraday":
                # For intraday data, we need horizon + sequence_length rows
                min_required_rows = horizon + sequence_length
            else:
                # For daily data, we need enough days to cover the horizon in minutes
                # plus the sequence length
                days_needed = max(10, (horizon / (24 * 60)) + 1)  # At least 10 days
                min_required_rows = int(days_needed) + sequence_length

            logger.info(f"Detected data frequency: {data_frequency}")
            logger.info(f"Minimum required rows: {min_required_rows}")

            if len(df_features) <= min_required_rows:
                logger.error(f"Not enough data for horizon {horizon} minutes. Need more than {min_required_rows} rows, but got {len(df_features)} rows.")
                continue

            # Create target variable for this horizon
            logger.info(f"Creating target variable for horizon {horizon} minutes...")

            # For daily data, we need to adjust the shift based on data frequency
            if data_frequency == "intraday":
                # For intraday data, shift by the exact number of minutes
                shift_periods = horizon
            else:
                # For daily data, convert minutes to days (approximately)
                # 1 day = 1440 minutes (24 * 60)
                shift_days = max(1, int(horizon / (24 * 60)))
                shift_periods = shift_days
                logger.info(f"Converting {horizon} minutes to approximately {shift_days} days for target shifting")

            df_features[f'Target_{horizon}'] = df_features['Close'].shift(-shift_periods)

            # Log target statistics
            valid_targets = df_features[f'Target_{horizon}'].notna().sum()
            logger.info(f"Created target variable. Valid targets: {valid_targets}/{len(df_features)}")

            # Check if we have any valid targets after shifting
            if valid_targets == 0:
                logger.error(f"No valid targets for horizon {horizon} after shifting.")
                continue

            # Drop rows with NaN targets
            logger.info("Dropping rows with NaN targets...")
            df_train = df_features.dropna()
            logger.info(f"Data shape after dropping NaN rows: {df_train.shape}")

            # Check if we have enough data after dropping NaN values
            if len(df_train) < sequence_length + 10:  # Need at least sequence_length + some extra for training
                logger.error(f"Not enough data after dropping NaN values. Need at least {sequence_length + 10} rows, but got {len(df_train)}.")
                continue

            # Preprocess data
            logger.info("Preprocessing data...")
            X, y, scaler = preprocess_data(df_train, sequence_length=sequence_length)

            # Check if preprocessing was successful
            if X is None or y is None or scaler is None:
                logger.error(f"Preprocessing failed for horizon {horizon}. Skipping this horizon.")
                continue

            # Check if we have enough data for training
            if len(X) < 10:  # Arbitrary minimum, adjust as needed
                logger.error(f"Not enough data for training horizon {horizon}. Need at least 10 samples, but got {len(X)}.")
                continue

            # Save scaler for later use
            logger.info(f"Saving scaler for horizon {horizon}...")
            saved_files = save_scaler(scaler, symbol, horizon=horizon, model_type=model_type, path=save_path)
            logger.info(f"Saved scaler with {len(saved_files)} naming conventions: {saved_files}")

            # Split data into training and validation sets
            logger.info("Splitting data into training and validation sets...")
            train_size = int(len(X) * 0.8)
            X_train = X[:train_size]
            y_train = y[:train_size]
            logger.info(f"Training data shape: {X_train.shape}")
        except Exception as e:
            logger.error(f"Error during data preparation for horizon {horizon}: {str(e)}")
            continue

        try:
            # Initialize and train model based on model type
            logger.info(f"Initializing model for horizon {horizon}...")

            # Initialize the appropriate model based on model type
            if model_type.lower() == 'transformer' and TRANSFORMER_AVAILABLE:
                # Use Transformer model
                logger.info(f"Using Transformer model for {horizon} days horizon")
                model = StockTransformerModel(
                    sequence_length=sequence_length,
                    prediction_horizon=horizon
                )
            elif model_type.lower() == 'ensemble':
                # Use our robust ensemble model implementation
                logger.info(f"Using robust ensemble model for {horizon} minutes horizon")
                try:
                    # Import the robust ensemble model
                    from models.robust_ensemble import create_robust_ensemble

                    # Create base models
                    from models.sklearn_model import StockPredictionModel as SKLearnModel

                    # Create a set of base models
                    base_models = []
                    base_model_types = ['rf', 'gb', 'lr']  # Simple set of reliable models

                    for base_type in base_model_types:
                        try:
                            # Create the underlying scikit-learn model directly
                            base_model_instance = SKLearnModel(model_type=base_type)._create_model()
                            base_models.append(base_model_instance)
                            logger.info(f"Added {base_type} as base model for ensemble")
                        except Exception as base_error:
                            logger.warning(f"Could not create {base_type} base model: {str(base_error)}")

                    # Create the ensemble model
                    model = create_robust_ensemble(base_models=base_models)
                    logger.info(f"Created robust ensemble with {len(base_models)} base models")

                except Exception as ensemble_error:
                    # Fall back to standard sklearn model if robust ensemble fails
                    logger.warning(f"Error creating robust ensemble: {str(ensemble_error)}. Falling back to RandomForest.")
                    from models.sklearn_model import StockPredictionModel as SKLearnModel
                    model = SKLearnModel(
                        sequence_length=sequence_length,
                        prediction_horizon=horizon,
                        model_type='rf'  # Fall back to RandomForest
                    )
            elif model_type.lower() in ['hybrid', 'rf', 'gb', 'lr', 'svr', 'prophet', 'xgb', 'arima_ml'] and USING_TENSORFLOW:
                # For scikit-learn based models, explicitly use sklearn_model even when TensorFlow is available
                logger.info(f"Using scikit-learn {model_type} model for {horizon} days horizon")
                from models.sklearn_model import StockPredictionModel as SKLearnModel
                model = SKLearnModel(
                    sequence_length=sequence_length,
                    prediction_horizon=horizon,
                    model_type=model_type
                )
            else:
                # Use default model (LSTM or scikit-learn)
                logger.info(f"Using {model_type} model for {horizon} days horizon")
                model = StockPredictionModel(
                    sequence_length=sequence_length,
                    prediction_horizon=horizon,
                    model_type=model_type
                )

            # Train the model
            logger.info(f"Training model for horizon {horizon}...")

            # Check if this is a machine learning model (not deep learning)
            is_ml_model = model_type.lower() in ['rf', 'gb', 'lr', 'svr', 'xgb', 'prophet', 'ensemble', 'hybrid']

            if is_ml_model:
                logger.info(f"Training machine learning model ({model_type}) for horizon {horizon}. This may be quick as ML models don't use epochs like neural networks.")

            # Train the model
            if isinstance(model, StockPredictionModel):
                # For StockPredictionModel which has its own train method
                train_result = model.train(X_train, y_train, save_path=save_path, symbol=symbol, horizon=horizon)
            elif hasattr(model, 'train'):
                # Use train method for custom models
                train_result = model.train(X_train, y_train, save_path=save_path, symbol=symbol, horizon=horizon)
            elif hasattr(model, 'fit'):
                # Direct fit method for standard sklearn models
                train_result = model.fit(X_train, y_train)

            # Log training completion with model-specific details
            if is_ml_model:
                logger.info(f"Machine learning model ({model_type}) training completed for horizon {horizon}")
            else:
                logger.info(f"Deep learning model ({model_type}) training completed for horizon {horizon}")

            # Save model
            logger.info(f"Saving model for horizon {horizon}...")
            model.save(path=save_path, symbol=symbol, horizon=horizon)
            logger.info(f"Model saved for horizon {horizon}")

            # Store model in dictionary
            trained_models[horizon] = model

            logger.info(f"Model for {horizon} days horizon trained and saved successfully")
        except Exception as e:
            logger.error(f"Error training model for horizon {horizon}: {str(e)}")
            # Continue with the next horizon
            continue

    return trained_models

def train_from_csv(csv_path, symbol, horizons=[5, 15, 30, 60], sequence_length=60,
                  model_type='lstm', epochs=100, batch_size=32, save_path='saved_models'):
    """
    Train models from CSV file

    Args:
        csv_path (str): Path to CSV file
        symbol (str): Stock symbol
        horizons (list): List of prediction horizons in minutes
        sequence_length (int): Number of time steps to look back
        model_type (str): Type of model ('lstm', 'bilstm', 'rf', 'gb', 'lr', 'svr', 'prophet')
        epochs (int): Number of epochs
        batch_size (int): Batch size
        save_path (str): Path to save the models

    Returns:
        dict: Dictionary with trained models, or empty dict if training failed
    """
    logger.info(f"Starting training from CSV: {csv_path}")
    logger.info(f"Symbol: {symbol}, Model type: {model_type}")
    logger.info(f"Horizons: {horizons} (in minutes)")

    try:
        # Check if file exists
        if not os.path.exists(csv_path):
            error_msg = f"CSV file not found: {csv_path}"
            logger.error(error_msg)
            return {}

        # Load data
        logger.info(f"Loading data from {csv_path}...")
        df = pd.read_csv(csv_path)
        logger.info(f"Data loaded. Shape: {df.shape}")

        # Check if we have enough data
        if len(df) <= sequence_length:
            error_msg = f"Not enough data for training. Need more than {sequence_length} rows, but got {len(df)} rows."
            logger.error(error_msg)
            return {}

        # Convert Date column to datetime
        logger.info("Converting Date column to datetime...")
        df['Date'] = pd.to_datetime(df['Date'])

        # Check for missing values in key columns
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Date']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            error_msg = f"Missing required columns: {', '.join(missing_columns)}"
            logger.error(error_msg)
            return {}

        # Check for NaN values in key columns
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if col in df.columns and df[col].isna().any():
                # Count NaN values
                nan_count = df[col].isna().sum()
                logger.warning(f"Found {nan_count} NaN values in column '{col}'. Attempting to fill them.")

                # Fill NaN values with forward fill then backward fill
                df[col] = df[col].fillna(method='ffill').fillna(method='bfill')

                # If still have NaNs, raise error
                if df[col].isna().any():
                    error_msg = f"Could not fill all NaN values in column '{col}'. Please check your data."
                    logger.error(error_msg)
                    return {}

        # Train models
        logger.info("Starting model training...")
        trained_models = train_model(
            df, symbol, horizons, sequence_length,
            model_type, epochs, batch_size, save_path
        )

        # Check if any models were trained
        if trained_models and len(trained_models) > 0:
            logger.info(f"Successfully trained {len(trained_models)} models")
            return trained_models
        else:
            logger.warning("No models were trained successfully")
            return {}

    except Exception as e:
        logger.error(f"Error training models from CSV: {str(e)}")
        # Return empty dict instead of raising exception
        return {}
