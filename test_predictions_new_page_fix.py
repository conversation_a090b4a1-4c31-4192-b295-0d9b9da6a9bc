#!/usr/bin/env python3
"""
Test script to verify the Predictions (New) page fixes
Tests that all 9 models are available and working properly
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_predictions_page_model_availability():
    """Test that all 9 models are available in the Predictions (New) page"""
    print("\n🧪 Testing Predictions (New) Page Model Availability")
    print("=" * 60)
    
    try:
        # Import the predictions page functions
        from app.pages.predictions_consolidated import get_trained_models_cache
        
        # Test symbol
        symbol = 'COMI'
        
        print(f"Testing model availability for symbol: {symbol}")
        
        # Get trained models
        trained_models = get_trained_models_cache(symbol)
        
        print(f"\nTrained models found: {list(trained_models.keys())}")
        
        # Expected models (all 9)
        expected_models = ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']
        
        # Check availability
        available_models = []
        missing_models = []
        
        for model in expected_models:
            if model in trained_models and trained_models[model]:
                available_models.append(model)
                horizons = trained_models[model]
                print(f"   ✅ {model.upper()}: Available for horizons {horizons}")
            else:
                missing_models.append(model)
                print(f"   ❌ {model.upper()}: Not available")
        
        # Summary
        print(f"\n📊 Model Availability Summary:")
        print(f"   Available models: {len(available_models)}/9 ({len(available_models)/9*100:.1f}%)")
        print(f"   Missing models: {missing_models}")
        
        # Success criteria
        if len(available_models) >= 7:  # At least 7/9 models should be available
            print(f"   ✅ SUCCESS: Most models are available!")
            return True
        elif len(available_models) >= 5:
            print(f"   ⚠️ PARTIAL: Some models available, but could be better")
            return True
        else:
            print(f"   ❌ FAILURE: Too few models available")
            return False
            
    except Exception as e:
        print(f"❌ Error testing model availability: {str(e)}")
        return False

def test_model_lists_in_code():
    """Test that all model lists in the code include all 9 models"""
    print("\n🔍 Testing Model Lists in Predictions (New) Page Code")
    print("=" * 60)
    
    try:
        # Read the predictions consolidated file
        with open('app/pages/predictions_consolidated.py', 'r') as f:
            content = f.read()
        
        # Expected models
        expected_models = ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']
        
        # Find all model lists in the code
        import re
        
        # Pattern to find model lists
        pattern = r"model_types\s*=\s*\[(.*?)\]|models\s*=\s*\[(.*?)\]|for\s+\w+\s+in\s+\[(.*?)\]"
        matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
        
        print(f"Found {len(matches)} model list patterns in the code")
        
        issues_found = []
        lists_checked = 0
        
        for i, match in enumerate(matches):
            # Get the actual list content (one of the three groups will be non-empty)
            list_content = match[0] or match[1] or match[2]
            
            if list_content and any(model in list_content for model in ['rf', 'ensemble', 'lstm']):
                lists_checked += 1
                print(f"\n   List {lists_checked}: {list_content.strip()}")
                
                # Check if all expected models are present
                missing_in_list = []
                for model in expected_models:
                    if f"'{model}'" not in list_content and f'"{model}"' not in list_content:
                        missing_in_list.append(model)
                
                if missing_in_list:
                    issues_found.append(f"List {lists_checked} missing: {missing_in_list}")
                    print(f"      ❌ Missing: {missing_in_list}")
                else:
                    print(f"      ✅ Complete: All 9 models present")
        
        # Summary
        print(f"\n📊 Code Analysis Summary:")
        print(f"   Model lists checked: {lists_checked}")
        print(f"   Issues found: {len(issues_found)}")
        
        if issues_found:
            print(f"   ❌ Issues:")
            for issue in issues_found:
                print(f"      - {issue}")
            return False
        else:
            print(f"   ✅ All model lists include all 9 models!")
            return True
            
    except Exception as e:
        print(f"❌ Error analyzing code: {str(e)}")
        return False

def test_model_predictions_functionality():
    """Test that the models can actually make predictions"""
    print("\n🔄 Testing Model Prediction Functionality")
    print("=" * 60)
    
    try:
        from models.predict import predict_future_prices
        
        # Create test data
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        
        initial_price = 89.98
        returns = np.random.normal(0.001, 0.02, len(dates))
        prices = [initial_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        test_data = pd.DataFrame({
            'Date': dates,
            'Open': [p * 0.999 for p in prices],
            'High': [p * 1.002 for p in prices],
            'Low': [p * 0.998 for p in prices],
            'Close': prices,
            'Volume': np.random.randint(50000, 200000, len(dates))
        })
        
        current_price = test_data['Close'].iloc[-1]
        print(f"Test data created. Current price: {current_price:.2f} EGP")
        
        # Test all models
        models_to_test = ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']
        horizons = [30]
        symbol = 'COMI'
        
        working_models = []
        broken_models = []
        mock_models = []
        
        print(f"\nTesting prediction functionality:")
        
        for model_type in models_to_test:
            try:
                predictions = predict_future_prices(
                    test_data, 
                    symbol, 
                    horizons=horizons,
                    model_type=model_type,
                    models_path='saved_models'
                )
                
                if horizons[0] in predictions and predictions[horizons[0]] is not None:
                    predicted_price = predictions[horizons[0]]
                    price_change_pct = ((predicted_price - current_price) / current_price) * 100
                    
                    if abs(price_change_pct) < 0.01:
                        mock_models.append(model_type)
                        status = "⚠️ MOCK"
                    else:
                        working_models.append(model_type)
                        status = "✅ WORKING"
                    
                    print(f"   {status} {model_type.upper()}: {current_price:.2f} -> {predicted_price:.2f} EGP ({price_change_pct:+.2f}%)")
                    
                else:
                    broken_models.append(model_type)
                    print(f"   ❌ BROKEN {model_type.upper()}: No prediction returned")
                    
            except Exception as e:
                broken_models.append(model_type)
                print(f"   ❌ ERROR {model_type.upper()}: {str(e)}")
        
        # Summary
        print(f"\n📊 Prediction Functionality Summary:")
        print(f"   Working models: {len(working_models)}/9 ({len(working_models)/9*100:.1f}%)")
        print(f"   Mock data models: {len(mock_models)}/9 ({len(mock_models)/9*100:.1f}%)")
        print(f"   Broken models: {len(broken_models)}/9 ({len(broken_models)/9*100:.1f}%)")
        
        print(f"\n   ✅ Working: {working_models}")
        print(f"   ⚠️ Mock: {mock_models}")
        print(f"   ❌ Broken: {broken_models}")
        
        # Success criteria
        if len(working_models) >= 7:
            print(f"\n🎉 EXCELLENT: Most models working properly!")
            return True
        elif len(working_models) >= 5:
            print(f"\n✅ GOOD: Majority of models working!")
            return True
        else:
            print(f"\n❌ POOR: Too many models not working properly!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing prediction functionality: {str(e)}")
        return False

def main():
    """Run all tests for the Predictions (New) page fixes"""
    print("🚀 Testing Predictions (New) Page Fixes")
    print("=" * 70)
    
    # Run tests
    test1_result = test_predictions_page_model_availability()
    test2_result = test_model_lists_in_code()
    test3_result = test_model_predictions_functionality()
    
    # Final summary
    print("\n" + "=" * 70)
    print("📊 PREDICTIONS (NEW) PAGE FIX RESULTS")
    print("=" * 70)
    
    tests_passed = sum([test1_result, test2_result, test3_result])
    
    print(f"Tests Results:")
    print(f"   ✅ Model Availability: {'PASS' if test1_result else 'FAIL'}")
    print(f"   ✅ Code Model Lists: {'PASS' if test2_result else 'FAIL'}")
    print(f"   ✅ Prediction Functionality: {'PASS' if test3_result else 'FAIL'}")
    
    print(f"\nOverall: {tests_passed}/3 tests passed")
    
    if tests_passed == 3:
        print("\n🎉 COMPLETE SUCCESS!")
        print("   ✅ All 9 models are available in Predictions (New) page")
        print("   ✅ All model lists in code are updated")
        print("   ✅ Model predictions are working properly")
        print("   ✅ Analysis & Comparison will now be comprehensive")
        return True
    elif tests_passed >= 2:
        print("\n⚠️ MOSTLY SUCCESSFUL!")
        print("   ✅ Major improvements made")
        print("   ⚠️ Some minor issues may remain")
        return True
    else:
        print("\n❌ NEEDS MORE WORK!")
        print("   ❌ Significant issues still present")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
