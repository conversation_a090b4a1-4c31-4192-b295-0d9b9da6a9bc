#!/usr/bin/env python3
"""
Test script for the Enhanced Market Overview Dashboard
Tests real-time data integration, expanded stock coverage, and backtesting features
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def test_enhanced_dashboard_import():
    """Test if we can import the enhanced dashboard"""
    try:
        from app.pages.market_overview_dashboard import (
            show_market_overview_dashboard, 
            EGXMarketDashboard,
            EGX_STOCKS,
            check_api_server_status,
            fetch_live_data,
            process_live_data_to_dataframe
        )
        print("✅ Successfully imported Enhanced Market Overview Dashboard")
        
        # Test the enhanced dashboard class
        dashboard = EGXMarketDashboard()
        print("✅ Successfully created Enhanced EGXMarketDashboard instance")
        
        # Test expanded EGX stocks configuration
        print(f"✅ Found {len(EGX_STOCKS)} EGX stocks configured (expanded from 8 to {len(EGX_STOCKS)}):")
        for symbol, name in EGX_STOCKS.items():
            print(f"   {symbol}: {name}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_api_integration():
    """Test API server integration"""
    print("\n🧪 Testing API Server Integration")
    print("=" * 40)
    
    try:
        from app.pages.market_overview_dashboard import check_api_server_status, fetch_live_data
        
        # Test API status check
        api_status = check_api_server_status()
        print(f"📡 API Server Status: {'🟢 Online' if api_status else '🔴 Offline'}")
        
        if api_status:
            # Test live data fetching
            print("🔄 Testing live data fetch for COMI...")
            live_data = fetch_live_data('COMI', ['1D'])
            
            if live_data:
                print("✅ Successfully fetched live data")
                print(f"   Data structure: {type(live_data)}")
                if isinstance(live_data, list) and live_data:
                    print(f"   Sample data keys: {list(live_data[0].keys()) if live_data[0] else 'No keys'}")
            else:
                print("⚠️ No live data returned (may be normal if market is closed)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing API integration: {str(e)}")
        return False

def test_backtesting_functionality():
    """Test backtesting functionality"""
    print("\n🧪 Testing Backtesting Functionality")
    print("=" * 40)
    
    try:
        from app.pages.market_overview_dashboard import EGXMarketDashboard
        
        # Create test data
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)  # For reproducible results
        
        # Generate realistic stock price data
        initial_price = 100
        returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns
        prices = [initial_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        test_data = pd.DataFrame({
            'Date': dates,
            'Open': [p * 0.999 for p in prices],
            'High': [p * 1.002 for p in prices],
            'Low': [p * 0.998 for p in prices],
            'Close': prices,
            'Volume': np.random.randint(50000, 200000, len(dates))
        })
        
        print(f"✅ Created test dataset with {len(test_data)} data points")
        
        # Test dashboard initialization
        dashboard = EGXMarketDashboard()
        
        # Test data processing
        processed_data = dashboard.process_egx_data('COMI', test_data)
        
        if not processed_data.empty:
            print("✅ Successfully processed test data")
            print(f"   Processed data shape: {processed_data.shape}")
            print(f"   Available indicators: {[col for col in processed_data.columns if col not in ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']]}")
        else:
            print("❌ Failed to process test data")
            return False
        
        # Test backtesting analysis
        print("🔄 Testing backtesting analysis...")
        backtest_results = dashboard.run_backtesting_analysis('COMI', processed_data, 'rf', 10)
        
        if backtest_results.get('success'):
            print("✅ Backtesting analysis completed successfully")
            print(f"   Accuracy: {backtest_results.get('accuracy', 0):.1f}%")
            print(f"   Average Error: {backtest_results.get('avg_error', 0):.2f}%")
            print(f"   Total Predictions: {backtest_results.get('total_predictions', 0)}")
        elif backtest_results.get('error'):
            print(f"⚠️ Backtesting returned error: {backtest_results['error']}")
            print("   This may be normal if models are not trained yet")
        else:
            print("❌ Backtesting failed without specific error")
            return False
        
        # Test performance report generation
        print("🔄 Testing performance report generation...")
        performance_report = dashboard.generate_performance_report('COMI', backtest_results)
        
        if performance_report and len(performance_report) > 100:
            print("✅ Performance report generated successfully")
            print(f"   Report length: {len(performance_report)} characters")
        else:
            print("❌ Failed to generate performance report")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing backtesting functionality: {str(e)}")
        return False

def test_expanded_stock_coverage():
    """Test expanded stock coverage"""
    print("\n🧪 Testing Expanded Stock Coverage")
    print("=" * 40)
    
    try:
        from app.pages.market_overview_dashboard import EGX_STOCKS
        
        # Expected categories
        expected_categories = {
            'Banking': ['COMI', 'ABUK', 'EGAL', 'UBEE'],
            'Technology': ['FWRY', 'OBRI'],
            'Real Estate': ['PHDC', 'MTFG'],
            'Industrial': ['SWDY', 'EFID', 'OCDI'],
            'Financial Services': ['BTFH', 'EFIC'],
            'Agriculture': ['GGRN', 'UTOP']
        }
        
        total_expected = sum(len(stocks) for stocks in expected_categories.values())
        actual_count = len(EGX_STOCKS)
        
        print(f"✅ Stock coverage expanded to {actual_count} stocks")
        print(f"   Expected: {total_expected}, Actual: {actual_count}")
        
        # Check coverage by category
        for category, stocks in expected_categories.items():
            found_stocks = [stock for stock in stocks if stock in EGX_STOCKS]
            print(f"   {category}: {len(found_stocks)}/{len(stocks)} stocks found")
            
            if len(found_stocks) < len(stocks):
                missing = [stock for stock in stocks if stock not in EGX_STOCKS]
                print(f"     Missing: {missing}")
        
        # Verify all stocks have proper names
        unnamed_stocks = [symbol for symbol, name in EGX_STOCKS.items() if not name or len(name) < 5]
        if unnamed_stocks:
            print(f"⚠️ Stocks with short/missing names: {unnamed_stocks}")
        else:
            print("✅ All stocks have proper company names")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing expanded stock coverage: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Enhanced Market Overview Dashboard")
    print("=" * 50)
    
    tests = [
        test_enhanced_dashboard_import,
        test_expanded_stock_coverage,
        test_api_integration,
        test_backtesting_functionality
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {str(e)}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced Market Overview Dashboard is ready.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
