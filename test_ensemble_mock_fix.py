#!/usr/bin/env python3
"""
Test script to verify the ENSEMBLE mock data fix
Tests that RF, GB, and LR models now generate realistic predictions instead of mock data
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def create_test_data():
    """Create test stock data"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)  # For reproducible results
    
    # Generate realistic stock price data
    initial_price = 89.98
    returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    test_data = pd.DataFrame({
        'Date': dates,
        'Open': [p * 0.999 for p in prices],
        'High': [p * 1.002 for p in prices],
        'Low': [p * 0.998 for p in prices],
        'Close': prices,
        'Volume': np.random.randint(50000, 200000, len(dates))
    })
    
    return test_data

def test_individual_model_predictions():
    """Test individual model predictions to see if they're still returning mock data"""
    print("\n🧪 Testing Individual Model Predictions")
    print("=" * 50)
    
    try:
        from models.predict import predict_future_prices
        
        # Create test data
        test_data = create_test_data()
        print(f"✅ Created test data with {len(test_data)} data points")
        
        # Test each model individually
        models_to_test = ['rf', 'gb', 'lr', 'lstm', 'ensemble']
        horizons = [30]  # 30 minutes
        symbol = 'COMI'
        
        results = {}
        current_price = test_data['Close'].iloc[-1]
        
        print(f"Current price: {current_price:.2f} EGP")
        print("\nTesting individual models:")
        
        for model_type in models_to_test:
            try:
                print(f"\n🔄 Testing {model_type.upper()} model...")
                
                predictions = predict_future_prices(
                    test_data, 
                    symbol, 
                    horizons=horizons,
                    model_type=model_type,
                    models_path='saved_models'
                )
                
                if horizons[0] in predictions and predictions[horizons[0]] is not None:
                    predicted_price = predictions[horizons[0]]
                    price_change = predicted_price - current_price
                    price_change_pct = (price_change / current_price) * 100
                    
                    results[model_type] = {
                        'predicted_price': predicted_price,
                        'price_change_pct': price_change_pct,
                        'is_mock': abs(price_change_pct) < 0.01  # Less than 0.01% change
                    }
                    
                    status = "⚠️ MOCK" if results[model_type]['is_mock'] else "✅ REAL"
                    print(f"   {status} {model_type.upper()}: {current_price:.2f} -> {predicted_price:.2f} EGP ({price_change_pct:+.2f}%)")
                    
                else:
                    print(f"   ❌ {model_type.upper()}: No prediction returned")
                    results[model_type] = {'predicted_price': None, 'price_change_pct': 0, 'is_mock': True}
                    
            except Exception as e:
                print(f"   ❌ {model_type.upper()}: Error - {str(e)}")
                results[model_type] = {'predicted_price': None, 'price_change_pct': 0, 'is_mock': True}
        
        return results
        
    except Exception as e:
        print(f"❌ Error testing individual models: {str(e)}")
        return {}

def analyze_ensemble_composition(results):
    """Analyze how the ensemble prediction is composed"""
    print("\n📊 Analyzing ENSEMBLE Composition")
    print("=" * 50)
    
    if not results:
        print("❌ No results to analyze")
        return False
    
    # Count mock vs real predictions
    mock_models = [model for model, data in results.items() 
                   if model != 'ensemble' and data['is_mock']]
    real_models = [model for model, data in results.items() 
                   if model != 'ensemble' and not data['is_mock']]
    
    print(f"Models with MOCK data: {mock_models}")
    print(f"Models with REAL data: {real_models}")
    
    # Check if ensemble is affected
    if 'ensemble' in results:
        ensemble_data = results['ensemble']
        ensemble_is_mock = ensemble_data['is_mock']
        
        print(f"\nENSEMBLE prediction:")
        print(f"   Price change: {ensemble_data['price_change_pct']:+.2f}%")
        print(f"   Status: {'⚠️ MOCK-AFFECTED' if ensemble_is_mock else '✅ REALISTIC'}")
        
        # Analysis
        mock_ratio = len(mock_models) / (len(mock_models) + len(real_models)) if (mock_models or real_models) else 0
        
        print(f"\n📈 Analysis:")
        print(f"   Mock models ratio: {mock_ratio:.1%}")
        print(f"   Real models ratio: {(1-mock_ratio):.1%}")
        
        if mock_ratio > 0.5:
            print("   ⚠️ ENSEMBLE is likely compromised by mock data")
            return False
        elif mock_ratio > 0:
            print("   ⚠️ ENSEMBLE is partially affected by mock data")
            return mock_ratio < 0.3  # Pass if less than 30% mock
        else:
            print("   ✅ ENSEMBLE is based on real predictions")
            return True
    else:
        print("❌ No ensemble results to analyze")
        return False

def test_prediction_variance():
    """Test that predictions show reasonable variance (not all identical)"""
    print("\n🎲 Testing Prediction Variance")
    print("=" * 50)
    
    try:
        from models.predict import predict_future_prices
        
        test_data = create_test_data()
        current_price = test_data['Close'].iloc[-1]
        
        # Test multiple predictions from the same model to check variance
        model_type = 'rf'  # Test RF since it was problematic
        horizons = [30]
        symbol = 'COMI'
        
        predictions = []
        
        print(f"Testing {model_type.upper()} model variance (5 predictions):")
        
        for i in range(5):
            try:
                pred_result = predict_future_prices(
                    test_data, 
                    symbol, 
                    horizons=horizons,
                    model_type=model_type,
                    models_path='saved_models'
                )
                
                if horizons[0] in pred_result and pred_result[horizons[0]] is not None:
                    predicted_price = pred_result[horizons[0]]
                    price_change_pct = ((predicted_price - current_price) / current_price) * 100
                    predictions.append(predicted_price)
                    print(f"   Prediction {i+1}: {current_price:.2f} -> {predicted_price:.2f} EGP ({price_change_pct:+.2f}%)")
                else:
                    print(f"   Prediction {i+1}: No result")
                    
            except Exception as e:
                print(f"   Prediction {i+1}: Error - {str(e)}")
        
        if len(predictions) >= 2:
            # Calculate variance
            pred_array = np.array(predictions)
            variance = np.var(pred_array)
            std_dev = np.std(pred_array)
            
            print(f"\n📊 Variance Analysis:")
            print(f"   Predictions: {len(predictions)}")
            print(f"   Mean: {np.mean(pred_array):.2f} EGP")
            print(f"   Std Dev: {std_dev:.4f} EGP")
            print(f"   Variance: {variance:.6f}")
            
            # Check if all predictions are identical (mock behavior)
            all_identical = len(set(predictions)) == 1
            
            if all_identical:
                print("   ❌ All predictions identical - MOCK DATA DETECTED")
                return False
            else:
                print("   ✅ Predictions show variance - REAL DATA")
                return True
        else:
            print("   ❌ Insufficient predictions to analyze variance")
            return False
            
    except Exception as e:
        print(f"❌ Error testing prediction variance: {str(e)}")
        return False

def main():
    """Run all tests for the ensemble mock fix"""
    print("🚀 Testing ENSEMBLE Mock Data Fix")
    print("=" * 60)
    
    tests = [
        test_individual_model_predictions,
        test_prediction_variance
    ]
    
    # Run individual model tests
    results = test_individual_model_predictions()
    
    # Analyze ensemble composition
    ensemble_ok = analyze_ensemble_composition(results)
    
    # Test variance
    variance_ok = test_prediction_variance()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ENSEMBLE Mock Fix Test Results")
    print("=" * 60)
    
    if results:
        mock_count = sum(1 for data in results.values() if data['is_mock'])
        total_count = len(results)
        
        print(f"Individual Models:")
        for model, data in results.items():
            status = "⚠️ MOCK" if data['is_mock'] else "✅ REAL"
            if data['predicted_price'] is not None:
                print(f"   {model.upper()}: {status} ({data['price_change_pct']:+.2f}%)")
            else:
                print(f"   {model.upper()}: ❌ FAILED")
        
        print(f"\nSummary:")
        print(f"   Mock models: {mock_count}/{total_count}")
        print(f"   Real models: {total_count - mock_count}/{total_count}")
        print(f"   Ensemble quality: {'✅ GOOD' if ensemble_ok else '⚠️ COMPROMISED'}")
        print(f"   Prediction variance: {'✅ GOOD' if variance_ok else '❌ POOR'}")
        
        # Overall assessment
        if mock_count <= 1 and ensemble_ok and variance_ok:
            print("\n🎉 ENSEMBLE Mock Fix: SUCCESS!")
            print("   Models are generating realistic, varied predictions")
            return True
        elif mock_count <= 2 and variance_ok:
            print("\n⚠️ ENSEMBLE Mock Fix: PARTIAL SUCCESS")
            print("   Some improvement, but still some mock data present")
            return True
        else:
            print("\n❌ ENSEMBLE Mock Fix: NEEDS MORE WORK")
            print("   Too many models still using mock data")
            return False
    else:
        print("❌ No test results available")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
