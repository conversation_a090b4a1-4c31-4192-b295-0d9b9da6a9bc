# Can be enabled at the command line with --debug-xxx.

debug_disposal_code = 0
debug_temp_alloc = 0
debug_coercion = 0

# Write comments into the C code that show where temporary variables
# are allocated and released.
debug_temp_code_comments = 0

# Write a call trace of the code generation phase into the C code.
debug_trace_code_generation = 0

# Do not replace exceptions with user-friendly error messages.
debug_no_exception_intercept = 0

# Print a message each time a new stage in the pipeline is entered.
debug_verbose_pipeline = 0

# Print a message each time an Entry type is assigned.
debug_verbose_entry_types = False

# Raise an exception when an error is encountered.
debug_exception_on_error = 0
