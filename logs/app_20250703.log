2025-07-03 12:46:46,624 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-03 12:46:51,164 - app - INFO - Memory management utilities loaded
2025-07-03 12:46:51,166 - app - INFO - Error handling utilities loaded
2025-07-03 12:46:51,169 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-03 12:46:51,172 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-03 12:46:51,173 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-03 12:46:51,173 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-03 12:46:51,183 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-03 12:46:51,183 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-03 12:46:51,183 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-03 12:46:51,183 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-03 12:46:51,183 - app - INFO - Applied NumPy fix
2025-07-03 12:46:51,185 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-03 12:46:51,185 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-03 12:46:51,185 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-03 12:46:51,185 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-03 12:46:51,188 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-03 12:46:51,188 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-03 12:46:51,188 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-03 12:46:51,188 - app - INFO - Applied NumPy BitGenerator fix
2025-07-03 12:47:10,838 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-07-03 12:47:10,839 - app - INFO - Applied TensorFlow fix
2025-07-03 12:47:10,843 - app.config - INFO - Configuration initialized
2025-07-03 12:47:10,851 - models.train - INFO - TensorFlow version: 2.9.1
2025-07-03 12:47:11,159 - models.train - INFO - TensorFlow test successful
2025-07-03 12:47:15,930 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-07-03 12:47:15,930 - models.train - INFO - Transformer model is available
2025-07-03 12:47:15,930 - models.train - INFO - Using TensorFlow-based models
2025-07-03 12:47:15,943 - models.predict - INFO - Transformer model is available for predictions
2025-07-03 12:47:15,944 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-03 12:47:15,949 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-03 12:47:17,795 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-03 12:47:17,797 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-03 12:47:17,797 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-03 12:47:17,797 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-03 12:47:17,797 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-03 12:47:17,798 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-03 12:47:17,798 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-03 12:47:17,798 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-03 12:47:17,798 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-03 12:47:17,798 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-03 12:47:18,154 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-03 12:47:18,162 - app - INFO - Using TensorFlow-based LSTM model
2025-07-03 12:47:18,745 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-03 12:47:21,258 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-03 12:47:21,571 - app.utils.session_state - INFO - Initializing session state
2025-07-03 12:47:21,572 - app.utils.session_state - INFO - Session state initialized
2025-07-03 12:47:22,896 - app - INFO - Found 14 stock files in data/stocks
2025-07-03 12:47:22,921 - app.utils.memory_management - INFO - Memory before cleanup: 429.71 MB
2025-07-03 12:47:23,168 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-03 12:47:23,169 - app.utils.memory_management - INFO - Memory after cleanup: 429.72 MB (freed -0.01 MB)
2025-07-03 13:20:03,568 - app - INFO - Cleaning up resources...
2025-07-03 13:20:03,570 - app.utils.memory_management - INFO - Memory before cleanup: 176.02 MB
2025-07-03 13:20:03,993 - app.utils.memory_management - INFO - Garbage collection: collected 93 objects
2025-07-03 13:20:03,994 - app.utils.memory_management - INFO - Memory after cleanup: 367.26 MB (freed -191.25 MB)
2025-07-03 13:20:03,994 - app - INFO - Application shutdown complete
