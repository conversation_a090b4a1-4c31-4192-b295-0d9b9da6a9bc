2025-06-06 21:19:07,526 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-06 21:19:14,008 - app - INFO - Memory management utilities loaded
2025-06-06 21:19:14,022 - app - INFO - Error handling utilities loaded
2025-06-06 21:19:14,029 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 21:19:14,033 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 21:19:14,033 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 21:19:14,034 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 21:19:14,043 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-06 21:19:14,054 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-06 21:19:14,056 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-06 21:19:14,056 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-06 21:19:14,056 - app - INFO - Applied NumPy fix
2025-06-06 21:19:14,069 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 21:19:14,069 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 21:19:14,069 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 21:19:14,073 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-06 21:19:14,073 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 21:19:14,073 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 21:19:14,073 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 21:19:14,073 - app - INFO - Applied NumPy BitGenerator fix
2025-06-06 21:19:40,711 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-06 21:19:40,713 - app - INFO - Applied TensorFlow fix
2025-06-06 21:19:40,738 - app.config - INFO - Configuration initialized
2025-06-06 21:19:40,754 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-06 21:19:40,765 - models.train - INFO - TensorFlow test successful
2025-06-06 21:19:46,158 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-06 21:19:46,159 - models.train - INFO - Transformer model is available
2025-06-06 21:19:46,159 - models.train - INFO - Using TensorFlow-based models
2025-06-06 21:19:46,168 - models.predict - INFO - Transformer model is available for predictions
2025-06-06 21:19:46,169 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-06 21:19:46,202 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-06 21:19:48,150 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 21:19:48,151 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 21:19:48,151 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 21:19:48,151 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 21:19:48,151 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 21:19:48,152 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-06 21:19:48,152 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-06 21:19:48,152 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 21:19:48,153 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 21:19:48,153 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 21:19:48,565 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-06 21:19:48,606 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:19:49,594 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-06 21:19:52,836 - app.utils.session_state - INFO - Initializing session state
2025-06-06 21:19:52,837 - app.utils.session_state - INFO - Session state initialized
2025-06-06 21:19:54,486 - app - INFO - Found 8 stock files in data/stocks
2025-06-06 21:19:54,508 - app.utils.memory_management - INFO - Memory before cleanup: 425.31 MB
2025-06-06 21:19:54,687 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 21:19:54,687 - app.utils.memory_management - INFO - Memory after cleanup: 425.71 MB (freed -0.40 MB)
2025-06-06 21:20:01,670 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:20:01,862 - app.utils.memory_management - INFO - Memory before cleanup: 429.64 MB
2025-06-06 21:20:02,194 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 21:20:02,196 - app.utils.memory_management - INFO - Memory after cleanup: 429.64 MB (freed 0.00 MB)
2025-06-06 21:20:07,175 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:20:07,214 - app.utils.memory_management - INFO - Memory before cleanup: 430.54 MB
2025-06-06 21:20:07,444 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-06 21:20:07,446 - app.utils.memory_management - INFO - Memory after cleanup: 430.58 MB (freed -0.04 MB)
2025-06-06 21:20:08,666 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:20:28,917 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-06 21:20:28,919 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-06 21:20:28,919 - app.utils.memory_management - INFO - Memory before cleanup: 431.93 MB
2025-06-06 21:20:29,205 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-06 21:20:29,210 - app.utils.memory_management - INFO - Memory after cleanup: 431.93 MB (freed 0.00 MB)
2025-06-06 21:20:29,210 - app.utils.memory_management - INFO - Memory before cleanup: 431.93 MB
2025-06-06 21:20:29,390 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-06 21:20:29,390 - app.utils.memory_management - INFO - Memory after cleanup: 431.93 MB (freed 0.00 MB)
2025-06-06 21:20:41,810 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:20:41,867 - app.utils.memory_management - INFO - Memory before cleanup: 432.59 MB
2025-06-06 21:20:42,122 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-06 21:20:42,123 - app.utils.memory_management - INFO - Memory after cleanup: 432.59 MB (freed 0.00 MB)
2025-06-06 21:20:56,510 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:20:56,552 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-06 21:20:56,636 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-06 21:20:56,637 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-06 21:20:56,640 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-06 21:20:57,084 - app.components.advanced_smc_features - INFO - Found 12 high swing points with lookback 10
2025-06-06 21:20:57,174 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-06 21:20:57,174 - app.components.advanced_smc_features - INFO - Found 12 swing highs and 8 swing lows
2025-06-06 21:20:57,183 - app.components.advanced_smc_features - INFO - Detected 11 BOS events
2025-06-06 21:20:57,647 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-06 21:20:57,704 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-06 21:20:57,725 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-06 21:20:57,734 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-06 21:20:58,127 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-06 21:20:58,227 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-06 21:20:58,227 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-06 21:20:58,238 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-06 21:20:58,630 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-06 21:20:58,690 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-06 21:20:58,690 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-06 21:20:58,710 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-06 21:20:58,759 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-06 21:20:58,759 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-06 21:21:01,127 - app.utils.memory_management - INFO - Memory before cleanup: 449.07 MB
2025-06-06 21:21:01,320 - app.utils.memory_management - INFO - Garbage collection: collected 1097 objects
2025-06-06 21:21:01,322 - app.utils.memory_management - INFO - Memory after cleanup: 449.12 MB (freed -0.06 MB)
2025-06-06 21:21:33,053 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:21:33,110 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 21:21:33,129 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 21:21:33,131 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:21:33,133 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:21:33,134 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:21:33,145 - app.utils.memory_management - INFO - Memory before cleanup: 448.18 MB
2025-06-06 21:21:33,508 - app.utils.memory_management - INFO - Garbage collection: collected 267 objects
2025-06-06 21:21:33,510 - app.utils.memory_management - INFO - Memory after cleanup: 447.42 MB (freed 0.75 MB)
2025-06-06 21:21:33,747 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:21:33,894 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 21:21:33,965 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 21:21:33,968 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:21:33,970 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:21:33,976 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:21:34,141 - app.utils.memory_management - INFO - Memory before cleanup: 448.15 MB
2025-06-06 21:21:34,403 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 21:21:34,404 - app.utils.memory_management - INFO - Memory after cleanup: 448.15 MB (freed -0.00 MB)
2025-06-06 21:21:40,056 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:21:40,218 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.04 seconds
2025-06-06 21:21:40,220 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:21:40,223 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:21:40,225 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:21:40,370 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 21:21:40,439 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 21:21:40,446 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:21:40,450 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:21:40,454 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:21:40,701 - app.utils.memory_management - INFO - Memory before cleanup: 447.75 MB
2025-06-06 21:21:40,933 - app.utils.memory_management - INFO - Garbage collection: collected 296 objects
2025-06-06 21:21:40,933 - app.utils.memory_management - INFO - Memory after cleanup: 447.75 MB (freed 0.00 MB)
2025-06-06 21:22:55,443 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:22:55,555 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 21:22:55,603 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 21:22:55,607 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:22:55,608 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:22:55,609 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:22:55,748 - app.utils.memory_management - INFO - Memory before cleanup: 447.77 MB
2025-06-06 21:22:56,003 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-06 21:22:56,004 - app.utils.memory_management - INFO - Memory after cleanup: 447.77 MB (freed 0.00 MB)
2025-06-06 21:23:21,279 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:23:21,432 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 21:23:21,434 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:23:21,436 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:23:21,445 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:23:21,628 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 21:23:21,717 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 21:23:21,720 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:23:21,723 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:23:21,725 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:23:21,886 - app.utils.memory_management - INFO - Memory before cleanup: 447.82 MB
2025-06-06 21:23:22,146 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 21:23:22,153 - app.utils.memory_management - INFO - Memory after cleanup: 447.80 MB (freed 0.02 MB)
2025-06-06 21:23:33,091 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:23:33,243 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 21:23:33,348 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 21:23:33,350 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:23:33,352 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:23:33,357 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:23:33,618 - app.utils.memory_management - INFO - Memory before cleanup: 447.82 MB
2025-06-06 21:23:33,852 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-06 21:23:33,869 - app.utils.memory_management - INFO - Memory after cleanup: 447.82 MB (freed 0.00 MB)
2025-06-06 21:23:36,330 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 21:23:36,401 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 21:23:36,403 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:23:36,403 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:23:36,403 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:23:36,452 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 21:23:36,508 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 21:23:36,509 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 21:23:36,510 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 21:23:36,510 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 21:23:36,575 - app.utils.memory_management - INFO - Memory before cleanup: 447.84 MB
2025-06-06 21:23:36,791 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-06 21:23:36,792 - app.utils.memory_management - INFO - Memory after cleanup: 447.84 MB (freed 0.00 MB)
2025-06-06 22:42:11,026 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-06 22:42:14,123 - app - INFO - Memory management utilities loaded
2025-06-06 22:42:14,123 - app - INFO - Error handling utilities loaded
2025-06-06 22:42:14,123 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 22:42:14,123 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 22:42:14,123 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 22:42:14,123 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 22:42:14,129 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-06 22:42:14,129 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-06 22:42:14,129 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-06 22:42:14,129 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-06 22:42:14,131 - app - INFO - Applied NumPy fix
2025-06-06 22:42:14,131 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 22:42:14,131 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 22:42:14,131 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 22:42:14,133 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-06 22:42:14,133 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 22:42:14,133 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 22:42:14,133 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 22:42:14,133 - app - INFO - Applied NumPy BitGenerator fix
2025-06-06 22:42:29,776 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-06 22:42:29,778 - app - INFO - Applied TensorFlow fix
2025-06-06 22:42:29,781 - app.config - INFO - Configuration initialized
2025-06-06 22:42:29,787 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-06 22:42:29,798 - models.train - INFO - TensorFlow test successful
2025-06-06 22:42:32,492 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-06 22:42:32,493 - models.train - INFO - Transformer model is available
2025-06-06 22:42:32,493 - models.train - INFO - Using TensorFlow-based models
2025-06-06 22:42:32,494 - models.predict - INFO - Transformer model is available for predictions
2025-06-06 22:42:32,495 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-06 22:42:32,497 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-06 22:42:32,976 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 22:42:32,977 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 22:42:32,977 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 22:42:32,978 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 22:42:32,978 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 22:42:32,978 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-06 22:42:32,979 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-06 22:42:32,979 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 22:42:32,979 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 22:42:32,979 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 22:42:33,088 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-06 22:42:33,091 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 22:42:33,465 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-06 22:42:34,399 - app.utils.session_state - INFO - Initializing session state
2025-06-06 22:42:34,400 - app.utils.session_state - INFO - Session state initialized
2025-06-06 22:42:35,571 - app - INFO - Found 8 stock files in data/stocks
2025-06-06 22:42:35,587 - app.utils.memory_management - INFO - Memory before cleanup: 425.69 MB
2025-06-06 22:42:35,783 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 22:42:35,784 - app.utils.memory_management - INFO - Memory after cleanup: 425.70 MB (freed -0.00 MB)
2025-06-06 22:43:01,952 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 22:43:01,990 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 22:43:02,104 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.11 seconds
2025-06-06 22:43:02,105 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 22:43:02,106 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 22:43:02,106 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 22:43:02,108 - app.utils.memory_management - INFO - Memory before cleanup: 54.06 MB
2025-06-06 22:43:02,415 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 22:43:02,417 - app.utils.memory_management - INFO - Memory after cleanup: 242.30 MB (freed -188.24 MB)
2025-06-06 22:43:02,644 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 22:43:02,730 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 22:43:02,773 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 22:43:02,774 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 22:43:02,774 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 22:43:02,774 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 22:43:02,837 - app.utils.memory_management - INFO - Memory before cleanup: 246.21 MB
2025-06-06 22:43:03,037 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 22:43:03,038 - app.utils.memory_management - INFO - Memory after cleanup: 246.39 MB (freed -0.17 MB)
2025-06-06 22:43:08,836 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 22:43:08,897 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 22:43:08,898 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 22:43:08,898 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 22:43:08,899 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 22:43:08,978 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 22:43:09,012 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 22:43:09,013 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 22:43:09,014 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 22:43:09,015 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 22:43:09,081 - app.utils.memory_management - INFO - Memory before cleanup: 250.02 MB
2025-06-06 22:43:09,282 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 22:43:09,282 - app.utils.memory_management - INFO - Memory after cleanup: 250.02 MB (freed 0.00 MB)
2025-06-06 22:50:59,895 - app - INFO - Cleaning up resources...
2025-06-06 22:50:59,895 - app.utils.memory_management - INFO - Memory before cleanup: 254.48 MB
2025-06-06 22:51:00,056 - app.utils.memory_management - INFO - Garbage collection: collected 379 objects
2025-06-06 22:51:00,058 - app.utils.memory_management - INFO - Memory after cleanup: 254.49 MB (freed -0.01 MB)
2025-06-06 22:51:00,058 - app - INFO - Application shutdown complete
2025-06-06 23:12:36,983 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-06 23:12:38,601 - app - INFO - Memory management utilities loaded
2025-06-06 23:12:38,603 - app - INFO - Error handling utilities loaded
2025-06-06 23:12:38,605 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 23:12:38,605 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 23:12:38,605 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 23:12:38,607 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 23:12:38,607 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-06 23:12:38,609 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-06 23:12:38,615 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-06 23:12:38,615 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-06 23:12:38,619 - app - INFO - Applied NumPy fix
2025-06-06 23:12:38,621 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:12:38,621 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:12:38,623 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:12:38,623 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-06 23:12:38,623 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:12:38,623 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:12:38,623 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:12:38,623 - app - INFO - Applied NumPy BitGenerator fix
2025-06-06 23:12:43,245 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-06 23:12:43,245 - app - INFO - Applied TensorFlow fix
2025-06-06 23:12:43,245 - app.config - INFO - Configuration initialized
2025-06-06 23:12:43,245 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-06 23:12:43,265 - models.train - INFO - TensorFlow test successful
2025-06-06 23:12:43,797 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-06 23:12:43,798 - models.train - INFO - Transformer model is available
2025-06-06 23:12:43,798 - models.train - INFO - Using TensorFlow-based models
2025-06-06 23:12:43,799 - models.predict - INFO - Transformer model is available for predictions
2025-06-06 23:12:43,800 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-06 23:12:43,802 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-06 23:12:44,175 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 23:12:44,176 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 23:12:44,177 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:12:44,177 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:12:44,178 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:12:44,178 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-06 23:12:44,178 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-06 23:12:44,179 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:12:44,179 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:12:44,179 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:12:44,287 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-06 23:12:44,294 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:12:44,984 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-06 23:12:45,640 - app.utils.session_state - INFO - Initializing session state
2025-06-06 23:12:45,643 - app.utils.session_state - INFO - Session state initialized
2025-06-06 23:12:46,870 - app - INFO - Found 8 stock files in data/stocks
2025-06-06 23:12:46,900 - app.utils.memory_management - INFO - Memory before cleanup: 424.93 MB
2025-06-06 23:12:47,097 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 23:12:47,097 - app.utils.memory_management - INFO - Memory after cleanup: 424.93 MB (freed -0.01 MB)
2025-06-06 23:12:49,187 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:12:49,209 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:12:49,245 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-06 23:12:49,251 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:12:49,251 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:12:49,251 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:12:49,253 - app.utils.memory_management - INFO - Memory before cleanup: 430.43 MB
2025-06-06 23:12:49,477 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 23:12:49,479 - app.utils.memory_management - INFO - Memory after cleanup: 430.43 MB (freed 0.00 MB)
2025-06-06 23:12:49,656 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:12:49,744 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:12:49,801 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:12:49,801 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:12:49,802 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:12:49,802 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:12:49,855 - app.utils.memory_management - INFO - Memory before cleanup: 431.20 MB
2025-06-06 23:12:50,093 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 23:12:50,095 - app.utils.memory_management - INFO - Memory after cleanup: 431.24 MB (freed -0.04 MB)
2025-06-06 23:12:55,503 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:12:55,775 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.05 seconds
2025-06-06 23:12:55,779 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:12:55,784 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:12:55,792 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:12:55,912 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:12:55,970 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-06 23:12:55,972 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:12:55,975 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:12:55,977 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:12:56,058 - app.utils.memory_management - INFO - Memory before cleanup: 432.12 MB
2025-06-06 23:12:56,287 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 23:12:56,289 - app.utils.memory_management - INFO - Memory after cleanup: 432.11 MB (freed 0.02 MB)
2025-06-06 23:12:56,928 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:12:56,942 - app.utils.session_state - INFO - Initializing session state
2025-06-06 23:12:56,960 - app.utils.session_state - INFO - Session state initialized
2025-06-06 23:12:57,051 - app.utils.memory_management - INFO - Memory before cleanup: 432.10 MB
2025-06-06 23:12:57,373 - app.utils.memory_management - INFO - Garbage collection: collected 308 objects
2025-06-06 23:12:57,376 - app.utils.memory_management - INFO - Memory after cleanup: 432.10 MB (freed 0.00 MB)
2025-06-06 23:13:01,921 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:13:01,941 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:13:01,957 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:13:01,958 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:13:01,959 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:13:01,959 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:13:01,961 - app.utils.memory_management - INFO - Memory before cleanup: 433.47 MB
2025-06-06 23:13:02,258 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-06 23:13:02,258 - app.utils.memory_management - INFO - Memory after cleanup: 433.49 MB (freed -0.02 MB)
2025-06-06 23:13:02,476 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:13:02,587 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:13:02,637 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:13:02,640 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:13:02,642 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:13:02,643 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:13:02,735 - app.utils.memory_management - INFO - Memory before cleanup: 433.49 MB
2025-06-06 23:13:03,336 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 23:13:03,337 - app.utils.memory_management - INFO - Memory after cleanup: 433.49 MB (freed 0.00 MB)
2025-06-06 23:13:04,671 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:13:04,735 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:13:04,737 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:13:04,737 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:13:04,738 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:13:04,775 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:13:04,825 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:13:04,826 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:13:04,826 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:13:04,827 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:13:04,877 - app.utils.memory_management - INFO - Memory before cleanup: 434.15 MB
2025-06-06 23:13:05,089 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 23:13:05,089 - app.utils.memory_management - INFO - Memory after cleanup: 434.15 MB (freed 0.00 MB)
2025-06-06 23:14:55,607 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-06 23:14:56,693 - app - INFO - Memory management utilities loaded
2025-06-06 23:14:56,693 - app - INFO - Error handling utilities loaded
2025-06-06 23:14:56,693 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 23:14:56,693 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 23:14:56,693 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 23:14:56,693 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 23:14:56,693 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-06 23:14:56,693 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-06 23:14:56,693 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-06 23:14:56,693 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-06 23:14:56,693 - app - INFO - Applied NumPy fix
2025-06-06 23:14:56,693 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:14:56,700 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:14:56,701 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:14:56,701 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-06 23:14:56,702 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:14:56,702 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:14:56,702 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:14:56,703 - app - INFO - Applied NumPy BitGenerator fix
2025-06-06 23:15:00,368 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-06 23:15:00,374 - app - INFO - Applied TensorFlow fix
2025-06-06 23:15:01,349 - app - INFO - Cleaning up resources...
2025-06-06 23:15:01,353 - app.utils.memory_management - INFO - Memory before cleanup: 323.85 MB
2025-06-06 23:15:01,494 - app.utils.memory_management - INFO - Garbage collection: collected 33 objects
2025-06-06 23:15:01,494 - app.utils.memory_management - INFO - Memory after cleanup: 323.85 MB (freed -0.00 MB)
2025-06-06 23:15:01,494 - app - INFO - Application shutdown complete
2025-06-06 23:15:33,624 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:15:33,633 - app.utils.session_state - INFO - Initializing session state
2025-06-06 23:15:33,649 - app.utils.session_state - INFO - Session state initialized
2025-06-06 23:15:33,680 - app.utils.memory_management - INFO - Memory before cleanup: 434.03 MB
2025-06-06 23:15:33,904 - app.utils.memory_management - INFO - Garbage collection: collected 308 objects
2025-06-06 23:15:33,910 - app.utils.memory_management - INFO - Memory after cleanup: 434.03 MB (freed 0.00 MB)
2025-06-06 23:15:37,169 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:15:37,187 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:15:37,205 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 23:15:37,208 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:15:37,209 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:15:37,209 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:15:37,211 - app.utils.memory_management - INFO - Memory before cleanup: 434.29 MB
2025-06-06 23:15:37,411 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-06 23:15:37,412 - app.utils.memory_management - INFO - Memory after cleanup: 434.29 MB (freed 0.00 MB)
2025-06-06 23:15:37,575 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:15:37,661 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:15:37,709 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 23:15:37,710 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:15:37,712 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:15:37,713 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:15:37,801 - app.utils.memory_management - INFO - Memory before cleanup: 434.30 MB
2025-06-06 23:15:38,062 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 23:15:38,065 - app.utils.memory_management - INFO - Memory after cleanup: 434.30 MB (freed 0.00 MB)
2025-06-06 23:15:44,242 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:15:44,309 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 23:15:44,315 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:15:44,317 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:15:44,321 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:15:44,397 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:15:44,474 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.06 seconds
2025-06-06 23:15:44,493 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:15:44,525 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:15:44,543 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:15:44,836 - app.utils.memory_management - INFO - Memory before cleanup: 434.30 MB
2025-06-06 23:15:45,143 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 23:15:45,146 - app.utils.memory_management - INFO - Memory after cleanup: 434.30 MB (freed 0.00 MB)
2025-06-06 23:16:18,591 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:16:18,598 - app.utils.session_state - INFO - Initializing session state
2025-06-06 23:16:18,599 - app.utils.session_state - INFO - Session state initialized
2025-06-06 23:16:18,620 - app.utils.memory_management - INFO - Memory before cleanup: 434.29 MB
2025-06-06 23:16:18,826 - app.utils.memory_management - INFO - Garbage collection: collected 308 objects
2025-06-06 23:16:18,827 - app.utils.memory_management - INFO - Memory after cleanup: 434.29 MB (freed 0.00 MB)
2025-06-06 23:17:29,210 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:17:29,229 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:17:29,241 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 23:17:29,243 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:17:29,245 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:17:29,246 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:17:29,249 - app.utils.memory_management - INFO - Memory before cleanup: 434.43 MB
2025-06-06 23:17:29,435 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-06 23:17:29,436 - app.utils.memory_management - INFO - Memory after cleanup: 434.43 MB (freed 0.00 MB)
2025-06-06 23:17:29,662 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:17:29,711 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:17:29,739 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 23:17:29,740 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:17:29,741 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:17:29,741 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:17:29,788 - app.utils.memory_management - INFO - Memory before cleanup: 435.05 MB
2025-06-06 23:17:29,985 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 23:17:29,985 - app.utils.memory_management - INFO - Memory after cleanup: 435.05 MB (freed 0.00 MB)
2025-06-06 23:17:33,261 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:17:33,328 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 23:17:33,330 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:17:33,333 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:17:33,337 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:17:33,410 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:17:33,447 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 23:17:33,448 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:17:33,450 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:17:33,455 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:17:33,519 - app.utils.memory_management - INFO - Memory before cleanup: 435.46 MB
2025-06-06 23:17:33,735 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 23:17:33,735 - app.utils.memory_management - INFO - Memory after cleanup: 435.46 MB (freed 0.00 MB)
2025-06-06 23:23:15,463 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-06 23:23:16,089 - app - INFO - Memory management utilities loaded
2025-06-06 23:23:16,089 - app - INFO - Error handling utilities loaded
2025-06-06 23:23:16,089 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 23:23:16,089 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 23:23:16,089 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 23:23:16,089 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 23:23:16,089 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-06 23:23:16,089 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-06 23:23:16,089 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-06 23:23:16,089 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-06 23:23:16,089 - app - INFO - Applied NumPy fix
2025-06-06 23:23:16,100 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:23:16,101 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:23:16,101 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:23:16,102 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-06 23:23:16,102 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:23:16,103 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:23:16,103 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:23:16,103 - app - INFO - Applied NumPy BitGenerator fix
2025-06-06 23:23:19,649 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-06 23:23:19,651 - app - INFO - Applied TensorFlow fix
2025-06-06 23:23:19,657 - app - INFO - Cleaning up resources...
2025-06-06 23:23:19,663 - app.utils.memory_management - INFO - Memory before cleanup: 309.12 MB
2025-06-06 23:23:19,790 - app.utils.memory_management - INFO - Garbage collection: collected 33 objects
2025-06-06 23:23:19,790 - app.utils.memory_management - INFO - Memory after cleanup: 309.50 MB (freed -0.37 MB)
2025-06-06 23:23:19,791 - app - INFO - Application shutdown complete
2025-06-06 23:24:04,111 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-06 23:24:04,787 - app - INFO - Memory management utilities loaded
2025-06-06 23:24:04,789 - app - INFO - Error handling utilities loaded
2025-06-06 23:24:04,789 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 23:24:04,789 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 23:24:04,789 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 23:24:04,789 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 23:24:04,793 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-06 23:24:04,795 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-06 23:24:04,798 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-06 23:24:04,803 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-06 23:24:04,804 - app - INFO - Applied NumPy fix
2025-06-06 23:24:04,805 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:24:04,805 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:24:04,805 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:24:04,806 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-06 23:24:04,806 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:24:04,806 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:24:04,807 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:24:04,807 - app - INFO - Applied NumPy BitGenerator fix
2025-06-06 23:24:08,570 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-06 23:24:08,570 - app - INFO - Applied TensorFlow fix
2025-06-06 23:24:09,189 - app.utils.common - INFO - Loaded stock data for COMI from data\COMI.csv in 0.62 seconds
2025-06-06 23:24:09,191 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:24:09,191 - app.utils.common - INFO - Data shape: (585, 6)
2025-06-06 23:24:09,195 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:24:09,958 - app - INFO - Cleaning up resources...
2025-06-06 23:24:09,964 - app.utils.memory_management - INFO - Memory before cleanup: 311.54 MB
2025-06-06 23:24:10,072 - app.utils.memory_management - INFO - Garbage collection: collected 159 objects
2025-06-06 23:24:10,072 - app.utils.memory_management - INFO - Memory after cleanup: 311.66 MB (freed -0.12 MB)
2025-06-06 23:24:10,072 - app - INFO - Application shutdown complete
2025-06-06 23:24:43,835 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-06 23:24:43,844 - app - INFO - Memory management utilities loaded
2025-06-06 23:24:43,846 - app - INFO - Error handling utilities loaded
2025-06-06 23:24:43,848 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 23:24:43,848 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 23:24:43,849 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 23:24:43,850 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 23:25:10,547 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-06 23:25:12,623 - app - INFO - Memory management utilities loaded
2025-06-06 23:25:12,625 - app - INFO - Error handling utilities loaded
2025-06-06 23:25:12,628 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 23:25:12,629 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 23:25:12,631 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 23:25:12,632 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 23:25:12,633 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-06 23:25:12,635 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-06 23:25:12,636 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-06 23:25:12,639 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-06 23:25:12,639 - app - INFO - Applied NumPy fix
2025-06-06 23:25:12,641 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:25:12,641 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:25:12,642 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:25:12,642 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-06 23:25:12,646 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:25:12,649 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:25:12,651 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:25:12,652 - app - INFO - Applied NumPy BitGenerator fix
2025-06-06 23:25:17,061 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-06 23:25:17,063 - app - INFO - Applied TensorFlow fix
2025-06-06 23:25:17,068 - app.config - INFO - Configuration initialized
2025-06-06 23:25:17,076 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-06 23:25:17,086 - models.train - INFO - TensorFlow test successful
2025-06-06 23:25:17,667 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-06 23:25:17,667 - models.train - INFO - Transformer model is available
2025-06-06 23:25:17,668 - models.train - INFO - Using TensorFlow-based models
2025-06-06 23:25:17,669 - models.predict - INFO - Transformer model is available for predictions
2025-06-06 23:25:17,670 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-06 23:25:17,672 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-06 23:25:18,041 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 23:25:18,041 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 23:25:18,041 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:25:18,041 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:25:18,042 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:25:18,042 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-06 23:25:18,043 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-06 23:25:18,043 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:25:18,043 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:25:18,043 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:25:18,141 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-06 23:25:18,144 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:25:18,913 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-06 23:25:19,465 - app.utils.session_state - INFO - Initializing session state
2025-06-06 23:25:19,469 - app.utils.session_state - INFO - Session state initialized
2025-06-06 23:25:20,682 - app - INFO - Found 8 stock files in data/stocks
2025-06-06 23:25:20,696 - app.utils.memory_management - INFO - Memory before cleanup: 424.27 MB
2025-06-06 23:25:20,928 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 23:25:20,938 - app.utils.memory_management - INFO - Memory after cleanup: 424.65 MB (freed -0.38 MB)
2025-06-06 23:25:27,127 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:25:27,163 - app.utils.memory_management - INFO - Memory before cleanup: 428.34 MB
2025-06-06 23:25:27,454 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 23:25:27,459 - app.utils.memory_management - INFO - Memory after cleanup: 428.34 MB (freed 0.00 MB)
2025-06-06 23:25:28,267 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:25:28,328 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.04 seconds
2025-06-06 23:25:28,329 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:25:28,330 - app - INFO - Data shape: (585, 36)
2025-06-06 23:25:28,330 - app - INFO - File COMI contains 2025 data
2025-06-06 23:25:28,395 - app - INFO - Feature engineering for COMI completed in 0.06 seconds
2025-06-06 23:25:28,397 - app - INFO - Features shape: (585, 36)
2025-06-06 23:25:28,422 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:25:28,423 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:25:28,423 - app - INFO - Data shape: (585, 36)
2025-06-06 23:25:28,425 - app - INFO - File COMI contains 2025 data
2025-06-06 23:25:28,433 - app.utils.memory_management - INFO - Memory before cleanup: 432.60 MB
2025-06-06 23:25:28,654 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-06 23:25:28,654 - app.utils.memory_management - INFO - Memory after cleanup: 432.64 MB (freed -0.04 MB)
2025-06-06 23:25:28,906 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:25:29,066 - app.utils.memory_management - INFO - Memory before cleanup: 433.55 MB
2025-06-06 23:25:29,328 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 23:25:29,330 - app.utils.memory_management - INFO - Memory after cleanup: 433.55 MB (freed 0.00 MB)
2025-06-06 23:25:30,790 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:25:30,853 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:25:30,904 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:25:30,907 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:25:30,909 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:25:30,910 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:25:30,963 - app.utils.memory_management - INFO - Memory before cleanup: 435.45 MB
2025-06-06 23:25:31,172 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-06 23:25:31,173 - app.utils.memory_management - INFO - Memory after cleanup: 435.45 MB (freed 0.00 MB)
2025-06-06 23:25:34,929 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:25:34,979 - app.utils.common - INFO - Loaded stock data for COMI from data\COMI.csv in 0.01 seconds
2025-06-06 23:25:34,981 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:25:34,984 - app.utils.common - INFO - Data shape: (585, 6)
2025-06-06 23:25:34,985 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:25:34,999 - app.components.tradingview_charts - INFO - Prepared 100 valid data points for chart
2025-06-06 23:25:35,040 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:25:35,077 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:25:35,082 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:25:35,087 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:25:35,089 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:25:35,169 - app.utils.memory_management - INFO - Memory before cleanup: 435.52 MB
2025-06-06 23:25:35,404 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-06 23:25:35,404 - app.utils.memory_management - INFO - Memory after cleanup: 435.52 MB (freed 0.00 MB)
2025-06-06 23:27:11,698 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:27:11,705 - app.utils.session_state - INFO - Initializing session state
2025-06-06 23:27:11,714 - app.utils.session_state - INFO - Session state initialized
2025-06-06 23:27:11,754 - app.utils.memory_management - INFO - Memory before cleanup: 435.29 MB
2025-06-06 23:27:11,979 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-06 23:27:11,981 - app.utils.memory_management - INFO - Memory after cleanup: 435.29 MB (freed -0.00 MB)
2025-06-06 23:27:16,065 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:27:16,087 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:27:16,102 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:27:16,104 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:27:16,104 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:27:16,104 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:27:16,106 - app.utils.memory_management - INFO - Memory before cleanup: 435.74 MB
2025-06-06 23:27:16,308 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-06 23:27:16,309 - app.utils.memory_management - INFO - Memory after cleanup: 435.74 MB (freed 0.00 MB)
2025-06-06 23:27:16,472 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:27:16,544 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:27:16,600 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:27:16,601 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:27:16,601 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:27:16,602 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:27:16,657 - app.utils.memory_management - INFO - Memory before cleanup: 435.74 MB
2025-06-06 23:27:16,852 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 23:27:16,852 - app.utils.memory_management - INFO - Memory after cleanup: 435.74 MB (freed 0.00 MB)
2025-06-06 23:27:19,077 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:27:19,155 - app.utils.common - INFO - Loaded stock data for COMI from data\COMI.csv in 0.01 seconds
2025-06-06 23:27:19,168 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:27:19,169 - app.utils.common - INFO - Data shape: (585, 6)
2025-06-06 23:27:19,171 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:27:19,187 - app.components.tradingview_charts - INFO - Prepared 100 valid data points for chart
2025-06-06 23:27:19,227 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:27:19,262 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-06 23:27:19,267 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:27:19,269 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:27:19,271 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:27:19,348 - app.utils.memory_management - INFO - Memory before cleanup: 435.77 MB
2025-06-06 23:27:19,594 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 23:27:19,595 - app.utils.memory_management - INFO - Memory after cleanup: 435.77 MB (freed 0.00 MB)
2025-06-06 23:30:12,882 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-06 23:30:14,953 - app - INFO - Memory management utilities loaded
2025-06-06 23:30:14,957 - app - INFO - Error handling utilities loaded
2025-06-06 23:30:14,959 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-06 23:30:14,959 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-06 23:30:14,961 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-06 23:30:14,961 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-06 23:30:14,968 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-06 23:30:14,971 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-06 23:30:14,975 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-06 23:30:14,976 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-06 23:30:14,978 - app - INFO - Applied NumPy fix
2025-06-06 23:30:14,979 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:30:14,980 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:30:14,982 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:30:14,984 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-06 23:30:14,986 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:30:14,988 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:30:14,988 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:30:14,988 - app - INFO - Applied NumPy BitGenerator fix
2025-06-06 23:30:19,469 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-06 23:30:19,473 - app - INFO - Applied TensorFlow fix
2025-06-06 23:30:19,482 - app.config - INFO - Configuration initialized
2025-06-06 23:30:19,499 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-06 23:30:19,535 - models.train - INFO - TensorFlow test successful
2025-06-06 23:30:20,173 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-06 23:30:20,175 - models.train - INFO - Transformer model is available
2025-06-06 23:30:20,176 - models.train - INFO - Using TensorFlow-based models
2025-06-06 23:30:20,179 - models.predict - INFO - Transformer model is available for predictions
2025-06-06 23:30:20,180 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-06 23:30:20,182 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-06 23:30:20,588 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 23:30:20,592 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-06 23:30:20,595 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-06 23:30:20,595 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-06 23:30:20,595 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-06 23:30:20,597 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-06 23:30:20,597 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-06 23:30:20,599 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-06 23:30:20,601 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-06 23:30:20,601 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-06 23:30:20,715 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-06 23:30:20,717 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:30:21,107 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-06 23:30:21,614 - app.utils.session_state - INFO - Initializing session state
2025-06-06 23:30:21,616 - app.utils.session_state - INFO - Session state initialized
2025-06-06 23:30:22,841 - app - INFO - Found 8 stock files in data/stocks
2025-06-06 23:30:22,875 - app.utils.memory_management - INFO - Memory before cleanup: 424.24 MB
2025-06-06 23:30:23,117 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-06 23:30:23,119 - app.utils.memory_management - INFO - Memory after cleanup: 424.70 MB (freed -0.46 MB)
2025-06-06 23:30:25,265 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:30:25,288 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:30:25,317 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-06 23:30:25,319 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:30:25,319 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:30:25,320 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:30:25,322 - app.utils.memory_management - INFO - Memory before cleanup: 430.60 MB
2025-06-06 23:30:25,542 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-06 23:30:25,543 - app.utils.memory_management - INFO - Memory after cleanup: 430.60 MB (freed 0.00 MB)
2025-06-06 23:30:25,727 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:30:25,820 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:30:25,884 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-06 23:30:25,888 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:30:25,890 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:30:25,893 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:30:25,958 - app.utils.memory_management - INFO - Memory before cleanup: 431.36 MB
2025-06-06 23:30:26,183 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 23:30:26,184 - app.utils.memory_management - INFO - Memory after cleanup: 431.40 MB (freed -0.04 MB)
2025-06-06 23:30:28,146 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:30:28,267 - app.utils.common - INFO - Loaded stock data for COMI from data\COMI.csv in 0.01 seconds
2025-06-06 23:30:28,285 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:30:28,286 - app.utils.common - INFO - Data shape: (585, 6)
2025-06-06 23:30:28,288 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:30:28,312 - app.components.tradingview_charts - INFO - Prepared 100 valid data points for chart
2025-06-06 23:30:28,398 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:30:28,506 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.05 seconds
2025-06-06 23:30:28,509 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:30:28,510 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:30:28,511 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:30:29,203 - app.utils.memory_management - INFO - Memory before cleanup: 432.77 MB
2025-06-06 23:30:29,621 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 23:30:29,622 - app.utils.memory_management - INFO - Memory after cleanup: 432.77 MB (freed 0.00 MB)
2025-06-06 23:30:33,855 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:30:33,894 - app.utils.session_state - INFO - Initializing session state
2025-06-06 23:30:33,912 - app.utils.session_state - INFO - Session state initialized
2025-06-06 23:30:33,956 - app.utils.memory_management - INFO - Memory before cleanup: 432.77 MB
2025-06-06 23:30:34,160 - app.utils.memory_management - INFO - Garbage collection: collected 308 objects
2025-06-06 23:30:34,161 - app.utils.memory_management - INFO - Memory after cleanup: 432.77 MB (freed 0.00 MB)
2025-06-06 23:30:38,180 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:30:38,224 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:30:38,246 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:30:38,248 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:30:38,248 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:30:38,249 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:30:38,250 - app.utils.memory_management - INFO - Memory before cleanup: 433.39 MB
2025-06-06 23:30:38,466 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-06 23:30:38,466 - app.utils.memory_management - INFO - Memory after cleanup: 433.39 MB (freed 0.00 MB)
2025-06-06 23:30:38,664 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:30:38,743 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:30:38,784 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-06 23:30:38,787 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:30:38,791 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:30:38,795 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:30:38,895 - app.utils.memory_management - INFO - Memory before cleanup: 433.39 MB
2025-06-06 23:30:39,182 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-06 23:30:39,182 - app.utils.memory_management - INFO - Memory after cleanup: 433.39 MB (freed 0.00 MB)
2025-06-06 23:30:40,707 - app - INFO - Using TensorFlow-based LSTM model
2025-06-06 23:30:40,782 - app.utils.common - INFO - Loaded stock data for COMI from data\COMI.csv in 0.02 seconds
2025-06-06 23:30:40,789 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:30:40,789 - app.utils.common - INFO - Data shape: (585, 6)
2025-06-06 23:30:40,791 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:30:40,807 - app.components.tradingview_charts - INFO - Prepared 100 valid data points for chart
2025-06-06 23:30:40,856 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-06 23:30:40,919 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-06 23:30:40,924 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-06 23:30:40,926 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-06 23:30:40,935 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-06 23:30:41,015 - app.utils.memory_management - INFO - Memory before cleanup: 433.41 MB
2025-06-06 23:30:41,273 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-06 23:30:41,274 - app.utils.memory_management - INFO - Memory after cleanup: 433.41 MB (freed 0.00 MB)
2025-06-06 23:32:28,548 - app - INFO - Cleaning up resources...
2025-06-06 23:32:28,548 - app.utils.memory_management - INFO - Memory before cleanup: 433.03 MB
2025-06-06 23:32:28,743 - app.utils.memory_management - INFO - Garbage collection: collected 1016 objects
2025-06-06 23:32:28,744 - app.utils.memory_management - INFO - Memory after cleanup: 433.03 MB (freed 0.00 MB)
2025-06-06 23:32:28,744 - app - INFO - Application shutdown complete
