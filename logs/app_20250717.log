2025-07-17 10:18:49,285 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-17 10:18:51,654 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 10:18:51,655 - app - INFO - Memory management utilities loaded
2025-07-17 10:18:51,657 - app - INFO - Error handling utilities loaded
2025-07-17 10:18:51,658 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 10:18:51,659 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 10:18:51,659 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 10:18:51,659 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 10:18:51,662 - app.utils.numpy_fix - INFO - <PERSON>T19937 is properly registered as a BitGenerator
2025-07-17 10:18:51,663 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 10:18:51,663 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 10:18:51,664 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 10:18:51,664 - app - INFO - Applied NumPy fix
2025-07-17 10:18:51,666 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 10:18:51,667 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 10:18:51,669 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 10:19:12,796 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 10:19:12,797 - app - INFO - Applied TensorFlow fix
2025-07-17 10:19:12,799 - app.config - INFO - Configuration initialized
2025-07-17 10:19:12,805 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-17 10:19:12,966 - models.train - INFO - TensorFlow test successful
2025-07-17 10:19:13,063 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 10:19:13,063 - models.train - INFO - Transformer model is available
2025-07-17 10:19:13,064 - models.train - INFO - Using TensorFlow-based models
2025-07-17 10:19:13,066 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 10:19:13,066 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 10:19:13,069 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 10:19:14,942 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 10:19:14,942 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 10:19:14,942 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 10:19:15,179 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 10:19:15,181 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:15,688 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-17 10:19:17,720 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-17 10:19:17,721 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-17 10:19:17,990 - app.utils.session_state - INFO - Initializing session state
2025-07-17 10:19:17,992 - app.utils.session_state - INFO - Session state initialized
2025-07-17 10:19:18,645 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 10:19:18,653 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:18,654 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:18,858 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:19:18,858 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:18,858 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:33,706 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:33,744 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:33,744 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:33,963 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:19:33,964 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:33,965 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:35,077 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:35,248 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.16 seconds
2025-07-17 10:19:35,254 - app - INFO - Date range: 2020-10-01 to 2025-07-16
2025-07-17 10:19:35,254 - app - INFO - Data shape: (1250, 36)
2025-07-17 10:19:35,254 - app - INFO - File COMI contains 2025 data
2025-07-17 10:19:35,294 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-07-17 10:19:35,295 - app - INFO - Features shape: (1250, 36)
2025-07-17 10:19:35,316 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-17 10:19:35,317 - app - INFO - Date range: 2020-10-01 to 2025-07-16
2025-07-17 10:19:35,318 - app - INFO - Data shape: (1250, 36)
2025-07-17 10:19:35,318 - app - INFO - File COMI contains 2025 data
2025-07-17 10:19:35,328 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:35,328 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:35,521 - app.utils.memory_management - INFO - Garbage collection: collected 626 objects
2025-07-17 10:19:35,521 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:35,522 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:35,942 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:36,172 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:36,172 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:36,375 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:19:36,375 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:36,376 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:47,169 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:47,239 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:47,240 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:47,462 - app.utils.memory_management - INFO - Garbage collection: collected 878 objects
2025-07-17 10:19:47,462 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:47,463 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:52,927 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:52,979 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:52,980 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:53,209 - app.utils.memory_management - INFO - Garbage collection: collected 772 objects
2025-07-17 10:19:53,210 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:53,210 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:55,096 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:55,208 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:55,209 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:55,411 - app.utils.memory_management - INFO - Garbage collection: collected 748 objects
2025-07-17 10:19:55,411 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:55,412 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:20:15,251 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:20:15,312 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:15,312 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:20:15,505 - app.utils.memory_management - INFO - Garbage collection: collected 1109 objects
2025-07-17 10:20:15,506 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:15,507 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:20:15,977 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:20:16,084 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:16,085 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:20:16,300 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:20:16,301 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:16,302 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:20:18,729 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:20:19,141 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-07-17 10:20:19,143 - app.models.adaptive - INFO - No valid models for COMI with 1min horizon, using equal weights
2025-07-17 10:20:19,143 - app.models.predict - INFO - Ensemble weights for COMI with 1min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 10:20:19,297 - models.predict - INFO - Making predictions for 1 minutes horizon
2025-07-17 10:20:19,492 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:19,493 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1_scaler.pkl (0.53 KB)
2025-07-17 10:20:19,696 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:19,697 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:20:19,702 - models.predict - INFO - Loading lstm model for COMI with horizon 1
2025-07-17 10:20:19,704 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_1min.keras
2025-07-17 10:20:19,707 - models.predict - ERROR - Error making predictions: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 10:20:19,707 - app.utils.performance - WARNING - enhanced_prediction failed after 0.5656s: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 10:20:19,708 - app.pages.professional_trading_strategy - WARNING - Prediction error: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 10:20:19,869 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:19,870 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:20:20,073 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:20:20,074 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:20,075 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:15,232 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:15,297 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:15,298 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:15,558 - app.utils.memory_management - INFO - Garbage collection: collected 1504 objects
2025-07-17 10:21:15,559 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:15,560 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:16,055 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:16,101 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:16,102 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:16,332 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:21:16,333 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:16,333 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:18,970 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:19,064 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:21:19,064 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:21:19,065 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:21:19,065 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:21:19,076 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 10:21:19,081 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 10:21:19,086 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 10:21:19,089 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 10:21:19,095 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 10:21:19,100 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 10:21:19,104 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 10:21:19,108 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 10:21:19,112 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 10:21:19,118 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 10:21:19,123 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 10:21:19,127 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 10:21:19,132 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 10:21:19,134 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 10:21:19,139 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 10:21:19,141 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 10:21:19,141 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,141 - app.pages.professional_trading_strategy - INFO - ✅ Using ensemble model with horizons: [5, 15, 30] (weight: 0.203)
2025-07-17 10:21:19,141 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-07-17 10:21:19,142 - app.models.adaptive - INFO - No valid models for COMI with 5min horizon, using equal weights
2025-07-17 10:21:19,142 - app.models.predict - INFO - Ensemble weights for COMI with 5min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 10:21:19,248 - app.models.predict - INFO - Adaptive ensemble prediction for 5min horizon: 87.20008976417155
2025-07-17 10:21:19,248 - app.models.adaptive - INFO - No valid models for COMI with 15min horizon, using equal weights
2025-07-17 10:21:19,248 - app.models.predict - INFO - Ensemble weights for COMI with 15min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 10:21:19,373 - app.models.predict - INFO - Adaptive ensemble prediction for 15min horizon: 87.22960192347634
2025-07-17 10:21:19,374 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-07-17 10:21:19,374 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 10:21:19,496 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 87.21315557996898
2025-07-17 10:21:19,496 - app.models.predict - INFO - Prediction completed in 0.35 seconds
2025-07-17 10:21:19,496 - app.pages.professional_trading_strategy - INFO - ✅ ENSEMBLE: BEARISH (67.3%) - Pred: 87.21
2025-07-17 10:21:19,496 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:21:19,502 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:21:19,502 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:21:19,506 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:21:19,511 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 10:21:19,516 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 10:21:19,523 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:21:19,525 - app.pages.professional_trading_strategy - INFO - ✅ Using lstm model with horizons: [5, 15, 30] (weight: 0.196)
2025-07-17 10:21:19,525 - app.models.predict - INFO - Using specified model type: lstm
2025-07-17 10:21:19,578 - app.models.predict - INFO - Prediction completed in 0.05 seconds
2025-07-17 10:21:19,579 - app.pages.professional_trading_strategy - INFO - ✅ LSTM: BEARISH (92.8%) - Pred: 85.68
2025-07-17 10:21:19,579 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:21:19,579 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:21:19,579 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:21:19,580 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:21:19,582 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 10:21:19,586 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 10:21:19,586 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,586 - app.pages.professional_trading_strategy - INFO - ✅ Using gb model with horizons: [5, 15, 30] (weight: 0.157)
2025-07-17 10:21:19,587 - app.models.predict - INFO - Using specified model type: gb
2025-07-17 10:21:19,635 - app.models.predict - INFO - Prediction completed in 0.05 seconds
2025-07-17 10:21:19,635 - app.pages.professional_trading_strategy - INFO - ✅ GB: BEARISH (54.7%) - Pred: 87.98
2025-07-17 10:21:19,635 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:21:19,635 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:21:19,635 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:21:19,635 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:21:19,638 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 10:21:19,640 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 10:21:19,640 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,641 - app.pages.professional_trading_strategy - INFO - ✅ Using rf model with horizons: [5, 15, 30] (weight: 0.152)
2025-07-17 10:21:19,641 - app.models.predict - INFO - Using specified model type: rf
2025-07-17 10:21:19,683 - app.models.predict - INFO - Prediction completed in 0.04 seconds
2025-07-17 10:21:19,683 - app.pages.professional_trading_strategy - INFO - ✅ RF: BEARISH (54.2%) - Pred: 87.98
2025-07-17 10:21:19,684 - app.utils.data_processing - INFO - Found svr model for COMI with 5 minutes horizon
2025-07-17 10:21:19,684 - app.utils.data_processing - INFO - Found svr model for COMI with 15 minutes horizon
2025-07-17 10:21:19,684 - app.utils.data_processing - INFO - Found svr model for COMI with 30 minutes horizon
2025-07-17 10:21:19,684 - app.utils.data_processing - INFO - Found svr model for COMI with 60 minutes horizon
2025-07-17 10:21:19,686 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 10:21:19,690 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 10:21:19,690 - app.utils.data_processing - INFO - Found svr model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,691 - app.pages.professional_trading_strategy - INFO - ✅ Using svr model with horizons: [5, 15, 30] (weight: 0.148)
2025-07-17 10:21:19,691 - app.models.predict - INFO - Using specified model type: svr
2025-07-17 10:21:19,742 - app.models.predict - INFO - Prediction completed in 0.05 seconds
2025-07-17 10:21:19,743 - app.pages.professional_trading_strategy - INFO - ✅ SVR: BEARISH (53.8%) - Pred: 87.98
2025-07-17 10:21:19,745 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:21:19,745 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:21:19,746 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:21:19,746 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:21:19,752 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 10:21:19,755 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 10:21:19,756 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,756 - app.pages.professional_trading_strategy - INFO - ✅ Using lr model with horizons: [5, 15, 30] (weight: 0.144)
2025-07-17 10:21:19,756 - app.models.predict - INFO - Using specified model type: lr
2025-07-17 10:21:19,791 - app.models.predict - INFO - Prediction completed in 0.03 seconds
2025-07-17 10:21:19,791 - app.pages.professional_trading_strategy - INFO - ✅ LR: BEARISH (53.4%) - Pred: 87.98
2025-07-17 10:21:19,791 - app.pages.professional_trading_strategy - INFO - ✅ Enhanced AI Consensus: 0/6 models predict BEARISH (0%)
2025-07-17 10:21:19,791 - app.pages.professional_trading_strategy - INFO - 📊 Market Regime: NEUTRAL | Volatility: 0.33
2025-07-17 10:21:19,845 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-07-17 10:21:19,876 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.166
2025-07-17 10:21:19,986 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:19,986 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:20,185 - app.utils.memory_management - INFO - Garbage collection: collected 712 objects
2025-07-17 10:21:20,185 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:20,186 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:38,835 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:38,903 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:38,906 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:39,197 - app.utils.memory_management - INFO - Garbage collection: collected 1015 objects
2025-07-17 10:21:39,198 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:39,201 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:39,732 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:39,787 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:39,788 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:40,016 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:21:40,017 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:40,018 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:44,201 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:44,379 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:44,379 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:44,584 - app.utils.memory_management - INFO - Garbage collection: collected 986 objects
2025-07-17 10:21:44,584 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:44,585 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:48,579 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:48,663 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:48,664 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:48,874 - app.utils.memory_management - INFO - Garbage collection: collected 1084 objects
2025-07-17 10:21:48,875 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:48,875 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:49,429 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:49,486 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:49,486 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:49,712 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:21:49,713 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:49,713 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:54,334 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:54,454 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:54,454 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:54,701 - app.utils.memory_management - INFO - Garbage collection: collected 897 objects
2025-07-17 10:21:54,703 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:54,703 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:22:02,218 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:22:02,364 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:02,364 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:22:02,693 - app.utils.memory_management - INFO - Garbage collection: collected 958 objects
2025-07-17 10:22:02,693 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:02,695 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:22:03,151 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:22:03,212 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:03,212 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:22:03,407 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:22:03,408 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:03,409 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:22:52,074 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:22:52,110 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:52,110 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:22:52,316 - app.utils.memory_management - INFO - Garbage collection: collected 760 objects
2025-07-17 10:22:52,316 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:52,316 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:22:57,976 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:22:58,002 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:58,002 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:22:58,209 - app.utils.memory_management - INFO - Garbage collection: collected 604 objects
2025-07-17 10:22:58,210 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:58,210 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:01,952 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:02,380 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:02,380 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:02,560 - app.utils.memory_management - INFO - Garbage collection: collected 619 objects
2025-07-17 10:23:02,561 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:02,561 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:11,333 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:11,355 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:11,356 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:11,558 - app.utils.memory_management - INFO - Garbage collection: collected 692 objects
2025-07-17 10:23:11,559 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:11,559 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:12,793 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:12,860 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-17 10:23:12,860 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:12,860 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:12,861 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,861 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,861 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-17 10:23:12,861 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-17 10:23:12,866 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:12,867 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:23:12,874 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:12,879 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:23:12,884 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-17 10:23:12,890 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-17 10:23:12,905 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-17 10:23:12,905 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:12,905 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:23:12,905 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:12,906 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,906 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-17 10:23:12,913 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-17 10:23:12,913 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:12,913 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:12,913 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,914 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,914 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-17 10:23:12,949 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,951 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,951 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:12,955 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:12,956 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:12,970 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:12,971 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:12,973 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:12,973 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,974 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,974 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:23:12,979 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:12,979 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:23:12,983 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:12,990 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:23:12,990 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:23:12,991 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:12,992 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:23:12,992 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:12,993 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,994 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:23:12,994 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:12,996 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:23:12,997 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:12,997 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,997 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:12,998 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:12,998 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:12,998 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,998 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:13,008 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:13,014 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:13,015 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:13,015 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:13,015 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:13,027 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:13,027 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:13,236 - app.utils.memory_management - INFO - Garbage collection: collected 710 objects
2025-07-17 10:23:13,237 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:13,237 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:26,968 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:27,016 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:27,017 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:27,017 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:27,025 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:27,026 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:27,039 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:27,040 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:27,041 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:27,042 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:27,042 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:27,043 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:23:27,051 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:27,052 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:23:27,062 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:27,068 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:23:27,068 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:23:27,069 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:27,069 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:23:27,069 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:27,069 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:27,084 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:27,092 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:27,093 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:27,094 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:27,094 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:27,108 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:27,109 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:27,339 - app.utils.memory_management - INFO - Garbage collection: collected 1588 objects
2025-07-17 10:23:27,339 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:27,340 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:29,750 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:29,804 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:29,814 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:29,814 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:29,815 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:29,816 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:29,817 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-17 10:23:29,819 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:29,819 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:29,819 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:29,823 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:29,824 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:29,824 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-17 10:23:29,824 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:41,609 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-17 10:23:41,645 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:23:41,839 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:41,840 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:23:42,032 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:42,033 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:23:42,034 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 10:23:42,169 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 10:23:42,169 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 10:23:42,169 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 10:23:42,169 - models.predict - INFO - Ensemble model already loaded
2025-07-17 10:23:42,224 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 10:23:42,224 - models.predict - INFO - Current price: 88.0, Predicted scaled value: 0.42449626261334245
2025-07-17 10:23:42,224 - models.predict - INFO - Prediction for 60 minutes horizon: 86.16654505425815
2025-07-17 10:23:42,227 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.17 vs current 88.00 (2.1% change, limit: 2.0%). Applying correction.
2025-07-17 10:23:42,228 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.17 -> 86.77 (change: -1.4%)
2025-07-17 10:23:42,228 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 86.17 -> 86.77 for 60min
2025-07-17 10:23:42,235 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,236 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,236 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:42,240 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:42,241 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:42,330 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,330 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,330 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:42,337 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:42,339 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:42,352 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:42,352 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:42,353 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:42,354 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,355 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:42,355 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:23:42,364 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:42,364 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:23:42,371 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:42,377 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:23:42,377 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:42,380 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,380 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:42,388 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:42,398 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:42,398 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:42,399 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:42,399 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:42,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:42,420 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:42,676 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:23:42,677 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:42,677 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:24:25,829 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:24:25,850 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 10:24:25,887 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:25,888 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:25,888 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:24:25,896 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:24:25,896 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:24:25,902 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:30,759 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-17 10:24:30,790 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:24:30,972 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:24:30,973 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:24:31,157 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:24:31,158 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:24:31,159 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 10:24:31,278 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 10:24:31,278 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 10:24:31,278 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 10:24:31,278 - models.predict - INFO - Ensemble model already loaded
2025-07-17 10:24:31,305 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 10:24:31,306 - models.predict - INFO - Current price: 88.0, Predicted scaled value: 0.42449626261334245
2025-07-17 10:24:31,306 - models.predict - INFO - Prediction for 60 minutes horizon: 86.16654505425815
2025-07-17 10:24:31,309 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.17 vs current 88.00 (2.1% change, limit: 2.0%). Applying correction.
2025-07-17 10:24:31,309 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.17 -> 86.77 (change: -1.4%)
2025-07-17 10:24:31,309 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 86.17 -> 86.77 for 60min
2025-07-17 10:24:31,337 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:24:31,337 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:24:31,338 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:24:31,338 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:31,339 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:24:31,340 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:24:31,349 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:24:31,350 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:24:31,358 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:24:31,363 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:24:31,364 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:24:31,366 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:24:31,366 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:24:31,366 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:24:31,367 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:24:31,367 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:24:31,368 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:24:31,370 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:31,370 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:24:31,379 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:24:31,384 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:24:31,384 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:24:31,385 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:24:31,385 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:24:31,425 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:24:31,426 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:24:31,619 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:24:31,620 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:24:31,621 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:25:26,799 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:25:26,850 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,853 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,854 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:26,859 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:26,860 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:26,880 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:25:26,880 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:25:26,881 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:25:26,881 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,882 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:25:26,882 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:25:26,888 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:25:26,888 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:25:26,892 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:26,896 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:25:26,897 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:25:26,897 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:25:26,897 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:25:26,897 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:26,900 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:25:26,900 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:25:26,901 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:25:26,901 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:25:26,901 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:26,901 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:25:26,902 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:25:26,902 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:25:26,902 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:25:26,902 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,908 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:25:26,924 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,929 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:26,929 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:26,929 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:26,930 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,947 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:26,947 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:25:27,159 - app.utils.memory_management - INFO - Garbage collection: collected 1919 objects
2025-07-17 10:25:27,160 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:27,161 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:25:33,770 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:25:33,814 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,818 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,821 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:33,835 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:33,836 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:25:33,867 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:25:33,869 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:25:33,879 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:25:33,886 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:25:33,886 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:25:33,886 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:25:33,886 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:33,888 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:25:33,889 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:25:33,889 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:25:33,890 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:25:33,891 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,891 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:25:33,898 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,900 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:33,901 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:33,901 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:33,901 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,905 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:39,468 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=True
2025-07-17 10:25:39,503 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:25:39,710 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:39,711 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:25:39,904 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:39,904 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:25:39,906 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-17 10:25:39,910 - models.hybrid_model - INFO - XGBoost is available
2025-07-17 10:25:39,910 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-17 10:25:39,912 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-17 10:25:39,912 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-17 10:25:39,912 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 10:25:39,913 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 10:25:39,916 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 10:25:39,917 - models.predict - INFO - Using fallback price due to error: 87.6
2025-07-17 10:25:39,917 - models.predict - INFO - Prediction for 60 minutes horizon: 87.6
2025-07-17 10:25:39,924 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:45,185 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=True
2025-07-17 10:25:45,219 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:25:45,414 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:45,415 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:25:45,605 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:45,606 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:25:45,607 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-17 10:25:45,608 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-17 10:25:46,180 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 10:25:47,635 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 10:25:47,636 - models.predict - INFO - Current price: 87.6, Predicted scaled value: 0.48016396164894104
2025-07-17 10:25:47,636 - models.predict - INFO - Prediction for 60 minutes horizon: 91.189998532617
2025-07-17 10:25:47,637 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 91.19 vs current 87.60 (4.1% change, limit: 2.0%). Applying correction.
2025-07-17 10:25:47,638 - app.pages.predictions_consolidated - INFO - Corrected prediction: 91.19 -> 88.83 (change: 1.4%)
2025-07-17 10:25:47,638 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 91.19 -> 88.83 for 60min
2025-07-17 10:25:47,638 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:52,733 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-17 10:25:52,768 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:25:52,996 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:52,997 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:25:53,186 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:53,186 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:25:53,188 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-17 10:25:53,188 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-17 10:25:53,188 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-17 10:25:53,188 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 10:25:53,208 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 10:25:53,208 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 10:25:53,208 - models.predict - INFO - Using fallback price due to error: 87.6
2025-07-17 10:25:53,209 - models.predict - INFO - Prediction for 60 minutes horizon: 87.6
2025-07-17 10:25:53,210 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:58,229 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-17 10:25:58,257 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:25:58,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:58,450 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:25:58,640 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:58,640 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:25:58,642 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-17 10:25:58,642 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-17 10:25:58,642 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-17 10:25:58,642 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 10:25:58,642 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 10:25:58,643 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 10:25:58,643 - models.predict - INFO - Using fallback price due to error: 87.6
2025-07-17 10:25:58,643 - models.predict - INFO - Prediction for 60 minutes horizon: 87.6
2025-07-17 10:25:58,645 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:26:03,628 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-17 10:26:03,658 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:26:03,852 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:26:03,853 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:26:04,044 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:26:04,045 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:26:04,047 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 10:26:04,150 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 10:26:04,150 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 10:26:04,150 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 10:26:04,151 - models.predict - INFO - Ensemble model already loaded
2025-07-17 10:26:04,178 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 10:26:04,178 - models.predict - INFO - Current price: 87.6, Predicted scaled value: 0.4207718075204704
2025-07-17 10:26:04,179 - models.predict - INFO - Prediction for 60 minutes horizon: 85.83045020544259
2025-07-17 10:26:04,181 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 85.83 vs current 87.60 (2.0% change, limit: 2.0%). Applying correction.
2025-07-17 10:26:04,182 - app.pages.predictions_consolidated - INFO - Corrected prediction: 85.83 -> 86.37 (change: -1.4%)
2025-07-17 10:26:04,182 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 85.83 -> 86.37 for 60min
2025-07-17 10:26:04,217 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:26:04,218 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:26:04,424 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:26:04,425 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:26:04,425 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:28:41,194 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:28:41,564 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-17 10:28:42,029 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-17 10:28:42,045 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-17 10:28:42,047 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-17 10:28:42,048 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-17 10:28:42,049 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-17 10:28:42,421 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.39 seconds
2025-07-17 10:28:42,822 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:28:42,823 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:28:43,049 - app.utils.memory_management - INFO - Garbage collection: collected 5822 objects
2025-07-17 10:28:43,050 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:28:43,051 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:29:43,388 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:29:43,408 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 10:29:43,735 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-17 10:29:43,967 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-17 10:29:43,990 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-17 10:29:43,992 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-17 10:29:43,992 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-17 10:29:43,993 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-17 10:29:44,326 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.36 seconds
2025-07-17 10:29:44,840 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:29:44,842 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:29:45,097 - app.utils.memory_management - INFO - Garbage collection: collected 5904 objects
2025-07-17 10:29:45,099 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:29:45,100 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:29:51,965 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:29:52,017 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-07-17 10:29:52,025 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.392
2025-07-17 10:29:52,096 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-17 10:29:52,228 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-17 10:29:52,230 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-07-17 10:29:52,230 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-17 10:29:52,230 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (53.8)
2025-07-17 10:29:52,230 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-17 10:29:59,815 - app.utils.ai_pattern_recognition - INFO - Using live price 52.15 EGP from API for ABUK
2025-07-17 10:29:59,816 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-07-17 10:29:59,816 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-07-17 10:29:59,817 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-07-17 10:29:59,820 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-17 10:29:59,852 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-07-17 10:29:59,852 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 50.7 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 50.69684046821175, 'bearish_score': 0}
2025-07-17 10:29:59,853 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-07-17 10:30:00,744 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:30:00,744 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:30:01,069 - app.utils.memory_management - INFO - Garbage collection: collected 5087 objects
2025-07-17 10:30:01,069 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:30:01,071 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:30:07,101 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:30:07,171 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-07-17 10:30:07,200 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.131
2025-07-17 10:30:07,295 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-17 10:30:07,358 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-17 10:30:07,359 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.97%) exceeds limit (15.00%)
2025-07-17 10:30:07,359 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-17 10:30:07,359 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (54.5)
2025-07-17 10:30:07,360 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-17 10:30:13,500 - app.utils.ai_pattern_recognition - INFO - Using live price 88.00 EGP from API for COMI
2025-07-17 10:30:13,500 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-07-17 10:30:13,500 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-07-17 10:30:13,502 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-17 10:30:13,534 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='BUY', volatility_factor=0.050
2025-07-17 10:30:13,534 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'BUY', 'confidence': 0.5, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 33.5 vs 22.5)', 'risk_level': 'Low', 'bullish_score': 33.5, 'bearish_score': 22.5}
2025-07-17 10:30:13,534 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=87.54, stop=83.16, target=98.48
2025-07-17 10:30:13,627 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:30:13,627 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:30:13,831 - app.utils.memory_management - INFO - Garbage collection: collected 3792 objects
2025-07-17 10:30:13,832 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:30:13,832 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:31:16,507 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:31:16,536 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:16,537 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:31:16,756 - app.utils.memory_management - INFO - Garbage collection: collected 1747 objects
2025-07-17 10:31:16,757 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:16,758 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:31:21,406 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:31:21,436 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:21,436 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:31:21,636 - app.utils.memory_management - INFO - Garbage collection: collected 696 objects
2025-07-17 10:31:21,637 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:21,638 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:31:23,794 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:31:23,934 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-07-17 10:31:23,968 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-11-07 to 2025-07-16
2025-07-17 10:31:23,968 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-07-17 10:31:29,639 - app.pages.smc_analysis - INFO - ✅ Using live price from API: 87.66 EGP for COMI
2025-07-17 10:31:30,770 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:30,772 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:31:30,992 - app.utils.memory_management - INFO - Garbage collection: collected 699 objects
2025-07-17 10:31:30,993 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:30,994 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:10,142 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:10,177 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:10,177 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:10,420 - app.utils.memory_management - INFO - Garbage collection: collected 2426 objects
2025-07-17 10:32:10,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:10,421 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:15,287 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:15,326 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:15,327 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:15,533 - app.utils.memory_management - INFO - Garbage collection: collected 766 objects
2025-07-17 10:32:15,534 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:15,535 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:18,564 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:18,601 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:18,601 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:18,819 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 10:32:18,821 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:18,823 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:24,669 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:24,714 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:24,714 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:24,915 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 10:32:24,917 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:24,917 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:26,367 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:32,151 - app.pages.advanced_technical_analysis - INFO - Loaded 782 days of historical data for COMI
2025-07-17 10:32:32,230 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:32,231 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:32,464 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 10:32:32,465 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:32,465 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 11:26:18,494 - app - INFO - Cleaning up resources...
2025-07-17 11:26:18,496 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 11:26:18,497 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 11:26:19,128 - app.utils.memory_management - INFO - Garbage collection: collected 1800 objects
2025-07-17 11:26:19,128 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 11:26:19,129 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 11:26:19,129 - app - INFO - Application shutdown complete
2025-07-17 22:02:00,167 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 22:02:02,882 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 22:02:02,882 - app - INFO - Memory management utilities loaded
2025-07-17 22:02:02,884 - app - INFO - Error handling utilities loaded
2025-07-17 22:02:02,885 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 22:02:02,886 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 22:02:02,886 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 22:02:02,886 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 22:02:02,887 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 22:02:02,887 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 22:02:02,887 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 22:02:02,887 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 22:02:02,887 - app - INFO - Applied NumPy fix
2025-07-17 22:02:02,893 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:02:02,893 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:02:02,893 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:02:02,893 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 22:02:02,893 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:02:02,893 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:02:02,893 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:02:02,893 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 22:02:46,951 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 22:02:46,952 - app - INFO - Applied TensorFlow fix
2025-07-17 22:02:46,971 - app.config - INFO - Configuration initialized
2025-07-17 22:02:46,984 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-17 22:02:47,025 - models.train - INFO - TensorFlow test successful
2025-07-17 22:02:47,215 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 22:02:47,215 - models.train - INFO - Transformer model is available
2025-07-17 22:02:47,215 - models.train - INFO - Using TensorFlow-based models
2025-07-17 22:02:47,217 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 22:02:47,217 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 22:02:47,222 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 22:02:51,095 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 22:02:51,095 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 22:02:51,096 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:02:51,096 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:02:51,096 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:02:51,096 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 22:02:51,096 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 22:02:51,096 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:02:51,096 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:02:51,096 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:02:51,622 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 22:02:51,624 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:02:52,442 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-17 22:02:55,663 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-17 22:02:55,663 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-17 22:02:55,992 - app.utils.session_state - INFO - Initializing session state
2025-07-17 22:02:55,994 - app.utils.session_state - INFO - Session state initialized
2025-07-17 22:02:56,663 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 22:02:56,689 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:02:56,689 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:02:56,964 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:02:56,965 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:02:56,968 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:03:07,779 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:03:07,816 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-17 22:03:07,829 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-17 22:03:07,832 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-17 22:03:07,833 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-17 22:03:07,835 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-17 22:03:07,863 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-07-17 22:03:08,423 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-17 22:03:08,427 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-17 22:03:08,433 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-17 22:03:08,446 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-17 22:03:08,477 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-17 22:03:08,487 - app.utils.error_handling - INFO - live_trading_component executed in 0.67 seconds
2025-07-17 22:03:08,489 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:03:08,489 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:03:08,732 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:03:08,732 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:03:08,732 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:03:11,667 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:03:36,375 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-17 22:03:36,378 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-17 22:03:36,380 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-17 22:03:36,383 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-17 22:03:36,392 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-17 22:03:36,398 - app.utils.error_handling - INFO - live_trading_component executed in 24.70 seconds
2025-07-17 22:03:36,398 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:03:36,398 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:03:36,634 - app.utils.memory_management - INFO - Garbage collection: collected 1150 objects
2025-07-17 22:03:36,635 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:03:36,635 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:03:54,002 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:03:54,091 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-17 22:03:54,108 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-17 22:03:54,123 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-17 22:03:54,134 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-17 22:03:54,152 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-17 22:03:54,157 - app.utils.error_handling - INFO - live_trading_component executed in 0.14 seconds
2025-07-17 22:03:54,157 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:03:54,157 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:03:54,373 - app.utils.memory_management - INFO - Garbage collection: collected 1272 objects
2025-07-17 22:03:54,374 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:03:54,375 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:03:59,699 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:04:10,454 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-17 22:04:10,457 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-17 22:04:10,461 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-17 22:04:10,465 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-17 22:04:10,479 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-17 22:04:10,493 - app.utils.error_handling - INFO - live_trading_component executed in 10.77 seconds
2025-07-17 22:04:10,493 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:10,494 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:04:10,774 - app.utils.memory_management - INFO - Garbage collection: collected 1175 objects
2025-07-17 22:04:10,775 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:10,777 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:04:19,433 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:04:19,538 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.08 seconds
2025-07-17 22:04:19,538 - app - INFO - Date range: 2020-10-01 to 2025-07-16
2025-07-17 22:04:19,539 - app - INFO - Data shape: (1250, 36)
2025-07-17 22:04:19,539 - app - INFO - File COMI contains 2025 data
2025-07-17 22:04:19,572 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-07-17 22:04:19,572 - app - INFO - Features shape: (1250, 36)
2025-07-17 22:04:19,594 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-17 22:04:19,595 - app - INFO - Date range: 2020-10-01 to 2025-07-16
2025-07-17 22:04:19,595 - app - INFO - Data shape: (1250, 36)
2025-07-17 22:04:19,595 - app - INFO - File COMI contains 2025 data
2025-07-17 22:04:19,602 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:19,603 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:04:19,785 - app.utils.memory_management - INFO - Garbage collection: collected 1376 objects
2025-07-17 22:04:19,786 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:19,787 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:04:20,178 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:04:20,261 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 22:04:20,261 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 22:04:20,261 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:04:20,261 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:04:20,269 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 22:04:20,273 - app.utils.error_handling - INFO - live_trading_component executed in 0.08 seconds
2025-07-17 22:04:20,273 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:20,274 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:04:20,509 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 22:04:20,510 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:20,510 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:04:26,094 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:04:39,458 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 22:04:39,459 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 22:04:39,460 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:04:39,460 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:04:39,493 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 22:04:39,498 - app.utils.error_handling - INFO - live_trading_component executed in 13.38 seconds
2025-07-17 22:04:39,500 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:39,502 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:04:39,743 - app.utils.memory_management - INFO - Garbage collection: collected 1077 objects
2025-07-17 22:04:39,751 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:39,751 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:04:54,947 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:04:54,990 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-17 22:04:54,994 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-17 22:04:55,075 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:55,075 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:04:55,280 - app.utils.memory_management - INFO - Garbage collection: collected 1259 objects
2025-07-17 22:04:55,280 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:04:55,281 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:05:08,108 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:05:08,133 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-17 22:05:08,140 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-17 22:05:08,241 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:05:08,241 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:05:08,437 - app.utils.memory_management - INFO - Garbage collection: collected 1621 objects
2025-07-17 22:05:08,438 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:05:08,439 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:05:10,847 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:05:10,869 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-17 22:05:10,885 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-17 22:05:10,920 - app.utils.historical_data_downloader - INFO - Starting historical data generation for COMI (5 years)
2025-07-17 22:05:10,920 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-07-17 22:05:10,929 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-07-17 22:05:10,931 - app.utils.historical_data_downloader - INFO - Fetching current live data for COMI...
2025-07-17 22:05:10,931 - app.utils.historical_data_downloader - INFO - Downloading historical data for COMI with intervals: ['1D', '1W']
2025-07-17 22:05:10,931 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-07-17 22:05:10,931 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-COMI'], 'intervals': ['1D', '1W']}
2025-07-17 22:05:25,211 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-07-17 22:05:25,212 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-COMI': [{'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 86448.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 85828.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 84702.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 83827.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 83672.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 82844.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 82437.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81849.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 81119.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 80113.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 80003.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 80291.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 83515.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 83580.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 89780.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 99100.0, 'fibo': 90680.0, 'camarilla': 86616.0, 'woodie': 96930.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 90680.0, 'fibo': 87464.0, 'camarilla': 85844.0, 'woodie': 91190.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 87490.0, 'fibo': 85476.0, 'camarilla': 85072.0, 'woodie': 88510.0, 'dm': 89085.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82260.0, 'fibo': 82260.0, 'camarilla': 82260.0, 'woodie': 82770.0, 'dm': 83058.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 79070.0, 'fibo': 79044.0, 'camarilla': 83528.0, 'woodie': 80090.0, 'dm': 80665.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 73840.0, 'fibo': 77056.0, 'camarilla': 82756.0, 'woodie': 74350.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 65420.0, 'fibo': 73840.0, 'camarilla': 81985.0, 'woodie': 71670.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 83643.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 82922.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 82026.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 81091.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 81321.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 79967.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 79990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81316.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 74265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 77238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 63133.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 57419.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 82650.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 81238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 86906.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]}, 'message': 'Successfully scraped 1 pairs'}
2025-07-17 22:05:25,213 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-COMI']
2025-07-17 22:05:25,213 - app.utils.historical_data_downloader - INFO - Found data for EGX-COMI: [{'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 86448.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 85828.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 84702.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 83827.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 83672.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 82844.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 82437.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81849.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 81119.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 80113.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 80003.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 80291.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 83515.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 83580.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 89780.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 99100.0, 'fibo': 90680.0, 'camarilla': 86616.0, 'woodie': 96930.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 90680.0, 'fibo': 87464.0, 'camarilla': 85844.0, 'woodie': 91190.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 87490.0, 'fibo': 85476.0, 'camarilla': 85072.0, 'woodie': 88510.0, 'dm': 89085.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82260.0, 'fibo': 82260.0, 'camarilla': 82260.0, 'woodie': 82770.0, 'dm': 83058.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 79070.0, 'fibo': 79044.0, 'camarilla': 83528.0, 'woodie': 80090.0, 'dm': 80665.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 73840.0, 'fibo': 77056.0, 'camarilla': 82756.0, 'woodie': 74350.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 65420.0, 'fibo': 73840.0, 'camarilla': 81985.0, 'woodie': 71670.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 83643.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 82922.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 82026.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 81091.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 81321.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 79967.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 79990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81316.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 74265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 77238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 63133.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 57419.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 82650.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 81238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 86906.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-07-17 22:05:25,214 - app.utils.historical_data_downloader - INFO - Processing 2 data points for COMI
2025-07-17 22:05:25,214 - app.utils.historical_data_downloader - INFO - Raw data structure: [{'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 86448.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 85828.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 84702.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 83827.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 83672.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 82844.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 82437.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81849.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 81119.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 80113.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 80003.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 80291.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 83515.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 83580.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 89780.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 99100.0, 'fibo': 90680.0, 'camarilla': 86616.0, 'woodie': 96930.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 90680.0, 'fibo': 87464.0, 'camarilla': 85844.0, 'woodie': 91190.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 87490.0, 'fibo': 85476.0, 'camarilla': 85072.0, 'woodie': 88510.0, 'dm': 89085.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82260.0, 'fibo': 82260.0, 'camarilla': 82260.0, 'woodie': 82770.0, 'dm': 83058.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 79070.0, 'fibo': 79044.0, 'camarilla': 83528.0, 'woodie': 80090.0, 'dm': 80665.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 73840.0, 'fibo': 77056.0, 'camarilla': 82756.0, 'woodie': 74350.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 65420.0, 'fibo': 73840.0, 'camarilla': 81985.0, 'woodie': 71670.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 83643.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 82922.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 82026.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 81091.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 81321.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 79967.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 79990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81316.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 74265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 77238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 63133.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 57419.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 82650.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 81238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 86906.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-07-17 22:05:25,215 - app.utils.historical_data_downloader - INFO - First item structure: {'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 86448.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 85828.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 84702.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 83827.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 83672.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 82844.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 82437.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81849.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 81119.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 80113.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 80003.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 80291.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 83515.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 83580.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 89780.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 99100.0, 'fibo': 90680.0, 'camarilla': 86616.0, 'woodie': 96930.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 90680.0, 'fibo': 87464.0, 'camarilla': 85844.0, 'woodie': 91190.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 87490.0, 'fibo': 85476.0, 'camarilla': 85072.0, 'woodie': 88510.0, 'dm': 89085.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82260.0, 'fibo': 82260.0, 'camarilla': 82260.0, 'woodie': 82770.0, 'dm': 83058.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 79070.0, 'fibo': 79044.0, 'camarilla': 83528.0, 'woodie': 80090.0, 'dm': 80665.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 73840.0, 'fibo': 77056.0, 'camarilla': 82756.0, 'woodie': 74350.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 65420.0, 'fibo': 73840.0, 'camarilla': 81985.0, 'woodie': 71670.0, 'dm': None}]}
2025-07-17 22:05:25,216 - app.utils.historical_data_downloader - INFO - Processing interval 1D: pair=EGX-COMI, price=89980.0
2025-07-17 22:05:25,216 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 89980.0 -> 89.98 EGP
2025-07-17 22:05:25,216 - app.utils.historical_data_downloader - INFO - Successfully processed 1D: price=89.98 EGP
2025-07-17 22:05:25,218 - app.utils.historical_data_downloader - INFO - Processing interval 1W: pair=EGX-COMI, price=89980.0
2025-07-17 22:05:25,218 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 89980.0 -> 89.98 EGP
2025-07-17 22:05:25,218 - app.utils.historical_data_downloader - INFO - Successfully processed 1W: price=89.98 EGP
2025-07-17 22:05:25,218 - app.utils.historical_data_downloader - INFO - Successfully processed 2 intervals for COMI
2025-07-17 22:05:25,219 - app.utils.historical_data_downloader - INFO - Successfully processed historical data for COMI
2025-07-17 22:05:25,220 - app.utils.historical_data_downloader - INFO - Successfully retrieved current data for COMI: {'1D': {'symbol': 'COMI', 'interval': '1D', 'current_price': 89.98, 'timestamp': datetime.datetime(2025, 7, 17, 22, 5, 25, 216894), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 86448.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 85828.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 84702.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 83827.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 83672.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 82844.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 82437.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81849.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 81119.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 80113.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 80003.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 80291.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 83515.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 83580.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 89780.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 99100.0, 'fibo': 90680.0, 'camarilla': 86616.0, 'woodie': 96930.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 90680.0, 'fibo': 87464.0, 'camarilla': 85844.0, 'woodie': 91190.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 87490.0, 'fibo': 85476.0, 'camarilla': 85072.0, 'woodie': 88510.0, 'dm': 89085.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82260.0, 'fibo': 82260.0, 'camarilla': 82260.0, 'woodie': 82770.0, 'dm': 83058.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 79070.0, 'fibo': 79044.0, 'camarilla': 83528.0, 'woodie': 80090.0, 'dm': 80665.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 73840.0, 'fibo': 77056.0, 'camarilla': 82756.0, 'woodie': 74350.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 65420.0, 'fibo': 73840.0, 'camarilla': 81985.0, 'woodie': 71670.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 86448.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 85828.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 84702.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 83827.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 83672.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 82844.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 82437.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81849.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 81119.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 80113.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 80003.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 80291.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 83515.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 83580.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 89780.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 99100.0, 'fibo': 90680.0, 'camarilla': 86616.0, 'woodie': 96930.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 90680.0, 'fibo': 87464.0, 'camarilla': 85844.0, 'woodie': 91190.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 87490.0, 'fibo': 85476.0, 'camarilla': 85072.0, 'woodie': 88510.0, 'dm': 89085.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82260.0, 'fibo': 82260.0, 'camarilla': 82260.0, 'woodie': 82770.0, 'dm': 83058.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 79070.0, 'fibo': 79044.0, 'camarilla': 83528.0, 'woodie': 80090.0, 'dm': 80665.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 73840.0, 'fibo': 77056.0, 'camarilla': 82756.0, 'woodie': 74350.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 65420.0, 'fibo': 73840.0, 'camarilla': 81985.0, 'woodie': 71670.0, 'dm': None}]}}, '1W': {'symbol': 'COMI', 'interval': '1W', 'current_price': 89.98, 'timestamp': datetime.datetime(2025, 7, 17, 22, 5, 25, 218804), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 83643.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 82922.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 82026.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 81091.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 81321.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 79967.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 79990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81316.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 74265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 77238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 63133.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 57419.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 82650.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 81238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 86906.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 89980.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Relative Strength Index (14)', 'value': 69271.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93778.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Commodity Channel Index (20)', 'value': 157422.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Average Directional Index (14)', 'value': 29137.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Awesome Oscillator', 'value': 4649.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Momentum (10)', 'value': 5020.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'MACD Level (12, 26)', 'value': 1970.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 88592.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Williams Percent Range (14)', 'value': -250.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Bull Bear Power', 'value': 5944.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 71923.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (10)', 'value': 83643.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (10)', 'value': 82922.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (20)', 'value': 82026.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (20)', 'value': 81091.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (30)', 'value': 81321.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (30)', 'value': 79967.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (50)', 'value': 79990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (50)', 'value': 81316.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (100)', 'value': 74265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (100)', 'value': 77238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Exponential Moving Average (200)', 'value': 63133.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Simple Moving Average (200)', 'value': 57419.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 82650.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 81238.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'name': 'Hull Moving Average (9)', 'value': 86906.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '17/07/2025 22:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}}}
2025-07-17 22:05:25,231 - app.utils.historical_data_downloader - INFO - Found valid price from 1D: 89.98 EGP
2025-07-17 22:05:25,231 - app.utils.historical_data_downloader - INFO - Using current price for COMI: 89.98 EGP
2025-07-17 22:05:25,231 - app.utils.historical_data_downloader - INFO - Generating 5 years of synthetic historical data...
2025-07-17 22:05:25,231 - app.utils.historical_data_downloader - INFO - Generating 5 years of synthetic data for COMI
2025-07-17 22:05:25,287 - app.utils.historical_data_downloader - INFO - Generated 1250 days of synthetic historical data for COMI
2025-07-17 22:05:25,288 - app.utils.historical_data_downloader - INFO - Generated 1250 days of synthetic data
2025-07-17 22:05:25,288 - app.utils.historical_data_downloader - INFO - Saving data to: data/stocks/COMI.csv
2025-07-17 22:05:25,326 - app.utils.historical_data_downloader - INFO - Saved 1250 days of historical data to data/stocks/COMI.csv
2025-07-17 22:05:25,326 - app.utils.historical_data_downloader - INFO - CSV file size: 54281 bytes
2025-07-17 22:05:25,329 - app.utils.historical_data_downloader - INFO - Verification: CSV contains 1250 rows
2025-07-17 22:05:25,330 - app.utils.historical_data_downloader - INFO - Successfully completed historical data generation for COMI
2025-07-17 22:05:25,430 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:05:25,431 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:05:25,753 - app.utils.memory_management - INFO - Garbage collection: collected 1585 objects
2025-07-17 22:05:25,765 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:05:25,769 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:05:38,381 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:05:38,409 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:05:38,409 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:05:38,619 - app.utils.memory_management - INFO - Garbage collection: collected 1745 objects
2025-07-17 22:05:38,621 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:05:38,622 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:11,866 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:11,977 - app - INFO - File COMI contains 2025 data
2025-07-17 22:06:11,997 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-17 22:06:11,998 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:06:12,000 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:12,001 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:12,221 - app.utils.memory_management - INFO - Garbage collection: collected 662 objects
2025-07-17 22:06:12,222 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:12,257 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:16,164 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:16,213 - app - INFO - File COMI contains 2025 data
2025-07-17 22:06:16,223 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-17 22:06:16,224 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:06:16,228 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:16,229 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:16,437 - app.utils.memory_management - INFO - Garbage collection: collected 919 objects
2025-07-17 22:06:16,439 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:16,439 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:19,344 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:19,438 - app - INFO - File COMI contains 2025 data
2025-07-17 22:06:19,451 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-17 22:06:19,452 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:06:19,459 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:19,459 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:19,677 - app.utils.memory_management - INFO - Garbage collection: collected 957 objects
2025-07-17 22:06:19,677 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:19,679 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:22,020 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:22,163 - app - INFO - File COMI contains 2025 data
2025-07-17 22:06:22,176 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-17 22:06:22,178 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:06:22,181 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:22,182 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:22,442 - app.utils.memory_management - INFO - Garbage collection: collected 1035 objects
2025-07-17 22:06:22,442 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:22,443 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:24,861 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:25,070 - app - INFO - File COMI contains 2025 data
2025-07-17 22:06:25,149 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-17 22:06:25,150 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:06:25,152 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:25,153 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:25,410 - app.utils.memory_management - INFO - Garbage collection: collected 1049 objects
2025-07-17 22:06:25,411 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:25,412 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:28,715 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:28,903 - app - INFO - File COMI contains 2025 data
2025-07-17 22:06:29,001 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-17 22:06:29,002 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:06:29,011 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:29,011 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:29,269 - app.utils.memory_management - INFO - Garbage collection: collected 1093 objects
2025-07-17 22:06:29,270 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:29,270 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:35,039 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:35,115 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:35,115 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:35,446 - app.utils.memory_management - INFO - Garbage collection: collected 1084 objects
2025-07-17 22:06:35,446 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:35,447 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:41,363 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:41,400 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:41,400 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:41,593 - app.utils.memory_management - INFO - Garbage collection: collected 863 objects
2025-07-17 22:06:41,593 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:41,595 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:43,769 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:43,874 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:43,874 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:44,060 - app.utils.memory_management - INFO - Garbage collection: collected 748 objects
2025-07-17 22:06:44,061 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:44,061 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:54,677 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:54,710 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:54,711 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:54,917 - app.utils.memory_management - INFO - Garbage collection: collected 1109 objects
2025-07-17 22:06:54,919 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:54,920 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:55,343 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:55,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:55,421 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:06:55,627 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 22:06:55,628 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:55,634 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:06:58,420 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:06:58,821 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-07-17 22:06:58,823 - app.models.adaptive - INFO - No valid models for COMI with 1min horizon, using equal weights
2025-07-17 22:06:58,823 - app.models.predict - INFO - Ensemble weights for COMI with 1min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 22:06:58,876 - models.predict - INFO - Making predictions for 1 minutes horizon
2025-07-17 22:06:59,058 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:59,059 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1_scaler.pkl (0.53 KB)
2025-07-17 22:06:59,274 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:59,274 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-07-17 22:06:59,279 - models.predict - INFO - Using scikit-learn rf model for 1 minutes horizon
2025-07-17 22:06:59,285 - models.hybrid_model - INFO - XGBoost is available
2025-07-17 22:06:59,286 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-17 22:06:59,287 - models.predict - INFO - Loading rf model for COMI with horizon 1
2025-07-17 22:06:59,288 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_1min.joblib or saved_models/COMI_rf_1min.pkl
2025-07-17 22:06:59,288 - models.sklearn_model - WARNING - Model not found at saved_models/COMI_rf_1min.joblib, searching for alternatives...
2025-07-17 22:06:59,306 - models.sklearn_model - INFO - Found 48 potential model files: ['COMI_gb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_gb_120960min.joblib', 'COMI_arima_ml_params_10080min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_rf_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_gb_20160min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_gb_15min.joblib', 'COMI_svr_10080min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_15min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_rf_10080min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_lr_10080min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_svr_20160min.joblib']
2025-07-17 22:06:59,307 - models.sklearn_model - INFO - Found matching model file: saved_models/COMI_rf_15min.joblib
2025-07-17 22:06:59,307 - models.sklearn_model - INFO - Loading model from saved_models/COMI_rf_15min.joblib
2025-07-17 22:06:59,371 - models.sklearn_model - INFO - Successfully loaded rf model from saved_models/COMI_rf_15min.joblib
2025-07-17 22:06:59,371 - models.predict - INFO - Successfully loaded model for COMI with horizon 1
2025-07-17 22:06:59,372 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3, while dim <= 2 is required by RandomForestRegressor.. Trying with prepared data.
2025-07-17 22:06:59,372 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-17 22:06:59,383 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:06:59,384 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.9182756531238556
2025-07-17 22:06:59,384 - models.predict - INFO - Prediction for 1 minutes horizon: 92.02150343858246
2025-07-17 22:06:59,450 - models.predict - INFO - Making predictions for 1 minutes horizon
2025-07-17 22:06:59,648 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:59,648 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1_scaler.pkl (0.53 KB)
2025-07-17 22:06:59,845 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:06:59,845 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:06:59,848 - models.predict - INFO - Using scikit-learn gb model for 1 minutes horizon
2025-07-17 22:06:59,848 - models.predict - INFO - Loading gb model for COMI with horizon 1
2025-07-17 22:06:59,849 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_1min.joblib or saved_models/COMI_gb_1min.pkl
2025-07-17 22:06:59,849 - models.sklearn_model - WARNING - Model not found at saved_models/COMI_gb_1min.joblib, searching for alternatives...
2025-07-17 22:06:59,851 - models.sklearn_model - INFO - Found 48 potential model files: ['COMI_gb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_gb_120960min.joblib', 'COMI_arima_ml_params_10080min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_rf_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_gb_20160min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_gb_15min.joblib', 'COMI_svr_10080min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_15min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_rf_10080min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_lr_10080min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_svr_20160min.joblib']
2025-07-17 22:06:59,853 - models.sklearn_model - INFO - Found matching model file: saved_models/COMI_gb_10080min.joblib
2025-07-17 22:06:59,853 - models.sklearn_model - INFO - Loading model from saved_models/COMI_gb_10080min.joblib
2025-07-17 22:06:59,871 - models.sklearn_model - INFO - Successfully loaded gb model from saved_models/COMI_gb_10080min.joblib
2025-07-17 22:06:59,872 - models.predict - INFO - Successfully loaded model for COMI with horizon 1
2025-07-17 22:06:59,895 - models.sklearn_model - WARNING - Prediction with original shape failed: Feature shape mismatch, expected: 420, got 60. Trying with prepared data.
2025-07-17 22:06:59,895 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-17 22:06:59,897 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:06:59,898 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.8882389664649963
2025-07-17 22:06:59,899 - models.predict - INFO - Prediction for 1 minutes horizon: 90.37549310165495
2025-07-17 22:06:59,972 - models.predict - INFO - Making predictions for 1 minutes horizon
2025-07-17 22:07:00,185 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:00,186 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1_scaler.pkl (0.53 KB)
2025-07-17 22:07:00,376 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:00,378 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:00,379 - models.predict - INFO - Loading lstm model for COMI with horizon 1
2025-07-17 22:07:00,379 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_1min.keras
2025-07-17 22:07:00,381 - models.predict - ERROR - Error making predictions: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 22:07:00,382 - app.utils.performance - WARNING - enhanced_prediction failed after 1.5604s: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 22:07:00,382 - app.pages.professional_trading_strategy - WARNING - Prediction error: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 22:07:00,541 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:00,542 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:07:00,743 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:07:00,744 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:00,745 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:07:47,039 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:07:47,099 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:47,099 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:07:47,312 - app.utils.memory_management - INFO - Garbage collection: collected 1504 objects
2025-07-17 22:07:47,313 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:47,314 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:07:47,767 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:07:47,803 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:47,804 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:07:48,012 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 22:07:48,013 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:48,013 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:07:50,823 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:07:50,920 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 22:07:50,921 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 22:07:50,921 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:07:50,921 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:07:50,940 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:07:50,942 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 22:07:50,947 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 22:07:50,949 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 22:07:50,951 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 22:07:50,954 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 22:07:50,956 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 22:07:50,958 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 22:07:50,962 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:07:50,965 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 22:07:50,968 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 22:07:50,970 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 22:07:50,973 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 22:07:50,975 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 22:07:50,978 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 22:07:50,980 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 22:07:50,980 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 22:07:50,980 - app.pages.professional_trading_strategy - INFO - ✅ Using ensemble model with horizons: [5, 15, 30] (weight: 0.203)
2025-07-17 22:07:50,980 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-07-17 22:07:50,980 - app.models.adaptive - INFO - No valid models for COMI with 5min horizon, using equal weights
2025-07-17 22:07:50,980 - app.models.predict - INFO - Ensemble weights for COMI with 5min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 22:07:51,034 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-17 22:07:51,234 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:51,235 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-17 22:07:51,440 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:51,440 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:51,442 - models.predict - INFO - Using scikit-learn rf model for 5 minutes horizon
2025-07-17 22:07:51,443 - models.predict - INFO - Loading rf model for COMI with horizon 5
2025-07-17 22:07:51,444 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_5min.joblib or saved_models/COMI_rf_5min.pkl
2025-07-17 22:07:51,444 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-17 22:07:51,445 - models.predict - ERROR - Error in prediction for horizon 5: Model not trained or loaded
2025-07-17 22:07:51,450 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:07:51,452 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:07:51,452 - models.predict - INFO - Prediction for 5 minutes horizon: 89.98
2025-07-17 22:07:51,515 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-17 22:07:51,704 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:51,704 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-17 22:07:51,891 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:51,891 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:51,893 - models.predict - INFO - Using scikit-learn gb model for 5 minutes horizon
2025-07-17 22:07:51,894 - models.predict - INFO - Loading gb model for COMI with horizon 5
2025-07-17 22:07:51,894 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_5min.joblib or saved_models/COMI_gb_5min.pkl
2025-07-17 22:07:51,894 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-17 22:07:51,912 - models.predict - ERROR - Error in prediction for horizon 5: Model not trained or loaded
2025-07-17 22:07:51,913 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:07:51,913 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:07:51,913 - models.predict - INFO - Prediction for 5 minutes horizon: 89.98
2025-07-17 22:07:51,967 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-17 22:07:52,163 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:52,164 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-17 22:07:52,353 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:52,353 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:52,355 - models.predict - INFO - Loading lstm model for COMI with horizon 5
2025-07-17 22:07:52,355 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_5min.keras
2025-07-17 22:07:52,934 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-17 22:07:54,135 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:07:54,135 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.44225189089775085
2025-07-17 22:07:54,136 - models.predict - INFO - Prediction for 5 minutes horizon: 87.76881305187594
2025-07-17 22:07:54,137 - app.models.predict - INFO - Adaptive ensemble prediction for 5min horizon: 89.24293768395864
2025-07-17 22:07:54,137 - app.models.adaptive - INFO - No valid models for COMI with 15min horizon, using equal weights
2025-07-17 22:07:54,138 - app.models.predict - INFO - Ensemble weights for COMI with 15min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 22:07:54,187 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-17 22:07:54,384 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:54,385 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-17 22:07:54,576 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:54,576 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:54,578 - models.predict - INFO - Using scikit-learn rf model for 15 minutes horizon
2025-07-17 22:07:54,578 - models.predict - INFO - Loading rf model for COMI with horizon 15
2025-07-17 22:07:54,578 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_15min.joblib or saved_models/COMI_rf_15min.pkl
2025-07-17 22:07:54,578 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-17 22:07:54,578 - models.predict - ERROR - Error in prediction for horizon 15: Model not trained or loaded
2025-07-17 22:07:54,579 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:07:54,579 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:07:54,579 - models.predict - INFO - Prediction for 15 minutes horizon: 89.98
2025-07-17 22:07:54,632 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-17 22:07:54,815 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:54,816 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-17 22:07:55,007 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:55,007 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:55,009 - models.predict - INFO - Using scikit-learn gb model for 15 minutes horizon
2025-07-17 22:07:55,009 - models.predict - INFO - Loading gb model for COMI with horizon 15
2025-07-17 22:07:55,010 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_15min.joblib or saved_models/COMI_gb_15min.pkl
2025-07-17 22:07:55,010 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-17 22:07:55,028 - models.predict - ERROR - Error in prediction for horizon 15: Model not trained or loaded
2025-07-17 22:07:55,028 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:07:55,029 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:07:55,029 - models.predict - INFO - Prediction for 15 minutes horizon: 89.98
2025-07-17 22:07:55,078 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-17 22:07:55,261 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:55,262 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-17 22:07:55,452 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:55,452 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:55,454 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-07-17 22:07:55,454 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_15min.keras
2025-07-17 22:07:55,616 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-17 22:07:56,119 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:07:56,120 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.4451531171798706
2025-07-17 22:07:56,120 - models.predict - INFO - Prediction for 15 minutes horizon: 88.03061972811561
2025-07-17 22:07:56,121 - app.models.predict - INFO - Adaptive ensemble prediction for 15min horizon: 89.33020657603853
2025-07-17 22:07:56,121 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-07-17 22:07:56,121 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 22:07:56,169 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:07:56,369 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:56,370 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:07:56,560 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:56,560 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:56,563 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-17 22:07:56,563 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-17 22:07:56,563 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_30min.joblib or saved_models/COMI_rf_30min.pkl
2025-07-17 22:07:56,564 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:07:56,564 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 22:07:56,564 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:07:56,565 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:07:56,565 - models.predict - INFO - Prediction for 30 minutes horizon: 89.98
2025-07-17 22:07:56,618 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:07:56,802 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:56,802 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:07:56,993 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:56,993 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:56,995 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-17 22:07:56,995 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-17 22:07:56,995 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_30min.joblib or saved_models/COMI_gb_30min.pkl
2025-07-17 22:07:57,011 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:07:57,030 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 22:07:57,030 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:07:57,031 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:07:57,031 - models.predict - INFO - Prediction for 30 minutes horizon: 89.98
2025-07-17 22:07:57,078 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:07:57,262 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:57,263 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:07:57,453 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:57,454 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:57,455 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-17 22:07:57,455 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_30min.keras
2025-07-17 22:07:57,617 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:07:58,134 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:07:58,134 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.4456096887588501
2025-07-17 22:07:58,135 - models.predict - INFO - Prediction for 30 minutes horizon: 88.07182075000584
2025-07-17 22:07:58,135 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 89.34394025000194
2025-07-17 22:07:58,135 - app.models.predict - INFO - Prediction completed in 7.15 seconds
2025-07-17 22:07:58,135 - app.pages.professional_trading_strategy - INFO - ✅ ENSEMBLE: BEARISH (65.5%) - Pred: 89.31
2025-07-17 22:07:58,136 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 22:07:58,144 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 22:07:58,144 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 22:07:58,147 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 22:07:58,181 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 22:07:58,184 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 22:07:58,187 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 22:07:58,187 - app.pages.professional_trading_strategy - INFO - ✅ Using lstm model with horizons: [5, 15, 30] (weight: 0.196)
2025-07-17 22:07:58,187 - app.models.predict - INFO - Using specified model type: lstm
2025-07-17 22:07:58,235 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-17 22:07:58,437 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:58,437 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-17 22:07:58,629 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:58,629 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:58,630 - models.predict - INFO - Loading lstm model for COMI with horizon 5
2025-07-17 22:07:58,631 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_5min.keras
2025-07-17 22:07:58,756 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-17 22:07:59,253 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:07:59,253 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.44225189089775085
2025-07-17 22:07:59,253 - models.predict - INFO - Prediction for 5 minutes horizon: 87.76881305187594
2025-07-17 22:07:59,253 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-17 22:07:59,442 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:59,443 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-17 22:07:59,639 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:07:59,639 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:07:59,641 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-07-17 22:07:59,641 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_15min.keras
2025-07-17 22:07:59,765 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-17 22:08:00,247 - tensorflow - WARNING - 5 out of the last 5 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x14948f2e0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-07-17 22:08:00,265 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:08:00,265 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.4451531171798706
2025-07-17 22:08:00,265 - models.predict - INFO - Prediction for 15 minutes horizon: 88.03061972811561
2025-07-17 22:08:00,265 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:08:00,467 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:00,467 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:08:00,660 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:00,660 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:00,662 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-17 22:08:00,662 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_30min.keras
2025-07-17 22:08:00,783 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:08:01,269 - tensorflow - WARNING - 6 out of the last 6 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x1491df910> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-07-17 22:08:01,287 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:08:01,287 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.4456096887588501
2025-07-17 22:08:01,288 - models.predict - INFO - Prediction for 30 minutes horizon: 88.07182075000584
2025-07-17 22:08:01,288 - app.models.predict - INFO - Prediction completed in 3.10 seconds
2025-07-17 22:08:01,288 - app.pages.professional_trading_strategy - INFO - ✅ LSTM: BEARISH (87.3%) - Pred: 87.96
2025-07-17 22:08:01,289 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 22:08:01,289 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 22:08:01,289 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 22:08:01,289 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 22:08:01,292 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 22:08:01,294 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 22:08:01,294 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 22:08:01,294 - app.pages.professional_trading_strategy - INFO - ✅ Using gb model with horizons: [5, 15, 30] (weight: 0.157)
2025-07-17 22:08:01,294 - app.models.predict - INFO - Using specified model type: gb
2025-07-17 22:08:01,354 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-17 22:08:01,571 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:01,571 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-17 22:08:01,759 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:01,759 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:01,762 - models.predict - INFO - Using scikit-learn gb model for 5 minutes horizon
2025-07-17 22:08:01,762 - models.predict - INFO - Loading gb model for COMI with horizon 5
2025-07-17 22:08:01,763 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_5min.joblib or saved_models/COMI_gb_5min.pkl
2025-07-17 22:08:01,763 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-17 22:08:01,782 - models.predict - ERROR - Error in prediction for horizon 5: Model not trained or loaded
2025-07-17 22:08:01,783 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:01,783 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:01,783 - models.predict - INFO - Prediction for 5 minutes horizon: 89.98
2025-07-17 22:08:01,783 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-17 22:08:01,970 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:01,971 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-17 22:08:02,160 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:02,161 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:02,163 - models.predict - INFO - Using scikit-learn gb model for 15 minutes horizon
2025-07-17 22:08:02,163 - models.predict - INFO - Loading gb model for COMI with horizon 15
2025-07-17 22:08:02,163 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_15min.joblib or saved_models/COMI_gb_15min.pkl
2025-07-17 22:08:02,163 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-17 22:08:02,182 - models.predict - ERROR - Error in prediction for horizon 15: Model not trained or loaded
2025-07-17 22:08:02,183 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:02,183 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:02,183 - models.predict - INFO - Prediction for 15 minutes horizon: 89.98
2025-07-17 22:08:02,183 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:08:02,368 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:02,369 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:08:02,558 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:02,559 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:02,560 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-17 22:08:02,561 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-17 22:08:02,561 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_30min.joblib or saved_models/COMI_gb_30min.pkl
2025-07-17 22:08:02,561 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:08:02,580 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 22:08:02,580 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:02,581 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:02,581 - models.predict - INFO - Prediction for 30 minutes horizon: 89.98
2025-07-17 22:08:02,581 - app.models.predict - INFO - Prediction completed in 1.29 seconds
2025-07-17 22:08:02,581 - app.pages.professional_trading_strategy - INFO - ✅ GB: BEARISH (54.7%) - Pred: 89.98
2025-07-17 22:08:02,582 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 22:08:02,582 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 22:08:02,582 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:08:02,582 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:08:02,584 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:08:02,587 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:08:02,588 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 22:08:02,588 - app.pages.professional_trading_strategy - INFO - ✅ Using rf model with horizons: [5, 15, 30] (weight: 0.152)
2025-07-17 22:08:02,588 - app.models.predict - INFO - Using specified model type: rf
2025-07-17 22:08:02,635 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-17 22:08:02,825 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:02,826 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-17 22:08:03,037 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:03,038 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:03,040 - models.predict - INFO - Using scikit-learn rf model for 5 minutes horizon
2025-07-17 22:08:03,040 - models.predict - INFO - Loading rf model for COMI with horizon 5
2025-07-17 22:08:03,040 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_5min.joblib or saved_models/COMI_rf_5min.pkl
2025-07-17 22:08:03,040 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-17 22:08:03,040 - models.predict - ERROR - Error in prediction for horizon 5: Model not trained or loaded
2025-07-17 22:08:03,041 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:03,041 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:03,041 - models.predict - INFO - Prediction for 5 minutes horizon: 89.98
2025-07-17 22:08:03,044 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-17 22:08:03,229 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:03,229 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-17 22:08:03,419 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:03,419 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:03,421 - models.predict - INFO - Using scikit-learn rf model for 15 minutes horizon
2025-07-17 22:08:03,421 - models.predict - INFO - Loading rf model for COMI with horizon 15
2025-07-17 22:08:03,421 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_15min.joblib or saved_models/COMI_rf_15min.pkl
2025-07-17 22:08:03,422 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-17 22:08:03,422 - models.predict - ERROR - Error in prediction for horizon 15: Model not trained or loaded
2025-07-17 22:08:03,422 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:03,422 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:03,423 - models.predict - INFO - Prediction for 15 minutes horizon: 89.98
2025-07-17 22:08:03,423 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:08:03,611 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:03,611 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:08:03,807 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:03,807 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:03,809 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-17 22:08:03,809 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-17 22:08:03,809 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_30min.joblib or saved_models/COMI_rf_30min.pkl
2025-07-17 22:08:03,810 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:08:03,810 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 22:08:03,810 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:03,810 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:03,811 - models.predict - INFO - Prediction for 30 minutes horizon: 89.98
2025-07-17 22:08:03,811 - app.models.predict - INFO - Prediction completed in 1.22 seconds
2025-07-17 22:08:03,812 - app.pages.professional_trading_strategy - INFO - ✅ RF: BEARISH (54.2%) - Pred: 89.98
2025-07-17 22:08:03,812 - app.utils.data_processing - INFO - Found svr model for COMI with 5 minutes horizon
2025-07-17 22:08:03,812 - app.utils.data_processing - INFO - Found svr model for COMI with 15 minutes horizon
2025-07-17 22:08:03,813 - app.utils.data_processing - INFO - Found svr model for COMI with 30 minutes horizon
2025-07-17 22:08:03,813 - app.utils.data_processing - INFO - Found svr model for COMI with 60 minutes horizon
2025-07-17 22:08:03,816 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 22:08:03,820 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 22:08:03,820 - app.utils.data_processing - INFO - Found svr model for COMI with 1440 minutes horizon
2025-07-17 22:08:03,820 - app.pages.professional_trading_strategy - INFO - ✅ Using svr model with horizons: [5, 15, 30] (weight: 0.148)
2025-07-17 22:08:03,821 - app.models.predict - INFO - Using specified model type: svr
2025-07-17 22:08:03,869 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-17 22:08:04,053 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:04,054 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-17 22:08:04,244 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:04,244 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:04,246 - models.predict - INFO - Using scikit-learn svr model for 5 minutes horizon
2025-07-17 22:08:04,246 - models.predict - INFO - Loading svr model for COMI with horizon 5
2025-07-17 22:08:04,246 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_svr_5min.joblib or saved_models/COMI_svr_5min.pkl
2025-07-17 22:08:04,246 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-17 22:08:04,246 - models.predict - ERROR - Error in prediction for horizon 5: Model not trained or loaded
2025-07-17 22:08:04,246 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:04,247 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:04,247 - models.predict - INFO - Prediction for 5 minutes horizon: 89.98
2025-07-17 22:08:04,247 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-17 22:08:04,434 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:04,434 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-17 22:08:04,625 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:04,625 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:04,627 - models.predict - INFO - Using scikit-learn svr model for 15 minutes horizon
2025-07-17 22:08:04,627 - models.predict - INFO - Loading svr model for COMI with horizon 15
2025-07-17 22:08:04,627 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_svr_15min.joblib or saved_models/COMI_svr_15min.pkl
2025-07-17 22:08:04,627 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-17 22:08:04,628 - models.predict - ERROR - Error in prediction for horizon 15: Model not trained or loaded
2025-07-17 22:08:04,628 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:04,628 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:04,628 - models.predict - INFO - Prediction for 15 minutes horizon: 89.98
2025-07-17 22:08:04,628 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:08:04,818 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:04,818 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:08:05,008 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:05,009 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:05,010 - models.predict - INFO - Using scikit-learn svr model for 30 minutes horizon
2025-07-17 22:08:05,010 - models.predict - INFO - Loading svr model for COMI with horizon 30
2025-07-17 22:08:05,010 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_svr_30min.joblib or saved_models/COMI_svr_30min.pkl
2025-07-17 22:08:05,011 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:08:05,011 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 22:08:05,011 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:05,011 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:05,011 - models.predict - INFO - Prediction for 30 minutes horizon: 89.98
2025-07-17 22:08:05,012 - app.models.predict - INFO - Prediction completed in 1.19 seconds
2025-07-17 22:08:05,012 - app.pages.professional_trading_strategy - INFO - ✅ SVR: BEARISH (53.8%) - Pred: 89.98
2025-07-17 22:08:05,012 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 22:08:05,012 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 22:08:05,013 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 22:08:05,013 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 22:08:05,017 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 22:08:05,020 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 22:08:05,021 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 22:08:05,021 - app.pages.professional_trading_strategy - INFO - ✅ Using lr model with horizons: [5, 15, 30] (weight: 0.144)
2025-07-17 22:08:05,021 - app.models.predict - INFO - Using specified model type: lr
2025-07-17 22:08:05,069 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-17 22:08:05,253 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:05,254 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-17 22:08:05,444 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:05,444 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:05,446 - models.predict - INFO - Using scikit-learn lr model for 5 minutes horizon
2025-07-17 22:08:05,446 - models.predict - INFO - Loading lr model for COMI with horizon 5
2025-07-17 22:08:05,446 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_5min.joblib or saved_models/COMI_lr_5min.pkl
2025-07-17 22:08:05,446 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-17 22:08:05,447 - models.predict - ERROR - Error in prediction for horizon 5: Model not trained or loaded
2025-07-17 22:08:05,447 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:05,447 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:05,447 - models.predict - INFO - Prediction for 5 minutes horizon: 89.98
2025-07-17 22:08:05,447 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-17 22:08:05,636 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:05,636 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-17 22:08:05,842 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:05,843 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:05,845 - models.predict - INFO - Using scikit-learn lr model for 15 minutes horizon
2025-07-17 22:08:05,845 - models.predict - INFO - Loading lr model for COMI with horizon 15
2025-07-17 22:08:05,845 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_15min.joblib or saved_models/COMI_lr_15min.pkl
2025-07-17 22:08:05,845 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-17 22:08:05,845 - models.predict - ERROR - Error in prediction for horizon 15: Model not trained or loaded
2025-07-17 22:08:05,845 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:05,846 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:05,846 - models.predict - INFO - Prediction for 15 minutes horizon: 89.98
2025-07-17 22:08:05,846 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:08:06,035 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:06,035 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:08:06,224 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:06,224 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:08:06,226 - models.predict - INFO - Using scikit-learn lr model for 30 minutes horizon
2025-07-17 22:08:06,226 - models.predict - INFO - Loading lr model for COMI with horizon 30
2025-07-17 22:08:06,226 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_30min.joblib or saved_models/COMI_lr_30min.pkl
2025-07-17 22:08:06,226 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:08:06,227 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 22:08:06,227 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:08:06,227 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 22:08:06,227 - models.predict - INFO - Prediction for 30 minutes horizon: 89.98
2025-07-17 22:08:06,228 - app.models.predict - INFO - Prediction completed in 1.21 seconds
2025-07-17 22:08:06,228 - app.pages.professional_trading_strategy - INFO - ✅ LR: BEARISH (53.4%) - Pred: 89.98
2025-07-17 22:08:06,228 - app.pages.professional_trading_strategy - INFO - ✅ Enhanced AI Consensus: 0/6 models predict BEARISH (0%)
2025-07-17 22:08:06,228 - app.pages.professional_trading_strategy - INFO - 📊 Market Regime: NEUTRAL | Volatility: 0.33
2025-07-17 22:08:06,279 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-07-17 22:08:06,327 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.130
2025-07-17 22:08:06,397 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:06,397 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:08:06,584 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:08:06,585 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:06,586 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:08:57,070 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:08:57,099 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 22:08:57,115 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:57,117 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:08:57,313 - app.utils.memory_management - INFO - Garbage collection: collected 1015 objects
2025-07-17 22:08:57,315 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:57,315 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:08:57,779 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:08:57,845 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:57,845 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:08:58,059 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 22:08:58,060 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:08:58,061 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:09:01,565 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:09:01,709 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:01,710 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:09:01,899 - app.utils.memory_management - INFO - Garbage collection: collected 986 objects
2025-07-17 22:09:01,900 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:01,900 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:09:26,713 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:09:26,795 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:26,795 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:09:27,011 - app.utils.memory_management - INFO - Garbage collection: collected 1084 objects
2025-07-17 22:09:27,013 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:27,013 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:09:27,451 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:09:27,514 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:27,514 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:09:27,777 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 22:09:27,778 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:27,778 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:09:30,825 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:09:30,977 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:30,977 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:09:31,180 - app.utils.memory_management - INFO - Garbage collection: collected 689 objects
2025-07-17 22:09:31,181 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:31,182 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:09:46,622 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:09:46,656 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:46,657 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:09:46,873 - app.utils.memory_management - INFO - Garbage collection: collected 958 objects
2025-07-17 22:09:46,873 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:46,873 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:09:47,316 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:09:47,409 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:47,410 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:09:47,670 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 22:09:47,670 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:09:47,670 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:10:02,491 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:10:02,541 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-07-17 22:10:02,551 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.297
2025-07-17 22:10:02,744 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-17 22:10:02,833 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-17 22:10:02,834 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-07-17 22:10:02,834 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-17 22:10:02,835 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (51.1)
2025-07-17 22:10:02,835 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-17 22:10:22,534 - app.utils.ai_pattern_recognition - INFO - Using live price 51.80 EGP from API for ABUK
2025-07-17 22:10:22,534 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-07-17 22:10:22,535 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-07-17 22:10:22,535 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-07-17 22:10:22,537 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-17 22:10:22,569 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-07-17 22:10:22,569 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.9478402644919861, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 47.4 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 47.3920132245993, 'bearish_score': 0}
2025-07-17 22:10:22,569 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-07-17 22:10:23,378 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:10:23,378 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:10:23,593 - app.utils.memory_management - INFO - Garbage collection: collected 1550 objects
2025-07-17 22:10:23,593 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:10:23,594 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:10:51,406 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:10:51,524 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-07-17 22:10:51,562 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.165
2025-07-17 22:10:51,655 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-17 22:10:51,718 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-17 22:10:51,719 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.97%) exceeds limit (15.00%)
2025-07-17 22:10:51,719 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-17 22:10:51,719 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (54.3)
2025-07-17 22:10:51,719 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-17 22:11:07,414 - app.utils.ai_pattern_recognition - INFO - Using live price 89.98 EGP from API for COMI
2025-07-17 22:11:07,415 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-07-17 22:11:07,415 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-07-17 22:11:07,417 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-17 22:11:07,448 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-07-17 22:11:07,448 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.6133782399865089, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 53.2 vs 22.5)', 'risk_level': 'Low', 'bullish_score': 53.168911999325445, 'bearish_score': 22.5}
2025-07-17 22:11:07,448 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=89.53, stop=85.05, target=100.72
2025-07-17 22:11:07,626 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:11:07,627 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:11:07,830 - app.utils.memory_management - INFO - Garbage collection: collected 2551 objects
2025-07-17 22:11:07,830 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:11:07,831 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:12:23,248 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:12:23,287 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:12:23,288 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:12:23,514 - app.utils.memory_management - INFO - Garbage collection: collected 1747 objects
2025-07-17 22:12:23,514 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:12:23,515 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:12:27,583 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:12:27,602 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:12:27,603 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:12:27,797 - app.utils.memory_management - INFO - Garbage collection: collected 696 objects
2025-07-17 22:12:27,798 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:12:27,799 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:12:28,915 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:12:28,949 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-07-17 22:12:28,997 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-11-08 to 2025-07-17
2025-07-17 22:12:28,998 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-07-17 22:12:43,240 - app.pages.smc_analysis - INFO - ✅ Using live price from API: 89.98 EGP for COMI
2025-07-17 22:12:44,415 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:12:44,415 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:12:44,629 - app.utils.memory_management - INFO - Garbage collection: collected 699 objects
2025-07-17 22:12:44,629 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:12:44,630 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:13:52,065 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:13:52,128 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:13:52,128 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:13:52,336 - app.utils.memory_management - INFO - Garbage collection: collected 2426 objects
2025-07-17 22:13:52,338 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:13:52,338 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:13:57,543 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:13:57,561 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 22:13:57,582 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:13:57,582 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:13:57,789 - app.utils.memory_management - INFO - Garbage collection: collected 766 objects
2025-07-17 22:13:57,790 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:13:57,790 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:13:59,911 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:13:59,955 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:13:59,955 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:14:00,156 - app.utils.memory_management - INFO - Garbage collection: collected 772 objects
2025-07-17 22:14:00,158 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:14:00,158 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:14:04,874 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:14:04,928 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:14:04,928 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:14:05,140 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 22:14:05,142 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:14:05,143 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:14:06,105 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:14:16,676 - app.pages.advanced_technical_analysis - INFO - Loaded 783 days of historical data for COMI
2025-07-17 22:14:16,778 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:14:16,779 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:14:17,013 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 22:14:17,014 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:14:17,015 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:15:12,647 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:15:12,690 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:15:12,690 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:15:12,920 - app.utils.memory_management - INFO - Garbage collection: collected 1746 objects
2025-07-17 22:15:12,920 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:15:12,921 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:15:15,178 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:15:15,222 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:15:15,223 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:15:15,438 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 22:15:15,439 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:15:15,440 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:15:18,357 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:15:37,178 - app.pages.advanced_technical_analysis - INFO - Loaded 783 days of historical data for COMI
2025-07-17 22:15:37,270 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:15:37,270 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:15:37,511 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 22:15:37,512 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:15:37,513 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:17:07,203 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:17:07,490 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:07,490 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:17:07,728 - app.utils.memory_management - INFO - Garbage collection: collected 2776 objects
2025-07-17 22:17:07,729 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:07,730 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:17:15,702 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:17:15,769 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:17:15,810 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:17:16,018 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:16,021 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:17:16,251 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:16,251 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 22:17:16,253 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-17 22:17:16,349 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-17 22:17:16,349 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-17 22:17:16,350 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-17 22:17:16,350 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:17:16,382 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:17:16,382 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.46183096509095045
2025-07-17 22:17:16,382 - models.predict - INFO - Prediction for 30 minutes horizon: 89.53562881869932
2025-07-17 22:17:16,384 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:17:16,410 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 22:17:16,619 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:16,620 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 22:17:16,831 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:16,831 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:17:16,833 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 22:17:16,911 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 22:17:16,911 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 22:17:16,911 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 22:17:16,911 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:17:16,939 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:17:16,939 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.46183096509095045
2025-07-17 22:17:16,940 - models.predict - INFO - Prediction for 60 minutes horizon: 89.53562881869932
2025-07-17 22:17:16,953 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:17:16,955 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 22:17:16,959 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 22:17:16,962 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 22:17:16,965 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 22:17:16,968 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 22:17:16,971 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 22:17:16,974 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 22:17:16,977 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:17:16,981 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:17:16,984 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 22:17:16,987 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 22:17:16,990 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 22:17:16,994 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 22:17:16,997 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 22:17:17,000 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 22:17:17,002 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 22:17:17,006 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:17:17,006 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 22:17:17,029 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 22:17:17,227 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:17,228 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 22:17:17,424 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:17,424 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:17:17,426 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-17 22:17:17,512 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-17 22:17:17,512 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-17 22:17:17,512 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-17 22:17:17,512 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:17:17,540 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:17:17,540 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.46183096509095045
2025-07-17 22:17:17,541 - models.predict - INFO - Prediction for 1440 minutes horizon: 89.53562881869932
2025-07-17 22:17:17,771 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:17,771 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:17:17,974 - app.utils.memory_management - INFO - Garbage collection: collected 909 objects
2025-07-17 22:17:17,975 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:17:17,976 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:28:41,117 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 22:28:42,140 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 22:28:42,140 - app - INFO - Memory management utilities loaded
2025-07-17 22:28:42,143 - app - INFO - Error handling utilities loaded
2025-07-17 22:28:42,144 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 22:28:42,144 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 22:28:42,144 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 22:28:42,144 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 22:28:42,146 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 22:28:42,146 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 22:28:42,146 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 22:28:42,147 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 22:28:42,147 - app - INFO - Applied NumPy fix
2025-07-17 22:28:42,148 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:28:42,148 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:28:42,148 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:28:42,148 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 22:28:42,148 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:28:42,149 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:28:42,149 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:28:42,149 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 22:28:52,414 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 22:28:52,415 - app - INFO - Applied TensorFlow fix
2025-07-17 22:28:53,376 - app - INFO - Cleaning up resources...
2025-07-17 22:28:53,376 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:28:53,377 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:28:53,590 - app.utils.memory_management - INFO - Garbage collection: collected 45 objects
2025-07-17 22:28:53,590 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:28:53,590 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:28:53,590 - app - INFO - Application shutdown complete
2025-07-17 22:29:32,778 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 22:29:33,727 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 22:29:33,728 - app - INFO - Memory management utilities loaded
2025-07-17 22:29:33,730 - app - INFO - Error handling utilities loaded
2025-07-17 22:29:33,733 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 22:29:33,734 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 22:29:33,734 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 22:29:33,734 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 22:29:33,735 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 22:29:33,735 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 22:29:33,735 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 22:29:33,735 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 22:29:33,735 - app - INFO - Applied NumPy fix
2025-07-17 22:29:33,736 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:29:33,736 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:29:33,736 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:29:33,736 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 22:29:33,736 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:29:33,736 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:29:33,737 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:29:33,737 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 22:29:43,795 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 22:29:43,796 - app - INFO - Applied TensorFlow fix
2025-07-17 22:29:44,796 - app - INFO - Cleaning up resources...
2025-07-17 22:29:44,796 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:29:44,798 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:29:44,967 - app.utils.memory_management - INFO - Garbage collection: collected 45 objects
2025-07-17 22:29:44,968 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:29:44,968 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:29:44,968 - app - INFO - Application shutdown complete
2025-07-17 22:30:10,138 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 22:30:11,066 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 22:30:11,067 - app - INFO - Memory management utilities loaded
2025-07-17 22:30:11,069 - app - INFO - Error handling utilities loaded
2025-07-17 22:30:11,070 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 22:30:11,071 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 22:30:11,071 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 22:30:11,071 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 22:30:11,073 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 22:30:11,073 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 22:30:11,073 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 22:30:11,073 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 22:30:11,073 - app - INFO - Applied NumPy fix
2025-07-17 22:30:11,074 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:30:11,074 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:30:11,074 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:30:11,074 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 22:30:11,075 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:30:11,075 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:30:11,075 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:30:11,075 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 22:30:21,598 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 22:30:21,603 - app - INFO - Applied TensorFlow fix
2025-07-17 22:30:22,773 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 22:30:22,773 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 22:30:22,774 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 22:30:22,777 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 22:30:23,423 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 22:30:23,423 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 22:30:23,423 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:30:23,424 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:30:23,424 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:30:23,424 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 22:30:23,424 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 22:30:23,424 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:30:23,424 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:30:23,424 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:30:23,663 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 22:30:48,853 - app.utils.backtesting - INFO - Preparing features for backtesting...
2025-07-17 22:30:48,877 - app.utils.backtesting - INFO - Added technical indicators. New shape: (365, 41)
2025-07-17 22:30:48,901 - app.utils.backtesting - INFO - Prepared features. Final shape: (365, 56)
2025-07-17 22:30:48,901 - app.utils.backtesting - INFO - Starting backtesting with 10 days and 8 features
2025-07-17 22:30:48,901 - app.utils.backtesting - INFO - Model type detection: sklearn=False, hybrid=False
2025-07-17 22:30:48,903 - app.utils.backtesting - WARNING - 3D reshape failed for day 0, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,903 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 0: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,904 - app.utils.backtesting - WARNING - 3D reshape failed for day 1, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,904 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 1: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,905 - app.utils.backtesting - WARNING - 3D reshape failed for day 2, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,906 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 2: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,910 - app.utils.backtesting - WARNING - 3D reshape failed for day 3, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,910 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 3: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,912 - app.utils.backtesting - WARNING - 3D reshape failed for day 4, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,912 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 4: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,913 - app.utils.backtesting - WARNING - 3D reshape failed for day 5, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,913 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 5: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,915 - app.utils.backtesting - WARNING - 3D reshape failed for day 6, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,915 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 6: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,918 - app.utils.backtesting - WARNING - 3D reshape failed for day 7, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,918 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 7: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,919 - app.utils.backtesting - WARNING - 3D reshape failed for day 8, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,919 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 8: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,921 - app.utils.backtesting - WARNING - 3D reshape failed for day 9, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,921 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 9: 'NoneType' object has no attribute 'predict'
2025-07-17 22:30:48,921 - app.utils.backtesting - ERROR - No valid predictions were made during backtesting
2025-07-17 22:30:48,922 - app.utils.backtesting - WARNING - Created 5 dummy results for visualization purposes
2025-07-17 22:30:48,949 - app - INFO - Cleaning up resources...
2025-07-17 22:30:48,949 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:30:48,950 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:30:49,124 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:30:49,124 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:30:49,124 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:30:49,124 - app - INFO - Application shutdown complete
2025-07-17 22:32:08,153 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 22:32:08,169 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 22:32:08,170 - app - INFO - Memory management utilities loaded
2025-07-17 22:32:08,172 - app - INFO - Error handling utilities loaded
2025-07-17 22:32:08,173 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 22:32:08,174 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 22:32:08,174 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 22:32:08,174 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 22:32:38,407 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 22:32:40,353 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 22:32:40,353 - app - INFO - Memory management utilities loaded
2025-07-17 22:32:40,355 - app - INFO - Error handling utilities loaded
2025-07-17 22:32:40,356 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 22:32:40,357 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 22:32:40,357 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 22:32:40,357 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 22:32:40,358 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 22:32:40,359 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 22:32:40,359 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 22:32:40,359 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 22:32:40,359 - app - INFO - Applied NumPy fix
2025-07-17 22:32:40,360 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:32:40,360 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:32:40,360 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:32:40,360 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 22:32:40,360 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:32:40,360 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:32:40,360 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:32:40,360 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 22:32:56,042 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 22:32:56,043 - app - INFO - Applied TensorFlow fix
2025-07-17 22:32:56,049 - app.config - INFO - Configuration initialized
2025-07-17 22:32:56,062 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-17 22:32:56,397 - models.train - INFO - TensorFlow test successful
2025-07-17 22:32:56,482 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 22:32:56,482 - models.train - INFO - Transformer model is available
2025-07-17 22:32:56,483 - models.train - INFO - Using TensorFlow-based models
2025-07-17 22:32:56,489 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 22:32:56,490 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 22:32:56,493 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 22:32:57,632 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 22:32:57,632 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 22:32:57,632 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:32:57,632 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:32:57,632 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:32:57,633 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 22:32:57,633 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 22:32:57,633 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:32:57,633 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:32:57,633 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:32:57,897 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 22:32:57,899 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:32:58,605 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-17 22:33:00,109 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-17 22:33:00,109 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-17 22:33:00,324 - app.utils.session_state - INFO - Initializing session state
2025-07-17 22:33:00,326 - app.utils.session_state - INFO - Session state initialized
2025-07-17 22:33:00,989 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 22:33:01,004 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:01,005 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:33:01,251 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:33:01,252 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:01,253 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:33:14,046 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:33:14,066 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:14,066 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:33:14,265 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:33:14,266 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:14,270 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:33:15,654 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:33:15,743 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.06 seconds
2025-07-17 22:33:15,746 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:33:15,746 - app - INFO - Data shape: (1250, 36)
2025-07-17 22:33:15,746 - app - INFO - File COMI contains 2025 data
2025-07-17 22:33:15,777 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-07-17 22:33:15,777 - app - INFO - Features shape: (1250, 36)
2025-07-17 22:33:15,807 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-17 22:33:15,808 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:33:15,808 - app - INFO - Data shape: (1250, 36)
2025-07-17 22:33:15,808 - app - INFO - File COMI contains 2025 data
2025-07-17 22:33:15,812 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:15,812 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:33:16,006 - app.utils.memory_management - INFO - Garbage collection: collected 730 objects
2025-07-17 22:33:16,007 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:16,007 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:33:16,410 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:33:16,473 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:16,473 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:33:16,701 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 22:33:16,702 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:16,702 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:33:19,036 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:33:41,813 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:41,814 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:33:42,054 - app.utils.memory_management - INFO - Garbage collection: collected 1894 objects
2025-07-17 22:33:42,054 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:33:42,055 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:33:57,100 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:34:06,264 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:34:06,316 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:34:06,503 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:34:06,504 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:34:06,701 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:34:06,702 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 22:34:06,704 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-17 22:34:07,000 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-17 22:34:07,000 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-17 22:34:07,001 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-17 22:34:07,001 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:34:07,037 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:34:07,037 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:34:07,038 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:34:07,038 - models.predict - INFO - Prediction for 30 minutes horizon: 91597.54267035739
2025-07-17 22:34:07,040 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:34:07,064 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 22:34:07,269 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:34:07,270 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 22:34:07,460 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:34:07,461 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:34:07,462 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 22:34:07,579 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 22:34:07,580 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 22:34:07,580 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 22:34:07,580 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:34:07,608 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:34:07,609 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:34:07,609 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:34:07,609 - models.predict - INFO - Prediction for 60 minutes horizon: 89527.03338394673
2025-07-17 22:34:07,634 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:34:07,637 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 22:34:07,640 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 22:34:07,643 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 22:34:07,646 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 22:34:07,649 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 22:34:07,652 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 22:34:07,655 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 22:34:07,659 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:34:07,664 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:34:07,667 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 22:34:07,670 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 22:34:07,672 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 22:34:07,676 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 22:34:07,680 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 22:34:07,682 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 22:34:07,686 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 22:34:07,689 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:34:07,690 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 22:34:07,714 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 22:34:07,911 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:34:07,912 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 22:34:08,105 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:34:08,105 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:34:08,107 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-17 22:34:08,247 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-17 22:34:08,247 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-17 22:34:08,247 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-17 22:34:08,247 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:34:08,275 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:34:08,276 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:34:08,276 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:34:08,276 - models.predict - INFO - Prediction for 1440 minutes horizon: 89795.11805795057
2025-07-17 22:34:08,541 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:34:08,545 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:34:08,742 - app.utils.memory_management - INFO - Garbage collection: collected 1011 objects
2025-07-17 22:34:08,743 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:34:08,744 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:35:42,771 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:35:52,367 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:35:52,370 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:35:52,575 - app.utils.memory_management - INFO - Garbage collection: collected 2792 objects
2025-07-17 22:35:52,576 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:35:52,576 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:35:54,122 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:36:11,764 - app.utils.backtesting - INFO - Preparing features for backtesting...
2025-07-17 22:36:11,783 - app.utils.backtesting - INFO - Added technical indicators. New shape: (1250, 56)
2025-07-17 22:36:11,813 - app.utils.backtesting - INFO - Prepared features. Final shape: (1250, 56)
2025-07-17 22:36:11,814 - app.utils.backtesting - INFO - Starting backtesting with 30 days and 8 features
2025-07-17 22:36:11,814 - app.utils.backtesting - INFO - Model type detection: sklearn=False, hybrid=False
2025-07-17 22:36:11,816 - app.utils.backtesting - WARNING - 3D reshape failed for day 0, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,817 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 0: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,826 - app.utils.backtesting - WARNING - 3D reshape failed for day 1, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,826 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 1: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,828 - app.utils.backtesting - WARNING - 3D reshape failed for day 2, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,828 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 2: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,829 - app.utils.backtesting - WARNING - 3D reshape failed for day 3, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,830 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 3: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,834 - app.utils.backtesting - WARNING - 3D reshape failed for day 4, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,834 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 4: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,836 - app.utils.backtesting - WARNING - 3D reshape failed for day 5, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,836 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 5: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,838 - app.utils.backtesting - WARNING - 3D reshape failed for day 6, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,838 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 6: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,843 - app.utils.backtesting - WARNING - 3D reshape failed for day 7, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,844 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 7: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,846 - app.utils.backtesting - WARNING - 3D reshape failed for day 8, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,846 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 8: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,848 - app.utils.backtesting - WARNING - 3D reshape failed for day 9, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,849 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 9: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,851 - app.utils.backtesting - WARNING - 3D reshape failed for day 10, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,852 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 10: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,853 - app.utils.backtesting - WARNING - 3D reshape failed for day 11, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,854 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 11: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,859 - app.utils.backtesting - WARNING - 3D reshape failed for day 12, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,860 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 12: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,862 - app.utils.backtesting - WARNING - 3D reshape failed for day 13, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,862 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 13: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,864 - app.utils.backtesting - WARNING - 3D reshape failed for day 14, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,865 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 14: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,867 - app.utils.backtesting - WARNING - 3D reshape failed for day 15, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,868 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 15: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,869 - app.utils.backtesting - WARNING - 3D reshape failed for day 16, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,870 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 16: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,873 - app.utils.backtesting - WARNING - 3D reshape failed for day 17, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,873 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 17: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,875 - app.utils.backtesting - WARNING - 3D reshape failed for day 18, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,875 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 18: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,877 - app.utils.backtesting - WARNING - 3D reshape failed for day 19, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,877 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 19: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,880 - app.utils.backtesting - WARNING - 3D reshape failed for day 20, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,880 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 20: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,882 - app.utils.backtesting - WARNING - 3D reshape failed for day 21, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,882 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 21: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,884 - app.utils.backtesting - WARNING - 3D reshape failed for day 22, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,884 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 22: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,887 - app.utils.backtesting - WARNING - 3D reshape failed for day 23, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,888 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 23: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,890 - app.utils.backtesting - WARNING - 3D reshape failed for day 24, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,890 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 24: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,892 - app.utils.backtesting - WARNING - 3D reshape failed for day 25, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,893 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 25: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,895 - app.utils.backtesting - WARNING - 3D reshape failed for day 26, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,895 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 26: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,897 - app.utils.backtesting - WARNING - 3D reshape failed for day 27, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,897 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 27: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,902 - app.utils.backtesting - WARNING - 3D reshape failed for day 28, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,902 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 28: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,904 - app.utils.backtesting - WARNING - 3D reshape failed for day 29, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,908 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 29: 'NoneType' object has no attribute 'predict'
2025-07-17 22:36:11,908 - app.utils.backtesting - ERROR - No valid predictions were made during backtesting
2025-07-17 22:36:11,911 - app.utils.backtesting - WARNING - Created 5 dummy results for visualization purposes
2025-07-17 22:36:11,983 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:36:11,983 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:36:12,175 - app.utils.memory_management - INFO - Garbage collection: collected 3099 objects
2025-07-17 22:36:12,176 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:36:12,177 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:41:34,326 - app - INFO - Cleaning up resources...
2025-07-17 22:41:34,333 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:41:34,334 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:41:34,752 - app.utils.memory_management - INFO - Garbage collection: collected 1952 objects
2025-07-17 22:41:34,753 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:41:34,754 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:41:34,754 - app - INFO - Application shutdown complete
2025-07-17 22:41:37,607 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 22:41:38,410 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 22:41:38,410 - app - INFO - Memory management utilities loaded
2025-07-17 22:41:38,412 - app - INFO - Error handling utilities loaded
2025-07-17 22:41:38,413 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 22:41:38,415 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 22:41:38,416 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 22:41:38,416 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 22:41:38,416 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 22:41:38,416 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 22:41:38,417 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 22:41:38,417 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 22:41:38,417 - app - INFO - Applied NumPy fix
2025-07-17 22:41:38,417 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:41:38,418 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:41:38,418 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:41:38,418 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 22:41:38,418 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:41:38,418 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:41:38,418 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:41:38,418 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 22:42:36,746 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 22:42:37,761 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 22:42:37,761 - app - INFO - Memory management utilities loaded
2025-07-17 22:42:37,762 - app - INFO - Error handling utilities loaded
2025-07-17 22:42:37,762 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 22:42:37,763 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 22:42:37,763 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 22:42:37,763 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 22:42:37,763 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 22:42:37,764 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 22:42:37,764 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 22:42:37,764 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 22:42:37,764 - app - INFO - Applied NumPy fix
2025-07-17 22:42:37,765 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:42:37,765 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:42:37,765 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:42:37,765 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 22:42:37,765 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:42:37,765 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:42:37,765 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:42:37,765 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 22:42:50,252 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 22:42:50,253 - app - INFO - Applied TensorFlow fix
2025-07-17 22:42:50,256 - app.config - INFO - Configuration initialized
2025-07-17 22:42:50,286 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-17 22:42:50,312 - models.train - INFO - TensorFlow test successful
2025-07-17 22:42:50,390 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 22:42:50,390 - models.train - INFO - Transformer model is available
2025-07-17 22:42:50,390 - models.train - INFO - Using TensorFlow-based models
2025-07-17 22:42:50,397 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 22:42:50,397 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 22:42:50,407 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 22:42:51,582 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 22:42:51,582 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 22:42:51,582 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 22:42:51,583 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 22:42:51,583 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 22:42:51,583 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 22:42:51,583 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 22:42:51,583 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 22:42:51,584 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 22:42:51,584 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 22:42:51,897 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 22:42:51,899 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:42:52,608 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-17 22:42:54,339 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-17 22:42:54,339 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-17 22:42:54,628 - app.utils.session_state - INFO - Initializing session state
2025-07-17 22:42:54,631 - app.utils.session_state - INFO - Session state initialized
2025-07-17 22:42:55,313 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 22:42:55,327 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:42:55,327 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:42:55,555 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:42:55,556 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:42:55,557 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:43:04,512 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:43:04,534 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:43:04,534 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:43:04,736 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 22:43:04,736 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:43:04,736 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:43:06,344 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:43:06,420 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.05 seconds
2025-07-17 22:43:06,421 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:43:06,421 - app - INFO - Data shape: (1250, 36)
2025-07-17 22:43:06,421 - app - INFO - File COMI contains 2025 data
2025-07-17 22:43:06,454 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-07-17 22:43:06,455 - app - INFO - Features shape: (1250, 36)
2025-07-17 22:43:06,477 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-17 22:43:06,478 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 22:43:06,478 - app - INFO - Data shape: (1250, 36)
2025-07-17 22:43:06,478 - app - INFO - File COMI contains 2025 data
2025-07-17 22:43:06,484 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:43:06,484 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:43:06,688 - app.utils.memory_management - INFO - Garbage collection: collected 730 objects
2025-07-17 22:43:06,688 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:43:06,690 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:43:07,099 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:43:07,176 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:43:07,177 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:43:07,443 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 22:43:07,443 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:43:07,444 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:43:10,300 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:43:40,458 - app.pages.market_overview_dashboard - ERROR - Error fetching live data for COMI: HTTPConnectionPool(host='127.0.0.1', port=8000): Read timed out. (read timeout=30)
2025-07-17 22:43:59,389 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:43:59,390 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:43:59,590 - app.utils.memory_management - INFO - Garbage collection: collected 1859 objects
2025-07-17 22:43:59,590 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:43:59,590 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:44:00,848 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:44:13,572 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:44:13,618 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:44:13,823 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:13,824 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:44:14,020 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:14,020 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 22:44:14,022 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-17 22:44:14,277 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-17 22:44:14,277 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-17 22:44:14,277 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-17 22:44:14,277 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:44:14,314 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:44:14,314 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:44:14,315 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:44:14,315 - models.predict - INFO - Prediction for 30 minutes horizon: 91347.30078965657
2025-07-17 22:44:14,317 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:44:14,341 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 22:44:14,545 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:14,545 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 22:44:14,734 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:14,734 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:44:14,735 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 22:44:14,854 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 22:44:14,854 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 22:44:14,854 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 22:44:14,854 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:44:14,883 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:44:14,884 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:44:14,884 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:44:14,884 - models.predict - INFO - Prediction for 60 minutes horizon: 88224.19108474106
2025-07-17 22:44:14,909 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:44:14,912 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 22:44:14,915 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 22:44:14,918 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 22:44:14,921 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 22:44:14,924 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 22:44:14,927 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 22:44:14,930 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 22:44:14,933 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:44:14,938 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:44:14,941 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 22:44:14,944 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 22:44:14,947 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 22:44:14,950 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 22:44:14,953 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 22:44:14,956 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 22:44:14,959 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 22:44:14,962 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:44:14,963 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 22:44:14,987 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 22:44:15,179 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:15,179 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 22:44:15,374 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:15,374 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:44:15,376 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-17 22:44:15,498 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-17 22:44:15,499 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-17 22:44:15,499 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-17 22:44:15,499 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:44:15,528 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:44:15,528 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:44:15,528 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:44:15,528 - models.predict - INFO - Prediction for 1440 minutes horizon: 89090.58764949649
2025-07-17 22:44:15,773 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:15,774 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:44:15,982 - app.utils.memory_management - INFO - Garbage collection: collected 933 objects
2025-07-17 22:44:15,983 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:15,984 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:44:47,764 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:44:56,028 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:56,029 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:44:56,216 - app.utils.memory_management - INFO - Garbage collection: collected 2727 objects
2025-07-17 22:44:56,216 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:44:56,216 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:44:57,766 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:45:04,552 - app.utils.backtesting - INFO - Preparing features for backtesting...
2025-07-17 22:45:04,579 - app.utils.backtesting - INFO - Added technical indicators. New shape: (1250, 56)
2025-07-17 22:45:04,629 - app.utils.backtesting - INFO - Prepared features. Final shape: (1250, 56)
2025-07-17 22:45:04,630 - app.utils.backtesting - INFO - Starting backtesting with 30 days and 8 features
2025-07-17 22:45:04,630 - app.utils.backtesting - INFO - Model type detection: sklearn=False, hybrid=False
2025-07-17 22:45:04,633 - app.utils.backtesting - WARNING - 3D reshape failed for day 0, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,633 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 0: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,637 - app.utils.backtesting - WARNING - 3D reshape failed for day 1, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,637 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 1: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,639 - app.utils.backtesting - WARNING - 3D reshape failed for day 2, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,639 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 2: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,641 - app.utils.backtesting - WARNING - 3D reshape failed for day 3, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,641 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 3: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,643 - app.utils.backtesting - WARNING - 3D reshape failed for day 4, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,644 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 4: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,645 - app.utils.backtesting - WARNING - 3D reshape failed for day 5, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,646 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 5: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,647 - app.utils.backtesting - WARNING - 3D reshape failed for day 6, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,648 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 6: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,650 - app.utils.backtesting - WARNING - 3D reshape failed for day 7, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,650 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 7: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,652 - app.utils.backtesting - WARNING - 3D reshape failed for day 8, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,652 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 8: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,654 - app.utils.backtesting - WARNING - 3D reshape failed for day 9, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,655 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 9: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,657 - app.utils.backtesting - WARNING - 3D reshape failed for day 10, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,657 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 10: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,659 - app.utils.backtesting - WARNING - 3D reshape failed for day 11, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,659 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 11: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,661 - app.utils.backtesting - WARNING - 3D reshape failed for day 12, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,661 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 12: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,664 - app.utils.backtesting - WARNING - 3D reshape failed for day 13, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,664 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 13: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,666 - app.utils.backtesting - WARNING - 3D reshape failed for day 14, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,666 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 14: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,668 - app.utils.backtesting - WARNING - 3D reshape failed for day 15, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,668 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 15: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,670 - app.utils.backtesting - WARNING - 3D reshape failed for day 16, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,671 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 16: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,673 - app.utils.backtesting - WARNING - 3D reshape failed for day 17, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,673 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 17: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,675 - app.utils.backtesting - WARNING - 3D reshape failed for day 18, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,675 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 18: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,678 - app.utils.backtesting - WARNING - 3D reshape failed for day 19, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,678 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 19: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,680 - app.utils.backtesting - WARNING - 3D reshape failed for day 20, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,680 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 20: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,682 - app.utils.backtesting - WARNING - 3D reshape failed for day 21, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,682 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 21: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,685 - app.utils.backtesting - WARNING - 3D reshape failed for day 22, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,685 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 22: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,687 - app.utils.backtesting - WARNING - 3D reshape failed for day 23, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,688 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 23: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,690 - app.utils.backtesting - WARNING - 3D reshape failed for day 24, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,690 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 24: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,691 - app.utils.backtesting - WARNING - 3D reshape failed for day 25, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,691 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 25: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,694 - app.utils.backtesting - WARNING - 3D reshape failed for day 26, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,695 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 26: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,697 - app.utils.backtesting - WARNING - 3D reshape failed for day 27, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,697 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 27: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,699 - app.utils.backtesting - WARNING - 3D reshape failed for day 28, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,699 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 28: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,702 - app.utils.backtesting - WARNING - 3D reshape failed for day 29, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,702 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 29: 'NoneType' object has no attribute 'predict'
2025-07-17 22:45:04,703 - app.utils.backtesting - ERROR - No valid predictions were made during backtesting
2025-07-17 22:45:04,704 - app.utils.backtesting - WARNING - Created 5 dummy results for visualization purposes
2025-07-17 22:45:04,796 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:45:04,797 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:45:04,999 - app.utils.memory_management - INFO - Garbage collection: collected 3095 objects
2025-07-17 22:45:05,000 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:45:05,000 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:45:54,455 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:46:10,095 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:46:10,126 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:46:10,324 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:10,325 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:46:10,527 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:10,528 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 22:46:10,530 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-17 22:46:10,642 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-17 22:46:10,642 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-17 22:46:10,643 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-17 22:46:10,643 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:46:10,672 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:46:10,673 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:46:10,673 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:46:10,673 - models.predict - INFO - Prediction for 30 minutes horizon: 90010.25716822765
2025-07-17 22:46:10,675 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:46:10,701 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 22:46:10,908 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:10,909 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 22:46:11,101 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:11,101 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:46:11,103 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 22:46:11,229 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 22:46:11,229 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 22:46:11,229 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 22:46:11,230 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:46:11,258 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:46:11,258 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:46:11,259 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:46:11,259 - models.predict - INFO - Prediction for 60 minutes horizon: 88880.43566838042
2025-07-17 22:46:11,283 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:46:11,286 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 22:46:11,289 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 22:46:11,292 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 22:46:11,295 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 22:46:11,298 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 22:46:11,301 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 22:46:11,304 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 22:46:11,307 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:46:11,311 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:46:11,315 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 22:46:11,318 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 22:46:11,321 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 22:46:11,324 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 22:46:11,327 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 22:46:11,330 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 22:46:11,333 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 22:46:11,336 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:46:11,337 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 22:46:11,362 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 22:46:11,553 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:11,553 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 22:46:11,748 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:11,748 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:46:11,749 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-17 22:46:11,852 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-17 22:46:11,853 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-17 22:46:11,853 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-17 22:46:11,853 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:46:11,882 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:46:11,882 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:46:11,882 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:46:11,882 - models.predict - INFO - Prediction for 1440 minutes horizon: 91322.70475081824
2025-07-17 22:46:12,173 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:12,174 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:46:12,450 - app.utils.memory_management - INFO - Garbage collection: collected 921 objects
2025-07-17 22:46:12,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:12,451 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:46:28,235 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:46:54,834 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:54,836 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:46:55,023 - app.utils.memory_management - INFO - Garbage collection: collected 2748 objects
2025-07-17 22:46:55,024 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:46:55,024 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:46:57,893 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:47:13,985 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 22:47:14,010 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:47:14,201 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:47:14,202 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:47:14,399 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:47:14,399 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 22:47:14,401 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-17 22:47:14,401 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_30min.keras
2025-07-17 22:47:14,909 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:47:15,654 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:47:15,655 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 1.3486124277114868
2025-07-17 22:47:15,655 - models.predict - WARNING - Prediction 169.55879306151394 is too far from current price 89980.0, using fallback
2025-07-17 22:47:15,655 - models.predict - INFO - Prediction for 30 minutes horizon: 89609.67365095338
2025-07-17 22:47:15,679 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 22:47:15,700 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 22:47:15,900 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:47:15,900 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 22:47:16,094 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:47:16,094 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:47:16,096 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-17 22:47:16,097 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-17 22:47:16,260 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 22:47:16,758 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:47:16,759 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 1.098739743232727
2025-07-17 22:47:16,759 - models.predict - WARNING - Prediction 147.01028058951482 is too far from current price 89980.0, using fallback
2025-07-17 22:47:16,759 - models.predict - INFO - Prediction for 60 minutes horizon: 88727.96471485058
2025-07-17 22:47:16,761 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 22:47:16,764 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:47:16,767 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 22:47:16,769 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:47:16,773 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 22:47:16,792 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 22:47:16,993 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:47:16,993 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 22:47:17,187 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:47:17,187 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:47:17,189 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-17 22:47:17,190 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_1440min.keras
2025-07-17 22:47:17,351 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-17 22:47:17,851 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 22:47:17,852 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 1.3769294023513794
2025-07-17 22:47:17,852 - models.predict - WARNING - Prediction 172.11411701446556 is too far from current price 89980.0, using fallback
2025-07-17 22:47:17,852 - models.predict - INFO - Prediction for 1440 minutes horizon: 90933.45929270374
2025-07-17 22:47:18,088 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:47:18,088 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:47:18,310 - app.utils.memory_management - INFO - Garbage collection: collected 17287 objects
2025-07-17 22:47:18,311 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:47:18,311 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:48:25,405 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:48:25,426 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 22:48:49,090 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:48:49,092 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:48:49,291 - app.utils.memory_management - INFO - Garbage collection: collected 2766 objects
2025-07-17 22:48:49,292 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:48:49,293 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:48:54,026 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:49:18,518 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:49:18,546 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:49:18,746 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:49:18,746 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:49:18,944 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:49:18,945 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:49:18,947 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-17 22:49:18,952 - models.hybrid_model - INFO - XGBoost is available
2025-07-17 22:49:18,952 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-17 22:49:18,953 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-17 22:49:18,953 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_30min.joblib or saved_models/COMI_rf_30min.pkl
2025-07-17 22:49:18,954 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:49:18,954 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 22:49:18,959 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:49:18,960 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 22:49:18,960 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-17 22:49:18,960 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:49:18,983 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 22:49:19,170 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:49:19,171 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 22:49:19,383 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:49:19,385 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:49:19,387 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-17 22:49:19,387 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-17 22:49:19,387 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-17 22:49:19,387 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 22:49:19,387 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 22:49:19,387 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:49:19,388 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 22:49:19,388 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-17 22:49:19,401 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:49:19,405 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:49:19,407 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 22:49:19,428 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 22:49:19,614 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:49:19,614 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 22:49:19,813 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:49:19,814 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:49:19,815 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-07-17 22:49:19,815 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-07-17 22:49:19,815 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_1440min.joblib or saved_models/COMI_rf_1440min.pkl
2025-07-17 22:49:19,815 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-17 22:49:19,816 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-17 22:49:19,816 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:49:19,816 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 22:49:19,816 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-17 22:49:20,070 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:49:20,070 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:49:20,321 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-17 22:49:20,322 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:49:20,322 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:50:22,192 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:50:52,278 - app.pages.market_overview_dashboard - ERROR - Error fetching live data for COMI: HTTPConnectionPool(host='127.0.0.1', port=8000): Read timed out. (read timeout=30)
2025-07-17 22:50:56,577 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:51:22,323 - app.pages.market_overview_dashboard - ERROR - Error fetching live data for COMI: HTTPConnectionPool(host='127.0.0.1', port=8000): Read timed out. (read timeout=30)
2025-07-17 22:51:22,331 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:22,332 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:51:22,529 - app.utils.memory_management - INFO - Garbage collection: collected 1876 objects
2025-07-17 22:51:22,530 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:22,530 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:51:23,602 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 22:51:23,632 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:51:23,846 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:23,847 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:51:24,048 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:24,048 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 22:51:24,051 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-17 22:51:24,051 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-17 22:51:24,051 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_30min.joblib or saved_models/COMI_gb_30min.pkl
2025-07-17 22:51:24,052 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 22:51:24,071 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 22:51:24,072 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:51:24,072 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 22:51:24,072 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-17 22:51:24,072 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 22:51:24,092 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 22:51:24,280 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:24,282 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 22:51:24,528 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:24,529 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:51:24,531 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-17 22:51:24,532 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-17 22:51:24,532 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-17 22:51:24,532 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 22:51:24,552 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 22:51:24,553 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:51:24,553 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 22:51:24,553 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-17 22:51:24,579 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 22:51:24,581 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:51:24,586 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 22:51:24,589 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:51:24,590 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 22:51:24,609 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 22:51:24,796 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:24,797 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 22:51:24,996 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:24,996 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:51:24,998 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-07-17 22:51:24,998 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-07-17 22:51:24,998 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_1440min.joblib or saved_models/COMI_gb_1440min.pkl
2025-07-17 22:51:24,999 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-17 22:51:25,017 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-17 22:51:25,018 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 22:51:25,018 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 22:51:25,018 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-17 22:51:25,289 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:25,289 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:51:25,480 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-17 22:51:25,481 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:51:25,481 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:54:37,962 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:54:37,982 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 22:54:38,004 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:54:38,005 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:54:38,215 - app.utils.memory_management - INFO - Garbage collection: collected 1756 objects
2025-07-17 22:54:38,216 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:54:38,216 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:54:40,630 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:54:40,683 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:54:40,683 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:54:40,879 - app.utils.memory_management - INFO - Garbage collection: collected 888 objects
2025-07-17 22:54:40,881 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:54:40,881 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:54:43,639 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:55:02,394 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:02,402 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:55:02,606 - app.utils.memory_management - INFO - Garbage collection: collected 1810 objects
2025-07-17 22:55:02,607 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:02,607 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:55:07,846 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:55:31,198 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 22:55:31,231 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 22:55:31,425 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:31,425 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 22:55:31,630 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:31,630 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 22:55:31,632 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-17 22:55:31,755 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-17 22:55:31,755 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-17 22:55:31,755 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-17 22:55:31,756 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:55:31,786 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:55:31,787 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:55:31,787 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:55:31,787 - models.predict - INFO - Prediction for 30 minutes horizon: 91344.79160648832
2025-07-17 22:55:31,789 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 22:55:31,814 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 22:55:32,023 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:32,023 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 22:55:32,217 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:32,217 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:55:32,219 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 22:55:32,322 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 22:55:32,323 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 22:55:32,323 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 22:55:32,323 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:55:32,351 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:55:32,352 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:55:32,352 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:55:32,352 - models.predict - INFO - Prediction for 60 minutes horizon: 89846.1230621752
2025-07-17 22:55:32,377 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:55:32,380 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 22:55:32,383 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 22:55:32,386 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 22:55:32,389 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 22:55:32,393 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 22:55:32,395 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 22:55:32,399 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 22:55:32,402 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 22:55:32,406 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:55:32,409 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 22:55:32,412 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 22:55:32,415 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 22:55:32,418 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 22:55:32,421 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 22:55:32,425 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 22:55:32,428 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 22:55:32,431 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 22:55:32,432 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 22:55:32,456 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 22:55:32,648 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:32,649 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 22:55:32,846 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:32,846 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 22:55:32,847 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-17 22:55:32,960 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-17 22:55:32,960 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-17 22:55:32,960 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-17 22:55:32,960 - models.predict - INFO - Ensemble model already loaded
2025-07-17 22:55:32,989 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 22:55:32,990 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 22:55:32,990 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 22:55:32,990 - models.predict - INFO - Prediction for 1440 minutes horizon: 90959.37513628844
2025-07-17 22:55:33,225 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:33,226 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:55:33,438 - app.utils.memory_management - INFO - Garbage collection: collected 997 objects
2025-07-17 22:55:33,439 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:33,440 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:55:53,370 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:55:53,410 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:53,411 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:55:53,617 - app.utils.memory_management - INFO - Garbage collection: collected 1756 objects
2025-07-17 22:55:53,618 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:55:53,619 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:56:01,259 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:56:01,300 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:56:01,300 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:56:01,501 - app.utils.memory_management - INFO - Garbage collection: collected 747 objects
2025-07-17 22:56:01,502 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:56:01,503 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:56:02,967 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:56:03,012 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:56:03,013 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:56:03,224 - app.utils.memory_management - INFO - Garbage collection: collected 732 objects
2025-07-17 22:56:03,225 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:56:03,226 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:56:05,704 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:56:18,234 - app.pages.advanced_technical_analysis - INFO - Loaded 783 days of historical data for COMI
2025-07-17 22:56:18,307 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:56:18,308 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:56:18,516 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 22:56:18,516 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:56:18,517 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 22:57:53,722 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 22:58:05,499 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:58:05,502 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 22:58:05,730 - app.utils.memory_management - INFO - Garbage collection: collected 3125 objects
2025-07-17 22:58:05,730 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 22:58:05,731 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:06:38,828 - app - INFO - Cleaning up resources...
2025-07-17 23:06:38,829 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:06:38,829 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:06:39,396 - app.utils.memory_management - INFO - Garbage collection: collected 1746 objects
2025-07-17 23:06:39,396 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:06:39,396 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:06:39,396 - app - INFO - Application shutdown complete
2025-07-17 23:06:42,162 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 23:06:43,009 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 23:06:43,009 - app - INFO - Memory management utilities loaded
2025-07-17 23:06:43,011 - app - INFO - Error handling utilities loaded
2025-07-17 23:06:43,012 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 23:06:43,013 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 23:06:43,013 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 23:06:43,014 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 23:06:43,017 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 23:06:43,017 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 23:06:43,017 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 23:06:43,018 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 23:06:43,018 - app - INFO - Applied NumPy fix
2025-07-17 23:06:43,019 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:06:43,020 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:06:43,020 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:06:43,020 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 23:06:43,021 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:06:43,021 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:06:43,021 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:06:43,021 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 23:06:53,178 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 23:06:53,178 - app - INFO - Applied TensorFlow fix
2025-07-17 23:06:54,013 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 23:06:54,013 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 23:06:54,014 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 23:06:54,018 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 23:06:54,559 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:06:54,559 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:06:54,560 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:06:54,560 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:06:54,560 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:06:54,561 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 23:06:54,561 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 23:06:54,561 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:06:54,561 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:06:54,561 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:06:54,750 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 23:06:54,752 - app.pages.market_overview_dashboard - WARNING - No stocks found in data/stocks folder, using fallback
2025-07-17 23:06:54,752 - app.pages.market_overview_dashboard - INFO - Loaded 3 stocks from data folder: ['COMI', 'ABUK', 'SWDY']
2025-07-17 23:06:54,753 - app.pages.market_overview_dashboard - WARNING - No stocks found in data/stocks folder, using fallback
2025-07-17 23:06:54,753 - app.pages.market_overview_dashboard - INFO - Loaded 3 stocks from data folder: ['COMI', 'ABUK', 'SWDY']
2025-07-17 23:06:54,776 - app - INFO - Cleaning up resources...
2025-07-17 23:06:54,777 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:06:54,777 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:06:54,950 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 23:06:54,950 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:06:54,950 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:06:54,950 - app - INFO - Application shutdown complete
2025-07-17 23:07:34,509 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 23:07:35,159 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 23:07:35,160 - app - INFO - Memory management utilities loaded
2025-07-17 23:07:35,163 - app - INFO - Error handling utilities loaded
2025-07-17 23:07:35,164 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 23:07:35,164 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 23:07:35,164 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 23:07:35,165 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 23:07:35,166 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 23:07:35,167 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 23:07:35,167 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 23:07:35,167 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 23:07:35,167 - app - INFO - Applied NumPy fix
2025-07-17 23:07:35,168 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:07:35,169 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:07:35,170 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:07:35,170 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 23:07:35,170 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:07:35,172 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:07:35,173 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:07:35,173 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 23:07:44,861 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 23:07:44,862 - app - INFO - Applied TensorFlow fix
2025-07-17 23:07:46,030 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 23:07:46,031 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 23:07:46,031 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 23:07:46,035 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 23:07:47,230 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:07:47,230 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:07:47,231 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:07:47,231 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:07:47,231 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:07:47,231 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 23:07:47,231 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 23:07:47,231 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:07:47,231 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:07:47,232 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:07:47,490 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 23:07:47,492 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:07:47,493 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:07:47,494 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:07:47,495 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:07:47,508 - app - INFO - Cleaning up resources...
2025-07-17 23:07:47,508 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:07:47,513 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:07:47,853 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 23:07:47,853 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:07:47,853 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:07:47,854 - app - INFO - Application shutdown complete
2025-07-17 23:08:17,767 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 23:08:18,500 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 23:08:18,500 - app - INFO - Memory management utilities loaded
2025-07-17 23:08:18,503 - app - INFO - Error handling utilities loaded
2025-07-17 23:08:18,504 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 23:08:18,505 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 23:08:18,505 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 23:08:18,508 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 23:08:18,509 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 23:08:18,509 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 23:08:18,510 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 23:08:18,510 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 23:08:18,510 - app - INFO - Applied NumPy fix
2025-07-17 23:08:18,511 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:08:18,511 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:08:18,512 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:08:18,512 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 23:08:18,514 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:08:18,514 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:08:18,514 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:08:18,514 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 23:08:27,537 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 23:08:27,537 - app - INFO - Applied TensorFlow fix
2025-07-17 23:08:28,588 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 23:08:28,588 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 23:08:28,588 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 23:08:28,591 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 23:08:29,157 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:08:29,158 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:08:29,158 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:08:29,158 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:08:29,159 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:08:29,159 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 23:08:29,160 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 23:08:29,160 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:08:29,160 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:08:29,160 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:08:29,464 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 23:08:29,466 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:08:29,466 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:08:29,467 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:08:29,467 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:08:29,481 - app - INFO - Cleaning up resources...
2025-07-17 23:08:29,481 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:08:29,481 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:08:29,652 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 23:08:29,653 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:08:29,653 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:08:29,653 - app - INFO - Application shutdown complete
2025-07-17 23:08:54,284 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 23:08:55,058 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 23:08:55,059 - app - INFO - Memory management utilities loaded
2025-07-17 23:08:55,061 - app - INFO - Error handling utilities loaded
2025-07-17 23:08:55,062 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 23:08:55,063 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 23:08:55,064 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 23:08:55,064 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 23:08:55,065 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 23:08:55,066 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 23:08:55,066 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 23:08:55,066 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 23:08:55,067 - app - INFO - Applied NumPy fix
2025-07-17 23:08:55,068 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:08:55,068 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:08:55,068 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:08:55,069 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 23:08:55,069 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:08:55,073 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:08:55,073 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:08:55,073 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 23:09:01,345 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 23:09:01,345 - app - INFO - Applied TensorFlow fix
2025-07-17 23:09:02,004 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 23:09:02,005 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 23:09:02,005 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 23:09:02,008 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 23:09:02,802 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:09:02,802 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:09:02,806 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:09:02,806 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:09:02,806 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:09:02,807 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 23:09:02,807 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 23:09:02,807 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:09:02,807 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:09:02,807 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:09:02,950 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 23:09:02,953 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:09:02,954 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:09:02,956 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:09:02,956 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:09:02,975 - app - INFO - Cleaning up resources...
2025-07-17 23:09:02,975 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:09:02,975 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:09:03,170 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 23:09:03,170 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:09:03,170 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:09:03,170 - app - INFO - Application shutdown complete
2025-07-17 23:09:28,232 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 23:09:30,468 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 23:09:30,469 - app - INFO - Memory management utilities loaded
2025-07-17 23:09:30,471 - app - INFO - Error handling utilities loaded
2025-07-17 23:09:30,473 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 23:09:30,475 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 23:09:30,476 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 23:09:30,476 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 23:09:30,477 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 23:09:30,477 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 23:09:30,477 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 23:09:30,477 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 23:09:30,478 - app - INFO - Applied NumPy fix
2025-07-17 23:09:30,478 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:09:30,479 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:09:30,479 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:09:30,481 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 23:09:30,481 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:09:30,481 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:09:30,482 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:09:30,482 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 23:09:36,573 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 23:09:36,574 - app - INFO - Applied TensorFlow fix
2025-07-17 23:09:37,372 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 23:09:37,372 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 23:09:37,372 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 23:09:37,375 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 23:09:38,200 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:09:38,201 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:09:38,201 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:09:38,201 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:09:38,201 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:09:38,201 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 23:09:38,201 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 23:09:38,201 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:09:38,201 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:09:38,201 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:09:38,505 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 23:09:38,519 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:09:38,520 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:09:38,529 - app - INFO - Cleaning up resources...
2025-07-17 23:09:38,529 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:09:38,529 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:09:38,759 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 23:09:38,759 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:09:38,759 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:09:38,759 - app - INFO - Application shutdown complete
2025-07-17 23:09:48,151 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 23:09:48,799 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 23:09:48,799 - app - INFO - Memory management utilities loaded
2025-07-17 23:09:48,801 - app - INFO - Error handling utilities loaded
2025-07-17 23:09:48,802 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 23:09:48,803 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 23:09:48,803 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 23:09:48,804 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 23:09:48,805 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 23:09:48,805 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 23:09:48,807 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 23:09:48,807 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 23:09:48,808 - app - INFO - Applied NumPy fix
2025-07-17 23:09:48,809 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:09:48,810 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:09:48,810 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:09:48,812 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 23:09:48,812 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:09:48,812 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:09:48,812 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:09:48,812 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 23:09:55,449 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 23:09:55,449 - app - INFO - Applied TensorFlow fix
2025-07-17 23:09:56,213 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 23:09:56,214 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 23:09:56,214 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 23:09:56,218 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 23:09:57,150 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:09:57,150 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:09:57,151 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:09:57,151 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:09:57,151 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:09:57,151 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 23:09:57,151 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 23:09:57,152 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:09:57,152 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:09:57,152 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:09:57,300 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 23:09:57,303 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:09:57,303 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:09:57,304 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:09:57,304 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:09:57,319 - app - INFO - Cleaning up resources...
2025-07-17 23:09:57,319 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:09:57,319 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:09:57,495 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 23:09:57,495 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:09:57,495 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:09:57,495 - app - INFO - Application shutdown complete
2025-07-17 23:10:50,310 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-17 23:10:51,712 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 23:10:51,712 - app - INFO - Memory management utilities loaded
2025-07-17 23:10:51,714 - app - INFO - Error handling utilities loaded
2025-07-17 23:10:51,715 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 23:10:51,716 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 23:10:51,716 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 23:10:51,716 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 23:10:51,717 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-17 23:10:51,717 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 23:10:51,717 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 23:10:51,718 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 23:10:51,718 - app - INFO - Applied NumPy fix
2025-07-17 23:10:51,719 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:10:51,719 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:10:51,719 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:10:51,719 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 23:10:51,719 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:10:51,720 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:10:51,720 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:10:51,720 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 23:11:01,569 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 23:11:01,570 - app - INFO - Applied TensorFlow fix
2025-07-17 23:11:01,572 - app.config - INFO - Configuration initialized
2025-07-17 23:11:01,583 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-17 23:11:01,680 - models.train - INFO - TensorFlow test successful
2025-07-17 23:11:01,720 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 23:11:01,720 - models.train - INFO - Transformer model is available
2025-07-17 23:11:01,720 - models.train - INFO - Using TensorFlow-based models
2025-07-17 23:11:01,722 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 23:11:01,722 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 23:11:01,725 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 23:11:02,286 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:11:02,286 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 23:11:02,287 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 23:11:02,287 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 23:11:02,287 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 23:11:02,287 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 23:11:02,287 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 23:11:02,287 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 23:11:02,287 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 23:11:02,287 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 23:11:02,418 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 23:11:02,420 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:11:02,906 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-17 23:11:03,807 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-17 23:11:03,807 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-17 23:11:03,976 - app.utils.session_state - INFO - Initializing session state
2025-07-17 23:11:03,978 - app.utils.session_state - INFO - Session state initialized
2025-07-17 23:11:04,618 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 23:11:04,626 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:04,626 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:11:04,822 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 23:11:04,822 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:04,823 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:11:15,077 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:11:15,099 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:15,100 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:11:15,299 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 23:11:15,300 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:15,301 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:11:16,316 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:11:16,385 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.05 seconds
2025-07-17 23:11:16,386 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 23:11:16,388 - app - INFO - Data shape: (1250, 36)
2025-07-17 23:11:16,388 - app - INFO - File COMI contains 2025 data
2025-07-17 23:11:16,445 - app - INFO - Feature engineering for COMI completed in 0.06 seconds
2025-07-17 23:11:16,445 - app - INFO - Features shape: (1250, 36)
2025-07-17 23:11:16,483 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-17 23:11:16,484 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-17 23:11:16,484 - app - INFO - Data shape: (1250, 36)
2025-07-17 23:11:16,484 - app - INFO - File COMI contains 2025 data
2025-07-17 23:11:16,486 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:16,487 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:11:16,675 - app.utils.memory_management - INFO - Garbage collection: collected 730 objects
2025-07-17 23:11:16,675 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:16,675 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:11:17,105 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:11:17,168 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:17,172 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:11:17,419 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 23:11:17,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:17,420 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:11:19,496 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:11:19,519 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-17 23:11:19,520 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-17 23:11:37,656 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:37,657 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:11:37,845 - app.utils.memory_management - INFO - Garbage collection: collected 1859 objects
2025-07-17 23:11:37,846 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:11:37,847 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:11:44,131 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:12:04,418 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 23:12:04,461 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 23:12:04,657 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:12:04,658 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 23:12:04,860 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:12:04,860 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:12:04,863 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-17 23:12:05,199 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-17 23:12:05,200 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-17 23:12:05,200 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-17 23:12:05,200 - models.predict - INFO - Ensemble model already loaded
2025-07-17 23:12:05,238 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 23:12:05,238 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 23:12:05,238 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 23:12:05,238 - models.predict - INFO - Prediction for 30 minutes horizon: 91348.75174433136
2025-07-17 23:12:05,240 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 23:12:05,266 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 23:12:05,482 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:12:05,482 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 23:12:05,671 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:12:05,671 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:12:05,673 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 23:12:05,785 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 23:12:05,785 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 23:12:05,785 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 23:12:05,786 - models.predict - INFO - Ensemble model already loaded
2025-07-17 23:12:05,814 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 23:12:05,814 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 23:12:05,814 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 23:12:05,815 - models.predict - INFO - Prediction for 60 minutes horizon: 91384.85373274358
2025-07-17 23:12:05,840 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 23:12:05,843 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 23:12:05,846 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 23:12:05,849 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 23:12:05,852 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 23:12:05,856 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 23:12:05,859 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 23:12:05,862 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 23:12:05,865 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 23:12:05,869 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 23:12:05,873 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 23:12:05,876 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 23:12:05,879 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 23:12:05,882 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 23:12:05,885 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 23:12:05,888 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 23:12:05,891 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 23:12:05,894 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 23:12:05,896 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 23:12:05,920 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 23:12:06,111 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:12:06,112 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 23:12:06,307 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:12:06,307 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:12:06,309 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-17 23:12:06,421 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-17 23:12:06,422 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-17 23:12:06,422 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-17 23:12:06,422 - models.predict - INFO - Ensemble model already loaded
2025-07-17 23:12:06,451 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 23:12:06,451 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1592014884957
2025-07-17 23:12:06,451 - models.predict - WARNING - Prediction 30382.86825881147 is too far from current price 89980.0, using fallback
2025-07-17 23:12:06,451 - models.predict - INFO - Prediction for 1440 minutes horizon: 90119.09126854925
2025-07-17 23:12:06,730 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:12:06,731 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:12:06,940 - app.utils.memory_management - INFO - Garbage collection: collected 1011 objects
2025-07-17 23:12:06,941 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:12:06,941 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:13:02,843 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:13:21,092 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:21,094 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:13:21,281 - app.utils.memory_management - INFO - Garbage collection: collected 2778 objects
2025-07-17 23:13:21,282 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:21,282 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:13:24,721 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:13:35,416 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 23:13:35,460 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 23:13:35,648 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:35,649 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 23:13:35,846 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:35,847 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 23:13:35,848 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-17 23:13:35,859 - models.hybrid_model - INFO - XGBoost is available
2025-07-17 23:13:35,860 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-17 23:13:35,861 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-17 23:13:35,861 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_30min.joblib or saved_models/COMI_rf_30min.pkl
2025-07-17 23:13:35,861 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 23:13:35,862 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 23:13:35,866 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:13:35,867 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 23:13:35,867 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-17 23:13:35,867 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 23:13:35,891 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 23:13:36,075 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:36,076 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 23:13:36,281 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:36,282 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:13:36,284 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-17 23:13:36,284 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-17 23:13:36,284 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-17 23:13:36,284 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 23:13:36,285 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 23:13:36,285 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:13:36,285 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 23:13:36,285 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-17 23:13:36,317 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 23:13:36,322 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 23:13:36,323 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 23:13:36,346 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 23:13:36,527 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:36,528 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 23:13:36,720 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:36,720 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:13:36,722 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-07-17 23:13:36,722 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-07-17 23:13:36,722 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_1440min.joblib or saved_models/COMI_rf_1440min.pkl
2025-07-17 23:13:36,722 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-17 23:13:36,722 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-17 23:13:36,723 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:13:36,723 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 23:13:36,723 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-17 23:13:36,969 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:36,970 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:13:37,201 - app.utils.memory_management - INFO - Garbage collection: collected 892 objects
2025-07-17 23:13:37,201 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:13:37,202 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:15:48,924 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:16:13,008 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:16:13,010 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:16:13,218 - app.utils.memory_management - INFO - Garbage collection: collected 2773 objects
2025-07-17 23:16:13,218 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:16:13,219 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:16:17,000 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:16:17,039 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 23:16:47,109 - app.pages.market_overview_dashboard - ERROR - Error fetching live data for COMI: HTTPConnectionPool(host='127.0.0.1', port=8000): Read timed out. (read timeout=30)
2025-07-17 23:16:47,115 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 23:16:47,147 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 23:16:47,348 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:16:47,349 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 23:16:47,552 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:16:47,553 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 23:16:47,555 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-17 23:16:47,555 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_30min.keras
2025-07-17 23:16:47,972 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 23:16:48,645 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 23:16:48,646 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.4456096887588501
2025-07-17 23:16:48,646 - models.predict - INFO - Prediction for 30 minutes horizon: 88.07182075000584
2025-07-17 23:16:48,669 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 23:16:48,690 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 23:16:48,922 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:16:48,923 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 23:16:49,117 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:16:49,117 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:16:49,119 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-17 23:16:49,119 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-17 23:16:49,296 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 23:16:49,833 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 23:16:49,834 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.42978495359420776
2025-07-17 23:16:49,837 - models.predict - INFO - Prediction for 60 minutes horizon: 86.64379655852463
2025-07-17 23:16:49,840 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 23:16:49,844 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 23:16:49,848 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 23:16:49,852 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 23:16:49,855 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 23:16:49,877 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 23:16:50,095 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:16:50,095 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 23:16:50,289 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:16:50,289 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:16:50,291 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-17 23:16:50,291 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_1440min.keras
2025-07-17 23:16:50,468 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-17 23:16:51,025 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 23:16:51,026 - models.predict - INFO - Current price: 89.98, Predicted scaled value: 0.44002068042755127
2025-07-17 23:16:51,026 - models.predict - INFO - Prediction for 1440 minutes horizon: 87.567468606324
2025-07-17 23:17:03,573 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:17:03,575 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:17:03,809 - app.utils.memory_management - INFO - Garbage collection: collected 17267 objects
2025-07-17 23:17:03,810 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:17:03,810 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:17:50,713 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:18:07,422 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:07,425 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:18:07,654 - app.utils.memory_management - INFO - Garbage collection: collected 2830 objects
2025-07-17 23:18:07,655 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:07,656 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:18:10,646 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:18:40,763 - app.pages.market_overview_dashboard - ERROR - Error fetching live data for COMI: HTTPConnectionPool(host='127.0.0.1', port=8000): Read timed out. (read timeout=30)
2025-07-17 23:18:40,768 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 23:18:40,797 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 23:18:41,009 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:41,010 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 23:18:41,213 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:41,213 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-17 23:18:41,215 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-17 23:18:41,215 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-17 23:18:41,215 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_30min.joblib or saved_models/COMI_gb_30min.pkl
2025-07-17 23:18:41,215 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 23:18:41,235 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 23:18:41,235 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:18:41,235 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 23:18:41,235 - models.predict - INFO - Prediction for 30 minutes horizon: 89.98
2025-07-17 23:18:41,236 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 23:18:41,254 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 23:18:41,443 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:41,444 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 23:18:41,646 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:41,647 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:18:41,649 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-17 23:18:41,649 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-17 23:18:41,649 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-17 23:18:41,649 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 23:18:41,668 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 23:18:41,668 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:18:41,668 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 23:18:41,668 - models.predict - INFO - Prediction for 60 minutes horizon: 89.98
2025-07-17 23:18:41,696 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 23:18:41,699 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 23:18:41,704 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 23:18:41,706 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 23:18:41,708 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 23:18:41,726 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 23:18:41,915 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:41,916 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 23:18:42,111 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:42,111 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:18:42,112 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-07-17 23:18:42,113 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-07-17 23:18:42,113 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_1440min.joblib or saved_models/COMI_gb_1440min.pkl
2025-07-17 23:18:42,113 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-17 23:18:42,132 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-17 23:18:42,135 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:18:42,135 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-17 23:18:42,135 - models.predict - INFO - Prediction for 1440 minutes horizon: 89.98
2025-07-17 23:18:54,820 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:54,822 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:18:55,010 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-17 23:18:55,011 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:18:55,011 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:19:23,063 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:19:39,471 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:19:39,472 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:19:39,688 - app.utils.memory_management - INFO - Garbage collection: collected 2787 objects
2025-07-17 23:19:39,688 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:19:39,689 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:19:43,092 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 23:20:04,299 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 23:20:04,328 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-17 23:20:04,522 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:20:04,523 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-17 23:20:04,714 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:20:04,715 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:20:04,717 - models.predict - INFO - Using scikit-learn lr model for 30 minutes horizon
2025-07-17 23:20:04,717 - models.predict - INFO - Loading lr model for COMI with horizon 30
2025-07-17 23:20:04,717 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_30min.joblib or saved_models/COMI_lr_30min.pkl
2025-07-17 23:20:04,717 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-17 23:20:04,717 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-17 23:20:04,717 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:20:04,718 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 23:20:04,718 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-17 23:20:04,718 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 23:20:04,742 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 23:20:04,930 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:20:04,931 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 23:20:05,127 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:20:05,127 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:20:05,129 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-17 23:20:05,129 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-17 23:20:05,129 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-17 23:20:05,130 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 23:20:05,130 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 23:20:05,130 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:20:05,130 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 23:20:05,131 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-17 23:20:05,135 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 23:20:05,138 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 23:20:05,142 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 23:20:05,144 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 23:20:05,145 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 23:20:05,165 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-17 23:20:05,352 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:20:05,353 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-17 23:20:05,540 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:20:05,540 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 23:20:05,542 - models.predict - INFO - Using scikit-learn lr model for 1440 minutes horizon
2025-07-17 23:20:05,542 - models.predict - INFO - Loading lr model for COMI with horizon 1440
2025-07-17 23:20:05,542 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_1440min.joblib or saved_models/COMI_lr_1440min.pkl
2025-07-17 23:20:05,542 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-17 23:20:05,542 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-17 23:20:05,543 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 23:20:05,543 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-17 23:20:05,543 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-17 23:20:05,762 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:20:05,763 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:20:05,977 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-17 23:20:05,977 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:20:05,978 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:25:37,280 - app - INFO - Cleaning up resources...
2025-07-17 23:25:37,285 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:25:37,285 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 23:25:37,665 - app.utils.memory_management - INFO - Garbage collection: collected 1927 objects
2025-07-17 23:25:37,666 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 23:25:37,666 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 23:25:37,666 - app - INFO - Application shutdown complete
