2025-07-17 10:18:49,285 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-17 10:18:51,654 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-17 10:18:51,655 - app - INFO - Memory management utilities loaded
2025-07-17 10:18:51,657 - app - INFO - Error handling utilities loaded
2025-07-17 10:18:51,658 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-17 10:18:51,659 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-17 10:18:51,659 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-17 10:18:51,659 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-17 10:18:51,662 - app.utils.numpy_fix - INFO - <PERSON>T19937 is properly registered as a BitGenerator
2025-07-17 10:18:51,663 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-17 10:18:51,663 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-17 10:18:51,664 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-17 10:18:51,664 - app - INFO - Applied NumPy fix
2025-07-17 10:18:51,666 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 10:18:51,667 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 10:18:51,668 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 10:18:51,669 - app - INFO - Applied NumPy BitGenerator fix
2025-07-17 10:19:12,796 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-17 10:19:12,797 - app - INFO - Applied TensorFlow fix
2025-07-17 10:19:12,799 - app.config - INFO - Configuration initialized
2025-07-17 10:19:12,805 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-17 10:19:12,966 - models.train - INFO - TensorFlow test successful
2025-07-17 10:19:13,063 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-17 10:19:13,063 - models.train - INFO - Transformer model is available
2025-07-17 10:19:13,064 - models.train - INFO - Using TensorFlow-based models
2025-07-17 10:19:13,066 - models.predict - INFO - Transformer model is available for predictions
2025-07-17 10:19:13,066 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-17 10:19:13,069 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-17 10:19:14,942 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 10:19:14,942 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-17 10:19:14,942 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-17 10:19:14,943 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-17 10:19:15,179 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-17 10:19:15,181 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:15,688 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-17 10:19:17,720 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-17 10:19:17,721 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-17 10:19:17,990 - app.utils.session_state - INFO - Initializing session state
2025-07-17 10:19:17,992 - app.utils.session_state - INFO - Session state initialized
2025-07-17 10:19:18,645 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 10:19:18,653 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:18,654 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:18,858 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:19:18,858 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:18,858 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:33,706 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:33,744 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:33,744 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:33,963 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:19:33,964 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:33,965 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:35,077 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:35,248 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.16 seconds
2025-07-17 10:19:35,254 - app - INFO - Date range: 2020-10-01 to 2025-07-16
2025-07-17 10:19:35,254 - app - INFO - Data shape: (1250, 36)
2025-07-17 10:19:35,254 - app - INFO - File COMI contains 2025 data
2025-07-17 10:19:35,294 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-07-17 10:19:35,295 - app - INFO - Features shape: (1250, 36)
2025-07-17 10:19:35,316 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-17 10:19:35,317 - app - INFO - Date range: 2020-10-01 to 2025-07-16
2025-07-17 10:19:35,318 - app - INFO - Data shape: (1250, 36)
2025-07-17 10:19:35,318 - app - INFO - File COMI contains 2025 data
2025-07-17 10:19:35,328 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:35,328 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:35,521 - app.utils.memory_management - INFO - Garbage collection: collected 626 objects
2025-07-17 10:19:35,521 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:35,522 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:35,942 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:36,172 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:36,172 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:36,375 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:19:36,375 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:36,376 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:47,169 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:47,239 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:47,240 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:47,462 - app.utils.memory_management - INFO - Garbage collection: collected 878 objects
2025-07-17 10:19:47,462 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:47,463 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:52,927 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:52,979 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:52,980 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:53,209 - app.utils.memory_management - INFO - Garbage collection: collected 772 objects
2025-07-17 10:19:53,210 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:53,210 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:19:55,096 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:19:55,208 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:55,209 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:19:55,411 - app.utils.memory_management - INFO - Garbage collection: collected 748 objects
2025-07-17 10:19:55,411 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:19:55,412 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:20:15,251 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:20:15,312 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:15,312 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:20:15,505 - app.utils.memory_management - INFO - Garbage collection: collected 1109 objects
2025-07-17 10:20:15,506 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:15,507 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:20:15,977 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:20:16,084 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:16,085 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:20:16,300 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:20:16,301 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:16,302 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:20:18,729 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:20:19,141 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-07-17 10:20:19,143 - app.models.adaptive - INFO - No valid models for COMI with 1min horizon, using equal weights
2025-07-17 10:20:19,143 - app.models.predict - INFO - Ensemble weights for COMI with 1min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 10:20:19,297 - models.predict - INFO - Making predictions for 1 minutes horizon
2025-07-17 10:20:19,492 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:19,493 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1_scaler.pkl (0.53 KB)
2025-07-17 10:20:19,696 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:19,697 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:20:19,702 - models.predict - INFO - Loading lstm model for COMI with horizon 1
2025-07-17 10:20:19,704 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_1min.keras
2025-07-17 10:20:19,707 - models.predict - ERROR - Error making predictions: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 10:20:19,707 - app.utils.performance - WARNING - enhanced_prediction failed after 0.5656s: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 10:20:19,708 - app.pages.professional_trading_strategy - WARNING - Prediction error: File not found: filepath=saved_models/COMI_lstm_1min.keras. Please ensure the file is an accessible `.keras` zip file.
2025-07-17 10:20:19,869 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:19,870 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:20:20,073 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:20:20,074 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:20:20,075 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:15,232 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:15,297 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:15,298 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:15,558 - app.utils.memory_management - INFO - Garbage collection: collected 1504 objects
2025-07-17 10:21:15,559 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:15,560 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:16,055 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:16,101 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:16,102 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:16,332 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:21:16,333 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:16,333 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:18,970 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:19,064 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:21:19,064 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:21:19,065 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:21:19,065 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:21:19,076 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 10:21:19,081 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 10:21:19,086 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 10:21:19,089 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 10:21:19,095 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 10:21:19,100 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-17 10:21:19,104 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-17 10:21:19,108 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-17 10:21:19,112 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 10:21:19,118 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 10:21:19,123 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 10:21:19,127 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 10:21:19,132 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 10:21:19,134 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-17 10:21:19,139 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-17 10:21:19,141 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-17 10:21:19,141 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,141 - app.pages.professional_trading_strategy - INFO - ✅ Using ensemble model with horizons: [5, 15, 30] (weight: 0.203)
2025-07-17 10:21:19,141 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-07-17 10:21:19,142 - app.models.adaptive - INFO - No valid models for COMI with 5min horizon, using equal weights
2025-07-17 10:21:19,142 - app.models.predict - INFO - Ensemble weights for COMI with 5min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 10:21:19,248 - app.models.predict - INFO - Adaptive ensemble prediction for 5min horizon: 87.20008976417155
2025-07-17 10:21:19,248 - app.models.adaptive - INFO - No valid models for COMI with 15min horizon, using equal weights
2025-07-17 10:21:19,248 - app.models.predict - INFO - Ensemble weights for COMI with 15min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 10:21:19,373 - app.models.predict - INFO - Adaptive ensemble prediction for 15min horizon: 87.22960192347634
2025-07-17 10:21:19,374 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-07-17 10:21:19,374 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-17 10:21:19,496 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 87.21315557996898
2025-07-17 10:21:19,496 - app.models.predict - INFO - Prediction completed in 0.35 seconds
2025-07-17 10:21:19,496 - app.pages.professional_trading_strategy - INFO - ✅ ENSEMBLE: BEARISH (67.3%) - Pred: 87.21
2025-07-17 10:21:19,496 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:21:19,502 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:21:19,502 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:21:19,506 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:21:19,511 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-17 10:21:19,516 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-17 10:21:19,523 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:21:19,525 - app.pages.professional_trading_strategy - INFO - ✅ Using lstm model with horizons: [5, 15, 30] (weight: 0.196)
2025-07-17 10:21:19,525 - app.models.predict - INFO - Using specified model type: lstm
2025-07-17 10:21:19,578 - app.models.predict - INFO - Prediction completed in 0.05 seconds
2025-07-17 10:21:19,579 - app.pages.professional_trading_strategy - INFO - ✅ LSTM: BEARISH (92.8%) - Pred: 85.68
2025-07-17 10:21:19,579 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:21:19,579 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:21:19,579 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:21:19,580 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:21:19,582 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-17 10:21:19,586 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-17 10:21:19,586 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,586 - app.pages.professional_trading_strategy - INFO - ✅ Using gb model with horizons: [5, 15, 30] (weight: 0.157)
2025-07-17 10:21:19,587 - app.models.predict - INFO - Using specified model type: gb
2025-07-17 10:21:19,635 - app.models.predict - INFO - Prediction completed in 0.05 seconds
2025-07-17 10:21:19,635 - app.pages.professional_trading_strategy - INFO - ✅ GB: BEARISH (54.7%) - Pred: 87.98
2025-07-17 10:21:19,635 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:21:19,635 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:21:19,635 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:21:19,635 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:21:19,638 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-17 10:21:19,640 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-17 10:21:19,640 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,641 - app.pages.professional_trading_strategy - INFO - ✅ Using rf model with horizons: [5, 15, 30] (weight: 0.152)
2025-07-17 10:21:19,641 - app.models.predict - INFO - Using specified model type: rf
2025-07-17 10:21:19,683 - app.models.predict - INFO - Prediction completed in 0.04 seconds
2025-07-17 10:21:19,683 - app.pages.professional_trading_strategy - INFO - ✅ RF: BEARISH (54.2%) - Pred: 87.98
2025-07-17 10:21:19,684 - app.utils.data_processing - INFO - Found svr model for COMI with 5 minutes horizon
2025-07-17 10:21:19,684 - app.utils.data_processing - INFO - Found svr model for COMI with 15 minutes horizon
2025-07-17 10:21:19,684 - app.utils.data_processing - INFO - Found svr model for COMI with 30 minutes horizon
2025-07-17 10:21:19,684 - app.utils.data_processing - INFO - Found svr model for COMI with 60 minutes horizon
2025-07-17 10:21:19,686 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-17 10:21:19,690 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-17 10:21:19,690 - app.utils.data_processing - INFO - Found svr model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,691 - app.pages.professional_trading_strategy - INFO - ✅ Using svr model with horizons: [5, 15, 30] (weight: 0.148)
2025-07-17 10:21:19,691 - app.models.predict - INFO - Using specified model type: svr
2025-07-17 10:21:19,742 - app.models.predict - INFO - Prediction completed in 0.05 seconds
2025-07-17 10:21:19,743 - app.pages.professional_trading_strategy - INFO - ✅ SVR: BEARISH (53.8%) - Pred: 87.98
2025-07-17 10:21:19,745 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:21:19,745 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:21:19,746 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:21:19,746 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:21:19,752 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-17 10:21:19,755 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-17 10:21:19,756 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:21:19,756 - app.pages.professional_trading_strategy - INFO - ✅ Using lr model with horizons: [5, 15, 30] (weight: 0.144)
2025-07-17 10:21:19,756 - app.models.predict - INFO - Using specified model type: lr
2025-07-17 10:21:19,791 - app.models.predict - INFO - Prediction completed in 0.03 seconds
2025-07-17 10:21:19,791 - app.pages.professional_trading_strategy - INFO - ✅ LR: BEARISH (53.4%) - Pred: 87.98
2025-07-17 10:21:19,791 - app.pages.professional_trading_strategy - INFO - ✅ Enhanced AI Consensus: 0/6 models predict BEARISH (0%)
2025-07-17 10:21:19,791 - app.pages.professional_trading_strategy - INFO - 📊 Market Regime: NEUTRAL | Volatility: 0.33
2025-07-17 10:21:19,845 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-07-17 10:21:19,876 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.166
2025-07-17 10:21:19,986 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:19,986 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:20,185 - app.utils.memory_management - INFO - Garbage collection: collected 712 objects
2025-07-17 10:21:20,185 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:20,186 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:38,835 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:38,903 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:38,906 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:39,197 - app.utils.memory_management - INFO - Garbage collection: collected 1015 objects
2025-07-17 10:21:39,198 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:39,201 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:39,732 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:39,787 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:39,788 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:40,016 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:21:40,017 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:40,018 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:44,201 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:44,379 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:44,379 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:44,584 - app.utils.memory_management - INFO - Garbage collection: collected 986 objects
2025-07-17 10:21:44,584 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:44,585 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:48,579 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:48,663 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:48,664 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:48,874 - app.utils.memory_management - INFO - Garbage collection: collected 1084 objects
2025-07-17 10:21:48,875 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:48,875 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:49,429 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:49,486 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:49,486 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:49,712 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:21:49,713 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:49,713 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:21:54,334 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:21:54,454 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:54,454 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:21:54,701 - app.utils.memory_management - INFO - Garbage collection: collected 897 objects
2025-07-17 10:21:54,703 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:21:54,703 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:22:02,218 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:22:02,364 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:02,364 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:22:02,693 - app.utils.memory_management - INFO - Garbage collection: collected 958 objects
2025-07-17 10:22:02,693 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:02,695 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:22:03,151 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:22:03,212 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:03,212 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:22:03,407 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-17 10:22:03,408 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:03,409 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:22:52,074 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:22:52,110 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:52,110 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:22:52,316 - app.utils.memory_management - INFO - Garbage collection: collected 760 objects
2025-07-17 10:22:52,316 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:52,316 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:22:57,976 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:22:58,002 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:58,002 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:22:58,209 - app.utils.memory_management - INFO - Garbage collection: collected 604 objects
2025-07-17 10:22:58,210 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:22:58,210 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:01,952 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:02,380 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:02,380 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:02,560 - app.utils.memory_management - INFO - Garbage collection: collected 619 objects
2025-07-17 10:23:02,561 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:02,561 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:11,333 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:11,355 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:11,356 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:11,558 - app.utils.memory_management - INFO - Garbage collection: collected 692 objects
2025-07-17 10:23:11,559 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:11,559 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:12,793 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:12,860 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-17 10:23:12,860 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:12,860 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:12,861 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,861 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,861 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-17 10:23:12,861 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-17 10:23:12,866 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:12,867 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:23:12,874 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:12,879 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:23:12,884 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-17 10:23:12,890 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,891 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-17 10:23:12,905 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-17 10:23:12,905 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:12,905 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:23:12,905 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:12,906 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,906 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-17 10:23:12,913 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-17 10:23:12,913 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:12,913 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:12,913 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,914 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,914 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-17 10:23:12,949 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,951 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,951 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:12,955 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:12,956 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:12,970 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:12,971 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:12,973 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:12,973 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,974 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,974 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:23:12,979 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:12,979 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:23:12,983 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:12,990 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:23:12,990 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:23:12,991 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:12,992 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:23:12,992 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:12,993 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,994 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:23:12,994 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:12,996 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:23:12,997 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:12,997 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:23:12,997 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:12,998 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:12,998 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:12,998 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:12,998 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:13,008 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:13,014 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:13,015 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:13,015 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:13,015 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:13,027 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:13,027 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:13,236 - app.utils.memory_management - INFO - Garbage collection: collected 710 objects
2025-07-17 10:23:13,237 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:13,237 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:26,968 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:27,016 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:27,017 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:27,017 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:27,025 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:27,026 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:27,039 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:27,040 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:27,041 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:27,042 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:27,042 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:27,043 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:23:27,051 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:27,052 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:23:27,062 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:27,068 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:23:27,068 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:23:27,069 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:27,069 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:23:27,069 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:27,069 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:27,070 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:27,071 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:27,084 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:27,092 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:27,093 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:27,094 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:27,094 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:27,108 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:27,109 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:27,339 - app.utils.memory_management - INFO - Garbage collection: collected 1588 objects
2025-07-17 10:23:27,339 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:27,340 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:23:29,750 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:23:29,804 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:29,814 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:29,814 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:29,815 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:29,816 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:29,817 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-17 10:23:29,819 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:29,819 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:29,819 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:29,823 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:29,824 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:29,824 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-17 10:23:29,824 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:41,609 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-17 10:23:41,645 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:23:41,839 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:41,840 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:23:42,032 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:42,033 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:23:42,034 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 10:23:42,169 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 10:23:42,169 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 10:23:42,169 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 10:23:42,169 - models.predict - INFO - Ensemble model already loaded
2025-07-17 10:23:42,224 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 10:23:42,224 - models.predict - INFO - Current price: 88.0, Predicted scaled value: 0.42449626261334245
2025-07-17 10:23:42,224 - models.predict - INFO - Prediction for 60 minutes horizon: 86.16654505425815
2025-07-17 10:23:42,227 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.17 vs current 88.00 (2.1% change, limit: 2.0%). Applying correction.
2025-07-17 10:23:42,228 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.17 -> 86.77 (change: -1.4%)
2025-07-17 10:23:42,228 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 86.17 -> 86.77 for 60min
2025-07-17 10:23:42,235 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,236 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,236 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:42,240 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:42,241 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:42,330 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,330 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,330 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:42,337 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:42,339 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:42,352 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:42,352 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:42,353 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:42,354 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,355 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:42,355 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:23:42,364 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:42,364 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:23:42,371 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:23:42,377 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:23:42,377 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:42,378 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:42,379 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:23:42,380 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:23:42,380 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:23:42,388 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:42,398 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:23:42,398 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:23:42,399 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:23:42,399 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:23:42,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:42,420 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:23:42,676 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:23:42,677 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:23:42,677 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:24:25,829 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:24:25,850 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 10:24:25,887 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:25,888 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:25,888 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:24:25,896 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:24:25,896 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:24:25,902 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:30,759 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-17 10:24:30,790 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:24:30,972 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:24:30,973 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:24:31,157 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:24:31,158 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:24:31,159 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 10:24:31,278 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 10:24:31,278 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 10:24:31,278 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 10:24:31,278 - models.predict - INFO - Ensemble model already loaded
2025-07-17 10:24:31,305 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 10:24:31,306 - models.predict - INFO - Current price: 88.0, Predicted scaled value: 0.42449626261334245
2025-07-17 10:24:31,306 - models.predict - INFO - Prediction for 60 minutes horizon: 86.16654505425815
2025-07-17 10:24:31,309 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.17 vs current 88.00 (2.1% change, limit: 2.0%). Applying correction.
2025-07-17 10:24:31,309 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.17 -> 86.77 (change: -1.4%)
2025-07-17 10:24:31,309 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 86.17 -> 86.77 for 60min
2025-07-17 10:24:31,337 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:24:31,337 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:24:31,338 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:24:31,338 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:31,339 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:24:31,340 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:24:31,349 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:24:31,350 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:24:31,358 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:24:31,363 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:24:31,364 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:24:31,366 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:24:31,366 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:24:31,366 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:24:31,367 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:24:31,367 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:24:31,368 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:24:31,369 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:24:31,370 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:24:31,370 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:24:31,379 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:24:31,384 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:24:31,384 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:24:31,385 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:24:31,385 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:24:31,425 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:24:31,426 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:24:31,619 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:24:31,620 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:24:31,621 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:25:26,799 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:25:26,850 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,853 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,854 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:26,859 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:26,860 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:26,880 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:25:26,880 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:25:26,881 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:25:26,881 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,882 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:25:26,882 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:25:26,888 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:25:26,888 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:25:26,892 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:26,896 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:25:26,897 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:25:26,897 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:25:26,897 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:25:26,897 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:26,900 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:25:26,900 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:25:26,901 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:25:26,901 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:25:26,901 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:26,901 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:25:26,902 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:25:26,902 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:25:26,902 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:25:26,902 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,908 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:25:26,924 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,929 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:26,929 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:26,929 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:26,930 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:26,947 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:26,947 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:25:27,159 - app.utils.memory_management - INFO - Garbage collection: collected 1919 objects
2025-07-17 10:25:27,160 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:27,161 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:25:33,770 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:25:33,814 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,818 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,821 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:33,835 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:33,836 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:25:33,862 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-17 10:25:33,867 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-17 10:25:33,869 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-17 10:25:33,879 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:33,885 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-17 10:25:33,886 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-17 10:25:33,886 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-17 10:25:33,886 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-17 10:25:33,886 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:33,888 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-17 10:25:33,889 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-17 10:25:33,889 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-17 10:25:33,890 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-17 10:25:33,891 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,891 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-17 10:25:33,898 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,900 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:33,901 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:33,901 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:33,901 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:33,905 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:25:39,468 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=True
2025-07-17 10:25:39,503 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:25:39,710 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:39,711 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:25:39,904 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:39,904 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:25:39,906 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-17 10:25:39,910 - models.hybrid_model - INFO - XGBoost is available
2025-07-17 10:25:39,910 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-17 10:25:39,912 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-17 10:25:39,912 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-17 10:25:39,912 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 10:25:39,913 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 10:25:39,916 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 10:25:39,917 - models.predict - INFO - Using fallback price due to error: 87.6
2025-07-17 10:25:39,917 - models.predict - INFO - Prediction for 60 minutes horizon: 87.6
2025-07-17 10:25:39,924 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-17 10:25:45,185 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=True
2025-07-17 10:25:45,219 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:25:45,414 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:45,415 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:25:45,605 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:45,606 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:25:45,607 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-17 10:25:45,608 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-17 10:25:46,180 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 10:25:47,635 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-17 10:25:47,636 - models.predict - INFO - Current price: 87.6, Predicted scaled value: 0.48016396164894104
2025-07-17 10:25:47,636 - models.predict - INFO - Prediction for 60 minutes horizon: 91.189998532617
2025-07-17 10:25:47,637 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 91.19 vs current 87.60 (4.1% change, limit: 2.0%). Applying correction.
2025-07-17 10:25:47,638 - app.pages.predictions_consolidated - INFO - Corrected prediction: 91.19 -> 88.83 (change: 1.4%)
2025-07-17 10:25:47,638 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 91.19 -> 88.83 for 60min
2025-07-17 10:25:47,638 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-17 10:25:52,733 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-17 10:25:52,768 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:25:52,996 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:52,997 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:25:53,186 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:53,186 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:25:53,188 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-17 10:25:53,188 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-17 10:25:53,188 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-17 10:25:53,188 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 10:25:53,208 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 10:25:53,208 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 10:25:53,208 - models.predict - INFO - Using fallback price due to error: 87.6
2025-07-17 10:25:53,209 - models.predict - INFO - Prediction for 60 minutes horizon: 87.6
2025-07-17 10:25:53,210 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-17 10:25:58,229 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-17 10:25:58,257 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:25:58,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:58,450 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:25:58,640 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:25:58,640 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:25:58,642 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-17 10:25:58,642 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-17 10:25:58,642 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-17 10:25:58,642 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-17 10:25:58,642 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-17 10:25:58,643 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-17 10:25:58,643 - models.predict - INFO - Using fallback price due to error: 87.6
2025-07-17 10:25:58,643 - models.predict - INFO - Prediction for 60 minutes horizon: 87.6
2025-07-17 10:25:58,645 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-17 10:26:03,628 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-17 10:26:03,658 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-17 10:26:03,852 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:26:03,853 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-17 10:26:04,044 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:26:04,045 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-17 10:26:04,047 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-17 10:26:04,150 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-17 10:26:04,150 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-17 10:26:04,150 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-17 10:26:04,151 - models.predict - INFO - Ensemble model already loaded
2025-07-17 10:26:04,178 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-17 10:26:04,178 - models.predict - INFO - Current price: 87.6, Predicted scaled value: 0.4207718075204704
2025-07-17 10:26:04,179 - models.predict - INFO - Prediction for 60 minutes horizon: 85.83045020544259
2025-07-17 10:26:04,181 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 85.83 vs current 87.60 (2.0% change, limit: 2.0%). Applying correction.
2025-07-17 10:26:04,182 - app.pages.predictions_consolidated - INFO - Corrected prediction: 85.83 -> 86.37 (change: -1.4%)
2025-07-17 10:26:04,182 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 85.83 -> 86.37 for 60min
2025-07-17 10:26:04,217 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:26:04,218 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:26:04,424 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-17 10:26:04,425 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:26:04,425 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:28:41,194 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:28:41,564 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-17 10:28:42,029 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-17 10:28:42,045 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-17 10:28:42,047 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-17 10:28:42,048 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-17 10:28:42,049 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-17 10:28:42,421 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.39 seconds
2025-07-17 10:28:42,822 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:28:42,823 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:28:43,049 - app.utils.memory_management - INFO - Garbage collection: collected 5822 objects
2025-07-17 10:28:43,050 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:28:43,051 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:29:43,388 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:29:43,408 - app - INFO - Found 14 stock files in data/stocks
2025-07-17 10:29:43,735 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-17 10:29:43,967 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-17 10:29:43,990 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-17 10:29:43,992 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-17 10:29:43,992 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-17 10:29:43,993 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-17 10:29:44,326 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.36 seconds
2025-07-17 10:29:44,840 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:29:44,842 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:29:45,097 - app.utils.memory_management - INFO - Garbage collection: collected 5904 objects
2025-07-17 10:29:45,099 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:29:45,100 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:29:51,965 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:29:52,017 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-07-17 10:29:52,025 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.392
2025-07-17 10:29:52,096 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-17 10:29:52,228 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-17 10:29:52,230 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-07-17 10:29:52,230 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-17 10:29:52,230 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (53.8)
2025-07-17 10:29:52,230 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-17 10:29:59,815 - app.utils.ai_pattern_recognition - INFO - Using live price 52.15 EGP from API for ABUK
2025-07-17 10:29:59,816 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-07-17 10:29:59,816 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-07-17 10:29:59,817 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-07-17 10:29:59,820 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-17 10:29:59,852 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-07-17 10:29:59,852 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 50.7 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 50.69684046821175, 'bearish_score': 0}
2025-07-17 10:29:59,853 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-07-17 10:30:00,744 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:30:00,744 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:30:01,069 - app.utils.memory_management - INFO - Garbage collection: collected 5087 objects
2025-07-17 10:30:01,069 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:30:01,071 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:30:07,101 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:30:07,171 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-07-17 10:30:07,200 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.131
2025-07-17 10:30:07,295 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-17 10:30:07,358 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-17 10:30:07,359 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.97%) exceeds limit (15.00%)
2025-07-17 10:30:07,359 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-17 10:30:07,359 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (54.5)
2025-07-17 10:30:07,360 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-17 10:30:13,500 - app.utils.ai_pattern_recognition - INFO - Using live price 88.00 EGP from API for COMI
2025-07-17 10:30:13,500 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-07-17 10:30:13,500 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-07-17 10:30:13,502 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-17 10:30:13,534 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='BUY', volatility_factor=0.050
2025-07-17 10:30:13,534 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'BUY', 'confidence': 0.5, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 33.5 vs 22.5)', 'risk_level': 'Low', 'bullish_score': 33.5, 'bearish_score': 22.5}
2025-07-17 10:30:13,534 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=87.54, stop=83.16, target=98.48
2025-07-17 10:30:13,627 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:30:13,627 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:30:13,831 - app.utils.memory_management - INFO - Garbage collection: collected 3792 objects
2025-07-17 10:30:13,832 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:30:13,832 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:31:16,507 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:31:16,536 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:16,537 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:31:16,756 - app.utils.memory_management - INFO - Garbage collection: collected 1747 objects
2025-07-17 10:31:16,757 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:16,758 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:31:21,406 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:31:21,436 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:21,436 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:31:21,636 - app.utils.memory_management - INFO - Garbage collection: collected 696 objects
2025-07-17 10:31:21,637 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:21,638 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:31:23,794 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:31:23,934 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-07-17 10:31:23,968 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-11-07 to 2025-07-16
2025-07-17 10:31:23,968 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-07-17 10:31:29,639 - app.pages.smc_analysis - INFO - ✅ Using live price from API: 87.66 EGP for COMI
2025-07-17 10:31:30,770 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:30,772 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:31:30,992 - app.utils.memory_management - INFO - Garbage collection: collected 699 objects
2025-07-17 10:31:30,993 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:31:30,994 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:10,142 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:10,177 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:10,177 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:10,420 - app.utils.memory_management - INFO - Garbage collection: collected 2426 objects
2025-07-17 10:32:10,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:10,421 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:15,287 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:15,326 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:15,327 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:15,533 - app.utils.memory_management - INFO - Garbage collection: collected 766 objects
2025-07-17 10:32:15,534 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:15,535 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:18,564 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:18,601 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:18,601 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:18,819 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 10:32:18,821 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:18,823 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:24,669 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:24,714 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:24,714 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:24,915 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 10:32:24,917 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:24,917 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 10:32:26,367 - app - INFO - Using TensorFlow-based LSTM model
2025-07-17 10:32:32,151 - app.pages.advanced_technical_analysis - INFO - Loaded 782 days of historical data for COMI
2025-07-17 10:32:32,230 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:32,231 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 10:32:32,464 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-17 10:32:32,465 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 10:32:32,465 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 11:26:18,494 - app - INFO - Cleaning up resources...
2025-07-17 11:26:18,496 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 11:26:18,497 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-17 11:26:19,128 - app.utils.memory_management - INFO - Garbage collection: collected 1800 objects
2025-07-17 11:26:19,128 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-17 11:26:19,129 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-17 11:26:19,129 - app - INFO - Application shutdown complete
