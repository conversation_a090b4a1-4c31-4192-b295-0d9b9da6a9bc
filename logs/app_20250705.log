2025-07-05 03:25:20,686 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-05 03:25:33,370 - app - INFO - Memory management utilities loaded
2025-07-05 03:25:33,373 - app - INFO - Error handling utilities loaded
2025-07-05 03:25:33,375 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-05 03:25:33,376 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-05 03:25:33,377 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-05 03:25:33,377 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-05 03:25:33,392 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-05 03:25:33,403 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-05 03:25:33,403 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-05 03:25:33,404 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-05 03:25:33,404 - app - INFO - Applied NumPy fix
2025-07-05 03:25:33,406 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-05 03:25:33,407 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-05 03:25:33,407 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-05 03:25:33,408 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-05 03:25:33,408 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-05 03:25:33,408 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-05 03:25:33,408 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-05 03:25:33,408 - app - INFO - Applied NumPy BitGenerator fix
2025-07-05 03:26:32,141 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-07-05 03:26:32,142 - app - INFO - Applied TensorFlow fix
2025-07-05 03:26:32,146 - app.config - INFO - Configuration initialized
2025-07-05 03:26:32,169 - models.train - INFO - TensorFlow version: 2.9.1
2025-07-05 03:26:32,693 - models.train - INFO - TensorFlow test successful
2025-07-05 03:26:42,853 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-07-05 03:26:42,854 - models.train - INFO - Transformer model is available
2025-07-05 03:26:42,854 - models.train - INFO - Using TensorFlow-based models
2025-07-05 03:26:42,884 - models.predict - INFO - Transformer model is available for predictions
2025-07-05 03:26:42,885 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-05 03:26:42,895 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-05 03:26:46,182 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-05 03:26:46,183 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-05 03:26:46,183 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-05 03:26:46,183 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-05 03:26:46,184 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-05 03:26:46,184 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-05 03:26:46,184 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-05 03:26:46,185 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-05 03:26:46,185 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-05 03:26:46,185 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-05 03:26:46,731 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-05 03:26:46,736 - app - INFO - Using TensorFlow-based LSTM model
2025-07-05 03:26:47,564 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-05 03:26:52,437 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-05 03:26:52,976 - app.utils.session_state - INFO - Initializing session state
2025-07-05 03:26:52,977 - app.utils.session_state - INFO - Session state initialized
2025-07-05 03:26:57,132 - app - INFO - Found 14 stock files in data/stocks
2025-07-05 03:26:57,192 - app.utils.memory_management - INFO - Memory before cleanup: 426.79 MB
2025-07-05 03:26:57,584 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-05 03:26:57,586 - app.utils.memory_management - INFO - Memory after cleanup: 426.80 MB (freed -0.01 MB)
2025-07-05 03:28:27,804 - app - INFO - Cleaning up resources...
2025-07-05 03:28:27,804 - app.utils.memory_management - INFO - Memory before cleanup: 367.07 MB
2025-07-05 03:28:27,986 - app.utils.memory_management - INFO - Garbage collection: collected 92 objects
2025-07-05 03:28:27,986 - app.utils.memory_management - INFO - Memory after cleanup: 367.07 MB (freed 0.00 MB)
2025-07-05 03:28:27,986 - app - INFO - Application shutdown complete
