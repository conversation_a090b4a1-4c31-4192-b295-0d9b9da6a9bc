2025-06-22 01:35:57,059 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-22 01:36:00,404 - app - INFO - Memory management utilities loaded
2025-06-22 01:36:00,416 - app - INFO - Error handling utilities loaded
2025-06-22 01:36:00,438 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-22 01:36:00,440 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-22 01:36:00,440 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-22 01:36:00,440 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-22 01:36:00,441 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-22 01:36:00,442 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-22 01:36:00,442 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-22 01:36:00,442 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-22 01:36:00,442 - app - INFO - Applied NumPy fix
2025-06-22 01:36:00,468 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 01:36:00,470 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 01:36:00,470 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 01:36:00,470 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-22 01:36:00,470 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 01:36:00,470 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 01:36:00,470 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 01:36:00,470 - app - INFO - Applied NumPy BitGenerator fix
2025-06-22 01:36:22,731 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-22 01:36:22,732 - app - INFO - Applied TensorFlow fix
2025-06-22 01:36:22,755 - app.config - INFO - Configuration initialized
2025-06-22 01:36:22,797 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-22 01:36:22,808 - models.train - INFO - TensorFlow test successful
2025-06-22 01:36:27,559 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-22 01:36:27,559 - models.train - INFO - Transformer model is available
2025-06-22 01:36:27,559 - models.train - INFO - Using TensorFlow-based models
2025-06-22 01:36:27,566 - models.predict - INFO - Transformer model is available for predictions
2025-06-22 01:36:27,566 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-22 01:36:27,594 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-22 01:36:29,409 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 01:36:29,410 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 01:36:29,411 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 01:36:29,411 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 01:36:29,411 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 01:36:29,411 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-22 01:36:29,411 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-22 01:36:29,411 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 01:36:29,411 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 01:36:29,412 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 01:36:29,883 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-22 01:36:29,900 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 01:36:30,878 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-22 01:36:34,557 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-22 01:36:35,028 - app.utils.session_state - INFO - Initializing session state
2025-06-22 01:36:35,029 - app.utils.session_state - INFO - Session state initialized
2025-06-22 01:36:36,186 - app - INFO - Found 14 stock files in data/stocks
2025-06-22 01:36:36,203 - app.utils.memory_management - INFO - Memory before cleanup: 429.84 MB
2025-06-22 01:36:36,401 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-22 01:36:36,401 - app.utils.memory_management - INFO - Memory after cleanup: 429.84 MB (freed -0.00 MB)
2025-06-22 01:37:46,630 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 01:37:46,776 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-22 01:37:46,790 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.334
2025-06-22 01:37:46,939 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 01:37:47,065 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 01:37:47,067 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-22 01:37:47,067 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 01:37:47,069 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (52.2)
2025-06-22 01:37:47,069 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 01:37:52,867 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 01:37:53,117 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-22 01:37:53,135 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.082
2025-06-22 01:37:53,353 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 01:37:53,463 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 01:37:53,466 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-22 01:37:53,468 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 01:37:53,470 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (60.2)
2025-06-22 01:37:53,472 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 01:38:03,517 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-22 01:38:03,519 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-22 01:38:03,520 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-22 01:38:03,522 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-22 01:38:03,526 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 01:38:03,957 - app.utils.ai_pattern_recognition - INFO - Using live price 45.97 EGP from API for ABUK
2025-06-22 01:38:03,957 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-22 01:38:03,958 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-22 01:38:03,959 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-22 01:38:03,960 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 01:38:03,962 - app.utils.memory_management - INFO - Memory before cleanup: 451.77 MB
2025-06-22 01:38:04,129 - app.utils.memory_management - INFO - Garbage collection: collected 137 objects
2025-06-22 01:38:04,129 - app.utils.memory_management - INFO - Memory after cleanup: 451.77 MB (freed 0.00 MB)
2025-06-22 01:38:06,335 - app.utils.memory_management - INFO - Memory before cleanup: 458.62 MB
2025-06-22 01:38:06,522 - app.utils.memory_management - INFO - Garbage collection: collected 1037 objects
2025-06-22 01:38:06,522 - app.utils.memory_management - INFO - Memory after cleanup: 458.66 MB (freed -0.04 MB)
2025-06-22 01:48:41,781 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-22 01:48:43,298 - app - INFO - Memory management utilities loaded
2025-06-22 01:48:43,300 - app - INFO - Error handling utilities loaded
2025-06-22 01:48:43,300 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-22 01:48:43,304 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-22 01:48:43,304 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-22 01:48:43,304 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-22 01:48:43,305 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-22 01:48:43,306 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-22 01:48:43,306 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-22 01:48:43,306 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-22 01:48:43,307 - app - INFO - Applied NumPy fix
2025-06-22 01:48:43,309 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 01:48:43,310 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 01:48:43,310 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 01:48:43,311 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-22 01:48:43,311 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 01:48:43,312 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 01:48:43,312 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 01:48:43,312 - app - INFO - Applied NumPy BitGenerator fix
2025-06-22 01:48:47,745 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-22 01:48:47,747 - app - INFO - Applied TensorFlow fix
2025-06-22 01:48:48,270 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-22 01:48:48,295 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-22 01:48:48,305 - app - INFO - Cleaning up resources...
2025-06-22 01:48:48,311 - app.utils.memory_management - INFO - Memory before cleanup: 322.04 MB
2025-06-22 01:48:48,435 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-22 01:48:48,435 - app.utils.memory_management - INFO - Memory after cleanup: 322.07 MB (freed -0.04 MB)
2025-06-22 01:48:48,435 - app - INFO - Application shutdown complete
2025-06-22 01:49:49,806 - app - INFO - Cleaning up resources...
2025-06-22 01:49:49,807 - app.utils.memory_management - INFO - Memory before cleanup: 456.09 MB
2025-06-22 01:49:50,034 - app.utils.memory_management - INFO - Garbage collection: collected 312 objects
2025-06-22 01:49:50,035 - app.utils.memory_management - INFO - Memory after cleanup: 456.10 MB (freed -0.00 MB)
2025-06-22 01:49:50,036 - app - INFO - Application shutdown complete
2025-06-22 01:50:22,008 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-22 01:50:24,271 - app - INFO - Memory management utilities loaded
2025-06-22 01:50:24,276 - app - INFO - Error handling utilities loaded
2025-06-22 01:50:24,278 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-22 01:50:24,281 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-22 01:50:24,282 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-22 01:50:24,282 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-22 01:50:24,284 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-22 01:50:24,286 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-22 01:50:24,288 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-22 01:50:24,289 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-22 01:50:24,292 - app - INFO - Applied NumPy fix
2025-06-22 01:50:24,295 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 01:50:24,296 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 01:50:24,298 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 01:50:24,299 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-22 01:50:24,301 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 01:50:24,302 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 01:50:24,303 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 01:50:24,304 - app - INFO - Applied NumPy BitGenerator fix
2025-06-22 01:50:28,930 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-22 01:50:28,931 - app - INFO - Applied TensorFlow fix
2025-06-22 01:50:28,933 - app.config - INFO - Configuration initialized
2025-06-22 01:50:28,939 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-22 01:50:28,949 - models.train - INFO - TensorFlow test successful
2025-06-22 01:50:29,509 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-22 01:50:29,510 - models.train - INFO - Transformer model is available
2025-06-22 01:50:29,510 - models.train - INFO - Using TensorFlow-based models
2025-06-22 01:50:29,512 - models.predict - INFO - Transformer model is available for predictions
2025-06-22 01:50:29,512 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-22 01:50:29,515 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-22 01:50:29,861 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 01:50:29,861 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 01:50:29,861 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 01:50:29,862 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 01:50:29,862 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 01:50:29,862 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-22 01:50:29,862 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-22 01:50:29,862 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 01:50:29,862 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 01:50:29,863 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 01:50:29,968 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-22 01:50:29,970 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 01:50:30,349 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-22 01:50:31,002 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-22 01:50:31,207 - app.utils.session_state - INFO - Initializing session state
2025-06-22 01:50:31,209 - app.utils.session_state - INFO - Session state initialized
2025-06-22 01:50:32,389 - app - INFO - Found 14 stock files in data/stocks
2025-06-22 01:50:32,402 - app.utils.memory_management - INFO - Memory before cleanup: 429.57 MB
2025-06-22 01:50:32,567 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-22 01:50:32,568 - app.utils.memory_management - INFO - Memory after cleanup: 429.57 MB (freed -0.00 MB)
2025-06-22 01:50:35,984 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 01:50:36,062 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-22 01:50:36,071 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.336
2025-06-22 01:50:36,172 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 01:50:36,244 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 01:50:36,246 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-22 01:50:36,248 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 01:50:36,249 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: MEDIUM (48.3)
2025-06-22 01:50:36,249 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 01:50:41,336 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 01:50:41,412 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-22 01:50:41,430 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.120
2025-06-22 01:50:41,543 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 01:50:41,653 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 01:50:41,655 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-22 01:50:41,655 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 01:50:41,656 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (63.4)
2025-06-22 01:50:41,656 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 01:50:44,573 - app.utils.ai_pattern_recognition - INFO - Using live price 45.97 EGP from API for ABUK
2025-06-22 01:50:44,573 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-22 01:50:44,573 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-22 01:50:44,573 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-22 01:50:44,573 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 01:50:44,573 - app.utils.memory_management - INFO - Memory before cleanup: 451.00 MB
2025-06-22 01:50:44,758 - app.utils.memory_management - INFO - Garbage collection: collected 56 objects
2025-06-22 01:50:44,759 - app.utils.memory_management - INFO - Memory after cleanup: 451.00 MB (freed 0.00 MB)
2025-06-22 01:50:47,986 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-22 01:50:47,986 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-22 01:50:47,986 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-22 01:50:47,986 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-22 01:50:47,986 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 01:50:48,821 - app.utils.memory_management - INFO - Memory before cleanup: 458.74 MB
2025-06-22 01:50:48,985 - app.utils.memory_management - INFO - Garbage collection: collected 972 objects
2025-06-22 01:50:48,985 - app.utils.memory_management - INFO - Memory after cleanup: 458.78 MB (freed -0.04 MB)
2025-06-22 02:10:40,862 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-22 02:10:45,701 - app - INFO - Memory management utilities loaded
2025-06-22 02:10:45,707 - app - INFO - Error handling utilities loaded
2025-06-22 02:10:45,711 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-22 02:10:45,714 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-22 02:10:45,716 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-22 02:10:45,716 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-22 02:10:45,716 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-22 02:10:45,718 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-22 02:10:45,718 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-22 02:10:45,718 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-22 02:10:45,718 - app - INFO - Applied NumPy fix
2025-06-22 02:10:45,722 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 02:10:45,722 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 02:10:45,722 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 02:10:45,722 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-22 02:10:45,724 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 02:10:45,724 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 02:10:45,724 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 02:10:45,724 - app - INFO - Applied NumPy BitGenerator fix
2025-06-22 02:11:02,472 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-22 02:11:02,472 - app - INFO - Applied TensorFlow fix
2025-06-22 02:11:02,482 - app.config - INFO - Configuration initialized
2025-06-22 02:11:02,504 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-22 02:11:02,518 - models.train - INFO - TensorFlow test successful
2025-06-22 02:11:04,235 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-22 02:11:04,235 - models.train - INFO - Transformer model is available
2025-06-22 02:11:04,235 - models.train - INFO - Using TensorFlow-based models
2025-06-22 02:11:04,243 - models.predict - INFO - Transformer model is available for predictions
2025-06-22 02:11:04,243 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-22 02:11:04,251 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-22 02:11:04,842 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 02:11:04,842 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 02:11:04,842 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 02:11:04,843 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 02:11:04,843 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 02:11:04,843 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-22 02:11:04,843 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-22 02:11:04,844 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 02:11:04,844 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 02:11:04,844 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 02:11:05,221 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-22 02:11:05,236 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:11:06,201 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-22 02:11:08,431 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-22 02:11:08,950 - app.utils.session_state - INFO - Initializing session state
2025-06-22 02:11:08,950 - app.utils.session_state - INFO - Session state initialized
2025-06-22 02:11:10,065 - app - INFO - Found 14 stock files in data/stocks
2025-06-22 02:11:10,079 - app.utils.memory_management - INFO - Memory before cleanup: 440.58 MB
2025-06-22 02:11:10,276 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-22 02:11:10,276 - app.utils.memory_management - INFO - Memory after cleanup: 440.58 MB (freed -0.00 MB)
2025-06-22 02:13:37,319 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:13:37,366 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-22 02:13:37,375 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.332
2025-06-22 02:13:37,454 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:13:37,554 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:13:37,558 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-22 02:13:37,561 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:13:37,565 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (50.5)
2025-06-22 02:13:37,568 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:13:39,616 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 49.90 EGP for ABUK
2025-06-22 02:13:39,616 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for ABUK
2025-06-22 02:13:39,619 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:13:39,656 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-22 02:13:39,656 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.5, 'reasoning': 'AI consensus shows strong bullish signals across multiple models (Score: 48.9 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 48.89827184220253, 'bearish_score': 0}
2025-06-22 02:13:39,656 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-06-22 02:13:41,676 - app.utils.memory_management - INFO - Memory before cleanup: 464.70 MB
2025-06-22 02:13:41,946 - app.utils.memory_management - INFO - Garbage collection: collected 781 objects
2025-06-22 02:13:41,948 - app.utils.memory_management - INFO - Memory after cleanup: 464.70 MB (freed 0.00 MB)
2025-06-22 02:13:43,387 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:13:43,439 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-22 02:13:43,453 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.076
2025-06-22 02:13:43,544 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:13:43,621 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:13:43,624 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-22 02:13:43,624 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:13:43,624 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (59.0)
2025-06-22 02:13:43,624 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:13:45,653 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 78.62 EGP for COMI
2025-06-22 02:13:45,654 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for COMI
2025-06-22 02:13:45,656 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:13:45,709 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='HOLD', volatility_factor=0.050
2025-06-22 02:13:45,720 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'HOLD', 'confidence': 0.5, 'reasoning': 'Mixed AI signals suggest maintaining current position (Bullish: 16.0, Bearish: 17.5)', 'risk_level': 'Moderate', 'bullish_score': 16.0, 'bearish_score': 17.5}
2025-06-22 02:13:45,723 - app.pages.advanced_ai_features - INFO - Using HOLD logic: entry=78.62, stop=74.69, target=86.48
2025-06-22 02:13:45,854 - app.utils.memory_management - INFO - Memory before cleanup: 469.90 MB
2025-06-22 02:13:46,053 - app.utils.memory_management - INFO - Garbage collection: collected 1711 objects
2025-06-22 02:13:46,053 - app.utils.memory_management - INFO - Memory after cleanup: 469.94 MB (freed -0.04 MB)
2025-06-22 02:17:16,318 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-22 02:17:16,325 - app - INFO - Memory management utilities loaded
2025-06-22 02:17:16,328 - app - INFO - Error handling utilities loaded
2025-06-22 02:17:16,330 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-22 02:17:16,330 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-22 02:17:16,331 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-22 02:17:16,331 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-22 02:17:39,194 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-22 02:17:41,140 - app - INFO - Memory management utilities loaded
2025-06-22 02:17:41,143 - app - INFO - Error handling utilities loaded
2025-06-22 02:17:41,145 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-22 02:17:41,151 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-22 02:17:41,152 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-22 02:17:41,153 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-22 02:17:41,153 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-22 02:17:41,156 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-22 02:17:41,156 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-22 02:17:41,156 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-22 02:17:41,157 - app - INFO - Applied NumPy fix
2025-06-22 02:17:41,159 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 02:17:41,159 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 02:17:41,160 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 02:17:41,160 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-22 02:17:41,161 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 02:17:41,167 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 02:17:41,173 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 02:17:41,179 - app - INFO - Applied NumPy BitGenerator fix
2025-06-22 02:17:45,594 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-22 02:17:45,594 - app - INFO - Applied TensorFlow fix
2025-06-22 02:17:45,597 - app.config - INFO - Configuration initialized
2025-06-22 02:17:45,605 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-22 02:17:45,614 - models.train - INFO - TensorFlow test successful
2025-06-22 02:17:46,162 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-22 02:17:46,162 - models.train - INFO - Transformer model is available
2025-06-22 02:17:46,163 - models.train - INFO - Using TensorFlow-based models
2025-06-22 02:17:46,164 - models.predict - INFO - Transformer model is available for predictions
2025-06-22 02:17:46,165 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-22 02:17:46,168 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-22 02:17:46,513 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 02:17:46,514 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 02:17:46,514 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 02:17:46,514 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 02:17:46,514 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 02:17:46,514 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-22 02:17:46,515 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-22 02:17:46,515 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 02:17:46,515 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 02:17:46,515 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 02:17:46,620 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-22 02:17:46,623 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:17:46,995 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-22 02:17:47,593 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-22 02:17:47,766 - app.utils.session_state - INFO - Initializing session state
2025-06-22 02:17:47,768 - app.utils.session_state - INFO - Session state initialized
2025-06-22 02:17:49,070 - app - INFO - Found 14 stock files in data/stocks
2025-06-22 02:17:49,083 - app.utils.memory_management - INFO - Memory before cleanup: 431.12 MB
2025-06-22 02:17:49,267 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-22 02:17:49,267 - app.utils.memory_management - INFO - Memory after cleanup: 431.12 MB (freed -0.01 MB)
2025-06-22 02:18:23,197 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:18:23,237 - app.utils.memory_management - INFO - Memory before cleanup: 433.49 MB
2025-06-22 02:18:23,518 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-22 02:18:23,518 - app.utils.memory_management - INFO - Memory after cleanup: 433.49 MB (freed 0.00 MB)
2025-06-22 02:18:24,514 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:18:24,552 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-22 02:18:24,552 - app - INFO - Date range: 2022-08-05 to 2025-06-19
2025-06-22 02:18:24,552 - app - INFO - Data shape: (750, 6)
2025-06-22 02:18:24,552 - app - INFO - File COMI contains 2025 data
2025-06-22 02:18:24,591 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-22 02:18:24,592 - app - INFO - Features shape: (750, 36)
2025-06-22 02:18:24,610 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-22 02:18:24,612 - app - INFO - Date range: 2022-08-05 to 2025-06-19
2025-06-22 02:18:24,613 - app - INFO - Data shape: (750, 6)
2025-06-22 02:18:24,616 - app - INFO - File COMI contains 2025 data
2025-06-22 02:18:24,624 - app.utils.memory_management - INFO - Memory before cleanup: 437.52 MB
2025-06-22 02:18:24,825 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-06-22 02:18:24,825 - app.utils.memory_management - INFO - Memory after cleanup: 437.56 MB (freed -0.04 MB)
2025-06-22 02:18:24,993 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:18:25,107 - app.utils.memory_management - INFO - Memory before cleanup: 439.80 MB
2025-06-22 02:18:25,295 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-06-22 02:18:25,296 - app.utils.memory_management - INFO - Memory after cleanup: 439.80 MB (freed 0.00 MB)
2025-06-22 02:18:26,724 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:18:26,766 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-22 02:18:26,773 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.337
2025-06-22 02:18:26,855 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:18:26,945 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:18:26,947 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-22 02:18:26,947 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:18:26,948 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (51.6)
2025-06-22 02:18:26,948 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:18:28,979 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 49.90 EGP for ABUK
2025-06-22 02:18:28,979 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for ABUK
2025-06-22 02:18:28,982 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:18:29,018 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-22 02:18:29,018 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 48.8 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 48.84843948843823, 'bearish_score': 0}
2025-06-22 02:18:29,018 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-06-22 02:18:29,727 - app.utils.memory_management - INFO - Memory before cleanup: 458.75 MB
2025-06-22 02:18:29,881 - app.utils.memory_management - INFO - Garbage collection: collected 1068 objects
2025-06-22 02:18:29,882 - app.utils.memory_management - INFO - Memory after cleanup: 458.75 MB (freed 0.00 MB)
2025-06-22 02:18:35,075 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:18:35,121 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-22 02:18:35,130 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.107
2025-06-22 02:18:35,202 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:18:35,304 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:18:35,306 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-22 02:18:35,307 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:18:35,307 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (61.2)
2025-06-22 02:18:35,307 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:18:37,337 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 78.62 EGP for COMI
2025-06-22 02:18:37,337 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for COMI
2025-06-22 02:18:37,341 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:18:37,378 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='SELL', volatility_factor=0.050
2025-06-22 02:18:37,378 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'SELL', 'confidence': 0.5, 'reasoning': 'AI consensus shows bearish signals across multiple models (Score: 17.5 vs 0.0)', 'risk_level': 'Moderate', 'bullish_score': 0, 'bearish_score': 17.5}
2025-06-22 02:18:37,378 - app.pages.advanced_ai_features - INFO - Using SELL logic: entry=79.01, stop=82.96, target=69.14
2025-06-22 02:18:37,495 - app.utils.memory_management - INFO - Memory before cleanup: 464.55 MB
2025-06-22 02:18:37,666 - app.utils.memory_management - INFO - Garbage collection: collected 1676 objects
2025-06-22 02:18:37,667 - app.utils.memory_management - INFO - Memory after cleanup: 464.55 MB (freed 0.00 MB)
2025-06-22 02:19:25,883 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:19:25,888 - app.utils.session_state - INFO - Initializing session state
2025-06-22 02:19:25,889 - app.utils.session_state - INFO - Session state initialized
2025-06-22 02:19:25,904 - app - INFO - Found 14 stock files in data/stocks
2025-06-22 02:19:25,917 - app.utils.memory_management - INFO - Memory before cleanup: 455.63 MB
2025-06-22 02:19:26,139 - app.utils.memory_management - INFO - Garbage collection: collected 289 objects
2025-06-22 02:19:26,139 - app.utils.memory_management - INFO - Memory after cleanup: 455.63 MB (freed 0.00 MB)
2025-06-22 02:19:39,213 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:19:39,258 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-22 02:19:39,269 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.352
2025-06-22 02:19:39,423 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:19:39,500 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:19:39,501 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-22 02:19:39,502 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:19:39,502 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: MEDIUM (49.5)
2025-06-22 02:19:39,502 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:19:41,533 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 49.90 EGP for ABUK
2025-06-22 02:19:41,533 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for ABUK
2025-06-22 02:19:41,533 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:19:41,589 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-22 02:19:41,590 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 48.8 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 48.79868248087273, 'bearish_score': 0}
2025-06-22 02:19:41,591 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-06-22 02:19:41,712 - app.utils.memory_management - INFO - Memory before cleanup: 464.47 MB
2025-06-22 02:19:41,886 - app.utils.memory_management - INFO - Garbage collection: collected 1709 objects
2025-06-22 02:19:41,887 - app.utils.memory_management - INFO - Memory after cleanup: 464.47 MB (freed 0.00 MB)
2025-06-22 02:19:44,009 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:19:44,052 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-22 02:19:44,061 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.175
2025-06-22 02:19:44,143 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:19:44,224 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:19:44,226 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-22 02:19:44,226 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:19:44,227 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (59.5)
2025-06-22 02:19:44,227 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:19:46,269 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 78.62 EGP for COMI
2025-06-22 02:19:46,270 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for COMI
2025-06-22 02:19:46,270 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:19:46,289 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='SELL', volatility_factor=0.050
2025-06-22 02:19:46,302 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'SELL', 'confidence': 0.5, 'reasoning': 'AI consensus shows bearish signals across multiple models (Score: 30.6 vs 16.0)', 'risk_level': 'Moderate', 'bullish_score': 16.0, 'bearish_score': 30.55151276595527}
2025-06-22 02:19:46,303 - app.pages.advanced_ai_features - INFO - Using SELL logic: entry=79.01, stop=82.96, target=69.14
2025-06-22 02:19:46,395 - app.utils.memory_management - INFO - Memory before cleanup: 465.25 MB
2025-06-22 02:19:46,568 - app.utils.memory_management - INFO - Garbage collection: collected 1110 objects
2025-06-22 02:19:46,570 - app.utils.memory_management - INFO - Memory after cleanup: 465.25 MB (freed 0.00 MB)
2025-06-22 02:21:50,196 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-22 02:21:51,640 - app - INFO - Memory management utilities loaded
2025-06-22 02:21:51,641 - app - INFO - Error handling utilities loaded
2025-06-22 02:21:51,643 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-22 02:21:51,644 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-22 02:21:51,644 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-22 02:21:51,645 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-22 02:21:51,645 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-22 02:21:51,646 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-22 02:21:51,647 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-22 02:21:51,647 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-22 02:21:51,647 - app - INFO - Applied NumPy fix
2025-06-22 02:21:51,648 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 02:21:51,650 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 02:21:51,650 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 02:21:51,651 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-22 02:21:51,652 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 02:21:51,653 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 02:21:51,653 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 02:21:51,653 - app - INFO - Applied NumPy BitGenerator fix
2025-06-22 02:21:57,496 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-22 02:21:57,498 - app - INFO - Applied TensorFlow fix
2025-06-22 02:21:57,504 - app.config - INFO - Configuration initialized
2025-06-22 02:21:57,516 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-22 02:21:57,527 - models.train - INFO - TensorFlow test successful
2025-06-22 02:21:58,289 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-22 02:21:58,289 - models.train - INFO - Transformer model is available
2025-06-22 02:21:58,291 - models.train - INFO - Using TensorFlow-based models
2025-06-22 02:21:58,295 - models.predict - INFO - Transformer model is available for predictions
2025-06-22 02:21:58,297 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-22 02:21:58,301 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-22 02:21:58,745 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 02:21:58,747 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-22 02:21:58,747 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-22 02:21:58,747 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-22 02:21:58,747 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-22 02:21:58,748 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-22 02:21:58,748 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-22 02:21:58,748 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-22 02:21:58,748 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-22 02:21:58,749 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-22 02:21:58,888 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-22 02:21:58,891 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:21:59,324 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-22 02:21:59,958 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-22 02:22:00,221 - app.utils.session_state - INFO - Initializing session state
2025-06-22 02:22:00,223 - app.utils.session_state - INFO - Session state initialized
2025-06-22 02:22:01,473 - app - INFO - Found 14 stock files in data/stocks
2025-06-22 02:22:01,487 - app.utils.memory_management - INFO - Memory before cleanup: 433.79 MB
2025-06-22 02:22:01,674 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-22 02:22:01,674 - app.utils.memory_management - INFO - Memory after cleanup: 433.80 MB (freed -0.01 MB)
2025-06-22 02:23:18,135 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:23:18,191 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-22 02:23:18,201 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.333
2025-06-22 02:23:18,307 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:23:18,398 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:23:18,398 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-22 02:23:18,400 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:23:18,400 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (51.0)
2025-06-22 02:23:18,400 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:23:20,443 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 49.90 EGP for ABUK
2025-06-22 02:23:20,444 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for ABUK
2025-06-22 02:23:20,446 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:23:20,489 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-22 02:23:20,489 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 48.8 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 48.84985589216926, 'bearish_score': 0}
2025-06-22 02:23:20,490 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-06-22 02:23:21,210 - app.utils.memory_management - INFO - Memory before cleanup: 455.86 MB
2025-06-22 02:23:21,368 - app.utils.memory_management - INFO - Garbage collection: collected 892 objects
2025-06-22 02:23:21,368 - app.utils.memory_management - INFO - Memory after cleanup: 455.86 MB (freed 0.00 MB)
2025-06-22 02:23:23,082 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:23:23,133 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-22 02:23:23,141 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.097
2025-06-22 02:23:23,214 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:23:23,315 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:23:23,317 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-22 02:23:23,317 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:23:23,317 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (62.7)
2025-06-22 02:23:23,318 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:23:25,363 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 78.62 EGP for COMI
2025-06-22 02:23:25,364 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for COMI
2025-06-22 02:23:25,365 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:23:25,396 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='SELL', volatility_factor=0.050
2025-06-22 02:23:25,397 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'SELL', 'confidence': 0.5, 'reasoning': 'AI consensus shows bearish signals across multiple models (Score: 17.5 vs 0.0)', 'risk_level': 'Moderate', 'bullish_score': 0, 'bearish_score': 17.5}
2025-06-22 02:23:25,399 - app.pages.advanced_ai_features - INFO - Using SELL logic: entry=79.01, stop=82.96, target=69.14
2025-06-22 02:23:25,399 - app.pages.advanced_ai_features - WARNING - FORCED SELL OVERRIDE: entry=79.01, stop=82.96, target=69.14
2025-06-22 02:23:25,512 - app.utils.memory_management - INFO - Memory before cleanup: 461.79 MB
2025-06-22 02:23:25,668 - app.utils.memory_management - INFO - Garbage collection: collected 1708 objects
2025-06-22 02:23:25,669 - app.utils.memory_management - INFO - Memory after cleanup: 461.83 MB (freed -0.04 MB)
2025-06-22 02:23:34,802 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:23:34,808 - app.utils.session_state - INFO - Initializing session state
2025-06-22 02:23:34,809 - app.utils.session_state - INFO - Session state initialized
2025-06-22 02:23:34,830 - app.utils.memory_management - INFO - Memory before cleanup: 460.11 MB
2025-06-22 02:23:35,072 - app.utils.memory_management - INFO - Garbage collection: collected 254 objects
2025-06-22 02:23:35,073 - app.utils.memory_management - INFO - Memory after cleanup: 460.11 MB (freed 0.00 MB)
2025-06-22 02:23:46,191 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:23:46,235 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-22 02:23:46,242 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.329
2025-06-22 02:23:46,326 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:23:46,415 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:23:46,416 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-22 02:23:46,417 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:23:46,417 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (51.9)
2025-06-22 02:23:46,418 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:23:48,436 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 49.90 EGP for ABUK
2025-06-22 02:23:48,437 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for ABUK
2025-06-22 02:23:48,439 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:23:48,478 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-22 02:23:48,480 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 48.1 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 48.09765030764083, 'bearish_score': 0}
2025-06-22 02:23:48,482 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-06-22 02:23:48,592 - app.utils.memory_management - INFO - Memory before cleanup: 462.19 MB
2025-06-22 02:23:48,780 - app.utils.memory_management - INFO - Garbage collection: collected 1707 objects
2025-06-22 02:23:48,781 - app.utils.memory_management - INFO - Memory after cleanup: 462.19 MB (freed 0.00 MB)
2025-06-22 02:23:54,671 - app - INFO - Using TensorFlow-based LSTM model
2025-06-22 02:23:54,710 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-22 02:23:54,719 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.174
2025-06-22 02:23:54,779 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-22 02:23:54,867 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-22 02:23:54,870 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-22 02:23:54,870 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-22 02:23:54,870 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (58.7)
2025-06-22 02:23:54,871 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-22 02:23:56,914 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 78.62 EGP for COMI
2025-06-22 02:23:56,914 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for COMI
2025-06-22 02:23:56,916 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-22 02:23:56,955 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='SELL', volatility_factor=0.050
2025-06-22 02:23:56,956 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'SELL', 'confidence': 0.5, 'reasoning': 'AI consensus shows bearish signals across multiple models (Score: 31.8 vs 16.0)', 'risk_level': 'Moderate', 'bullish_score': 16.0, 'bearish_score': 31.80721367673945}
2025-06-22 02:23:56,961 - app.pages.advanced_ai_features - INFO - Using SELL logic: entry=79.01, stop=82.96, target=69.14
2025-06-22 02:23:56,962 - app.pages.advanced_ai_features - WARNING - FORCED SELL OVERRIDE: entry=79.01, stop=82.96, target=69.14
2025-06-22 02:23:57,067 - app.utils.memory_management - INFO - Memory before cleanup: 462.45 MB
2025-06-22 02:23:57,244 - app.utils.memory_management - INFO - Garbage collection: collected 1092 objects
2025-06-22 02:23:57,245 - app.utils.memory_management - INFO - Memory after cleanup: 461.23 MB (freed 1.21 MB)
