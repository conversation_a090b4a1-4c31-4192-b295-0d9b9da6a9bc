2025-07-11 10:38:49,144 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-11 10:38:52,342 - app - INFO - Memory management utilities loaded
2025-07-11 10:38:52,344 - app - INFO - Error handling utilities loaded
2025-07-11 10:38:52,346 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-11 10:38:52,348 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-11 10:38:52,350 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-11 10:38:52,350 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-11 10:38:52,367 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-11 10:38:52,376 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-11 10:38:52,378 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-11 10:38:52,384 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-11 10:38:52,386 - app - INFO - Applied NumPy fix
2025-07-11 10:38:52,390 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-11 10:38:52,392 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-11 10:38:52,394 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-11 10:38:52,396 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-11 10:38:52,398 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-11 10:38:52,398 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-11 10:38:52,398 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-11 10:38:52,400 - app - INFO - Applied NumPy BitGenerator fix
2025-07-11 10:39:12,208 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-07-11 10:39:12,209 - app - INFO - Applied TensorFlow fix
2025-07-11 10:39:12,223 - app.config - INFO - Configuration initialized
2025-07-11 10:39:12,233 - models.train - INFO - TensorFlow version: 2.9.1
2025-07-11 10:39:12,537 - models.train - INFO - TensorFlow test successful
2025-07-11 10:39:17,434 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-07-11 10:39:17,435 - models.train - INFO - Transformer model is available
2025-07-11 10:39:17,435 - models.train - INFO - Using TensorFlow-based models
2025-07-11 10:39:17,445 - models.predict - INFO - Transformer model is available for predictions
2025-07-11 10:39:17,446 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-11 10:39:17,453 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-11 10:39:19,402 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-11 10:39:19,402 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-11 10:39:19,403 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-11 10:39:19,403 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-11 10:39:19,403 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-11 10:39:19,403 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-11 10:39:19,403 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-11 10:39:19,403 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-11 10:39:19,404 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-11 10:39:19,404 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-11 10:39:19,782 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-11 10:39:19,785 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:39:20,361 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-11 10:39:22,912 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-11 10:39:23,255 - app.utils.session_state - INFO - Initializing session state
2025-07-11 10:39:23,256 - app.utils.session_state - INFO - Session state initialized
2025-07-11 10:39:25,012 - app - INFO - Found 14 stock files in data/stocks
2025-07-11 10:39:25,256 - app.utils.memory_management - INFO - Memory before cleanup: 429.16 MB
2025-07-11 10:39:26,084 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-11 10:39:26,239 - app.utils.memory_management - INFO - Memory after cleanup: 429.32 MB (freed -0.16 MB)
2025-07-11 10:39:34,417 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:39:34,714 - app - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.11 seconds
2025-07-11 10:39:34,717 - app - INFO - Date range: 2024-07-01 to 2025-06-13
2025-07-11 10:39:34,718 - app - INFO - Data shape: (250, 6)
2025-07-11 10:39:34,718 - app - INFO - File ABUK contains 2025 data
2025-07-11 10:39:34,887 - app - INFO - Feature engineering for ABUK completed in 0.17 seconds
2025-07-11 10:39:34,893 - app - INFO - Features shape: (250, 36)
2025-07-11 10:39:35,028 - app - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-07-11 10:39:35,035 - app - INFO - Date range: 2024-07-01 to 2025-06-13
2025-07-11 10:39:35,037 - app - INFO - Data shape: (250, 6)
2025-07-11 10:39:35,039 - app - INFO - File ABUK contains 2025 data
2025-07-11 10:39:35,053 - app.utils.memory_management - INFO - Memory before cleanup: 435.50 MB
2025-07-11 10:39:35,325 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-11 10:39:35,325 - app.utils.memory_management - INFO - Memory after cleanup: 435.46 MB (freed 0.04 MB)
2025-07-11 10:39:35,575 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:39:35,722 - app.utils.memory_management - INFO - Memory before cleanup: 436.48 MB
2025-07-11 10:39:36,107 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-07-11 10:39:36,107 - app.utils.memory_management - INFO - Memory after cleanup: 436.52 MB (freed -0.04 MB)
2025-07-11 10:39:40,922 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:39:40,974 - app.utils.data_processing - INFO - Found rf model for ABUK with 4 minutes horizon
2025-07-11 10:39:40,978 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:40,979 - app.utils.data_processing - INFO - Found rf model for ABUK with 30 minutes horizon
2025-07-11 10:39:40,982 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:40,999 - app.utils.data_processing - INFO - Found rf model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:41,000 - app.utils.data_processing - INFO - Found rf model for ABUK with 10080 minutes horizon
2025-07-11 10:39:41,022 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-11 10:39:41,056 - app.utils.data_processing - INFO - Found lstm model for ABUK with 15 minutes horizon using glob
2025-07-11 10:39:41,075 - app.utils.data_processing - INFO - Found lstm model for ABUK with 30 minutes horizon using glob
2025-07-11 10:39:41,083 - app.utils.data_processing - INFO - Found lstm model for ABUK with 60 minutes horizon using glob
2025-07-11 10:39:41,096 - app.utils.data_processing - INFO - Found lstm model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:41,111 - app.utils.data_processing - INFO - Found lstm model for ABUK with 10080 minutes horizon using glob
2025-07-11 10:39:41,162 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-11 10:39:41,163 - app.utils.data_processing - INFO - Found gb model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,164 - app.utils.data_processing - INFO - Found gb model for ABUK with 30 minutes horizon
2025-07-11 10:39:41,164 - app.utils.data_processing - INFO - Found gb model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,165 - app.utils.data_processing - INFO - Found gb model for ABUK with 1440 minutes horizon
2025-07-11 10:39:41,166 - app.utils.data_processing - INFO - Found gb model for ABUK with 10080 minutes horizon
2025-07-11 10:39:41,185 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-11 10:39:41,188 - app.utils.data_processing - INFO - Found lr model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,193 - app.utils.data_processing - INFO - Found lr model for ABUK with 30 minutes horizon
2025-07-11 10:39:41,194 - app.utils.data_processing - INFO - Found lr model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,196 - app.utils.data_processing - INFO - Found lr model for ABUK with 1440 minutes horizon
2025-07-11 10:39:41,196 - app.utils.data_processing - INFO - Found lr model for ABUK with 10080 minutes horizon
2025-07-11 10:39:41,197 - app.utils.data_processing - INFO - Found rf model for ABUK with 4 minutes horizon
2025-07-11 10:39:41,197 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,198 - app.utils.data_processing - INFO - Found rf model for ABUK with 30 minutes horizon
2025-07-11 10:39:41,199 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,292 - app.utils.data_processing - INFO - Found rf model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:41,294 - app.utils.data_processing - INFO - Found rf model for ABUK with 10080 minutes horizon
2025-07-11 10:39:41,431 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,433 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,433 - app.utils.data_processing - INFO - Found gb model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,450 - app.utils.data_processing - INFO - Found lstm model for ABUK with 60 minutes horizon using glob
2025-07-11 10:39:41,451 - app.utils.data_processing - INFO - Found lr model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,480 - app.utils.data_processing - INFO - Found rf model for ABUK with 5 minutes horizon
2025-07-11 10:39:41,484 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,485 - app.utils.data_processing - INFO - Found rf model for ABUK with 30 minutes horizon
2025-07-11 10:39:41,486 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,492 - app.utils.data_processing - INFO - Found rf model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:41,513 - app.utils.data_processing - INFO - Found lstm model for ABUK with 5 minutes horizon using glob
2025-07-11 10:39:41,527 - app.utils.data_processing - INFO - Found lstm model for ABUK with 15 minutes horizon using glob
2025-07-11 10:39:41,551 - app.utils.data_processing - INFO - Found lstm model for ABUK with 30 minutes horizon using glob
2025-07-11 10:39:41,613 - app.utils.data_processing - INFO - Found lstm model for ABUK with 60 minutes horizon using glob
2025-07-11 10:39:41,631 - app.utils.data_processing - INFO - Found lstm model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:41,636 - app.utils.data_processing - INFO - Found gb model for ABUK with 5 minutes horizon
2025-07-11 10:39:41,640 - app.utils.data_processing - INFO - Found gb model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,640 - app.utils.data_processing - INFO - Found gb model for ABUK with 30 minutes horizon
2025-07-11 10:39:41,642 - app.utils.data_processing - INFO - Found gb model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,650 - app.utils.data_processing - INFO - Found gb model for ABUK with 1440 minutes horizon
2025-07-11 10:39:41,667 - app.utils.data_processing - INFO - Found lr model for ABUK with 5 minutes horizon
2025-07-11 10:39:41,667 - app.utils.data_processing - INFO - Found lr model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,667 - app.utils.data_processing - INFO - Found lr model for ABUK with 30 minutes horizon
2025-07-11 10:39:41,669 - app.utils.data_processing - INFO - Found lr model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,669 - app.utils.data_processing - INFO - Found lr model for ABUK with 1440 minutes horizon
2025-07-11 10:39:41,673 - app.utils.data_processing - INFO - Found rf model for ABUK with 5 minutes horizon
2025-07-11 10:39:41,673 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,675 - app.utils.data_processing - INFO - Found rf model for ABUK with 30 minutes horizon
2025-07-11 10:39:41,676 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:41,689 - app.utils.data_processing - INFO - Found rf model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:41,719 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,734 - app.utils.data_processing - INFO - Found lstm model for ABUK with 15 minutes horizon using glob
2025-07-11 10:39:41,735 - app.utils.data_processing - INFO - Found gb model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,739 - app.utils.data_processing - INFO - Found lr model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,742 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:41,774 - app.utils.memory_management - INFO - Memory before cleanup: 437.53 MB
2025-07-11 10:39:42,043 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-07-11 10:39:42,049 - app.utils.memory_management - INFO - Memory after cleanup: 437.53 MB (freed 0.00 MB)
2025-07-11 10:39:48,140 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:39:48,210 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:48,211 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:48,213 - app.utils.data_processing - INFO - Found gb model for ABUK with 60 minutes horizon
2025-07-11 10:39:48,241 - app.utils.data_processing - INFO - Found lstm model for ABUK with 60 minutes horizon using glob
2025-07-11 10:39:48,242 - app.utils.data_processing - INFO - Found lr model for ABUK with 60 minutes horizon
2025-07-11 10:39:48,255 - app.utils.data_processing - INFO - Found rf model for ABUK with 5 minutes horizon
2025-07-11 10:39:48,258 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:48,260 - app.utils.data_processing - INFO - Found rf model for ABUK with 30 minutes horizon
2025-07-11 10:39:48,262 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:48,276 - app.utils.data_processing - INFO - Found rf model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:48,287 - app.utils.data_processing - INFO - Found lstm model for ABUK with 5 minutes horizon using glob
2025-07-11 10:39:48,324 - app.utils.data_processing - INFO - Found lstm model for ABUK with 15 minutes horizon using glob
2025-07-11 10:39:48,348 - app.utils.data_processing - INFO - Found lstm model for ABUK with 30 minutes horizon using glob
2025-07-11 10:39:48,381 - app.utils.data_processing - INFO - Found lstm model for ABUK with 60 minutes horizon using glob
2025-07-11 10:39:48,394 - app.utils.data_processing - INFO - Found lstm model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:48,395 - app.utils.data_processing - INFO - Found gb model for ABUK with 5 minutes horizon
2025-07-11 10:39:48,396 - app.utils.data_processing - INFO - Found gb model for ABUK with 15 minutes horizon
2025-07-11 10:39:48,396 - app.utils.data_processing - INFO - Found gb model for ABUK with 30 minutes horizon
2025-07-11 10:39:48,398 - app.utils.data_processing - INFO - Found gb model for ABUK with 60 minutes horizon
2025-07-11 10:39:48,398 - app.utils.data_processing - INFO - Found gb model for ABUK with 1440 minutes horizon
2025-07-11 10:39:48,400 - app.utils.data_processing - INFO - Found lr model for ABUK with 5 minutes horizon
2025-07-11 10:39:48,403 - app.utils.data_processing - INFO - Found lr model for ABUK with 15 minutes horizon
2025-07-11 10:39:48,405 - app.utils.data_processing - INFO - Found lr model for ABUK with 30 minutes horizon
2025-07-11 10:39:48,406 - app.utils.data_processing - INFO - Found lr model for ABUK with 60 minutes horizon
2025-07-11 10:39:48,406 - app.utils.data_processing - INFO - Found lr model for ABUK with 1440 minutes horizon
2025-07-11 10:39:48,408 - app.utils.data_processing - INFO - Found rf model for ABUK with 5 minutes horizon
2025-07-11 10:39:48,409 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:48,411 - app.utils.data_processing - INFO - Found rf model for ABUK with 30 minutes horizon
2025-07-11 10:39:48,411 - app.utils.data_processing - INFO - Found rf model for ABUK with 60 minutes horizon
2025-07-11 10:39:48,423 - app.utils.data_processing - INFO - Found rf model for ABUK with 1440 minutes horizon using glob
2025-07-11 10:39:48,436 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:48,448 - app.utils.data_processing - INFO - Found lstm model for ABUK with 15 minutes horizon using glob
2025-07-11 10:39:48,448 - app.utils.data_processing - INFO - Found gb model for ABUK with 15 minutes horizon
2025-07-11 10:39:48,449 - app.utils.data_processing - INFO - Found lr model for ABUK with 15 minutes horizon
2025-07-11 10:39:48,450 - app.utils.data_processing - INFO - Found rf model for ABUK with 15 minutes horizon
2025-07-11 10:39:48,468 - app.utils.memory_management - INFO - Memory before cleanup: 437.55 MB
2025-07-11 10:39:48,772 - app.utils.memory_management - INFO - Garbage collection: collected 270 objects
2025-07-11 10:39:48,777 - app.utils.memory_management - INFO - Memory after cleanup: 437.55 MB (freed 0.00 MB)
2025-07-11 10:39:50,323 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:39:50,421 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.07 seconds
2025-07-11 10:39:50,423 - app - INFO - Date range: 2020-09-24 to 2025-07-09
2025-07-11 10:39:50,423 - app - INFO - Data shape: (1250, 36)
2025-07-11 10:39:50,424 - app - INFO - File COMI contains 2025 data
2025-07-11 10:39:50,486 - app - INFO - Feature engineering for COMI completed in 0.06 seconds
2025-07-11 10:39:50,486 - app - INFO - Features shape: (1250, 36)
2025-07-11 10:39:50,517 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-07-11 10:39:50,518 - app - INFO - Date range: 2020-09-24 to 2025-07-09
2025-07-11 10:39:50,521 - app - INFO - Data shape: (1250, 36)
2025-07-11 10:39:50,522 - app - INFO - File COMI contains 2025 data
2025-07-11 10:39:50,540 - app.utils.memory_management - INFO - Memory before cleanup: 440.63 MB
2025-07-11 10:39:50,761 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-07-11 10:39:50,762 - app.utils.memory_management - INFO - Memory after cleanup: 440.63 MB (freed 0.00 MB)
2025-07-11 10:39:50,942 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:39:50,983 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-11 10:39:50,986 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:39:50,988 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:39:50,988 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:39:51,000 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:39:51,009 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-11 10:39:51,017 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-11 10:39:51,043 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:39:51,044 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:39:51,059 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:39:51,083 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:39:51,156 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-11 10:39:51,158 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-07-11 10:39:51,160 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:39:51,164 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:39:51,165 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:39:51,166 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:39:51,168 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-11 10:39:51,171 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-07-11 10:39:51,172 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:39:51,173 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:39:51,174 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:39:51,176 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:39:51,180 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-11 10:39:51,181 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-11 10:39:51,183 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:39:51,184 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:39:51,184 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:39:51,186 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:39:51,186 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-11 10:39:51,258 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:39:51,260 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:39:51,260 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:39:51,272 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:39:51,273 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:39:51,290 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:39:51,291 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:39:51,292 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:39:51,293 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:39:51,293 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:39:51,297 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 10:39:51,316 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:39:51,317 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:39:51,327 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:39:51,339 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:39:51,341 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 10:39:51,341 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:39:51,342 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:39:51,342 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:39:51,342 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:39:51,343 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 10:39:51,343 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:39:51,344 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:39:51,345 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:39:51,347 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:39:51,349 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:39:51,350 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:39:51,351 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:39:51,352 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:39:51,353 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:39:51,358 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:39:51,373 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:39:51,375 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:39:51,375 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:39:51,377 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:39:51,387 - app.utils.memory_management - INFO - Memory before cleanup: 440.61 MB
2025-07-11 10:39:51,586 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-07-11 10:39:51,590 - app.utils.memory_management - INFO - Memory after cleanup: 440.59 MB (freed 0.02 MB)
2025-07-11 10:39:59,984 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:40:00,121 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:00,122 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:00,122 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:00,150 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:00,152 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:00,189 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:40:00,193 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:00,194 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:40:00,210 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:00,214 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:40:00,215 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 10:40:00,236 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:40:00,240 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:40:00,260 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:00,277 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:40:00,278 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 10:40:00,279 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:40:00,279 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:40:00,279 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:00,280 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:40:00,281 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 10:40:00,281 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:40:00,282 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:40:00,282 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:00,282 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:40:00,283 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:40:00,284 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:00,285 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:40:00,286 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:00,288 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:40:00,312 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:00,329 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:40:00,330 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:40:00,331 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:40:00,331 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:00,351 - app.utils.memory_management - INFO - Memory before cleanup: 440.58 MB
2025-07-11 10:40:00,564 - app.utils.memory_management - INFO - Garbage collection: collected 299 objects
2025-07-11 10:40:00,565 - app.utils.memory_management - INFO - Memory after cleanup: 440.58 MB (freed 0.00 MB)
2025-07-11 10:40:02,033 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:40:02,095 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:02,104 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:02,108 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:02,110 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:02,112 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:02,123 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-11 10:40:02,125 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:02,128 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:02,129 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:02,140 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:02,142 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:02,143 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-11 10:40:02,144 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:15,307 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-11 10:40:15,363 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 10:40:15,587 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 10:40:15,771 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.03 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-11 10:40:15,789 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-11 10:40:15,894 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-11 10:40:15,894 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-11 10:40:15,898 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-11 10:40:15,900 - models.predict - INFO - Ensemble model already loaded
2025-07-11 10:40:15,942 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-11 10:40:15,942 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.5492210850303927
2025-07-11 10:40:15,942 - models.predict - INFO - Prediction for 60 minutes horizon: 94.06363135926371
2025-07-11 10:40:15,942 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 94.06 vs current 84.94 (10.7% change, limit: 2.0%). Applying correction.
2025-07-11 10:40:15,942 - app.pages.predictions_consolidated - INFO - Corrected prediction: 94.06 -> 86.13 (change: 1.4%)
2025-07-11 10:40:15,942 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 94.06 -> 86.13 for 60min
2025-07-11 10:40:15,942 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.13 vs current 84.00 (2.5% change, limit: 2.0%). Applying correction.
2025-07-11 10:40:15,950 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.13 -> 85.18 (change: 1.4%)
2025-07-11 10:40:15,963 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:15,964 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:15,965 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:15,982 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:15,982 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:16,307 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:16,307 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:16,307 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:16,328 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:16,329 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:16,349 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:40:16,351 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:16,351 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:40:16,352 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:16,352 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:40:16,354 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 10:40:16,367 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:40:16,368 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:40:16,391 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:16,403 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:40:16,404 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 10:40:16,406 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:40:16,408 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:40:16,408 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:16,409 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:40:16,410 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 10:40:16,411 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:40:16,411 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:40:16,413 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:16,413 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:40:16,413 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:40:16,415 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:16,415 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:40:16,415 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:16,415 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:40:16,423 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:16,433 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:40:16,433 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:40:16,435 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:40:16,435 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:16,447 - app.utils.memory_management - INFO - Memory before cleanup: 445.26 MB
2025-07-11 10:40:16,644 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-07-11 10:40:16,645 - app.utils.memory_management - INFO - Memory after cleanup: 445.26 MB (freed 0.00 MB)
2025-07-11 10:40:36,386 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:40:36,470 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:36,470 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:36,472 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:36,487 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:36,665 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:36,714 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:43,331 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-11 10:40:43,383 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 10:40:43,581 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 10:40:43,750 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-11 10:40:43,750 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-11 10:40:43,785 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-11 10:40:43,785 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-11 10:40:43,785 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-11 10:40:43,785 - models.predict - INFO - Ensemble model already loaded
2025-07-11 10:40:43,817 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-11 10:40:43,817 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.5492210850303927
2025-07-11 10:40:43,817 - models.predict - INFO - Prediction for 60 minutes horizon: 94.06363135926371
2025-07-11 10:40:43,817 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 94.06 vs current 84.94 (10.7% change, limit: 2.0%). Applying correction.
2025-07-11 10:40:43,817 - app.pages.predictions_consolidated - INFO - Corrected prediction: 94.06 -> 86.13 (change: 1.4%)
2025-07-11 10:40:43,817 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 94.06 -> 86.13 for 60min
2025-07-11 10:40:43,929 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:40:43,935 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:43,937 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:40:43,938 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:43,938 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:40:43,940 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 10:40:43,952 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:40:43,954 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:40:43,970 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:40:43,980 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:40:43,984 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 10:40:43,985 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:40:43,985 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:40:43,985 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:40:43,986 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:40:43,986 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 10:40:43,986 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:40:43,986 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:40:43,987 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:40:43,987 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:40:43,988 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:40:43,988 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:43,988 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:40:43,989 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:40:43,989 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:40:44,005 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:44,018 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:40:44,020 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:40:44,021 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:40:44,021 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:40:44,035 - app.utils.memory_management - INFO - Memory before cleanup: 447.71 MB
2025-07-11 10:40:44,235 - app.utils.memory_management - INFO - Garbage collection: collected 36 objects
2025-07-11 10:40:44,235 - app.utils.memory_management - INFO - Memory after cleanup: 447.71 MB (freed 0.00 MB)
2025-07-11 10:41:12,007 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:41:12,139 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:12,144 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:12,145 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:12,160 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:12,160 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:12,180 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:41:12,181 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:12,182 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:41:12,183 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:12,184 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:41:12,185 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 10:41:12,207 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:41:12,207 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:41:12,220 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:12,243 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:41:12,243 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 10:41:12,244 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:41:12,245 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:41:12,247 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:12,247 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:41:12,247 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 10:41:12,249 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:41:12,249 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:41:12,253 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:12,254 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:41:12,265 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:41:12,267 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:12,268 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:41:12,268 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:12,269 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:41:12,281 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:12,305 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:41:12,309 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:41:12,311 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:41:12,312 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:12,334 - app.utils.memory_management - INFO - Memory before cleanup: 447.64 MB
2025-07-11 10:41:12,717 - app.utils.memory_management - INFO - Garbage collection: collected 291 objects
2025-07-11 10:41:12,717 - app.utils.memory_management - INFO - Memory after cleanup: 447.64 MB (freed 0.00 MB)
2025-07-11 10:41:13,754 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:41:13,865 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:13,890 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:13,899 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:13,911 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:13,919 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:14,004 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 10:41:14,188 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 10:41:14,341 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-11 10:41:14,343 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-11 10:41:14,379 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-11 10:41:14,380 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-11 10:41:14,381 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-11 10:41:14,381 - models.predict - INFO - Ensemble model already loaded
2025-07-11 10:41:14,405 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-11 10:41:14,405 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.5709771613587099
2025-07-11 10:41:14,406 - models.predict - INFO - Prediction for 60 minutes horizon: 95.95923824388434
2025-07-11 10:41:14,416 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:41:14,418 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:14,418 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:41:14,418 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:14,418 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:41:14,418 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 10:41:14,434 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:41:14,435 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:41:14,448 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:14,469 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:41:14,471 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 10:41:14,471 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:41:14,471 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:41:14,471 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:14,473 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:41:14,473 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 10:41:14,482 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:41:14,483 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:41:14,484 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:14,484 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:41:14,485 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:41:14,485 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:14,486 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:41:14,486 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:14,488 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:41:14,500 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:14,513 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:41:14,515 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:41:14,516 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:41:14,518 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:14,534 - app.utils.memory_management - INFO - Memory before cleanup: 447.73 MB
2025-07-11 10:41:14,710 - app.utils.memory_management - INFO - Garbage collection: collected 36 objects
2025-07-11 10:41:14,710 - app.utils.memory_management - INFO - Memory after cleanup: 447.73 MB (freed 0.00 MB)
2025-07-11 10:41:29,864 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:41:29,945 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:29,947 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:29,952 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:29,968 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:29,968 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:29,988 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:41:29,988 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:29,989 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:41:29,994 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:29,996 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:41:29,997 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 10:41:30,009 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:41:30,011 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:41:30,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:30,089 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:41:30,093 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 10:41:30,093 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:41:30,094 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:41:30,094 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:30,094 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:41:30,097 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 10:41:30,097 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:41:30,098 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:41:30,099 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:30,101 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:41:30,102 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:41:30,103 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:30,104 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:41:30,106 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:30,107 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:41:30,137 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:30,151 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:30,152 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:30,152 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:30,153 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:30,168 - app.utils.memory_management - INFO - Memory before cleanup: 447.73 MB
2025-07-11 10:41:30,442 - app.utils.memory_management - INFO - Garbage collection: collected 274 objects
2025-07-11 10:41:30,442 - app.utils.memory_management - INFO - Memory after cleanup: 447.73 MB (freed 0.00 MB)
2025-07-11 10:41:32,958 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 10:41:33,041 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:33,050 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:33,051 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:33,069 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:33,081 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:33,101 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:41:33,102 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:33,102 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:41:33,103 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:33,104 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:41:33,104 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 10:41:33,120 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 10:41:33,124 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 10:41:33,141 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:33,159 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 10:41:33,165 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 10:41:33,168 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 10:41:33,168 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 10:41:33,169 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:33,170 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 10:41:33,170 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 10:41:33,170 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 10:41:33,172 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 10:41:33,172 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:33,174 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 10:41:33,175 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 10:41:33,176 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 10:41:33,176 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 10:41:33,177 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:33,177 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 10:41:33,191 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:33,206 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:33,207 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:41:33,208 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:41:33,208 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:33,214 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:41:39,155 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=True
2025-07-11 10:41:39,202 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 10:41:39,401 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 10:41:39,584 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-11 10:41:39,584 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-11 10:41:39,957 - models.hybrid_model - INFO - XGBoost is available
2025-07-11 10:41:39,968 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-11 10:41:39,979 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-11 10:41:39,979 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-11 10:41:39,979 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-11 10:41:40,110 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-11 10:41:40,110 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-11 10:41:40,110 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-11 10:41:40,110 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-11 10:41:40,118 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.42991853177547457
2025-07-11 10:41:40,119 - models.predict - INFO - Prediction for 60 minutes horizon: 83.66880014567354
2025-07-11 10:41:40,133 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 10:41:45,290 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=True
2025-07-11 10:41:45,336 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 10:41:45,534 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 10:41:45,691 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-11 10:41:45,691 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-11 10:41:45,691 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-11 10:41:47,284 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-11 10:41:49,427 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-11 10:41:49,427 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.415570467710495
2025-07-11 10:41:49,427 - models.predict - INFO - Prediction for 60 minutes horizon: 82.41865335394013
2025-07-11 10:41:49,427 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 82.42 vs current 84.94 (3.0% change, limit: 2.0%). Applying correction.
2025-07-11 10:41:49,427 - app.pages.predictions_consolidated - INFO - Corrected prediction: 82.42 -> 83.75 (change: -1.4%)
2025-07-11 10:41:49,427 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 82.42 -> 83.75 for 60min
2025-07-11 10:41:49,427 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 10:42:03,079 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-11 10:42:03,122 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 10:42:03,777 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 10:42:03,952 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-11 10:42:03,954 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-11 10:42:03,954 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-11 10:42:03,954 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-07-11 10:42:03,955 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-07-11 10:42:03,980 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-11 10:42:04,009 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-11 10:42:04,009 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-11 10:42:04,009 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-11 10:42:04,009 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.43182627736366613
2025-07-11 10:42:04,009 - models.predict - INFO - Prediction for 60 minutes horizon: 83.8350220147508
2025-07-11 10:42:04,009 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 10:42:09,933 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-11 10:42:09,989 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 10:42:10,184 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 10:42:10,370 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-11 10:42:10,372 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-11 10:42:10,372 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-11 10:42:10,372 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-07-11 10:42:10,372 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_60min.joblib
2025-07-11 10:42:10,390 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-11 10:42:10,391 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-07-11 10:42:10,391 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-11 10:42:10,392 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-11 10:42:10,393 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.49962935430320876
2025-07-11 10:42:10,393 - models.predict - INFO - Prediction for 60 minutes horizon: 89.7427039655523
2025-07-11 10:42:10,394 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 89.74 vs current 84.94 (5.7% change, limit: 2.0%). Applying correction.
2025-07-11 10:42:10,395 - app.pages.predictions_consolidated - INFO - Corrected prediction: 89.74 -> 86.13 (change: 1.4%)
2025-07-11 10:42:10,395 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 89.74 -> 86.13 for 60min
2025-07-11 10:42:10,396 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 10:42:15,839 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-11 10:42:15,927 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 10:42:16,103 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 10:42:16,269 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-11 10:42:16,269 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-11 10:42:16,313 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-11 10:42:16,313 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-11 10:42:16,313 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-11 10:42:16,313 - models.predict - INFO - Ensemble model already loaded
2025-07-11 10:42:16,338 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-11 10:42:16,338 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.5492210850303927
2025-07-11 10:42:16,338 - models.predict - INFO - Prediction for 60 minutes horizon: 94.06363135926371
2025-07-11 10:42:16,338 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 94.06 vs current 84.94 (10.7% change, limit: 2.0%). Applying correction.
2025-07-11 10:42:16,338 - app.pages.predictions_consolidated - INFO - Corrected prediction: 94.06 -> 86.13 (change: 1.4%)
2025-07-11 10:42:16,338 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 94.06 -> 86.13 for 60min
2025-07-11 10:42:16,345 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.13 vs current 84.00 (2.5% change, limit: 2.0%). Applying correction.
2025-07-11 10:42:16,345 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.13 -> 85.18 (change: 1.4%)
2025-07-11 10:42:16,347 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.13 vs current 84.00 (2.5% change, limit: 2.0%). Applying correction.
2025-07-11 10:42:16,347 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.13 -> 85.18 (change: 1.4%)
2025-07-11 10:42:16,382 - app.utils.memory_management - INFO - Memory before cleanup: 480.36 MB
2025-07-11 10:42:16,695 - app.utils.memory_management - INFO - Garbage collection: collected 36 objects
2025-07-11 10:42:16,696 - app.utils.memory_management - INFO - Memory after cleanup: 480.36 MB (freed 0.00 MB)
2025-07-11 11:00:56,053 - app - INFO - Using TensorFlow-based LSTM model
2025-07-11 11:00:56,357 - app - INFO - Found 14 stock files in data/stocks
2025-07-11 11:00:57,122 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-11 11:00:57,122 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 11:00:57,123 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 11:00:57,124 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:00:57,126 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 11:00:57,127 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-11 11:00:57,127 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-11 11:00:57,270 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 11:00:57,273 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 11:00:57,285 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 11:00:57,310 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 11:00:57,328 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-11 11:00:57,329 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-07-11 11:00:57,329 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 11:00:57,330 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 11:00:57,331 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 11:00:57,338 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 11:00:57,339 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-11 11:00:57,340 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-07-11 11:00:57,341 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 11:00:57,342 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 11:00:57,343 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 11:00:57,344 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 11:00:57,346 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-11 11:00:57,350 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-11 11:00:57,353 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 11:00:57,356 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 11:00:57,357 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:00:57,357 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 11:00:57,358 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-11 11:00:57,433 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:00:57,447 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 11:00:57,448 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:00:57,449 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 11:00:57,449 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 11:00:57,449 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-11 11:00:57,465 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:00:57,466 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:00:57,469 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 11:00:57,482 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 11:00:57,483 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 11:00:57,486 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-11 11:00:57,492 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:17,043 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-11 11:01:17,670 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-11 11:01:19,351 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-11 11:01:19,575 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.25 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.05s
2025-07-11 11:01:19,621 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-11 11:01:19,745 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-11 11:01:19,746 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-11 11:01:19,746 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-11 11:01:19,746 - models.predict - INFO - Ensemble model already loaded
2025-07-11 11:01:19,813 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-11 11:01:19,814 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.5492210850303927
2025-07-11 11:01:19,815 - models.predict - INFO - Prediction for 60 minutes horizon: 94.06363135926371
2025-07-11 11:01:19,817 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 94.06 vs current 84.94 (10.7% change, limit: 2.0%). Applying correction.
2025-07-11 11:01:19,818 - app.pages.predictions_consolidated - INFO - Corrected prediction: 94.06 -> 86.13 (change: 1.4%)
2025-07-11 11:01:19,818 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 94.06 -> 86.13 for 60min
2025-07-11 11:01:19,819 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.13 vs current 84.00 (2.5% change, limit: 2.0%). Applying correction.
2025-07-11 11:01:19,820 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.13 -> 85.18 (change: 1.4%)
2025-07-11 11:01:19,833 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:19,835 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:19,836 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 11:01:19,849 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 11:01:19,851 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 11:01:19,917 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:19,919 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:19,920 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 11:01:19,933 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 11:01:19,934 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 11:01:19,953 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 11:01:19,954 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 11:01:19,955 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 11:01:19,957 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:19,959 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 11:01:19,960 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-11 11:01:19,976 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-11 11:01:19,977 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-11 11:01:19,988 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 11:01:20,003 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-11 11:01:20,004 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-11 11:01:20,005 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-11 11:01:20,006 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-11 11:01:20,008 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 11:01:20,009 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-11 11:01:20,010 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-11 11:01:20,010 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-11 11:01:20,011 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-11 11:01:20,011 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 11:01:20,012 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-11 11:01:20,012 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-11 11:01:20,013 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-11 11:01:20,013 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-11 11:01:20,015 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:20,015 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-11 11:01:20,317 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:20,354 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-11 11:01:20,371 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-11 11:01:20,380 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-11 11:01:20,382 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-11 11:01:20,399 - app.utils.memory_management - INFO - Memory before cleanup: 269.75 MB
2025-07-11 11:01:20,677 - app.utils.memory_management - INFO - Garbage collection: collected 213 objects
2025-07-11 11:01:20,679 - app.utils.memory_management - INFO - Memory after cleanup: 269.75 MB (freed -0.00 MB)
2025-07-11 11:02:26,115 - app - INFO - Cleaning up resources...
2025-07-11 11:02:26,116 - app.utils.memory_management - INFO - Memory before cleanup: 274.43 MB
2025-07-11 11:02:26,342 - app.utils.memory_management - INFO - Garbage collection: collected 357 objects
2025-07-11 11:02:26,344 - app.utils.memory_management - INFO - Memory after cleanup: 274.49 MB (freed -0.06 MB)
2025-07-11 11:02:26,346 - app - INFO - Application shutdown complete
