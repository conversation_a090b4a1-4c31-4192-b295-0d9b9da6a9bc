2025-06-20 00:13:20,600 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 00:13:25,539 - app - INFO - Memory management utilities loaded
2025-06-20 00:13:25,553 - app - INFO - Error handling utilities loaded
2025-06-20 00:13:25,559 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 00:13:25,573 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 00:13:25,577 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 00:13:25,586 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 00:13:25,588 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 00:13:25,594 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 00:13:25,594 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 00:13:25,596 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 00:13:25,598 - app - INFO - Applied NumPy fix
2025-06-20 00:13:25,600 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 00:13:25,600 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 00:13:25,602 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 00:13:25,606 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 00:13:25,608 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 00:13:25,611 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 00:13:25,624 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 00:13:25,629 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 00:13:42,306 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 00:13:42,311 - app - INFO - Applied TensorFlow fix
2025-06-20 00:13:42,321 - app.config - INFO - Configuration initialized
2025-06-20 00:13:42,352 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 00:13:42,378 - models.train - INFO - TensorFlow test successful
2025-06-20 00:13:43,752 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 00:13:43,755 - models.train - INFO - Transformer model is available
2025-06-20 00:13:43,756 - models.train - INFO - Using TensorFlow-based models
2025-06-20 00:13:43,770 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 00:13:43,772 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 00:13:43,816 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 00:13:44,320 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 00:13:44,326 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 00:13:44,329 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 00:13:44,332 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 00:13:44,339 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 00:13:44,346 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 00:13:44,349 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 00:13:44,358 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 00:13:44,362 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 00:13:44,365 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 00:13:44,555 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 00:13:44,575 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:13:45,451 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 00:13:46,985 - app.utils.session_state - INFO - Initializing session state
2025-06-20 00:13:46,988 - app.utils.session_state - INFO - Session state initialized
2025-06-20 00:13:50,192 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 00:13:50,279 - app.utils.memory_management - INFO - Memory before cleanup: 432.56 MB
2025-06-20 00:13:50,702 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 00:13:50,707 - app.utils.memory_management - INFO - Memory after cleanup: 432.58 MB (freed -0.02 MB)
2025-06-20 00:13:55,106 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:13:55,327 - app.utils.memory_management - INFO - Memory before cleanup: 434.91 MB
2025-06-20 00:13:55,703 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 00:13:55,711 - app.utils.memory_management - INFO - Memory after cleanup: 434.92 MB (freed -0.00 MB)
2025-06-20 00:14:00,696 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:14:00,830 - app.utils.memory_management - INFO - Memory before cleanup: 434.94 MB
2025-06-20 00:14:01,195 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 00:14:01,199 - app.utils.memory_management - INFO - Memory after cleanup: 434.98 MB (freed -0.04 MB)
2025-06-20 00:14:01,977 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:14:02,107 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 00:14:02,158 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 00:14:02,162 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 00:14:14,505 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 00:14:15,080 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 00:14:15,085 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 00:14:15,087 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 00:14:15,089 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 00:14:15,093 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 00:14:15,093 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 00:14:15,099 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 00:14:15,216 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 00:14:15,292 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 00:14:15,292 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 00:14:15,312 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 00:14:15,892 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:14:16,057 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:14:16,057 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:14:16,086 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:14:16,702 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:14:16,853 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:14:16,857 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:14:16,892 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:14:17,654 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:14:17,797 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:14:17,799 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:14:17,821 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:14:17,895 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 00:14:17,902 - app.utils.session_state - ERROR - Error tracked: app_crash - name 'display_smc_results' is not defined
2025-06-20 00:14:17,904 - app - ERROR - Application crashed: name 'display_smc_results' is not defined
2025-06-20 00:14:17,909 - app.utils.memory_management - INFO - Memory before cleanup: 437.73 MB
2025-06-20 00:14:18,179 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 00:14:18,183 - app.utils.memory_management - INFO - Memory after cleanup: 437.73 MB (freed 0.00 MB)
2025-06-20 00:14:18,192 - app.utils.memory_management - INFO - Memory before cleanup: 437.73 MB
2025-06-20 00:14:18,408 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-06-20 00:14:18,410 - app.utils.memory_management - INFO - Memory after cleanup: 437.73 MB (freed 0.00 MB)
2025-06-20 00:16:27,007 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 00:16:27,011 - app - INFO - Memory management utilities loaded
2025-06-20 00:16:27,012 - app - INFO - Error handling utilities loaded
2025-06-20 00:16:27,013 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 00:16:27,014 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 00:16:27,014 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 00:16:27,014 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 00:17:47,837 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 00:17:49,598 - app - INFO - Memory management utilities loaded
2025-06-20 00:17:49,600 - app - INFO - Error handling utilities loaded
2025-06-20 00:17:49,602 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 00:17:49,602 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 00:17:49,612 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 00:17:49,614 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 00:17:49,615 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 00:17:49,616 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 00:17:49,619 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 00:17:49,631 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 00:17:49,635 - app - INFO - Applied NumPy fix
2025-06-20 00:17:49,643 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 00:17:49,646 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 00:17:49,650 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 00:17:49,652 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 00:17:49,660 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 00:17:49,660 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 00:17:49,662 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 00:17:49,662 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 00:17:53,391 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 00:17:53,391 - app - INFO - Applied TensorFlow fix
2025-06-20 00:17:53,393 - app.config - INFO - Configuration initialized
2025-06-20 00:17:53,397 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 00:17:53,406 - models.train - INFO - TensorFlow test successful
2025-06-20 00:17:53,839 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 00:17:53,839 - models.train - INFO - Transformer model is available
2025-06-20 00:17:53,839 - models.train - INFO - Using TensorFlow-based models
2025-06-20 00:17:53,839 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 00:17:53,839 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 00:17:53,851 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 00:17:54,161 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 00:17:54,161 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 00:17:54,163 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 00:17:54,163 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 00:17:54,163 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 00:17:54,163 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 00:17:54,163 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 00:17:54,163 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 00:17:54,163 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 00:17:54,163 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 00:17:54,230 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 00:17:54,230 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:17:54,514 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 00:17:55,090 - app.utils.session_state - INFO - Initializing session state
2025-06-20 00:17:55,090 - app.utils.session_state - INFO - Session state initialized
2025-06-20 00:17:56,285 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 00:17:56,305 - app.utils.memory_management - INFO - Memory before cleanup: 430.23 MB
2025-06-20 00:17:56,473 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 00:17:56,473 - app.utils.memory_management - INFO - Memory after cleanup: 430.24 MB (freed -0.01 MB)
2025-06-20 00:18:05,055 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:18:05,078 - app.utils.memory_management - INFO - Memory before cleanup: 433.33 MB
2025-06-20 00:18:05,268 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 00:18:05,270 - app.utils.memory_management - INFO - Memory after cleanup: 433.33 MB (freed 0.00 MB)
2025-06-20 00:18:08,813 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:18:08,837 - app.utils.memory_management - INFO - Memory before cleanup: 433.35 MB
2025-06-20 00:18:09,054 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 00:18:09,057 - app.utils.memory_management - INFO - Memory after cleanup: 433.39 MB (freed -0.04 MB)
2025-06-20 00:18:10,068 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:18:10,096 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 00:18:10,111 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 00:18:10,111 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 00:18:16,197 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 00:18:16,516 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 00:18:16,516 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 00:18:16,518 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 00:18:16,518 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 00:18:16,518 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 00:18:16,518 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 00:18:16,518 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 00:18:16,566 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 00:18:16,627 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 00:18:16,627 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 00:18:16,633 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 00:18:16,943 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:18:16,999 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:18:16,999 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:18:17,011 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:18:17,319 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:18:17,366 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:18:17,366 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:18:17,383 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:18:17,707 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:18:17,759 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:18:17,759 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:18:17,767 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:18:17,784 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 00:18:17,804 - app.utils.memory_management - INFO - Memory before cleanup: 437.23 MB
2025-06-20 00:18:17,994 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 00:18:17,995 - app.utils.memory_management - INFO - Memory after cleanup: 437.23 MB (freed 0.00 MB)
2025-06-20 00:18:46,921 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:18:46,967 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 00:18:46,977 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 00:18:46,980 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 00:18:52,500 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 00:18:52,751 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 00:18:52,751 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 00:18:52,751 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 00:18:52,751 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 00:18:52,753 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 00:18:52,753 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 00:18:52,753 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 00:18:52,807 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 00:18:52,865 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 00:18:52,865 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 00:18:52,874 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 00:18:53,172 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:18:53,218 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:18:53,218 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:18:53,231 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:18:53,535 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:18:53,597 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:18:53,597 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:18:53,609 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:18:53,926 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:18:53,981 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:18:53,981 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:18:53,986 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:18:54,002 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 00:18:54,018 - app.utils.memory_management - INFO - Memory before cleanup: 438.32 MB
2025-06-20 00:18:54,173 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-20 00:18:54,173 - app.utils.memory_management - INFO - Memory after cleanup: 438.32 MB (freed 0.00 MB)
2025-06-20 00:19:16,412 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:16,439 - app.utils.memory_management - INFO - Memory before cleanup: 438.30 MB
2025-06-20 00:19:16,613 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 00:19:16,613 - app.utils.memory_management - INFO - Memory after cleanup: 438.30 MB (freed 0.00 MB)
2025-06-20 00:19:21,997 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:22,030 - app.utils.memory_management - INFO - Memory before cleanup: 438.30 MB
2025-06-20 00:19:22,268 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-20 00:19:22,268 - app.utils.memory_management - INFO - Memory after cleanup: 438.30 MB (freed 0.00 MB)
2025-06-20 00:19:23,170 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:23,191 - app.utils.common - INFO - Found 13 stock files in data/stocks
2025-06-20 00:19:23,203 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-06-20 00:19:23,205 - app.utils.common - INFO - Date range: 2024-07-01 to 2025-06-13
2025-06-20 00:19:23,205 - app.utils.common - INFO - Data shape: (250, 6)
2025-06-20 00:19:23,207 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-20 00:19:23,854 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.66 seconds
2025-06-20 00:19:23,856 - app.utils.memory_management - INFO - Memory before cleanup: 444.89 MB
2025-06-20 00:19:24,026 - app.utils.memory_management - INFO - Garbage collection: collected 1782 objects
2025-06-20 00:19:24,027 - app.utils.memory_management - INFO - Memory after cleanup: 444.89 MB (freed 0.00 MB)
2025-06-20 00:19:34,066 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:34,096 - app.utils.common - INFO - Found 13 stock files in data/stocks
2025-06-20 00:19:34,109 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-06-20 00:19:34,110 - app.utils.common - INFO - Date range: 2024-07-01 to 2025-06-13
2025-06-20 00:19:34,111 - app.utils.common - INFO - Data shape: (250, 6)
2025-06-20 00:19:34,112 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-20 00:19:34,395 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.30 seconds
2025-06-20 00:19:34,397 - app.utils.memory_management - INFO - Memory before cleanup: 446.87 MB
2025-06-20 00:19:34,609 - app.utils.memory_management - INFO - Garbage collection: collected 1447 objects
2025-06-20 00:19:34,609 - app.utils.memory_management - INFO - Memory after cleanup: 446.87 MB (freed 0.00 MB)
2025-06-20 00:19:35,354 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:35,402 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-20 00:19:35,403 - app - INFO - Date range: 2022-08-05 to 2025-06-19
2025-06-20 00:19:35,403 - app - INFO - Data shape: (750, 6)
2025-06-20 00:19:35,403 - app - INFO - File COMI contains 2025 data
2025-06-20 00:19:35,443 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-20 00:19:35,444 - app - INFO - Features shape: (750, 36)
2025-06-20 00:19:35,454 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-20 00:19:35,455 - app - INFO - Date range: 2022-08-05 to 2025-06-19
2025-06-20 00:19:35,456 - app - INFO - Data shape: (750, 6)
2025-06-20 00:19:35,456 - app - INFO - File COMI contains 2025 data
2025-06-20 00:19:35,460 - app.utils.memory_management - INFO - Memory before cleanup: 449.05 MB
2025-06-20 00:19:35,645 - app.utils.memory_management - INFO - Garbage collection: collected 243 objects
2025-06-20 00:19:35,646 - app.utils.memory_management - INFO - Memory after cleanup: 449.05 MB (freed 0.00 MB)
2025-06-20 00:19:35,820 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:36,130 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-20 00:19:36,473 - app.utils.common - INFO - Found 13 stock files in data/stocks
2025-06-20 00:19:36,486 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-06-20 00:19:36,487 - app.utils.common - INFO - Date range: 2024-07-01 to 2025-06-13
2025-06-20 00:19:36,487 - app.utils.common - INFO - Data shape: (250, 6)
2025-06-20 00:19:36,488 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-20 00:19:36,742 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.27 seconds
2025-06-20 00:19:37,056 - app.utils.memory_management - INFO - Memory before cleanup: 450.35 MB
2025-06-20 00:19:37,262 - app.utils.memory_management - INFO - Garbage collection: collected 2611 objects
2025-06-20 00:19:37,282 - app.utils.memory_management - INFO - Memory after cleanup: 450.33 MB (freed 0.02 MB)
2025-06-20 00:19:48,031 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:48,062 - app.utils.memory_management - INFO - Memory before cleanup: 451.50 MB
2025-06-20 00:19:48,282 - app.utils.memory_management - INFO - Garbage collection: collected 355 objects
2025-06-20 00:19:48,286 - app.utils.memory_management - INFO - Memory after cleanup: 451.50 MB (freed 0.00 MB)
2025-06-20 00:19:50,995 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:51,021 - app.utils.memory_management - INFO - Memory before cleanup: 451.48 MB
2025-06-20 00:19:51,205 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-06-20 00:19:51,206 - app.utils.memory_management - INFO - Memory after cleanup: 451.48 MB (freed 0.00 MB)
2025-06-20 00:19:52,138 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:52,157 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-20 00:19:52,163 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-20 00:19:52,241 - app.utils.memory_management - INFO - Memory before cleanup: 451.49 MB
2025-06-20 00:19:52,438 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-06-20 00:19:52,443 - app.utils.memory_management - INFO - Memory after cleanup: 451.49 MB (freed 0.00 MB)
2025-06-20 00:19:54,726 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:54,747 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-20 00:19:54,770 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-20 00:19:54,852 - app.utils.memory_management - INFO - Memory before cleanup: 451.51 MB
2025-06-20 00:19:55,039 - app.utils.memory_management - INFO - Garbage collection: collected 245 objects
2025-06-20 00:19:55,039 - app.utils.memory_management - INFO - Memory after cleanup: 451.51 MB (freed 0.00 MB)
2025-06-20 00:19:55,747 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:55,768 - app.utils.state_manager - INFO - Found 13 stock files in data/stocks
2025-06-20 00:19:56,278 - app.utils.memory_management - INFO - Memory before cleanup: 462.28 MB
2025-06-20 00:19:56,483 - app.utils.memory_management - INFO - Garbage collection: collected 600 objects
2025-06-20 00:19:56,484 - app.utils.memory_management - INFO - Memory after cleanup: 462.28 MB (freed 0.00 MB)
2025-06-20 00:19:58,135 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:58,432 - app.utils.memory_management - INFO - Memory before cleanup: 466.19 MB
2025-06-20 00:19:58,610 - app.utils.memory_management - INFO - Garbage collection: collected 462 objects
2025-06-20 00:19:58,611 - app.utils.memory_management - INFO - Memory after cleanup: 466.15 MB (freed 0.04 MB)
2025-06-20 00:19:59,051 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:19:59,086 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-06-20 00:19:59,207 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-20 00:19:59,209 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:19:59,209 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:19:59,210 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:19:59,215 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-20 00:19:59,219 - app.utils.error_handling - INFO - live_trading_component executed in 0.15 seconds
2025-06-20 00:19:59,221 - app.utils.memory_management - INFO - Memory before cleanup: 467.98 MB
2025-06-20 00:19:59,411 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-06-20 00:19:59,412 - app.utils.memory_management - INFO - Memory after cleanup: 467.98 MB (freed -0.01 MB)
2025-06-20 00:20:00,700 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:00,744 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-20 00:20:00,747 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:00,747 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:00,750 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:00,756 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-20 00:20:00,759 - app.utils.error_handling - INFO - live_trading_component executed in 0.03 seconds
2025-06-20 00:20:00,761 - app.utils.memory_management - INFO - Memory before cleanup: 468.82 MB
2025-06-20 00:20:00,955 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-06-20 00:20:00,956 - app.utils.memory_management - INFO - Memory after cleanup: 468.82 MB (freed 0.00 MB)
2025-06-20 00:20:01,965 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:01,992 - app.utils.memory_management - INFO - Memory before cleanup: 468.81 MB
2025-06-20 00:20:02,215 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-20 00:20:02,217 - app.utils.memory_management - INFO - Memory after cleanup: 468.81 MB (freed 0.00 MB)
2025-06-20 00:20:05,665 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:05,709 - app.utils.memory_management - INFO - Memory before cleanup: 468.81 MB
2025-06-20 00:20:06,000 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-20 00:20:06,002 - app.utils.memory_management - INFO - Memory after cleanup: 468.81 MB (freed 0.00 MB)
2025-06-20 00:20:06,651 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:06,679 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-20 00:20:06,680 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:06,681 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:06,681 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:06,682 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-20 00:20:06,685 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-20 00:20:06,687 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-20 00:20:06,701 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-20 00:20:06,703 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-20 00:20:06,715 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:06,725 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-20 00:20:06,735 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-06-20 00:20:06,737 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-20 00:20:06,739 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-20 00:20:06,740 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-20 00:20:06,741 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:06,742 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-20 00:20:06,743 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-06-20 00:20:06,744 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-20 00:20:06,744 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-20 00:20:06,745 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-20 00:20:06,745 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-20 00:20:06,747 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-20 00:20:06,747 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-06-20 00:20:06,748 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-20 00:20:06,748 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:06,749 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:06,750 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:06,750 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-20 00:20:06,751 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-20 00:20:06,767 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:06,781 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:06,782 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:06,782 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:06,790 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-20 00:20:06,790 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:06,791 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:06,793 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:06,793 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-20 00:20:06,793 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-20 00:20:06,804 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-20 00:20:06,805 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-20 00:20:06,815 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:06,828 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-20 00:20:06,830 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-20 00:20:06,830 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-20 00:20:06,830 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:06,832 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-20 00:20:06,844 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:06,855 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-20 00:20:06,856 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-20 00:20:06,857 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-20 00:20:06,858 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:06,870 - app.utils.memory_management - INFO - Memory before cleanup: 468.84 MB
2025-06-20 00:20:07,057 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-20 00:20:07,059 - app.utils.memory_management - INFO - Memory after cleanup: 468.84 MB (freed 0.00 MB)
2025-06-20 00:20:16,350 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:16,391 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:16,407 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:16,409 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:16,411 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:16,418 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-20 00:20:16,420 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:16,422 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:16,423 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:16,423 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-20 00:20:16,425 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-20 00:20:16,435 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-20 00:20:16,436 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-20 00:20:16,446 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:16,457 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-20 00:20:16,457 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-20 00:20:16,458 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-20 00:20:16,459 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-20 00:20:16,459 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:16,460 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-20 00:20:16,460 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-20 00:20:16,461 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-20 00:20:16,461 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-20 00:20:16,462 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-20 00:20:16,462 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-20 00:20:16,463 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-20 00:20:16,464 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:16,465 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:16,466 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:16,466 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-20 00:20:16,473 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:16,490 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-20 00:20:16,490 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-20 00:20:16,490 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-20 00:20:16,490 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:16,514 - app.utils.memory_management - INFO - Memory before cleanup: 468.82 MB
2025-06-20 00:20:16,721 - app.utils.memory_management - INFO - Garbage collection: collected 261 objects
2025-06-20 00:20:16,723 - app.utils.memory_management - INFO - Memory after cleanup: 468.82 MB (freed 0.00 MB)
2025-06-20 00:20:17,892 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:17,931 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:17,941 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:17,942 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:17,943 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:17,943 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-20 00:20:17,943 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-06-20 00:20:17,944 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:17,960 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:17,960 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:17,961 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:17,961 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-20 00:20:17,962 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-06-20 00:20:17,962 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-06-20 00:20:24,663 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-20 00:20:24,858 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-20 00:20:25,022 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-20 00:20:25,025 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-06-20 00:20:25,027 - models.hybrid_model - INFO - XGBoost is available
2025-06-20 00:20:25,027 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-06-20 00:20:25,027 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-06-20 00:20:25,027 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-06-20 00:20:25,030 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-06-20 00:20:25,067 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-20 00:20:25,067 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-20 00:20:25,069 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-20 00:20:25,075 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-20 00:20:25,075 - models.predict - INFO - Current price: 78.62, Predicted scaled value: 0.7456881201267243
2025-06-20 00:20:25,075 - models.predict - INFO - Prediction for 60 minutes horizon: 74.31239869751296
2025-06-20 00:20:25,075 - app.pages.predictions_consolidated - WARNING - Unrealistic prediction detected: 74.31 vs current 78.62 (5.5% change). Correcting to reasonable range.
2025-06-20 00:20:25,075 - app.pages.predictions_consolidated - INFO - Corrected prediction: 80.96 (change: 3.0%)
2025-06-20 00:20:25,102 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:25,112 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:25,113 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:25,113 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:25,118 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-20 00:20:25,118 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:25,121 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:25,123 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:25,124 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-20 00:20:25,125 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-20 00:20:25,134 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-20 00:20:25,136 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-20 00:20:25,147 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-20 00:20:25,159 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-20 00:20:25,159 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-20 00:20:25,160 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-20 00:20:25,160 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-20 00:20:25,160 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-20 00:20:25,161 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-20 00:20:25,161 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-20 00:20:25,161 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-20 00:20:25,161 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-20 00:20:25,162 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-20 00:20:25,162 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-20 00:20:25,162 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-20 00:20:25,162 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:25,162 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-20 00:20:25,163 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-20 00:20:25,163 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-20 00:20:25,170 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:25,181 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-20 00:20:25,183 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-20 00:20:25,183 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-20 00:20:25,183 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-20 00:20:25,197 - app.utils.memory_management - INFO - Memory before cleanup: 471.00 MB
2025-06-20 00:20:25,391 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-20 00:20:25,391 - app.utils.memory_management - INFO - Memory after cleanup: 471.00 MB (freed 0.00 MB)
2025-06-20 00:20:34,652 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:34,701 - app.utils.memory_management - INFO - Memory before cleanup: 471.25 MB
2025-06-20 00:20:34,901 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-06-20 00:20:34,902 - app.utils.memory_management - INFO - Memory after cleanup: 471.25 MB (freed 0.00 MB)
2025-06-20 00:20:42,399 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:42,449 - app.utils.memory_management - INFO - Memory before cleanup: 471.25 MB
2025-06-20 00:20:42,719 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-06-20 00:20:42,721 - app.utils.memory_management - INFO - Memory after cleanup: 471.25 MB (freed 0.00 MB)
2025-06-20 00:20:43,315 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:43,398 - app.utils.memory_management - INFO - Memory before cleanup: 471.27 MB
2025-06-20 00:20:43,617 - app.utils.memory_management - INFO - Garbage collection: collected 209 objects
2025-06-20 00:20:43,619 - app.utils.memory_management - INFO - Memory after cleanup: 471.27 MB (freed 0.00 MB)
2025-06-20 00:20:50,925 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:51,003 - app.utils.memory_management - INFO - Memory before cleanup: 471.27 MB
2025-06-20 00:20:51,293 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-06-20 00:20:51,293 - app.utils.memory_management - INFO - Memory after cleanup: 471.27 MB (freed 0.00 MB)
2025-06-20 00:20:51,948 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:20:51,979 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 7 days
2025-06-20 00:20:51,988 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.198
2025-06-20 00:20:52,006 - app.utils.memory_management - INFO - Memory before cleanup: 471.49 MB
2025-06-20 00:20:52,205 - app.utils.memory_management - INFO - Garbage collection: collected 685 objects
2025-06-20 00:20:52,206 - app.utils.memory_management - INFO - Memory after cleanup: 471.49 MB (freed 0.00 MB)
2025-06-20 00:21:00,826 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:21:00,864 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 7 days
2025-06-20 00:21:00,872 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.097
2025-06-20 00:21:00,894 - app.utils.memory_management - INFO - Memory before cleanup: 471.57 MB
2025-06-20 00:21:01,115 - app.utils.memory_management - INFO - Garbage collection: collected 595 objects
2025-06-20 00:21:01,115 - app.utils.memory_management - INFO - Memory after cleanup: 471.57 MB (freed 0.00 MB)
2025-06-20 00:21:01,967 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:21:01,998 - app.utils.memory_management - INFO - Memory before cleanup: 471.55 MB
2025-06-20 00:21:02,202 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-20 00:21:02,202 - app.utils.memory_management - INFO - Memory after cleanup: 471.55 MB (freed 0.00 MB)
2025-06-20 00:21:07,289 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:21:07,322 - app.utils.memory_management - INFO - Memory before cleanup: 471.55 MB
2025-06-20 00:21:07,521 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-20 00:21:07,521 - app.utils.memory_management - INFO - Memory after cleanup: 471.55 MB (freed 0.00 MB)
2025-06-20 00:21:10,251 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:21:10,288 - app.utils.memory_management - INFO - Memory before cleanup: 471.55 MB
2025-06-20 00:21:10,492 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-20 00:21:10,494 - app.utils.memory_management - INFO - Memory after cleanup: 471.55 MB (freed 0.00 MB)
2025-06-20 00:21:11,945 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:21:18,511 - app.utils.memory_management - INFO - Memory before cleanup: 471.55 MB
2025-06-20 00:21:18,691 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-20 00:21:18,691 - app.utils.memory_management - INFO - Memory after cleanup: 471.55 MB (freed 0.00 MB)
2025-06-20 00:21:28,575 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:21:28,611 - app.utils.memory_management - INFO - Memory before cleanup: 471.54 MB
2025-06-20 00:21:28,829 - app.utils.memory_management - INFO - Garbage collection: collected 239 objects
2025-06-20 00:21:28,830 - app.utils.memory_management - INFO - Memory after cleanup: 471.54 MB (freed 0.00 MB)
2025-06-20 00:21:32,827 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:21:32,855 - app.utils.memory_management - INFO - Memory before cleanup: 471.55 MB
2025-06-20 00:21:33,055 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 00:21:33,055 - app.utils.memory_management - INFO - Memory after cleanup: 471.55 MB (freed 0.00 MB)
2025-06-20 00:21:34,557 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:21:34,581 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 00:21:34,590 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 00:21:34,590 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 00:21:40,600 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 00:21:40,860 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 00:21:40,860 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 00:21:40,861 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 00:21:40,861 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 00:21:40,861 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 00:21:40,863 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 00:21:40,863 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 00:21:40,913 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 00:21:40,970 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 00:21:40,970 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 00:21:40,977 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 00:21:41,298 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:21:41,356 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:21:41,356 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:21:41,371 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:21:41,681 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:21:41,730 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:21:41,730 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:21:41,755 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:21:42,064 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 00:21:42,114 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 00:21:42,114 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 00:21:42,137 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 00:21:42,148 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 00:21:42,167 - app.utils.memory_management - INFO - Memory before cleanup: 471.57 MB
2025-06-20 00:21:42,348 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 00:21:42,348 - app.utils.memory_management - INFO - Memory after cleanup: 471.57 MB (freed 0.00 MB)
2025-06-20 00:31:03,153 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:31:03,192 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 00:31:03,270 - app.utils.memory_management - INFO - Memory before cleanup: 471.72 MB
2025-06-20 00:31:03,603 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 00:31:03,604 - app.utils.memory_management - INFO - Memory after cleanup: 471.72 MB (freed 0.00 MB)
2025-06-20 00:31:05,041 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:31:05,142 - app.utils.memory_management - INFO - Memory before cleanup: 471.70 MB
2025-06-20 00:31:05,349 - app.utils.memory_management - INFO - Garbage collection: collected 226 objects
2025-06-20 00:31:05,350 - app.utils.memory_management - INFO - Memory after cleanup: 471.70 MB (freed 0.00 MB)
2025-06-20 00:31:07,891 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:31:07,919 - app.utils.memory_management - INFO - Memory before cleanup: 471.70 MB
2025-06-20 00:31:08,132 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-20 00:31:08,132 - app.utils.memory_management - INFO - Memory after cleanup: 471.70 MB (freed 0.00 MB)
2025-06-20 00:31:10,136 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 00:31:10,172 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/ABUK.csv
2025-06-20 00:31:10,180 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-07 to 2025-06-13
2025-06-20 00:31:10,181 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 00:31:19,033 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 249 days)
2025-06-20 00:31:19,301 - app.pages.smc_analysis - INFO - Enhanced 2 order_blocks with live data context
2025-06-20 00:31:19,301 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 00:31:19,301 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 00:31:19,301 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/5 structures selected for market status: WEEKEND
2025-06-20 00:31:19,301 - app.pages.smc_analysis - INFO - Using live price 45.97 for market structure analysis
2025-06-20 00:31:19,355 - app.components.advanced_smc_features - INFO - Found 11 high swing points with lookback 10
2025-06-20 00:31:19,417 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-20 00:31:19,417 - app.components.advanced_smc_features - INFO - Found 11 swing highs and 8 swing lows
2025-06-20 00:31:19,426 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 00:31:19,759 - app.components.advanced_smc_features - INFO - Found 17 high swing points with lookback 7
2025-06-20 00:31:19,815 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 7
2025-06-20 00:31:19,815 - app.components.advanced_smc_features - INFO - Found 17 swing highs and 11 swing lows
2025-06-20 00:31:19,824 - app.components.advanced_smc_features - INFO - Detected 17 BOS events
2025-06-20 00:31:20,160 - app.components.advanced_smc_features - INFO - Found 17 high swing points with lookback 7
2025-06-20 00:31:20,205 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 7
2025-06-20 00:31:20,205 - app.components.advanced_smc_features - INFO - Found 17 swing highs and 11 swing lows
2025-06-20 00:31:20,223 - app.components.advanced_smc_features - INFO - Detected 17 BOS events
2025-06-20 00:31:20,552 - app.components.advanced_smc_features - INFO - Found 17 high swing points with lookback 7
2025-06-20 00:31:20,608 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 7
2025-06-20 00:31:20,608 - app.components.advanced_smc_features - INFO - Found 17 swing highs and 11 swing lows
2025-06-20 00:31:20,620 - app.components.advanced_smc_features - INFO - Detected 17 BOS events
2025-06-20 00:31:20,641 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 00:31:20,641 - app.utils.memory_management - INFO - Memory before cleanup: 471.72 MB
2025-06-20 00:31:20,846 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 00:31:20,848 - app.utils.memory_management - INFO - Memory after cleanup: 471.72 MB (freed 0.00 MB)
2025-06-20 01:00:29,924 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 01:00:30,949 - app - INFO - Memory management utilities loaded
2025-06-20 01:00:30,949 - app - INFO - Error handling utilities loaded
2025-06-20 01:00:30,949 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 01:00:30,949 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 01:00:30,949 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 01:00:30,949 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 01:00:30,949 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 01:00:30,949 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 01:00:30,949 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 01:00:30,949 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 01:00:30,949 - app - INFO - Applied NumPy fix
2025-06-20 01:00:30,949 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 01:00:30,949 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 01:00:30,949 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 01:00:30,949 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 01:00:30,956 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 01:00:30,957 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 01:00:30,957 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 01:00:30,957 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 01:00:34,427 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 01:00:34,427 - app - INFO - Applied TensorFlow fix
2025-06-20 01:00:34,429 - app.config - INFO - Configuration initialized
2025-06-20 01:00:34,431 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 01:00:34,443 - models.train - INFO - TensorFlow test successful
2025-06-20 01:00:34,890 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 01:00:34,890 - models.train - INFO - Transformer model is available
2025-06-20 01:00:34,890 - models.train - INFO - Using TensorFlow-based models
2025-06-20 01:00:34,890 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 01:00:34,896 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 01:00:34,898 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 01:00:35,070 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 01:00:35,070 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 01:00:35,070 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 01:00:35,070 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 01:00:35,070 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 01:00:35,070 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 01:00:35,070 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 01:00:35,070 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 01:00:35,077 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 01:00:35,077 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 01:00:35,155 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 01:00:35,157 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:00:35,584 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 01:00:36,151 - app.utils.session_state - INFO - Initializing session state
2025-06-20 01:00:36,158 - app.utils.session_state - INFO - Session state initialized
2025-06-20 01:00:37,412 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 01:00:37,427 - app.utils.memory_management - INFO - Memory before cleanup: 414.27 MB
2025-06-20 01:00:37,575 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-06-20 01:00:37,575 - app.utils.memory_management - INFO - Memory after cleanup: 414.64 MB (freed -0.37 MB)
2025-06-20 01:00:37,927 - app - INFO - Cleaning up resources...
2025-06-20 01:00:37,927 - app.utils.memory_management - INFO - Memory before cleanup: 414.80 MB
2025-06-20 01:00:38,087 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-06-20 01:00:38,089 - app.utils.memory_management - INFO - Memory after cleanup: 414.80 MB (freed 0.00 MB)
2025-06-20 01:00:38,089 - app - INFO - Application shutdown complete
2025-06-20 01:06:21,228 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 01:06:21,237 - app - INFO - Memory management utilities loaded
2025-06-20 01:06:21,298 - app - INFO - Error handling utilities loaded
2025-06-20 01:06:21,306 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 01:06:21,310 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 01:06:21,321 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 01:06:21,329 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 01:21:25,107 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 01:21:26,535 - app - INFO - Memory management utilities loaded
2025-06-20 01:21:26,537 - app - INFO - Error handling utilities loaded
2025-06-20 01:21:26,538 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 01:21:26,539 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 01:21:26,539 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 01:21:26,539 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 01:21:26,541 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 01:21:26,541 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 01:21:26,542 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 01:21:26,542 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 01:21:26,542 - app - INFO - Applied NumPy fix
2025-06-20 01:21:26,543 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 01:21:26,544 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 01:21:26,544 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 01:21:26,544 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 01:21:26,544 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 01:21:26,544 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 01:21:26,545 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 01:21:26,545 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 01:21:30,442 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 01:21:30,443 - app - INFO - Applied TensorFlow fix
2025-06-20 01:21:30,446 - app.config - INFO - Configuration initialized
2025-06-20 01:21:30,451 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 01:21:30,463 - models.train - INFO - TensorFlow test successful
2025-06-20 01:21:30,937 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 01:21:30,937 - models.train - INFO - Transformer model is available
2025-06-20 01:21:30,938 - models.train - INFO - Using TensorFlow-based models
2025-06-20 01:21:30,939 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 01:21:30,940 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 01:21:30,943 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 01:21:31,262 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 01:21:31,263 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 01:21:31,263 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 01:21:31,263 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 01:21:31,263 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 01:21:31,265 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 01:21:31,265 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 01:21:31,265 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 01:21:31,265 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 01:21:31,265 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 01:21:31,352 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 01:21:31,354 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:21:31,672 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 01:21:32,290 - app.utils.session_state - INFO - Initializing session state
2025-06-20 01:21:32,292 - app.utils.session_state - INFO - Session state initialized
2025-06-20 01:21:33,723 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 01:21:33,741 - app.utils.memory_management - INFO - Memory before cleanup: 430.00 MB
2025-06-20 01:21:33,938 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 01:21:33,939 - app.utils.memory_management - INFO - Memory after cleanup: 430.00 MB (freed -0.00 MB)
2025-06-20 01:21:36,572 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:21:36,612 - app.utils.memory_management - INFO - Memory before cleanup: 432.60 MB
2025-06-20 01:21:36,818 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 01:21:36,818 - app.utils.memory_management - INFO - Memory after cleanup: 432.60 MB (freed 0.00 MB)
2025-06-20 01:21:40,965 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:21:41,041 - app.utils.memory_management - INFO - Memory before cleanup: 432.60 MB
2025-06-20 01:21:41,342 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 01:21:41,346 - app.utils.memory_management - INFO - Memory after cleanup: 432.64 MB (freed -0.04 MB)
2025-06-20 01:21:43,364 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:21:43,405 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 01:21:43,420 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 01:21:43,421 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 01:21:52,833 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 01:21:53,136 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 01:21:53,136 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 01:21:53,136 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 01:21:53,136 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 01:21:53,136 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 01:21:53,136 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 01:21:53,136 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 01:21:53,184 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 01:21:53,245 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 01:21:53,245 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 01:21:53,255 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 01:21:53,583 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:21:53,646 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:21:53,646 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:21:53,660 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:21:54,034 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:21:54,097 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:21:54,097 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:21:54,110 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:21:54,417 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:21:54,471 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:21:54,471 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:21:54,479 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:21:54,507 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 01:21:54,535 - app.utils.session_state - ERROR - Error tracked: app_crash - name 'display_ai_pattern_recognition' is not defined
2025-06-20 01:21:54,539 - app - ERROR - Application crashed: name 'display_ai_pattern_recognition' is not defined
2025-06-20 01:21:54,540 - app.utils.memory_management - INFO - Memory before cleanup: 437.24 MB
2025-06-20 01:21:54,738 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 01:21:54,742 - app.utils.memory_management - INFO - Memory after cleanup: 437.24 MB (freed 0.00 MB)
2025-06-20 01:21:54,744 - app.utils.memory_management - INFO - Memory before cleanup: 437.24 MB
2025-06-20 01:21:54,934 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-06-20 01:21:54,936 - app.utils.memory_management - INFO - Memory after cleanup: 437.24 MB (freed 0.00 MB)
2025-06-20 01:30:11,901 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 01:30:11,906 - app - INFO - Memory management utilities loaded
2025-06-20 01:30:11,910 - app - INFO - Error handling utilities loaded
2025-06-20 01:30:11,914 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 01:30:11,916 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 01:30:11,919 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 01:30:11,921 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 01:30:28,186 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 01:30:30,229 - app - INFO - Memory management utilities loaded
2025-06-20 01:30:30,235 - app - INFO - Error handling utilities loaded
2025-06-20 01:30:30,237 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 01:30:30,247 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 01:30:30,252 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 01:30:30,256 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 01:30:30,260 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 01:30:30,260 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 01:30:30,262 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 01:30:30,267 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 01:30:30,269 - app - INFO - Applied NumPy fix
2025-06-20 01:30:30,273 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 01:30:30,277 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 01:30:30,279 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 01:30:30,287 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 01:30:30,289 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 01:30:30,292 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 01:30:30,293 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 01:30:30,295 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 01:30:34,295 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 01:30:34,295 - app - INFO - Applied TensorFlow fix
2025-06-20 01:30:34,297 - app.config - INFO - Configuration initialized
2025-06-20 01:30:34,301 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 01:30:34,310 - models.train - INFO - TensorFlow test successful
2025-06-20 01:30:34,770 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 01:30:34,771 - models.train - INFO - Transformer model is available
2025-06-20 01:30:34,771 - models.train - INFO - Using TensorFlow-based models
2025-06-20 01:30:34,773 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 01:30:34,775 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 01:30:34,778 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 01:30:35,144 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 01:30:35,144 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 01:30:35,145 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 01:30:35,145 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 01:30:35,145 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 01:30:35,145 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 01:30:35,145 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 01:30:35,146 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 01:30:35,146 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 01:30:35,146 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 01:30:35,241 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 01:30:35,243 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:30:35,243 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:30:35,585 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 01:30:36,235 - app.utils.memory_management - INFO - Memory before cleanup: 428.11 MB
2025-06-20 01:30:36,236 - app.utils.session_state - INFO - Initializing session state
2025-06-20 01:30:36,404 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 01:30:36,405 - app.utils.session_state - INFO - Session state initialized
2025-06-20 01:30:36,406 - app.utils.memory_management - INFO - Memory after cleanup: 428.13 MB (freed -0.02 MB)
2025-06-20 01:30:37,898 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 01:30:37,918 - app.utils.memory_management - INFO - Memory before cleanup: 431.29 MB
2025-06-20 01:30:38,135 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 01:30:38,139 - app.utils.memory_management - INFO - Memory after cleanup: 431.29 MB (freed 0.00 MB)
2025-06-20 01:30:40,842 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:30:40,878 - app.utils.memory_management - INFO - Memory before cleanup: 433.48 MB
2025-06-20 01:30:41,081 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-20 01:30:41,082 - app.utils.memory_management - INFO - Memory after cleanup: 433.52 MB (freed -0.04 MB)
2025-06-20 01:30:44,765 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:30:44,792 - app.utils.memory_management - INFO - Memory before cleanup: 433.53 MB
2025-06-20 01:30:44,987 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 01:30:44,987 - app.utils.memory_management - INFO - Memory after cleanup: 433.53 MB (freed 0.00 MB)
2025-06-20 01:30:46,506 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:30:46,568 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 01:30:46,651 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 01:30:46,662 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 01:30:53,656 - app.pages.smc_analysis - INFO - Using live price 78.62 EGP from API for SMC analysis of COMI
2025-06-20 01:30:53,656 - app.pages.smc_analysis - INFO - Market Status: WEEKEND
2025-06-20 01:30:53,662 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 01:30:53,933 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 01:30:53,933 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 01:30:53,933 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 01:30:53,933 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 01:30:53,933 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 01:30:53,933 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 01:30:53,933 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 01:30:53,995 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 01:30:54,107 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 01:30:54,109 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 01:30:54,126 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 01:30:54,438 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:30:54,491 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:30:54,491 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:30:54,500 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:30:54,800 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:30:54,839 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:30:54,839 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:30:54,860 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:30:55,164 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:30:55,224 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:30:55,224 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:30:55,245 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:30:55,259 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 01:30:55,306 - app.utils.session_state - ERROR - Error tracked: app_crash - name 'display_ai_pattern_recognition' is not defined
2025-06-20 01:30:55,306 - app - ERROR - Application crashed: name 'display_ai_pattern_recognition' is not defined
2025-06-20 01:30:55,309 - app.utils.memory_management - INFO - Memory before cleanup: 438.31 MB
2025-06-20 01:30:55,520 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 01:30:55,521 - app.utils.memory_management - INFO - Memory after cleanup: 438.31 MB (freed 0.00 MB)
2025-06-20 01:30:55,521 - app.utils.memory_management - INFO - Memory before cleanup: 438.31 MB
2025-06-20 01:30:55,678 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-06-20 01:30:55,679 - app.utils.memory_management - INFO - Memory after cleanup: 438.31 MB (freed 0.00 MB)
2025-06-20 01:35:17,764 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:35:17,769 - app.utils.session_state - INFO - Initializing session state
2025-06-20 01:35:17,770 - app.utils.session_state - INFO - Session state initialized
2025-06-20 01:35:17,787 - app.utils.memory_management - INFO - Memory before cleanup: 438.98 MB
2025-06-20 01:35:18,035 - app.utils.memory_management - INFO - Garbage collection: collected 217 objects
2025-06-20 01:35:18,035 - app.utils.memory_management - INFO - Memory after cleanup: 438.98 MB (freed 0.00 MB)
2025-06-20 01:35:22,239 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:35:22,375 - app.utils.memory_management - INFO - Memory before cleanup: 439.20 MB
2025-06-20 01:35:22,812 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-20 01:35:22,813 - app.utils.memory_management - INFO - Memory after cleanup: 439.20 MB (freed 0.00 MB)
2025-06-20 01:35:25,749 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:35:25,792 - app.utils.memory_management - INFO - Memory before cleanup: 439.20 MB
2025-06-20 01:35:26,074 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 01:35:26,076 - app.utils.memory_management - INFO - Memory after cleanup: 439.20 MB (freed 0.00 MB)
2025-06-20 01:35:27,099 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:35:27,278 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 01:35:27,339 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 01:35:27,354 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 01:35:38,598 - app.pages.smc_analysis - INFO - Using live price 78.62 EGP from API for SMC analysis of COMI
2025-06-20 01:35:38,600 - app.pages.smc_analysis - INFO - Market Status: WEEKEND
2025-06-20 01:35:38,604 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 01:35:39,018 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 01:35:39,018 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 01:35:39,020 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 01:35:39,020 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 01:35:39,022 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 01:35:39,024 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 01:35:39,024 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 01:35:39,171 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 01:35:39,293 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 01:35:39,295 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 01:35:39,321 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 01:35:39,929 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:35:40,010 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:35:40,010 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:35:40,036 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:35:40,670 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:35:40,770 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:35:40,770 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:35:40,786 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:35:41,491 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:35:41,585 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:35:41,589 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:35:41,623 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:35:41,679 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 01:35:41,922 - app.utils.session_state - ERROR - Error tracked: app_crash - name 'display_ai_pattern_recognition' is not defined
2025-06-20 01:35:41,926 - app - ERROR - Application crashed: name 'display_ai_pattern_recognition' is not defined
2025-06-20 01:35:41,936 - app.utils.memory_management - INFO - Memory before cleanup: 439.67 MB
2025-06-20 01:35:42,372 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 01:35:42,374 - app.utils.memory_management - INFO - Memory after cleanup: 439.67 MB (freed 0.00 MB)
2025-06-20 01:35:42,378 - app.utils.memory_management - INFO - Memory before cleanup: 439.67 MB
2025-06-20 01:35:42,605 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-06-20 01:35:42,607 - app.utils.memory_management - INFO - Memory after cleanup: 439.67 MB (freed 0.00 MB)
2025-06-20 01:40:49,492 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 01:40:49,496 - app - INFO - Memory management utilities loaded
2025-06-20 01:40:49,497 - app - INFO - Error handling utilities loaded
2025-06-20 01:40:49,498 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 01:40:49,498 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 01:40:49,498 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 01:40:49,499 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 01:41:19,051 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 01:41:20,398 - app - INFO - Memory management utilities loaded
2025-06-20 01:41:20,400 - app - INFO - Error handling utilities loaded
2025-06-20 01:41:20,402 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 01:41:20,402 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 01:41:20,405 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 01:41:20,405 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 01:41:20,407 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 01:41:20,407 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 01:41:20,407 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 01:41:20,412 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 01:41:20,414 - app - INFO - Applied NumPy fix
2025-06-20 01:41:20,416 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 01:41:20,416 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 01:41:20,418 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 01:41:20,422 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 01:41:20,422 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 01:41:20,424 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 01:41:20,428 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 01:41:20,430 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 01:41:23,974 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 01:41:23,975 - app - INFO - Applied TensorFlow fix
2025-06-20 01:41:23,977 - app.config - INFO - Configuration initialized
2025-06-20 01:41:23,981 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 01:41:23,992 - models.train - INFO - TensorFlow test successful
2025-06-20 01:41:24,442 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 01:41:24,443 - models.train - INFO - Transformer model is available
2025-06-20 01:41:24,443 - models.train - INFO - Using TensorFlow-based models
2025-06-20 01:41:24,444 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 01:41:24,445 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 01:41:24,447 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 01:41:24,763 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 01:41:24,765 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 01:41:24,765 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 01:41:24,766 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 01:41:24,768 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 01:41:24,769 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 01:41:24,770 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 01:41:24,771 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 01:41:24,774 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 01:41:24,776 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 01:41:24,858 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 01:41:24,861 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:41:25,174 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 01:41:25,742 - app.utils.session_state - INFO - Initializing session state
2025-06-20 01:41:25,743 - app.utils.session_state - INFO - Session state initialized
2025-06-20 01:41:27,061 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 01:41:27,079 - app.utils.memory_management - INFO - Memory before cleanup: 430.45 MB
2025-06-20 01:41:27,278 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 01:41:27,279 - app.utils.memory_management - INFO - Memory after cleanup: 430.45 MB (freed -0.00 MB)
2025-06-20 01:41:30,748 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:41:30,775 - app.utils.memory_management - INFO - Memory before cleanup: 432.81 MB
2025-06-20 01:41:30,974 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 01:41:30,974 - app.utils.memory_management - INFO - Memory after cleanup: 432.81 MB (freed 0.00 MB)
2025-06-20 01:41:34,442 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:41:34,469 - app.utils.memory_management - INFO - Memory before cleanup: 432.83 MB
2025-06-20 01:41:34,672 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 01:41:34,672 - app.utils.memory_management - INFO - Memory after cleanup: 432.87 MB (freed -0.04 MB)
2025-06-20 01:41:35,900 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 01:41:35,930 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 01:41:35,949 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 01:41:35,950 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 01:41:42,531 - app.pages.smc_analysis - INFO - Using live price 78.62 EGP from API for SMC analysis of COMI
2025-06-20 01:41:42,531 - app.pages.smc_analysis - INFO - Market Status: WEEKEND
2025-06-20 01:41:42,538 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 01:41:42,815 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 01:41:42,815 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 01:41:42,815 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 01:41:42,815 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 01:41:42,817 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 01:41:42,817 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 01:41:42,817 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 01:41:42,874 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 01:41:42,934 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 01:41:42,936 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 01:41:42,946 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 01:41:43,274 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:41:43,333 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:41:43,333 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:41:43,333 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:41:43,649 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:41:43,720 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:41:43,722 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:41:43,733 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:41:44,063 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 01:41:44,116 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 01:41:44,116 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 01:41:44,133 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 01:41:44,159 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 01:41:44,193 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 01:41:44,194 - app.pages.smc_analysis - ERROR - Error in risk management display: 'list' object has no attribute 'get'
2025-06-20 01:41:44,197 - app.utils.memory_management - INFO - Memory before cleanup: 437.09 MB
2025-06-20 01:41:44,404 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 01:41:44,404 - app.utils.memory_management - INFO - Memory after cleanup: 437.09 MB (freed 0.00 MB)
2025-06-20 01:42:20,502 - app - INFO - Cleaning up resources...
2025-06-20 01:42:20,502 - app.utils.memory_management - INFO - Memory before cleanup: 437.86 MB
2025-06-20 01:42:20,649 - app.utils.memory_management - INFO - Garbage collection: collected 291 objects
2025-06-20 01:42:20,649 - app.utils.memory_management - INFO - Memory after cleanup: 437.86 MB (freed 0.00 MB)
2025-06-20 01:42:20,649 - app - INFO - Application shutdown complete
2025-06-20 12:48:46,283 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 12:48:48,206 - app - INFO - Memory management utilities loaded
2025-06-20 12:48:48,214 - app - INFO - Error handling utilities loaded
2025-06-20 12:48:48,221 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 12:48:48,223 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 12:48:48,226 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 12:48:48,227 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 12:48:48,242 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 12:48:48,244 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 12:48:48,244 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 12:48:48,246 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 12:48:48,246 - app - INFO - Applied NumPy fix
2025-06-20 12:48:48,260 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 12:48:48,260 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 12:48:48,264 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 12:48:48,264 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 12:48:48,266 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 12:48:48,266 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 12:48:48,266 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 12:48:48,268 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 12:49:05,398 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 12:49:05,398 - app - INFO - Applied TensorFlow fix
2025-06-20 12:49:05,421 - app.config - INFO - Configuration initialized
2025-06-20 12:49:05,446 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 12:49:05,463 - models.train - INFO - TensorFlow test successful
2025-06-20 12:49:07,240 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 12:49:07,241 - models.train - INFO - Transformer model is available
2025-06-20 12:49:07,241 - models.train - INFO - Using TensorFlow-based models
2025-06-20 12:49:07,254 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 12:49:07,256 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 12:49:07,276 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 12:49:08,028 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 12:49:08,028 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 12:49:08,028 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 12:49:08,028 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 12:49:08,028 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 12:49:08,028 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 12:49:08,028 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 12:49:08,028 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 12:49:08,028 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 12:49:08,028 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 12:49:08,282 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 12:49:08,298 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 12:49:09,076 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 12:49:11,869 - app.utils.session_state - INFO - Initializing session state
2025-06-20 12:49:11,873 - app.utils.session_state - INFO - Session state initialized
2025-06-20 12:49:13,123 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 12:49:13,138 - app.utils.memory_management - INFO - Memory before cleanup: 431.83 MB
2025-06-20 12:49:13,347 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 12:49:13,347 - app.utils.memory_management - INFO - Memory after cleanup: 431.84 MB (freed -0.00 MB)
2025-06-20 12:49:19,581 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 12:49:19,620 - app.utils.memory_management - INFO - Memory before cleanup: 434.28 MB
2025-06-20 12:49:19,928 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 12:49:19,931 - app.utils.memory_management - INFO - Memory after cleanup: 434.28 MB (freed -0.00 MB)
2025-06-20 12:49:23,075 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 12:49:23,111 - app.utils.memory_management - INFO - Memory before cleanup: 434.29 MB
2025-06-20 12:49:23,320 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 12:49:23,320 - app.utils.memory_management - INFO - Memory after cleanup: 434.32 MB (freed -0.04 MB)
2025-06-20 12:49:25,085 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 12:49:25,133 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 12:49:25,169 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 12:49:25,171 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 12:49:38,484 - app.pages.smc_analysis - INFO - Using live price 78.62 EGP from API for SMC analysis of COMI
2025-06-20 12:49:38,484 - app.pages.smc_analysis - INFO - Market Status: WEEKEND
2025-06-20 12:49:38,484 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 12:49:38,754 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 12:49:38,754 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 12:49:38,754 - app.pages.smc_analysis - INFO - Enhanced 1 liquidity_zones with live data context
2025-06-20 12:49:38,754 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 12:49:38,754 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 12:49:38,754 - app.pages.smc_analysis - INFO - Enhanced filtering: 1/1 structures selected for market status: WEEKEND
2025-06-20 12:49:38,754 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 12:49:38,822 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 12:49:38,869 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 12:49:38,869 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 12:49:38,889 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 12:49:39,202 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 12:49:39,254 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 12:49:39,254 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 12:49:39,269 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 12:49:39,577 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 12:49:39,634 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 12:49:39,634 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 12:49:39,637 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 12:49:39,956 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 12:49:40,013 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 12:49:40,015 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 12:49:40,025 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 12:49:40,072 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 12:49:40,110 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 12:49:40,111 - app.pages.smc_analysis - ERROR - Error in risk management display: 'list' object has no attribute 'get'
2025-06-20 12:49:40,116 - app.utils.memory_management - INFO - Memory before cleanup: 438.35 MB
2025-06-20 12:49:40,316 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 12:49:40,318 - app.utils.memory_management - INFO - Memory after cleanup: 438.35 MB (freed 0.00 MB)
2025-06-20 13:24:49,569 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 13:24:50,745 - app - INFO - Memory management utilities loaded
2025-06-20 13:24:50,745 - app - INFO - Error handling utilities loaded
2025-06-20 13:24:50,749 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 13:24:50,749 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 13:24:50,749 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 13:24:50,749 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 13:24:50,749 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 13:24:50,749 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 13:24:50,749 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 13:24:50,749 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 13:24:50,749 - app - INFO - Applied NumPy fix
2025-06-20 13:24:50,753 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 13:24:50,754 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 13:24:50,754 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 13:24:50,754 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 13:24:50,754 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 13:24:50,755 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 13:24:50,755 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 13:24:50,755 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 13:25:06,930 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 13:25:06,930 - app - INFO - Applied TensorFlow fix
2025-06-20 13:25:06,990 - app - INFO - Cleaning up resources...
2025-06-20 13:25:06,993 - app.utils.memory_management - INFO - Memory before cleanup: 324.75 MB
2025-06-20 13:25:07,115 - app.utils.memory_management - INFO - Garbage collection: collected 33 objects
2025-06-20 13:25:07,115 - app.utils.memory_management - INFO - Memory after cleanup: 324.76 MB (freed -0.01 MB)
2025-06-20 13:25:07,115 - app - INFO - Application shutdown complete
2025-06-20 13:27:53,549 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 13:27:55,425 - app - INFO - Memory management utilities loaded
2025-06-20 13:27:55,428 - app - INFO - Error handling utilities loaded
2025-06-20 13:27:55,430 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 13:27:55,432 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 13:27:55,433 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 13:27:55,435 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 13:27:55,437 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 13:27:55,443 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 13:27:55,445 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 13:27:55,445 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 13:27:55,445 - app - INFO - Applied NumPy fix
2025-06-20 13:27:55,450 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 13:27:55,451 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 13:27:55,458 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 13:27:55,462 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 13:27:55,467 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 13:27:55,480 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 13:27:55,485 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 13:27:55,495 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 13:28:00,227 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 13:28:00,227 - app - INFO - Applied TensorFlow fix
2025-06-20 13:28:00,230 - app.config - INFO - Configuration initialized
2025-06-20 13:28:00,235 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 13:28:00,246 - models.train - INFO - TensorFlow test successful
2025-06-20 13:28:05,532 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 13:28:05,533 - models.train - INFO - Transformer model is available
2025-06-20 13:28:05,533 - models.train - INFO - Using TensorFlow-based models
2025-06-20 13:28:05,535 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 13:28:05,535 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 13:28:05,538 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 13:28:07,305 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 13:28:07,305 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 13:28:07,305 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 13:28:07,305 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 13:28:07,306 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 13:28:07,306 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 13:28:07,306 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 13:28:07,306 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 13:28:07,306 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 13:28:07,306 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 13:28:07,547 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 13:28:07,550 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:28:07,915 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 13:28:09,976 - app.utils.session_state - INFO - Initializing session state
2025-06-20 13:28:09,977 - app.utils.session_state - INFO - Session state initialized
2025-06-20 13:28:11,426 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 13:28:11,603 - app.utils.memory_management - INFO - Memory before cleanup: 430.62 MB
2025-06-20 13:28:11,885 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 13:28:11,888 - app.utils.memory_management - INFO - Memory after cleanup: 430.62 MB (freed -0.00 MB)
2025-06-20 13:28:13,818 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:28:13,845 - app.utils.memory_management - INFO - Memory before cleanup: 433.21 MB
2025-06-20 13:28:14,043 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 13:28:14,043 - app.utils.memory_management - INFO - Memory after cleanup: 433.21 MB (freed 0.00 MB)
2025-06-20 13:28:19,155 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:28:19,198 - app.utils.memory_management - INFO - Memory before cleanup: 433.22 MB
2025-06-20 13:28:19,401 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 13:28:19,401 - app.utils.memory_management - INFO - Memory after cleanup: 433.26 MB (freed -0.04 MB)
2025-06-20 13:28:21,053 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:28:21,090 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 13:28:21,125 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 13:28:21,126 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 13:28:38,614 - app.pages.smc_analysis - INFO - Using live price 78.62 EGP from API for SMC analysis of COMI
2025-06-20 13:28:38,614 - app.pages.smc_analysis - INFO - Market Status: WEEKEND
2025-06-20 13:28:38,614 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 13:28:38,936 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 13:28:38,936 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 13:28:38,936 - app.pages.smc_analysis - INFO - Enhanced 2 liquidity_zones with live data context
2025-06-20 13:28:38,936 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 13:28:38,936 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 13:28:38,936 - app.pages.smc_analysis - INFO - Enhanced filtering: 2/2 structures selected for market status: WEEKEND
2025-06-20 13:28:38,936 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 13:28:38,994 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 13:28:39,047 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 13:28:39,047 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 13:28:39,055 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 13:28:39,370 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:28:39,424 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:28:39,426 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:28:39,435 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:28:39,822 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:28:39,883 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:28:39,883 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:28:39,893 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:28:40,198 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:28:40,256 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:28:40,256 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:28:40,267 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:28:40,292 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 13:28:40,310 - app.utils.session_state - ERROR - Error tracked: app_crash - 'LiquidityType' object has no attribute 'upper'
2025-06-20 13:28:40,311 - app - ERROR - Application crashed: 'LiquidityType' object has no attribute 'upper'
2025-06-20 13:28:40,318 - app.utils.memory_management - INFO - Memory before cleanup: 436.70 MB
2025-06-20 13:28:40,515 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 13:28:40,515 - app.utils.memory_management - INFO - Memory after cleanup: 436.71 MB (freed -0.00 MB)
2025-06-20 13:28:40,516 - app.utils.memory_management - INFO - Memory before cleanup: 436.71 MB
2025-06-20 13:28:40,665 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-06-20 13:28:40,665 - app.utils.memory_management - INFO - Memory after cleanup: 436.71 MB (freed 0.00 MB)
2025-06-20 13:33:27,860 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 13:33:27,886 - app - INFO - Memory management utilities loaded
2025-06-20 13:33:27,898 - app - INFO - Error handling utilities loaded
2025-06-20 13:33:27,905 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 13:33:27,910 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 13:33:27,912 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 13:33:27,912 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 13:33:50,128 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 13:33:51,722 - app - INFO - Memory management utilities loaded
2025-06-20 13:33:51,724 - app - INFO - Error handling utilities loaded
2025-06-20 13:33:51,729 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 13:33:51,735 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 13:33:51,737 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 13:33:51,739 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 13:33:51,741 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 13:33:51,741 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 13:33:51,743 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 13:33:51,743 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 13:33:51,743 - app - INFO - Applied NumPy fix
2025-06-20 13:33:51,751 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 13:33:51,755 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 13:33:51,759 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 13:33:51,761 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 13:33:51,761 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 13:33:51,763 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 13:33:51,766 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 13:33:51,768 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 13:34:02,574 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 13:34:02,575 - app - INFO - Applied TensorFlow fix
2025-06-20 13:34:02,596 - app.config - INFO - Configuration initialized
2025-06-20 13:34:02,618 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 13:34:02,628 - models.train - INFO - TensorFlow test successful
2025-06-20 13:34:03,144 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 13:34:03,145 - models.train - INFO - Transformer model is available
2025-06-20 13:34:03,145 - models.train - INFO - Using TensorFlow-based models
2025-06-20 13:34:03,155 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 13:34:03,155 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 13:34:03,160 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 13:34:03,501 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 13:34:03,501 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 13:34:03,502 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 13:34:03,502 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 13:34:03,502 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 13:34:03,502 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 13:34:03,502 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 13:34:03,503 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 13:34:03,503 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 13:34:03,503 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 13:34:03,591 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 13:34:03,607 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:34:04,102 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 13:34:04,918 - app.utils.session_state - INFO - Initializing session state
2025-06-20 13:34:04,920 - app.utils.session_state - INFO - Session state initialized
2025-06-20 13:34:06,088 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 13:34:06,115 - app.utils.memory_management - INFO - Memory before cleanup: 429.89 MB
2025-06-20 13:34:06,310 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 13:34:06,310 - app.utils.memory_management - INFO - Memory after cleanup: 429.89 MB (freed -0.00 MB)
2025-06-20 13:34:10,419 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:34:10,450 - app.utils.memory_management - INFO - Memory before cleanup: 432.61 MB
2025-06-20 13:34:10,644 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 13:34:10,645 - app.utils.memory_management - INFO - Memory after cleanup: 432.61 MB (freed 0.00 MB)
2025-06-20 13:34:13,535 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:34:13,570 - app.utils.memory_management - INFO - Memory before cleanup: 432.62 MB
2025-06-20 13:34:13,753 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 13:34:13,755 - app.utils.memory_management - INFO - Memory after cleanup: 432.66 MB (freed -0.04 MB)
2025-06-20 13:34:15,063 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:34:15,092 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 13:34:15,127 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 13:34:15,128 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 13:34:21,758 - app.pages.smc_analysis - INFO - Using live price 78.62 EGP from API for SMC analysis of COMI
2025-06-20 13:34:21,758 - app.pages.smc_analysis - INFO - Market Status: WEEKEND
2025-06-20 13:34:21,761 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 13:34:22,058 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 13:34:22,063 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 13:34:22,065 - app.pages.smc_analysis - INFO - Enhanced 2 liquidity_zones with live data context
2025-06-20 13:34:22,067 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 13:34:22,067 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 13:34:22,067 - app.pages.smc_analysis - INFO - Enhanced filtering: 2/2 structures selected for market status: WEEKEND
2025-06-20 13:34:22,067 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 13:34:22,145 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 13:34:22,195 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 13:34:22,195 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 13:34:22,207 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 13:34:22,534 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:34:22,591 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:34:22,591 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:34:22,595 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:34:22,928 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:34:22,985 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:34:22,985 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:34:22,995 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:34:23,314 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:34:23,362 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:34:23,362 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:34:23,379 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:34:23,403 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 13:34:23,488 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 13:34:23,489 - app.pages.smc_analysis - ERROR - Error in risk management display: 'list' object has no attribute 'get'
2025-06-20 13:34:23,492 - app.utils.memory_management - INFO - Memory before cleanup: 437.04 MB
2025-06-20 13:34:23,690 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 13:34:23,690 - app.utils.memory_management - INFO - Memory after cleanup: 437.04 MB (freed 0.00 MB)
2025-06-20 13:43:51,853 - app - INFO - Cleaning up resources...
2025-06-20 13:43:51,853 - app.utils.memory_management - INFO - Memory before cleanup: 437.73 MB
2025-06-20 13:43:52,031 - app.utils.memory_management - INFO - Garbage collection: collected 291 objects
2025-06-20 13:43:52,031 - app.utils.memory_management - INFO - Memory after cleanup: 437.73 MB (freed 0.00 MB)
2025-06-20 13:43:52,032 - app - INFO - Application shutdown complete
2025-06-20 13:53:10,181 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 13:53:12,970 - app - INFO - Memory management utilities loaded
2025-06-20 13:53:12,970 - app - INFO - Error handling utilities loaded
2025-06-20 13:53:12,985 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 13:53:12,987 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 13:53:12,989 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 13:53:12,989 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 13:53:12,989 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 13:53:12,991 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 13:53:12,991 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 13:53:12,992 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 13:53:12,992 - app - INFO - Applied NumPy fix
2025-06-20 13:53:12,997 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 13:53:12,997 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 13:53:12,997 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 13:53:12,997 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 13:53:12,997 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 13:53:12,997 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 13:53:13,002 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 13:53:13,003 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 13:53:27,725 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 13:53:27,725 - app - INFO - Applied TensorFlow fix
2025-06-20 13:53:27,760 - app.config - INFO - Configuration initialized
2025-06-20 13:53:27,827 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 13:53:27,836 - models.train - INFO - TensorFlow test successful
2025-06-20 13:53:29,318 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 13:53:29,318 - models.train - INFO - Transformer model is available
2025-06-20 13:53:29,318 - models.train - INFO - Using TensorFlow-based models
2025-06-20 13:53:29,327 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 13:53:29,328 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 13:53:29,349 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 13:53:29,932 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 13:53:29,932 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 13:53:29,932 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 13:53:29,933 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 13:53:29,933 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 13:53:29,933 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 13:53:29,933 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 13:53:29,934 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 13:53:29,934 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 13:53:29,934 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 13:53:30,180 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 13:53:30,196 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:53:31,075 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 13:55:53,080 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:59:29,915 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:59:30,515 - app.utils.session_state - INFO - Initializing session state
2025-06-20 13:59:30,518 - app.utils.session_state - INFO - Session state initialized
2025-06-20 13:59:30,531 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 13:59:30,546 - app.utils.memory_management - INFO - Memory before cleanup: 438.30 MB
2025-06-20 13:59:30,823 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-20 13:59:30,824 - app.utils.memory_management - INFO - Memory after cleanup: 438.34 MB (freed -0.05 MB)
2025-06-20 13:59:33,643 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:59:33,684 - app.utils.memory_management - INFO - Memory before cleanup: 440.01 MB
2025-06-20 13:59:33,900 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-06-20 13:59:33,900 - app.utils.memory_management - INFO - Memory after cleanup: 440.01 MB (freed 0.00 MB)
2025-06-20 13:59:37,351 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:59:37,378 - app.utils.memory_management - INFO - Memory before cleanup: 440.01 MB
2025-06-20 13:59:37,579 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 13:59:37,580 - app.utils.memory_management - INFO - Memory after cleanup: 440.01 MB (freed 0.00 MB)
2025-06-20 13:59:38,841 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 13:59:38,911 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 13:59:39,094 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 13:59:39,098 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 13:59:51,788 - app.pages.smc_analysis - INFO - Using live price 78.62 EGP from API for SMC analysis of COMI
2025-06-20 13:59:51,788 - app.pages.smc_analysis - INFO - Market Status: WEEKEND
2025-06-20 13:59:51,832 - app.pages.smc_analysis - INFO - SMC Data Quality: 80.0% (180 bars, 251 days)
2025-06-20 13:59:52,149 - app.pages.smc_analysis - INFO - Enhanced 3 order_blocks with live data context
2025-06-20 13:59:52,149 - app.pages.smc_analysis - INFO - Enhanced 15 fvgs with live data context
2025-06-20 13:59:52,151 - app.pages.smc_analysis - INFO - Enhanced 2 liquidity_zones with live data context
2025-06-20 13:59:52,151 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/1 structures selected for market status: WEEKEND
2025-06-20 13:59:52,151 - app.pages.smc_analysis - INFO - Enhanced filtering: 0/3 structures selected for market status: WEEKEND
2025-06-20 13:59:52,151 - app.pages.smc_analysis - INFO - Enhanced filtering: 2/2 structures selected for market status: WEEKEND
2025-06-20 13:59:52,151 - app.pages.smc_analysis - INFO - Using live price 78.62 for market structure analysis
2025-06-20 13:59:52,209 - app.components.advanced_smc_features - INFO - Found 9 high swing points with lookback 10
2025-06-20 13:59:52,265 - app.components.advanced_smc_features - INFO - Found 11 low swing points with lookback 10
2025-06-20 13:59:52,265 - app.components.advanced_smc_features - INFO - Found 9 swing highs and 11 swing lows
2025-06-20 13:59:52,274 - app.components.advanced_smc_features - INFO - Detected 12 BOS events
2025-06-20 13:59:52,708 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:59:52,770 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:59:52,773 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:59:52,786 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:59:53,183 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:59:53,285 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:59:53,285 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:59:53,298 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:59:53,679 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:59:53,736 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:59:53,736 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:59:53,748 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:59:53,802 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 13:59:54,107 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:59:54,157 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:59:54,159 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:59:54,167 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:59:54,478 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:59:54,532 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:59:54,534 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:59:54,545 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:59:54,860 - app.components.advanced_smc_features - INFO - Found 13 high swing points with lookback 7
2025-06-20 13:59:54,914 - app.components.advanced_smc_features - INFO - Found 14 low swing points with lookback 7
2025-06-20 13:59:54,916 - app.components.advanced_smc_features - INFO - Found 13 swing highs and 14 swing lows
2025-06-20 13:59:54,927 - app.components.advanced_smc_features - INFO - Detected 16 BOS events
2025-06-20 13:59:55,152 - app.pages.smc_analysis - INFO - Generated 4 SMC-based predictions
2025-06-20 13:59:55,166 - app.utils.memory_management - INFO - Memory before cleanup: 443.64 MB
2025-06-20 13:59:55,370 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-06-20 13:59:55,371 - app.utils.memory_management - INFO - Memory after cleanup: 443.64 MB (freed 0.00 MB)
2025-06-20 14:14:44,056 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 14:14:44,071 - app - INFO - Memory management utilities loaded
2025-06-20 14:14:44,074 - app - INFO - Error handling utilities loaded
2025-06-20 14:14:44,077 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 14:14:44,078 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 14:14:44,079 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 14:14:44,080 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 14:15:02,704 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 14:15:04,339 - app - INFO - Memory management utilities loaded
2025-06-20 14:15:04,341 - app - INFO - Error handling utilities loaded
2025-06-20 14:15:04,343 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 14:15:04,347 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 14:15:04,348 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 14:15:04,350 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 14:15:04,353 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 14:15:04,354 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 14:15:04,356 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 14:15:04,358 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 14:15:04,363 - app - INFO - Applied NumPy fix
2025-06-20 14:15:04,365 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 14:15:04,366 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 14:15:04,367 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 14:15:04,367 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 14:15:04,369 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 14:15:04,371 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 14:15:04,372 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 14:15:04,372 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 14:15:08,751 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 14:15:08,751 - app - INFO - Applied TensorFlow fix
2025-06-20 14:15:08,758 - app.config - INFO - Configuration initialized
2025-06-20 14:15:08,763 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 14:15:08,774 - models.train - INFO - TensorFlow test successful
2025-06-20 14:15:09,271 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 14:15:09,272 - models.train - INFO - Transformer model is available
2025-06-20 14:15:09,272 - models.train - INFO - Using TensorFlow-based models
2025-06-20 14:15:09,274 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 14:15:09,274 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 14:15:09,277 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 14:15:09,607 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 14:15:09,608 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 14:15:09,608 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 14:15:09,608 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 14:15:09,608 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 14:15:09,608 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 14:15:09,608 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 14:15:09,609 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 14:15:09,609 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 14:15:09,609 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 14:15:09,702 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 14:15:09,705 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 14:15:09,705 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 14:15:10,041 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 14:21:01,231 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 14:21:01,265 - app - INFO - Memory management utilities loaded
2025-06-20 14:21:01,278 - app - INFO - Error handling utilities loaded
2025-06-20 14:21:01,293 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 14:21:01,294 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 14:21:01,296 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 14:21:01,298 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 14:21:22,356 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-20 14:21:24,977 - app - INFO - Memory management utilities loaded
2025-06-20 14:21:24,980 - app - INFO - Error handling utilities loaded
2025-06-20 14:21:24,981 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-20 14:21:24,982 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-20 14:21:24,983 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-20 14:21:24,983 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-20 14:21:24,984 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-20 14:21:24,985 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-20 14:21:24,985 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-20 14:21:24,986 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-20 14:21:24,987 - app - INFO - Applied NumPy fix
2025-06-20 14:21:24,998 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 14:21:24,999 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 14:21:24,999 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 14:21:24,999 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-20 14:21:24,999 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 14:21:25,000 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 14:21:25,000 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 14:21:25,000 - app - INFO - Applied NumPy BitGenerator fix
2025-06-20 14:21:39,128 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-20 14:21:39,130 - app - INFO - Applied TensorFlow fix
2025-06-20 14:21:39,157 - app.config - INFO - Configuration initialized
2025-06-20 14:21:39,193 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-20 14:21:39,202 - models.train - INFO - TensorFlow test successful
2025-06-20 14:21:40,572 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-20 14:21:40,573 - models.train - INFO - Transformer model is available
2025-06-20 14:21:40,573 - models.train - INFO - Using TensorFlow-based models
2025-06-20 14:21:40,585 - models.predict - INFO - Transformer model is available for predictions
2025-06-20 14:21:40,585 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-20 14:21:40,605 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-20 14:21:41,096 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 14:21:41,100 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-20 14:21:41,104 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-20 14:21:41,106 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-20 14:21:41,108 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-20 14:21:41,108 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-20 14:21:41,109 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-20 14:21:41,110 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-20 14:21:41,111 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-20 14:21:41,112 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-20 14:21:41,345 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-20 14:21:41,361 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 14:21:41,361 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 14:21:42,224 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-20 14:21:43,793 - app.utils.session_state - INFO - Initializing session state
2025-06-20 14:21:43,794 - app.utils.session_state - INFO - Initializing session state
2025-06-20 14:21:43,795 - app.utils.session_state - INFO - Session state initialized
2025-06-20 14:21:43,796 - app.utils.session_state - INFO - Session state initialized
2025-06-20 14:21:43,812 - app - INFO - Found 13 stock files in data/stocks
2025-06-20 14:21:43,830 - app.utils.memory_management - INFO - Memory before cleanup: 427.40 MB
2025-06-20 14:21:44,003 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-20 14:21:44,004 - app.utils.memory_management - INFO - Memory after cleanup: 427.41 MB (freed -0.01 MB)
2025-06-20 14:21:46,244 - app.utils.memory_management - INFO - Memory before cleanup: 432.97 MB
2025-06-20 14:21:46,444 - app.utils.memory_management - INFO - Garbage collection: collected 113 objects
2025-06-20 14:21:46,444 - app.utils.memory_management - INFO - Memory after cleanup: 432.97 MB (freed 0.00 MB)
2025-06-20 14:21:48,651 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 14:21:48,688 - app.utils.memory_management - INFO - Memory before cleanup: 433.66 MB
2025-06-20 14:21:48,900 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-06-20 14:21:48,900 - app.utils.memory_management - INFO - Memory after cleanup: 433.70 MB (freed -0.04 MB)
2025-06-20 14:21:53,188 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 14:21:53,214 - app.utils.memory_management - INFO - Memory before cleanup: 433.70 MB
2025-06-20 14:21:53,411 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 14:21:53,414 - app.utils.memory_management - INFO - Memory after cleanup: 433.70 MB (freed 0.00 MB)
2025-06-20 14:21:54,525 - app - INFO - Using TensorFlow-based LSTM model
2025-06-20 14:21:54,566 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-20 14:21:54,602 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-20 14:21:54,604 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-20 14:21:54,922 - app.utils.memory_management - INFO - Memory before cleanup: 436.46 MB
2025-06-20 14:21:55,113 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-20 14:21:55,113 - app.utils.memory_management - INFO - Memory after cleanup: 436.44 MB (freed 0.02 MB)
2025-06-20 16:31:22,081 - app - INFO - Cleaning up resources...
2025-06-20 16:31:22,081 - app.utils.memory_management - INFO - Memory before cleanup: 383.67 MB
2025-06-20 16:31:22,282 - app.utils.memory_management - INFO - Garbage collection: collected 358 objects
2025-06-20 16:31:22,284 - app.utils.memory_management - INFO - Memory after cleanup: 425.17 MB (freed -41.50 MB)
2025-06-20 16:31:22,284 - app - INFO - Application shutdown complete
