2025-06-30 11:08:53,726 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-30 11:08:56,101 - app - INFO - Memory management utilities loaded
2025-06-30 11:08:56,107 - app - INFO - Error handling utilities loaded
2025-06-30 11:08:56,112 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-30 11:08:56,115 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-30 11:08:56,117 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-30 11:08:56,118 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-30 11:08:56,135 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-30 11:08:56,137 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-30 11:08:56,140 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-30 11:08:56,141 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-30 11:08:56,142 - app - INFO - Applied NumPy fix
2025-06-30 11:08:56,143 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-30 11:08:56,144 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-30 11:08:56,145 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-30 11:08:56,147 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-30 11:08:56,147 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-30 11:08:56,147 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-30 11:08:56,148 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-30 11:08:56,148 - app - INFO - Applied NumPy BitGenerator fix
2025-06-30 11:09:13,733 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-30 11:09:13,734 - app - INFO - Applied TensorFlow fix
2025-06-30 11:09:13,737 - app.config - INFO - Configuration initialized
2025-06-30 11:09:13,746 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-30 11:09:14,054 - models.train - INFO - TensorFlow test successful
2025-06-30 11:09:18,810 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-30 11:09:18,811 - models.train - INFO - Transformer model is available
2025-06-30 11:09:18,811 - models.train - INFO - Using TensorFlow-based models
2025-06-30 11:09:18,820 - models.predict - INFO - Transformer model is available for predictions
2025-06-30 11:09:18,820 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-30 11:09:18,825 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-30 11:09:20,688 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-30 11:09:20,689 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-30 11:09:20,689 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-30 11:09:20,689 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-30 11:09:20,690 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-30 11:09:20,690 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-30 11:09:20,690 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-30 11:09:20,690 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-30 11:09:20,690 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-30 11:09:20,690 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-30 11:09:21,017 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-30 11:09:21,021 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:09:21,614 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-30 11:09:24,091 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-30 11:09:24,370 - app.utils.session_state - INFO - Initializing session state
2025-06-30 11:09:24,371 - app.utils.session_state - INFO - Session state initialized
2025-06-30 11:09:25,503 - app - INFO - Found 14 stock files in data/stocks
2025-06-30 11:09:25,519 - app.utils.memory_management - INFO - Memory before cleanup: 429.54 MB
2025-06-30 11:09:25,727 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-30 11:09:25,727 - app.utils.memory_management - INFO - Memory after cleanup: 429.55 MB (freed -0.01 MB)
2025-06-30 11:09:34,298 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:09:34,336 - app.utils.memory_management - INFO - Memory before cleanup: 433.69 MB
2025-06-30 11:09:34,622 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-30 11:09:34,624 - app.utils.memory_management - INFO - Memory after cleanup: 433.69 MB (freed 0.00 MB)
2025-06-30 11:09:35,535 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:09:35,707 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.12 seconds
2025-06-30 11:09:35,708 - app - INFO - Date range: 2022-08-15 to 2025-06-27
2025-06-30 11:09:35,711 - app - INFO - Data shape: (750, 36)
2025-06-30 11:09:35,712 - app - INFO - File COMI contains 2025 data
2025-06-30 11:09:35,755 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-30 11:09:35,756 - app - INFO - Features shape: (750, 36)
2025-06-30 11:09:35,776 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-30 11:09:35,776 - app - INFO - Date range: 2022-08-15 to 2025-06-27
2025-06-30 11:09:35,778 - app - INFO - Data shape: (750, 36)
2025-06-30 11:09:35,779 - app - INFO - File COMI contains 2025 data
2025-06-30 11:09:35,782 - app.utils.memory_management - INFO - Memory before cleanup: 438.18 MB
2025-06-30 11:09:35,953 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-30 11:09:35,953 - app.utils.memory_management - INFO - Memory after cleanup: 438.22 MB (freed -0.04 MB)
2025-06-30 11:09:36,132 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:09:36,206 - app.utils.memory_management - INFO - Memory before cleanup: 439.25 MB
2025-06-30 11:09:36,376 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-06-30 11:09:36,376 - app.utils.memory_management - INFO - Memory after cleanup: 439.23 MB (freed 0.02 MB)
2025-06-30 11:09:53,034 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:09:53,412 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-30 11:09:55,176 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-06-30 11:09:55,215 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.03 seconds
2025-06-30 11:09:55,216 - app.utils.common - INFO - Date range: 2024-07-01 to 2025-06-13
2025-06-30 11:09:55,217 - app.utils.common - INFO - Data shape: (250, 6)
2025-06-30 11:09:55,218 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-30 11:09:56,112 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.94 seconds
2025-06-30 11:09:57,241 - app.utils.memory_management - INFO - Memory before cleanup: 451.02 MB
2025-06-30 11:09:57,512 - app.utils.memory_management - INFO - Garbage collection: collected 4054 objects
2025-06-30 11:09:57,515 - app.utils.memory_management - INFO - Memory after cleanup: 451.02 MB (freed 0.00 MB)
2025-06-30 11:10:26,595 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:10:27,083 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-30 11:10:27,364 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-06-30 11:10:27,373 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-06-30 11:10:27,374 - app.utils.common - INFO - Date range: 2024-07-01 to 2025-06-13
2025-06-30 11:10:27,374 - app.utils.common - INFO - Data shape: (250, 6)
2025-06-30 11:10:27,375 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-30 11:10:27,639 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.28 seconds
2025-06-30 11:10:28,014 - app.utils.memory_management - INFO - Memory before cleanup: 453.17 MB
2025-06-30 11:10:28,184 - app.utils.memory_management - INFO - Garbage collection: collected 2757 objects
2025-06-30 11:10:28,185 - app.utils.memory_management - INFO - Memory after cleanup: 453.15 MB (freed 0.02 MB)
2025-06-30 11:10:28,434 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:10:28,629 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-30 11:10:28,629 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:28,630 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:10:28,638 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:28,646 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:10:28,647 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-30 11:10:28,648 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-30 11:10:28,666 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:10:28,670 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:10:28,688 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:28,717 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:10:28,740 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-06-30 11:10:28,743 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-30 11:10:28,743 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:10:28,744 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:10:28,744 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:28,745 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:10:28,749 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-06-30 11:10:28,750 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-30 11:10:28,750 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:10:28,751 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:10:28,751 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:10:28,754 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:10:28,757 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-06-30 11:10:28,758 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-30 11:10:28,758 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:28,758 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:10:28,758 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:28,758 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:10:28,763 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-30 11:10:28,804 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:28,823 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:28,828 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:28,833 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:28,841 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:10:28,842 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:28,845 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:10:28,848 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:28,852 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:10:28,854 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 11:10:28,883 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:10:28,886 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:10:28,906 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:28,926 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:10:28,930 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-30 11:10:28,930 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:10:28,931 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:10:28,932 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:28,935 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:10:28,939 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-30 11:10:28,939 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:10:28,939 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:10:28,940 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:10:28,943 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:10:28,947 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:10:28,950 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:28,953 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:10:28,958 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:28,961 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:10:28,983 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:29,005 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:10:29,008 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:10:29,011 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:10:29,015 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:29,038 - app.utils.memory_management - INFO - Memory before cleanup: 453.16 MB
2025-06-30 11:10:29,306 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-06-30 11:10:29,306 - app.utils.memory_management - INFO - Memory after cleanup: 453.14 MB (freed 0.02 MB)
2025-06-30 11:10:38,269 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:10:38,322 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:38,345 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:38,346 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:38,347 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:38,354 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:10:38,358 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:38,359 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:10:38,360 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:38,361 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:10:38,361 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 11:10:38,377 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:10:38,378 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:10:38,390 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:38,400 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:10:38,402 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-30 11:10:38,402 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:10:38,403 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:10:38,403 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:38,404 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:10:38,410 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-30 11:10:38,411 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:10:38,413 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:10:38,414 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:10:38,415 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:10:38,418 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:10:38,426 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:38,430 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:10:38,431 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:38,433 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:10:38,452 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:38,464 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:10:38,464 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:10:38,465 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:10:38,467 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:38,484 - app.utils.memory_management - INFO - Memory before cleanup: 453.14 MB
2025-06-30 11:10:38,676 - app.utils.memory_management - INFO - Garbage collection: collected 260 objects
2025-06-30 11:10:38,676 - app.utils.memory_management - INFO - Memory after cleanup: 453.14 MB (freed 0.00 MB)
2025-06-30 11:10:41,916 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:10:41,960 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:41,971 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:41,972 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:41,975 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:41,977 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:10:41,979 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-06-30 11:10:41,982 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:41,996 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:41,997 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:41,998 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:41,998 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:10:41,998 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-06-30 11:10:41,999 - app.pages.predictions_consolidated - INFO - Auto mode selected: rf from available models: ['rf', 'lstm', 'ensemble', 'gb', 'lr', 'hybrid']
2025-06-30 11:10:57,891 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 11:10:58,089 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 11:10:58,259 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-06-30 11:10:58,261 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-06-30 11:10:58,616 - models.hybrid_model - INFO - XGBoost is available
2025-06-30 11:10:58,618 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-06-30 11:10:58,630 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-06-30 11:10:58,630 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-06-30 11:10:58,630 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-06-30 11:10:58,686 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 11:10:58,686 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-30 11:10:58,686 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 11:10:58,695 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 11:10:58,695 - models.predict - INFO - Current price: 84.82, Predicted scaled value: 0.7410916185379028
2025-06-30 11:10:58,695 - models.predict - INFO - Prediction for 60 minutes horizon: 74.13639862448058
2025-06-30 11:10:58,695 - app.pages.predictions_consolidated - WARNING - Unrealistic prediction detected: 74.14 vs current 84.94 (12.7% change). Correcting to reasonable range.
2025-06-30 11:10:58,699 - app.pages.predictions_consolidated - INFO - Corrected prediction: 85.91 (change: 1.1%)
2025-06-30 11:10:58,728 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:58,744 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:58,745 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:58,745 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:58,750 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:10:58,750 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:58,752 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:10:58,752 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:58,752 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:10:58,752 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 11:10:58,764 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:10:58,766 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:10:58,779 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:10:58,793 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:10:58,794 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-30 11:10:58,795 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:10:58,795 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:10:58,796 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:10:58,796 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:10:58,796 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-30 11:10:58,796 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:10:58,796 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:10:58,797 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:10:58,798 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:10:58,798 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:10:58,798 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:58,800 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:10:58,800 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:10:58,800 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:10:58,806 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:58,819 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:10:58,819 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:10:58,820 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:10:58,820 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:10:58,832 - app.utils.memory_management - INFO - Memory before cleanup: 457.01 MB
2025-06-30 11:10:59,019 - app.utils.memory_management - INFO - Garbage collection: collected 276 objects
2025-06-30 11:10:59,019 - app.utils.memory_management - INFO - Memory after cleanup: 457.01 MB (freed 0.00 MB)
2025-06-30 11:11:36,897 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:11:37,365 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-30 11:11:37,627 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-06-30 11:11:37,640 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-06-30 11:11:37,642 - app.utils.common - INFO - Date range: 2024-07-01 to 2025-06-13
2025-06-30 11:11:37,644 - app.utils.common - INFO - Data shape: (250, 6)
2025-06-30 11:11:37,646 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-30 11:11:38,142 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.52 seconds
2025-06-30 11:11:38,583 - app.utils.memory_management - INFO - Memory before cleanup: 458.03 MB
2025-06-30 11:11:38,793 - app.utils.memory_management - INFO - Garbage collection: collected 1867 objects
2025-06-30 11:11:38,794 - app.utils.memory_management - INFO - Memory after cleanup: 458.01 MB (freed 0.02 MB)
2025-06-30 11:11:53,539 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:11:53,913 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-30 11:11:54,273 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-06-30 11:11:54,284 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-06-30 11:11:54,286 - app.utils.common - INFO - Date range: 2024-07-01 to 2025-06-13
2025-06-30 11:11:54,287 - app.utils.common - INFO - Data shape: (250, 6)
2025-06-30 11:11:54,287 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-30 11:11:54,569 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.30 seconds
2025-06-30 11:11:54,895 - app.utils.memory_management - INFO - Memory before cleanup: 458.03 MB
2025-06-30 11:11:55,136 - app.utils.memory_management - INFO - Garbage collection: collected 4099 objects
2025-06-30 11:11:55,136 - app.utils.memory_management - INFO - Memory after cleanup: 458.01 MB (freed 0.02 MB)
2025-06-30 11:11:55,396 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:11:55,471 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:11:55,503 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:11:55,504 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:11:55,507 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:11:55,520 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:11:55,521 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:11:55,522 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:11:55,524 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:11:55,524 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:11:55,524 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 11:11:55,557 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:11:55,557 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:11:55,616 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:11:55,631 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:11:55,632 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-30 11:11:55,634 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:11:55,634 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:11:55,636 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:11:55,640 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:11:55,665 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-30 11:11:55,665 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:11:55,667 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:11:55,669 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:11:55,670 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:11:55,671 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:11:55,672 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:11:55,672 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:11:55,673 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:11:55,674 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:11:55,683 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:11:55,697 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:11:55,698 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:11:55,698 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:11:55,699 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:11:55,710 - app.utils.memory_management - INFO - Memory before cleanup: 458.00 MB
2025-06-30 11:11:55,919 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-06-30 11:11:55,920 - app.utils.memory_management - INFO - Memory after cleanup: 458.00 MB (freed 0.00 MB)
2025-06-30 11:12:07,360 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:12:07,404 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:12:07,416 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:12:07,418 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:12:07,419 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:12:07,423 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:12:07,423 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:12:07,425 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:12:07,427 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:12:07,427 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:12:07,429 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 11:12:07,443 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:12:07,444 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:12:07,460 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:12:07,470 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:12:07,471 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-30 11:12:07,471 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:12:07,472 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:12:07,473 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:12:07,474 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:12:07,474 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-30 11:12:07,475 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:12:07,476 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:12:07,477 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:12:07,478 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:12:07,480 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:12:07,481 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:12:07,482 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:12:07,482 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:12:07,483 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:12:07,494 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:12:07,513 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:12:07,516 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:12:07,518 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:12:07,521 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:12:07,536 - app.utils.memory_management - INFO - Memory before cleanup: 457.99 MB
2025-06-30 11:12:07,796 - app.utils.memory_management - INFO - Garbage collection: collected 257 objects
2025-06-30 11:12:07,796 - app.utils.memory_management - INFO - Memory after cleanup: 457.99 MB (freed 0.00 MB)
2025-06-30 11:12:12,836 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:12:12,891 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:12:12,903 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:12:12,903 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:12:12,904 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:12:18,564 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 11:12:18,753 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 11:12:18,911 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 11:12:18,911 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-06-30 11:12:18,925 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-06-30 11:12:20,446 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 11:12:22,527 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 11:12:22,527 - models.predict - INFO - Current price: 84.92, Predicted scaled value: 1.33536958694458
2025-06-30 11:12:22,529 - models.predict - INFO - Prediction for 60 minutes horizon: 96.89130555097518
2025-06-30 11:12:22,543 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:12:22,544 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:12:22,546 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:12:22,547 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:12:22,547 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:12:22,548 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 11:12:22,562 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:12:22,566 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:12:22,599 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:12:22,617 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:12:22,618 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-30 11:12:22,619 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:12:22,619 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:12:22,620 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:12:22,620 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:12:22,624 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-30 11:12:22,625 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:12:22,625 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:12:22,626 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:12:22,626 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:12:22,627 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:12:22,628 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:12:22,628 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:12:22,628 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:12:22,628 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:12:22,634 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:12:22,648 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:12:22,648 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:12:22,649 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:12:22,649 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:12:22,666 - app.utils.memory_management - INFO - Memory before cleanup: 504.32 MB
2025-06-30 11:12:22,887 - app.utils.memory_management - INFO - Garbage collection: collected 16967 objects
2025-06-30 11:12:22,889 - app.utils.memory_management - INFO - Memory after cleanup: 482.84 MB (freed 21.49 MB)
2025-06-30 11:14:27,928 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:14:27,960 - app - INFO - Found 14 stock files in data/stocks
2025-06-30 11:14:28,005 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:28,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:14:28,032 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:14:28,038 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:28,045 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:14:28,046 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:14:28,048 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:14:28,050 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:28,052 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:14:28,055 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 11:14:28,079 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:14:28,084 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:14:28,103 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:14:28,125 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:14:28,130 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-30 11:14:28,132 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:14:28,135 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:14:28,137 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:14:28,140 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:14:28,141 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-30 11:14:28,142 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:14:28,145 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:14:28,146 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:14:28,148 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:14:28,149 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:14:28,151 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:14:28,151 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:14:28,153 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:28,155 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:14:28,168 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:28,183 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:14:28,184 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:14:28,184 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:14:28,185 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:28,201 - app.utils.memory_management - INFO - Memory before cleanup: 484.47 MB
2025-06-30 11:14:28,485 - app.utils.memory_management - INFO - Garbage collection: collected 273 objects
2025-06-30 11:14:28,485 - app.utils.memory_management - INFO - Memory after cleanup: 484.45 MB (freed 0.02 MB)
2025-06-30 11:14:55,881 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:14:55,940 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:55,955 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:14:55,956 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:14:55,966 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:55,978 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:14:55,981 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:14:55,990 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:14:55,991 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:55,993 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:14:56,007 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 11:14:56,026 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-30 11:14:56,028 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-30 11:14:56,040 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:14:56,061 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-30 11:14:56,065 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-06-30 11:14:56,073 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-30 11:14:56,076 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-30 11:14:56,080 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:14:56,082 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-30 11:14:56,091 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-06-30 11:14:56,097 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-30 11:14:56,107 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-30 11:14:56,111 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:14:56,113 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-30 11:14:56,114 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 11:14:56,115 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 11:14:56,116 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 11:14:56,119 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:56,122 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-30 11:14:56,132 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:14:56,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-30 11:14:56,147 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-06-30 11:14:56,148 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-06-30 11:14:56,149 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 11:15:19,426 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 11:15:19,637 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 11:15:19,794 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 11:15:19,795 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-06-30 11:15:19,796 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-06-30 11:15:19,796 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-06-30 11:15:19,796 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-06-30 11:15:19,825 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 11:15:19,826 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-30 11:15:19,826 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 11:15:19,831 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 11:15:19,832 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7452127933502197
2025-06-30 11:15:19,833 - models.predict - INFO - Prediction for 60 minutes horizon: 74.2941984324282
2025-06-30 11:15:20,786 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 11:15:20,963 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 11:15:21,117 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 11:15:21,118 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-06-30 11:15:21,119 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-06-30 11:15:21,925 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 11:15:23,082 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 11:15:23,082 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 1.3625086545944214
2025-06-30 11:15:23,083 - models.predict - INFO - Prediction for 60 minutes horizon: 97.93046061186308
2025-06-30 11:15:29,912 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 11:15:30,124 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 11:15:30,277 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: -0.04 MB, VMS: -0.04 MB, Percent: -0.00%, Execution time: 0.00s
2025-06-30 11:15:30,278 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-06-30 11:15:30,279 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-06-30 11:15:30,279 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-06-30 11:15:30,279 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-06-30 11:15:30,318 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 11:15:30,337 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-30 11:15:30,338 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 11:15:30,338 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 11:15:30,339 - models.predict - INFO - Current price: 84.86, Predicted scaled value: 0.7869878003017466
2025-06-30 11:15:30,339 - models.predict - INFO - Prediction for 60 minutes horizon: 75.89376369577506
2025-06-30 11:15:35,679 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 11:15:35,864 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 11:15:36,016 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 11:15:36,018 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-06-30 11:15:36,018 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-06-30 11:15:36,019 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-06-30 11:15:36,019 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_60min.joblib
2025-06-30 11:15:36,036 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 11:15:36,037 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-06-30 11:15:36,037 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 11:15:36,037 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 11:15:36,039 - models.predict - INFO - Current price: 84.86, Predicted scaled value: 1.39749124946682
2025-06-30 11:15:36,039 - models.predict - INFO - Prediction for 60 minutes horizon: 99.26994437651102
2025-06-30 11:15:41,666 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 11:15:41,893 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 11:15:42,055 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 11:15:42,059 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-06-30 11:15:42,147 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-06-30 11:15:42,147 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-06-30 11:15:42,148 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-06-30 11:15:42,148 - models.predict - INFO - Ensemble model already loaded
2025-06-30 11:15:42,171 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 11:15:42,172 - models.predict - INFO - Current price: 84.86, Predicted scaled value: 0.9522212408851387
2025-06-30 11:15:42,173 - models.predict - INFO - Prediction for 60 minutes horizon: 82.22055311336054
2025-06-30 11:15:42,175 - app.pages.predictions_consolidated - WARNING - Unrealistic prediction detected: 74.29 vs current 84.94 (12.5% change). Correcting to reasonable range.
2025-06-30 11:15:42,175 - app.pages.predictions_consolidated - INFO - Corrected prediction: 81.26 (change: -4.3%)
2025-06-30 11:15:42,176 - app.pages.predictions_consolidated - WARNING - Unrealistic prediction detected: 97.93 vs current 84.94 (15.3% change). Correcting to reasonable range.
2025-06-30 11:15:42,176 - app.pages.predictions_consolidated - INFO - Corrected prediction: 82.04 (change: -3.4%)
2025-06-30 11:15:42,176 - app.pages.predictions_consolidated - WARNING - Unrealistic prediction detected: 75.89 vs current 84.94 (10.7% change). Correcting to reasonable range.
2025-06-30 11:15:42,177 - app.pages.predictions_consolidated - INFO - Corrected prediction: 85.55 (change: 0.7%)
2025-06-30 11:15:42,177 - app.pages.predictions_consolidated - WARNING - Unrealistic prediction detected: 99.27 vs current 84.94 (16.9% change). Correcting to reasonable range.
2025-06-30 11:15:42,178 - app.pages.predictions_consolidated - INFO - Corrected prediction: 84.68 (change: -0.3%)
2025-06-30 11:15:42,227 - app.utils.memory_management - INFO - Memory before cleanup: 490.62 MB
2025-06-30 11:15:42,409 - app.utils.memory_management - INFO - Garbage collection: collected 36 objects
2025-06-30 11:15:42,410 - app.utils.memory_management - INFO - Memory after cleanup: 490.62 MB (freed 0.00 MB)
2025-06-30 11:24:25,495 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:24:25,535 - app - INFO - Found 14 stock files in data/stocks
2025-06-30 11:24:25,577 - app.utils.memory_management - INFO - Memory before cleanup: 485.26 MB
2025-06-30 11:24:25,829 - app.utils.memory_management - INFO - Garbage collection: collected 314 objects
2025-06-30 11:24:25,832 - app.utils.memory_management - INFO - Memory after cleanup: 485.27 MB (freed -0.00 MB)
2025-06-30 11:24:33,413 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:24:33,475 - app.utils.memory_management - INFO - Memory before cleanup: 485.27 MB
2025-06-30 11:24:33,692 - app.utils.memory_management - INFO - Garbage collection: collected 215 objects
2025-06-30 11:24:33,694 - app.utils.memory_management - INFO - Memory after cleanup: 485.27 MB (freed 0.00 MB)
2025-06-30 11:24:51,285 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:26:02,585 - app.utils.memory_management - INFO - Memory before cleanup: 487.79 MB
2025-06-30 11:26:02,791 - app.utils.memory_management - INFO - Garbage collection: collected 581 objects
2025-06-30 11:26:02,791 - app.utils.memory_management - INFO - Memory after cleanup: 487.79 MB (freed 0.00 MB)
2025-06-30 11:26:35,311 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:26:35,516 - app.utils.memory_management - INFO - Memory before cleanup: 487.78 MB
2025-06-30 11:26:35,731 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-06-30 11:26:35,733 - app.utils.memory_management - INFO - Memory after cleanup: 487.78 MB (freed 0.00 MB)
2025-06-30 11:26:43,622 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:26:43,714 - app.utils.memory_management - INFO - Memory before cleanup: 487.79 MB
2025-06-30 11:26:43,948 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-06-30 11:26:43,950 - app.utils.memory_management - INFO - Memory after cleanup: 487.79 MB (freed 0.00 MB)
2025-06-30 11:26:44,180 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:26:50,930 - app.pages.ai_advisor - WARNING - Advanced prediction module not available, using fallback
2025-06-30 11:26:50,932 - app.pages.ai_advisor - INFO - Generating hybrid predictions for COMI
2025-06-30 11:26:50,932 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-06-30 11:26:50,932 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-06-30 11:26:50,932 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 11:26:51,130 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 77.42252543520277
2025-06-30 11:26:51,130 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-06-30 11:26:51,130 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 11:26:51,312 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 82.77570912440734
2025-06-30 11:26:51,312 - app.models.adaptive - INFO - No valid models for COMI with 240min horizon, using equal weights
2025-06-30 11:26:51,314 - app.models.predict - INFO - Ensemble weights for COMI with 240min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 11:26:51,470 - app.models.predict - WARNING - No models have predictions for 240min horizon, using original ensemble
2025-06-30 11:26:51,525 - app.models.adaptive - INFO - No valid models for COMI with 1440min horizon, using equal weights
2025-06-30 11:26:51,525 - app.models.predict - INFO - Ensemble weights for COMI with 1440min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 11:26:51,687 - app.models.predict - INFO - Adaptive ensemble prediction for 1440min horizon: 84.6642883910504
2025-06-30 11:26:51,687 - app.models.adaptive - INFO - No valid models for COMI with 4320min horizon, using equal weights
2025-06-30 11:26:51,687 - app.models.predict - INFO - Ensemble weights for COMI with 4320min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 11:26:51,866 - app.models.predict - INFO - Adaptive ensemble prediction for 4320min horizon: 84.60821384388493
2025-06-30 11:26:51,866 - app.models.predict - INFO - Prediction completed in 0.93 seconds
2025-06-30 11:26:51,866 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-06-30 11:26:51,866 - app.models.predict - INFO - Using specified model type: lstm
2025-06-30 11:26:51,927 - app.models.predict - INFO - Prediction completed in 0.06 seconds
2025-06-30 11:26:51,929 - app.models.predict - INFO - Using specified model type: bilstm
2025-06-30 11:26:51,994 - app.models.predict - INFO - Prediction completed in 0.06 seconds
2025-06-30 11:26:51,994 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-06-30 11:26:52,257 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 11:26:52,331 - cmdstanpy - DEBUG - Adding TBB (D:\AI Stocks Bot\python310_venv\lib\site-packages\prophet\stan_model\cmdstan-2.33.1\stan\lib\stan_math\lib\tbb) to PATH
2025-06-30 11:26:52,340 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 11:26:52,373 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\12nq2zzk.json
2025-06-30 11:26:52,438 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\cgaqbpfu.json
2025-06-30 11:26:52,449 - cmdstanpy - DEBUG - idx 0
2025-06-30 11:26:52,451 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 11:26:52,451 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=75988', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\12nq2zzk.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\cgaqbpfu.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_modelnul941jy\\prophet_model-20250630112652.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 11:26:52,451 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 11:26:53,466 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 11:26:53,518 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 11:26:53,591 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 11:26:53,600 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 11:26:53,618 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\_p7miilx.json
2025-06-30 11:26:53,677 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\stcg_199.json
2025-06-30 11:26:53,679 - cmdstanpy - DEBUG - idx 0
2025-06-30 11:26:53,679 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 11:26:53,679 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=44083', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\_p7miilx.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\stcg_199.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_modeltlgtiym4\\prophet_model-20250630112653.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 11:26:53,681 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 11:26:54,415 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 11:26:54,465 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 11:26:54,540 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 11:26:54,546 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 11:26:54,563 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\lvoofhr7.json
2025-06-30 11:26:54,622 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\0x1you6x.json
2025-06-30 11:26:54,626 - cmdstanpy - DEBUG - idx 0
2025-06-30 11:26:54,626 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 11:26:54,626 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=82009', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\lvoofhr7.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\0x1you6x.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_modelr6rywf36\\prophet_model-20250630112654.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 11:26:54,626 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 11:26:55,338 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 11:26:55,388 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 11:26:55,476 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 11:26:55,484 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 11:26:55,501 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\n3quyq5j.json
2025-06-30 11:26:55,560 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\gv7fmu3z.json
2025-06-30 11:26:55,562 - cmdstanpy - DEBUG - idx 0
2025-06-30 11:26:55,562 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 11:26:55,562 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=54987', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\n3quyq5j.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\gv7fmu3z.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_modelyxxuwmtl\\prophet_model-20250630112655.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 11:26:55,562 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 11:26:56,287 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 11:26:56,334 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 11:26:56,409 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 11:26:56,415 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 11:26:56,431 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\kblkeuzb.json
2025-06-30 11:26:56,498 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\c8a3ag3k.json
2025-06-30 11:26:56,500 - cmdstanpy - DEBUG - idx 0
2025-06-30 11:26:56,502 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 11:26:56,502 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=78733', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\kblkeuzb.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\c8a3ag3k.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_modelw521qnx6\\prophet_model-20250630112656.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 11:26:56,504 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 11:26:57,235 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 11:26:57,282 - app.models.hybrid_predict - INFO - Statistical predictions generated for COMI
2025-06-30 11:26:57,301 - app.models.hybrid_predict - INFO - Trend-adjusted predictions generated for COMI
2025-06-30 11:26:57,303 - app.models.hybrid_predict - WARNING - Prediction for COMI at horizon 1440 was floored: 43.05 -> 67.95
2025-06-30 11:26:57,303 - app.pages.ai_advisor - INFO - Hybrid predictions generated for 5 horizons
2025-06-30 11:26:57,303 - app.pages.ai_advisor - INFO - Detecting market regime for COMI
2025-06-30 11:26:57,305 - app.pages.ai_advisor - INFO - Market regime detected: sideways
2025-06-30 11:26:57,315 - app.pages.ai_advisor - INFO - Created enhanced basic fallback predictions with 3 models
2025-06-30 11:26:57,315 - app.pages.ai_advisor - INFO - Consensus predictions generated for 5 horizons
2025-06-30 11:26:58,502 - app.utils.memory_management - INFO - Memory before cleanup: 496.84 MB
2025-06-30 11:26:58,691 - app.utils.memory_management - INFO - Garbage collection: collected 161 objects
2025-06-30 11:26:58,691 - app.utils.memory_management - INFO - Memory after cleanup: 496.84 MB (freed 0.00 MB)
2025-06-30 11:31:41,567 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:31:41,598 - app - INFO - Found 14 stock files in data/stocks
2025-06-30 11:31:41,885 - app.utils.memory_management - INFO - Memory before cleanup: 496.24 MB
2025-06-30 11:31:42,086 - app.utils.memory_management - INFO - Garbage collection: collected 533 objects
2025-06-30 11:31:42,087 - app.utils.memory_management - INFO - Memory after cleanup: 496.24 MB (freed 0.00 MB)
2025-06-30 11:33:12,404 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:33:13,824 - app.utils.memory_management - INFO - Memory before cleanup: 496.40 MB
2025-06-30 11:33:14,055 - app.utils.memory_management - INFO - Garbage collection: collected 444 objects
2025-06-30 11:33:14,055 - app.utils.memory_management - INFO - Memory after cleanup: 496.40 MB (freed 0.00 MB)
2025-06-30 11:33:30,104 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:33:31,391 - app.utils.memory_management - INFO - Memory before cleanup: 496.41 MB
2025-06-30 11:33:31,603 - app.utils.memory_management - INFO - Garbage collection: collected 442 objects
2025-06-30 11:33:31,604 - app.utils.memory_management - INFO - Memory after cleanup: 496.41 MB (freed 0.00 MB)
2025-06-30 11:34:02,807 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:34:04,082 - app.utils.memory_management - INFO - Memory before cleanup: 496.65 MB
2025-06-30 11:34:04,269 - app.utils.memory_management - INFO - Garbage collection: collected 442 objects
2025-06-30 11:34:04,269 - app.utils.memory_management - INFO - Memory after cleanup: 496.65 MB (freed 0.00 MB)
2025-06-30 11:36:27,494 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:36:27,573 - app.utils.memory_management - INFO - Memory before cleanup: 479.62 MB
2025-06-30 11:36:27,853 - app.utils.memory_management - INFO - Garbage collection: collected 318 objects
2025-06-30 11:36:27,857 - app.utils.memory_management - INFO - Memory after cleanup: 479.62 MB (freed 0.00 MB)
2025-06-30 11:36:28,104 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 11:36:28,382 - app.utils.memory_management - INFO - Memory before cleanup: 479.69 MB
2025-06-30 11:36:28,602 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-06-30 11:36:28,603 - app.utils.memory_management - INFO - Memory after cleanup: 479.72 MB (freed -0.03 MB)
2025-06-30 13:59:00,109 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 13:59:00,125 - app - INFO - Found 14 stock files in data/stocks
2025-06-30 13:59:00,158 - app.utils.memory_management - INFO - Memory before cleanup: 433.16 MB
2025-06-30 13:59:00,407 - app.utils.memory_management - INFO - Garbage collection: collected 314 objects
2025-06-30 13:59:00,410 - app.utils.memory_management - INFO - Memory after cleanup: 433.16 MB (freed -0.00 MB)
2025-06-30 13:59:00,683 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 13:59:19,654 - app.pages.ai_advisor - WARNING - Advanced prediction module not available, using fallback
2025-06-30 13:59:19,654 - app.pages.ai_advisor - INFO - Generating hybrid predictions for COMI
2025-06-30 13:59:19,654 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-06-30 13:59:19,654 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-06-30 13:59:19,654 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 13:59:19,721 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-30 13:59:19,898 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-30 13:59:20,122 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-06-30 13:59:20,122 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-06-30 13:59:20,126 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-06-30 13:59:20,126 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-06-30 13:59:20,126 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-06-30 13:59:20,178 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-30 13:59:20,178 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-30 13:59:20,178 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 13:59:20,185 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 13:59:20,185 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7452127933502197
2025-06-30 13:59:20,187 - models.predict - INFO - Prediction for 30 minutes horizon: 74.2941984324282
2025-06-30 13:59:20,250 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-30 13:59:20,424 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-30 13:59:20,596 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:20,600 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-06-30 13:59:20,600 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-06-30 13:59:20,600 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-06-30 13:59:20,600 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-06-30 13:59:20,631 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-30 13:59:20,652 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-30 13:59:20,652 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 13:59:20,654 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 13:59:20,655 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7924384297325539
2025-06-30 13:59:20,655 - models.predict - INFO - Prediction for 30 minutes horizon: 76.10246832893075
2025-06-30 13:59:20,709 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-30 13:59:20,872 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-30 13:59:21,055 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:21,058 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-06-30 13:59:21,059 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-06-30 13:59:21,940 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-30 13:59:23,158 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:23,159 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.9430897831916809
2025-06-30 13:59:23,159 - models.predict - INFO - Prediction for 30 minutes horizon: 81.87090954424936
2025-06-30 13:59:23,161 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 77.42252543520277
2025-06-30 13:59:23,162 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-06-30 13:59:23,162 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 13:59:23,239 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 13:59:23,257 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 13:59:23,550 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-30 13:59:23,550 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 13:59:23,766 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.70 MB, VMS: 1.05 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:23,775 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-06-30 13:59:23,777 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.340
2025-06-30 13:59:23,777 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-06-30 13:59:23,782 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-06-30 13:59:23,787 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-06-30 13:59:23,837 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 13:59:23,839 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-30 13:59:23,839 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 13:59:23,851 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 13:59:23,851 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7452127933502197
2025-06-30 13:59:23,854 - models.predict - INFO - Prediction for 60 minutes horizon: 74.2941984324282
2025-06-30 13:59:23,984 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 13:59:24,159 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 13:59:24,362 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.45 MB, VMS: 0.32 MB, Percent: 0.00%, Execution time: 0.01s
2025-06-30 13:59:24,364 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-06-30 13:59:24,364 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-06-30 13:59:24,364 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-06-30 13:59:24,366 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-06-30 13:59:24,391 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 13:59:24,411 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-30 13:59:24,411 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 13:59:24,411 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 13:59:24,417 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7924384297325539
2025-06-30 13:59:24,417 - models.predict - INFO - Prediction for 60 minutes horizon: 76.10246832893075
2025-06-30 13:59:24,505 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 13:59:24,687 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 13:59:24,687 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-30 13:59:24,877 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.03 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-06-30 13:59:24,879 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-06-30 13:59:24,881 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-06-30 13:59:25,020 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-30 13:59:25,022 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-30 13:59:25,026 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-30 13:59:25,027 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: MEDIUM (49.3)
2025-06-30 13:59:25,027 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-30 13:59:26,163 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 13:59:27,604 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:27,604 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 1.3625086545944214
2025-06-30 13:59:27,604 - models.predict - INFO - Prediction for 60 minutes horizon: 97.93046061186308
2025-06-30 13:59:27,606 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 82.77570912440734
2025-06-30 13:59:27,608 - app.models.adaptive - INFO - No valid models for COMI with 240min horizon, using equal weights
2025-06-30 13:59:27,610 - app.models.predict - INFO - Ensemble weights for COMI with 240min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 13:59:27,688 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-30 13:59:27,920 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-30 13:59:28,212 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.09 MB, Percent: 0.00%, Execution time: 0.07s
2025-06-30 13:59:28,215 - models.predict - INFO - Using scikit-learn rf model for 240 minutes horizon
2025-06-30 13:59:28,215 - models.predict - INFO - Loading rf model for COMI with horizon 240
2025-06-30 13:59:28,215 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_240min.joblib
2025-06-30 13:59:28,216 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_240min.joblib, searching for alternatives...
2025-06-30 13:59:28,218 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-06-30 13:59:28,222 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-06-30 13:59:28,225 - models.predict - WARNING - Model file not found or import error for rf with horizon 240: No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-06-30 13:59:28,227 - models.predict - INFO - Skipping rf model for horizon 240 - model not trained for this horizon
2025-06-30 13:59:28,309 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-30 13:59:28,518 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-30 13:59:28,735 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:28,737 - models.predict - INFO - Using scikit-learn gb model for 240 minutes horizon
2025-06-30 13:59:28,738 - models.predict - INFO - Loading gb model for COMI with horizon 240
2025-06-30 13:59:28,738 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_240min.joblib
2025-06-30 13:59:28,738 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_240min.joblib, searching for alternatives...
2025-06-30 13:59:28,742 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-06-30 13:59:28,749 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-06-30 13:59:28,753 - models.predict - WARNING - Model file not found or import error for gb with horizon 240: No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-06-30 13:59:28,755 - models.predict - INFO - Skipping gb model for horizon 240 - model not trained for this horizon
2025-06-30 13:59:28,850 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-30 13:59:29,103 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-30 13:59:29,343 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-06-30 13:59:29,345 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-06-30 13:59:29,345 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-06-30 13:59:29,346 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-06-30 13:59:29,347 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-06-30 13:59:29,354 - app.models.predict - WARNING - No models have predictions for 240min horizon, using original ensemble
2025-06-30 13:59:29,419 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-30 13:59:29,668 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-30 13:59:29,864 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:29,866 - models.predict - INFO - Using RobustEnsembleModel for 240 minutes horizon
2025-06-30 13:59:29,867 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_240min.joblib
2025-06-30 13:59:29,867 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 240
2025-06-30 13:59:29,867 - models.predict - INFO - Created fallback ensemble model with base price: 84.94
2025-06-30 13:59:29,867 - models.predict - INFO - Loading ensemble model for COMI with horizon 240
2025-06-30 13:59:29,868 - models.predict - INFO - Ensemble model already loaded
2025-06-30 13:59:29,893 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-06-30 13:59:29,894 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 89.9367625240908
2025-06-30 13:59:29,895 - models.predict - WARNING - Prediction 4970.234311376188 is too far from current price 84.94, using fallback
2025-06-30 13:59:29,895 - models.predict - INFO - Prediction for 240 minutes horizon: 85.78155373500763
2025-06-30 13:59:29,898 - app.models.adaptive - INFO - No valid models for COMI with 1440min horizon, using equal weights
2025-06-30 13:59:29,899 - app.models.predict - INFO - Ensemble weights for COMI with 1440min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 13:59:29,978 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-30 13:59:30,179 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-30 13:59:30,445 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-06-30 13:59:30,449 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-06-30 13:59:30,450 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-06-30 13:59:30,450 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_1440min.joblib
2025-06-30 13:59:30,451 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_1440min.joblib
2025-06-30 13:59:30,529 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-30 13:59:30,530 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-30 13:59:30,532 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 13:59:30,538 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 13:59:30,539 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7886369001865386
2025-06-30 13:59:30,539 - models.predict - INFO - Prediction for 1440 minutes horizon: 84.91730017459577
2025-06-30 13:59:30,617 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-30 13:59:30,875 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-30 13:59:31,095 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:31,097 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-06-30 13:59:31,097 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-06-30 13:59:31,099 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_1440min.joblib
2025-06-30 13:59:31,100 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_1440min.joblib
2025-06-30 13:59:31,140 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-30 13:59:31,168 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-30 13:59:31,169 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 13:59:31,170 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 13:59:31,171 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.758768370128838
2025-06-30 13:59:31,173 - models.predict - INFO - Prediction for 1440 minutes horizon: 83.28050481889686
2025-06-30 13:59:31,242 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-30 13:59:31,451 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-30 13:59:31,664 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:31,665 - app.utils.ai_pattern_recognition - INFO - Using live price 50.19 EGP from API for ABUK
2025-06-30 13:59:31,666 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-06-30 13:59:31,668 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-30 13:59:31,668 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-30 13:59:31,668 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-06-30 13:59:31,670 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-30 13:59:31,672 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-30 13:59:31,738 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-30 13:59:31,739 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 49.1 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 49.13803972029677, 'bearish_score': 0}
2025-06-30 13:59:31,740 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-06-30 13:59:33,123 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-30 13:59:35,145 - app.utils.memory_management - INFO - Memory before cleanup: 496.41 MB
2025-06-30 13:59:35,502 - tensorflow - WARNING - 5 out of the last 5 calls to <function Model.make_predict_function.<locals>.predict_function at 0x00000225F4092C20> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-06-30 13:59:35,507 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:35,509 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.8046544194221497
2025-06-30 13:59:35,513 - models.predict - INFO - Prediction for 1440 minutes horizon: 85.79506017965859
2025-06-30 13:59:35,521 - app.utils.memory_management - INFO - Garbage collection: collected 10255 objects
2025-06-30 13:59:35,523 - app.models.predict - INFO - Adaptive ensemble prediction for 1440min horizon: 84.6642883910504
2025-06-30 13:59:35,525 - app.utils.memory_management - INFO - Memory after cleanup: 484.57 MB (freed 11.84 MB)
2025-06-30 13:59:35,525 - app.models.adaptive - INFO - No valid models for COMI with 4320min horizon, using equal weights
2025-06-30 13:59:35,725 - app.models.predict - INFO - Ensemble weights for COMI with 4320min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-30 13:59:35,829 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-30 13:59:35,843 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-30 13:59:36,063 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: -11.81 MB, VMS: -6.96 MB, Percent: -0.07%, Execution time: 0.03s
2025-06-30 13:59:36,065 - models.predict - INFO - Using scikit-learn rf model for 4320 minutes horizon
2025-06-30 13:59:36,066 - models.predict - INFO - Loading rf model for COMI with horizon 4320
2025-06-30 13:59:36,066 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4320min.joblib
2025-06-30 13:59:36,067 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4320min.joblib
2025-06-30 13:59:36,162 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-30 13:59:36,163 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-30 13:59:36,163 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 13:59:36,170 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 13:59:36,171 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7951971232891083
2025-06-30 13:59:36,172 - models.predict - INFO - Prediction for 4320 minutes horizon: 85.27680038052794
2025-06-30 13:59:36,234 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-30 13:59:36,411 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-30 13:59:36,589 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:36,592 - models.predict - INFO - Using scikit-learn gb model for 4320 minutes horizon
2025-06-30 13:59:36,593 - models.predict - INFO - Loading gb model for COMI with horizon 4320
2025-06-30 13:59:36,594 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_4320min.joblib
2025-06-30 13:59:36,594 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_4320min.joblib
2025-06-30 13:59:36,634 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-30 13:59:36,665 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-30 13:59:36,666 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-30 13:59:36,668 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-30 13:59:36,673 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7699433736337192
2025-06-30 13:59:36,673 - models.predict - INFO - Prediction for 4320 minutes horizon: 83.89289497674437
2025-06-30 13:59:36,770 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-30 13:59:37,049 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-30 13:59:37,324 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:37,327 - models.predict - INFO - Loading lstm model for COMI with horizon 4320
2025-06-30 13:59:37,331 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_4320min.keras
2025-06-30 13:59:39,233 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-30 13:59:41,334 - tensorflow - WARNING - 6 out of the last 6 calls to <function Model.make_predict_function.<locals>.predict_function at 0x00000225F3D56830> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-06-30 13:59:41,344 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:41,346 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7838494181632996
2025-06-30 13:59:41,347 - models.predict - INFO - Prediction for 4320 minutes horizon: 84.65494617438245
2025-06-30 13:59:41,349 - app.models.predict - INFO - Adaptive ensemble prediction for 4320min horizon: 84.60821384388493
2025-06-30 13:59:41,349 - app.models.predict - INFO - Prediction completed in 21.69 seconds
2025-06-30 13:59:41,350 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-06-30 13:59:41,351 - app.models.predict - INFO - Using specified model type: lstm
2025-06-30 13:59:41,438 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-30 13:59:41,732 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-30 13:59:41,998 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.98 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:42,000 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-06-30 13:59:42,001 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-06-30 13:59:42,958 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-30 13:59:44,313 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:44,314 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.9430897831916809
2025-06-30 13:59:44,314 - models.predict - INFO - Prediction for 30 minutes horizon: 81.87090954424936
2025-06-30 13:59:44,315 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 13:59:44,521 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 13:59:44,732 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:44,734 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-06-30 13:59:44,734 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-06-30 13:59:45,552 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 13:59:46,725 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:46,726 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 1.3625086545944214
2025-06-30 13:59:46,726 - models.predict - INFO - Prediction for 60 minutes horizon: 97.93046061186308
2025-06-30 13:59:46,726 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-30 13:59:46,947 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-30 13:59:47,117 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:47,119 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-06-30 13:59:47,120 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-06-30 13:59:47,120 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-06-30 13:59:47,120 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-06-30 13:59:47,120 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-30 13:59:47,325 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-30 13:59:47,505 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:47,507 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-06-30 13:59:47,508 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-06-30 13:59:48,366 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-30 13:59:49,537 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:49,538 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.8046544194221497
2025-06-30 13:59:49,538 - models.predict - INFO - Prediction for 1440 minutes horizon: 85.79506017965859
2025-06-30 13:59:49,538 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-30 13:59:49,717 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-30 13:59:49,891 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 13:59:49,892 - models.predict - INFO - Loading lstm model for COMI with horizon 4320
2025-06-30 13:59:49,893 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_4320min.keras
2025-06-30 13:59:50,717 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-30 13:59:52,006 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:52,007 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7838494181632996
2025-06-30 13:59:52,007 - models.predict - INFO - Prediction for 4320 minutes horizon: 84.65494617438245
2025-06-30 13:59:52,012 - app.models.predict - INFO - Prediction completed in 10.66 seconds
2025-06-30 13:59:52,013 - app.models.predict - INFO - Using specified model type: bilstm
2025-06-30 13:59:52,081 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-30 13:59:52,437 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-30 13:59:52,730 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.09 MB, Percent: 0.00%, Execution time: 0.03s
2025-06-30 13:59:52,732 - models.predict - INFO - Loading bilstm model for COMI with horizon 30
2025-06-30 13:59:52,740 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_30min.keras
2025-06-30 13:59:54,026 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 13:59:54,248 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-30 13:59:54,341 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.060
2025-06-30 13:59:54,564 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-30 13:59:54,846 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-30 13:59:54,857 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-30 13:59:54,859 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-30 13:59:54,861 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (62.6)
2025-06-30 13:59:54,865 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-30 13:59:56,444 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-30 13:59:59,657 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 13:59:59,658 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 1.2314177751541138
2025-06-30 13:59:59,661 - models.predict - INFO - Prediction for 30 minutes horizon: 92.91099006245982
2025-06-30 13:59:59,662 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-30 13:59:59,981 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-30 14:00:00,232 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 14:00:00,234 - models.predict - INFO - Loading bilstm model for COMI with horizon 60
2025-06-30 14:00:00,241 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_60min.keras
2025-06-30 14:00:00,963 - app.utils.ai_pattern_recognition - INFO - Using live price 85.33 EGP from API for COMI
2025-06-30 14:00:00,965 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-30 14:00:00,967 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-30 14:00:00,969 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-30 14:00:00,969 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-30 14:00:01,079 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='SELL', volatility_factor=0.050
2025-06-30 14:00:01,081 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'SELL', 'confidence': 0.5, 'reasoning': 'AI consensus shows bearish signals across multiple models (Score: 17.5 vs 0.0)', 'risk_level': 'Moderate', 'bullish_score': 0, 'bearish_score': 17.5}
2025-06-30 14:00:01,082 - app.pages.advanced_ai_features - INFO - Using SELL logic: entry=85.36, stop=89.63, target=74.69
2025-06-30 14:00:01,085 - app.pages.advanced_ai_features - WARNING - FORCED SELL OVERRIDE: entry=85.36, stop=89.63, target=74.69
2025-06-30 14:00:01,245 - app.utils.memory_management - INFO - Memory before cleanup: 531.95 MB
2025-06-30 14:00:01,804 - app.utils.memory_management - INFO - Garbage collection: collected 26865 objects
2025-06-30 14:00:01,804 - app.utils.memory_management - INFO - Memory after cleanup: 489.52 MB (freed 42.44 MB)
2025-06-30 14:00:03,364 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-30 14:00:06,560 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 14:00:06,560 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 1.2891823053359985
2025-06-30 14:00:06,561 - models.predict - INFO - Prediction for 60 minutes horizon: 95.12279426490332
2025-06-30 14:00:06,561 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-30 14:00:06,788 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-30 14:00:06,983 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 14:00:06,985 - models.predict - INFO - Loading bilstm model for COMI with horizon 240
2025-06-30 14:00:06,986 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-06-30 14:00:06,986 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 240: Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-06-30 14:00:06,986 - models.predict - INFO - Skipping bilstm model for horizon 240 - model not trained for this horizon
2025-06-30 14:00:06,986 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-30 14:00:07,242 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-30 14:00:07,422 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 14:00:07,424 - models.predict - INFO - Loading bilstm model for COMI with horizon 1440
2025-06-30 14:00:07,424 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_1440min.keras
2025-06-30 14:00:09,072 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-30 14:00:11,380 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 14:00:11,381 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7672058939933777
2025-06-30 14:00:11,382 - models.predict - INFO - Prediction for 1440 minutes horizon: 83.74288110083633
2025-06-30 14:00:11,383 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-30 14:00:11,614 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-30 14:00:11,798 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-30 14:00:11,800 - models.predict - INFO - Loading bilstm model for COMI with horizon 4320
2025-06-30 14:00:11,800 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_4320min.keras
2025-06-30 14:00:13,453 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-30 14:00:15,686 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-30 14:00:15,686 - models.predict - INFO - Current price: 84.94, Predicted scaled value: 0.7587751746177673
2025-06-30 14:00:15,687 - models.predict - INFO - Prediction for 4320 minutes horizon: 83.28087770486934
2025-06-30 14:00:15,688 - app.models.predict - INFO - Prediction completed in 23.68 seconds
2025-06-30 14:00:15,689 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-06-30 14:00:15,694 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 14:00:15,770 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 14:00:15,777 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 14:00:15,794 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\b8m_aycz.json
2025-06-30 14:00:15,850 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\pllr_gke.json
2025-06-30 14:00:15,852 - cmdstanpy - DEBUG - idx 0
2025-06-30 14:00:15,852 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 14:00:15,852 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=46515', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\b8m_aycz.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\pllr_gke.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_modeleobsxo1c\\prophet_model-20250630140015.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 14:00:15,852 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 14:00:16,724 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 14:00:16,782 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 14:00:16,856 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 14:00:16,863 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 14:00:16,881 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\pnvvtgf0.json
2025-06-30 14:00:16,936 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\en10wvok.json
2025-06-30 14:00:16,938 - cmdstanpy - DEBUG - idx 0
2025-06-30 14:00:16,938 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 14:00:16,938 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=79139', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\pnvvtgf0.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\en10wvok.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_model1f2muyzi\\prophet_model-20250630140016.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 14:00:16,939 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 14:00:17,617 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 14:00:17,664 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 14:00:17,731 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 14:00:17,737 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 14:00:17,754 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\o1ffezxb.json
2025-06-30 14:00:17,815 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\49ytx8wn.json
2025-06-30 14:00:17,817 - cmdstanpy - DEBUG - idx 0
2025-06-30 14:00:17,817 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 14:00:17,818 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=77357', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\o1ffezxb.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\49ytx8wn.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_model56caqosj\\prophet_model-20250630140017.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 14:00:17,818 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 14:00:18,508 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 14:00:18,552 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 14:00:18,620 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 14:00:18,627 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 14:00:18,644 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\fljq64ky.json
2025-06-30 14:00:18,701 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\_inndlpu.json
2025-06-30 14:00:18,703 - cmdstanpy - DEBUG - idx 0
2025-06-30 14:00:18,703 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 14:00:18,703 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=37579', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\fljq64ky.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\_inndlpu.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_model_ksz6xqw\\prophet_model-20250630140018.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 14:00:18,703 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 14:00:19,379 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 14:00:19,421 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-30 14:00:19,493 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-30 14:00:19,500 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-30 14:00:19,516 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\_e06yc4e.json
2025-06-30 14:00:19,570 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpiq0oymwz\b0wehkez.json
2025-06-30 14:00:19,572 - cmdstanpy - DEBUG - idx 0
2025-06-30 14:00:19,573 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-30 14:00:19,573 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=11157', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\_e06yc4e.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\b0wehkez.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpiq0oymwz\\prophet_modelrp375lqj\\prophet_model-20250630140019.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-30 14:00:19,573 - cmdstanpy - INFO - Chain [1] start processing
2025-06-30 14:00:20,270 - cmdstanpy - INFO - Chain [1] done processing
2025-06-30 14:00:20,315 - app.models.hybrid_predict - INFO - Statistical predictions generated for COMI
2025-06-30 14:00:20,316 - app.models.hybrid_predict - INFO - Trend-adjusted predictions generated for COMI
2025-06-30 14:00:20,317 - app.models.hybrid_predict - WARNING - Prediction for COMI at horizon 1440 was floored: 43.05 -> 67.95
2025-06-30 14:00:20,317 - app.pages.ai_advisor - INFO - Hybrid predictions generated for 5 horizons
2025-06-30 14:00:20,317 - app.pages.ai_advisor - INFO - Detecting market regime for COMI
2025-06-30 14:00:20,319 - app.pages.ai_advisor - INFO - Market regime detected: sideways
2025-06-30 14:00:20,327 - app.pages.ai_advisor - INFO - Created enhanced basic fallback predictions with 3 models
2025-06-30 14:00:20,328 - app.pages.ai_advisor - INFO - Consensus predictions generated for 5 horizons
2025-06-30 14:00:20,331 - app.utils.memory_management - INFO - Memory before cleanup: 592.34 MB
2025-06-30 14:00:20,652 - app.utils.memory_management - INFO - Garbage collection: collected 59133 objects
2025-06-30 14:00:20,652 - app.utils.memory_management - INFO - Memory after cleanup: 497.59 MB (freed 94.75 MB)
2025-06-30 14:00:27,356 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:00:27,466 - app.utils.memory_management - INFO - Memory before cleanup: 494.36 MB
2025-06-30 14:00:27,762 - app.utils.memory_management - INFO - Garbage collection: collected 345 objects
2025-06-30 14:00:27,762 - app.utils.memory_management - INFO - Memory after cleanup: 494.36 MB (freed 0.00 MB)
2025-06-30 14:00:43,779 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:00:43,829 - app.utils.memory_management - INFO - Memory before cleanup: 494.65 MB
2025-06-30 14:00:44,059 - app.utils.memory_management - INFO - Garbage collection: collected 190 objects
2025-06-30 14:00:44,059 - app.utils.memory_management - INFO - Memory after cleanup: 494.65 MB (freed 0.00 MB)
2025-06-30 14:00:50,410 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:00:50,469 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-30 14:00:50,504 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.104
2025-06-30 14:00:50,608 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-30 14:00:50,693 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-30 14:00:50,697 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (21.00%) exceeds limit (15.00%)
2025-06-30 14:00:50,697 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-30 14:00:50,697 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (56.7)
2025-06-30 14:00:50,697 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-30 14:00:56,860 - app.utils.ai_pattern_recognition - INFO - Using live price 85.33 EGP from API for COMI
2025-06-30 14:00:56,862 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-30 14:00:56,862 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-30 14:00:56,864 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-30 14:00:56,938 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='HOLD', volatility_factor=0.050
2025-06-30 14:00:56,939 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'HOLD', 'confidence': 0.5, 'reasoning': 'Mixed AI signals suggest maintaining current position (Bullish: 16.0, Bearish: 17.5)', 'risk_level': 'Moderate', 'bullish_score': 16.0, 'bearish_score': 17.5}
2025-06-30 14:00:56,939 - app.pages.advanced_ai_features - INFO - Using HOLD logic: entry=84.94, stop=80.69, target=93.43
2025-06-30 14:00:57,081 - app.utils.memory_management - INFO - Memory before cleanup: 500.75 MB
2025-06-30 14:00:57,301 - app.utils.memory_management - INFO - Garbage collection: collected 1009 objects
2025-06-30 14:00:57,303 - app.utils.memory_management - INFO - Memory after cleanup: 500.75 MB (freed 0.00 MB)
2025-06-30 14:02:01,363 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:02:01,411 - app.utils.memory_management - INFO - Memory before cleanup: 500.73 MB
2025-06-30 14:02:01,724 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-30 14:02:01,725 - app.utils.memory_management - INFO - Memory after cleanup: 500.73 MB (freed 0.00 MB)
2025-06-30 14:02:07,093 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:02:07,122 - app.utils.memory_management - INFO - Memory before cleanup: 500.73 MB
2025-06-30 14:02:07,399 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-30 14:02:07,399 - app.utils.memory_management - INFO - Memory after cleanup: 500.73 MB (freed 0.00 MB)
2025-06-30 14:02:09,697 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:02:09,727 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-30 14:02:09,748 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-21 to 2025-06-27
2025-06-30 14:02:09,748 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-30 14:02:17,722 - app.utils.memory_management - INFO - Memory before cleanup: 501.29 MB
2025-06-30 14:02:17,944 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-30 14:02:17,944 - app.utils.memory_management - INFO - Memory after cleanup: 501.29 MB (freed 0.00 MB)
2025-06-30 14:03:48,007 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:03:48,069 - app.utils.memory_management - INFO - Memory before cleanup: 501.29 MB
2025-06-30 14:03:48,368 - app.utils.memory_management - INFO - Garbage collection: collected 302 objects
2025-06-30 14:03:48,370 - app.utils.memory_management - INFO - Memory after cleanup: 501.29 MB (freed 0.00 MB)
2025-06-30 14:03:55,643 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:03:55,678 - app.utils.memory_management - INFO - Memory before cleanup: 501.30 MB
2025-06-30 14:03:55,904 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-30 14:03:55,904 - app.utils.memory_management - INFO - Memory after cleanup: 501.30 MB (freed 0.00 MB)
2025-06-30 14:03:57,059 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:03:57,116 - app.utils.memory_management - INFO - Memory before cleanup: 501.30 MB
2025-06-30 14:03:57,330 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-30 14:03:57,330 - app.utils.memory_management - INFO - Memory after cleanup: 501.30 MB (freed 0.00 MB)
2025-06-30 14:03:59,519 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:03:59,563 - app.utils.memory_management - INFO - Memory before cleanup: 501.30 MB
2025-06-30 14:03:59,784 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-30 14:03:59,784 - app.utils.memory_management - INFO - Memory after cleanup: 501.30 MB (freed 0.00 MB)
2025-06-30 14:04:00,852 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 14:04:00,875 - app - INFO - Found 14 stock files in data/stocks
2025-06-30 14:04:06,655 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-30 14:04:06,739 - app.utils.memory_management - INFO - Memory before cleanup: 501.59 MB
2025-06-30 14:04:06,956 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-30 14:04:06,956 - app.utils.memory_management - INFO - Memory after cleanup: 501.59 MB (freed 0.00 MB)
2025-06-30 16:22:51,708 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:22:51,741 - app - INFO - Found 14 stock files in data/stocks
2025-06-30 16:22:51,817 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-06-30 16:22:51,860 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 16:22:51,863 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 16:22:51,865 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 16:22:51,869 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 16:22:51,889 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 16:22:51,895 - app.utils.error_handling - INFO - live_trading_component executed in 0.15 seconds
2025-06-30 16:22:51,900 - app.utils.memory_management - INFO - Memory before cleanup: 195.55 MB
2025-06-30 16:22:52,400 - app.utils.memory_management - INFO - Garbage collection: collected 253 objects
2025-06-30 16:22:52,402 - app.utils.memory_management - INFO - Memory after cleanup: 416.84 MB (freed -221.30 MB)
2025-06-30 16:22:57,484 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:23:05,706 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-06-30 16:23:05,707 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-30 16:23:05,708 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-30 16:23:05,709 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-06-30 16:23:05,714 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-30 16:23:05,719 - app.utils.error_handling - INFO - live_trading_component executed in 8.21 seconds
2025-06-30 16:23:05,721 - app.utils.memory_management - INFO - Memory before cleanup: 419.72 MB
2025-06-30 16:23:05,963 - app.utils.memory_management - INFO - Garbage collection: collected 216 objects
2025-06-30 16:23:05,964 - app.utils.memory_management - INFO - Memory after cleanup: 419.79 MB (freed -0.06 MB)
2025-06-30 16:24:24,534 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:24:24,589 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-30 16:24:24,598 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-30 16:24:24,799 - app.utils.memory_management - INFO - Memory before cleanup: 421.95 MB
2025-06-30 16:24:25,061 - app.utils.memory_management - INFO - Garbage collection: collected 221 objects
2025-06-30 16:24:25,061 - app.utils.memory_management - INFO - Memory after cleanup: 421.95 MB (freed 0.00 MB)
2025-06-30 16:24:31,208 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:24:31,237 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-30 16:24:31,243 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-30 16:24:31,343 - app.utils.memory_management - INFO - Memory before cleanup: 424.66 MB
2025-06-30 16:24:31,570 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-30 16:24:31,571 - app.utils.memory_management - INFO - Memory after cleanup: 424.66 MB (freed 0.00 MB)
2025-06-30 16:24:34,195 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:24:34,217 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-30 16:24:34,222 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-30 16:24:34,256 - app.utils.historical_data_downloader - INFO - Starting historical data generation for COMI (3 years)
2025-06-30 16:24:34,256 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-30 16:24:34,267 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-30 16:24:34,268 - app.utils.historical_data_downloader - INFO - Fetching current live data for COMI...
2025-06-30 16:24:34,272 - app.utils.historical_data_downloader - INFO - Downloading historical data for COMI with intervals: ['1D', '1W']
2025-06-30 16:24:34,292 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-30 16:24:34,302 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-COMI'], 'intervals': ['1D', '1W']}
2025-06-30 16:24:40,971 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-06-30 16:24:40,971 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-COMI': [{'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 803.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96187.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -13658.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 5781.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 65573.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 82171.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81239.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 81379.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 81175.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 80896.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 80475.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 80251.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 79951.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 79129.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 79328.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 80304.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 81240.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 81186.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 85403.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 810.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 68008.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -11330.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 6827.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 63661.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 81608.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81498.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 80781.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 80073.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80424.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 79697.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 79358.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 81112.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 73723.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 76506.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 62640.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 56851.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 80072.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 82414.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]}, 'message': 'Successfully scraped 1 pairs'}
2025-06-30 16:24:40,973 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-COMI']
2025-06-30 16:24:40,973 - app.utils.historical_data_downloader - INFO - Found data for EGX-COMI: [{'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 803.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96187.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -13658.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 5781.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 65573.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 82171.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81239.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 81379.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 81175.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 80896.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 80475.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 80251.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 79951.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 79129.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 79328.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 80304.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 81240.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 81186.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 85403.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 810.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 68008.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -11330.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 6827.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 63661.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 81608.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81498.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 80781.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 80073.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80424.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 79697.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 79358.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 81112.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 73723.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 76506.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 62640.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 56851.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 80072.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 82414.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-06-30 16:24:40,975 - app.utils.historical_data_downloader - INFO - Processing 2 data points for COMI
2025-06-30 16:24:40,975 - app.utils.historical_data_downloader - INFO - Raw data structure: [{'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 803.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96187.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -13658.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 5781.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 65573.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 82171.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81239.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 81379.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 81175.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 80896.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 80475.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 80251.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 79951.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 79129.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 79328.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 80304.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 81240.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 81186.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 85403.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 810.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 68008.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -11330.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 6827.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 63661.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 81608.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81498.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 80781.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 80073.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80424.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 79697.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 79358.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 81112.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 73723.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 76506.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 62640.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 56851.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 80072.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 82414.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-06-30 16:24:40,975 - app.utils.historical_data_downloader - INFO - First item structure: {'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 803.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96187.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -13658.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 5781.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 65573.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 82171.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81239.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 81379.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 81175.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 80896.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 80475.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 80251.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 79951.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 79129.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 79328.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 80304.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 81240.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 81186.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 85403.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}
2025-06-30 16:24:40,977 - app.utils.historical_data_downloader - INFO - Processing interval 1D: pair=EGX-COMI, price=84300.0
2025-06-30 16:24:40,977 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 84300.0 -> 84.3 EGP
2025-06-30 16:24:40,977 - app.utils.historical_data_downloader - INFO - Successfully processed 1D: price=84.3 EGP
2025-06-30 16:24:40,979 - app.utils.historical_data_downloader - INFO - Processing interval 1W: pair=EGX-COMI, price=84300.0
2025-06-30 16:24:40,979 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 84300.0 -> 84.3 EGP
2025-06-30 16:24:40,981 - app.utils.historical_data_downloader - INFO - Successfully processed 1W: price=84.3 EGP
2025-06-30 16:24:40,981 - app.utils.historical_data_downloader - INFO - Successfully processed 2 intervals for COMI
2025-06-30 16:24:40,981 - app.utils.historical_data_downloader - INFO - Successfully processed historical data for COMI
2025-06-30 16:24:40,981 - app.utils.historical_data_downloader - INFO - Successfully retrieved current data for COMI: {'1D': {'symbol': 'COMI', 'interval': '1D', 'current_price': 84.3, 'timestamp': datetime.datetime(2025, 6, 30, 16, 24, 40, 977062), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 803.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96187.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -13658.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 5781.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 65573.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 82171.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81239.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 81379.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 81175.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 80896.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 80475.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 80251.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 79951.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 79129.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 79328.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 80304.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 81240.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 81186.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 85403.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 803.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96187.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -13658.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 5781.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 65573.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 82171.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81239.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 81379.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 81175.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80990.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 80896.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 80475.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 80251.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 79951.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 79129.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 79328.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 80304.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 81240.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 81186.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 85403.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}}, '1W': {'symbol': 'COMI', 'interval': '1W', 'current_price': 84.3, 'timestamp': datetime.datetime(2025, 6, 30, 16, 24, 40, 981498), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 810.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 68008.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -11330.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 6827.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 63661.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 81608.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81498.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 80781.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 80073.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80424.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 79697.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 79358.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 81112.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 73723.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 76506.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 62640.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 56851.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 80072.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 82414.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 84300.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Relative Strength Index (14)', 'value': 60923.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic %K (14, 3, 3)', 'value': 93996.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Commodity Channel Index (20)', 'value': 169536.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Average Directional Index (14)', 'value': 20825.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Awesome Oscillator', 'value': 1869.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Momentum (10)', 'value': 6300.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'MACD Level (12, 26)', 'value': 810.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 68008.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Williams Percent Range (14)', 'value': -11330.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Bull Bear Power', 'value': 6827.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 63661.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (10)', 'value': 81608.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (10)', 'value': 81498.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (20)', 'value': 80781.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (20)', 'value': 80073.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (30)', 'value': 80424.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (30)', 'value': 79697.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (50)', 'value': 79358.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (50)', 'value': 81112.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (100)', 'value': 73723.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (100)', 'value': 76506.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Exponential Moving Average (200)', 'value': 62640.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Simple Moving Average (200)', 'value': 56851.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Volume Weighted Moving Average (20)', 'value': 80072.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'name': 'Hull Moving Average (9)', 'value': 82414.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '30/06/2025 16:24', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}}}
2025-06-30 16:24:40,983 - app.utils.historical_data_downloader - INFO - Found valid price from 1D: 84.3 EGP
2025-06-30 16:24:40,985 - app.utils.historical_data_downloader - INFO - Using current price for COMI: 84.3 EGP
2025-06-30 16:24:40,985 - app.utils.historical_data_downloader - INFO - Generating 3 years of synthetic historical data...
2025-06-30 16:24:40,985 - app.utils.historical_data_downloader - INFO - Generating 3 years of synthetic data for COMI
2025-06-30 16:24:41,015 - app.utils.historical_data_downloader - INFO - Generated 750 days of synthetic historical data for COMI
2025-06-30 16:24:41,029 - app.utils.historical_data_downloader - INFO - Generated 750 days of synthetic data
2025-06-30 16:24:41,029 - app.utils.historical_data_downloader - INFO - Saving data to: data\stocks\COMI.csv
2025-06-30 16:24:41,055 - app.utils.historical_data_downloader - INFO - Saved 750 days of historical data to data\stocks\COMI.csv
2025-06-30 16:24:41,055 - app.utils.historical_data_downloader - INFO - CSV file size: 33925 bytes
2025-06-30 16:24:41,073 - app.utils.historical_data_downloader - INFO - Verification: CSV contains 750 rows
2025-06-30 16:24:41,073 - app.utils.historical_data_downloader - INFO - Successfully completed historical data generation for COMI
2025-06-30 16:24:41,151 - app.utils.memory_management - INFO - Memory before cleanup: 428.80 MB
2025-06-30 16:24:41,364 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-30 16:24:41,364 - app.utils.memory_management - INFO - Memory after cleanup: 428.80 MB (freed -0.00 MB)
2025-06-30 16:24:52,036 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:24:52,092 - app.utils.memory_management - INFO - Memory before cleanup: 429.02 MB
2025-06-30 16:24:52,311 - app.utils.memory_management - INFO - Garbage collection: collected 260 objects
2025-06-30 16:24:52,313 - app.utils.memory_management - INFO - Memory after cleanup: 429.03 MB (freed -0.00 MB)
2025-06-30 16:24:58,794 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:24:58,876 - app - INFO - File COMI contains 2025 data
2025-06-30 16:24:58,890 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-30 16:24:58,894 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-06-30 16:24:58,902 - app.utils.memory_management - INFO - Memory before cleanup: 429.48 MB
2025-06-30 16:24:59,167 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-30 16:24:59,173 - app.utils.memory_management - INFO - Memory after cleanup: 429.48 MB (freed -0.00 MB)
2025-06-30 16:25:05,043 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:05,163 - app - INFO - File COMI contains 2025 data
2025-06-30 16:25:05,193 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-30 16:25:05,198 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-06-30 16:25:05,205 - app.utils.memory_management - INFO - Memory before cleanup: 429.82 MB
2025-06-30 16:25:05,555 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-30 16:25:05,558 - app.utils.memory_management - INFO - Memory after cleanup: 429.82 MB (freed 0.00 MB)
2025-06-30 16:25:09,595 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:09,677 - app - INFO - File COMI contains 2025 data
2025-06-30 16:25:09,685 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-30 16:25:09,687 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-06-30 16:25:09,693 - app.utils.memory_management - INFO - Memory before cleanup: 430.24 MB
2025-06-30 16:25:09,924 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-06-30 16:25:09,925 - app.utils.memory_management - INFO - Memory after cleanup: 430.25 MB (freed -0.01 MB)
2025-06-30 16:25:10,914 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:10,999 - app - INFO - File COMI contains 2025 data
2025-06-30 16:25:11,008 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-30 16:25:11,009 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-06-30 16:25:11,012 - app.utils.memory_management - INFO - Memory before cleanup: 430.43 MB
2025-06-30 16:25:11,287 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-30 16:25:11,288 - app.utils.memory_management - INFO - Memory after cleanup: 430.43 MB (freed 0.00 MB)
2025-06-30 16:25:14,316 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:14,468 - app - INFO - File COMI contains 2025 data
2025-06-30 16:25:14,515 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-30 16:25:14,516 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-06-30 16:25:14,519 - app.utils.memory_management - INFO - Memory before cleanup: 431.90 MB
2025-06-30 16:25:14,749 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-30 16:25:14,752 - app.utils.memory_management - INFO - Memory after cleanup: 431.90 MB (freed 0.00 MB)
2025-06-30 16:25:17,917 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:18,107 - app - INFO - File COMI contains 2025 data
2025-06-30 16:25:18,149 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-30 16:25:18,151 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-06-30 16:25:18,153 - app.utils.memory_management - INFO - Memory before cleanup: 432.50 MB
2025-06-30 16:25:18,372 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-30 16:25:18,372 - app.utils.memory_management - INFO - Memory after cleanup: 432.50 MB (freed 0.00 MB)
2025-06-30 16:25:38,801 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:38,830 - app.utils.memory_management - INFO - Memory before cleanup: 432.59 MB
2025-06-30 16:25:39,056 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-06-30 16:25:39,056 - app.utils.memory_management - INFO - Memory after cleanup: 432.59 MB (freed 0.00 MB)
2025-06-30 16:25:43,044 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:43,072 - app.utils.memory_management - INFO - Memory before cleanup: 432.63 MB
2025-06-30 16:25:43,283 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-30 16:25:43,285 - app.utils.memory_management - INFO - Memory after cleanup: 432.63 MB (freed 0.00 MB)
2025-06-30 16:25:51,883 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:51,913 - app.utils.memory_management - INFO - Memory before cleanup: 432.66 MB
2025-06-30 16:25:52,159 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-30 16:25:52,161 - app.utils.memory_management - INFO - Memory after cleanup: 432.66 MB (freed 0.00 MB)
2025-06-30 16:25:54,193 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:25:54,221 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-30 16:25:54,259 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-22 to 2025-06-30
2025-06-30 16:25:54,260 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-30 16:26:01,753 - app.utils.memory_management - INFO - Memory before cleanup: 435.82 MB
2025-06-30 16:26:01,957 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-30 16:26:01,957 - app.utils.memory_management - INFO - Memory after cleanup: 435.82 MB (freed 0.00 MB)
2025-06-30 16:27:56,421 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:27:56,480 - app - INFO - Found 14 stock files in data/stocks
2025-06-30 16:27:56,510 - app.utils.memory_management - INFO - Memory before cleanup: 436.05 MB
2025-06-30 16:27:56,757 - app.utils.memory_management - INFO - Garbage collection: collected 302 objects
2025-06-30 16:27:56,758 - app.utils.memory_management - INFO - Memory after cleanup: 436.05 MB (freed 0.00 MB)
2025-06-30 16:28:01,283 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:28:01,319 - app.utils.memory_management - INFO - Memory before cleanup: 436.06 MB
2025-06-30 16:28:01,539 - app.utils.memory_management - INFO - Garbage collection: collected 189 objects
2025-06-30 16:28:01,539 - app.utils.memory_management - INFO - Memory after cleanup: 436.06 MB (freed 0.00 MB)
2025-06-30 16:28:03,830 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:28:03,878 - app.utils.memory_management - INFO - Memory before cleanup: 436.15 MB
2025-06-30 16:28:04,092 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-30 16:28:04,094 - app.utils.memory_management - INFO - Memory after cleanup: 436.15 MB (freed 0.00 MB)
2025-06-30 16:28:07,681 - app - INFO - Using TensorFlow-based LSTM model
2025-06-30 16:28:14,676 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-30 16:28:14,752 - app.utils.memory_management - INFO - Memory before cleanup: 436.66 MB
2025-06-30 16:28:14,965 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-30 16:28:14,967 - app.utils.memory_management - INFO - Memory after cleanup: 436.66 MB (freed 0.00 MB)
