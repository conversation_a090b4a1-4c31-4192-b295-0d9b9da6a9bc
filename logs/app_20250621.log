2025-06-21 00:35:12,781 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 00:35:15,411 - app - INFO - Memory management utilities loaded
2025-06-21 00:35:15,412 - app - INFO - Error handling utilities loaded
2025-06-21 00:35:15,414 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 00:35:15,416 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 00:35:15,418 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 00:35:15,418 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 00:35:15,418 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 00:35:15,418 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 00:35:15,420 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 00:35:15,420 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 00:35:15,420 - app - INFO - Applied NumPy fix
2025-06-21 00:35:15,421 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 00:35:15,422 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 00:35:15,422 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 00:35:15,422 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 00:35:15,422 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 00:35:15,423 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 00:35:15,423 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 00:35:15,423 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 00:35:32,284 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 00:35:32,285 - app - INFO - Applied TensorFlow fix
2025-06-21 00:35:32,289 - app.config - INFO - Configuration initialized
2025-06-21 00:35:32,297 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 00:35:32,309 - models.train - INFO - TensorFlow test successful
2025-06-21 00:35:36,763 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 00:35:36,763 - models.train - INFO - Transformer model is available
2025-06-21 00:35:36,764 - models.train - INFO - Using TensorFlow-based models
2025-06-21 00:35:36,766 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 00:35:36,767 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 00:35:36,771 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 00:35:38,615 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 00:35:38,616 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 00:35:38,616 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 00:35:38,616 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 00:35:38,616 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 00:35:38,617 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 00:35:38,617 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 00:35:38,617 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 00:35:38,617 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 00:35:38,617 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 00:35:38,872 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 00:35:38,875 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 00:35:39,303 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 00:35:41,809 - app.utils.session_state - INFO - Initializing session state
2025-06-21 00:35:41,810 - app.utils.session_state - INFO - Session state initialized
2025-06-21 00:35:43,074 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 00:35:43,108 - app.utils.memory_management - INFO - Memory before cleanup: 429.37 MB
2025-06-21 00:35:43,326 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 00:35:43,328 - app.utils.memory_management - INFO - Memory after cleanup: 429.37 MB (freed -0.00 MB)
2025-06-21 00:35:51,945 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 00:35:51,980 - app.utils.memory_management - INFO - Memory before cleanup: 432.94 MB
2025-06-21 00:35:52,236 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 00:35:52,238 - app.utils.memory_management - INFO - Memory after cleanup: 432.94 MB (freed 0.00 MB)
2025-06-21 00:35:54,769 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 00:35:54,805 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/ABUK.csv
2025-06-21 00:35:54,838 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-07 to 2025-06-13
2025-06-21 00:35:54,840 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 00:35:55,244 - app.utils.memory_management - INFO - Memory before cleanup: 436.12 MB
2025-06-21 00:35:55,472 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 00:35:55,473 - app.utils.memory_management - INFO - Memory after cleanup: 436.14 MB (freed -0.02 MB)
2025-06-21 00:53:08,701 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 00:53:08,712 - app.utils.session_state - INFO - Initializing session state
2025-06-21 00:53:08,718 - app.utils.session_state - INFO - Session state initialized
2025-06-21 00:53:08,782 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 00:53:08,809 - app.utils.memory_management - INFO - Memory before cleanup: 436.79 MB
2025-06-21 00:53:09,023 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-21 00:53:09,023 - app.utils.memory_management - INFO - Memory after cleanup: 436.79 MB (freed 0.00 MB)
2025-06-21 00:53:14,802 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 00:53:14,836 - app.utils.memory_management - INFO - Memory before cleanup: 436.96 MB
2025-06-21 00:53:15,117 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-21 00:53:15,118 - app.utils.memory_management - INFO - Memory after cleanup: 436.96 MB (freed 0.00 MB)
2025-06-21 00:53:20,447 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 00:53:20,525 - app.utils.memory_management - INFO - Memory before cleanup: 436.96 MB
2025-06-21 00:53:20,838 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 00:53:20,838 - app.utils.memory_management - INFO - Memory after cleanup: 436.96 MB (freed 0.00 MB)
2025-06-21 00:53:21,845 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 00:53:21,879 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 00:53:21,905 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 00:53:21,907 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 00:53:22,204 - app.utils.memory_management - INFO - Memory before cleanup: 437.53 MB
2025-06-21 00:53:22,392 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 00:53:22,393 - app.utils.memory_management - INFO - Memory after cleanup: 437.51 MB (freed 0.02 MB)
2025-06-21 00:57:19,716 - app - INFO - Cleaning up resources...
2025-06-21 00:57:19,716 - app.utils.memory_management - INFO - Memory before cleanup: 437.26 MB
2025-06-21 00:57:19,946 - app.utils.memory_management - INFO - Garbage collection: collected 394 objects
2025-06-21 00:57:19,948 - app.utils.memory_management - INFO - Memory after cleanup: 437.26 MB (freed 0.00 MB)
2025-06-21 00:57:19,950 - app - INFO - Application shutdown complete
2025-06-21 01:03:01,566 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:03:03,277 - app - INFO - Memory management utilities loaded
2025-06-21 01:03:03,284 - app - INFO - Error handling utilities loaded
2025-06-21 01:03:03,287 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:03:03,290 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:03:03,291 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:03:03,292 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:03:03,293 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:03:03,295 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:03:03,297 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:03:03,299 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:03:03,301 - app - INFO - Applied NumPy fix
2025-06-21 01:03:03,304 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:03:03,305 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:03:03,308 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:03:03,310 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:03:03,313 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:03:03,315 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:03:03,318 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:03:03,320 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:03:09,237 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:03:09,237 - app - INFO - Applied TensorFlow fix
2025-06-21 01:03:09,241 - app.config - INFO - Configuration initialized
2025-06-21 01:03:09,247 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 01:03:09,263 - models.train - INFO - TensorFlow test successful
2025-06-21 01:03:10,038 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 01:03:10,038 - models.train - INFO - Transformer model is available
2025-06-21 01:03:10,039 - models.train - INFO - Using TensorFlow-based models
2025-06-21 01:03:10,041 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 01:03:10,042 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 01:03:10,045 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 01:03:10,440 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:03:10,440 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:03:10,440 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:03:10,440 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:03:10,441 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:03:10,441 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 01:03:10,441 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 01:03:10,441 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:03:10,441 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:03:10,441 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:03:10,580 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 01:03:10,584 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:03:11,007 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 01:03:12,029 - app.utils.session_state - INFO - Initializing session state
2025-06-21 01:03:12,030 - app.utils.session_state - INFO - Session state initialized
2025-06-21 01:03:13,356 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 01:03:13,380 - app.utils.memory_management - INFO - Memory before cleanup: 429.80 MB
2025-06-21 01:03:13,591 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:03:13,592 - app.utils.memory_management - INFO - Memory after cleanup: 429.80 MB (freed -0.01 MB)
2025-06-21 01:03:15,433 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:03:15,464 - app.utils.memory_management - INFO - Memory before cleanup: 432.95 MB
2025-06-21 01:03:15,678 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:03:15,680 - app.utils.memory_management - INFO - Memory after cleanup: 432.95 MB (freed -0.00 MB)
2025-06-21 01:03:19,184 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:03:19,215 - app.utils.memory_management - INFO - Memory before cleanup: 433.11 MB
2025-06-21 01:03:19,455 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:03:19,456 - app.utils.memory_management - INFO - Memory after cleanup: 433.15 MB (freed -0.04 MB)
2025-06-21 01:03:24,581 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:03:24,625 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 01:03:24,645 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 01:03:24,649 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:03:24,649 - app.pages.smc_analysis - ERROR - Error running SMC analysis: 'SMCAnalysisEngine' object has no attribute 'analyze'
2025-06-21 01:03:24,651 - app.utils.memory_management - INFO - Memory before cleanup: 434.87 MB
2025-06-21 01:03:24,911 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:03:24,911 - app.utils.memory_management - INFO - Memory after cleanup: 434.87 MB (freed 0.00 MB)
2025-06-21 01:06:24,344 - app - INFO - Cleaning up resources...
2025-06-21 01:06:24,345 - app.utils.memory_management - INFO - Memory before cleanup: 434.48 MB
2025-06-21 01:06:24,495 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-21 01:06:24,495 - app.utils.memory_management - INFO - Memory after cleanup: 434.46 MB (freed 0.02 MB)
2025-06-21 01:06:24,495 - app - INFO - Application shutdown complete
2025-06-21 01:06:54,868 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:06:57,478 - app - INFO - Memory management utilities loaded
2025-06-21 01:06:57,482 - app - INFO - Error handling utilities loaded
2025-06-21 01:06:57,486 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:06:57,489 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:06:57,492 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:06:57,494 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:06:57,495 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:06:57,497 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:06:57,497 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:06:57,498 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:06:57,500 - app - INFO - Applied NumPy fix
2025-06-21 01:06:57,505 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:06:57,505 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:06:57,509 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:06:57,511 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:06:57,511 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:06:57,514 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:06:57,514 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:06:57,518 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:07:04,085 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:07:04,085 - app - INFO - Applied TensorFlow fix
2025-06-21 01:07:04,089 - app.config - INFO - Configuration initialized
2025-06-21 01:07:04,094 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 01:07:04,106 - models.train - INFO - TensorFlow test successful
2025-06-21 01:07:04,813 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 01:07:04,814 - models.train - INFO - Transformer model is available
2025-06-21 01:07:04,815 - models.train - INFO - Using TensorFlow-based models
2025-06-21 01:07:04,818 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 01:07:04,818 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 01:07:04,822 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 01:07:05,193 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:07:05,193 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:07:05,193 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:07:05,193 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:07:05,194 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:07:05,194 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 01:07:05,194 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 01:07:05,194 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:07:05,194 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:07:05,194 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:07:05,302 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 01:07:05,305 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:07:05,698 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 01:07:06,587 - app.utils.session_state - INFO - Initializing session state
2025-06-21 01:07:06,589 - app.utils.session_state - INFO - Session state initialized
2025-06-21 01:07:07,893 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 01:07:07,914 - app.utils.memory_management - INFO - Memory before cleanup: 429.65 MB
2025-06-21 01:07:08,123 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:07:08,125 - app.utils.memory_management - INFO - Memory after cleanup: 429.66 MB (freed -0.01 MB)
2025-06-21 01:07:16,380 - app - INFO - Cleaning up resources...
2025-06-21 01:07:16,380 - app.utils.memory_management - INFO - Memory before cleanup: 432.64 MB
2025-06-21 01:07:16,536 - app.utils.memory_management - INFO - Garbage collection: collected 84 objects
2025-06-21 01:07:16,538 - app.utils.memory_management - INFO - Memory after cleanup: 432.64 MB (freed 0.00 MB)
2025-06-21 01:07:16,538 - app - INFO - Application shutdown complete
2025-06-21 01:13:30,278 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:13:30,946 - app - INFO - Memory management utilities loaded
2025-06-21 01:13:30,946 - app - INFO - Error handling utilities loaded
2025-06-21 01:13:30,946 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:13:30,946 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:13:30,946 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:13:30,952 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:13:30,953 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:13:30,953 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:13:30,955 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:13:30,955 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:13:30,956 - app - INFO - Applied NumPy fix
2025-06-21 01:13:30,957 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:13:30,957 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:13:30,958 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:13:30,959 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:13:30,960 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:13:30,962 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:13:30,963 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:13:30,963 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:13:35,667 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:13:35,667 - app - INFO - Applied TensorFlow fix
2025-06-21 01:13:35,703 - app - INFO - Cleaning up resources...
2025-06-21 01:13:35,706 - app.utils.memory_management - INFO - Memory before cleanup: 310.16 MB
2025-06-21 01:13:35,814 - app.utils.memory_management - INFO - Garbage collection: collected 43 objects
2025-06-21 01:13:35,814 - app.utils.memory_management - INFO - Memory after cleanup: 310.52 MB (freed -0.36 MB)
2025-06-21 01:13:35,814 - app - INFO - Application shutdown complete
2025-06-21 01:19:06,835 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:19:08,809 - app - INFO - Memory management utilities loaded
2025-06-21 01:19:08,811 - app - INFO - Error handling utilities loaded
2025-06-21 01:19:08,812 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:19:08,816 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:19:08,816 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:19:08,816 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:19:08,817 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:19:08,817 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:19:08,818 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:19:08,818 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:19:08,818 - app - INFO - Applied NumPy fix
2025-06-21 01:19:08,819 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:19:08,820 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:19:08,820 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:19:08,820 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:19:08,820 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:19:08,820 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:19:08,821 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:19:08,821 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:19:14,635 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:19:14,635 - app - INFO - Applied TensorFlow fix
2025-06-21 01:19:14,639 - app.config - INFO - Configuration initialized
2025-06-21 01:19:14,645 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 01:19:14,655 - models.train - INFO - TensorFlow test successful
2025-06-21 01:19:15,257 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 01:19:15,257 - models.train - INFO - Transformer model is available
2025-06-21 01:19:15,257 - models.train - INFO - Using TensorFlow-based models
2025-06-21 01:19:15,259 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 01:19:15,259 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 01:19:15,263 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 01:19:15,662 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:19:15,674 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:19:15,674 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:19:15,674 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:19:15,674 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:19:15,674 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 01:19:15,674 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 01:19:15,674 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:19:15,674 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:19:15,674 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:19:15,800 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 01:19:15,802 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:19:16,220 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 01:19:17,000 - app.utils.session_state - INFO - Initializing session state
2025-06-21 01:19:17,002 - app.utils.session_state - INFO - Session state initialized
2025-06-21 01:19:18,277 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 01:19:18,291 - app.utils.memory_management - INFO - Memory before cleanup: 428.76 MB
2025-06-21 01:19:18,465 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:19:18,467 - app.utils.memory_management - INFO - Memory after cleanup: 428.77 MB (freed -0.00 MB)
2025-06-21 01:19:22,613 - app - INFO - Cleaning up resources...
2025-06-21 01:19:22,615 - app.utils.memory_management - INFO - Memory before cleanup: 432.82 MB
2025-06-21 01:19:22,774 - app.utils.memory_management - INFO - Garbage collection: collected 84 objects
2025-06-21 01:19:22,774 - app.utils.memory_management - INFO - Memory after cleanup: 432.82 MB (freed 0.00 MB)
2025-06-21 01:19:22,774 - app - INFO - Application shutdown complete
2025-06-21 01:20:01,950 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:20:03,884 - app - INFO - Memory management utilities loaded
2025-06-21 01:20:03,886 - app - INFO - Error handling utilities loaded
2025-06-21 01:20:03,888 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:20:03,890 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:20:03,890 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:20:03,892 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:20:03,892 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:20:03,893 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:20:03,893 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:20:03,894 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:20:03,894 - app - INFO - Applied NumPy fix
2025-06-21 01:20:03,896 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:20:03,897 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:20:03,898 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:20:03,898 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:20:03,898 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:20:03,899 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:20:03,901 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:20:03,901 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:20:10,049 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:20:10,050 - app - INFO - Applied TensorFlow fix
2025-06-21 01:20:10,053 - app.config - INFO - Configuration initialized
2025-06-21 01:20:10,059 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 01:20:10,067 - models.train - INFO - TensorFlow test successful
2025-06-21 01:20:10,611 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 01:20:10,611 - models.train - INFO - Transformer model is available
2025-06-21 01:20:10,611 - models.train - INFO - Using TensorFlow-based models
2025-06-21 01:20:10,611 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 01:20:10,611 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 01:20:10,615 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 01:20:10,947 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:20:10,949 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:20:10,951 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:20:10,951 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:20:10,951 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:20:10,953 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 01:20:10,953 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 01:20:10,953 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:20:10,955 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:20:10,956 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:20:11,066 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 01:20:11,069 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:20:11,465 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 01:20:12,302 - app.utils.session_state - INFO - Initializing session state
2025-06-21 01:20:12,302 - app.utils.session_state - INFO - Session state initialized
2025-06-21 01:20:13,754 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 01:20:13,797 - app.utils.memory_management - INFO - Memory before cleanup: 429.51 MB
2025-06-21 01:20:14,045 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:20:14,045 - app.utils.memory_management - INFO - Memory after cleanup: 429.51 MB (freed -0.00 MB)
2025-06-21 01:20:15,737 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:20:15,768 - app.utils.memory_management - INFO - Memory before cleanup: 433.57 MB
2025-06-21 01:20:15,965 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:20:15,968 - app.utils.memory_management - INFO - Memory after cleanup: 433.57 MB (freed 0.00 MB)
2025-06-21 01:20:18,874 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:20:18,922 - app.utils.memory_management - INFO - Memory before cleanup: 433.84 MB
2025-06-21 01:20:19,211 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:20:19,213 - app.utils.memory_management - INFO - Memory after cleanup: 433.88 MB (freed -0.04 MB)
2025-06-21 01:20:20,361 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:20:20,408 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 01:20:20,425 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 01:20:20,428 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:20:21,348 - app.utils.memory_management - INFO - Memory before cleanup: 436.20 MB
2025-06-21 01:20:21,594 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:20:21,594 - app.utils.memory_management - INFO - Memory after cleanup: 436.20 MB (freed 0.00 MB)
2025-06-21 01:28:42,621 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:28:42,643 - app - INFO - Memory management utilities loaded
2025-06-21 01:28:42,649 - app - INFO - Error handling utilities loaded
2025-06-21 01:28:42,658 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:28:42,661 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:28:42,664 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:28:42,670 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:30:21,017 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:30:22,658 - app - INFO - Memory management utilities loaded
2025-06-21 01:30:22,660 - app - INFO - Error handling utilities loaded
2025-06-21 01:30:22,660 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:30:22,661 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:30:22,661 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:30:22,661 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:30:22,664 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:30:22,665 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:30:22,666 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:30:22,666 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:30:22,666 - app - INFO - Applied NumPy fix
2025-06-21 01:30:22,668 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:30:22,668 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:30:22,668 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:30:22,668 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:30:22,668 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:30:22,669 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:30:22,669 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:30:22,669 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:30:27,392 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:30:27,392 - app - INFO - Applied TensorFlow fix
2025-06-21 01:30:28,168 - app - INFO - Cleaning up resources...
2025-06-21 01:30:28,173 - app.utils.memory_management - INFO - Memory before cleanup: 321.22 MB
2025-06-21 01:30:28,310 - app.utils.memory_management - INFO - Garbage collection: collected 86 objects
2025-06-21 01:30:28,310 - app.utils.memory_management - INFO - Memory after cleanup: 321.56 MB (freed -0.34 MB)
2025-06-21 01:30:28,312 - app - INFO - Application shutdown complete
2025-06-21 01:32:03,459 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:32:04,115 - app - INFO - Memory management utilities loaded
2025-06-21 01:32:04,120 - app - INFO - Error handling utilities loaded
2025-06-21 01:32:04,122 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:32:04,124 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:32:04,126 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:32:04,127 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:32:04,127 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:32:04,140 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:32:04,141 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:32:04,141 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:32:04,141 - app - INFO - Applied NumPy fix
2025-06-21 01:32:04,143 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:32:04,143 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:32:04,144 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:32:04,144 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:32:04,144 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:32:04,151 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:32:04,156 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:32:04,156 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:32:08,514 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:32:08,514 - app - INFO - Applied TensorFlow fix
2025-06-21 01:32:09,038 - app - INFO - Cleaning up resources...
2025-06-21 01:32:09,042 - app.utils.memory_management - INFO - Memory before cleanup: 320.39 MB
2025-06-21 01:32:09,157 - app.utils.memory_management - INFO - Garbage collection: collected 676 objects
2025-06-21 01:32:09,157 - app.utils.memory_management - INFO - Memory after cleanup: 320.71 MB (freed -0.32 MB)
2025-06-21 01:32:09,157 - app - INFO - Application shutdown complete
2025-06-21 01:32:34,970 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:32:36,945 - app - INFO - Memory management utilities loaded
2025-06-21 01:32:36,947 - app - INFO - Error handling utilities loaded
2025-06-21 01:32:36,951 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:32:36,955 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:32:36,958 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:32:36,961 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:32:36,963 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:32:36,964 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:32:36,967 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:32:36,969 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:32:36,971 - app - INFO - Applied NumPy fix
2025-06-21 01:32:36,974 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:32:36,976 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:32:36,978 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:32:36,980 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:32:36,983 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:32:36,986 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:32:36,988 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:32:36,990 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:32:41,993 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:32:41,994 - app - INFO - Applied TensorFlow fix
2025-06-21 01:32:41,996 - app.config - INFO - Configuration initialized
2025-06-21 01:32:42,002 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 01:32:42,012 - models.train - INFO - TensorFlow test successful
2025-06-21 01:32:42,571 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 01:32:42,572 - models.train - INFO - Transformer model is available
2025-06-21 01:32:42,573 - models.train - INFO - Using TensorFlow-based models
2025-06-21 01:32:42,575 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 01:32:42,575 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 01:32:42,578 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 01:32:43,000 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:32:43,000 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:32:43,000 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:32:43,000 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:32:43,000 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:32:43,001 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 01:32:43,001 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 01:32:43,001 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:32:43,001 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:32:43,001 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:32:43,107 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 01:32:43,109 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:32:43,477 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 01:32:44,285 - app.utils.session_state - INFO - Initializing session state
2025-06-21 01:32:44,286 - app.utils.session_state - INFO - Session state initialized
2025-06-21 01:32:45,450 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 01:32:45,465 - app.utils.memory_management - INFO - Memory before cleanup: 428.54 MB
2025-06-21 01:32:45,677 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:32:45,679 - app.utils.memory_management - INFO - Memory after cleanup: 428.55 MB (freed -0.01 MB)
2025-06-21 01:32:47,679 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:32:47,708 - app.utils.memory_management - INFO - Memory before cleanup: 432.45 MB
2025-06-21 01:32:47,925 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:32:47,926 - app.utils.memory_management - INFO - Memory after cleanup: 432.45 MB (freed 0.00 MB)
2025-06-21 01:32:51,245 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:32:51,274 - app.utils.memory_management - INFO - Memory before cleanup: 432.59 MB
2025-06-21 01:32:51,477 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:32:51,477 - app.utils.memory_management - INFO - Memory after cleanup: 432.62 MB (freed -0.04 MB)
2025-06-21 01:32:52,688 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:32:52,718 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 01:32:52,733 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 01:32:52,734 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:32:53,868 - app.utils.memory_management - INFO - Memory before cleanup: 435.27 MB
2025-06-21 01:32:54,058 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:32:54,060 - app.utils.memory_management - INFO - Memory after cleanup: 435.27 MB (freed 0.00 MB)
2025-06-21 01:34:49,599 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:34:49,631 - app.utils.memory_management - INFO - Memory before cleanup: 435.34 MB
2025-06-21 01:34:49,859 - app.utils.memory_management - INFO - Garbage collection: collected 303 objects
2025-06-21 01:34:49,859 - app.utils.memory_management - INFO - Memory after cleanup: 435.34 MB (freed 0.00 MB)
2025-06-21 01:34:52,482 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:34:52,529 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/ABUK.csv
2025-06-21 01:34:52,535 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-07 to 2025-06-13
2025-06-21 01:34:52,535 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:34:53,645 - app.utils.memory_management - INFO - Memory before cleanup: 435.68 MB
2025-06-21 01:34:53,827 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:34:53,827 - app.utils.memory_management - INFO - Memory after cleanup: 435.68 MB (freed 0.00 MB)
2025-06-21 01:35:25,714 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:35:25,754 - app.utils.memory_management - INFO - Memory before cleanup: 435.68 MB
2025-06-21 01:35:26,012 - app.utils.memory_management - INFO - Garbage collection: collected 299 objects
2025-06-21 01:35:26,012 - app.utils.memory_management - INFO - Memory after cleanup: 435.68 MB (freed 0.00 MB)
2025-06-21 01:35:28,835 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:35:28,867 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/DOMT.csv
2025-06-21 01:35:28,900 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-07 to 2025-06-13
2025-06-21 01:35:28,900 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:35:30,162 - app.utils.memory_management - INFO - Memory before cleanup: 435.73 MB
2025-06-21 01:35:30,351 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:35:30,351 - app.utils.memory_management - INFO - Memory after cleanup: 435.73 MB (freed 0.00 MB)
2025-06-21 01:35:52,655 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:35:52,681 - app.utils.memory_management - INFO - Memory before cleanup: 435.72 MB
2025-06-21 01:35:52,873 - app.utils.memory_management - INFO - Garbage collection: collected 299 objects
2025-06-21 01:35:52,875 - app.utils.memory_management - INFO - Memory after cleanup: 435.72 MB (freed 0.00 MB)
2025-06-21 01:35:54,659 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:35:54,695 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/TMGH.csv
2025-06-21 01:35:54,725 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-07 to 2025-06-13
2025-06-21 01:35:54,726 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:35:55,814 - app.utils.memory_management - INFO - Memory before cleanup: 435.75 MB
2025-06-21 01:35:55,994 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:35:55,997 - app.utils.memory_management - INFO - Memory after cleanup: 435.75 MB (freed 0.00 MB)
2025-06-21 01:36:46,582 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:36:46,617 - app.utils.memory_management - INFO - Memory before cleanup: 435.76 MB
2025-06-21 01:36:46,839 - app.utils.memory_management - INFO - Garbage collection: collected 299 objects
2025-06-21 01:36:46,839 - app.utils.memory_management - INFO - Memory after cleanup: 435.76 MB (freed 0.00 MB)
2025-06-21 01:36:48,580 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:36:48,625 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/SWDY.csv
2025-06-21 01:36:48,672 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-12 to 2025-06-11
2025-06-21 01:36:48,673 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:36:49,871 - app.utils.memory_management - INFO - Memory before cleanup: 436.00 MB
2025-06-21 01:36:50,062 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:36:50,063 - app.utils.memory_management - INFO - Memory after cleanup: 436.00 MB (freed 0.00 MB)
2025-06-21 01:44:23,302 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:44:23,310 - app - INFO - Memory management utilities loaded
2025-06-21 01:44:23,312 - app - INFO - Error handling utilities loaded
2025-06-21 01:44:23,315 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:44:23,317 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:44:23,319 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:44:23,321 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:48:42,743 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:48:44,261 - app - INFO - Memory management utilities loaded
2025-06-21 01:48:44,263 - app - INFO - Error handling utilities loaded
2025-06-21 01:48:44,265 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:48:44,271 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:48:44,273 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:48:44,275 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:48:44,275 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:48:44,276 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:48:44,276 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:48:44,277 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:48:44,277 - app - INFO - Applied NumPy fix
2025-06-21 01:48:44,281 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:48:44,282 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:48:44,284 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:48:44,286 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:48:44,288 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:48:44,288 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:48:44,288 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:48:44,288 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:48:48,841 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:48:48,841 - app - INFO - Applied TensorFlow fix
2025-06-21 01:49:39,158 - app - INFO - Cleaning up resources...
2025-06-21 01:49:39,162 - app.utils.memory_management - INFO - Memory before cleanup: 323.24 MB
2025-06-21 01:49:39,305 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-21 01:49:39,305 - app.utils.memory_management - INFO - Memory after cleanup: 323.54 MB (freed -0.29 MB)
2025-06-21 01:49:39,305 - app - INFO - Application shutdown complete
2025-06-21 01:50:14,040 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:50:15,519 - app - INFO - Memory management utilities loaded
2025-06-21 01:50:15,521 - app - INFO - Error handling utilities loaded
2025-06-21 01:50:15,523 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:50:15,526 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:50:15,527 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:50:15,528 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:50:15,531 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:50:15,531 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:50:15,534 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:50:15,536 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:50:15,537 - app - INFO - Applied NumPy fix
2025-06-21 01:50:15,540 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:50:15,542 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:50:15,544 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:50:15,544 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:50:15,546 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:50:15,548 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:50:15,550 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:50:15,552 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:50:22,064 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:50:22,066 - app - INFO - Applied TensorFlow fix
2025-06-21 01:50:22,068 - app.config - INFO - Configuration initialized
2025-06-21 01:50:22,074 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 01:50:22,082 - models.train - INFO - TensorFlow test successful
2025-06-21 01:50:22,687 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 01:50:22,688 - models.train - INFO - Transformer model is available
2025-06-21 01:50:22,688 - models.train - INFO - Using TensorFlow-based models
2025-06-21 01:50:22,691 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 01:50:22,691 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 01:50:22,695 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 01:50:23,146 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:50:23,146 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:50:23,146 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:50:23,146 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:50:23,146 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:50:23,146 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 01:50:23,148 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 01:50:23,148 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:50:23,148 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:50:23,148 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:50:23,270 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 01:50:23,273 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:50:23,724 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 01:50:24,921 - app.utils.session_state - INFO - Initializing session state
2025-06-21 01:50:24,926 - app.utils.session_state - INFO - Session state initialized
2025-06-21 01:50:26,229 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 01:50:26,248 - app.utils.memory_management - INFO - Memory before cleanup: 428.87 MB
2025-06-21 01:50:26,426 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:50:26,428 - app.utils.memory_management - INFO - Memory after cleanup: 428.88 MB (freed -0.01 MB)
2025-06-21 01:50:37,642 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:50:37,667 - app.utils.memory_management - INFO - Memory before cleanup: 432.98 MB
2025-06-21 01:50:37,865 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:50:37,867 - app.utils.memory_management - INFO - Memory after cleanup: 432.98 MB (freed -0.00 MB)
2025-06-21 01:50:41,102 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:50:41,146 - app.utils.memory_management - INFO - Memory before cleanup: 433.00 MB
2025-06-21 01:50:41,430 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:50:41,432 - app.utils.memory_management - INFO - Memory after cleanup: 433.04 MB (freed -0.04 MB)
2025-06-21 01:50:42,286 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:50:42,334 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 01:50:42,351 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 01:50:42,352 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:50:53,688 - app.utils.memory_management - INFO - Memory before cleanup: 435.83 MB
2025-06-21 01:50:53,875 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:50:53,878 - app.utils.memory_management - INFO - Memory after cleanup: 435.83 MB (freed 0.00 MB)
2025-06-21 01:51:22,146 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:51:22,199 - app.utils.memory_management - INFO - Memory before cleanup: 436.61 MB
2025-06-21 01:51:22,430 - app.utils.memory_management - INFO - Garbage collection: collected 303 objects
2025-06-21 01:51:22,432 - app.utils.memory_management - INFO - Memory after cleanup: 436.61 MB (freed 0.00 MB)
2025-06-21 01:51:24,178 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:51:24,210 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/SWDY.csv
2025-06-21 01:51:24,221 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-12 to 2025-06-11
2025-06-21 01:51:24,221 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:51:37,499 - app.utils.memory_management - INFO - Memory before cleanup: 437.93 MB
2025-06-21 01:51:37,674 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:51:37,676 - app.utils.memory_management - INFO - Memory after cleanup: 437.93 MB (freed 0.00 MB)
2025-06-21 01:56:02,305 - app - INFO - Cleaning up resources...
2025-06-21 01:56:02,305 - app.utils.memory_management - INFO - Memory before cleanup: 437.77 MB
2025-06-21 01:56:02,506 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 01:56:02,507 - app.utils.memory_management - INFO - Memory after cleanup: 437.77 MB (freed 0.00 MB)
2025-06-21 01:56:02,507 - app - INFO - Application shutdown complete
2025-06-21 01:58:04,707 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 01:58:06,410 - app - INFO - Memory management utilities loaded
2025-06-21 01:58:06,413 - app - INFO - Error handling utilities loaded
2025-06-21 01:58:06,415 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 01:58:06,419 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 01:58:06,423 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 01:58:06,424 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 01:58:06,427 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 01:58:06,429 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 01:58:06,429 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 01:58:06,433 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 01:58:06,434 - app - INFO - Applied NumPy fix
2025-06-21 01:58:06,439 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:58:06,440 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:58:06,442 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:58:06,443 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 01:58:06,444 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:58:06,445 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:58:06,446 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:58:06,447 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 01:58:11,919 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 01:58:11,919 - app - INFO - Applied TensorFlow fix
2025-06-21 01:58:11,922 - app.config - INFO - Configuration initialized
2025-06-21 01:58:11,928 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 01:58:11,938 - models.train - INFO - TensorFlow test successful
2025-06-21 01:58:12,537 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 01:58:12,537 - models.train - INFO - Transformer model is available
2025-06-21 01:58:12,537 - models.train - INFO - Using TensorFlow-based models
2025-06-21 01:58:12,537 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 01:58:12,537 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 01:58:12,537 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 01:58:12,901 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:58:12,901 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 01:58:12,901 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 01:58:12,901 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 01:58:12,901 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 01:58:12,901 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 01:58:12,901 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 01:58:12,901 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 01:58:12,901 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 01:58:12,901 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 01:58:13,016 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 01:58:13,019 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:58:13,386 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 01:58:14,171 - app.utils.session_state - INFO - Initializing session state
2025-06-21 01:58:14,171 - app.utils.session_state - INFO - Session state initialized
2025-06-21 01:58:15,372 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 01:58:15,385 - app.utils.memory_management - INFO - Memory before cleanup: 430.11 MB
2025-06-21 01:58:15,559 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:58:15,559 - app.utils.memory_management - INFO - Memory after cleanup: 430.12 MB (freed -0.01 MB)
2025-06-21 01:58:26,738 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:58:26,797 - app.utils.memory_management - INFO - Memory before cleanup: 433.70 MB
2025-06-21 01:58:27,150 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 01:58:27,177 - app.utils.memory_management - INFO - Memory after cleanup: 433.70 MB (freed 0.00 MB)
2025-06-21 01:58:30,036 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:58:30,074 - app.utils.memory_management - INFO - Memory before cleanup: 433.77 MB
2025-06-21 01:58:30,314 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:58:30,316 - app.utils.memory_management - INFO - Memory after cleanup: 433.81 MB (freed -0.04 MB)
2025-06-21 01:58:32,589 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 01:58:32,624 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 01:58:32,640 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 01:58:32,641 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 01:58:54,947 - app.components.smc_analysis_engine - ERROR - Error generating summary: 'SMCSignal' object has no attribute 'get'
2025-06-21 01:58:55,417 - app.components.smc_analysis_engine - ERROR - Error generating summary: 'SMCSignal' object has no attribute 'get'
2025-06-21 01:58:55,543 - app.utils.memory_management - INFO - Memory before cleanup: 436.58 MB
2025-06-21 01:58:55,731 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 01:58:55,732 - app.utils.memory_management - INFO - Memory after cleanup: 436.59 MB (freed -0.02 MB)
2025-06-21 01:59:11,804 - app - INFO - Cleaning up resources...
2025-06-21 01:59:11,804 - app.utils.memory_management - INFO - Memory before cleanup: 437.00 MB
2025-06-21 01:59:11,958 - app.utils.memory_management - INFO - Garbage collection: collected 374 objects
2025-06-21 01:59:11,958 - app.utils.memory_management - INFO - Memory after cleanup: 437.00 MB (freed 0.00 MB)
2025-06-21 01:59:11,958 - app - INFO - Application shutdown complete
2025-06-21 02:01:03,420 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 02:01:04,951 - app - INFO - Memory management utilities loaded
2025-06-21 02:01:04,953 - app - INFO - Error handling utilities loaded
2025-06-21 02:01:04,953 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 02:01:04,953 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 02:01:04,953 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 02:01:04,953 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 02:01:04,953 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 02:01:04,953 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 02:01:04,953 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 02:01:04,959 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 02:01:04,960 - app - INFO - Applied NumPy fix
2025-06-21 02:01:04,962 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 02:01:04,963 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 02:01:04,963 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 02:01:04,963 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 02:01:04,964 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 02:01:04,964 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 02:01:04,964 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 02:01:04,964 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 02:01:09,519 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 02:01:09,519 - app - INFO - Applied TensorFlow fix
2025-06-21 02:01:09,530 - app - INFO - Cleaning up resources...
2025-06-21 02:01:09,535 - app.utils.memory_management - INFO - Memory before cleanup: 309.05 MB
2025-06-21 02:01:09,647 - app.utils.memory_management - INFO - Garbage collection: collected 43 objects
2025-06-21 02:01:09,649 - app.utils.memory_management - INFO - Memory after cleanup: 309.39 MB (freed -0.34 MB)
2025-06-21 02:01:09,649 - app - INFO - Application shutdown complete
2025-06-21 12:49:12,229 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 12:49:15,542 - app - INFO - Memory management utilities loaded
2025-06-21 12:49:15,552 - app - INFO - Error handling utilities loaded
2025-06-21 12:49:15,558 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 12:49:15,560 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 12:49:15,560 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 12:49:15,560 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 12:49:15,571 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 12:49:15,573 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 12:49:15,573 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 12:49:15,574 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 12:49:15,575 - app - INFO - Applied NumPy fix
2025-06-21 12:49:15,582 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 12:49:15,582 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 12:49:15,583 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 12:49:15,583 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 12:49:15,584 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 12:49:15,584 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 12:49:15,584 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 12:49:15,584 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 12:49:33,527 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 12:49:33,528 - app - INFO - Applied TensorFlow fix
2025-06-21 12:49:33,550 - app.config - INFO - Configuration initialized
2025-06-21 12:49:33,578 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 12:49:33,600 - models.train - INFO - TensorFlow test successful
2025-06-21 12:49:35,496 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 12:49:35,496 - models.train - INFO - Transformer model is available
2025-06-21 12:49:35,496 - models.train - INFO - Using TensorFlow-based models
2025-06-21 12:49:35,510 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 12:49:35,510 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 12:49:35,530 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 12:49:36,305 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 12:49:36,305 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 12:49:36,305 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 12:49:36,305 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 12:49:36,305 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 12:49:36,305 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 12:49:36,305 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 12:49:36,305 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 12:49:36,305 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 12:49:36,307 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 12:49:36,554 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 12:49:36,562 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:49:37,412 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 12:49:39,507 - app.utils.session_state - INFO - Initializing session state
2025-06-21 12:49:39,509 - app.utils.session_state - INFO - Session state initialized
2025-06-21 12:49:40,858 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 12:49:40,891 - app.utils.memory_management - INFO - Memory before cleanup: 429.96 MB
2025-06-21 12:49:41,130 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 12:49:41,131 - app.utils.memory_management - INFO - Memory after cleanup: 429.96 MB (freed -0.01 MB)
2025-06-21 12:49:49,210 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:49:49,255 - app.utils.memory_management - INFO - Memory before cleanup: 433.44 MB
2025-06-21 12:49:49,482 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 12:49:49,490 - app.utils.memory_management - INFO - Memory after cleanup: 433.44 MB (freed 0.00 MB)
2025-06-21 12:49:50,649 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:49:50,710 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-21 12:49:50,713 - app - INFO - Date range: 2022-08-05 to 2025-06-19
2025-06-21 12:49:50,714 - app - INFO - Data shape: (750, 6)
2025-06-21 12:49:50,715 - app - INFO - File COMI contains 2025 data
2025-06-21 12:49:50,765 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-21 12:49:50,767 - app - INFO - Features shape: (750, 36)
2025-06-21 12:49:50,800 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-21 12:49:50,827 - app - INFO - Date range: 2022-08-05 to 2025-06-19
2025-06-21 12:49:50,833 - app - INFO - Data shape: (750, 6)
2025-06-21 12:49:50,837 - app - INFO - File COMI contains 2025 data
2025-06-21 12:49:50,845 - app.utils.memory_management - INFO - Memory before cleanup: 437.38 MB
2025-06-21 12:49:51,082 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-06-21 12:49:51,082 - app.utils.memory_management - INFO - Memory after cleanup: 437.42 MB (freed -0.04 MB)
2025-06-21 12:49:51,254 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:49:51,342 - app.utils.memory_management - INFO - Memory before cleanup: 438.38 MB
2025-06-21 12:49:51,531 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-06-21 12:49:51,531 - app.utils.memory_management - INFO - Memory after cleanup: 438.38 MB (freed 0.00 MB)
2025-06-21 12:49:59,663 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:49:59,717 - app.utils.memory_management - INFO - Memory before cleanup: 439.06 MB
2025-06-21 12:49:59,919 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-21 12:49:59,923 - app.utils.memory_management - INFO - Memory after cleanup: 439.06 MB (freed 0.00 MB)
2025-06-21 12:50:07,536 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:50:07,572 - app.utils.memory_management - INFO - Memory before cleanup: 439.04 MB
2025-06-21 12:50:07,819 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 12:50:07,819 - app.utils.memory_management - INFO - Memory after cleanup: 439.04 MB (freed 0.00 MB)
2025-06-21 12:50:12,302 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:50:12,337 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 12:50:12,346 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 12:50:12,348 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 12:50:25,342 - app.components.smc_analysis_engine - ERROR - Error generating summary: 'SMCSignal' object has no attribute 'get'
2025-06-21 12:50:25,860 - app.components.smc_analysis_engine - ERROR - Error generating summary: 'SMCSignal' object has no attribute 'get'
2025-06-21 12:50:26,030 - app.utils.memory_management - INFO - Memory before cleanup: 433.80 MB
2025-06-21 12:50:26,208 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 12:50:26,210 - app.utils.memory_management - INFO - Memory after cleanup: 433.80 MB (freed -0.00 MB)
2025-06-21 12:57:46,332 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:57:46,381 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 12:57:46,423 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 7 days
2025-06-21 12:57:46,440 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.110
2025-06-21 12:57:46,770 - app.utils.memory_management - INFO - Memory before cleanup: 434.79 MB
2025-06-21 12:57:46,962 - app.utils.memory_management - INFO - Garbage collection: collected 484 objects
2025-06-21 12:57:46,962 - app.utils.memory_management - INFO - Memory after cleanup: 434.75 MB (freed 0.04 MB)
2025-06-21 12:58:24,028 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:58:24,082 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 12:58:32,010 - app.utils.ai_pattern_recognition - INFO - Using live price 45.97 EGP from API for ABUK
2025-06-21 12:58:32,010 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-21 12:58:32,010 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-21 12:58:32,023 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 12:58:32,033 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 12:58:32,060 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 12:58:32,069 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 49.90 EGP for None
2025-06-21 12:58:32,071 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for None
2025-06-21 12:58:32,074 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 12:58:32,093 - app.utils.memory_management - INFO - Memory before cleanup: 435.93 MB
2025-06-21 12:58:32,276 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-21 12:58:32,276 - app.utils.memory_management - INFO - Memory after cleanup: 435.93 MB (freed 0.00 MB)
2025-06-21 12:59:13,410 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:59:13,632 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 12:59:21,795 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-21 12:59:21,795 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-21 12:59:21,795 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-21 12:59:21,795 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 12:59:21,795 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 12:59:21,851 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 12:59:21,863 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 78.62 EGP for None
2025-06-21 12:59:21,868 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for None
2025-06-21 12:59:21,881 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 12:59:21,904 - app.utils.memory_management - INFO - Memory before cleanup: 436.39 MB
2025-06-21 12:59:22,086 - app.utils.memory_management - INFO - Garbage collection: collected 235 objects
2025-06-21 12:59:22,087 - app.utils.memory_management - INFO - Memory after cleanup: 436.39 MB (freed 0.00 MB)
2025-06-21 12:59:42,054 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 12:59:42,095 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 14 days
2025-06-21 12:59:42,103 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.035
2025-06-21 12:59:42,105 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 12:59:42,107 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (42.24%) exceeds limit (15.00%)
2025-06-21 12:59:42,107 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 12:59:42,107 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (57.5)
2025-06-21 12:59:42,107 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 12:59:49,722 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-21 12:59:49,722 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-21 12:59:49,722 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-21 12:59:49,722 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 12:59:49,725 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 12:59:49,899 - app.utils.memory_management - INFO - Memory before cleanup: 436.53 MB
2025-06-21 12:59:50,078 - app.utils.memory_management - INFO - Garbage collection: collected 453 objects
2025-06-21 12:59:50,079 - app.utils.memory_management - INFO - Memory after cleanup: 436.53 MB (freed 0.00 MB)
2025-06-21 13:00:31,710 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:00:31,781 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 7 days
2025-06-21 13:00:31,794 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.084
2025-06-21 13:00:31,796 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 13:00:31,798 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (42.24%) exceeds limit (15.00%)
2025-06-21 13:00:31,800 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 13:00:31,802 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (62.6)
2025-06-21 13:00:31,804 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 13:00:40,273 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-21 13:00:40,273 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-21 13:00:40,273 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-21 13:00:40,273 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 13:00:40,273 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 13:00:40,330 - app.utils.memory_management - INFO - Memory before cleanup: 437.64 MB
2025-06-21 13:00:40,524 - app.utils.memory_management - INFO - Garbage collection: collected 453 objects
2025-06-21 13:00:40,525 - app.utils.memory_management - INFO - Memory after cleanup: 437.64 MB (freed 0.00 MB)
2025-06-21 13:01:03,110 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:01:03,178 - app.utils.memory_management - INFO - Memory before cleanup: 437.63 MB
2025-06-21 13:01:03,492 - app.utils.memory_management - INFO - Garbage collection: collected 210 objects
2025-06-21 13:01:03,496 - app.utils.memory_management - INFO - Memory after cleanup: 437.63 MB (freed 0.00 MB)
2025-06-21 13:01:09,493 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:01:09,529 - app.utils.memory_management - INFO - Memory before cleanup: 437.63 MB
2025-06-21 13:01:09,744 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:01:09,744 - app.utils.memory_management - INFO - Memory after cleanup: 437.63 MB (freed 0.00 MB)
2025-06-21 13:01:10,977 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:01:11,035 - app.utils.memory_management - INFO - Memory before cleanup: 437.63 MB
2025-06-21 13:01:11,251 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:01:11,254 - app.utils.memory_management - INFO - Memory after cleanup: 437.63 MB (freed 0.00 MB)
2025-06-21 13:01:13,750 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:01:34,108 - app.utils.memory_management - INFO - Memory before cleanup: 437.63 MB
2025-06-21 13:01:34,476 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:01:34,476 - app.utils.memory_management - INFO - Memory after cleanup: 437.63 MB (freed 0.00 MB)
2025-06-21 13:02:05,198 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:02:05,261 - app.utils.memory_management - INFO - Memory before cleanup: 437.63 MB
2025-06-21 13:02:05,537 - app.utils.memory_management - INFO - Garbage collection: collected 239 objects
2025-06-21 13:02:05,537 - app.utils.memory_management - INFO - Memory after cleanup: 437.63 MB (freed 0.00 MB)
2025-06-21 13:02:13,787 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:02:13,839 - app.utils.memory_management - INFO - Memory before cleanup: 437.64 MB
2025-06-21 13:02:14,204 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 13:02:14,207 - app.utils.memory_management - INFO - Memory after cleanup: 437.64 MB (freed 0.00 MB)
2025-06-21 13:02:17,368 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:02:17,409 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 13:02:17,429 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 13:02:17,432 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 13:02:29,737 - app.components.smc_analysis_engine - ERROR - Error generating summary: 'SMCSignal' object has no attribute 'get'
2025-06-21 13:02:30,400 - app.components.smc_analysis_engine - ERROR - Error generating summary: 'SMCSignal' object has no attribute 'get'
2025-06-21 13:02:30,528 - app.utils.memory_management - INFO - Memory before cleanup: 437.63 MB
2025-06-21 13:02:30,743 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 13:02:30,743 - app.utils.memory_management - INFO - Memory after cleanup: 437.63 MB (freed 0.00 MB)
2025-06-21 13:09:04,411 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:09:04,460 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 13:09:04,485 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 7 days
2025-06-21 13:09:04,497 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.142
2025-06-21 13:09:04,524 - app.utils.memory_management - INFO - Memory before cleanup: 437.71 MB
2025-06-21 13:09:04,924 - app.utils.memory_management - INFO - Garbage collection: collected 700 objects
2025-06-21 13:09:04,928 - app.utils.memory_management - INFO - Memory after cleanup: 437.71 MB (freed 0.00 MB)
2025-06-21 13:17:18,319 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:17:19,789 - app - INFO - Memory management utilities loaded
2025-06-21 13:17:19,789 - app - INFO - Error handling utilities loaded
2025-06-21 13:17:19,791 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:17:19,793 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:17:19,793 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:17:19,793 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:17:19,793 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:17:19,793 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:17:19,795 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:17:19,795 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:17:19,795 - app - INFO - Applied NumPy fix
2025-06-21 13:17:19,797 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:17:19,799 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:17:19,799 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:17:19,800 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:17:19,800 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:17:19,801 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:17:19,801 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:17:19,801 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:17:24,408 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:17:24,412 - app - INFO - Applied TensorFlow fix
2025-06-21 13:17:25,096 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:17:25,101 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:17:25,608 - app.components.smc_analysis_engine - ERROR - Error generating summary: 'SMCSignal' object has no attribute 'get'
2025-06-21 13:17:25,625 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:17:26,022 - app.components.smc_analysis_engine - ERROR - Error generating summary: 'SMCSignal' object has no attribute 'get'
2025-06-21 13:17:26,022 - app - INFO - Cleaning up resources...
2025-06-21 13:17:26,027 - app.utils.memory_management - INFO - Memory before cleanup: 323.69 MB
2025-06-21 13:17:26,252 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-21 13:17:26,254 - app.utils.memory_management - INFO - Memory after cleanup: 323.70 MB (freed -0.01 MB)
2025-06-21 13:17:26,254 - app - INFO - Application shutdown complete
2025-06-21 13:19:11,407 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:19:12,985 - app - INFO - Memory management utilities loaded
2025-06-21 13:19:12,985 - app - INFO - Error handling utilities loaded
2025-06-21 13:19:12,987 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:19:12,989 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:19:12,989 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:19:12,989 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:19:12,989 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:19:12,989 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:19:12,991 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:19:12,991 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:19:12,991 - app - INFO - Applied NumPy fix
2025-06-21 13:19:12,993 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:19:12,993 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:19:12,993 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:19:12,993 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:19:12,994 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:19:12,994 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:19:12,994 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:19:12,995 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:19:18,310 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:19:18,310 - app - INFO - Applied TensorFlow fix
2025-06-21 13:19:18,849 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:19:18,862 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:19:19,261 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:19:19,657 - app - INFO - Cleaning up resources...
2025-06-21 13:19:19,657 - app.utils.memory_management - INFO - Memory before cleanup: 324.70 MB
2025-06-21 13:19:19,773 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-21 13:19:19,773 - app.utils.memory_management - INFO - Memory after cleanup: 324.71 MB (freed -0.01 MB)
2025-06-21 13:19:19,773 - app - INFO - Application shutdown complete
2025-06-21 13:20:11,184 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:20:13,200 - app - INFO - Memory management utilities loaded
2025-06-21 13:20:13,205 - app - INFO - Error handling utilities loaded
2025-06-21 13:20:13,207 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:20:13,208 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:20:13,209 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:20:13,209 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:20:13,210 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:20:13,211 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:20:13,211 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:20:13,211 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:20:13,212 - app - INFO - Applied NumPy fix
2025-06-21 13:20:13,215 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:20:13,217 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:20:13,226 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:20:13,231 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:20:13,237 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:20:13,238 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:20:13,238 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:20:13,240 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:20:18,206 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:20:18,206 - app - INFO - Applied TensorFlow fix
2025-06-21 13:20:18,753 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:20:18,760 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:20:19,171 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:20:19,576 - app - INFO - Cleaning up resources...
2025-06-21 13:20:19,576 - app.utils.memory_management - INFO - Memory before cleanup: 323.24 MB
2025-06-21 13:20:19,695 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-21 13:20:19,695 - app.utils.memory_management - INFO - Memory after cleanup: 323.25 MB (freed -0.01 MB)
2025-06-21 13:20:19,695 - app - INFO - Application shutdown complete
2025-06-21 13:21:26,746 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:21:28,313 - app - INFO - Memory management utilities loaded
2025-06-21 13:21:28,315 - app - INFO - Error handling utilities loaded
2025-06-21 13:21:28,315 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:21:28,317 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:21:28,317 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:21:28,317 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:21:28,317 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:21:28,319 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:21:28,319 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:21:28,319 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:21:28,319 - app - INFO - Applied NumPy fix
2025-06-21 13:21:28,320 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:21:28,321 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:21:28,322 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:21:28,322 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:21:28,323 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:21:28,323 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:21:28,323 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:21:28,323 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:21:32,821 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:21:32,821 - app - INFO - Applied TensorFlow fix
2025-06-21 13:21:33,365 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:21:33,771 - app - INFO - Cleaning up resources...
2025-06-21 13:21:33,778 - app.utils.memory_management - INFO - Memory before cleanup: 322.93 MB
2025-06-21 13:21:33,886 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-21 13:21:33,888 - app.utils.memory_management - INFO - Memory after cleanup: 323.05 MB (freed -0.12 MB)
2025-06-21 13:21:33,888 - app - INFO - Application shutdown complete
2025-06-21 13:25:20,952 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:25:20,961 - app - INFO - Memory management utilities loaded
2025-06-21 13:25:20,967 - app - INFO - Error handling utilities loaded
2025-06-21 13:25:20,971 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:25:20,974 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:25:20,978 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:25:20,984 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:27:23,623 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:27:25,742 - app - INFO - Memory management utilities loaded
2025-06-21 13:27:25,746 - app - INFO - Error handling utilities loaded
2025-06-21 13:27:25,746 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:27:25,748 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:27:25,748 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:27:25,748 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:27:25,750 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:27:25,751 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:27:25,751 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:27:25,752 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:27:25,753 - app - INFO - Applied NumPy fix
2025-06-21 13:27:25,754 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:27:25,754 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:27:25,754 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:27:25,754 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:27:25,754 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:27:25,755 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:27:25,755 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:27:25,755 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:27:31,164 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:27:31,167 - app - INFO - Applied TensorFlow fix
2025-06-21 13:27:31,182 - app.config - INFO - Configuration initialized
2025-06-21 13:27:31,197 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 13:27:31,219 - models.train - INFO - TensorFlow test successful
2025-06-21 13:27:32,767 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 13:27:32,767 - models.train - INFO - Transformer model is available
2025-06-21 13:27:32,767 - models.train - INFO - Using TensorFlow-based models
2025-06-21 13:27:32,769 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 13:27:32,769 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 13:27:32,771 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 13:27:33,171 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 13:27:33,171 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 13:27:33,171 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:27:33,171 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:27:33,171 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:27:33,171 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 13:27:33,171 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 13:27:33,173 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:27:33,173 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:27:33,173 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:27:33,334 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 13:27:33,339 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:27:34,535 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 13:27:36,529 - app.utils.session_state - INFO - Initializing session state
2025-06-21 13:27:36,531 - app.utils.session_state - INFO - Session state initialized
2025-06-21 13:27:40,533 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 13:27:40,576 - app.utils.memory_management - INFO - Memory before cleanup: 430.30 MB
2025-06-21 13:27:40,914 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 13:27:40,953 - app.utils.memory_management - INFO - Memory after cleanup: 430.66 MB (freed -0.36 MB)
2025-06-21 13:27:47,712 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:27:47,855 - app.utils.memory_management - INFO - Memory before cleanup: 433.68 MB
2025-06-21 13:27:48,173 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 13:27:48,177 - app.utils.memory_management - INFO - Memory after cleanup: 433.68 MB (freed -0.00 MB)
2025-06-21 13:27:52,415 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:27:52,496 - app.utils.memory_management - INFO - Memory before cleanup: 434.80 MB
2025-06-21 13:27:52,843 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:27:52,850 - app.utils.memory_management - INFO - Memory after cleanup: 434.84 MB (freed -0.04 MB)
2025-06-21 13:27:54,497 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:27:54,598 - app.utils.memory_management - INFO - Memory before cleanup: 434.85 MB
2025-06-21 13:27:54,993 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:27:54,997 - app.utils.memory_management - INFO - Memory after cleanup: 434.85 MB (freed 0.00 MB)
2025-06-21 13:27:56,491 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:28:10,708 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:28:11,820 - app.pages.advanced_technical_analysis - ERROR - Error in comprehensive analysis tab: 'SMCSignal' object has no attribute 'get'
2025-06-21 13:28:11,827 - app.utils.memory_management - INFO - Memory before cleanup: 437.72 MB
2025-06-21 13:28:12,205 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:28:12,215 - app.utils.memory_management - INFO - Memory after cleanup: 437.72 MB (freed 0.00 MB)
2025-06-21 13:32:09,571 - app - INFO - Cleaning up resources...
2025-06-21 13:32:09,572 - app.utils.memory_management - INFO - Memory before cleanup: 438.32 MB
2025-06-21 13:32:09,747 - app.utils.memory_management - INFO - Garbage collection: collected 326 objects
2025-06-21 13:32:09,747 - app.utils.memory_management - INFO - Memory after cleanup: 438.32 MB (freed 0.00 MB)
2025-06-21 13:32:09,749 - app - INFO - Application shutdown complete
2025-06-21 13:32:28,048 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:32:29,418 - app - INFO - Memory management utilities loaded
2025-06-21 13:32:29,418 - app - INFO - Error handling utilities loaded
2025-06-21 13:32:29,418 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:32:29,418 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:32:29,418 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:32:29,418 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:32:29,418 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:32:29,418 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:32:29,418 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:32:29,418 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:32:29,418 - app - INFO - Applied NumPy fix
2025-06-21 13:32:29,424 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:32:29,424 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:32:29,424 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:32:29,424 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:32:29,425 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:32:29,425 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:32:29,425 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:32:29,425 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:32:33,201 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:32:33,202 - app - INFO - Applied TensorFlow fix
2025-06-21 13:32:33,660 - app - INFO - Cleaning up resources...
2025-06-21 13:32:33,664 - app.utils.memory_management - INFO - Memory before cleanup: 319.85 MB
2025-06-21 13:32:33,802 - app.utils.memory_management - INFO - Garbage collection: collected 86 objects
2025-06-21 13:32:33,802 - app.utils.memory_management - INFO - Memory after cleanup: 320.11 MB (freed -0.26 MB)
2025-06-21 13:32:33,804 - app - INFO - Application shutdown complete
2025-06-21 13:33:02,693 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:33:04,224 - app - INFO - Memory management utilities loaded
2025-06-21 13:33:04,226 - app - INFO - Error handling utilities loaded
2025-06-21 13:33:04,232 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:33:04,233 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:33:04,234 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:33:04,235 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:33:04,236 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:33:04,237 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:33:04,237 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:33:04,239 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:33:04,240 - app - INFO - Applied NumPy fix
2025-06-21 13:33:04,242 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:33:04,242 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:33:04,246 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:33:04,250 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:33:04,254 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:33:04,255 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:33:04,256 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:33:04,257 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:33:08,095 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:33:08,095 - app - INFO - Applied TensorFlow fix
2025-06-21 13:33:08,099 - app.config - INFO - Configuration initialized
2025-06-21 13:33:08,105 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 13:33:08,119 - models.train - INFO - TensorFlow test successful
2025-06-21 13:33:08,712 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 13:33:08,715 - models.train - INFO - Transformer model is available
2025-06-21 13:33:08,717 - models.train - INFO - Using TensorFlow-based models
2025-06-21 13:33:08,720 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 13:33:08,720 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 13:33:08,723 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 13:33:09,047 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 13:33:09,048 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 13:33:09,048 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:33:09,048 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:33:09,049 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:33:09,049 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 13:33:09,049 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 13:33:09,049 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:33:09,049 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:33:09,050 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:33:09,140 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 13:33:09,142 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:33:09,451 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 13:33:10,029 - app.utils.session_state - INFO - Initializing session state
2025-06-21 13:33:10,030 - app.utils.session_state - INFO - Session state initialized
2025-06-21 13:33:11,337 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 13:33:11,353 - app.utils.memory_management - INFO - Memory before cleanup: 428.30 MB
2025-06-21 13:33:11,555 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 13:33:11,558 - app.utils.memory_management - INFO - Memory after cleanup: 428.31 MB (freed -0.01 MB)
2025-06-21 13:33:14,462 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:33:14,516 - app.utils.memory_management - INFO - Memory before cleanup: 432.37 MB
2025-06-21 13:33:14,723 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 13:33:14,724 - app.utils.memory_management - INFO - Memory after cleanup: 432.37 MB (freed 0.00 MB)
2025-06-21 13:33:18,299 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:33:18,347 - app.utils.memory_management - INFO - Memory before cleanup: 433.56 MB
2025-06-21 13:33:18,594 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:33:18,594 - app.utils.memory_management - INFO - Memory after cleanup: 433.60 MB (freed -0.04 MB)
2025-06-21 13:33:19,865 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:33:19,902 - app.utils.memory_management - INFO - Memory before cleanup: 433.60 MB
2025-06-21 13:33:20,088 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:33:20,090 - app.utils.memory_management - INFO - Memory after cleanup: 433.60 MB (freed 0.00 MB)
2025-06-21 13:33:21,383 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:33:31,724 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:33:32,343 - app.pages.advanced_technical_analysis - ERROR - Error in comprehensive analysis tab: 'SMCSignal' object has no attribute 'get'
2025-06-21 13:33:32,345 - app.utils.memory_management - INFO - Memory before cleanup: 436.91 MB
2025-06-21 13:33:32,525 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:33:32,525 - app.utils.memory_management - INFO - Memory after cleanup: 436.91 MB (freed 0.00 MB)
2025-06-21 13:35:16,255 - app - INFO - Cleaning up resources...
2025-06-21 13:35:16,255 - app.utils.memory_management - INFO - Memory before cleanup: 437.28 MB
2025-06-21 13:35:16,412 - app.utils.memory_management - INFO - Garbage collection: collected 327 objects
2025-06-21 13:35:16,414 - app.utils.memory_management - INFO - Memory after cleanup: 437.28 MB (freed 0.00 MB)
2025-06-21 13:35:16,414 - app - INFO - Application shutdown complete
2025-06-21 13:36:50,825 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:36:52,472 - app - INFO - Memory management utilities loaded
2025-06-21 13:36:52,475 - app - INFO - Error handling utilities loaded
2025-06-21 13:36:52,477 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:36:52,478 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:36:52,479 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:36:52,480 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:36:52,482 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:36:52,483 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:36:52,483 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:36:52,483 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:36:52,483 - app - INFO - Applied NumPy fix
2025-06-21 13:36:52,484 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:36:52,484 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:36:52,485 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:36:52,485 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:36:52,486 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:36:52,486 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:36:52,486 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:36:52,486 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:36:56,390 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:36:56,391 - app - INFO - Applied TensorFlow fix
2025-06-21 13:36:56,863 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:36:57,314 - app - INFO - Cleaning up resources...
2025-06-21 13:36:57,318 - app.utils.memory_management - INFO - Memory before cleanup: 322.88 MB
2025-06-21 13:36:57,443 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-21 13:36:57,444 - app.utils.memory_management - INFO - Memory after cleanup: 323.00 MB (freed -0.12 MB)
2025-06-21 13:36:57,444 - app - INFO - Application shutdown complete
2025-06-21 13:38:17,738 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:38:19,645 - app - INFO - Memory management utilities loaded
2025-06-21 13:38:19,647 - app - INFO - Error handling utilities loaded
2025-06-21 13:38:19,649 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:38:19,651 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:38:19,651 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:38:19,651 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:38:19,651 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:38:19,653 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:38:19,653 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:38:19,653 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:38:19,653 - app - INFO - Applied NumPy fix
2025-06-21 13:38:19,653 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:38:19,653 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:38:19,655 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:38:19,655 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:38:19,655 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:38:19,655 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:38:19,659 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:38:19,661 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:38:23,627 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:38:23,627 - app - INFO - Applied TensorFlow fix
2025-06-21 13:38:23,629 - app.config - INFO - Configuration initialized
2025-06-21 13:38:23,633 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 13:38:23,645 - models.train - INFO - TensorFlow test successful
2025-06-21 13:38:24,220 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 13:38:24,221 - models.train - INFO - Transformer model is available
2025-06-21 13:38:24,221 - models.train - INFO - Using TensorFlow-based models
2025-06-21 13:38:24,223 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 13:38:24,223 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 13:38:24,224 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 13:38:24,640 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 13:38:24,642 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 13:38:24,646 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:38:24,649 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:38:24,652 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:38:24,652 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 13:38:24,653 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 13:38:24,653 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:38:24,653 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:38:24,657 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:38:24,761 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 13:38:24,763 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:38:25,125 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 13:38:25,849 - app.utils.session_state - INFO - Initializing session state
2025-06-21 13:38:25,851 - app.utils.session_state - INFO - Session state initialized
2025-06-21 13:38:27,380 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 13:38:27,401 - app.utils.memory_management - INFO - Memory before cleanup: 428.99 MB
2025-06-21 13:38:27,635 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 13:38:27,637 - app.utils.memory_management - INFO - Memory after cleanup: 429.00 MB (freed -0.00 MB)
2025-06-21 13:38:30,281 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:38:30,352 - app.utils.memory_management - INFO - Memory before cleanup: 432.73 MB
2025-06-21 13:38:30,695 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 13:38:30,695 - app.utils.memory_management - INFO - Memory after cleanup: 432.73 MB (freed 0.00 MB)
2025-06-21 13:38:32,640 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:38:32,685 - app.utils.memory_management - INFO - Memory before cleanup: 433.68 MB
2025-06-21 13:38:32,896 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:38:32,896 - app.utils.memory_management - INFO - Memory after cleanup: 433.71 MB (freed -0.04 MB)
2025-06-21 13:38:35,449 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:38:35,484 - app.utils.memory_management - INFO - Memory before cleanup: 433.71 MB
2025-06-21 13:38:35,690 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:38:35,690 - app.utils.memory_management - INFO - Memory after cleanup: 433.71 MB (freed 0.00 MB)
2025-06-21 13:38:37,475 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:38:46,739 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:38:47,297 - app.pages.advanced_technical_analysis - ERROR - Error in comprehensive analysis tab: Expanders may not be nested inside other expanders.
2025-06-21 13:38:47,302 - app.utils.memory_management - INFO - Memory before cleanup: 437.12 MB
2025-06-21 13:38:47,490 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:38:47,491 - app.utils.memory_management - INFO - Memory after cleanup: 437.12 MB (freed 0.00 MB)
2025-06-21 13:41:00,288 - app - INFO - Cleaning up resources...
2025-06-21 13:41:00,288 - app.utils.memory_management - INFO - Memory before cleanup: 437.40 MB
2025-06-21 13:41:00,455 - app.utils.memory_management - INFO - Garbage collection: collected 330 objects
2025-06-21 13:41:00,455 - app.utils.memory_management - INFO - Memory after cleanup: 437.40 MB (freed 0.00 MB)
2025-06-21 13:41:00,455 - app - INFO - Application shutdown complete
2025-06-21 13:41:47,845 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 13:41:49,415 - app - INFO - Memory management utilities loaded
2025-06-21 13:41:49,421 - app - INFO - Error handling utilities loaded
2025-06-21 13:41:49,428 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 13:41:49,431 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 13:41:49,434 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 13:41:49,439 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 13:41:49,442 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 13:41:49,443 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 13:41:49,444 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 13:41:49,446 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 13:41:49,448 - app - INFO - Applied NumPy fix
2025-06-21 13:41:49,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:41:49,454 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:41:49,454 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:41:49,456 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 13:41:49,458 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:41:49,458 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:41:49,460 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:41:49,462 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 13:41:52,642 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 13:41:52,642 - app - INFO - Applied TensorFlow fix
2025-06-21 13:41:52,644 - app.config - INFO - Configuration initialized
2025-06-21 13:41:52,648 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 13:41:52,658 - models.train - INFO - TensorFlow test successful
2025-06-21 13:41:53,059 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 13:41:53,060 - models.train - INFO - Transformer model is available
2025-06-21 13:41:53,060 - models.train - INFO - Using TensorFlow-based models
2025-06-21 13:41:53,061 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 13:41:53,061 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 13:41:53,063 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 13:41:53,338 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 13:41:53,338 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 13:41:53,338 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 13:41:53,340 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 13:41:53,340 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 13:41:53,340 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 13:41:53,340 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 13:41:53,340 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 13:41:53,340 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 13:41:53,340 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 13:41:53,409 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 13:41:53,409 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:41:53,692 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 13:41:54,267 - app.utils.session_state - INFO - Initializing session state
2025-06-21 13:41:54,270 - app.utils.session_state - INFO - Session state initialized
2025-06-21 13:41:55,589 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 13:41:55,617 - app.utils.memory_management - INFO - Memory before cleanup: 429.30 MB
2025-06-21 13:41:55,838 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 13:41:55,839 - app.utils.memory_management - INFO - Memory after cleanup: 429.31 MB (freed -0.00 MB)
2025-06-21 13:41:59,074 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:41:59,120 - app.utils.memory_management - INFO - Memory before cleanup: 433.04 MB
2025-06-21 13:41:59,310 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 13:41:59,312 - app.utils.memory_management - INFO - Memory after cleanup: 433.04 MB (freed 0.00 MB)
2025-06-21 13:42:01,742 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:42:01,781 - app.utils.memory_management - INFO - Memory before cleanup: 434.12 MB
2025-06-21 13:42:01,974 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:42:01,974 - app.utils.memory_management - INFO - Memory after cleanup: 434.16 MB (freed -0.04 MB)
2025-06-21 13:42:04,203 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:42:04,236 - app.utils.memory_management - INFO - Memory before cleanup: 434.16 MB
2025-06-21 13:42:04,424 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:42:04,424 - app.utils.memory_management - INFO - Memory after cleanup: 434.16 MB (freed 0.00 MB)
2025-06-21 13:42:05,636 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 13:42:15,728 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:42:16,268 - app.pages.advanced_technical_analysis - INFO - Prepared SMC data for COMI: 180 bars
2025-06-21 13:42:16,819 - app.utils.memory_management - INFO - Memory before cleanup: 437.67 MB
2025-06-21 13:42:16,999 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 13:42:16,999 - app.utils.memory_management - INFO - Memory after cleanup: 437.67 MB (freed 0.00 MB)
2025-06-21 14:51:08,858 - app - INFO - Cleaning up resources...
2025-06-21 14:51:08,860 - app.utils.memory_management - INFO - Memory before cleanup: 355.42 MB
2025-06-21 14:51:09,122 - app.utils.memory_management - INFO - Garbage collection: collected 348 objects
2025-06-21 14:51:09,124 - app.utils.memory_management - INFO - Memory after cleanup: 414.97 MB (freed -59.55 MB)
2025-06-21 14:51:09,124 - app - INFO - Application shutdown complete
2025-06-21 14:55:20,102 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 14:55:22,123 - app - INFO - Memory management utilities loaded
2025-06-21 14:55:22,156 - app - INFO - Error handling utilities loaded
2025-06-21 14:55:22,160 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 14:55:22,168 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 14:55:22,186 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 14:55:22,228 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 14:55:22,253 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 14:55:22,290 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 14:55:22,294 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 14:55:22,298 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 14:55:22,306 - app - INFO - Applied NumPy fix
2025-06-21 14:55:22,313 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 14:55:22,319 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 14:55:22,325 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 14:55:22,329 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 14:55:22,335 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 14:55:22,340 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 14:55:22,344 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 14:55:22,346 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 14:55:26,896 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 14:55:26,902 - app - INFO - Applied TensorFlow fix
2025-06-21 14:55:26,907 - app.config - INFO - Configuration initialized
2025-06-21 14:55:26,918 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 14:55:26,935 - models.train - INFO - TensorFlow test successful
2025-06-21 14:55:27,732 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 14:55:27,732 - models.train - INFO - Transformer model is available
2025-06-21 14:55:27,734 - models.train - INFO - Using TensorFlow-based models
2025-06-21 14:55:27,736 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 14:55:27,738 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 14:55:27,744 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 14:55:28,117 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 14:55:28,122 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 14:55:28,124 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 14:55:28,128 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 14:55:28,130 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 14:55:28,132 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 14:55:28,136 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 14:55:28,136 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 14:55:28,136 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 14:55:28,136 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 14:55:28,264 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 14:55:28,268 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 14:55:28,724 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 14:55:29,697 - app.utils.session_state - INFO - Initializing session state
2025-06-21 14:55:29,699 - app.utils.session_state - INFO - Session state initialized
2025-06-21 14:55:31,261 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 14:55:31,286 - app.utils.memory_management - INFO - Memory before cleanup: 429.54 MB
2025-06-21 14:55:31,479 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 14:55:31,483 - app.utils.memory_management - INFO - Memory after cleanup: 429.54 MB (freed -0.00 MB)
2025-06-21 14:55:34,146 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 14:55:34,202 - app.utils.memory_management - INFO - Memory before cleanup: 433.03 MB
2025-06-21 14:55:34,406 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 14:55:34,406 - app.utils.memory_management - INFO - Memory after cleanup: 433.03 MB (freed 0.00 MB)
2025-06-21 14:55:36,547 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 14:55:36,588 - app.utils.memory_management - INFO - Memory before cleanup: 434.00 MB
2025-06-21 14:55:36,784 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 14:55:36,786 - app.utils.memory_management - INFO - Memory after cleanup: 434.04 MB (freed -0.04 MB)
2025-06-21 14:55:39,169 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 14:55:39,239 - app.utils.memory_management - INFO - Memory before cleanup: 434.04 MB
2025-06-21 14:55:39,531 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 14:55:39,531 - app.utils.memory_management - INFO - Memory after cleanup: 434.04 MB (freed 0.00 MB)
2025-06-21 14:55:40,432 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 14:55:53,520 - app.utils.memory_management - INFO - Memory before cleanup: 436.64 MB
2025-06-21 14:55:53,721 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 14:55:53,722 - app.utils.memory_management - INFO - Memory after cleanup: 436.64 MB (freed 0.00 MB)
2025-06-21 15:02:55,605 - app - INFO - Cleaning up resources...
2025-06-21 15:02:55,607 - app.utils.memory_management - INFO - Memory before cleanup: 437.09 MB
2025-06-21 15:02:55,785 - app.utils.memory_management - INFO - Garbage collection: collected 329 objects
2025-06-21 15:02:55,788 - app.utils.memory_management - INFO - Memory after cleanup: 437.09 MB (freed 0.00 MB)
2025-06-21 15:02:55,791 - app - INFO - Application shutdown complete
2025-06-21 15:07:49,754 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:07:51,123 - app - INFO - Memory management utilities loaded
2025-06-21 15:07:51,132 - app - INFO - Error handling utilities loaded
2025-06-21 15:07:51,138 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:07:51,142 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:07:51,146 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:07:51,150 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:07:51,156 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:07:51,162 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:07:51,165 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:07:51,167 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:07:51,167 - app - INFO - Applied NumPy fix
2025-06-21 15:07:51,171 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:07:51,171 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:07:51,174 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:07:51,176 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:07:51,178 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:07:51,180 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:07:51,184 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:07:51,186 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:07:54,930 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:07:54,930 - app - INFO - Applied TensorFlow fix
2025-06-21 15:07:54,933 - app.config - INFO - Configuration initialized
2025-06-21 15:07:54,938 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 15:07:54,953 - models.train - INFO - TensorFlow test successful
2025-06-21 15:07:55,555 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 15:07:55,555 - models.train - INFO - Transformer model is available
2025-06-21 15:07:55,555 - models.train - INFO - Using TensorFlow-based models
2025-06-21 15:07:55,555 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 15:07:55,555 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 15:07:55,559 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 15:07:55,878 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 15:07:55,878 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 15:07:55,878 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:07:55,878 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:07:55,879 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:07:55,879 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 15:07:55,879 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 15:07:55,879 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:07:55,879 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:07:55,879 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:07:55,958 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 15:07:55,960 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:07:56,270 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 15:07:56,867 - app.utils.session_state - INFO - Initializing session state
2025-06-21 15:07:56,869 - app.utils.session_state - INFO - Session state initialized
2025-06-21 15:07:58,088 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 15:07:58,103 - app.utils.memory_management - INFO - Memory before cleanup: 429.43 MB
2025-06-21 15:07:58,304 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 15:07:58,304 - app.utils.memory_management - INFO - Memory after cleanup: 429.43 MB (freed -0.01 MB)
2025-06-21 15:08:00,891 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:08:00,930 - app.utils.memory_management - INFO - Memory before cleanup: 432.75 MB
2025-06-21 15:08:01,146 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 15:08:01,146 - app.utils.memory_management - INFO - Memory after cleanup: 432.75 MB (freed 0.00 MB)
2025-06-21 15:08:04,798 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:08:04,829 - app.utils.memory_management - INFO - Memory before cleanup: 432.77 MB
2025-06-21 15:08:05,036 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 15:08:05,037 - app.utils.memory_management - INFO - Memory after cleanup: 432.81 MB (freed -0.04 MB)
2025-06-21 15:08:07,698 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:08:07,742 - app.utils.memory_management - INFO - Memory before cleanup: 432.86 MB
2025-06-21 15:08:07,949 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 15:08:07,949 - app.utils.memory_management - INFO - Memory after cleanup: 432.86 MB (freed -0.00 MB)
2025-06-21 15:08:11,493 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:08:11,527 - app.utils.memory_management - INFO - Memory before cleanup: 433.88 MB
2025-06-21 15:08:11,725 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:08:11,726 - app.utils.memory_management - INFO - Memory after cleanup: 433.88 MB (freed 0.00 MB)
2025-06-21 15:08:13,773 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:08:13,819 - app.utils.memory_management - INFO - Memory before cleanup: 433.88 MB
2025-06-21 15:08:14,012 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:08:14,012 - app.utils.memory_management - INFO - Memory after cleanup: 433.88 MB (freed 0.00 MB)
2025-06-21 15:08:16,077 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:08:26,474 - app.utils.memory_management - INFO - Memory before cleanup: 436.76 MB
2025-06-21 15:08:26,675 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:08:26,676 - app.utils.memory_management - INFO - Memory after cleanup: 436.76 MB (freed 0.00 MB)
2025-06-21 15:12:28,707 - app - INFO - Cleaning up resources...
2025-06-21 15:12:28,709 - app.utils.memory_management - INFO - Memory before cleanup: 437.07 MB
2025-06-21 15:12:28,859 - app.utils.memory_management - INFO - Garbage collection: collected 329 objects
2025-06-21 15:12:28,859 - app.utils.memory_management - INFO - Memory after cleanup: 437.07 MB (freed 0.00 MB)
2025-06-21 15:12:28,859 - app - INFO - Application shutdown complete
2025-06-21 15:13:46,479 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:13:47,811 - app - INFO - Memory management utilities loaded
2025-06-21 15:13:47,811 - app - INFO - Error handling utilities loaded
2025-06-21 15:13:47,811 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:13:47,811 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:13:47,818 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:13:47,818 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:13:47,820 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:13:47,820 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:13:47,821 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:13:47,821 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:13:47,821 - app - INFO - Applied NumPy fix
2025-06-21 15:13:47,823 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:13:47,823 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:13:47,823 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:13:47,823 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:13:47,824 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:13:47,824 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:13:47,824 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:13:47,824 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:13:51,252 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:13:51,253 - app - INFO - Applied TensorFlow fix
2025-06-21 15:13:51,791 - app - INFO - Cleaning up resources...
2025-06-21 15:13:51,795 - app.utils.memory_management - INFO - Memory before cleanup: 320.27 MB
2025-06-21 15:13:51,927 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-06-21 15:13:51,927 - app.utils.memory_management - INFO - Memory after cleanup: 320.57 MB (freed -0.30 MB)
2025-06-21 15:13:51,927 - app - INFO - Application shutdown complete
2025-06-21 15:27:07,483 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:27:08,785 - app - INFO - Memory management utilities loaded
2025-06-21 15:27:08,785 - app - INFO - Error handling utilities loaded
2025-06-21 15:27:08,785 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:27:08,785 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:27:08,785 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:27:08,785 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:27:08,791 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:27:08,791 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:27:08,791 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:27:08,793 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:27:08,793 - app - INFO - Applied NumPy fix
2025-06-21 15:27:08,794 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:27:08,794 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:27:08,794 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:27:08,794 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:27:08,795 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:27:08,795 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:27:08,795 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:27:08,795 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:27:13,433 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:27:13,438 - app - INFO - Applied TensorFlow fix
2025-06-21 15:27:14,717 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:27:14,779 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-21 15:27:14,799 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-21 15:27:14,842 - app - INFO - Cleaning up resources...
2025-06-21 15:27:14,846 - app.utils.memory_management - INFO - Memory before cleanup: 323.62 MB
2025-06-21 15:27:14,978 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:27:14,979 - app.utils.memory_management - INFO - Memory after cleanup: 323.63 MB (freed -0.01 MB)
2025-06-21 15:27:14,982 - app - INFO - Application shutdown complete
2025-06-21 15:27:37,881 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:27:39,126 - app - INFO - Memory management utilities loaded
2025-06-21 15:27:39,126 - app - INFO - Error handling utilities loaded
2025-06-21 15:27:39,126 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:27:39,131 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:27:39,131 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:27:39,131 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:27:39,131 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:27:39,131 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:27:39,131 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:27:39,133 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:27:39,133 - app - INFO - Applied NumPy fix
2025-06-21 15:27:39,134 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:27:39,134 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:27:39,134 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:27:39,134 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:27:39,135 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:27:39,135 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:27:39,135 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:27:39,135 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:27:42,781 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:27:42,783 - app - INFO - Applied TensorFlow fix
2025-06-21 15:27:43,153 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:27:43,176 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:27:43,195 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:27:43,212 - app - INFO - Cleaning up resources...
2025-06-21 15:27:43,214 - app.utils.memory_management - INFO - Memory before cleanup: 322.06 MB
2025-06-21 15:27:43,324 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:27:43,325 - app.utils.memory_management - INFO - Memory after cleanup: 322.09 MB (freed -0.03 MB)
2025-06-21 15:27:43,326 - app - INFO - Application shutdown complete
2025-06-21 15:28:02,445 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:28:03,814 - app - INFO - Memory management utilities loaded
2025-06-21 15:28:03,817 - app - INFO - Error handling utilities loaded
2025-06-21 15:28:03,822 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:28:03,824 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:28:03,826 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:28:03,828 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:28:03,831 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:28:03,832 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:28:03,833 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:28:03,834 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:28:03,835 - app - INFO - Applied NumPy fix
2025-06-21 15:28:03,837 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:28:03,838 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:28:03,839 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:28:03,840 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:28:03,841 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:28:03,842 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:28:03,843 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:28:03,844 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:28:06,965 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:28:06,965 - app - INFO - Applied TensorFlow fix
2025-06-21 15:28:06,967 - app.config - INFO - Configuration initialized
2025-06-21 15:28:06,971 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 15:28:06,983 - models.train - INFO - TensorFlow test successful
2025-06-21 15:28:07,442 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 15:28:07,442 - models.train - INFO - Transformer model is available
2025-06-21 15:28:07,442 - models.train - INFO - Using TensorFlow-based models
2025-06-21 15:28:07,444 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 15:28:07,444 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 15:28:07,445 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 15:28:07,754 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 15:28:07,754 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 15:28:07,754 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:28:07,756 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:28:07,756 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:28:07,756 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 15:28:07,756 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 15:28:07,756 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:28:07,756 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:28:07,756 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:28:07,840 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 15:28:07,842 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:28:08,153 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 15:28:08,599 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:28:08,732 - app.utils.session_state - INFO - Initializing session state
2025-06-21 15:28:08,736 - app.utils.session_state - INFO - Session state initialized
2025-06-21 15:28:09,991 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 15:28:10,015 - app.utils.memory_management - INFO - Memory before cleanup: 428.69 MB
2025-06-21 15:28:10,275 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 15:28:10,275 - app.utils.memory_management - INFO - Memory after cleanup: 428.69 MB (freed -0.00 MB)
2025-06-21 15:28:12,981 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:28:13,033 - app.utils.memory_management - INFO - Memory before cleanup: 432.57 MB
2025-06-21 15:28:13,239 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 15:28:13,240 - app.utils.memory_management - INFO - Memory after cleanup: 432.57 MB (freed 0.00 MB)
2025-06-21 15:28:15,413 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:28:15,448 - app.utils.memory_management - INFO - Memory before cleanup: 433.67 MB
2025-06-21 15:28:15,652 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:28:15,654 - app.utils.memory_management - INFO - Memory after cleanup: 433.71 MB (freed -0.04 MB)
2025-06-21 15:28:18,106 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:28:18,143 - app.utils.memory_management - INFO - Memory before cleanup: 433.71 MB
2025-06-21 15:28:18,344 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:28:18,344 - app.utils.memory_management - INFO - Memory after cleanup: 433.71 MB (freed 0.00 MB)
2025-06-21 15:28:19,921 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:28:32,541 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-21 15:28:32,625 - app.utils.memory_management - INFO - Memory before cleanup: 436.96 MB
2025-06-21 15:28:32,835 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:28:32,837 - app.utils.memory_management - INFO - Memory after cleanup: 436.96 MB (freed 0.00 MB)
2025-06-21 15:33:31,875 - app - INFO - Cleaning up resources...
2025-06-21 15:33:31,875 - app.utils.memory_management - INFO - Memory before cleanup: 437.28 MB
2025-06-21 15:33:32,068 - app.utils.memory_management - INFO - Garbage collection: collected 337 objects
2025-06-21 15:33:32,068 - app.utils.memory_management - INFO - Memory after cleanup: 437.28 MB (freed 0.00 MB)
2025-06-21 15:33:32,068 - app - INFO - Application shutdown complete
2025-06-21 15:40:50,708 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:40:51,980 - app - INFO - Memory management utilities loaded
2025-06-21 15:40:51,982 - app - INFO - Error handling utilities loaded
2025-06-21 15:40:51,983 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:40:51,984 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:40:51,984 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:40:51,986 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:40:51,987 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:40:51,988 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:40:51,988 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:40:51,990 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:40:51,990 - app - INFO - Applied NumPy fix
2025-06-21 15:40:51,992 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:40:51,992 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:40:51,992 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:40:51,992 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:40:51,992 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:40:51,992 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:40:51,994 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:40:51,994 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:41:01,366 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:41:01,366 - app - INFO - Applied TensorFlow fix
2025-06-21 15:41:01,751 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:41:01,760 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:41:01,772 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:41:01,781 - app - INFO - Cleaning up resources...
2025-06-21 15:41:01,783 - app.utils.memory_management - INFO - Memory before cleanup: 322.91 MB
2025-06-21 15:41:01,920 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:41:01,921 - app.utils.memory_management - INFO - Memory after cleanup: 322.92 MB (freed -0.01 MB)
2025-06-21 15:41:01,922 - app - INFO - Application shutdown complete
2025-06-21 15:41:09,678 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:41:11,146 - app - INFO - Memory management utilities loaded
2025-06-21 15:41:11,146 - app - INFO - Error handling utilities loaded
2025-06-21 15:41:11,151 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:41:11,151 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:41:11,151 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:41:11,153 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:41:11,155 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:41:11,157 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:41:11,158 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:41:11,158 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:41:11,158 - app - INFO - Applied NumPy fix
2025-06-21 15:41:11,159 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:41:11,159 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:41:11,160 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:41:11,161 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:41:11,161 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:41:11,161 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:41:11,163 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:41:11,163 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:41:14,770 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:41:14,770 - app - INFO - Applied TensorFlow fix
2025-06-21 15:41:15,161 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:41:15,176 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:41:15,199 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:41:15,216 - app - INFO - Cleaning up resources...
2025-06-21 15:41:15,220 - app.utils.memory_management - INFO - Memory before cleanup: 322.50 MB
2025-06-21 15:41:15,344 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:41:15,346 - app.utils.memory_management - INFO - Memory after cleanup: 322.52 MB (freed -0.03 MB)
2025-06-21 15:41:15,346 - app - INFO - Application shutdown complete
2025-06-21 15:45:18,380 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:45:19,642 - app - INFO - Memory management utilities loaded
2025-06-21 15:45:19,642 - app - INFO - Error handling utilities loaded
2025-06-21 15:45:19,644 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:45:19,646 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:45:19,646 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:45:19,646 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:45:19,646 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:45:19,646 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:45:19,646 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:45:19,646 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:45:19,649 - app - INFO - Applied NumPy fix
2025-06-21 15:45:19,649 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:45:19,649 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:45:19,649 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:45:19,649 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:45:19,651 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:45:19,651 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:45:19,651 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:45:19,651 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:45:23,142 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:45:23,142 - app - INFO - Applied TensorFlow fix
2025-06-21 15:45:23,578 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:45:23,590 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:45:23,610 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:45:23,623 - app - INFO - Cleaning up resources...
2025-06-21 15:45:23,627 - app.utils.memory_management - INFO - Memory before cleanup: 323.20 MB
2025-06-21 15:45:23,751 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:45:23,751 - app.utils.memory_management - INFO - Memory after cleanup: 323.20 MB (freed -0.01 MB)
2025-06-21 15:45:23,751 - app - INFO - Application shutdown complete
2025-06-21 15:46:01,186 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:46:02,629 - app - INFO - Memory management utilities loaded
2025-06-21 15:46:02,631 - app - INFO - Error handling utilities loaded
2025-06-21 15:46:02,633 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:46:02,633 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:46:02,635 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:46:02,635 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:46:02,638 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:46:02,638 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:46:02,639 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:46:02,640 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:46:02,644 - app - INFO - Applied NumPy fix
2025-06-21 15:46:02,644 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:46:02,646 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:46:02,646 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:46:02,648 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:46:02,648 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:46:02,648 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:46:02,650 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:46:02,650 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:46:06,116 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:46:06,118 - app - INFO - Applied TensorFlow fix
2025-06-21 15:46:06,539 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:46:06,550 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:46:06,566 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:46:06,635 - app - INFO - Cleaning up resources...
2025-06-21 15:46:06,700 - app.utils.memory_management - INFO - Memory before cleanup: 323.20 MB
2025-06-21 15:46:06,857 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:46:06,857 - app.utils.memory_management - INFO - Memory after cleanup: 323.20 MB (freed -0.01 MB)
2025-06-21 15:46:06,857 - app - INFO - Application shutdown complete
2025-06-21 15:46:28,982 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:46:30,413 - app - INFO - Memory management utilities loaded
2025-06-21 15:46:30,413 - app - INFO - Error handling utilities loaded
2025-06-21 15:46:30,415 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:46:30,416 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:46:30,416 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:46:30,416 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:46:30,416 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:46:30,416 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:46:30,416 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:46:30,416 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:46:30,416 - app - INFO - Applied NumPy fix
2025-06-21 15:46:30,420 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:46:30,420 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:46:30,420 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:46:30,422 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:46:30,422 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:46:30,422 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:46:30,422 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:46:30,422 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:46:34,529 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:46:34,529 - app - INFO - Applied TensorFlow fix
2025-06-21 15:46:34,942 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:46:34,950 - app - INFO - Cleaning up resources...
2025-06-21 15:46:34,955 - app.utils.memory_management - INFO - Memory before cleanup: 320.20 MB
2025-06-21 15:46:35,078 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:46:35,078 - app.utils.memory_management - INFO - Memory after cleanup: 320.52 MB (freed -0.32 MB)
2025-06-21 15:46:35,078 - app - INFO - Application shutdown complete
2025-06-21 15:48:49,956 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:48:51,198 - app - INFO - Memory management utilities loaded
2025-06-21 15:48:51,200 - app - INFO - Error handling utilities loaded
2025-06-21 15:48:51,200 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:48:51,200 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:48:51,200 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:48:51,200 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:48:51,200 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:48:51,200 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:48:51,204 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:48:51,204 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:48:51,204 - app - INFO - Applied NumPy fix
2025-06-21 15:48:51,206 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:48:51,206 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:48:51,206 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:48:51,206 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:48:51,206 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:48:51,206 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:48:51,208 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:48:51,208 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:48:54,651 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:48:54,653 - app - INFO - Applied TensorFlow fix
2025-06-21 15:48:55,070 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:48:55,072 - app - INFO - Cleaning up resources...
2025-06-21 15:48:55,076 - app.utils.memory_management - INFO - Memory before cleanup: 321.14 MB
2025-06-21 15:48:55,196 - app.utils.memory_management - INFO - Garbage collection: collected 391 objects
2025-06-21 15:48:55,198 - app.utils.memory_management - INFO - Memory after cleanup: 321.16 MB (freed -0.02 MB)
2025-06-21 15:48:55,198 - app - INFO - Application shutdown complete
2025-06-21 15:51:03,248 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:51:04,544 - app - INFO - Memory management utilities loaded
2025-06-21 15:51:04,546 - app - INFO - Error handling utilities loaded
2025-06-21 15:51:04,546 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:51:04,546 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:51:04,546 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:51:04,549 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:51:04,549 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:51:04,551 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:51:04,551 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:51:04,551 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:51:04,551 - app - INFO - Applied NumPy fix
2025-06-21 15:51:04,553 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:51:04,554 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:51:04,554 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:51:04,554 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:51:04,555 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:51:04,555 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:51:04,555 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:51:04,555 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:51:08,297 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:51:08,299 - app - INFO - Applied TensorFlow fix
2025-06-21 15:51:08,841 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:51:08,861 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:51:08,876 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 15:51:08,911 - app - INFO - Cleaning up resources...
2025-06-21 15:51:08,916 - app.utils.memory_management - INFO - Memory before cleanup: 322.18 MB
2025-06-21 15:51:09,054 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:51:09,055 - app.utils.memory_management - INFO - Memory after cleanup: 322.19 MB (freed -0.01 MB)
2025-06-21 15:51:09,056 - app - INFO - Application shutdown complete
2025-06-21 15:51:29,160 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:51:30,363 - app - INFO - Memory management utilities loaded
2025-06-21 15:51:30,363 - app - INFO - Error handling utilities loaded
2025-06-21 15:51:30,368 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:51:30,369 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:51:30,369 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:51:30,369 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:51:30,369 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:51:30,369 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:51:30,369 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:51:30,369 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:51:30,369 - app - INFO - Applied NumPy fix
2025-06-21 15:51:30,369 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:51:30,369 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:51:30,369 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:51:30,369 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:51:30,369 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:51:30,369 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:51:30,369 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:51:30,369 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:51:34,573 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:51:34,575 - app - INFO - Applied TensorFlow fix
2025-06-21 15:51:34,958 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:51:34,960 - app - INFO - Cleaning up resources...
2025-06-21 15:51:34,963 - app.utils.memory_management - INFO - Memory before cleanup: 319.03 MB
2025-06-21 15:51:35,075 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 15:51:35,075 - app.utils.memory_management - INFO - Memory after cleanup: 319.38 MB (freed -0.35 MB)
2025-06-21 15:51:35,076 - app - INFO - Application shutdown complete
2025-06-21 15:52:22,456 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 15:52:23,777 - app - INFO - Memory management utilities loaded
2025-06-21 15:52:23,779 - app - INFO - Error handling utilities loaded
2025-06-21 15:52:23,782 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 15:52:23,785 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 15:52:23,785 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 15:52:23,785 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 15:52:23,786 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 15:52:23,787 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 15:52:23,787 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 15:52:23,787 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 15:52:23,787 - app - INFO - Applied NumPy fix
2025-06-21 15:52:23,788 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:52:23,788 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:52:23,788 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:52:23,789 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 15:52:23,789 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:52:23,789 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:52:23,789 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:52:23,789 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 15:52:26,428 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 15:52:26,428 - app - INFO - Applied TensorFlow fix
2025-06-21 15:52:26,430 - app.config - INFO - Configuration initialized
2025-06-21 15:52:26,432 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 15:52:29,282 - models.train - INFO - TensorFlow test successful
2025-06-21 15:52:30,487 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 15:52:30,487 - models.train - INFO - Transformer model is available
2025-06-21 15:52:30,487 - models.train - INFO - Using TensorFlow-based models
2025-06-21 15:52:30,489 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 15:52:30,489 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 15:52:30,491 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 15:52:30,821 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 15:52:30,821 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 15:52:30,822 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 15:52:30,822 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 15:52:30,822 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 15:52:30,822 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 15:52:30,822 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 15:52:30,823 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 15:52:30,823 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 15:52:30,823 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 15:52:30,907 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 15:52:30,911 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:52:31,223 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 15:52:31,828 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 15:52:31,971 - app.utils.session_state - INFO - Initializing session state
2025-06-21 15:52:31,973 - app.utils.session_state - INFO - Session state initialized
2025-06-21 15:52:33,979 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 15:52:34,088 - app.utils.memory_management - INFO - Memory before cleanup: 432.13 MB
2025-06-21 15:52:34,352 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 15:52:34,354 - app.utils.memory_management - INFO - Memory after cleanup: 432.13 MB (freed -0.00 MB)
2025-06-21 15:52:40,822 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:52:40,865 - app.utils.memory_management - INFO - Memory before cleanup: 434.57 MB
2025-06-21 15:52:41,076 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 15:52:41,076 - app.utils.memory_management - INFO - Memory after cleanup: 434.57 MB (freed 0.00 MB)
2025-06-21 15:52:45,502 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:52:45,554 - app.utils.memory_management - INFO - Memory before cleanup: 434.96 MB
2025-06-21 15:52:45,782 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:52:45,783 - app.utils.memory_management - INFO - Memory after cleanup: 435.00 MB (freed -0.04 MB)
2025-06-21 15:52:48,103 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:52:48,142 - app.utils.memory_management - INFO - Memory before cleanup: 435.00 MB
2025-06-21 15:52:48,336 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:52:48,336 - app.utils.memory_management - INFO - Memory after cleanup: 435.00 MB (freed 0.00 MB)
2025-06-21 15:52:49,487 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:53:02,914 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-21 15:53:02,977 - app.utils.memory_management - INFO - Memory before cleanup: 437.88 MB
2025-06-21 15:53:03,172 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:53:03,172 - app.utils.memory_management - INFO - Memory after cleanup: 437.88 MB (freed 0.00 MB)
2025-06-21 15:55:39,105 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:55:39,160 - app.utils.memory_management - INFO - Memory before cleanup: 438.30 MB
2025-06-21 15:55:39,375 - app.utils.memory_management - INFO - Garbage collection: collected 265 objects
2025-06-21 15:55:39,377 - app.utils.memory_management - INFO - Memory after cleanup: 438.30 MB (freed 0.00 MB)
2025-06-21 15:55:42,898 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:55:42,930 - app.utils.memory_management - INFO - Memory before cleanup: 438.31 MB
2025-06-21 15:55:43,140 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 15:55:43,140 - app.utils.memory_management - INFO - Memory after cleanup: 438.31 MB (freed 0.00 MB)
2025-06-21 15:55:44,639 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:55:44,671 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 15:55:44,681 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 15:55:44,683 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 15:55:54,521 - app.utils.memory_management - INFO - Memory before cleanup: 438.74 MB
2025-06-21 15:55:54,698 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 15:55:54,699 - app.utils.memory_management - INFO - Memory after cleanup: 438.74 MB (freed 0.00 MB)
2025-06-21 15:58:21,340 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:58:21,359 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 15:58:21,380 - app.utils.memory_management - INFO - Memory before cleanup: 438.93 MB
2025-06-21 15:58:21,592 - app.utils.memory_management - INFO - Garbage collection: collected 302 objects
2025-06-21 15:58:21,593 - app.utils.memory_management - INFO - Memory after cleanup: 438.93 MB (freed 0.00 MB)
2025-06-21 15:58:24,193 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:58:24,230 - app.utils.memory_management - INFO - Memory before cleanup: 438.91 MB
2025-06-21 15:58:24,424 - app.utils.memory_management - INFO - Garbage collection: collected 189 objects
2025-06-21 15:58:24,424 - app.utils.memory_management - INFO - Memory after cleanup: 438.91 MB (freed 0.00 MB)
2025-06-21 15:58:27,578 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:58:27,613 - app.utils.memory_management - INFO - Memory before cleanup: 438.91 MB
2025-06-21 15:58:27,789 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:58:27,791 - app.utils.memory_management - INFO - Memory after cleanup: 438.91 MB (freed 0.00 MB)
2025-06-21 15:58:29,037 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 15:58:39,496 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-21 15:58:39,555 - app.utils.memory_management - INFO - Memory before cleanup: 438.92 MB
2025-06-21 15:58:39,766 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 15:58:39,767 - app.utils.memory_management - INFO - Memory after cleanup: 438.92 MB (freed 0.00 MB)
2025-06-21 16:00:57,989 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:00:58,118 - app.utils.memory_management - INFO - Memory before cleanup: 438.86 MB
2025-06-21 16:00:58,403 - app.utils.memory_management - INFO - Garbage collection: collected 265 objects
2025-06-21 16:00:58,404 - app.utils.memory_management - INFO - Memory after cleanup: 438.86 MB (freed 0.00 MB)
2025-06-21 16:01:01,981 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:01:02,009 - app.utils.memory_management - INFO - Memory before cleanup: 438.86 MB
2025-06-21 16:01:02,207 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 16:01:02,207 - app.utils.memory_management - INFO - Memory after cleanup: 438.86 MB (freed 0.00 MB)
2025-06-21 16:01:03,380 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:01:03,411 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-21 16:01:03,418 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-11 to 2025-06-19
2025-06-21 16:01:03,418 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-21 16:01:15,677 - app.utils.memory_management - INFO - Memory before cleanup: 439.18 MB
2025-06-21 16:01:15,852 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-21 16:01:15,853 - app.utils.memory_management - INFO - Memory after cleanup: 439.18 MB (freed 0.00 MB)
2025-06-21 16:04:00,148 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:04:00,190 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 16:04:00,216 - app.utils.memory_management - INFO - Memory before cleanup: 439.21 MB
2025-06-21 16:04:00,447 - app.utils.memory_management - INFO - Garbage collection: collected 302 objects
2025-06-21 16:04:00,447 - app.utils.memory_management - INFO - Memory after cleanup: 439.21 MB (freed 0.00 MB)
2025-06-21 16:04:03,284 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:04:03,321 - app.utils.memory_management - INFO - Memory before cleanup: 439.19 MB
2025-06-21 16:04:03,518 - app.utils.memory_management - INFO - Garbage collection: collected 189 objects
2025-06-21 16:04:03,519 - app.utils.memory_management - INFO - Memory after cleanup: 439.19 MB (freed 0.00 MB)
2025-06-21 16:04:06,654 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:04:06,688 - app.utils.memory_management - INFO - Memory before cleanup: 439.19 MB
2025-06-21 16:04:06,881 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 16:04:06,884 - app.utils.memory_management - INFO - Memory after cleanup: 439.19 MB (freed 0.00 MB)
2025-06-21 16:04:07,989 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:04:18,116 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-21 16:04:18,206 - app.utils.memory_management - INFO - Memory before cleanup: 439.21 MB
2025-06-21 16:04:18,388 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 16:04:18,389 - app.utils.memory_management - INFO - Memory after cleanup: 439.21 MB (freed 0.00 MB)
2025-06-21 16:12:10,535 - app - INFO - Cleaning up resources...
2025-06-21 16:12:10,547 - app.utils.memory_management - INFO - Memory before cleanup: 438.90 MB
2025-06-21 16:12:10,776 - app.utils.memory_management - INFO - Garbage collection: collected 337 objects
2025-06-21 16:12:10,792 - app.utils.memory_management - INFO - Memory after cleanup: 438.90 MB (freed 0.00 MB)
2025-06-21 16:12:10,840 - app - INFO - Application shutdown complete
2025-06-21 16:13:47,474 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 16:13:48,806 - app - INFO - Memory management utilities loaded
2025-06-21 16:13:48,807 - app - INFO - Error handling utilities loaded
2025-06-21 16:13:48,808 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 16:13:48,808 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 16:13:48,809 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 16:13:48,809 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 16:13:48,809 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 16:13:48,810 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 16:13:48,810 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 16:13:48,810 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 16:13:48,810 - app - INFO - Applied NumPy fix
2025-06-21 16:13:48,812 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 16:13:48,812 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 16:13:48,813 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 16:13:48,813 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 16:13:48,813 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 16:13:48,813 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 16:13:48,813 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 16:13:48,813 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 16:13:52,274 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 16:13:52,274 - app - INFO - Applied TensorFlow fix
2025-06-21 16:13:52,760 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 16:13:52,774 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 16:13:52,789 - app - INFO - Cleaning up resources...
2025-06-21 16:13:52,793 - app.utils.memory_management - INFO - Memory before cleanup: 322.11 MB
2025-06-21 16:13:52,930 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-21 16:13:52,930 - app.utils.memory_management - INFO - Memory after cleanup: 322.14 MB (freed -0.03 MB)
2025-06-21 16:13:52,933 - app - INFO - Application shutdown complete
2025-06-21 16:14:20,642 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 16:14:21,968 - app - INFO - Memory management utilities loaded
2025-06-21 16:14:21,970 - app - INFO - Error handling utilities loaded
2025-06-21 16:14:21,974 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 16:14:21,977 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 16:14:21,979 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 16:14:21,979 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 16:14:21,985 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 16:14:21,991 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 16:14:21,996 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 16:14:21,999 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 16:14:22,000 - app - INFO - Applied NumPy fix
2025-06-21 16:14:22,002 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 16:14:22,005 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 16:14:22,007 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 16:14:22,008 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 16:14:22,011 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 16:14:22,013 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 16:14:22,015 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 16:14:22,017 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 16:14:25,829 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 16:14:25,831 - app - INFO - Applied TensorFlow fix
2025-06-21 16:14:25,835 - app.config - INFO - Configuration initialized
2025-06-21 16:14:25,840 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 16:14:25,849 - models.train - INFO - TensorFlow test successful
2025-06-21 16:14:26,912 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 16:14:26,914 - models.train - INFO - Transformer model is available
2025-06-21 16:14:26,916 - models.train - INFO - Using TensorFlow-based models
2025-06-21 16:14:27,285 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 16:14:27,285 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 16:14:27,288 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 16:14:27,630 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 16:14:27,630 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 16:14:27,630 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 16:14:27,631 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 16:14:27,631 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 16:14:27,631 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 16:14:27,632 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 16:14:27,632 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 16:14:27,632 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 16:14:27,632 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 16:14:27,709 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 16:14:27,713 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:14:28,698 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 16:14:29,237 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 16:14:29,367 - app.utils.session_state - INFO - Initializing session state
2025-06-21 16:14:29,369 - app.utils.session_state - INFO - Session state initialized
2025-06-21 16:14:30,687 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 16:14:30,702 - app.utils.memory_management - INFO - Memory before cleanup: 432.61 MB
2025-06-21 16:14:30,911 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 16:14:30,911 - app.utils.memory_management - INFO - Memory after cleanup: 432.62 MB (freed -0.01 MB)
2025-06-21 16:14:34,021 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:14:34,060 - app.utils.memory_management - INFO - Memory before cleanup: 435.12 MB
2025-06-21 16:14:34,245 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 16:14:34,246 - app.utils.memory_management - INFO - Memory after cleanup: 435.12 MB (freed 0.00 MB)
2025-06-21 16:14:37,482 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:14:37,518 - app.utils.memory_management - INFO - Memory before cleanup: 435.52 MB
2025-06-21 16:14:37,722 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 16:14:37,726 - app.utils.memory_management - INFO - Memory after cleanup: 435.55 MB (freed -0.04 MB)
2025-06-21 16:14:40,230 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:14:40,281 - app.utils.memory_management - INFO - Memory before cleanup: 435.56 MB
2025-06-21 16:14:40,466 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 16:14:40,467 - app.utils.memory_management - INFO - Memory after cleanup: 435.56 MB (freed 0.00 MB)
2025-06-21 16:14:41,657 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 16:14:55,930 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-21 16:14:56,002 - app.utils.memory_management - INFO - Memory before cleanup: 438.10 MB
2025-06-21 16:14:56,210 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 16:14:56,211 - app.utils.memory_management - INFO - Memory after cleanup: 438.10 MB (freed 0.00 MB)
2025-06-21 17:26:40,264 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:26:40,300 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 17:26:40,317 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 17:26:40,326 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 17:26:40,440 - app.utils.memory_management - INFO - Memory before cleanup: 434.79 MB
2025-06-21 17:26:40,685 - app.utils.memory_management - INFO - Garbage collection: collected 267 objects
2025-06-21 17:26:40,686 - app.utils.memory_management - INFO - Memory after cleanup: 434.79 MB (freed -0.00 MB)
2025-06-21 17:27:03,701 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:27:03,734 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 17:27:03,741 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 17:27:03,833 - app.utils.memory_management - INFO - Memory before cleanup: 435.01 MB
2025-06-21 17:27:04,072 - app.utils.memory_management - INFO - Garbage collection: collected 248 objects
2025-06-21 17:27:04,074 - app.utils.memory_management - INFO - Memory after cleanup: 435.01 MB (freed 0.00 MB)
2025-06-21 17:27:10,172 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:27:10,204 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 17:27:10,210 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 17:27:10,292 - app.utils.memory_management - INFO - Memory before cleanup: 435.01 MB
2025-06-21 17:27:10,487 - app.utils.memory_management - INFO - Garbage collection: collected 245 objects
2025-06-21 17:27:10,488 - app.utils.memory_management - INFO - Memory after cleanup: 435.01 MB (freed 0.00 MB)
2025-06-21 17:27:26,007 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:27:26,035 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 17:27:26,041 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 17:27:26,069 - app.utils.historical_data_downloader - INFO - Starting historical data generation for SWDY (3 years)
2025-06-21 17:27:26,069 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-21 17:27:26,077 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-21 17:27:26,078 - app.utils.historical_data_downloader - INFO - Fetching current live data for SWDY...
2025-06-21 17:27:26,078 - app.utils.historical_data_downloader - INFO - Downloading historical data for SWDY with intervals: ['1D', '1W']
2025-06-21 17:27:26,079 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-21 17:27:26,079 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-SWDY'], 'intervals': ['1D', '1W']}
2025-06-21 17:27:57,232 - app.utils.historical_data_downloader - INFO - API response status: 500
2025-06-21 17:27:57,232 - app.utils.historical_data_downloader - ERROR - API request failed with status 500
2025-06-21 17:27:57,232 - app.utils.historical_data_downloader - ERROR - Response text: {"detail":"Error scraping data: Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"https://tradingview.com/symbols/EGX-SWDY/technicals/\", waiting until \"domcontentloaded\"\n"}...
2025-06-21 17:27:57,233 - app.utils.historical_data_downloader - ERROR - Could not get current data for SWDY
2025-06-21 17:27:57,233 - app.utils.historical_data_downloader - ERROR - This could be due to:
2025-06-21 17:27:57,233 - app.utils.historical_data_downloader - ERROR - 1. Stock symbol not found on EGX
2025-06-21 17:27:57,233 - app.utils.historical_data_downloader - ERROR - 2. API server connection issues
2025-06-21 17:27:57,234 - app.utils.historical_data_downloader - ERROR - 3. Invalid stock symbol format
2025-06-21 17:27:57,290 - app.utils.memory_management - INFO - Memory before cleanup: 435.02 MB
2025-06-21 17:27:57,477 - app.utils.memory_management - INFO - Garbage collection: collected 245 objects
2025-06-21 17:27:57,477 - app.utils.memory_management - INFO - Memory after cleanup: 435.02 MB (freed 0.00 MB)
2025-06-21 17:28:30,921 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:28:30,956 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 17:28:30,963 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 17:28:31,063 - app.utils.memory_management - INFO - Memory before cleanup: 435.02 MB
2025-06-21 17:28:31,253 - app.utils.memory_management - INFO - Garbage collection: collected 257 objects
2025-06-21 17:28:31,256 - app.utils.memory_management - INFO - Memory after cleanup: 435.02 MB (freed 0.00 MB)
2025-06-21 17:28:38,616 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:28:38,646 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 17:28:38,652 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 17:28:38,700 - app.utils.historical_data_downloader - INFO - Starting historical data generation for SWDY (3 years)
2025-06-21 17:28:38,701 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-21 17:28:38,712 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-21 17:28:38,715 - app.utils.historical_data_downloader - INFO - Fetching current live data for SWDY...
2025-06-21 17:28:38,717 - app.utils.historical_data_downloader - INFO - Downloading historical data for SWDY with intervals: ['1D', '1W']
2025-06-21 17:28:38,720 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-21 17:28:38,724 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-SWDY'], 'intervals': ['1D', '1W']}
2025-06-21 17:29:09,740 - app.utils.historical_data_downloader - INFO - API response status: 500
2025-06-21 17:29:09,740 - app.utils.historical_data_downloader - ERROR - API request failed with status 500
2025-06-21 17:29:09,740 - app.utils.historical_data_downloader - ERROR - Response text: {"detail":"Error scraping data: Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"https://tradingview.com/symbols/EGX-SWDY/technicals/\", waiting until \"domcontentloaded\"\n"}...
2025-06-21 17:29:09,740 - app.utils.historical_data_downloader - ERROR - Could not get current data for SWDY
2025-06-21 17:29:09,740 - app.utils.historical_data_downloader - ERROR - This could be due to:
2025-06-21 17:29:09,740 - app.utils.historical_data_downloader - ERROR - 1. Stock symbol not found on EGX
2025-06-21 17:29:09,740 - app.utils.historical_data_downloader - ERROR - 2. API server connection issues
2025-06-21 17:29:09,740 - app.utils.historical_data_downloader - ERROR - 3. Invalid stock symbol format
2025-06-21 17:29:09,796 - app.utils.memory_management - INFO - Memory before cleanup: 435.02 MB
2025-06-21 17:29:09,957 - app.utils.memory_management - INFO - Garbage collection: collected 245 objects
2025-06-21 17:29:09,957 - app.utils.memory_management - INFO - Memory after cleanup: 435.02 MB (freed 0.00 MB)
2025-06-21 17:30:10,486 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:30:10,529 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 17:30:10,537 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 17:30:10,577 - app.utils.historical_data_downloader - INFO - Starting historical data generation for SWDY (3 years)
2025-06-21 17:30:10,578 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-21 17:30:10,594 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-21 17:30:10,597 - app.utils.historical_data_downloader - INFO - Fetching current live data for SWDY...
2025-06-21 17:30:10,598 - app.utils.historical_data_downloader - INFO - Downloading historical data for SWDY with intervals: ['1D', '1W']
2025-06-21 17:30:10,599 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-21 17:30:10,602 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-SWDY'], 'intervals': ['1D', '1W']}
2025-06-21 17:30:20,462 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-06-21 17:30:20,462 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-SWDY': [{'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78872.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 79316.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 79589.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 79754.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 79833.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 80816.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 75164.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 80556.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78150.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 78960.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 73646.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 91617.0, 'fibo': 86317.0, 'camarilla': 83208.0, 'woodie': 89700.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 86317.0, 'fibo': 84292.0, 'camarilla': 82722.0, 'woodie': 86500.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 84033.0, 'fibo': 83041.0, 'camarilla': 82236.0, 'woodie': 84400.0, 'dm': 82525.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 81017.0, 'fibo': 81017.0, 'camarilla': 81017.0, 'woodie': 81200.0, 'dm': 80263.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 78733.0, 'fibo': 78992.0, 'camarilla': 81264.0, 'woodie': 79100.0, 'dm': 77225.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': 75717.0, 'fibo': 77741.0, 'camarilla': 80778.0, 'woodie': 75900.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': 70417.0, 'fibo': 75717.0, 'camarilla': 80293.0, 'woodie': 73800.0, 'dm': None}]}, {'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78380.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 81187.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 72994.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 76794.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 58841.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 53820.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 42200.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 32127.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80500.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 80569.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 77549.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 245977.0, 'fibo': 159787.0, 'camarilla': 100702.0, 'woodie': 205165.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 159787.0, 'fibo': 126862.0, 'camarilla': 92802.0, 'woodie': 160078.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 118393.0, 'fibo': 106521.0, 'camarilla': 84901.0, 'woodie': 118975.0, 'dm': 139090.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 73597.0, 'fibo': 73597.0, 'camarilla': 73597.0, 'woodie': 73887.0, 'dm': 83945.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 32203.0, 'fibo': 40672.0, 'camarilla': 69099.0, 'woodie': 32785.0, 'dm': 52900.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': None, 'fibo': 20331.0, 'camarilla': 61198.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': None, 'fibo': None, 'camarilla': 53298.0, 'woodie': None, 'dm': None}]}]}, 'message': 'Successfully scraped 1 pairs'}
2025-06-21 17:30:20,463 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-SWDY']
2025-06-21 17:30:20,463 - app.utils.historical_data_downloader - INFO - Found data for EGX-SWDY: [{'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78872.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 79316.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 79589.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 79754.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 79833.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 80816.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 75164.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 80556.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78150.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 78960.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 73646.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 91617.0, 'fibo': 86317.0, 'camarilla': 83208.0, 'woodie': 89700.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 86317.0, 'fibo': 84292.0, 'camarilla': 82722.0, 'woodie': 86500.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 84033.0, 'fibo': 83041.0, 'camarilla': 82236.0, 'woodie': 84400.0, 'dm': 82525.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 81017.0, 'fibo': 81017.0, 'camarilla': 81017.0, 'woodie': 81200.0, 'dm': 80263.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 78733.0, 'fibo': 78992.0, 'camarilla': 81264.0, 'woodie': 79100.0, 'dm': 77225.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': 75717.0, 'fibo': 77741.0, 'camarilla': 80778.0, 'woodie': 75900.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': 70417.0, 'fibo': 75717.0, 'camarilla': 80293.0, 'woodie': 73800.0, 'dm': None}]}, {'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78380.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 81187.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 72994.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 76794.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 58841.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 53820.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 42200.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 32127.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80500.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 80569.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 77549.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 245977.0, 'fibo': 159787.0, 'camarilla': 100702.0, 'woodie': 205165.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 159787.0, 'fibo': 126862.0, 'camarilla': 92802.0, 'woodie': 160078.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 118393.0, 'fibo': 106521.0, 'camarilla': 84901.0, 'woodie': 118975.0, 'dm': 139090.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 73597.0, 'fibo': 73597.0, 'camarilla': 73597.0, 'woodie': 73887.0, 'dm': 83945.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 32203.0, 'fibo': 40672.0, 'camarilla': 69099.0, 'woodie': 32785.0, 'dm': 52900.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': None, 'fibo': 20331.0, 'camarilla': 61198.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': None, 'fibo': None, 'camarilla': 53298.0, 'woodie': None, 'dm': None}]}]
2025-06-21 17:30:20,463 - app.utils.historical_data_downloader - INFO - Processing 2 data points for SWDY
2025-06-21 17:30:20,463 - app.utils.historical_data_downloader - INFO - Raw data structure: [{'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78872.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 79316.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 79589.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 79754.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 79833.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 80816.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 75164.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 80556.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78150.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 78960.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 73646.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 91617.0, 'fibo': 86317.0, 'camarilla': 83208.0, 'woodie': 89700.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 86317.0, 'fibo': 84292.0, 'camarilla': 82722.0, 'woodie': 86500.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 84033.0, 'fibo': 83041.0, 'camarilla': 82236.0, 'woodie': 84400.0, 'dm': 82525.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 81017.0, 'fibo': 81017.0, 'camarilla': 81017.0, 'woodie': 81200.0, 'dm': 80263.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 78733.0, 'fibo': 78992.0, 'camarilla': 81264.0, 'woodie': 79100.0, 'dm': 77225.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': 75717.0, 'fibo': 77741.0, 'camarilla': 80778.0, 'woodie': 75900.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': 70417.0, 'fibo': 75717.0, 'camarilla': 80293.0, 'woodie': 73800.0, 'dm': None}]}, {'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78380.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 81187.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 72994.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 76794.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 58841.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 53820.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 42200.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 32127.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80500.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 80569.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 77549.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 245977.0, 'fibo': 159787.0, 'camarilla': 100702.0, 'woodie': 205165.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 159787.0, 'fibo': 126862.0, 'camarilla': 92802.0, 'woodie': 160078.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 118393.0, 'fibo': 106521.0, 'camarilla': 84901.0, 'woodie': 118975.0, 'dm': 139090.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 73597.0, 'fibo': 73597.0, 'camarilla': 73597.0, 'woodie': 73887.0, 'dm': 83945.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 32203.0, 'fibo': 40672.0, 'camarilla': 69099.0, 'woodie': 32785.0, 'dm': 52900.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': None, 'fibo': 20331.0, 'camarilla': 61198.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': None, 'fibo': None, 'camarilla': 53298.0, 'woodie': None, 'dm': None}]}]
2025-06-21 17:30:20,465 - app.utils.historical_data_downloader - INFO - First item structure: {'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78872.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 79316.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 79589.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 79754.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 79833.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 80816.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 75164.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 80556.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78150.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 78960.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 73646.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 91617.0, 'fibo': 86317.0, 'camarilla': 83208.0, 'woodie': 89700.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 86317.0, 'fibo': 84292.0, 'camarilla': 82722.0, 'woodie': 86500.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 84033.0, 'fibo': 83041.0, 'camarilla': 82236.0, 'woodie': 84400.0, 'dm': 82525.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 81017.0, 'fibo': 81017.0, 'camarilla': 81017.0, 'woodie': 81200.0, 'dm': 80263.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 78733.0, 'fibo': 78992.0, 'camarilla': 81264.0, 'woodie': 79100.0, 'dm': 77225.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': 75717.0, 'fibo': 77741.0, 'camarilla': 80778.0, 'woodie': 75900.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': 70417.0, 'fibo': 75717.0, 'camarilla': 80293.0, 'woodie': 73800.0, 'dm': None}]}
2025-06-21 17:30:20,465 - app.utils.historical_data_downloader - INFO - Processing interval 1D: pair=EGX-SWDY, price=73000.0
2025-06-21 17:30:20,465 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 73000.0 -> 73.0 EGP
2025-06-21 17:30:20,467 - app.utils.historical_data_downloader - INFO - Successfully processed 1D: price=73.0 EGP
2025-06-21 17:30:20,467 - app.utils.historical_data_downloader - INFO - Processing interval 1W: pair=EGX-SWDY, price=73000.0
2025-06-21 17:30:20,467 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 73000.0 -> 73.0 EGP
2025-06-21 17:30:20,467 - app.utils.historical_data_downloader - INFO - Successfully processed 1W: price=73.0 EGP
2025-06-21 17:30:20,467 - app.utils.historical_data_downloader - INFO - Successfully processed 2 intervals for SWDY
2025-06-21 17:30:20,467 - app.utils.historical_data_downloader - INFO - Successfully processed historical data for SWDY
2025-06-21 17:30:20,467 - app.utils.historical_data_downloader - INFO - Successfully retrieved current data for SWDY: {'1D': {'symbol': 'SWDY', 'interval': '1D', 'current_price': 73.0, 'timestamp': datetime.datetime(2025, 6, 21, 17, 30, 20, 467721), 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78872.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 79316.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 79589.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 79754.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 79833.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 80816.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 75164.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 80556.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78150.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 78960.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 73646.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 91617.0, 'fibo': 86317.0, 'camarilla': 83208.0, 'woodie': 89700.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 86317.0, 'fibo': 84292.0, 'camarilla': 82722.0, 'woodie': 86500.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 84033.0, 'fibo': 83041.0, 'camarilla': 82236.0, 'woodie': 84400.0, 'dm': 82525.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 81017.0, 'fibo': 81017.0, 'camarilla': 81017.0, 'woodie': 81200.0, 'dm': 80263.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 78733.0, 'fibo': 78992.0, 'camarilla': 81264.0, 'woodie': 79100.0, 'dm': 77225.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': 75717.0, 'fibo': 77741.0, 'camarilla': 80778.0, 'woodie': 75900.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': 70417.0, 'fibo': 75717.0, 'camarilla': 80293.0, 'woodie': 73800.0, 'dm': None}], 'raw_data': {'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78872.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 79316.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 79589.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 79754.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 79833.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 80816.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 75164.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 80556.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78150.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 78960.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 73646.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 91617.0, 'fibo': 86317.0, 'camarilla': 83208.0, 'woodie': 89700.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 86317.0, 'fibo': 84292.0, 'camarilla': 82722.0, 'woodie': 86500.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 84033.0, 'fibo': 83041.0, 'camarilla': 82236.0, 'woodie': 84400.0, 'dm': 82525.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 81017.0, 'fibo': 81017.0, 'camarilla': 81017.0, 'woodie': 81200.0, 'dm': 80263.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 78733.0, 'fibo': 78992.0, 'camarilla': 81264.0, 'woodie': 79100.0, 'dm': 77225.0}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': 75717.0, 'fibo': 77741.0, 'camarilla': 80778.0, 'woodie': 75900.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1D', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': 70417.0, 'fibo': 75717.0, 'camarilla': 80293.0, 'woodie': 73800.0, 'dm': None}]}}, '1W': {'symbol': 'SWDY', 'interval': '1W', 'current_price': 73.0, 'timestamp': datetime.datetime(2025, 6, 21, 17, 30, 20, 467721), 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78380.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 81187.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 72994.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 76794.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 58841.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 53820.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 42200.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 32127.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80500.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 80569.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 77549.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 245977.0, 'fibo': 159787.0, 'camarilla': 100702.0, 'woodie': 205165.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 159787.0, 'fibo': 126862.0, 'camarilla': 92802.0, 'woodie': 160078.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 118393.0, 'fibo': 106521.0, 'camarilla': 84901.0, 'woodie': 118975.0, 'dm': 139090.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 73597.0, 'fibo': 73597.0, 'camarilla': 73597.0, 'woodie': 73887.0, 'dm': 83945.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 32203.0, 'fibo': 40672.0, 'camarilla': 69099.0, 'woodie': 32785.0, 'dm': 52900.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': None, 'fibo': 20331.0, 'camarilla': 61198.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': None, 'fibo': None, 'camarilla': 53298.0, 'woodie': None, 'dm': None}], 'raw_data': {'pair': 'EGX-SWDY', 'price': 73000.0, 'oscillators': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Relative Strength Index (14)', 'value': 27393.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic %K (14, 3, 3)', 'value': 6092.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Commodity Channel Index (20)', 'value': -201028.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Average Directional Index (14)', 'value': 24941.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Awesome Oscillator', 'value': -3799.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Momentum (10)', 'value': -7600.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'MACD Level (12, 26)', 'value': -1384.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': -0.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Williams Percent Range (14)', 'value': -100000.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Bull Bear Power', 'value': -6469.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 26168.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (10)', 'value': 76883.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (10)', 'value': 77726.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (20)', 'value': 78230.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (20)', 'value': 78868.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (30)', 'value': 78380.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (30)', 'value': 81187.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (50)', 'value': 72994.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (50)', 'value': 76794.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (100)', 'value': 58841.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (100)', 'value': 53820.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Exponential Moving Average (200)', 'value': 42200.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Simple Moving Average (200)', 'value': 32127.0, 'action': 'Buy'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80500.0, 'action': 'Neutral'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Volume Weighted Moving Average (20)', 'value': 80569.0, 'action': 'Sell'}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'name': 'Hull Moving Average (9)', 'value': 77549.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R3', 'classic': 245977.0, 'fibo': 159787.0, 'camarilla': 100702.0, 'woodie': 205165.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R2', 'classic': 159787.0, 'fibo': 126862.0, 'camarilla': 92802.0, 'woodie': 160078.0, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'R1', 'classic': 118393.0, 'fibo': 106521.0, 'camarilla': 84901.0, 'woodie': 118975.0, 'dm': 139090.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'P', 'classic': 73597.0, 'fibo': 73597.0, 'camarilla': 73597.0, 'woodie': 73887.0, 'dm': 83945.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S1', 'classic': 32203.0, 'fibo': 40672.0, 'camarilla': 69099.0, 'woodie': 32785.0, 'dm': 52900.0}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S2', 'classic': None, 'fibo': 20331.0, 'camarilla': 61198.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SWDY', 'interval': '1W', 'register_time': '21/06/2025 17:30', 'pivot': 'S3', 'classic': None, 'fibo': None, 'camarilla': 53298.0, 'woodie': None, 'dm': None}]}}}
2025-06-21 17:30:20,469 - app.utils.historical_data_downloader - INFO - Found valid price from 1D: 73.0 EGP
2025-06-21 17:30:20,471 - app.utils.historical_data_downloader - INFO - Using current price for SWDY: 73.0 EGP
2025-06-21 17:30:20,472 - app.utils.historical_data_downloader - INFO - Generating 3 years of synthetic historical data...
2025-06-21 17:30:20,472 - app.utils.historical_data_downloader - INFO - Generating 3 years of synthetic data for SWDY
2025-06-21 17:30:20,509 - app.utils.historical_data_downloader - INFO - Generated 750 days of synthetic historical data for SWDY
2025-06-21 17:30:20,509 - app.utils.historical_data_downloader - INFO - Generated 750 days of synthetic data
2025-06-21 17:30:20,509 - app.utils.historical_data_downloader - INFO - Saving data to: data\stocks\SWDY.csv
2025-06-21 17:30:20,516 - app.utils.historical_data_downloader - INFO - Saved 750 days of historical data to data\stocks\SWDY.csv
2025-06-21 17:30:20,517 - app.utils.historical_data_downloader - INFO - CSV file size: 33211 bytes
2025-06-21 17:30:20,531 - app.utils.historical_data_downloader - INFO - Verification: CSV contains 750 rows
2025-06-21 17:30:20,533 - app.utils.historical_data_downloader - INFO - Successfully completed historical data generation for SWDY
2025-06-21 17:30:20,602 - app.utils.memory_management - INFO - Memory before cleanup: 435.47 MB
2025-06-21 17:30:20,798 - app.utils.memory_management - INFO - Garbage collection: collected 257 objects
2025-06-21 17:30:20,798 - app.utils.memory_management - INFO - Memory after cleanup: 435.47 MB (freed 0.00 MB)
2025-06-21 17:30:34,503 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:30:34,534 - app.utils.memory_management - INFO - Memory before cleanup: 436.16 MB
2025-06-21 17:30:34,767 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-21 17:30:34,768 - app.utils.memory_management - INFO - Memory after cleanup: 436.16 MB (freed 0.00 MB)
2025-06-21 17:30:44,746 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:30:44,918 - app - INFO - File SWDY contains 2025 data
2025-06-21 17:30:44,933 - app - INFO - Saved data to data/stocks\SWDY.csv
2025-06-21 17:30:44,937 - app - INFO - Date range: 2022-08-08 to 2025-06-20
2025-06-21 17:30:44,945 - app.utils.memory_management - INFO - Memory before cleanup: 436.44 MB
2025-06-21 17:30:45,483 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-21 17:30:45,484 - app.utils.memory_management - INFO - Memory after cleanup: 436.44 MB (freed 0.00 MB)
2025-06-21 17:30:51,795 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:30:51,932 - app - INFO - File SWDY contains 2025 data
2025-06-21 17:30:51,948 - app - INFO - Saved data to data/stocks\SWDY.csv
2025-06-21 17:30:51,948 - app - INFO - Date range: 2022-08-08 to 2025-06-20
2025-06-21 17:30:51,956 - app.utils.memory_management - INFO - Memory before cleanup: 436.50 MB
2025-06-21 17:30:52,344 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-21 17:30:52,345 - app.utils.memory_management - INFO - Memory after cleanup: 436.50 MB (freed 0.00 MB)
2025-06-21 17:30:54,700 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:30:54,798 - app - INFO - File SWDY contains 2025 data
2025-06-21 17:30:54,816 - app - INFO - Saved data to data/stocks\SWDY.csv
2025-06-21 17:30:54,819 - app - INFO - Date range: 2022-08-08 to 2025-06-20
2025-06-21 17:30:54,826 - app.utils.memory_management - INFO - Memory before cleanup: 436.55 MB
2025-06-21 17:30:55,105 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-06-21 17:30:55,107 - app.utils.memory_management - INFO - Memory after cleanup: 436.55 MB (freed 0.00 MB)
2025-06-21 17:30:55,889 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:30:55,984 - app - INFO - File SWDY contains 2025 data
2025-06-21 17:30:55,993 - app - INFO - Saved data to data/stocks\SWDY.csv
2025-06-21 17:30:55,995 - app - INFO - Date range: 2022-08-08 to 2025-06-20
2025-06-21 17:30:55,997 - app.utils.memory_management - INFO - Memory before cleanup: 436.56 MB
2025-06-21 17:30:56,213 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-21 17:30:56,214 - app.utils.memory_management - INFO - Memory after cleanup: 436.56 MB (freed 0.00 MB)
2025-06-21 17:30:58,576 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:30:58,736 - app - INFO - File SWDY contains 2025 data
2025-06-21 17:30:58,777 - app - INFO - Saved data to data/stocks\SWDY.csv
2025-06-21 17:30:58,778 - app - INFO - Date range: 2022-08-08 to 2025-06-20
2025-06-21 17:30:58,779 - app.utils.memory_management - INFO - Memory before cleanup: 437.22 MB
2025-06-21 17:30:58,965 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-21 17:30:58,965 - app.utils.memory_management - INFO - Memory after cleanup: 437.22 MB (freed 0.00 MB)
2025-06-21 17:31:02,231 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:31:02,364 - app - INFO - File SWDY contains 2025 data
2025-06-21 17:31:02,408 - app - INFO - Saved data to data/stocks\SWDY.csv
2025-06-21 17:31:02,409 - app - INFO - Date range: 2022-08-08 to 2025-06-20
2025-06-21 17:31:02,411 - app.utils.memory_management - INFO - Memory before cleanup: 437.53 MB
2025-06-21 17:31:02,601 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-21 17:31:02,601 - app.utils.memory_management - INFO - Memory after cleanup: 437.53 MB (freed 0.00 MB)
2025-06-21 17:31:07,770 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:31:07,802 - app.utils.memory_management - INFO - Memory before cleanup: 437.54 MB
2025-06-21 17:31:08,041 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-06-21 17:31:08,043 - app.utils.memory_management - INFO - Memory after cleanup: 437.54 MB (freed 0.00 MB)
2025-06-21 17:31:10,756 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:31:10,816 - app.utils.memory_management - INFO - Memory before cleanup: 437.54 MB
2025-06-21 17:31:11,074 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-06-21 17:31:11,076 - app.utils.memory_management - INFO - Memory after cleanup: 437.54 MB (freed 0.00 MB)
2025-06-21 17:31:14,770 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:31:14,804 - app.utils.memory_management - INFO - Memory before cleanup: 437.54 MB
2025-06-21 17:31:15,009 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-06-21 17:31:15,009 - app.utils.memory_management - INFO - Memory after cleanup: 437.54 MB (freed 0.00 MB)
2025-06-21 17:31:16,668 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:31:27,458 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for SWDY
2025-06-21 17:31:27,538 - app.utils.memory_management - INFO - Memory before cleanup: 438.71 MB
2025-06-21 17:31:27,722 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 17:31:27,723 - app.utils.memory_management - INFO - Memory after cleanup: 438.71 MB (freed 0.00 MB)
2025-06-21 17:36:24,979 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:36:25,016 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 17:36:25,043 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 7 days
2025-06-21 17:36:25,052 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.083
2025-06-21 17:36:25,140 - app.utils.memory_management - INFO - Memory before cleanup: 438.80 MB
2025-06-21 17:36:25,354 - app.utils.memory_management - INFO - Garbage collection: collected 641 objects
2025-06-21 17:36:25,356 - app.utils.memory_management - INFO - Memory after cleanup: 438.80 MB (freed 0.00 MB)
2025-06-21 17:36:42,067 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:36:42,228 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 17:36:42,587 - app.utils.memory_management - INFO - Memory before cleanup: 450.02 MB
2025-06-21 17:36:42,804 - app.utils.memory_management - INFO - Garbage collection: collected 707 objects
2025-06-21 17:36:42,806 - app.utils.memory_management - INFO - Memory after cleanup: 450.01 MB (freed 0.02 MB)
2025-06-21 17:40:21,528 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:40:21,588 - app.utils.memory_management - INFO - Memory before cleanup: 451.31 MB
2025-06-21 17:40:21,805 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-21 17:40:21,806 - app.utils.memory_management - INFO - Memory after cleanup: 451.31 MB (freed 0.00 MB)
2025-06-21 17:40:24,870 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:40:35,273 - app.pages.advanced_technical_analysis - INFO - Loaded 250 days of historical data for ABUK
2025-06-21 17:40:35,336 - app.utils.memory_management - INFO - Memory before cleanup: 451.37 MB
2025-06-21 17:40:35,526 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-21 17:40:35,528 - app.utils.memory_management - INFO - Memory after cleanup: 451.37 MB (freed 0.00 MB)
2025-06-21 17:46:59,459 - app - INFO - Cleaning up resources...
2025-06-21 17:46:59,459 - app.utils.memory_management - INFO - Memory before cleanup: 445.39 MB
2025-06-21 17:46:59,651 - app.utils.memory_management - INFO - Garbage collection: collected 338 objects
2025-06-21 17:46:59,653 - app.utils.memory_management - INFO - Memory after cleanup: 445.39 MB (freed 0.00 MB)
2025-06-21 17:46:59,655 - app - INFO - Application shutdown complete
2025-06-21 17:50:33,427 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 17:50:34,021 - app - INFO - Memory management utilities loaded
2025-06-21 17:50:34,023 - app - INFO - Error handling utilities loaded
2025-06-21 17:50:34,023 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 17:50:34,025 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 17:50:34,025 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 17:50:34,025 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 17:50:34,025 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 17:50:34,025 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 17:50:34,025 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 17:50:34,025 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 17:50:34,025 - app - INFO - Applied NumPy fix
2025-06-21 17:50:34,029 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 17:50:34,029 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 17:50:34,029 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 17:50:34,029 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 17:50:34,030 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 17:50:34,030 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 17:50:34,030 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 17:50:34,030 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 17:50:37,577 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 17:50:37,577 - app - INFO - Applied TensorFlow fix
2025-06-21 17:50:39,105 - app - INFO - Cleaning up resources...
2025-06-21 17:50:39,107 - app.utils.memory_management - INFO - Memory before cleanup: 371.39 MB
2025-06-21 17:50:39,247 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-06-21 17:50:39,247 - app.utils.memory_management - INFO - Memory after cleanup: 371.71 MB (freed -0.32 MB)
2025-06-21 17:50:39,247 - app - INFO - Application shutdown complete
2025-06-21 17:51:36,516 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 17:51:37,258 - app - INFO - Memory management utilities loaded
2025-06-21 17:51:37,277 - app - INFO - Error handling utilities loaded
2025-06-21 17:51:37,283 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 17:51:37,314 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 17:51:37,318 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 17:51:37,328 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 17:51:37,330 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 17:51:37,331 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 17:51:37,331 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 17:51:37,331 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 17:51:37,332 - app - INFO - Applied NumPy fix
2025-06-21 17:51:37,333 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 17:51:37,333 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 17:51:37,334 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 17:51:37,334 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 17:51:37,335 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 17:51:37,340 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 17:51:37,342 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 17:51:37,343 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 17:51:40,752 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 17:51:40,752 - app - INFO - Applied TensorFlow fix
2025-06-21 17:51:42,088 - app - INFO - Cleaning up resources...
2025-06-21 17:51:42,088 - app.utils.memory_management - INFO - Memory before cleanup: 372.48 MB
2025-06-21 17:51:42,214 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-06-21 17:51:42,214 - app.utils.memory_management - INFO - Memory after cleanup: 372.49 MB (freed -0.01 MB)
2025-06-21 17:51:42,214 - app - INFO - Application shutdown complete
2025-06-21 17:52:57,860 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 17:52:59,579 - app - INFO - Memory management utilities loaded
2025-06-21 17:52:59,581 - app - INFO - Error handling utilities loaded
2025-06-21 17:52:59,582 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 17:52:59,584 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 17:52:59,584 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 17:52:59,584 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 17:52:59,585 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 17:52:59,586 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 17:52:59,586 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 17:52:59,586 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 17:52:59,586 - app - INFO - Applied NumPy fix
2025-06-21 17:52:59,587 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 17:52:59,587 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 17:52:59,588 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 17:52:59,588 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 17:52:59,588 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 17:52:59,588 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 17:52:59,588 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 17:52:59,589 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 17:53:03,068 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 17:53:03,070 - app - INFO - Applied TensorFlow fix
2025-06-21 17:53:03,074 - app.config - INFO - Configuration initialized
2025-06-21 17:53:03,079 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 17:53:03,089 - models.train - INFO - TensorFlow test successful
2025-06-21 17:53:03,531 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 17:53:03,531 - models.train - INFO - Transformer model is available
2025-06-21 17:53:03,531 - models.train - INFO - Using TensorFlow-based models
2025-06-21 17:53:03,533 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 17:53:03,533 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 17:53:03,535 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 17:53:03,834 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 17:53:03,834 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 17:53:03,834 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 17:53:03,834 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 17:53:03,834 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 17:53:03,836 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 17:53:03,836 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 17:53:03,836 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 17:53:03,836 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 17:53:03,836 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 17:53:03,928 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 17:53:03,930 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:53:04,257 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 17:53:04,792 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 17:53:04,939 - app.utils.session_state - INFO - Initializing session state
2025-06-21 17:53:04,941 - app.utils.session_state - INFO - Session state initialized
2025-06-21 17:53:06,091 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 17:53:06,105 - app.utils.memory_management - INFO - Memory before cleanup: 431.05 MB
2025-06-21 17:53:06,283 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 17:53:06,285 - app.utils.memory_management - INFO - Memory after cleanup: 431.06 MB (freed -0.01 MB)
2025-06-21 17:53:12,865 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:53:12,915 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-21 17:53:12,924 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.306
2025-06-21 17:53:13,010 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 17:53:13,145 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 17:53:13,147 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-21 17:53:13,148 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 17:53:13,148 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (52.2)
2025-06-21 17:53:13,149 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 17:53:25,381 - app.utils.ai_pattern_recognition - INFO - Using live price 45.97 EGP from API for ABUK
2025-06-21 17:53:25,383 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-21 17:53:25,383 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-21 17:53:25,383 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 17:53:25,383 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 17:53:26,421 - app.utils.memory_management - INFO - Memory before cleanup: 456.13 MB
2025-06-21 17:53:26,616 - app.utils.memory_management - INFO - Garbage collection: collected 893 objects
2025-06-21 17:53:26,617 - app.utils.memory_management - INFO - Memory after cleanup: 456.13 MB (freed 0.00 MB)
2025-06-21 17:53:47,553 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:53:47,614 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-21 17:53:47,632 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.002
2025-06-21 17:53:47,726 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 17:53:47,902 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 17:53:47,902 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-21 17:53:47,904 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 17:53:47,904 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (59.1)
2025-06-21 17:53:47,904 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 17:53:56,710 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-21 17:53:56,711 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-21 17:53:56,711 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-21 17:53:56,712 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 17:53:56,714 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 17:53:56,872 - app.utils.memory_management - INFO - Memory before cleanup: 460.93 MB
2025-06-21 17:53:57,055 - app.utils.memory_management - INFO - Garbage collection: collected 1694 objects
2025-06-21 17:53:57,056 - app.utils.memory_management - INFO - Memory after cleanup: 460.97 MB (freed -0.04 MB)
2025-06-21 17:56:17,472 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:56:17,543 - app.utils.memory_management - INFO - Memory before cleanup: 460.97 MB
2025-06-21 17:56:17,819 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-06-21 17:56:17,820 - app.utils.memory_management - INFO - Memory after cleanup: 460.97 MB (freed 0.00 MB)
2025-06-21 17:56:26,999 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 17:56:27,039 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-21 17:56:27,049 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.009
2025-06-21 17:56:27,144 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 17:56:27,220 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 17:56:27,222 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (20.99%) exceeds limit (15.00%)
2025-06-21 17:56:27,222 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 17:56:27,222 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (55.3)
2025-06-21 17:56:27,223 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 17:56:37,146 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-21 17:56:37,146 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-21 17:56:37,146 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-21 17:56:37,149 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 17:56:37,149 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 17:56:37,311 - app.utils.memory_management - INFO - Memory before cleanup: 462.26 MB
2025-06-21 17:56:37,540 - app.utils.memory_management - INFO - Garbage collection: collected 1649 objects
2025-06-21 17:56:37,541 - app.utils.memory_management - INFO - Memory after cleanup: 462.26 MB (freed 0.00 MB)
2025-06-21 21:20:01,883 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:20:01,919 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 21:20:01,983 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-21 21:20:01,995 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.100
2025-06-21 21:20:02,149 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 21:20:02,234 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 21:20:02,236 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-21 21:20:02,236 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 21:20:02,236 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (63.6)
2025-06-21 21:20:02,236 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 21:20:22,271 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-21 21:20:22,271 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-21 21:20:22,271 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-21 21:20:22,271 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 21:20:22,275 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 21:20:22,446 - app.utils.memory_management - INFO - Memory before cleanup: 224.55 MB
2025-06-21 21:20:22,784 - app.utils.memory_management - INFO - Garbage collection: collected 1749 objects
2025-06-21 21:20:22,785 - app.utils.memory_management - INFO - Memory after cleanup: 396.44 MB (freed -171.89 MB)
2025-06-21 21:24:49,078 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:24:49,220 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 21:24:49,632 - app.utils.memory_management - INFO - Memory before cleanup: 398.38 MB
2025-06-21 21:24:49,852 - app.utils.memory_management - INFO - Garbage collection: collected 742 objects
2025-06-21 21:24:49,853 - app.utils.memory_management - INFO - Memory after cleanup: 398.39 MB (freed -0.01 MB)
2025-06-21 21:34:02,317 - app - INFO - Cleaning up resources...
2025-06-21 21:34:02,317 - app.utils.memory_management - INFO - Memory before cleanup: 401.32 MB
2025-06-21 21:34:02,512 - app.utils.memory_management - INFO - Garbage collection: collected 276 objects
2025-06-21 21:34:02,512 - app.utils.memory_management - INFO - Memory after cleanup: 401.31 MB (freed 0.01 MB)
2025-06-21 21:34:02,512 - app - INFO - Application shutdown complete
2025-06-21 21:36:34,532 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-21 21:36:38,616 - app - INFO - Memory management utilities loaded
2025-06-21 21:36:38,619 - app - INFO - Error handling utilities loaded
2025-06-21 21:36:38,621 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-21 21:36:38,622 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-21 21:36:38,623 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-21 21:36:38,623 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-21 21:36:38,624 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-21 21:36:38,624 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-21 21:36:38,625 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-21 21:36:38,625 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-21 21:36:38,625 - app - INFO - Applied NumPy fix
2025-06-21 21:36:38,626 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 21:36:38,627 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 21:36:38,627 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 21:36:38,627 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-21 21:36:38,628 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 21:36:38,628 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 21:36:38,628 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 21:36:38,629 - app - INFO - Applied NumPy BitGenerator fix
2025-06-21 21:36:55,499 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-21 21:36:55,499 - app - INFO - Applied TensorFlow fix
2025-06-21 21:36:55,502 - app.config - INFO - Configuration initialized
2025-06-21 21:36:55,508 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-21 21:36:55,520 - models.train - INFO - TensorFlow test successful
2025-06-21 21:37:00,427 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-21 21:37:00,428 - models.train - INFO - Transformer model is available
2025-06-21 21:37:00,428 - models.train - INFO - Using TensorFlow-based models
2025-06-21 21:37:00,430 - models.predict - INFO - Transformer model is available for predictions
2025-06-21 21:37:00,430 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-21 21:37:00,433 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-21 21:37:02,143 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 21:37:02,144 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-21 21:37:02,144 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-21 21:37:02,144 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-21 21:37:02,144 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-21 21:37:02,144 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-21 21:37:02,145 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-21 21:37:02,145 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-21 21:37:02,145 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-21 21:37:02,145 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-21 21:37:02,415 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-21 21:37:02,418 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:37:02,835 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-21 21:37:04,984 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-21 21:37:05,188 - app.utils.session_state - INFO - Initializing session state
2025-06-21 21:37:05,190 - app.utils.session_state - INFO - Session state initialized
2025-06-21 21:37:06,465 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 21:37:06,488 - app.utils.memory_management - INFO - Memory before cleanup: 433.55 MB
2025-06-21 21:37:06,756 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-21 21:37:06,756 - app.utils.memory_management - INFO - Memory after cleanup: 433.56 MB (freed -0.01 MB)
2025-06-21 21:37:12,593 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:37:12,696 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-21 21:37:12,714 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.465
2025-06-21 21:37:12,802 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 21:37:12,946 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 21:37:12,951 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-21 21:37:12,995 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 21:37:12,998 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (53.7)
2025-06-21 21:37:13,003 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 21:37:25,607 - app.utils.ai_pattern_recognition - INFO - Using live price 45.97 EGP from API for ABUK
2025-06-21 21:37:25,607 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-21 21:37:25,607 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-21 21:37:25,611 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 21:37:25,615 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 21:37:26,657 - app.utils.memory_management - INFO - Memory before cleanup: 457.24 MB
2025-06-21 21:37:26,892 - app.utils.memory_management - INFO - Garbage collection: collected 901 objects
2025-06-21 21:37:26,894 - app.utils.memory_management - INFO - Memory after cleanup: 457.24 MB (freed 0.00 MB)
2025-06-21 21:37:48,668 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:37:48,737 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-21 21:37:48,758 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.060
2025-06-21 21:37:48,895 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 21:37:49,054 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 21:37:49,055 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-21 21:37:49,055 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 21:37:49,055 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (61.1)
2025-06-21 21:37:49,055 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 21:37:56,924 - app.utils.ai_pattern_recognition - INFO - Using live price 78.62 EGP from API for COMI
2025-06-21 21:37:56,925 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-21 21:37:56,925 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-21 21:37:56,925 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 21:37:56,925 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 21:37:57,083 - app.utils.memory_management - INFO - Memory before cleanup: 463.14 MB
2025-06-21 21:37:57,311 - app.utils.memory_management - INFO - Garbage collection: collected 1694 objects
2025-06-21 21:37:57,311 - app.utils.memory_management - INFO - Memory after cleanup: 463.18 MB (freed -0.04 MB)
2025-06-21 21:38:19,436 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:38:19,545 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for SWDY over 30 days
2025-06-21 21:38:19,578 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for SWDY: -0.076
2025-06-21 21:38:19,678 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 21:38:19,785 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 21:38:19,785 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.60%) exceeds limit (15.00%)
2025-06-21 21:38:19,787 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 21:38:19,787 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (60.1)
2025-06-21 21:38:19,787 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 21:38:28,256 - app.utils.ai_pattern_recognition - INFO - Using live price 73.00 EGP from API for SWDY
2025-06-21 21:38:28,256 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for SWDY
2025-06-21 21:38:28,256 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for SWDY
2025-06-21 21:38:28,256 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 21:38:28,260 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 21:38:28,456 - app.utils.memory_management - INFO - Memory before cleanup: 463.79 MB
2025-06-21 21:38:28,638 - app.utils.memory_management - INFO - Garbage collection: collected 1749 objects
2025-06-21 21:38:28,639 - app.utils.memory_management - INFO - Memory after cleanup: 463.79 MB (freed 0.00 MB)
2025-06-21 21:44:36,530 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:44:36,575 - app - INFO - Found 13 stock files in data/stocks
2025-06-21 21:44:36,605 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 21:44:36,616 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 21:44:36,736 - app.utils.memory_management - INFO - Memory before cleanup: 463.87 MB
2025-06-21 21:44:37,000 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-06-21 21:44:37,000 - app.utils.memory_management - INFO - Memory after cleanup: 462.67 MB (freed 1.20 MB)
2025-06-21 21:44:55,575 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:44:55,603 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 21:44:55,610 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 21:44:55,801 - app.utils.memory_management - INFO - Memory before cleanup: 462.80 MB
2025-06-21 21:44:56,000 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-21 21:44:56,001 - app.utils.memory_management - INFO - Memory after cleanup: 462.80 MB (freed 0.00 MB)
2025-06-21 21:44:59,674 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:44:59,698 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 21:44:59,705 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 21:44:59,736 - app.utils.historical_data_downloader - INFO - Starting historical data generation for HRHO (2 years)
2025-06-21 21:44:59,742 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-21 21:44:59,821 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-21 21:44:59,824 - app.utils.historical_data_downloader - INFO - Fetching current live data for HRHO...
2025-06-21 21:44:59,828 - app.utils.historical_data_downloader - INFO - Downloading historical data for HRHO with intervals: ['1D', '1W']
2025-06-21 21:44:59,830 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-21 21:44:59,834 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-HRHO'], 'intervals': ['1D', '1W']}
2025-06-21 21:45:10,722 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-06-21 21:45:10,722 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-HRHO': [{'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97255.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -6418.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 34647.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 27092.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 27899.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 28179.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 29299.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 28417.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 28989.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 28161.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 28548.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 26798.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 26442.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 24654.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 24255.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 27870.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 29172.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 23518.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 38820.0, 'fibo': 34550.0, 'camarilla': 32504.0, 'woodie': 37735.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 34550.0, 'fibo': 32919.0, 'camarilla': 32113.0, 'woodie': 34813.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 32940.0, 'fibo': 31911.0, 'camarilla': 31721.0, 'woodie': 33465.0, 'dm': 33745.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 30280.0, 'fibo': 30280.0, 'camarilla': 30280.0, 'woodie': 30543.0, 'dm': 30683.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 28670.0, 'fibo': 28649.0, 'camarilla': 30939.0, 'woodie': 29195.0, 'dm': 29475.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 26010.0, 'fibo': 27641.0, 'camarilla': 30547.0, 'woodie': 26273.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': 21740.0, 'fibo': 26010.0, 'camarilla': 30156.0, 'woodie': 24925.0, 'dm': None}]}, {'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97388.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -1667.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 44419.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 28379.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 28955.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 27283.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 27501.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 26164.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 25250.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 24389.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 24020.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 21311.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 20567.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 17602.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 15963.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 25545.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 27285.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 29217.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 42660.0, 'fibo': 31330.0, 'camarilla': 24046.0, 'woodie': 37695.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 31330.0, 'fibo': 27002.0, 'camarilla': 23007.0, 'woodie': 31448.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 26130.0, 'fibo': 24328.0, 'camarilla': 21969.0, 'woodie': 26365.0, 'dm': 28730.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 20000.0, 'fibo': 20000.0, 'camarilla': 20000.0, 'woodie': 20118.0, 'dm': 21300.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 14800.0, 'fibo': 15672.0, 'camarilla': 19891.0, 'woodie': 15035.0, 'dm': 17400.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 8670.0, 'fibo': 12998.0, 'camarilla': 18853.0, 'woodie': 8787.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': None, 'fibo': 8670.0, 'camarilla': 17814.0, 'woodie': 3705.0, 'dm': None}]}]}, 'message': 'Successfully scraped 1 pairs'}
2025-06-21 21:45:10,724 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-HRHO']
2025-06-21 21:45:10,724 - app.utils.historical_data_downloader - INFO - Found data for EGX-HRHO: [{'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97255.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -6418.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 34647.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 27092.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 27899.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 28179.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 29299.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 28417.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 28989.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 28161.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 28548.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 26798.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 26442.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 24654.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 24255.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 27870.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 29172.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 23518.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 38820.0, 'fibo': 34550.0, 'camarilla': 32504.0, 'woodie': 37735.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 34550.0, 'fibo': 32919.0, 'camarilla': 32113.0, 'woodie': 34813.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 32940.0, 'fibo': 31911.0, 'camarilla': 31721.0, 'woodie': 33465.0, 'dm': 33745.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 30280.0, 'fibo': 30280.0, 'camarilla': 30280.0, 'woodie': 30543.0, 'dm': 30683.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 28670.0, 'fibo': 28649.0, 'camarilla': 30939.0, 'woodie': 29195.0, 'dm': 29475.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 26010.0, 'fibo': 27641.0, 'camarilla': 30547.0, 'woodie': 26273.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': 21740.0, 'fibo': 26010.0, 'camarilla': 30156.0, 'woodie': 24925.0, 'dm': None}]}, {'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97388.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -1667.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 44419.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 28379.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 28955.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 27283.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 27501.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 26164.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 25250.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 24389.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 24020.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 21311.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 20567.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 17602.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 15963.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 25545.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 27285.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 29217.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 42660.0, 'fibo': 31330.0, 'camarilla': 24046.0, 'woodie': 37695.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 31330.0, 'fibo': 27002.0, 'camarilla': 23007.0, 'woodie': 31448.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 26130.0, 'fibo': 24328.0, 'camarilla': 21969.0, 'woodie': 26365.0, 'dm': 28730.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 20000.0, 'fibo': 20000.0, 'camarilla': 20000.0, 'woodie': 20118.0, 'dm': 21300.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 14800.0, 'fibo': 15672.0, 'camarilla': 19891.0, 'woodie': 15035.0, 'dm': 17400.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 8670.0, 'fibo': 12998.0, 'camarilla': 18853.0, 'woodie': 8787.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': None, 'fibo': 8670.0, 'camarilla': 17814.0, 'woodie': 3705.0, 'dm': None}]}]
2025-06-21 21:45:10,726 - app.utils.historical_data_downloader - INFO - Processing 2 data points for HRHO
2025-06-21 21:45:10,726 - app.utils.historical_data_downloader - INFO - Raw data structure: [{'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97255.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -6418.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 34647.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 27092.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 27899.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 28179.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 29299.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 28417.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 28989.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 28161.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 28548.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 26798.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 26442.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 24654.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 24255.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 27870.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 29172.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 23518.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 38820.0, 'fibo': 34550.0, 'camarilla': 32504.0, 'woodie': 37735.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 34550.0, 'fibo': 32919.0, 'camarilla': 32113.0, 'woodie': 34813.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 32940.0, 'fibo': 31911.0, 'camarilla': 31721.0, 'woodie': 33465.0, 'dm': 33745.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 30280.0, 'fibo': 30280.0, 'camarilla': 30280.0, 'woodie': 30543.0, 'dm': 30683.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 28670.0, 'fibo': 28649.0, 'camarilla': 30939.0, 'woodie': 29195.0, 'dm': 29475.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 26010.0, 'fibo': 27641.0, 'camarilla': 30547.0, 'woodie': 26273.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': 21740.0, 'fibo': 26010.0, 'camarilla': 30156.0, 'woodie': 24925.0, 'dm': None}]}, {'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97388.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -1667.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 44419.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 28379.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 28955.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 27283.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 27501.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 26164.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 25250.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 24389.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 24020.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 21311.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 20567.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 17602.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 15963.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 25545.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 27285.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 29217.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 42660.0, 'fibo': 31330.0, 'camarilla': 24046.0, 'woodie': 37695.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 31330.0, 'fibo': 27002.0, 'camarilla': 23007.0, 'woodie': 31448.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 26130.0, 'fibo': 24328.0, 'camarilla': 21969.0, 'woodie': 26365.0, 'dm': 28730.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 20000.0, 'fibo': 20000.0, 'camarilla': 20000.0, 'woodie': 20118.0, 'dm': 21300.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 14800.0, 'fibo': 15672.0, 'camarilla': 19891.0, 'woodie': 15035.0, 'dm': 17400.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 8670.0, 'fibo': 12998.0, 'camarilla': 18853.0, 'woodie': 8787.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': None, 'fibo': 8670.0, 'camarilla': 17814.0, 'woodie': 3705.0, 'dm': None}]}]
2025-06-21 21:45:10,728 - app.utils.historical_data_downloader - INFO - First item structure: {'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97255.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -6418.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 34647.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 27092.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 27899.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 28179.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 29299.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 28417.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 28989.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 28161.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 28548.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 26798.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 26442.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 24654.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 24255.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 27870.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 29172.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 23518.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 38820.0, 'fibo': 34550.0, 'camarilla': 32504.0, 'woodie': 37735.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 34550.0, 'fibo': 32919.0, 'camarilla': 32113.0, 'woodie': 34813.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 32940.0, 'fibo': 31911.0, 'camarilla': 31721.0, 'woodie': 33465.0, 'dm': 33745.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 30280.0, 'fibo': 30280.0, 'camarilla': 30280.0, 'woodie': 30543.0, 'dm': 30683.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 28670.0, 'fibo': 28649.0, 'camarilla': 30939.0, 'woodie': 29195.0, 'dm': 29475.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 26010.0, 'fibo': 27641.0, 'camarilla': 30547.0, 'woodie': 26273.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': 21740.0, 'fibo': 26010.0, 'camarilla': 30156.0, 'woodie': 24925.0, 'dm': None}]}
2025-06-21 21:45:10,729 - app.utils.historical_data_downloader - INFO - Processing interval 1D: pair=EGX-HRHO, price=24060.0
2025-06-21 21:45:10,729 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 24060.0 -> 24.06 EGP
2025-06-21 21:45:10,729 - app.utils.historical_data_downloader - INFO - Successfully processed 1D: price=24.06 EGP
2025-06-21 21:45:10,731 - app.utils.historical_data_downloader - INFO - Processing interval 1W: pair=EGX-HRHO, price=24060.0
2025-06-21 21:45:10,731 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 24060.0 -> 24.06 EGP
2025-06-21 21:45:10,731 - app.utils.historical_data_downloader - INFO - Successfully processed 1W: price=24.06 EGP
2025-06-21 21:45:10,731 - app.utils.historical_data_downloader - INFO - Successfully processed 2 intervals for HRHO
2025-06-21 21:45:10,731 - app.utils.historical_data_downloader - INFO - Successfully processed historical data for HRHO
2025-06-21 21:45:10,731 - app.utils.historical_data_downloader - INFO - Successfully retrieved current data for HRHO: {'1D': {'symbol': 'HRHO', 'interval': '1D', 'current_price': 24.06, 'timestamp': datetime.datetime(2025, 6, 21, 21, 45, 10, 729049), 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97255.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -6418.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 34647.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 27092.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 27899.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 28179.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 29299.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 28417.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 28989.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 28161.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 28548.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 26798.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 26442.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 24654.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 24255.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 27870.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 29172.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 23518.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 38820.0, 'fibo': 34550.0, 'camarilla': 32504.0, 'woodie': 37735.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 34550.0, 'fibo': 32919.0, 'camarilla': 32113.0, 'woodie': 34813.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 32940.0, 'fibo': 31911.0, 'camarilla': 31721.0, 'woodie': 33465.0, 'dm': 33745.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 30280.0, 'fibo': 30280.0, 'camarilla': 30280.0, 'woodie': 30543.0, 'dm': 30683.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 28670.0, 'fibo': 28649.0, 'camarilla': 30939.0, 'woodie': 29195.0, 'dm': 29475.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 26010.0, 'fibo': 27641.0, 'camarilla': 30547.0, 'woodie': 26273.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': 21740.0, 'fibo': 26010.0, 'camarilla': 30156.0, 'woodie': 24925.0, 'dm': None}], 'raw_data': {'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97255.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -6418.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 34647.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 27092.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 27899.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 28179.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 29299.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 28417.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 28989.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 28161.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 28548.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 26798.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 26442.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 24654.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 24255.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 27870.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 29172.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 23518.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 38820.0, 'fibo': 34550.0, 'camarilla': 32504.0, 'woodie': 37735.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 34550.0, 'fibo': 32919.0, 'camarilla': 32113.0, 'woodie': 34813.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 32940.0, 'fibo': 31911.0, 'camarilla': 31721.0, 'woodie': 33465.0, 'dm': 33745.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 30280.0, 'fibo': 30280.0, 'camarilla': 30280.0, 'woodie': 30543.0, 'dm': 30683.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 28670.0, 'fibo': 28649.0, 'camarilla': 30939.0, 'woodie': 29195.0, 'dm': 29475.0}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 26010.0, 'fibo': 27641.0, 'camarilla': 30547.0, 'woodie': 26273.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1D', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': 21740.0, 'fibo': 26010.0, 'camarilla': 30156.0, 'woodie': 24925.0, 'dm': None}]}}, '1W': {'symbol': 'HRHO', 'interval': '1W', 'current_price': 24.06, 'timestamp': datetime.datetime(2025, 6, 21, 21, 45, 10, 731059), 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97388.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -1667.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 44419.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 28379.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 28955.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 27283.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 27501.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 26164.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 25250.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 24389.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 24020.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 21311.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 20567.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 17602.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 15963.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 25545.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 27285.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 29217.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 42660.0, 'fibo': 31330.0, 'camarilla': 24046.0, 'woodie': 37695.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 31330.0, 'fibo': 27002.0, 'camarilla': 23007.0, 'woodie': 31448.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 26130.0, 'fibo': 24328.0, 'camarilla': 21969.0, 'woodie': 26365.0, 'dm': 28730.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 20000.0, 'fibo': 20000.0, 'camarilla': 20000.0, 'woodie': 20118.0, 'dm': 21300.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 14800.0, 'fibo': 15672.0, 'camarilla': 19891.0, 'woodie': 15035.0, 'dm': 17400.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 8670.0, 'fibo': 12998.0, 'camarilla': 18853.0, 'woodie': 8787.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': None, 'fibo': 8670.0, 'camarilla': 17814.0, 'woodie': 3705.0, 'dm': None}], 'raw_data': {'pair': 'EGX-HRHO', 'price': 24060.0, 'oscillators': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Relative Strength Index (14)', 'value': 24943.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic %K (14, 3, 3)', 'value': 4950.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Commodity Channel Index (20)', 'value': -190793.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Average Directional Index (14)', 'value': 33897.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Awesome Oscillator', 'value': -3089.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Momentum (10)', 'value': -5810.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'MACD Level (12, 26)', 'value': -931.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Williams Percent Range (14)', 'value': -97388.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Bull Bear Power', 'value': -1667.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 44419.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (10)', 'value': 28379.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (10)', 'value': 28955.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (20)', 'value': 27283.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (20)', 'value': 27501.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (30)', 'value': 26164.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (30)', 'value': 25250.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (50)', 'value': 24389.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (50)', 'value': 24020.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (100)', 'value': 21311.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (100)', 'value': 20567.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Exponential Moving Average (200)', 'value': 17602.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Simple Moving Average (200)', 'value': 15963.0, 'action': 'Buy'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 25545.0, 'action': 'Neutral'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Volume Weighted Moving Average (20)', 'value': 27285.0, 'action': 'Sell'}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'name': 'Hull Moving Average (9)', 'value': 29217.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R3', 'classic': 42660.0, 'fibo': 31330.0, 'camarilla': 24046.0, 'woodie': 37695.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R2', 'classic': 31330.0, 'fibo': 27002.0, 'camarilla': 23007.0, 'woodie': 31448.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'R1', 'classic': 26130.0, 'fibo': 24328.0, 'camarilla': 21969.0, 'woodie': 26365.0, 'dm': 28730.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'P', 'classic': 20000.0, 'fibo': 20000.0, 'camarilla': 20000.0, 'woodie': 20118.0, 'dm': 21300.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S1', 'classic': 14800.0, 'fibo': 15672.0, 'camarilla': 19891.0, 'woodie': 15035.0, 'dm': 17400.0}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S2', 'classic': 8670.0, 'fibo': 12998.0, 'camarilla': 18853.0, 'woodie': 8787.0, 'dm': None}, {'pair': 'EGX-HRHO', 'interval': '1W', 'register_time': '21/06/2025 21:45', 'pivot': 'S3', 'classic': None, 'fibo': 8670.0, 'camarilla': 17814.0, 'woodie': 3705.0, 'dm': None}]}}}
2025-06-21 21:45:10,733 - app.utils.historical_data_downloader - INFO - Found valid price from 1D: 24.06 EGP
2025-06-21 21:45:10,733 - app.utils.historical_data_downloader - INFO - Using current price for HRHO: 24.06 EGP
2025-06-21 21:45:10,733 - app.utils.historical_data_downloader - INFO - Generating 2 years of synthetic historical data...
2025-06-21 21:45:10,733 - app.utils.historical_data_downloader - INFO - Generating 2 years of synthetic data for HRHO
2025-06-21 21:45:10,759 - app.utils.historical_data_downloader - INFO - Generated 500 days of synthetic historical data for HRHO
2025-06-21 21:45:10,760 - app.utils.historical_data_downloader - INFO - Generated 500 days of synthetic data
2025-06-21 21:45:10,762 - app.utils.historical_data_downloader - INFO - Saving data to: data\stocks\HRHO.csv
2025-06-21 21:45:10,769 - app.utils.historical_data_downloader - INFO - Saved 500 days of historical data to data\stocks\HRHO.csv
2025-06-21 21:45:10,769 - app.utils.historical_data_downloader - INFO - CSV file size: 21658 bytes
2025-06-21 21:45:10,790 - app.utils.historical_data_downloader - INFO - Verification: CSV contains 500 rows
2025-06-21 21:45:10,790 - app.utils.historical_data_downloader - INFO - Successfully completed historical data generation for HRHO
2025-06-21 21:45:10,885 - app.utils.memory_management - INFO - Memory before cleanup: 463.89 MB
2025-06-21 21:45:11,104 - app.utils.memory_management - INFO - Garbage collection: collected 244 objects
2025-06-21 21:45:11,104 - app.utils.memory_management - INFO - Memory after cleanup: 463.89 MB (freed 0.00 MB)
2025-06-21 21:45:56,773 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:45:56,835 - app.utils.memory_management - INFO - Memory before cleanup: 464.07 MB
2025-06-21 21:45:57,122 - app.utils.memory_management - INFO - Garbage collection: collected 260 objects
2025-06-21 21:45:57,125 - app.utils.memory_management - INFO - Memory after cleanup: 464.07 MB (freed 0.00 MB)
2025-06-21 21:46:04,832 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:46:04,886 - app - INFO - File HRHO contains 2025 data
2025-06-21 21:46:04,893 - app - INFO - Saved data to data/stocks\HRHO.csv
2025-06-21 21:46:04,895 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 21:46:04,900 - app.utils.memory_management - INFO - Memory before cleanup: 464.28 MB
2025-06-21 21:46:05,109 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-21 21:46:05,110 - app.utils.memory_management - INFO - Memory after cleanup: 464.28 MB (freed 0.00 MB)
2025-06-21 21:46:08,749 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:46:08,818 - app - INFO - File HRHO contains 2025 data
2025-06-21 21:46:08,826 - app - INFO - Saved data to data/stocks\HRHO.csv
2025-06-21 21:46:08,828 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 21:46:08,832 - app.utils.memory_management - INFO - Memory before cleanup: 464.28 MB
2025-06-21 21:46:09,106 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-21 21:46:09,109 - app.utils.memory_management - INFO - Memory after cleanup: 464.28 MB (freed 0.00 MB)
2025-06-21 21:46:11,703 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:46:11,792 - app - INFO - File HRHO contains 2025 data
2025-06-21 21:46:11,798 - app - INFO - Saved data to data/stocks\HRHO.csv
2025-06-21 21:46:11,800 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 21:46:11,807 - app.utils.memory_management - INFO - Memory before cleanup: 464.31 MB
2025-06-21 21:46:12,018 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-06-21 21:46:12,020 - app.utils.memory_management - INFO - Memory after cleanup: 464.31 MB (freed 0.00 MB)
2025-06-21 21:46:12,734 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:46:12,812 - app - INFO - File HRHO contains 2025 data
2025-06-21 21:46:12,819 - app - INFO - Saved data to data/stocks\HRHO.csv
2025-06-21 21:46:12,820 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 21:46:12,822 - app.utils.memory_management - INFO - Memory before cleanup: 464.31 MB
2025-06-21 21:46:13,034 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-21 21:46:13,034 - app.utils.memory_management - INFO - Memory after cleanup: 464.31 MB (freed 0.00 MB)
2025-06-21 21:46:15,923 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:46:16,050 - app - INFO - File HRHO contains 2025 data
2025-06-21 21:46:16,079 - app - INFO - Saved data to data/stocks\HRHO.csv
2025-06-21 21:46:16,080 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 21:46:16,082 - app.utils.memory_management - INFO - Memory before cleanup: 464.38 MB
2025-06-21 21:46:16,282 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-21 21:46:16,283 - app.utils.memory_management - INFO - Memory after cleanup: 464.38 MB (freed 0.00 MB)
2025-06-21 21:46:18,855 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:46:18,989 - app - INFO - File HRHO contains 2025 data
2025-06-21 21:46:19,021 - app - INFO - Saved data to data/stocks\HRHO.csv
2025-06-21 21:46:19,021 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 21:46:19,024 - app.utils.memory_management - INFO - Memory before cleanup: 464.39 MB
2025-06-21 21:46:19,222 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-21 21:46:19,222 - app.utils.memory_management - INFO - Memory after cleanup: 464.39 MB (freed 0.00 MB)
2025-06-21 21:46:22,793 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:46:22,840 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-21 21:46:22,851 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.379
2025-06-21 21:46:23,002 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 21:46:23,085 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 21:46:23,087 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-21 21:46:23,087 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 21:46:23,087 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (52.2)
2025-06-21 21:46:23,087 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 21:46:31,854 - app.utils.ai_pattern_recognition - INFO - Using live price 45.97 EGP from API for ABUK
2025-06-21 21:46:31,855 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-21 21:46:31,856 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-21 21:46:31,857 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 21:46:31,858 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 21:46:32,033 - app.utils.memory_management - INFO - Memory before cleanup: 465.32 MB
2025-06-21 21:46:32,217 - app.utils.memory_management - INFO - Garbage collection: collected 1711 objects
2025-06-21 21:46:32,220 - app.utils.memory_management - INFO - Memory after cleanup: 465.32 MB (freed 0.00 MB)
2025-06-21 21:46:39,773 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 21:46:39,854 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for HRHO over 30 days
2025-06-21 21:46:39,878 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for HRHO: -0.076
2025-06-21 21:46:39,972 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 21:46:40,149 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 21:46:40,149 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (23.46%) exceeds limit (15.00%)
2025-06-21 21:46:40,149 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 21:46:40,149 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (54.7)
2025-06-21 21:46:40,149 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 21:46:47,898 - app.utils.ai_pattern_recognition - INFO - Using live price 24.06 EGP from API for HRHO
2025-06-21 21:46:47,903 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for HRHO
2025-06-21 21:46:47,903 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for HRHO
2025-06-21 21:46:47,904 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 21:46:47,905 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 21:46:48,062 - app.utils.memory_management - INFO - Memory before cleanup: 466.40 MB
2025-06-21 21:46:48,244 - app.utils.memory_management - INFO - Garbage collection: collected 1724 objects
2025-06-21 21:46:48,244 - app.utils.memory_management - INFO - Memory after cleanup: 466.40 MB (freed 0.00 MB)
2025-06-21 22:18:34,437 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:18:34,484 - app - INFO - Found 14 stock files in data/stocks
2025-06-21 22:18:34,493 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 22:18:34,503 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 22:18:34,697 - app.utils.memory_management - INFO - Memory before cleanup: 461.16 MB
2025-06-21 22:18:34,932 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-06-21 22:18:34,932 - app.utils.memory_management - INFO - Memory after cleanup: 461.17 MB (freed -0.00 MB)
2025-06-21 22:18:51,822 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:18:51,843 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 22:18:51,850 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 22:18:51,951 - app.utils.memory_management - INFO - Memory before cleanup: 461.18 MB
2025-06-21 22:18:52,160 - app.utils.memory_management - INFO - Garbage collection: collected 249 objects
2025-06-21 22:18:52,161 - app.utils.memory_management - INFO - Memory after cleanup: 461.18 MB (freed 0.00 MB)
2025-06-21 22:19:09,102 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:19:09,123 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 22:19:09,132 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 22:19:09,217 - app.utils.memory_management - INFO - Memory before cleanup: 461.18 MB
2025-06-21 22:19:09,443 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-06-21 22:19:09,445 - app.utils.memory_management - INFO - Memory after cleanup: 461.18 MB (freed 0.00 MB)
2025-06-21 22:19:14,109 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:19:14,134 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-21 22:19:14,141 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-21 22:19:14,191 - app.utils.historical_data_downloader - INFO - Starting historical data generation for SKPC (2 years)
2025-06-21 22:19:14,193 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-21 22:19:14,200 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-21 22:19:14,201 - app.utils.historical_data_downloader - INFO - Fetching current live data for SKPC...
2025-06-21 22:19:14,203 - app.utils.historical_data_downloader - INFO - Downloading historical data for SKPC with intervals: ['1D', '1W']
2025-06-21 22:19:14,205 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-21 22:19:14,206 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-SKPC'], 'intervals': ['1D', '1W']}
2025-06-21 22:19:25,613 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-06-21 22:19:25,613 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-SKPC': [{'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -98291.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -1984.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 23912.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 18993.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 19209.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 19603.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 19805.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 19985.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20222.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20400.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20773.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 20678.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 21008.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 20829.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 20519.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 19795.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 19981.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 17841.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 25147.0, 'fibo': 22947.0, 'camarilla': 20805.0, 'woodie': 23500.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 22947.0, 'fibo': 22106.0, 'camarilla': 20603.0, 'woodie': 22810.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 21573.0, 'fibo': 21587.0, 'camarilla': 20402.0, 'woodie': 21300.0, 'dm': 21160.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 20747.0, 'fibo': 20747.0, 'camarilla': 20747.0, 'woodie': 20610.0, 'dm': 20540.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 19373.0, 'fibo': 19906.0, 'camarilla': 19998.0, 'woodie': 19100.0, 'dm': 18960.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 18547.0, 'fibo': 19387.0, 'camarilla': 19797.0, 'woodie': 18410.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': 16347.0, 'fibo': 18547.0, 'camarilla': 19595.0, 'woodie': 16900.0, 'dm': None}]}, {'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -99185.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -2973.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 33010.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 20238.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 20747.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 20626.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 21096.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 20754.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20713.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20879.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20360.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 19967.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 22148.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 17003.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 15297.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 20515.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 21411.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 19012.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 72157.0, 'fibo': 48392.0, 'camarilla': 25865.0, 'woodie': 55047.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 48392.0, 'fibo': 39314.0, 'camarilla': 23687.0, 'woodie': 47103.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 33861.0, 'fibo': 33705.0, 'camarilla': 21508.0, 'woodie': 31282.0, 'dm': 29244.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 24627.0, 'fibo': 24627.0, 'camarilla': 24627.0, 'woodie': 23338.0, 'dm': 22319.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 10096.0, 'fibo': 15549.0, 'camarilla': 17152.0, 'woodie': 7518.0, 'dm': 5479.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 862.0, 'fibo': 9941.0, 'camarilla': 14973.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': None, 'fibo': 862.0, 'camarilla': 12795.0, 'woodie': None, 'dm': None}]}]}, 'message': 'Successfully scraped 1 pairs'}
2025-06-21 22:19:25,615 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-SKPC']
2025-06-21 22:19:25,615 - app.utils.historical_data_downloader - INFO - Found data for EGX-SKPC: [{'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -98291.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -1984.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 23912.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 18993.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 19209.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 19603.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 19805.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 19985.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20222.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20400.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20773.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 20678.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 21008.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 20829.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 20519.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 19795.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 19981.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 17841.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 25147.0, 'fibo': 22947.0, 'camarilla': 20805.0, 'woodie': 23500.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 22947.0, 'fibo': 22106.0, 'camarilla': 20603.0, 'woodie': 22810.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 21573.0, 'fibo': 21587.0, 'camarilla': 20402.0, 'woodie': 21300.0, 'dm': 21160.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 20747.0, 'fibo': 20747.0, 'camarilla': 20747.0, 'woodie': 20610.0, 'dm': 20540.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 19373.0, 'fibo': 19906.0, 'camarilla': 19998.0, 'woodie': 19100.0, 'dm': 18960.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 18547.0, 'fibo': 19387.0, 'camarilla': 19797.0, 'woodie': 18410.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': 16347.0, 'fibo': 18547.0, 'camarilla': 19595.0, 'woodie': 16900.0, 'dm': None}]}, {'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -99185.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -2973.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 33010.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 20238.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 20747.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 20626.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 21096.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 20754.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20713.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20879.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20360.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 19967.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 22148.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 17003.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 15297.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 20515.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 21411.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 19012.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 72157.0, 'fibo': 48392.0, 'camarilla': 25865.0, 'woodie': 55047.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 48392.0, 'fibo': 39314.0, 'camarilla': 23687.0, 'woodie': 47103.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 33861.0, 'fibo': 33705.0, 'camarilla': 21508.0, 'woodie': 31282.0, 'dm': 29244.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 24627.0, 'fibo': 24627.0, 'camarilla': 24627.0, 'woodie': 23338.0, 'dm': 22319.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 10096.0, 'fibo': 15549.0, 'camarilla': 17152.0, 'woodie': 7518.0, 'dm': 5479.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 862.0, 'fibo': 9941.0, 'camarilla': 14973.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': None, 'fibo': 862.0, 'camarilla': 12795.0, 'woodie': None, 'dm': None}]}]
2025-06-21 22:19:25,615 - app.utils.historical_data_downloader - INFO - Processing 2 data points for SKPC
2025-06-21 22:19:25,617 - app.utils.historical_data_downloader - INFO - Raw data structure: [{'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -98291.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -1984.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 23912.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 18993.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 19209.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 19603.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 19805.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 19985.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20222.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20400.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20773.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 20678.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 21008.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 20829.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 20519.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 19795.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 19981.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 17841.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 25147.0, 'fibo': 22947.0, 'camarilla': 20805.0, 'woodie': 23500.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 22947.0, 'fibo': 22106.0, 'camarilla': 20603.0, 'woodie': 22810.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 21573.0, 'fibo': 21587.0, 'camarilla': 20402.0, 'woodie': 21300.0, 'dm': 21160.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 20747.0, 'fibo': 20747.0, 'camarilla': 20747.0, 'woodie': 20610.0, 'dm': 20540.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 19373.0, 'fibo': 19906.0, 'camarilla': 19998.0, 'woodie': 19100.0, 'dm': 18960.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 18547.0, 'fibo': 19387.0, 'camarilla': 19797.0, 'woodie': 18410.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': 16347.0, 'fibo': 18547.0, 'camarilla': 19595.0, 'woodie': 16900.0, 'dm': None}]}, {'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -99185.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -2973.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 33010.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 20238.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 20747.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 20626.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 21096.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 20754.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20713.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20879.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20360.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 19967.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 22148.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 17003.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 15297.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 20515.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 21411.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 19012.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 72157.0, 'fibo': 48392.0, 'camarilla': 25865.0, 'woodie': 55047.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 48392.0, 'fibo': 39314.0, 'camarilla': 23687.0, 'woodie': 47103.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 33861.0, 'fibo': 33705.0, 'camarilla': 21508.0, 'woodie': 31282.0, 'dm': 29244.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 24627.0, 'fibo': 24627.0, 'camarilla': 24627.0, 'woodie': 23338.0, 'dm': 22319.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 10096.0, 'fibo': 15549.0, 'camarilla': 17152.0, 'woodie': 7518.0, 'dm': 5479.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 862.0, 'fibo': 9941.0, 'camarilla': 14973.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': None, 'fibo': 862.0, 'camarilla': 12795.0, 'woodie': None, 'dm': None}]}]
2025-06-21 22:19:25,617 - app.utils.historical_data_downloader - INFO - First item structure: {'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -98291.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -1984.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 23912.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 18993.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 19209.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 19603.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 19805.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 19985.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20222.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20400.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20773.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 20678.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 21008.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 20829.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 20519.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 19795.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 19981.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 17841.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 25147.0, 'fibo': 22947.0, 'camarilla': 20805.0, 'woodie': 23500.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 22947.0, 'fibo': 22106.0, 'camarilla': 20603.0, 'woodie': 22810.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 21573.0, 'fibo': 21587.0, 'camarilla': 20402.0, 'woodie': 21300.0, 'dm': 21160.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 20747.0, 'fibo': 20747.0, 'camarilla': 20747.0, 'woodie': 20610.0, 'dm': 20540.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 19373.0, 'fibo': 19906.0, 'camarilla': 19998.0, 'woodie': 19100.0, 'dm': 18960.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 18547.0, 'fibo': 19387.0, 'camarilla': 19797.0, 'woodie': 18410.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': 16347.0, 'fibo': 18547.0, 'camarilla': 19595.0, 'woodie': 16900.0, 'dm': None}]}
2025-06-21 22:19:25,619 - app.utils.historical_data_downloader - INFO - Processing interval 1D: pair=EGX-SKPC, price=18030.0
2025-06-21 22:19:25,619 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 18030.0 -> 18.03 EGP
2025-06-21 22:19:25,621 - app.utils.historical_data_downloader - INFO - Successfully processed 1D: price=18.03 EGP
2025-06-21 22:19:25,621 - app.utils.historical_data_downloader - INFO - Processing interval 1W: pair=EGX-SKPC, price=18030.0
2025-06-21 22:19:25,621 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 18030.0 -> 18.03 EGP
2025-06-21 22:19:25,621 - app.utils.historical_data_downloader - INFO - Successfully processed 1W: price=18.03 EGP
2025-06-21 22:19:25,623 - app.utils.historical_data_downloader - INFO - Successfully processed 2 intervals for SKPC
2025-06-21 22:19:25,623 - app.utils.historical_data_downloader - INFO - Successfully processed historical data for SKPC
2025-06-21 22:19:25,623 - app.utils.historical_data_downloader - INFO - Successfully retrieved current data for SKPC: {'1D': {'symbol': 'SKPC', 'interval': '1D', 'current_price': 18.03, 'timestamp': datetime.datetime(2025, 6, 21, 22, 19, 25, 621053), 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -98291.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -1984.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 23912.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 18993.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 19209.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 19603.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 19805.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 19985.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20222.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20400.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20773.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 20678.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 21008.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 20829.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 20519.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 19795.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 19981.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 17841.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 25147.0, 'fibo': 22947.0, 'camarilla': 20805.0, 'woodie': 23500.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 22947.0, 'fibo': 22106.0, 'camarilla': 20603.0, 'woodie': 22810.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 21573.0, 'fibo': 21587.0, 'camarilla': 20402.0, 'woodie': 21300.0, 'dm': 21160.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 20747.0, 'fibo': 20747.0, 'camarilla': 20747.0, 'woodie': 20610.0, 'dm': 20540.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 19373.0, 'fibo': 19906.0, 'camarilla': 19998.0, 'woodie': 19100.0, 'dm': 18960.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 18547.0, 'fibo': 19387.0, 'camarilla': 19797.0, 'woodie': 18410.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': 16347.0, 'fibo': 18547.0, 'camarilla': 19595.0, 'woodie': 16900.0, 'dm': None}], 'raw_data': {'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -98291.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -1984.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 23912.0, 'action': 'Sell'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 18993.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 19209.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 19603.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 19805.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 19985.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20222.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20400.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20773.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 20678.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 21008.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 20829.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 20519.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 19795.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 19981.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 17841.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 25147.0, 'fibo': 22947.0, 'camarilla': 20805.0, 'woodie': 23500.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 22947.0, 'fibo': 22106.0, 'camarilla': 20603.0, 'woodie': 22810.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 21573.0, 'fibo': 21587.0, 'camarilla': 20402.0, 'woodie': 21300.0, 'dm': 21160.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 20747.0, 'fibo': 20747.0, 'camarilla': 20747.0, 'woodie': 20610.0, 'dm': 20540.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 19373.0, 'fibo': 19906.0, 'camarilla': 19998.0, 'woodie': 19100.0, 'dm': 18960.0}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 18547.0, 'fibo': 19387.0, 'camarilla': 19797.0, 'woodie': 18410.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1D', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': 16347.0, 'fibo': 18547.0, 'camarilla': 19595.0, 'woodie': 16900.0, 'dm': None}]}}, '1W': {'symbol': 'SKPC', 'interval': '1W', 'current_price': 18.03, 'timestamp': datetime.datetime(2025, 6, 21, 22, 19, 25, 621053), 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -99185.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -2973.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 33010.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 20238.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 20747.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 20626.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 21096.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 20754.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20713.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20879.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20360.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 19967.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 22148.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 17003.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 15297.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 20515.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 21411.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 19012.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 72157.0, 'fibo': 48392.0, 'camarilla': 25865.0, 'woodie': 55047.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 48392.0, 'fibo': 39314.0, 'camarilla': 23687.0, 'woodie': 47103.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 33861.0, 'fibo': 33705.0, 'camarilla': 21508.0, 'woodie': 31282.0, 'dm': 29244.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 24627.0, 'fibo': 24627.0, 'camarilla': 24627.0, 'woodie': 23338.0, 'dm': 22319.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 10096.0, 'fibo': 15549.0, 'camarilla': 17152.0, 'woodie': 7518.0, 'dm': 5479.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 862.0, 'fibo': 9941.0, 'camarilla': 14973.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': None, 'fibo': 862.0, 'camarilla': 12795.0, 'woodie': None, 'dm': None}], 'raw_data': {'pair': 'EGX-SKPC', 'price': 18030.0, 'oscillators': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Relative Strength Index (14)', 'value': 22475.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic %K (14, 3, 3)', 'value': 2396.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Commodity Channel Index (20)', 'value': -171284.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Average Directional Index (14)', 'value': 43478.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Awesome Oscillator', 'value': -1907.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Momentum (10)', 'value': -1880.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'MACD Level (12, 26)', 'value': -708.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 1020.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Williams Percent Range (14)', 'value': -99185.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Bull Bear Power', 'value': -2973.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 33010.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (10)', 'value': 20238.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (10)', 'value': 20747.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (20)', 'value': 20626.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (20)', 'value': 21096.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (30)', 'value': 20754.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (30)', 'value': 20713.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (50)', 'value': 20879.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (50)', 'value': 20360.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (100)', 'value': 19967.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (100)', 'value': 22148.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Exponential Moving Average (200)', 'value': 17003.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Simple Moving Average (200)', 'value': 15297.0, 'action': 'Buy'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 20515.0, 'action': 'Neutral'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Volume Weighted Moving Average (20)', 'value': 21411.0, 'action': 'Sell'}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'name': 'Hull Moving Average (9)', 'value': 19012.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R3', 'classic': 72157.0, 'fibo': 48392.0, 'camarilla': 25865.0, 'woodie': 55047.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R2', 'classic': 48392.0, 'fibo': 39314.0, 'camarilla': 23687.0, 'woodie': 47103.0, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'R1', 'classic': 33861.0, 'fibo': 33705.0, 'camarilla': 21508.0, 'woodie': 31282.0, 'dm': 29244.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'P', 'classic': 24627.0, 'fibo': 24627.0, 'camarilla': 24627.0, 'woodie': 23338.0, 'dm': 22319.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S1', 'classic': 10096.0, 'fibo': 15549.0, 'camarilla': 17152.0, 'woodie': 7518.0, 'dm': 5479.0}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S2', 'classic': 862.0, 'fibo': 9941.0, 'camarilla': 14973.0, 'woodie': None, 'dm': None}, {'pair': 'EGX-SKPC', 'interval': '1W', 'register_time': '21/06/2025 22:19', 'pivot': 'S3', 'classic': None, 'fibo': 862.0, 'camarilla': 12795.0, 'woodie': None, 'dm': None}]}}}
2025-06-21 22:19:25,625 - app.utils.historical_data_downloader - INFO - Found valid price from 1D: 18.03 EGP
2025-06-21 22:19:25,626 - app.utils.historical_data_downloader - INFO - Using current price for SKPC: 18.03 EGP
2025-06-21 22:19:25,626 - app.utils.historical_data_downloader - INFO - Generating 2 years of synthetic historical data...
2025-06-21 22:19:25,627 - app.utils.historical_data_downloader - INFO - Generating 2 years of synthetic data for SKPC
2025-06-21 22:19:25,654 - app.utils.historical_data_downloader - INFO - Generated 500 days of synthetic historical data for SKPC
2025-06-21 22:19:25,655 - app.utils.historical_data_downloader - INFO - Generated 500 days of synthetic data
2025-06-21 22:19:25,655 - app.utils.historical_data_downloader - INFO - Saving data to: data\stocks\SKPC.csv
2025-06-21 22:19:25,660 - app.utils.historical_data_downloader - INFO - Saved 500 days of historical data to data\stocks\SKPC.csv
2025-06-21 22:19:25,661 - app.utils.historical_data_downloader - INFO - CSV file size: 20958 bytes
2025-06-21 22:19:25,682 - app.utils.historical_data_downloader - INFO - Verification: CSV contains 500 rows
2025-06-21 22:19:25,682 - app.utils.historical_data_downloader - INFO - Successfully completed historical data generation for SKPC
2025-06-21 22:19:25,758 - app.utils.memory_management - INFO - Memory before cleanup: 461.32 MB
2025-06-21 22:19:25,953 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-21 22:19:25,953 - app.utils.memory_management - INFO - Memory after cleanup: 461.32 MB (freed 0.00 MB)
2025-06-21 22:19:39,424 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:19:39,452 - app.utils.memory_management - INFO - Memory before cleanup: 461.32 MB
2025-06-21 22:19:39,665 - app.utils.memory_management - INFO - Garbage collection: collected 260 objects
2025-06-21 22:19:39,665 - app.utils.memory_management - INFO - Memory after cleanup: 461.32 MB (freed 0.00 MB)
2025-06-21 22:19:49,354 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:19:49,409 - app - INFO - File SKPC contains 2025 data
2025-06-21 22:19:49,416 - app - INFO - Saved data to data/stocks\SKPC.csv
2025-06-21 22:19:49,418 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 22:19:49,422 - app.utils.memory_management - INFO - Memory before cleanup: 461.33 MB
2025-06-21 22:19:49,630 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-21 22:19:49,631 - app.utils.memory_management - INFO - Memory after cleanup: 461.33 MB (freed 0.00 MB)
2025-06-21 22:19:52,801 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:19:52,863 - app - INFO - File SKPC contains 2025 data
2025-06-21 22:19:52,868 - app - INFO - Saved data to data/stocks\SKPC.csv
2025-06-21 22:19:52,869 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 22:19:52,873 - app.utils.memory_management - INFO - Memory before cleanup: 461.34 MB
2025-06-21 22:19:53,096 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-21 22:19:53,098 - app.utils.memory_management - INFO - Memory after cleanup: 461.34 MB (freed 0.00 MB)
2025-06-21 22:19:56,900 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:19:56,976 - app - INFO - File SKPC contains 2025 data
2025-06-21 22:19:56,981 - app - INFO - Saved data to data/stocks\SKPC.csv
2025-06-21 22:19:56,982 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 22:19:56,988 - app.utils.memory_management - INFO - Memory before cleanup: 461.35 MB
2025-06-21 22:19:57,207 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-06-21 22:19:57,209 - app.utils.memory_management - INFO - Memory after cleanup: 461.35 MB (freed 0.00 MB)
2025-06-21 22:19:58,140 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:19:58,224 - app - INFO - File SKPC contains 2025 data
2025-06-21 22:19:58,230 - app - INFO - Saved data to data/stocks\SKPC.csv
2025-06-21 22:19:58,231 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 22:19:58,236 - app.utils.memory_management - INFO - Memory before cleanup: 461.36 MB
2025-06-21 22:19:58,504 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-21 22:19:58,504 - app.utils.memory_management - INFO - Memory after cleanup: 461.36 MB (freed 0.00 MB)
2025-06-21 22:20:00,493 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:20:00,631 - app - INFO - File SKPC contains 2025 data
2025-06-21 22:20:00,657 - app - INFO - Saved data to data/stocks\SKPC.csv
2025-06-21 22:20:00,658 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 22:20:00,664 - app.utils.memory_management - INFO - Memory before cleanup: 461.38 MB
2025-06-21 22:20:00,868 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-21 22:20:00,869 - app.utils.memory_management - INFO - Memory after cleanup: 461.38 MB (freed 0.00 MB)
2025-06-21 22:20:02,789 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:20:02,922 - app - INFO - File SKPC contains 2025 data
2025-06-21 22:20:02,953 - app - INFO - Saved data to data/stocks\SKPC.csv
2025-06-21 22:20:02,954 - app - INFO - Date range: 2023-07-24 to 2025-06-20
2025-06-21 22:20:02,956 - app.utils.memory_management - INFO - Memory before cleanup: 461.38 MB
2025-06-21 22:20:03,144 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-21 22:20:03,145 - app.utils.memory_management - INFO - Memory after cleanup: 461.38 MB (freed 0.00 MB)
2025-06-21 22:20:07,378 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:20:07,421 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-21 22:20:07,432 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.379
2025-06-21 22:20:07,511 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 22:20:07,607 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 22:20:07,609 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-21 22:20:07,609 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 22:20:07,609 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (52.2)
2025-06-21 22:20:07,610 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 22:20:14,728 - app.utils.ai_pattern_recognition - INFO - Using live price 45.97 EGP from API for ABUK
2025-06-21 22:20:14,729 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-21 22:20:14,729 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-21 22:20:14,730 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 22:20:14,732 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 22:20:14,941 - app.utils.memory_management - INFO - Memory before cleanup: 461.65 MB
2025-06-21 22:20:15,204 - app.utils.memory_management - INFO - Garbage collection: collected 1713 objects
2025-06-21 22:20:15,205 - app.utils.memory_management - INFO - Memory after cleanup: 461.65 MB (freed 0.00 MB)
2025-06-21 22:20:17,046 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:20:17,124 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for SKPC over 30 days
2025-06-21 22:20:17,153 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for SKPC: -0.077
2025-06-21 22:20:17,232 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 22:20:17,315 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 22:20:17,317 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (23.43%) exceeds limit (15.00%)
2025-06-21 22:20:17,317 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 22:20:17,317 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (54.7)
2025-06-21 22:20:17,318 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 22:20:24,943 - app.utils.ai_pattern_recognition - INFO - Using live price 18.03 EGP from API for SKPC
2025-06-21 22:20:24,943 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for SKPC
2025-06-21 22:20:24,944 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for SKPC
2025-06-21 22:20:24,945 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 22:20:24,947 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 22:20:25,149 - app.utils.memory_management - INFO - Memory before cleanup: 462.27 MB
2025-06-21 22:20:25,342 - app.utils.memory_management - INFO - Garbage collection: collected 1741 objects
2025-06-21 22:20:25,344 - app.utils.memory_management - INFO - Memory after cleanup: 462.27 MB (freed 0.00 MB)
2025-06-21 22:23:29,326 - app - INFO - Using TensorFlow-based LSTM model
2025-06-21 22:23:29,404 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-21 22:23:29,418 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.350
2025-06-21 22:23:29,541 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-21 22:23:29,656 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-21 22:23:29,659 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-21 22:23:29,661 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-21 22:23:29,661 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (50.6)
2025-06-21 22:23:29,661 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-21 22:23:38,180 - app.utils.ai_pattern_recognition - INFO - Using live price 45.97 EGP from API for ABUK
2025-06-21 22:23:38,182 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-21 22:23:38,182 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-21 22:23:38,182 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-21 22:23:38,184 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-21 22:23:38,372 - app.utils.memory_management - INFO - Memory before cleanup: 434.42 MB
2025-06-21 22:23:38,550 - app.utils.memory_management - INFO - Garbage collection: collected 1748 objects
2025-06-21 22:23:38,550 - app.utils.memory_management - INFO - Memory after cleanup: 434.42 MB (freed 0.00 MB)
