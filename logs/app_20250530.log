2025-05-30 07:53:15,131 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-30 07:53:17,691 - app - INFO - Memory management utilities loaded
2025-05-30 07:53:17,693 - app - INFO - Error handling utilities loaded
2025-05-30 07:53:17,693 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 07:53:17,694 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 07:53:17,694 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 07:53:17,694 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 07:53:17,698 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 07:53:17,699 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 07:53:17,699 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 07:53:17,699 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 07:53:17,699 - app - INFO - Applied NumPy fix
2025-05-30 07:53:17,700 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 07:53:17,700 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 07:53:17,700 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 07:53:17,701 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 07:53:17,701 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 07:53:17,701 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 07:53:17,701 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 07:53:17,701 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 07:53:27,519 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 07:53:27,519 - app - INFO - Applied TensorFlow fix
2025-05-30 07:53:27,521 - app.config - INFO - Configuration initialized
2025-05-30 07:53:27,523 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 07:53:27,770 - models.train - INFO - TensorFlow test successful
2025-05-30 07:53:29,755 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 07:53:29,755 - models.train - INFO - Transformer model is available
2025-05-30 07:53:29,756 - models.train - INFO - Using TensorFlow-based models
2025-05-30 07:53:29,757 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 07:53:29,757 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 07:53:29,758 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 07:53:30,137 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 07:53:30,137 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 07:53:30,138 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 07:53:30,138 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 07:53:30,138 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 07:53:30,138 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 07:53:30,139 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 07:53:30,139 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 07:53:30,139 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 07:53:30,139 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 07:53:30,201 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 07:53:30,213 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:53:30,465 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 07:53:31,177 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 07:53:31,189 - app.utils.session_state - INFO - Initializing session state
2025-05-30 07:53:31,190 - app.utils.session_state - INFO - Session state initialized
2025-05-30 07:53:32,259 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 07:53:32,268 - app.utils.memory_management - INFO - Memory before cleanup: 430.06 MB
2025-05-30 07:53:32,382 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 07:53:32,383 - app.utils.memory_management - INFO - Memory after cleanup: 430.06 MB (freed -0.00 MB)
2025-05-30 07:54:01,894 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:01,946 - app.utils.memory_management - INFO - Memory before cleanup: 434.05 MB
2025-05-30 07:54:02,142 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 07:54:02,143 - app.utils.memory_management - INFO - Memory after cleanup: 434.05 MB (freed 0.00 MB)
2025-05-30 07:54:08,799 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:08,954 - app - INFO - File COMI contains 2025 data
2025-05-30 07:54:08,976 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:54:08,977 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:54:08,980 - app.utils.memory_management - INFO - Memory before cleanup: 437.45 MB
2025-05-30 07:54:09,144 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-05-30 07:54:09,145 - app.utils.memory_management - INFO - Memory after cleanup: 437.49 MB (freed -0.04 MB)
2025-05-30 07:54:42,238 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:42,251 - app.utils.session_state - INFO - Initializing session state
2025-05-30 07:54:42,261 - app.utils.session_state - INFO - Session state initialized
2025-05-30 07:54:42,281 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 07:54:42,289 - app.utils.memory_management - INFO - Memory before cleanup: 437.86 MB
2025-05-30 07:54:42,479 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-30 07:54:42,480 - app.utils.memory_management - INFO - Memory after cleanup: 437.86 MB (freed 0.00 MB)
2025-05-30 07:54:49,644 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:49,663 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-30 07:54:49,690 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.02 seconds
2025-05-30 07:54:49,691 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-30 07:54:49,691 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-30 07:54:49,691 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-30 07:54:50,172 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.51 seconds
2025-05-30 07:54:50,173 - app.utils.memory_management - INFO - Memory before cleanup: 444.59 MB
2025-05-30 07:54:50,281 - app.utils.memory_management - INFO - Garbage collection: collected 2395 objects
2025-05-30 07:54:50,282 - app.utils.memory_management - INFO - Memory after cleanup: 444.59 MB (freed 0.00 MB)
2025-05-30 07:54:53,083 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:53,111 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-30 07:54:53,122 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-30 07:54:53,123 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-30 07:54:53,123 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-30 07:54:53,124 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-30 07:54:53,320 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.21 seconds
2025-05-30 07:54:53,322 - app.utils.memory_management - INFO - Memory before cleanup: 447.00 MB
2025-05-30 07:54:53,430 - app.utils.memory_management - INFO - Garbage collection: collected 1450 objects
2025-05-30 07:54:53,431 - app.utils.memory_management - INFO - Memory after cleanup: 447.00 MB (freed 0.00 MB)
2025-05-30 07:54:54,294 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:54:54,313 - app.utils.memory_management - INFO - Memory before cleanup: 446.98 MB
2025-05-30 07:54:54,433 - app.utils.memory_management - INFO - Garbage collection: collected 215 objects
2025-05-30 07:54:54,434 - app.utils.memory_management - INFO - Memory after cleanup: 446.98 MB (freed 0.00 MB)
2025-05-30 07:55:01,388 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:01,434 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:01,438 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:01,439 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:01,441 - app.utils.memory_management - INFO - Memory before cleanup: 447.02 MB
2025-05-30 07:55:01,621 - app.utils.memory_management - INFO - Garbage collection: collected 207 objects
2025-05-30 07:55:01,621 - app.utils.memory_management - INFO - Memory after cleanup: 447.02 MB (freed 0.00 MB)
2025-05-30 07:55:06,036 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:06,076 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:06,080 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:06,081 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:06,083 - app.utils.memory_management - INFO - Memory before cleanup: 447.03 MB
2025-05-30 07:55:06,216 - app.utils.memory_management - INFO - Garbage collection: collected 222 objects
2025-05-30 07:55:06,216 - app.utils.memory_management - INFO - Memory after cleanup: 447.03 MB (freed 0.00 MB)
2025-05-30 07:55:10,835 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:10,893 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:10,897 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:10,898 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:10,899 - app.utils.memory_management - INFO - Memory before cleanup: 447.07 MB
2025-05-30 07:55:11,036 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-30 07:55:11,037 - app.utils.memory_management - INFO - Memory after cleanup: 447.07 MB (freed 0.00 MB)
2025-05-30 07:55:12,638 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:12,709 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:12,716 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:12,717 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:12,719 - app.utils.memory_management - INFO - Memory before cleanup: 447.07 MB
2025-05-30 07:55:12,850 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-05-30 07:55:12,851 - app.utils.memory_management - INFO - Memory after cleanup: 447.07 MB (freed 0.00 MB)
2025-05-30 07:55:15,851 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:15,943 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:15,980 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:15,981 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:15,982 - app.utils.memory_management - INFO - Memory before cleanup: 447.16 MB
2025-05-30 07:55:16,111 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-05-30 07:55:16,119 - app.utils.memory_management - INFO - Memory after cleanup: 447.16 MB (freed 0.00 MB)
2025-05-30 07:55:23,262 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:23,395 - app - INFO - File COMI contains 2025 data
2025-05-30 07:55:23,427 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-30 07:55:23,428 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:55:23,430 - app.utils.memory_management - INFO - Memory before cleanup: 447.37 MB
2025-05-30 07:55:23,598 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-05-30 07:55:23,598 - app.utils.memory_management - INFO - Memory after cleanup: 447.37 MB (freed 0.00 MB)
2025-05-30 07:55:40,621 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:55:40,657 - app.utils.memory_management - INFO - Memory before cleanup: 447.36 MB
2025-05-30 07:55:40,823 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-05-30 07:55:40,824 - app.utils.memory_management - INFO - Memory after cleanup: 447.36 MB (freed 0.00 MB)
2025-05-30 07:56:01,265 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:56:01,316 - app.utils.memory_management - INFO - Memory before cleanup: 447.36 MB
2025-05-30 07:56:01,447 - app.utils.memory_management - INFO - Garbage collection: collected 200 objects
2025-05-30 07:56:01,447 - app.utils.memory_management - INFO - Memory after cleanup: 447.36 MB (freed 0.00 MB)
2025-05-30 07:56:57,384 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:56:57,440 - app.utils.memory_management - INFO - Memory before cleanup: 447.36 MB
2025-05-30 07:56:57,667 - app.utils.memory_management - INFO - Garbage collection: collected 228 objects
2025-05-30 07:56:57,668 - app.utils.memory_management - INFO - Memory after cleanup: 447.36 MB (freed 0.00 MB)
2025-05-30 07:56:57,806 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:56:57,823 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-30 07:56:57,824 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:57,825 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-30 07:56:57,831 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,832 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-30 07:56:57,832 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-30 07:56:57,832 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-30 07:56:57,837 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-30 07:56:57,837 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-30 07:56:57,842 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,850 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-30 07:56:57,855 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-30 07:56:57,855 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-30 07:56:57,855 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-30 07:56:57,856 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-30 07:56:57,861 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,861 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-30 07:56:57,861 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-30 07:56:57,862 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-30 07:56:57,862 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-30 07:56:57,862 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-30 07:56:57,866 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,867 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-30 07:56:57,867 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-30 07:56:57,868 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-30 07:56:57,868 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:57,868 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-30 07:56:57,872 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,874 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-30 07:56:57,874 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-30 07:56:57,893 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-30 07:56:57,913 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-30 07:56:57,914 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-30 07:56:57,921 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:57,928 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-30 07:56:57,929 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-30 07:56:57,929 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-30 07:56:57,929 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-30 07:56:57,929 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-30 07:56:57,930 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-30 07:56:57,932 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-30 07:56:57,933 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-30 07:56:57,933 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-30 07:56:57,934 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-30 07:56:57,934 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-30 07:56:57,934 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-30 07:56:57,934 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-30 07:56:57,935 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-30 07:56:57,936 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-30 07:56:57,937 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-30 07:56:57,938 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-30 07:56:57,938 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-30 07:56:57,938 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-30 07:56:58,061 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-30 07:56:58,080 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-30 07:56:58,080 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:58,080 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-30 07:56:58,089 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,091 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-30 07:56:58,093 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-30 07:56:58,102 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-30 07:56:58,102 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-30 07:56:58,106 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,112 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-30 07:56:58,112 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-30 07:56:58,112 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-30 07:56:58,114 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-30 07:56:58,117 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,118 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-30 07:56:58,118 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-30 07:56:58,118 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-30 07:56:58,119 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-30 07:56:58,124 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,126 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-30 07:56:58,126 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-30 07:56:58,126 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:58,128 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-30 07:56:58,132 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-30 07:56:58,133 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-30 07:56:58,137 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:58,143 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-30 07:56:58,145 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-30 07:56:58,145 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-30 07:56:58,145 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-30 07:56:58,152 - app.utils.memory_management - INFO - Memory before cleanup: 447.40 MB
2025-05-30 07:56:58,276 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 07:56:58,276 - app.utils.memory_management - INFO - Memory after cleanup: 447.40 MB (freed 0.00 MB)
2025-05-30 07:57:17,652 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 07:57:17,683 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-30 07:57:17,701 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-30 07:57:17,701 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 07:57:17,702 - app.utils.common - INFO - Data shape: (581, 36)
2025-05-30 07:57:17,702 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-30 07:57:17,729 - app.utils.memory_management - INFO - Memory before cleanup: 449.32 MB
2025-05-30 07:57:17,913 - app.utils.memory_management - INFO - Garbage collection: collected 326 objects
2025-05-30 07:57:17,913 - app.utils.memory_management - INFO - Memory after cleanup: 449.32 MB (freed 0.00 MB)
2025-05-30 08:22:30,348 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 08:22:30,393 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 08:22:30,437 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-30 08:22:30,478 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-30 08:22:30,480 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 08:22:30,481 - app.utils.common - INFO - Data shape: (581, 36)
2025-05-30 08:22:30,481 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-30 08:22:30,526 - app.utils.memory_management - INFO - Memory before cleanup: 445.26 MB
2025-05-30 08:22:30,710 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-05-30 08:22:30,711 - app.utils.memory_management - INFO - Memory after cleanup: 445.26 MB (freed -0.00 MB)
2025-05-30 09:04:05,220 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:04:06,376 - app - INFO - Memory management utilities loaded
2025-05-30 09:04:06,378 - app - INFO - Error handling utilities loaded
2025-05-30 09:04:06,379 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:04:06,380 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:04:06,380 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:04:06,380 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:04:06,381 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 09:04:06,381 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 09:04:06,382 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 09:04:06,385 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 09:04:06,386 - app - INFO - Applied NumPy fix
2025-05-30 09:04:06,387 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:04:06,388 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:04:06,388 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:04:06,388 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 09:04:06,388 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:04:06,389 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:04:06,389 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:04:06,389 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 09:04:10,085 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 09:04:10,085 - app - INFO - Applied TensorFlow fix
2025-05-30 09:04:10,088 - app.config - INFO - Configuration initialized
2025-05-30 09:04:10,092 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 09:04:10,099 - models.train - INFO - TensorFlow test successful
2025-05-30 09:04:10,543 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 09:04:10,543 - models.train - INFO - Transformer model is available
2025-05-30 09:04:10,544 - models.train - INFO - Using TensorFlow-based models
2025-05-30 09:04:10,545 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 09:04:10,545 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 09:04:10,547 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 09:04:10,816 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:04:10,816 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:04:10,816 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 09:04:10,817 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:04:10,818 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:04:10,818 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:04:10,894 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 09:04:10,896 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:11,214 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 09:04:11,796 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 09:04:11,825 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:04:11,826 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:04:13,030 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 09:04:13,041 - app.utils.memory_management - INFO - Memory before cleanup: 430.63 MB
2025-05-30 09:04:13,158 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-05-30 09:04:13,158 - app.utils.memory_management - INFO - Memory after cleanup: 430.64 MB (freed -0.01 MB)
2025-05-30 09:04:19,836 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:19,861 - app.utils.memory_management - INFO - Memory before cleanup: 433.97 MB
2025-05-30 09:04:20,011 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:04:20,011 - app.utils.memory_management - INFO - Memory after cleanup: 433.98 MB (freed -0.02 MB)
2025-05-30 09:04:24,460 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:24,480 - app.utils.memory_management - INFO - Memory before cleanup: 434.20 MB
2025-05-30 09:04:24,704 - app.utils.memory_management - INFO - Garbage collection: collected 177 objects
2025-05-30 09:04:24,704 - app.utils.memory_management - INFO - Memory after cleanup: 434.25 MB (freed -0.04 MB)
2025-05-30 09:04:26,489 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:26,517 - app.utils.memory_management - INFO - Memory before cleanup: 434.25 MB
2025-05-30 09:04:26,657 - app.utils.memory_management - INFO - Garbage collection: collected 177 objects
2025-05-30 09:04:26,657 - app.utils.memory_management - INFO - Memory after cleanup: 434.25 MB (freed 0.00 MB)
2025-05-30 09:04:31,412 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:31,435 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:04:31,438 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:04:31,467 - app.utils.memory_management - INFO - Memory before cleanup: 434.25 MB
2025-05-30 09:04:31,624 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-30 09:04:31,626 - app.utils.memory_management - INFO - Memory after cleanup: 434.25 MB (freed 0.00 MB)
2025-05-30 09:04:37,450 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:37,478 - app.utils.memory_management - INFO - Memory before cleanup: 435.30 MB
2025-05-30 09:04:37,640 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-30 09:04:37,640 - app.utils.memory_management - INFO - Memory after cleanup: 435.30 MB (freed 0.00 MB)
2025-05-30 09:04:38,493 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:38,568 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-30 09:04:38,569 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:04:38,569 - app - INFO - Data shape: (581, 36)
2025-05-30 09:04:38,569 - app - INFO - File COMI contains 2025 data
2025-05-30 09:04:38,623 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-05-30 09:04:38,623 - app - INFO - Features shape: (581, 36)
2025-05-30 09:04:38,640 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-30 09:04:38,641 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:04:38,641 - app - INFO - Data shape: (581, 36)
2025-05-30 09:04:38,641 - app - INFO - File COMI contains 2025 data
2025-05-30 09:04:38,645 - app.utils.memory_management - INFO - Memory before cleanup: 438.93 MB
2025-05-30 09:04:38,796 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-30 09:04:38,796 - app.utils.memory_management - INFO - Memory after cleanup: 438.93 MB (freed 0.00 MB)
2025-05-30 09:04:38,953 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:39,010 - app.utils.memory_management - INFO - Memory before cleanup: 439.89 MB
2025-05-30 09:04:39,163 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 09:04:39,164 - app.utils.memory_management - INFO - Memory after cleanup: 439.83 MB (freed 0.06 MB)
2025-05-30 09:04:42,170 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:04:43,652 - app.utils.memory_management - INFO - Memory before cleanup: 453.00 MB
2025-05-30 09:04:43,787 - app.utils.memory_management - INFO - Garbage collection: collected 1672 objects
2025-05-30 09:04:43,788 - app.utils.memory_management - INFO - Memory after cleanup: 453.00 MB (freed 0.00 MB)
2025-05-30 09:06:01,383 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:06:01,390 - app - INFO - Memory management utilities loaded
2025-05-30 09:06:01,392 - app - INFO - Error handling utilities loaded
2025-05-30 09:06:01,393 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:06:01,393 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:06:01,393 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:06:01,393 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:06:01,394 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: 'CompatibleMT19937' object has no attribute 'capsule'
2025-05-30 09:06:01,395 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-30 09:06:01,396 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-30 09:06:01,396 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 09:06:01,396 - app - INFO - Applied NumPy fix
2025-05-30 09:06:01,397 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:06:01,397 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:06:01,397 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:06:01,398 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 09:06:01,398 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:06:01,398 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:06:01,398 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:06:01,398 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 09:06:01,400 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 09:06:01,400 - app - INFO - Applied TensorFlow fix
2025-05-30 09:06:01,402 - app.config - INFO - Configuration initialized
2025-05-30 09:06:01,407 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 09:06:01,414 - models.train - INFO - TensorFlow test successful
2025-05-30 09:06:01,417 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 09:06:01,418 - models.train - INFO - Transformer model is available
2025-05-30 09:06:01,419 - models.train - INFO - Using TensorFlow-based models
2025-05-30 09:06:01,421 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 09:06:01,422 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 09:06:01,424 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 09:06:01,433 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:06:01,435 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:06:01,436 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:06:01,436 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:06:01,437 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:06:01,437 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 09:06:01,437 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'CompatibleBitGenerator' has no attribute 'register'
2025-05-30 09:06:01,438 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:06:01,438 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:06:01,438 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:06:01,440 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 09:06:01,443 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:06:01,469 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 09:06:01,488 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 09:06:01,510 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:06:01,511 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:06:01,528 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 09:06:01,538 - app.utils.memory_management - INFO - Memory before cleanup: 199.43 MB
2025-05-30 09:06:01,932 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-05-30 09:06:01,933 - app.utils.memory_management - INFO - Memory after cleanup: 385.62 MB (freed -186.19 MB)
2025-05-30 09:06:24,395 - app - INFO - Cleaning up resources...
2025-05-30 09:06:24,395 - app.utils.memory_management - INFO - Memory before cleanup: 387.89 MB
2025-05-30 09:06:24,503 - app.utils.memory_management - INFO - Garbage collection: collected 333 objects
2025-05-30 09:06:24,504 - app.utils.memory_management - INFO - Memory after cleanup: 387.93 MB (freed -0.04 MB)
2025-05-30 09:06:24,504 - app - INFO - Application shutdown complete
2025-05-30 09:06:24,505 - app - INFO - Cleaning up resources...
2025-05-30 09:06:24,505 - app.utils.memory_management - INFO - Memory before cleanup: 387.93 MB
2025-05-30 09:06:24,614 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-05-30 09:06:24,614 - app.utils.memory_management - INFO - Memory after cleanup: 387.93 MB (freed 0.00 MB)
2025-05-30 09:06:24,614 - app - INFO - Application shutdown complete
2025-05-30 09:12:09,796 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:12:10,921 - app - INFO - Memory management utilities loaded
2025-05-30 09:12:10,922 - app - INFO - Error handling utilities loaded
2025-05-30 09:12:10,923 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:12:10,924 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:12:10,924 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:12:10,924 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:12:10,924 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 09:12:10,925 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 09:12:10,925 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 09:12:10,925 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 09:12:10,925 - app - INFO - Applied NumPy fix
2025-05-30 09:12:10,926 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:12:10,926 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:12:10,927 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:12:10,927 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 09:12:10,927 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:12:10,927 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:12:10,927 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:12:10,928 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 09:12:13,867 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 09:12:13,867 - app - INFO - Applied TensorFlow fix
2025-05-30 09:12:13,868 - app.config - INFO - Configuration initialized
2025-05-30 09:12:13,871 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 09:12:13,880 - models.train - INFO - TensorFlow test successful
2025-05-30 09:12:14,270 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 09:12:14,270 - models.train - INFO - Transformer model is available
2025-05-30 09:12:14,270 - models.train - INFO - Using TensorFlow-based models
2025-05-30 09:12:14,272 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 09:12:14,272 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 09:12:14,274 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 09:12:14,521 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:12:14,522 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:12:14,522 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:12:14,522 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:12:14,523 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:12:14,523 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 09:12:14,523 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 09:12:14,523 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:12:14,523 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:12:14,524 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:12:14,583 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 09:12:14,584 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:14,849 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 09:12:15,255 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 09:12:15,282 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:12:15,283 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:12:16,430 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 09:12:16,438 - app.utils.memory_management - INFO - Memory before cleanup: 431.04 MB
2025-05-30 09:12:16,556 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:12:16,556 - app.utils.memory_management - INFO - Memory after cleanup: 431.47 MB (freed -0.43 MB)
2025-05-30 09:12:23,430 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:23,454 - app.utils.memory_management - INFO - Memory before cleanup: 434.82 MB
2025-05-30 09:12:23,576 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:12:23,577 - app.utils.memory_management - INFO - Memory after cleanup: 434.82 MB (freed 0.00 MB)
2025-05-30 09:12:24,707 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:24,743 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-30 09:12:24,744 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:12:24,744 - app - INFO - Data shape: (581, 36)
2025-05-30 09:12:24,744 - app - INFO - File COMI contains 2025 data
2025-05-30 09:12:24,770 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-30 09:12:24,770 - app - INFO - Features shape: (581, 36)
2025-05-30 09:12:24,785 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-30 09:12:24,786 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:12:24,786 - app - INFO - Data shape: (581, 36)
2025-05-30 09:12:24,786 - app - INFO - File COMI contains 2025 data
2025-05-30 09:12:24,788 - app.utils.memory_management - INFO - Memory before cleanup: 438.50 MB
2025-05-30 09:12:24,942 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-30 09:12:24,942 - app.utils.memory_management - INFO - Memory after cleanup: 438.54 MB (freed -0.04 MB)
2025-05-30 09:12:25,064 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:25,101 - app.utils.memory_management - INFO - Memory before cleanup: 439.65 MB
2025-05-30 09:12:25,222 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 09:12:25,222 - app.utils.memory_management - INFO - Memory after cleanup: 439.65 MB (freed 0.00 MB)
2025-05-30 09:12:26,405 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:27,145 - app.utils.memory_management - INFO - Memory before cleanup: 453.01 MB
2025-05-30 09:12:27,272 - app.utils.memory_management - INFO - Garbage collection: collected 2557 objects
2025-05-30 09:12:27,272 - app.utils.memory_management - INFO - Memory after cleanup: 453.01 MB (freed 0.00 MB)
2025-05-30 09:12:42,462 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:42,467 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:12:42,468 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:12:42,493 - app.utils.memory_management - INFO - Memory before cleanup: 454.98 MB
2025-05-30 09:12:42,613 - app.utils.memory_management - INFO - Garbage collection: collected 210 objects
2025-05-30 09:12:42,613 - app.utils.memory_management - INFO - Memory after cleanup: 454.98 MB (freed 0.00 MB)
2025-05-30 09:12:47,868 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:47,884 - app.utils.memory_management - INFO - Memory before cleanup: 455.08 MB
2025-05-30 09:12:48,041 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-30 09:12:48,042 - app.utils.memory_management - INFO - Memory after cleanup: 455.08 MB (freed 0.00 MB)
2025-05-30 09:12:48,961 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:48,980 - app.utils.memory_management - INFO - Memory before cleanup: 455.11 MB
2025-05-30 09:12:49,114 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-05-30 09:12:49,114 - app.utils.memory_management - INFO - Memory after cleanup: 455.11 MB (freed 0.00 MB)
2025-05-30 09:12:49,241 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:49,269 - app.utils.memory_management - INFO - Memory before cleanup: 455.11 MB
2025-05-30 09:12:49,387 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 09:12:49,388 - app.utils.memory_management - INFO - Memory after cleanup: 455.11 MB (freed 0.00 MB)
2025-05-30 09:12:51,640 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:12:52,132 - app.utils.memory_management - INFO - Memory before cleanup: 460.14 MB
2025-05-30 09:12:52,271 - app.utils.memory_management - INFO - Garbage collection: collected 2855 objects
2025-05-30 09:12:52,272 - app.utils.memory_management - INFO - Memory after cleanup: 460.12 MB (freed 0.02 MB)
2025-05-30 09:40:31,532 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:40:31,538 - app - INFO - Memory management utilities loaded
2025-05-30 09:40:31,539 - app - INFO - Error handling utilities loaded
2025-05-30 09:40:31,540 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:40:31,540 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:40:31,540 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:40:31,541 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:41:19,049 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:41:20,261 - app - INFO - Memory management utilities loaded
2025-05-30 09:41:20,263 - app - INFO - Error handling utilities loaded
2025-05-30 09:41:20,264 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:41:20,266 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:41:20,266 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:41:20,267 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:41:20,268 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 09:41:20,269 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 09:41:20,269 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 09:41:20,269 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 09:41:20,271 - app - INFO - Applied NumPy fix
2025-05-30 09:41:20,280 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:41:20,280 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:41:20,280 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:41:20,281 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 09:41:20,281 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:41:20,283 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:41:20,284 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:41:20,286 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 09:41:23,006 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 09:41:23,006 - app - INFO - Applied TensorFlow fix
2025-05-30 09:41:23,008 - app.config - INFO - Configuration initialized
2025-05-30 09:41:23,011 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 09:41:23,018 - models.train - INFO - TensorFlow test successful
2025-05-30 09:41:23,379 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 09:41:23,379 - models.train - INFO - Transformer model is available
2025-05-30 09:41:23,380 - models.train - INFO - Using TensorFlow-based models
2025-05-30 09:41:23,381 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 09:41:23,381 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 09:41:23,382 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 09:41:23,609 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:41:23,609 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:41:23,611 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:41:23,611 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:41:23,611 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:41:23,611 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 09:41:23,612 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 09:41:23,612 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:41:23,612 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:41:23,612 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:41:23,674 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 09:41:23,676 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:41:23,939 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 09:41:24,334 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 09:41:24,359 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:41:24,362 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:41:25,411 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 09:41:25,424 - app.utils.memory_management - INFO - Memory before cleanup: 430.07 MB
2025-05-30 09:41:25,578 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:41:25,592 - app.utils.memory_management - INFO - Memory after cleanup: 430.08 MB (freed -0.01 MB)
2025-05-30 09:41:31,871 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:41:31,893 - app.utils.memory_management - INFO - Memory before cleanup: 433.11 MB
2025-05-30 09:41:32,036 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:41:32,036 - app.utils.memory_management - INFO - Memory after cleanup: 433.11 MB (freed 0.00 MB)
2025-05-30 09:41:33,106 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:41:33,140 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-30 09:41:33,140 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:41:33,140 - app - INFO - Data shape: (581, 36)
2025-05-30 09:41:33,140 - app - INFO - File COMI contains 2025 data
2025-05-30 09:41:33,173 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-30 09:41:33,173 - app - INFO - Features shape: (581, 36)
2025-05-30 09:41:33,173 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-30 09:41:33,188 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:41:33,189 - app - INFO - Data shape: (581, 36)
2025-05-30 09:41:33,190 - app - INFO - File COMI contains 2025 data
2025-05-30 09:41:33,192 - app.utils.memory_management - INFO - Memory before cleanup: 438.04 MB
2025-05-30 09:41:33,312 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-30 09:41:33,312 - app.utils.memory_management - INFO - Memory after cleanup: 438.08 MB (freed -0.04 MB)
2025-05-30 09:41:33,421 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:41:33,466 - app.utils.memory_management - INFO - Memory before cleanup: 439.20 MB
2025-05-30 09:41:33,590 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 09:41:33,590 - app.utils.memory_management - INFO - Memory after cleanup: 439.20 MB (freed 0.00 MB)
2025-05-30 09:41:36,192 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:41:36,942 - app.utils.memory_management - INFO - Memory before cleanup: 452.18 MB
2025-05-30 09:41:37,062 - app.utils.memory_management - INFO - Garbage collection: collected 2580 objects
2025-05-30 09:41:37,062 - app.utils.memory_management - INFO - Memory after cleanup: 452.18 MB (freed 0.00 MB)
2025-05-30 09:50:57,241 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:50:57,244 - app - INFO - Memory management utilities loaded
2025-05-30 09:50:57,246 - app - INFO - Error handling utilities loaded
2025-05-30 09:50:57,246 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:50:57,246 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:50:57,247 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:50:57,247 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:51:13,344 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:51:14,717 - app - INFO - Memory management utilities loaded
2025-05-30 09:51:14,721 - app - INFO - Error handling utilities loaded
2025-05-30 09:51:14,724 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:51:14,728 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:51:14,731 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:51:14,735 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:51:14,740 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 09:51:14,747 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 09:51:14,752 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 09:51:14,754 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 09:51:14,758 - app - INFO - Applied NumPy fix
2025-05-30 09:51:14,762 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:51:14,771 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:51:14,772 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:51:14,774 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 09:51:14,778 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:51:14,778 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:51:14,779 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:51:14,779 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 09:51:17,649 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 09:51:17,649 - app - INFO - Applied TensorFlow fix
2025-05-30 09:51:17,653 - app.config - INFO - Configuration initialized
2025-05-30 09:51:17,656 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 09:51:17,663 - models.train - INFO - TensorFlow test successful
2025-05-30 09:51:18,065 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 09:51:18,065 - models.train - INFO - Transformer model is available
2025-05-30 09:51:18,065 - models.train - INFO - Using TensorFlow-based models
2025-05-30 09:51:18,066 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 09:51:18,067 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 09:51:18,070 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 09:51:18,315 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:51:18,315 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:51:18,316 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:51:18,316 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:51:18,316 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:51:18,316 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 09:51:18,317 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 09:51:18,317 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:51:18,317 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:51:18,317 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:51:18,390 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 09:51:18,392 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:51:18,660 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 09:51:19,101 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 09:51:19,130 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:51:19,130 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:51:20,213 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 09:51:20,223 - app.utils.memory_management - INFO - Memory before cleanup: 431.13 MB
2025-05-30 09:51:20,339 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:51:20,339 - app.utils.memory_management - INFO - Memory after cleanup: 431.14 MB (freed -0.01 MB)
2025-05-30 09:51:24,972 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:51:24,988 - app.utils.memory_management - INFO - Memory before cleanup: 433.92 MB
2025-05-30 09:51:25,108 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:51:25,110 - app.utils.memory_management - INFO - Memory after cleanup: 433.92 MB (freed -0.00 MB)
2025-05-30 09:51:26,312 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:51:26,359 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-30 09:51:26,360 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:51:26,361 - app - INFO - Data shape: (581, 36)
2025-05-30 09:51:26,362 - app - INFO - File COMI contains 2025 data
2025-05-30 09:51:26,384 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-30 09:51:26,384 - app - INFO - Features shape: (581, 36)
2025-05-30 09:51:26,398 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-30 09:51:26,399 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:51:26,399 - app - INFO - Data shape: (581, 36)
2025-05-30 09:51:26,400 - app - INFO - File COMI contains 2025 data
2025-05-30 09:51:26,401 - app.utils.memory_management - INFO - Memory before cleanup: 437.86 MB
2025-05-30 09:51:26,523 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-30 09:51:26,523 - app.utils.memory_management - INFO - Memory after cleanup: 437.90 MB (freed -0.04 MB)
2025-05-30 09:51:26,645 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:51:26,686 - app.utils.memory_management - INFO - Memory before cleanup: 439.06 MB
2025-05-30 09:51:26,808 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 09:51:26,808 - app.utils.memory_management - INFO - Memory after cleanup: 439.06 MB (freed 0.00 MB)
2025-05-30 09:51:28,566 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:51:28,585 - app.utils.session_state - ERROR - Error tracked: app_crash - 'int' object has no attribute 'strftime'
2025-05-30 09:51:28,586 - app - ERROR - Application crashed: 'int' object has no attribute 'strftime'
2025-05-30 09:51:28,586 - app.utils.memory_management - INFO - Memory before cleanup: 439.42 MB
2025-05-30 09:51:28,696 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-05-30 09:51:28,696 - app.utils.memory_management - INFO - Memory after cleanup: 439.42 MB (freed 0.00 MB)
2025-05-30 09:51:28,696 - app.utils.memory_management - INFO - Memory before cleanup: 439.42 MB
2025-05-30 09:51:28,827 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-30 09:51:28,827 - app.utils.memory_management - INFO - Memory after cleanup: 439.42 MB (freed 0.00 MB)
2025-05-30 09:52:48,332 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:52:48,336 - app - INFO - Memory management utilities loaded
2025-05-30 09:52:48,337 - app - INFO - Error handling utilities loaded
2025-05-30 09:52:48,337 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:52:48,338 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:52:48,338 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:52:48,341 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:53:16,479 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 09:53:17,600 - app - INFO - Memory management utilities loaded
2025-05-30 09:53:17,605 - app - INFO - Error handling utilities loaded
2025-05-30 09:53:17,605 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 09:53:17,608 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 09:53:17,608 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 09:53:17,608 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 09:53:17,610 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 09:53:17,610 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 09:53:17,610 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 09:53:17,610 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 09:53:17,610 - app - INFO - Applied NumPy fix
2025-05-30 09:53:17,610 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:53:17,610 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:53:17,610 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:53:17,610 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 09:53:17,610 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:53:17,610 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:53:17,610 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:53:17,610 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 09:53:20,230 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 09:53:20,231 - app - INFO - Applied TensorFlow fix
2025-05-30 09:53:20,232 - app.config - INFO - Configuration initialized
2025-05-30 09:53:20,234 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 09:53:20,242 - models.train - INFO - TensorFlow test successful
2025-05-30 09:53:20,642 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 09:53:20,642 - models.train - INFO - Transformer model is available
2025-05-30 09:53:20,642 - models.train - INFO - Using TensorFlow-based models
2025-05-30 09:53:20,643 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 09:53:20,644 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 09:53:20,645 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 09:53:20,862 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:53:20,862 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 09:53:20,862 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 09:53:20,863 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 09:53:20,863 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 09:53:20,863 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 09:53:20,863 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 09:53:20,864 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 09:53:20,864 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 09:53:20,864 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 09:53:20,924 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 09:53:20,925 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:53:21,177 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 09:53:21,559 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 09:53:21,582 - app.utils.session_state - INFO - Initializing session state
2025-05-30 09:53:21,584 - app.utils.session_state - INFO - Session state initialized
2025-05-30 09:53:22,624 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 09:53:22,632 - app.utils.memory_management - INFO - Memory before cleanup: 430.21 MB
2025-05-30 09:53:22,750 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:53:22,750 - app.utils.memory_management - INFO - Memory after cleanup: 430.28 MB (freed -0.07 MB)
2025-05-30 09:53:27,495 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:53:27,509 - app.utils.memory_management - INFO - Memory before cleanup: 433.46 MB
2025-05-30 09:53:27,635 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 09:53:27,641 - app.utils.memory_management - INFO - Memory after cleanup: 433.46 MB (freed -0.00 MB)
2025-05-30 09:53:28,747 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:53:28,782 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-30 09:53:28,783 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:53:28,783 - app - INFO - Data shape: (581, 36)
2025-05-30 09:53:28,783 - app - INFO - File COMI contains 2025 data
2025-05-30 09:53:28,806 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-30 09:53:28,806 - app - INFO - Features shape: (581, 36)
2025-05-30 09:53:28,822 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-30 09:53:28,822 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 09:53:28,822 - app - INFO - Data shape: (581, 36)
2025-05-30 09:53:28,823 - app - INFO - File COMI contains 2025 data
2025-05-30 09:53:28,825 - app.utils.memory_management - INFO - Memory before cleanup: 437.99 MB
2025-05-30 09:53:28,929 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-30 09:53:28,929 - app.utils.memory_management - INFO - Memory after cleanup: 438.03 MB (freed -0.04 MB)
2025-05-30 09:53:29,063 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:53:29,101 - app.utils.memory_management - INFO - Memory before cleanup: 439.13 MB
2025-05-30 09:53:29,214 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 09:53:29,215 - app.utils.memory_management - INFO - Memory after cleanup: 439.13 MB (freed -0.00 MB)
2025-05-30 09:53:30,815 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 09:53:31,188 - app.utils.memory_management - INFO - Memory before cleanup: 451.98 MB
2025-05-30 09:53:31,314 - app.utils.memory_management - INFO - Garbage collection: collected 933 objects
2025-05-30 09:53:31,314 - app.utils.memory_management - INFO - Memory after cleanup: 451.98 MB (freed 0.00 MB)
2025-05-30 15:17:37,151 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 15:17:37,156 - app - INFO - Memory management utilities loaded
2025-05-30 15:17:37,158 - app - INFO - Error handling utilities loaded
2025-05-30 15:17:37,159 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 15:17:37,159 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 15:17:37,159 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 15:17:37,159 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 15:18:05,671 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 15:18:06,844 - app - INFO - Memory management utilities loaded
2025-05-30 15:18:06,856 - app - INFO - Error handling utilities loaded
2025-05-30 15:18:06,857 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 15:18:06,858 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 15:18:06,859 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 15:18:06,859 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 15:18:06,860 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 15:18:06,861 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 15:18:06,861 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 15:18:06,861 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 15:18:06,862 - app - INFO - Applied NumPy fix
2025-05-30 15:18:06,863 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 15:18:06,863 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 15:18:06,864 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 15:18:06,865 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 15:18:06,865 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 15:18:06,865 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 15:18:06,866 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 15:18:06,867 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 15:18:10,253 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 15:18:10,254 - app - INFO - Applied TensorFlow fix
2025-05-30 15:18:10,256 - app.config - INFO - Configuration initialized
2025-05-30 15:18:10,260 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 15:18:10,266 - models.train - INFO - TensorFlow test successful
2025-05-30 15:18:10,674 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 15:18:10,674 - models.train - INFO - Transformer model is available
2025-05-30 15:18:10,674 - models.train - INFO - Using TensorFlow-based models
2025-05-30 15:18:10,674 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 15:18:10,674 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 15:18:10,674 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 15:18:10,953 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 15:18:10,953 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 15:18:10,953 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 15:18:10,954 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 15:18:10,954 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 15:18:10,954 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 15:18:10,954 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 15:18:10,954 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 15:18:10,954 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 15:18:10,954 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 15:18:11,025 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 15:18:11,027 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:18:11,304 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 15:18:11,782 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 15:18:11,811 - app.utils.session_state - INFO - Initializing session state
2025-05-30 15:18:11,812 - app.utils.session_state - INFO - Session state initialized
2025-05-30 15:18:12,841 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 15:18:12,851 - app.utils.memory_management - INFO - Memory before cleanup: 431.78 MB
2025-05-30 15:18:12,977 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 15:18:12,978 - app.utils.memory_management - INFO - Memory after cleanup: 431.79 MB (freed -0.00 MB)
2025-05-30 15:18:17,967 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:18:18,027 - app.utils.memory_management - INFO - Memory before cleanup: 434.70 MB
2025-05-30 15:18:18,155 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 15:18:18,156 - app.utils.memory_management - INFO - Memory after cleanup: 434.70 MB (freed 0.00 MB)
2025-05-30 15:18:19,449 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:18:19,555 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.04 seconds
2025-05-30 15:18:19,555 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 15:18:19,555 - app - INFO - Data shape: (581, 36)
2025-05-30 15:18:19,555 - app - INFO - File COMI contains 2025 data
2025-05-30 15:18:19,593 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-05-30 15:18:19,594 - app - INFO - Features shape: (581, 36)
2025-05-30 15:18:19,614 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-30 15:18:19,617 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 15:18:19,618 - app - INFO - Data shape: (581, 36)
2025-05-30 15:18:19,618 - app - INFO - File COMI contains 2025 data
2025-05-30 15:18:19,622 - app.utils.memory_management - INFO - Memory before cleanup: 438.79 MB
2025-05-30 15:18:19,750 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-30 15:18:19,750 - app.utils.memory_management - INFO - Memory after cleanup: 438.82 MB (freed -0.04 MB)
2025-05-30 15:18:19,883 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:18:19,936 - app.utils.memory_management - INFO - Memory before cleanup: 439.93 MB
2025-05-30 15:18:20,061 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 15:18:20,062 - app.utils.memory_management - INFO - Memory after cleanup: 439.89 MB (freed 0.04 MB)
2025-05-30 15:18:24,562 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:18:25,071 - app.utils.memory_management - INFO - Memory before cleanup: 452.75 MB
2025-05-30 15:18:25,213 - app.utils.memory_management - INFO - Garbage collection: collected 940 objects
2025-05-30 15:18:25,214 - app.utils.memory_management - INFO - Memory after cleanup: 452.73 MB (freed 0.02 MB)
2025-05-30 15:28:51,463 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 15:28:51,466 - app - INFO - Memory management utilities loaded
2025-05-30 15:28:51,467 - app - INFO - Error handling utilities loaded
2025-05-30 15:28:51,467 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 15:28:51,468 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 15:28:51,468 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 15:28:51,468 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 15:29:18,355 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-30 15:29:19,424 - app - INFO - Memory management utilities loaded
2025-05-30 15:29:19,425 - app - INFO - Error handling utilities loaded
2025-05-30 15:29:19,427 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-30 15:29:19,428 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-30 15:29:19,428 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-30 15:29:19,429 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-30 15:29:19,429 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-30 15:29:19,430 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-30 15:29:19,430 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-30 15:29:19,430 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-30 15:29:19,430 - app - INFO - Applied NumPy fix
2025-05-30 15:29:19,431 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 15:29:19,431 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 15:29:19,432 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 15:29:19,432 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-30 15:29:19,432 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 15:29:19,432 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 15:29:19,432 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 15:29:19,433 - app - INFO - Applied NumPy BitGenerator fix
2025-05-30 15:29:22,106 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-30 15:29:22,106 - app - INFO - Applied TensorFlow fix
2025-05-30 15:29:22,106 - app.config - INFO - Configuration initialized
2025-05-30 15:29:22,119 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-30 15:29:22,130 - models.train - INFO - TensorFlow test successful
2025-05-30 15:29:22,520 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-30 15:29:22,521 - models.train - INFO - Transformer model is available
2025-05-30 15:29:22,521 - models.train - INFO - Using TensorFlow-based models
2025-05-30 15:29:22,523 - models.predict - INFO - Transformer model is available for predictions
2025-05-30 15:29:22,523 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-30 15:29:22,525 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-30 15:29:22,743 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 15:29:22,744 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-30 15:29:22,744 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-30 15:29:22,744 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-30 15:29:22,744 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-30 15:29:22,745 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-30 15:29:22,745 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-30 15:29:22,745 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-30 15:29:22,745 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-30 15:29:22,745 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-30 15:29:22,803 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-30 15:29:22,804 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:29:23,052 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-30 15:29:23,429 - app.services.llm_service - INFO - llama_cpp is available
2025-05-30 15:29:23,444 - app.utils.session_state - INFO - Initializing session state
2025-05-30 15:29:23,445 - app.utils.session_state - INFO - Session state initialized
2025-05-30 15:29:24,488 - app - INFO - Found 8 stock files in data/stocks
2025-05-30 15:29:24,492 - app.utils.memory_management - INFO - Memory before cleanup: 429.96 MB
2025-05-30 15:29:24,615 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 15:29:24,615 - app.utils.memory_management - INFO - Memory after cleanup: 429.96 MB (freed -0.01 MB)
2025-05-30 15:29:31,459 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:29:31,475 - app.utils.memory_management - INFO - Memory before cleanup: 433.71 MB
2025-05-30 15:29:31,616 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-30 15:29:31,616 - app.utils.memory_management - INFO - Memory after cleanup: 433.71 MB (freed 0.00 MB)
2025-05-30 15:29:32,789 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:29:32,824 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-30 15:29:32,824 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 15:29:32,824 - app - INFO - Data shape: (581, 36)
2025-05-30 15:29:32,824 - app - INFO - File COMI contains 2025 data
2025-05-30 15:29:32,856 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-30 15:29:32,856 - app - INFO - Features shape: (581, 36)
2025-05-30 15:29:32,872 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-30 15:29:32,872 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-05-30 15:29:32,872 - app - INFO - Data shape: (581, 36)
2025-05-30 15:29:32,873 - app - INFO - File COMI contains 2025 data
2025-05-30 15:29:32,874 - app.utils.memory_management - INFO - Memory before cleanup: 437.86 MB
2025-05-30 15:29:32,991 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-30 15:29:32,991 - app.utils.memory_management - INFO - Memory after cleanup: 437.90 MB (freed -0.04 MB)
2025-05-30 15:29:33,105 - app - INFO - Using TensorFlow-based LSTM model
2025-05-30 15:29:33,142 - app.utils.memory_management - INFO - Memory before cleanup: 439.00 MB
2025-05-30 15:29:33,258 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-30 15:29:33,258 - app.utils.memory_management - INFO - Memory after cleanup: 439.00 MB (freed 0.00 MB)
2025-05-30 15:30:27,193 - app - INFO - Cleaning up resources...
2025-05-30 15:30:27,193 - app.utils.memory_management - INFO - Memory before cleanup: 438.93 MB
2025-05-30 15:30:27,299 - app.utils.memory_management - INFO - Garbage collection: collected 274 objects
2025-05-30 15:30:27,299 - app.utils.memory_management - INFO - Memory after cleanup: 438.93 MB (freed 0.00 MB)
2025-05-30 15:30:27,299 - app - INFO - Application shutdown complete
