2025-06-09 01:43:37,804 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-09 01:43:44,573 - app - INFO - Memory management utilities loaded
2025-06-09 01:43:44,580 - app - INFO - Error handling utilities loaded
2025-06-09 01:43:44,604 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 01:43:44,607 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 01:43:44,608 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 01:43:44,609 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 01:43:44,610 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 01:43:44,611 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 01:43:44,611 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 01:43:44,612 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 01:43:44,612 - app - INFO - Applied NumPy fix
2025-06-09 01:43:44,618 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 01:43:44,619 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 01:43:44,622 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 01:43:44,622 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 01:43:44,622 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 01:43:44,623 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 01:43:44,623 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 01:43:44,624 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 01:44:05,025 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 01:44:05,027 - app - INFO - Applied TensorFlow fix
2025-06-09 01:44:05,045 - app.config - INFO - Configuration initialized
2025-06-09 01:44:05,070 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 01:44:05,079 - models.train - INFO - TensorFlow test successful
2025-06-09 01:44:10,435 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 01:44:10,437 - models.train - INFO - Transformer model is available
2025-06-09 01:44:10,437 - models.train - INFO - Using TensorFlow-based models
2025-06-09 01:44:10,441 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 01:44:10,441 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 01:44:10,484 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 01:44:12,491 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 01:44:12,491 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 01:44:12,491 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 01:44:12,491 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 01:44:12,493 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 01:44:12,493 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 01:44:12,493 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 01:44:12,493 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 01:44:12,493 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 01:44:12,495 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 01:44:13,005 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 01:44:13,021 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:44:13,992 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 01:44:17,226 - app.utils.session_state - INFO - Initializing session state
2025-06-09 01:44:17,228 - app.utils.session_state - INFO - Session state initialized
2025-06-09 01:44:18,627 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 01:44:18,643 - app.utils.memory_management - INFO - Memory before cleanup: 424.97 MB
2025-06-09 01:44:18,813 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 01:44:18,816 - app.utils.memory_management - INFO - Memory after cleanup: 425.32 MB (freed -0.34 MB)
2025-06-09 01:45:01,059 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:45:01,087 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-09 01:45:01,165 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.08 seconds
2025-06-09 01:45:01,168 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-09 01:45:01,168 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-09 01:45:01,169 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-09 01:45:01,172 - app.utils.memory_management - INFO - Memory before cleanup: 431.61 MB
2025-06-09 01:45:01,456 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 01:45:01,458 - app.utils.memory_management - INFO - Memory after cleanup: 431.61 MB (freed -0.00 MB)
2025-06-09 01:45:01,627 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:45:01,680 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-09 01:45:01,707 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-09 01:45:01,708 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-09 01:45:01,709 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-09 01:45:01,712 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-09 01:45:01,762 - app.utils.memory_management - INFO - Memory before cleanup: 432.64 MB
2025-06-09 01:45:01,939 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-09 01:45:01,939 - app.utils.memory_management - INFO - Memory after cleanup: 432.68 MB (freed -0.04 MB)
2025-06-09 01:45:07,444 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:45:09,520 - app.utils.memory_management - INFO - Memory before cleanup: 432.68 MB
2025-06-09 01:45:09,715 - app.utils.memory_management - INFO - Garbage collection: collected 412 objects
2025-06-09 01:45:09,717 - app.utils.memory_management - INFO - Memory after cleanup: 432.68 MB (freed 0.00 MB)
2025-06-09 01:46:14,724 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:46:14,729 - app.utils.session_state - INFO - Initializing session state
2025-06-09 01:46:14,732 - app.utils.session_state - INFO - Session state initialized
2025-06-09 01:46:14,774 - app.utils.memory_management - INFO - Memory before cleanup: 433.24 MB
2025-06-09 01:46:15,050 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-09 01:46:15,052 - app.utils.memory_management - INFO - Memory after cleanup: 433.24 MB (freed 0.00 MB)
2025-06-09 01:46:19,573 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:46:19,612 - app.utils.memory_management - INFO - Memory before cleanup: 433.41 MB
2025-06-09 01:46:19,880 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-09 01:46:19,880 - app.utils.memory_management - INFO - Memory after cleanup: 433.41 MB (freed 0.00 MB)
2025-06-09 01:46:24,437 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:46:24,480 - app.utils.memory_management - INFO - Memory before cleanup: 433.41 MB
2025-06-09 01:46:24,770 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-09 01:46:24,770 - app.utils.memory_management - INFO - Memory after cleanup: 433.41 MB (freed 0.00 MB)
2025-06-09 01:46:25,584 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:46:25,612 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-09 01:46:25,626 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-09 01:46:25,629 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-09 01:46:25,631 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-09 01:46:25,634 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-09 01:46:25,638 - app.utils.memory_management - INFO - Memory before cleanup: 433.41 MB
2025-06-09 01:46:25,880 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-09 01:46:25,880 - app.utils.memory_management - INFO - Memory after cleanup: 433.41 MB (freed 0.00 MB)
2025-06-09 01:46:26,040 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:46:26,116 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-09 01:46:26,155 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-09 01:46:26,157 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-09 01:46:26,160 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-09 01:46:26,162 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-09 01:46:26,252 - app.utils.memory_management - INFO - Memory before cleanup: 433.41 MB
2025-06-09 01:46:26,470 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-09 01:46:26,473 - app.utils.memory_management - INFO - Memory after cleanup: 433.41 MB (freed 0.00 MB)
2025-06-09 01:47:48,518 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:47:48,611 - app.utils.memory_management - INFO - Memory before cleanup: 434.47 MB
2025-06-09 01:47:48,811 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-09 01:47:48,815 - app.utils.memory_management - INFO - Memory after cleanup: 434.47 MB (freed 0.00 MB)
2025-06-09 01:47:54,138 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:47:54,214 - app.utils.memory_management - INFO - Memory before cleanup: 434.91 MB
2025-06-09 01:47:54,421 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 01:47:54,421 - app.utils.memory_management - INFO - Memory after cleanup: 434.91 MB (freed 0.00 MB)
2025-06-09 01:47:55,625 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:47:55,671 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-09 01:47:55,672 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-09 01:47:55,673 - app - INFO - Data shape: (585, 36)
2025-06-09 01:47:55,673 - app - INFO - File COMI contains 2025 data
2025-06-09 01:47:55,706 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-06-09 01:47:55,708 - app - INFO - Features shape: (585, 36)
2025-06-09 01:47:55,745 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-09 01:47:55,748 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-09 01:47:55,750 - app - INFO - Data shape: (585, 36)
2025-06-09 01:47:55,751 - app - INFO - File COMI contains 2025 data
2025-06-09 01:47:55,759 - app.utils.memory_management - INFO - Memory before cleanup: 436.07 MB
2025-06-09 01:47:55,968 - app.utils.memory_management - INFO - Garbage collection: collected 304 objects
2025-06-09 01:47:55,968 - app.utils.memory_management - INFO - Memory after cleanup: 436.07 MB (freed 0.00 MB)
2025-06-09 01:47:56,151 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:47:56,215 - app.utils.memory_management - INFO - Memory before cleanup: 436.04 MB
2025-06-09 01:47:56,418 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-09 01:47:56,419 - app.utils.memory_management - INFO - Memory after cleanup: 436.02 MB (freed 0.02 MB)
2025-06-09 01:48:22,539 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:48:22,915 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-09 01:48:25,786 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-09 01:48:25,833 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.03 seconds
2025-06-09 01:48:25,835 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-06-09 01:48:25,835 - app.utils.common - INFO - Data shape: (309, 6)
2025-06-09 01:48:25,835 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-09 01:48:26,766 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.98 seconds
2025-06-09 01:48:27,244 - app.utils.memory_management - INFO - Memory before cleanup: 445.53 MB
2025-06-09 01:48:27,488 - app.utils.memory_management - INFO - Garbage collection: collected 3027 objects
2025-06-09 01:48:27,490 - app.utils.memory_management - INFO - Memory after cleanup: 445.51 MB (freed 0.02 MB)
2025-06-09 01:48:42,426 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:48:42,965 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-09 01:48:43,380 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-09 01:48:43,397 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-06-09 01:48:43,399 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-06-09 01:48:43,401 - app.utils.common - INFO - Data shape: (309, 6)
2025-06-09 01:48:43,402 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-09 01:48:43,863 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.52 seconds
2025-06-09 01:48:44,183 - app.utils.memory_management - INFO - Memory before cleanup: 447.85 MB
2025-06-09 01:48:44,186 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:48:44,494 - app.utils.memory_management - INFO - Garbage collection: collected 2362 objects
2025-06-09 01:48:44,497 - app.utils.memory_management - INFO - Memory after cleanup: 447.83 MB (freed 0.02 MB)
2025-06-09 01:48:44,765 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-06-09 01:48:46,202 - app.utils.memory_management - INFO - Memory before cleanup: 459.14 MB
2025-06-09 01:48:46,407 - app.utils.memory_management - INFO - Garbage collection: collected 695 objects
2025-06-09 01:48:46,408 - app.utils.memory_management - INFO - Memory after cleanup: 459.16 MB (freed -0.02 MB)
2025-06-09 01:49:06,198 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:49:06,672 - app.utils.memory_management - INFO - Memory before cleanup: 462.44 MB
2025-06-09 01:49:06,942 - app.utils.memory_management - INFO - Garbage collection: collected 386 objects
2025-06-09 01:49:06,943 - app.utils.memory_management - INFO - Memory after cleanup: 462.44 MB (freed 0.00 MB)
2025-06-09 01:49:07,432 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:49:07,468 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-09 01:49:07,468 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:07,469 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-09 01:49:07,475 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,476 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-09 01:49:07,477 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-09 01:49:07,478 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-09 01:49:07,485 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-09 01:49:07,486 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-09 01:49:07,492 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,499 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-09 01:49:07,506 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-06-09 01:49:07,508 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-09 01:49:07,508 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-09 01:49:07,510 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-09 01:49:07,516 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,517 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-09 01:49:07,519 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-06-09 01:49:07,522 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-09 01:49:07,524 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-09 01:49:07,525 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-09 01:49:07,531 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,532 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-09 01:49:07,533 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-06-09 01:49:07,533 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-09 01:49:07,533 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:07,534 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-09 01:49:07,540 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,541 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-09 01:49:07,541 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-09 01:49:07,584 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-09 01:49:07,591 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-09 01:49:07,592 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-09 01:49:07,598 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,624 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-09 01:49:07,626 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-09 01:49:07,634 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-09 01:49:07,634 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-09 01:49:07,635 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-09 01:49:07,636 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-09 01:49:07,640 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-09 01:49:07,640 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-09 01:49:07,641 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-09 01:49:07,641 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-09 01:49:07,642 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-09 01:49:07,642 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-09 01:49:07,642 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-09 01:49:07,643 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-09 01:49:07,644 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-09 01:49:07,646 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-09 01:49:07,648 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-09 01:49:07,649 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-09 01:49:07,649 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-06-09 01:49:07,899 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-06-09 01:49:07,951 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-09 01:49:07,952 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:07,953 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-09 01:49:07,966 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,968 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-09 01:49:07,969 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-09 01:49:07,975 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-09 01:49:07,976 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-09 01:49:07,982 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,989 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-09 01:49:07,990 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-09 01:49:07,991 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-09 01:49:07,992 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-09 01:49:07,998 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:07,999 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-09 01:49:08,000 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-09 01:49:08,000 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-09 01:49:08,001 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-09 01:49:08,008 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:08,009 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-09 01:49:08,010 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-09 01:49:08,010 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:08,010 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-09 01:49:08,017 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:08,018 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-09 01:49:08,028 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:08,035 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-09 01:49:08,036 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-09 01:49:08,036 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-09 01:49:08,037 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:08,051 - app.utils.memory_management - INFO - Memory before cleanup: 462.42 MB
2025-06-09 01:49:08,261 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-09 01:49:08,262 - app.utils.memory_management - INFO - Memory after cleanup: 462.42 MB (freed 0.00 MB)
2025-06-09 01:49:16,376 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:49:16,435 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-09 01:49:16,449 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-09 01:49:16,456 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-09 01:49:16,465 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:16,474 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-09 01:49:16,476 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-09 01:49:16,483 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-09 01:49:16,485 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-09 01:49:16,487 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-09 01:49:16,492 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-09 01:49:16,494 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-09 01:49:16,495 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-09 01:49:16,497 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-09 01:49:16,498 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-06-09 01:49:16,711 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-06-09 01:49:16,767 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-09 01:49:16,768 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:16,769 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-09 01:49:16,779 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:16,779 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-09 01:49:16,780 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-09 01:49:16,786 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-09 01:49:16,788 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-09 01:49:16,795 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:16,800 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-09 01:49:16,802 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-09 01:49:16,805 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-09 01:49:16,805 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-09 01:49:16,811 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:16,812 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-09 01:49:16,812 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-09 01:49:16,814 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-09 01:49:16,815 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-09 01:49:16,820 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:16,821 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-09 01:49:16,822 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-09 01:49:16,822 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:16,824 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-09 01:49:16,831 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-09 01:49:16,833 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-09 01:49:16,845 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:16,852 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-09 01:49:16,853 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-09 01:49:16,854 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-09 01:49:16,854 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-09 01:49:16,870 - app.utils.memory_management - INFO - Memory before cleanup: 462.70 MB
2025-06-09 01:49:17,090 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 01:49:17,092 - app.utils.memory_management - INFO - Memory after cleanup: 462.70 MB (freed 0.00 MB)
2025-06-09 01:49:17,487 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:49:17,529 - app.utils.memory_management - INFO - Memory before cleanup: 462.70 MB
2025-06-09 01:49:17,769 - app.utils.memory_management - INFO - Garbage collection: collected 321 objects
2025-06-09 01:49:17,771 - app.utils.memory_management - INFO - Memory after cleanup: 462.70 MB (freed 0.00 MB)
2025-06-09 01:49:25,016 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 01:49:25,040 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 01:49:25,070 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-09 01:49:25,092 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-09 01:49:25,095 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-09 01:49:25,099 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-09 01:49:25,753 - app.components.advanced_smc_features - INFO - Found 12 high swing points with lookback 10
2025-06-09 01:49:25,827 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-09 01:49:25,829 - app.components.advanced_smc_features - INFO - Found 12 swing highs and 8 swing lows
2025-06-09 01:49:25,840 - app.components.advanced_smc_features - INFO - Detected 11 BOS events
2025-06-09 01:49:26,321 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-09 01:49:26,386 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-09 01:49:26,386 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-09 01:49:26,397 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-09 01:49:26,790 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-09 01:49:26,848 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-09 01:49:26,849 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-09 01:49:26,859 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-09 01:49:27,218 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-09 01:49:27,278 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-09 01:49:27,280 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-09 01:49:27,290 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-09 01:49:27,328 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 01:49:27,328 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 01:49:29,216 - app.utils.memory_management - INFO - Memory before cleanup: 470.14 MB
2025-06-09 01:49:29,448 - app.utils.memory_management - INFO - Garbage collection: collected 1200 objects
2025-06-09 01:49:29,450 - app.utils.memory_management - INFO - Memory after cleanup: 470.14 MB (freed 0.00 MB)
2025-06-09 11:41:14,294 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 11:41:18,317 - app - INFO - Memory management utilities loaded
2025-06-09 11:41:18,325 - app - INFO - Error handling utilities loaded
2025-06-09 11:41:18,332 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 11:41:18,334 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 11:41:18,336 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 11:41:18,338 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 11:41:18,344 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 11:41:18,346 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 11:41:18,347 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 11:41:18,348 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 11:41:18,349 - app - INFO - Applied NumPy fix
2025-06-09 11:41:18,364 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 11:41:18,365 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 11:41:18,366 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 11:41:18,367 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 11:41:18,368 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 11:41:18,368 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 11:41:18,369 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 11:41:18,370 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 11:41:39,548 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 11:41:39,550 - app - INFO - Applied TensorFlow fix
2025-06-09 11:41:39,572 - app.config - INFO - Configuration initialized
2025-06-09 11:41:39,599 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 11:41:39,623 - models.train - INFO - TensorFlow test successful
2025-06-09 11:41:41,585 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 11:41:41,585 - models.train - INFO - Transformer model is available
2025-06-09 11:41:41,585 - models.train - INFO - Using TensorFlow-based models
2025-06-09 11:41:41,596 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 11:41:41,597 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 11:41:41,613 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 11:41:42,323 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 11:41:42,329 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 11:41:42,329 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 11:41:42,329 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 11:41:42,329 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 11:41:42,329 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 11:41:42,329 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 11:41:42,329 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 11:41:42,329 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 11:41:42,331 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 11:41:42,601 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 11:41:42,612 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 11:41:43,488 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 11:41:45,571 - app.utils.session_state - INFO - Initializing session state
2025-06-09 11:41:45,573 - app.utils.session_state - INFO - Session state initialized
2025-06-09 11:41:46,795 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 11:41:46,810 - app.utils.memory_management - INFO - Memory before cleanup: 425.69 MB
2025-06-09 11:41:46,980 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-09 11:41:46,980 - app.utils.memory_management - INFO - Memory after cleanup: 425.70 MB (freed -0.00 MB)
2025-06-09 15:19:06,408 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:19:09,014 - app - INFO - Memory management utilities loaded
2025-06-09 15:19:09,014 - app - INFO - Error handling utilities loaded
2025-06-09 15:19:09,014 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:19:09,014 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:19:09,014 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:19:09,014 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:19:09,014 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 15:19:09,014 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 15:19:09,014 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 15:19:09,014 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 15:19:09,014 - app - INFO - Applied NumPy fix
2025-06-09 15:19:09,014 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:19:09,023 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:19:09,023 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:19:09,023 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 15:19:09,023 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:19:09,023 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:19:09,023 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:19:09,023 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 15:19:23,661 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 15:19:23,661 - app - INFO - Applied TensorFlow fix
2025-06-09 15:19:23,663 - app.config - INFO - Configuration initialized
2025-06-09 15:19:23,666 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 15:19:23,945 - models.train - INFO - TensorFlow test successful
2025-06-09 15:19:26,336 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 15:19:26,336 - models.train - INFO - Transformer model is available
2025-06-09 15:19:26,337 - models.train - INFO - Using TensorFlow-based models
2025-06-09 15:19:26,338 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 15:19:26,338 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 15:19:26,340 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 15:19:26,759 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:19:26,760 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:19:26,760 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:19:26,760 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:19:26,760 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:19:26,760 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 15:19:26,761 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 15:19:26,761 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:19:26,761 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:19:26,761 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:19:26,838 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 15:19:26,840 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:19:27,146 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 15:19:27,924 - app.utils.session_state - INFO - Initializing session state
2025-06-09 15:19:27,925 - app.utils.session_state - INFO - Session state initialized
2025-06-09 15:19:29,266 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 15:19:29,280 - app.utils.memory_management - INFO - Memory before cleanup: 425.40 MB
2025-06-09 15:19:29,471 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:19:29,471 - app.utils.memory_management - INFO - Memory after cleanup: 425.74 MB (freed -0.34 MB)
2025-06-09 15:19:55,629 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:19:55,666 - app.utils.memory_management - INFO - Memory before cleanup: 429.06 MB
2025-06-09 15:19:55,858 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:19:55,874 - app.utils.memory_management - INFO - Memory after cleanup: 429.07 MB (freed -0.00 MB)
2025-06-09 15:20:11,092 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:20:11,798 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:20:11,798 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:20:11,929 - app.utils.memory_management - INFO - Memory before cleanup: 433.36 MB
2025-06-09 15:20:12,143 - app.utils.memory_management - INFO - Garbage collection: collected 397 objects
2025-06-09 15:20:12,147 - app.utils.memory_management - INFO - Memory after cleanup: 433.40 MB (freed -0.04 MB)
2025-06-09 15:28:51,802 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:28:51,809 - app - INFO - Memory management utilities loaded
2025-06-09 15:28:51,812 - app - INFO - Error handling utilities loaded
2025-06-09 15:28:51,813 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:28:51,813 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:28:51,814 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:28:51,814 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:29:20,029 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:29:24,219 - app - INFO - Memory management utilities loaded
2025-06-09 15:29:24,221 - app - INFO - Error handling utilities loaded
2025-06-09 15:29:24,222 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:29:24,224 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:29:24,224 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:29:24,224 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:29:24,225 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 15:29:24,226 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 15:29:24,226 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 15:29:24,226 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 15:29:24,227 - app - INFO - Applied NumPy fix
2025-06-09 15:29:24,228 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:29:24,229 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:29:24,229 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:29:24,229 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 15:29:24,229 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:29:24,229 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:29:24,230 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:29:24,230 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 15:29:41,938 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 15:29:41,938 - app - INFO - Applied TensorFlow fix
2025-06-09 15:29:41,943 - app.config - INFO - Configuration initialized
2025-06-09 15:29:41,947 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 15:29:41,957 - models.train - INFO - TensorFlow test successful
2025-06-09 15:29:45,936 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 15:29:45,937 - models.train - INFO - Transformer model is available
2025-06-09 15:29:45,937 - models.train - INFO - Using TensorFlow-based models
2025-06-09 15:29:45,938 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 15:29:45,939 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 15:29:45,942 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 15:29:47,296 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:29:47,297 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:29:47,297 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:29:47,297 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:29:47,297 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:29:47,297 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 15:29:47,298 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 15:29:47,298 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:29:47,298 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:29:47,298 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:29:47,513 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 15:29:47,516 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:29:47,890 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 15:29:49,691 - app.utils.session_state - INFO - Initializing session state
2025-06-09 15:29:49,692 - app.utils.session_state - INFO - Session state initialized
2025-06-09 15:29:50,869 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 15:29:50,884 - app.utils.memory_management - INFO - Memory before cleanup: 425.14 MB
2025-06-09 15:29:51,058 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:29:51,059 - app.utils.memory_management - INFO - Memory after cleanup: 425.48 MB (freed -0.34 MB)
2025-06-09 15:29:55,696 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:29:55,752 - app.utils.memory_management - INFO - Memory before cleanup: 428.98 MB
2025-06-09 15:29:55,936 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:29:55,939 - app.utils.memory_management - INFO - Memory after cleanup: 428.98 MB (freed 0.00 MB)
2025-06-09 15:30:00,183 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:30:00,787 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:30:00,787 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:30:00,938 - app.utils.memory_management - INFO - Memory before cleanup: 433.20 MB
2025-06-09 15:30:01,123 - app.utils.memory_management - INFO - Garbage collection: collected 397 objects
2025-06-09 15:30:01,127 - app.utils.memory_management - INFO - Memory after cleanup: 433.24 MB (freed -0.04 MB)
2025-06-09 15:33:35,095 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:33:35,162 - app.utils.memory_management - INFO - Memory before cleanup: 434.60 MB
2025-06-09 15:33:35,437 - app.utils.memory_management - INFO - Garbage collection: collected 224 objects
2025-06-09 15:33:35,456 - app.utils.memory_management - INFO - Memory after cleanup: 434.60 MB (freed 0.00 MB)
2025-06-09 15:33:40,439 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:33:40,509 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:33:40,509 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:33:40,510 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:33:40,510 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:33:40,603 - app.utils.memory_management - INFO - Memory before cleanup: 436.10 MB
2025-06-09 15:33:40,786 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-06-09 15:33:40,786 - app.utils.memory_management - INFO - Memory after cleanup: 436.10 MB (freed 0.00 MB)
2025-06-09 15:43:26,811 - app - INFO - Cleaning up resources...
2025-06-09 15:43:26,811 - app.utils.memory_management - INFO - Memory before cleanup: 435.96 MB
2025-06-09 15:43:27,077 - app.utils.memory_management - INFO - Garbage collection: collected 303 objects
2025-06-09 15:43:27,077 - app.utils.memory_management - INFO - Memory after cleanup: 435.98 MB (freed -0.03 MB)
2025-06-09 15:43:27,077 - app - INFO - Application shutdown complete
2025-06-09 15:44:39,683 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:44:41,765 - app - INFO - Memory management utilities loaded
2025-06-09 15:44:41,773 - app - INFO - Error handling utilities loaded
2025-06-09 15:44:41,790 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:44:41,807 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:44:41,813 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:44:41,815 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:44:41,816 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 15:44:41,817 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 15:44:41,818 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 15:44:41,818 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 15:44:41,818 - app - INFO - Applied NumPy fix
2025-06-09 15:44:41,823 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:44:41,825 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:44:41,826 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:44:41,827 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 15:44:41,827 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:44:41,828 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:44:41,829 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:44:41,830 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 15:44:46,673 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 15:44:46,673 - app - INFO - Applied TensorFlow fix
2025-06-09 15:44:46,676 - app.config - INFO - Configuration initialized
2025-06-09 15:44:46,682 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 15:44:46,692 - models.train - INFO - TensorFlow test successful
2025-06-09 15:44:47,266 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 15:44:47,267 - models.train - INFO - Transformer model is available
2025-06-09 15:44:47,267 - models.train - INFO - Using TensorFlow-based models
2025-06-09 15:44:47,269 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 15:44:47,269 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 15:44:47,273 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 15:44:47,639 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:44:47,640 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:44:47,640 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:44:47,640 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:44:47,640 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:44:47,640 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 15:44:47,641 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 15:44:47,641 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:44:47,642 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:44:47,642 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:44:47,756 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 15:44:47,760 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:44:48,385 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 15:44:49,010 - app.utils.session_state - INFO - Initializing session state
2025-06-09 15:44:49,011 - app.utils.session_state - INFO - Session state initialized
2025-06-09 15:44:50,143 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 15:44:50,156 - app.utils.memory_management - INFO - Memory before cleanup: 425.80 MB
2025-06-09 15:44:50,325 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:44:50,326 - app.utils.memory_management - INFO - Memory after cleanup: 426.14 MB (freed -0.34 MB)
2025-06-09 15:44:56,306 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:44:56,365 - app.utils.memory_management - INFO - Memory before cleanup: 429.73 MB
2025-06-09 15:44:56,558 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:44:56,560 - app.utils.memory_management - INFO - Memory after cleanup: 429.73 MB (freed 0.00 MB)
2025-06-09 15:45:03,112 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:45:03,737 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:45:03,737 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 15:45:03,888 - app.utils.memory_management - INFO - Memory before cleanup: 434.06 MB
2025-06-09 15:45:04,079 - app.utils.memory_management - INFO - Garbage collection: collected 401 objects
2025-06-09 15:45:04,080 - app.utils.memory_management - INFO - Memory after cleanup: 434.10 MB (freed -0.04 MB)
2025-06-09 15:52:37,635 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:52:39,313 - app - INFO - Memory management utilities loaded
2025-06-09 15:52:39,315 - app - INFO - Error handling utilities loaded
2025-06-09 15:52:39,317 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:52:39,319 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:52:39,319 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:52:39,322 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:52:39,325 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 15:52:39,327 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 15:52:39,328 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 15:52:39,329 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 15:52:39,330 - app - INFO - Applied NumPy fix
2025-06-09 15:52:39,332 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:52:39,333 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:52:39,334 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:52:39,334 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 15:52:39,342 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:52:39,343 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:52:39,344 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:52:39,347 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 15:52:44,522 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 15:52:44,522 - app - INFO - Applied TensorFlow fix
2025-06-09 15:52:44,525 - app.config - INFO - Configuration initialized
2025-06-09 15:52:44,533 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 15:52:44,546 - models.train - INFO - TensorFlow test successful
2025-06-09 15:52:45,256 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 15:52:45,257 - models.train - INFO - Transformer model is available
2025-06-09 15:52:45,257 - models.train - INFO - Using TensorFlow-based models
2025-06-09 15:52:45,258 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 15:52:45,259 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 15:52:45,262 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 15:52:45,648 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:52:45,648 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:52:45,649 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:52:45,649 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:52:45,649 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:52:45,649 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 15:52:45,650 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 15:52:45,650 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:52:45,650 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:52:45,650 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:52:45,770 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 15:52:45,773 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:52:46,183 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 15:52:46,828 - app.utils.session_state - INFO - Initializing session state
2025-06-09 15:52:46,830 - app.utils.session_state - INFO - Session state initialized
2025-06-09 15:52:48,070 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 15:52:48,081 - app.utils.memory_management - INFO - Memory before cleanup: 424.59 MB
2025-06-09 15:52:48,297 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:52:48,298 - app.utils.memory_management - INFO - Memory after cleanup: 424.68 MB (freed -0.09 MB)
2025-06-09 15:52:52,048 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:52:52,116 - app.utils.memory_management - INFO - Memory before cleanup: 428.83 MB
2025-06-09 15:52:52,347 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:52:52,348 - app.utils.memory_management - INFO - Memory after cleanup: 428.83 MB (freed 0.00 MB)
2025-06-09 15:53:09,472 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:53:09,477 - app.utils.session_state - INFO - Initializing session state
2025-06-09 15:53:09,477 - app.utils.session_state - INFO - Session state initialized
2025-06-09 15:53:09,500 - app.utils.memory_management - INFO - Memory before cleanup: 429.20 MB
2025-06-09 15:53:09,689 - app.utils.memory_management - INFO - Garbage collection: collected 200 objects
2025-06-09 15:53:09,690 - app.utils.memory_management - INFO - Memory after cleanup: 429.23 MB (freed -0.04 MB)
2025-06-09 15:53:14,464 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:53:14,517 - app.utils.memory_management - INFO - Memory before cleanup: 430.08 MB
2025-06-09 15:53:14,718 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-09 15:53:14,719 - app.utils.memory_management - INFO - Memory after cleanup: 430.08 MB (freed 0.00 MB)
2025-06-09 15:54:27,046 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:54:27,055 - app - INFO - Memory management utilities loaded
2025-06-09 15:54:27,060 - app - INFO - Error handling utilities loaded
2025-06-09 15:54:27,070 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:54:27,072 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:54:27,076 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:54:27,078 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:54:43,408 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:54:43,441 - app - INFO - Memory management utilities loaded
2025-06-09 15:54:43,465 - app - INFO - Error handling utilities loaded
2025-06-09 15:54:43,470 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:54:43,472 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:54:43,474 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:54:43,478 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:55:15,983 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:55:17,724 - app - INFO - Memory management utilities loaded
2025-06-09 15:55:17,726 - app - INFO - Error handling utilities loaded
2025-06-09 15:55:17,728 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:55:17,730 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:55:17,730 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:55:17,730 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:55:17,730 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 15:55:17,732 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 15:55:17,733 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 15:55:17,733 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 15:55:17,733 - app - INFO - Applied NumPy fix
2025-06-09 15:55:17,741 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:55:17,742 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:55:17,743 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:55:17,744 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 15:55:17,744 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:55:17,745 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:55:17,745 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:55:17,745 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 15:55:22,402 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 15:55:22,402 - app - INFO - Applied TensorFlow fix
2025-06-09 15:55:22,404 - app.config - INFO - Configuration initialized
2025-06-09 15:55:22,413 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 15:55:22,423 - models.train - INFO - TensorFlow test successful
2025-06-09 15:55:23,042 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 15:55:23,042 - models.train - INFO - Transformer model is available
2025-06-09 15:55:23,042 - models.train - INFO - Using TensorFlow-based models
2025-06-09 15:55:23,044 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 15:55:23,045 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 15:55:23,048 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 15:55:23,389 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:55:23,389 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:55:23,390 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:55:23,390 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:55:23,390 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:55:23,390 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 15:55:23,390 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 15:55:23,390 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:55:23,390 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:55:23,391 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:55:23,505 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 15:55:23,508 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:55:23,910 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 15:55:24,543 - app.utils.session_state - INFO - Initializing session state
2025-06-09 15:55:24,544 - app.utils.session_state - INFO - Session state initialized
2025-06-09 15:55:25,735 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 15:55:25,749 - app.utils.memory_management - INFO - Memory before cleanup: 425.90 MB
2025-06-09 15:55:25,966 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:55:25,967 - app.utils.memory_management - INFO - Memory after cleanup: 425.91 MB (freed -0.00 MB)
2025-06-09 15:55:28,199 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:55:28,254 - app.utils.memory_management - INFO - Memory before cleanup: 429.43 MB
2025-06-09 15:55:28,442 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:55:28,446 - app.utils.memory_management - INFO - Memory after cleanup: 429.43 MB (freed 0.00 MB)
2025-06-09 15:58:34,293 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:58:36,287 - app - INFO - Memory management utilities loaded
2025-06-09 15:58:36,289 - app - INFO - Error handling utilities loaded
2025-06-09 15:58:36,291 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:58:36,293 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:58:36,295 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:58:36,297 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 15:58:36,302 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 15:58:36,305 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 15:58:36,308 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 15:58:36,309 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 15:58:36,311 - app - INFO - Applied NumPy fix
2025-06-09 15:58:36,313 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:58:36,314 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:58:36,319 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:58:36,321 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 15:58:36,322 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:58:36,325 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:58:36,326 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:58:36,327 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 15:58:42,026 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 15:58:42,026 - app - INFO - Applied TensorFlow fix
2025-06-09 15:58:42,029 - app.config - INFO - Configuration initialized
2025-06-09 15:58:42,036 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 15:58:42,046 - models.train - INFO - TensorFlow test successful
2025-06-09 15:58:42,746 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 15:58:42,746 - models.train - INFO - Transformer model is available
2025-06-09 15:58:42,747 - models.train - INFO - Using TensorFlow-based models
2025-06-09 15:58:42,749 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 15:58:42,749 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 15:58:42,753 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 15:58:43,135 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:58:43,136 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 15:58:43,136 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 15:58:43,136 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 15:58:43,136 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 15:58:43,136 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 15:58:43,137 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 15:58:43,137 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 15:58:43,137 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 15:58:43,137 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 15:58:43,326 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 15:58:43,330 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:58:43,765 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 15:58:44,633 - app.utils.session_state - INFO - Initializing session state
2025-06-09 15:58:44,633 - app.utils.session_state - INFO - Session state initialized
2025-06-09 15:58:45,962 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 15:58:45,978 - app.utils.memory_management - INFO - Memory before cleanup: 427.08 MB
2025-06-09 15:58:46,177 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:58:46,178 - app.utils.memory_management - INFO - Memory after cleanup: 427.38 MB (freed -0.29 MB)
2025-06-09 15:58:48,601 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 15:58:48,656 - app.utils.memory_management - INFO - Memory before cleanup: 431.06 MB
2025-06-09 15:58:48,853 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 15:58:48,854 - app.utils.memory_management - INFO - Memory after cleanup: 431.06 MB (freed 0.00 MB)
2025-06-09 15:59:47,331 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 15:59:47,340 - app - INFO - Memory management utilities loaded
2025-06-09 15:59:47,344 - app - INFO - Error handling utilities loaded
2025-06-09 15:59:47,348 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 15:59:47,360 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 15:59:47,360 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 15:59:47,361 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:01:02,049 - app - INFO - Cleaning up resources...
2025-06-09 16:01:02,053 - app.utils.memory_management - INFO - Memory before cleanup: 426.48 MB
2025-06-09 16:01:02,239 - app.utils.memory_management - INFO - Garbage collection: collected 215 objects
2025-06-09 16:01:02,247 - app.utils.memory_management - INFO - Memory after cleanup: 426.52 MB (freed -0.04 MB)
2025-06-09 16:01:02,255 - app - INFO - Application shutdown complete
2025-06-09 16:01:03,206 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:01:04,896 - app - INFO - Memory management utilities loaded
2025-06-09 16:01:04,896 - app - INFO - Error handling utilities loaded
2025-06-09 16:01:04,896 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:01:04,896 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:01:04,896 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:01:04,896 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:01:04,896 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:01:04,905 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:01:04,905 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:01:04,905 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:01:04,906 - app - INFO - Applied NumPy fix
2025-06-09 16:01:04,907 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:01:04,907 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:01:04,907 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:01:04,908 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:01:04,910 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:01:04,910 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:01:04,912 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:01:04,912 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:01:10,069 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:01:10,070 - app - INFO - Applied TensorFlow fix
2025-06-09 16:01:10,074 - app.config - INFO - Configuration initialized
2025-06-09 16:01:10,092 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:01:10,107 - models.train - INFO - TensorFlow test successful
2025-06-09 16:01:11,159 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:01:11,160 - models.train - INFO - Transformer model is available
2025-06-09 16:01:11,161 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:01:11,163 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:01:11,166 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:01:11,169 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:01:11,620 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:01:11,625 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:01:11,627 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:01:11,635 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:01:11,640 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:01:11,642 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:01:11,644 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:01:11,648 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:01:11,651 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:01:11,657 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:01:11,826 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:01:11,834 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:01:12,348 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:01:13,135 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:01:13,136 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:01:14,322 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:01:14,334 - app.utils.memory_management - INFO - Memory before cleanup: 426.23 MB
2025-06-09 16:01:14,503 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:01:14,504 - app.utils.memory_management - INFO - Memory after cleanup: 426.23 MB (freed -0.00 MB)
2025-06-09 16:01:18,202 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:01:18,243 - app.utils.memory_management - INFO - Memory before cleanup: 428.93 MB
2025-06-09 16:01:18,423 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:01:18,426 - app.utils.memory_management - INFO - Memory after cleanup: 428.93 MB (freed 0.00 MB)
2025-06-09 16:02:49,817 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:02:51,790 - app - INFO - Memory management utilities loaded
2025-06-09 16:02:51,795 - app - INFO - Error handling utilities loaded
2025-06-09 16:02:51,798 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:02:51,799 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:02:51,799 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:02:51,800 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:02:51,801 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:02:51,803 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:02:51,805 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:02:51,807 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:02:51,808 - app - INFO - Applied NumPy fix
2025-06-09 16:02:51,810 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:02:51,811 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:02:51,811 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:02:51,816 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:02:51,818 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:02:51,821 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:02:51,821 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:02:51,822 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:02:57,107 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:02:57,107 - app - INFO - Applied TensorFlow fix
2025-06-09 16:02:57,110 - app.config - INFO - Configuration initialized
2025-06-09 16:02:57,115 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:02:57,128 - models.train - INFO - TensorFlow test successful
2025-06-09 16:02:57,823 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:02:57,823 - models.train - INFO - Transformer model is available
2025-06-09 16:02:57,823 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:02:57,825 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:02:57,825 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:02:57,827 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:02:58,180 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:02:58,180 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:02:58,180 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:02:58,180 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:02:58,180 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:02:58,180 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:02:58,180 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:02:58,180 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:02:58,180 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:02:58,180 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:02:58,303 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:02:58,303 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:02:58,800 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:02:59,488 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:02:59,490 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:03:00,713 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:03:00,741 - app.utils.memory_management - INFO - Memory before cleanup: 426.12 MB
2025-06-09 16:03:00,905 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:03:00,907 - app.utils.memory_management - INFO - Memory after cleanup: 426.49 MB (freed -0.36 MB)
2025-06-09 16:03:03,993 - app - INFO - Cleaning up resources...
2025-06-09 16:03:03,995 - app.utils.memory_management - INFO - Memory before cleanup: 428.91 MB
2025-06-09 16:03:04,141 - app.utils.memory_management - INFO - Garbage collection: collected 277 objects
2025-06-09 16:03:04,141 - app.utils.memory_management - INFO - Memory after cleanup: 428.93 MB (freed -0.02 MB)
2025-06-09 16:03:04,141 - app - INFO - Application shutdown complete
2025-06-09 16:03:14,341 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:03:14,348 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:03:14,349 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:03:14,367 - app.utils.memory_management - INFO - Memory before cleanup: 429.57 MB
2025-06-09 16:03:14,568 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:03:14,568 - app.utils.memory_management - INFO - Memory after cleanup: 429.57 MB (freed 0.00 MB)
2025-06-09 16:03:19,138 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:03:19,182 - app.utils.memory_management - INFO - Memory before cleanup: 430.61 MB
2025-06-09 16:03:19,381 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-09 16:03:19,383 - app.utils.memory_management - INFO - Memory after cleanup: 430.65 MB (freed -0.04 MB)
2025-06-09 16:03:30,208 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:03:30,955 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:03:30,955 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:03:31,132 - app.utils.memory_management - INFO - Memory before cleanup: 434.47 MB
2025-06-09 16:03:31,317 - app.utils.memory_management - INFO - Garbage collection: collected 408 objects
2025-06-09 16:03:31,319 - app.utils.memory_management - INFO - Memory after cleanup: 434.47 MB (freed 0.00 MB)
2025-06-09 16:06:14,976 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:06:14,981 - app - INFO - Memory management utilities loaded
2025-06-09 16:06:14,983 - app - INFO - Error handling utilities loaded
2025-06-09 16:06:14,985 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:06:14,985 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:06:14,985 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:06:14,986 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:06:38,121 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:06:39,896 - app - INFO - Memory management utilities loaded
2025-06-09 16:06:39,900 - app - INFO - Error handling utilities loaded
2025-06-09 16:06:39,903 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:06:39,906 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:06:39,907 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:06:39,908 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:06:39,913 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:06:39,915 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:06:39,919 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:06:39,920 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:06:39,920 - app - INFO - Applied NumPy fix
2025-06-09 16:06:39,922 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:06:39,926 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:06:39,926 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:06:39,928 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:06:39,928 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:06:39,929 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:06:39,929 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:06:39,931 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:06:44,485 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:06:44,486 - app - INFO - Applied TensorFlow fix
2025-06-09 16:06:44,489 - app.config - INFO - Configuration initialized
2025-06-09 16:06:44,497 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:06:44,509 - models.train - INFO - TensorFlow test successful
2025-06-09 16:06:45,121 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:06:45,121 - models.train - INFO - Transformer model is available
2025-06-09 16:06:45,121 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:06:45,124 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:06:45,126 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:06:45,126 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:06:45,474 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:06:45,475 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:06:45,475 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:06:45,475 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:06:45,475 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:06:45,475 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:06:45,476 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:06:45,476 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:06:45,476 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:06:45,476 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:06:45,583 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:06:45,586 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:06:45,999 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:06:46,633 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:06:46,634 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:06:47,967 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:06:47,987 - app.utils.memory_management - INFO - Memory before cleanup: 425.46 MB
2025-06-09 16:06:48,170 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:06:48,170 - app.utils.memory_management - INFO - Memory after cleanup: 425.46 MB (freed -0.00 MB)
2025-06-09 16:06:50,571 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:06:50,590 - app.utils.memory_management - INFO - Memory before cleanup: 429.04 MB
2025-06-09 16:06:50,764 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:06:50,764 - app.utils.memory_management - INFO - Memory after cleanup: 429.04 MB (freed 0.00 MB)
2025-06-09 16:06:56,493 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:06:56,529 - app.utils.memory_management - INFO - Memory before cleanup: 429.21 MB
2025-06-09 16:06:56,716 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-09 16:06:56,719 - app.utils.memory_management - INFO - Memory after cleanup: 429.25 MB (freed -0.04 MB)
2025-06-09 16:08:18,297 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:08:18,304 - app - INFO - Memory management utilities loaded
2025-06-09 16:08:18,308 - app - INFO - Error handling utilities loaded
2025-06-09 16:08:18,315 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:08:18,318 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:08:18,320 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:08:18,321 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:08:38,533 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:08:40,272 - app - INFO - Memory management utilities loaded
2025-06-09 16:08:40,274 - app - INFO - Error handling utilities loaded
2025-06-09 16:08:40,276 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:08:40,278 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:08:40,278 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:08:40,278 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:08:40,281 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:08:40,287 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:08:40,289 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:08:40,289 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:08:40,291 - app - INFO - Applied NumPy fix
2025-06-09 16:08:40,294 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:08:40,295 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:08:40,296 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:08:40,297 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:08:40,298 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:08:40,302 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:08:40,302 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:08:40,304 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:08:44,710 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:08:44,710 - app - INFO - Applied TensorFlow fix
2025-06-09 16:08:44,712 - app.config - INFO - Configuration initialized
2025-06-09 16:08:44,718 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:08:44,731 - models.train - INFO - TensorFlow test successful
2025-06-09 16:08:45,317 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:08:45,317 - models.train - INFO - Transformer model is available
2025-06-09 16:08:45,318 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:08:45,319 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:08:45,320 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:08:45,321 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:08:45,668 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:08:45,669 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:08:45,669 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:08:45,669 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:08:45,669 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:08:45,670 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:08:45,671 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:08:45,671 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:08:45,671 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:08:45,671 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:08:45,776 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:08:45,778 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:08:46,145 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:08:46,782 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:08:46,783 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:08:48,056 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:08:48,069 - app.utils.memory_management - INFO - Memory before cleanup: 425.32 MB
2025-06-09 16:08:48,245 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:08:48,246 - app.utils.memory_management - INFO - Memory after cleanup: 425.33 MB (freed -0.01 MB)
2025-06-09 16:08:51,095 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:08:51,133 - app.utils.memory_management - INFO - Memory before cleanup: 428.81 MB
2025-06-09 16:08:51,317 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:08:51,317 - app.utils.memory_management - INFO - Memory after cleanup: 428.81 MB (freed -0.00 MB)
2025-06-09 16:08:57,175 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:08:57,842 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:08:57,845 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:08:58,026 - app.utils.memory_management - INFO - Memory before cleanup: 433.05 MB
2025-06-09 16:08:58,209 - app.utils.memory_management - INFO - Garbage collection: collected 406 objects
2025-06-09 16:08:58,211 - app.utils.memory_management - INFO - Memory after cleanup: 433.09 MB (freed -0.04 MB)
2025-06-09 16:13:01,095 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:13:01,104 - app - INFO - Memory management utilities loaded
2025-06-09 16:13:01,108 - app - INFO - Error handling utilities loaded
2025-06-09 16:13:01,135 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:13:01,136 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:13:01,137 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:13:01,137 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:13:25,107 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:13:26,895 - app - INFO - Memory management utilities loaded
2025-06-09 16:13:26,899 - app - INFO - Error handling utilities loaded
2025-06-09 16:13:26,899 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:13:26,901 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:13:26,903 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:13:26,903 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:13:26,905 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:13:26,906 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:13:26,907 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:13:26,907 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:13:26,908 - app - INFO - Applied NumPy fix
2025-06-09 16:13:26,916 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:13:26,918 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:13:26,919 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:13:26,919 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:13:26,919 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:13:26,920 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:13:26,920 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:13:26,920 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:13:31,349 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:13:31,349 - app - INFO - Applied TensorFlow fix
2025-06-09 16:13:31,352 - app.config - INFO - Configuration initialized
2025-06-09 16:13:31,357 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:13:31,370 - models.train - INFO - TensorFlow test successful
2025-06-09 16:13:31,926 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:13:31,926 - models.train - INFO - Transformer model is available
2025-06-09 16:13:31,926 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:13:31,926 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:13:31,926 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:13:31,933 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:13:32,288 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:13:32,288 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:13:32,289 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:13:32,289 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:13:32,289 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:13:32,289 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:13:32,289 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:13:32,290 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:13:32,290 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:13:32,290 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:13:32,396 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:13:32,400 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:13:32,759 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:13:33,378 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:13:33,380 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:13:34,605 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:13:34,620 - app.utils.memory_management - INFO - Memory before cleanup: 426.01 MB
2025-06-09 16:13:34,771 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:13:34,771 - app.utils.memory_management - INFO - Memory after cleanup: 426.37 MB (freed -0.36 MB)
2025-06-09 16:13:39,255 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:13:39,309 - app.utils.memory_management - INFO - Memory before cleanup: 429.82 MB
2025-06-09 16:13:39,492 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:13:39,493 - app.utils.memory_management - INFO - Memory after cleanup: 429.82 MB (freed 0.00 MB)
2025-06-09 16:20:08,808 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:20:08,817 - app - INFO - Memory management utilities loaded
2025-06-09 16:20:08,819 - app - INFO - Error handling utilities loaded
2025-06-09 16:20:08,822 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:20:08,822 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:20:08,823 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:20:08,823 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:20:35,368 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:20:37,133 - app - INFO - Memory management utilities loaded
2025-06-09 16:20:37,137 - app - INFO - Error handling utilities loaded
2025-06-09 16:20:37,137 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:20:37,139 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:20:37,139 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:20:37,140 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:20:37,144 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:20:37,144 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:20:37,146 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:20:37,148 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:20:37,152 - app - INFO - Applied NumPy fix
2025-06-09 16:20:37,154 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:20:37,154 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:20:37,154 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:20:37,154 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:20:37,156 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:20:37,156 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:20:37,156 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:20:37,158 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:20:41,659 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:20:41,659 - app - INFO - Applied TensorFlow fix
2025-06-09 16:20:41,662 - app.config - INFO - Configuration initialized
2025-06-09 16:20:41,667 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:20:41,678 - models.train - INFO - TensorFlow test successful
2025-06-09 16:20:42,263 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:20:42,264 - models.train - INFO - Transformer model is available
2025-06-09 16:20:42,265 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:20:42,267 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:20:42,268 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:20:42,272 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:20:42,613 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:20:42,613 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:20:42,613 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:20:42,613 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:20:42,613 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:20:42,614 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:20:42,614 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:20:42,614 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:20:42,614 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:20:42,614 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:20:42,721 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:20:42,724 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:20:43,116 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:20:43,743 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:20:43,744 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:20:45,096 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:20:45,111 - app.utils.memory_management - INFO - Memory before cleanup: 425.09 MB
2025-06-09 16:20:45,293 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:20:45,293 - app.utils.memory_management - INFO - Memory after cleanup: 425.45 MB (freed -0.36 MB)
2025-06-09 16:20:47,823 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:20:47,875 - app.utils.memory_management - INFO - Memory before cleanup: 429.12 MB
2025-06-09 16:20:48,057 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:20:48,058 - app.utils.memory_management - INFO - Memory after cleanup: 429.12 MB (freed 0.00 MB)
2025-06-09 16:20:54,301 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:20:55,024 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:20:55,025 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:20:55,201 - app.utils.memory_management - INFO - Memory before cleanup: 434.03 MB
2025-06-09 16:20:55,386 - app.utils.memory_management - INFO - Garbage collection: collected 406 objects
2025-06-09 16:20:55,386 - app.utils.memory_management - INFO - Memory after cleanup: 434.07 MB (freed -0.04 MB)
2025-06-09 16:31:02,532 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:31:02,559 - app - INFO - Memory management utilities loaded
2025-06-09 16:31:02,572 - app - INFO - Error handling utilities loaded
2025-06-09 16:31:02,587 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:31:02,588 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:31:02,592 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:31:02,598 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:31:19,312 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:31:20,926 - app - INFO - Memory management utilities loaded
2025-06-09 16:31:20,926 - app - INFO - Error handling utilities loaded
2025-06-09 16:31:20,931 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:31:20,932 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:31:20,932 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:31:20,933 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:31:20,933 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:31:20,934 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:31:20,934 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:31:20,934 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:31:20,935 - app - INFO - Applied NumPy fix
2025-06-09 16:31:20,936 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:31:20,936 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:31:20,937 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:31:20,937 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:31:20,938 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:31:20,939 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:31:20,940 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:31:20,940 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:31:25,927 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:31:25,927 - app - INFO - Applied TensorFlow fix
2025-06-09 16:31:25,930 - app.config - INFO - Configuration initialized
2025-06-09 16:31:25,936 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:31:25,945 - models.train - INFO - TensorFlow test successful
2025-06-09 16:31:26,524 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:31:26,524 - models.train - INFO - Transformer model is available
2025-06-09 16:31:26,524 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:31:26,526 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:31:26,527 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:31:26,530 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:31:26,863 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:31:26,864 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:31:26,864 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:31:26,864 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:31:26,864 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:31:26,864 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:31:26,864 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:31:26,865 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:31:26,865 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:31:26,865 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:31:26,980 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:31:26,982 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:31:26,983 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:31:27,351 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:31:28,008 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:31:28,012 - app.utils.memory_management - INFO - Memory before cleanup: 422.57 MB
2025-06-09 16:31:28,013 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:31:28,160 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:31:28,162 - app.utils.memory_management - INFO - Memory after cleanup: 422.60 MB (freed -0.03 MB)
2025-06-09 16:31:29,431 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:31:29,443 - app.utils.memory_management - INFO - Memory before cleanup: 426.54 MB
2025-06-09 16:31:29,634 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:31:29,635 - app.utils.memory_management - INFO - Memory after cleanup: 426.54 MB (freed -0.00 MB)
2025-06-09 16:31:32,051 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:31:32,102 - app.utils.memory_management - INFO - Memory before cleanup: 429.63 MB
2025-06-09 16:31:32,288 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 16:31:32,289 - app.utils.memory_management - INFO - Memory after cleanup: 429.71 MB (freed -0.07 MB)
2025-06-09 16:31:49,598 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:31:50,245 - app.pages.buy_sell_recommendations - ERROR - Error in comprehensive recommendation generation: PredictiveAnalytics.__init__() got an unexpected keyword argument 'use_ensemble'
2025-06-09 16:31:50,253 - app.utils.memory_management - INFO - Memory before cleanup: 432.27 MB
2025-06-09 16:31:50,432 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 16:31:50,432 - app.utils.memory_management - INFO - Memory after cleanup: 432.27 MB (freed 0.00 MB)
2025-06-09 16:35:59,864 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:35:59,872 - app - INFO - Memory management utilities loaded
2025-06-09 16:35:59,875 - app - INFO - Error handling utilities loaded
2025-06-09 16:35:59,878 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:35:59,879 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:35:59,879 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:35:59,879 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:36:22,104 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:36:23,842 - app - INFO - Memory management utilities loaded
2025-06-09 16:36:23,848 - app - INFO - Error handling utilities loaded
2025-06-09 16:36:23,851 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:36:23,853 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:36:23,854 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:36:23,857 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:36:23,859 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:36:23,861 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:36:23,862 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:36:23,864 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:36:23,867 - app - INFO - Applied NumPy fix
2025-06-09 16:36:23,871 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:36:23,875 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:36:23,876 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:36:23,878 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:36:23,880 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:36:23,881 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:36:23,881 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:36:23,883 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:36:28,522 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:36:28,523 - app - INFO - Applied TensorFlow fix
2025-06-09 16:36:28,526 - app.config - INFO - Configuration initialized
2025-06-09 16:36:28,531 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:36:28,543 - models.train - INFO - TensorFlow test successful
2025-06-09 16:36:29,100 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:36:29,101 - models.train - INFO - Transformer model is available
2025-06-09 16:36:29,101 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:36:29,103 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:36:29,103 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:36:29,106 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:36:29,444 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:36:29,444 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:36:29,444 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:36:29,444 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:36:29,444 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:36:29,445 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:36:29,445 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:36:29,445 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:36:29,445 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:36:29,445 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:36:29,552 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:36:29,555 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:36:29,555 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:36:29,950 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:36:30,602 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:36:30,605 - app.utils.memory_management - INFO - Memory before cleanup: 423.34 MB
2025-06-09 16:36:30,606 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:36:30,754 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-09 16:36:30,756 - app.utils.memory_management - INFO - Memory after cleanup: 423.36 MB (freed -0.02 MB)
2025-06-09 16:36:32,205 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:36:32,216 - app.utils.memory_management - INFO - Memory before cleanup: 426.75 MB
2025-06-09 16:36:32,399 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:36:32,401 - app.utils.memory_management - INFO - Memory after cleanup: 426.75 MB (freed 0.00 MB)
2025-06-09 16:36:35,174 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:36:35,223 - app.utils.memory_management - INFO - Memory before cleanup: 429.70 MB
2025-06-09 16:36:35,416 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 16:36:35,417 - app.utils.memory_management - INFO - Memory after cleanup: 429.73 MB (freed -0.04 MB)
2025-06-09 16:36:40,535 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:36:41,224 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:36:41,224 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:36:41,389 - app.utils.memory_management - INFO - Memory before cleanup: 433.90 MB
2025-06-09 16:36:41,575 - app.utils.memory_management - INFO - Garbage collection: collected 406 objects
2025-06-09 16:36:41,577 - app.utils.memory_management - INFO - Memory after cleanup: 433.90 MB (freed 0.00 MB)
2025-06-09 16:40:26,181 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:40:26,187 - app - INFO - Memory management utilities loaded
2025-06-09 16:40:26,190 - app - INFO - Error handling utilities loaded
2025-06-09 16:40:26,192 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:40:26,193 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:40:26,193 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:40:26,193 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:40:47,004 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:40:48,803 - app - INFO - Memory management utilities loaded
2025-06-09 16:40:48,809 - app - INFO - Error handling utilities loaded
2025-06-09 16:40:48,812 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:40:48,820 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:40:48,822 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:40:48,824 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:40:48,826 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:40:48,830 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:40:48,832 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:40:48,834 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:40:48,836 - app - INFO - Applied NumPy fix
2025-06-09 16:40:48,838 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:40:48,840 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:40:48,842 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:40:48,842 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:40:48,844 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:40:48,849 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:40:48,853 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:40:48,853 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:40:53,382 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:40:53,383 - app - INFO - Applied TensorFlow fix
2025-06-09 16:40:53,386 - app.config - INFO - Configuration initialized
2025-06-09 16:40:53,390 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:40:53,402 - models.train - INFO - TensorFlow test successful
2025-06-09 16:40:53,993 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:40:53,994 - models.train - INFO - Transformer model is available
2025-06-09 16:40:53,994 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:40:53,996 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:40:53,996 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:40:53,999 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:40:54,396 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:40:54,396 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:40:54,396 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:40:54,397 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:40:54,397 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:40:54,397 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:40:54,397 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:40:54,397 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:40:54,397 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:40:54,398 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:40:54,528 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:40:54,532 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:40:54,970 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:40:55,641 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:40:55,643 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:40:56,782 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:40:56,798 - app.utils.memory_management - INFO - Memory before cleanup: 425.06 MB
2025-06-09 16:40:56,979 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:40:56,981 - app.utils.memory_management - INFO - Memory after cleanup: 425.41 MB (freed -0.36 MB)
2025-06-09 16:40:59,725 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:40:59,775 - app.utils.memory_management - INFO - Memory before cleanup: 428.36 MB
2025-06-09 16:40:59,976 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:40:59,977 - app.utils.memory_management - INFO - Memory after cleanup: 428.36 MB (freed 0.00 MB)
2025-06-09 16:41:05,594 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:41:06,312 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: 'PredictiveAnalytics' object has no attribute 'generate_enhanced_predictions'
2025-06-09 16:41:06,325 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:41:06,325 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:41:06,328 - app.pages.buy_sell_recommendations - ERROR - Error in comprehensive recommendation generation: 'TradingRecommendationEngine' object has no attribute 'generate_enhanced_recommendations'
2025-06-09 16:41:06,340 - app.utils.memory_management - INFO - Memory before cleanup: 431.39 MB
2025-06-09 16:41:06,522 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-09 16:41:06,524 - app.utils.memory_management - INFO - Memory after cleanup: 431.43 MB (freed -0.04 MB)
2025-06-09 16:43:42,865 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:43:42,895 - app - INFO - Memory management utilities loaded
2025-06-09 16:43:42,909 - app - INFO - Error handling utilities loaded
2025-06-09 16:43:42,914 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:43:42,920 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:43:42,921 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:43:42,922 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:44:02,473 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:44:04,591 - app - INFO - Memory management utilities loaded
2025-06-09 16:44:04,591 - app - INFO - Error handling utilities loaded
2025-06-09 16:44:04,591 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:44:04,591 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:44:04,599 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:44:04,599 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:44:04,601 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:44:04,601 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:44:04,603 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:44:04,603 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:44:04,603 - app - INFO - Applied NumPy fix
2025-06-09 16:44:04,605 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:44:04,605 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:44:04,605 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:44:04,605 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:44:04,605 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:44:04,608 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:44:04,608 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:44:04,608 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:44:09,152 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:44:09,152 - app - INFO - Applied TensorFlow fix
2025-06-09 16:44:09,152 - app.config - INFO - Configuration initialized
2025-06-09 16:44:09,152 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:44:09,174 - models.train - INFO - TensorFlow test successful
2025-06-09 16:44:09,823 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:44:09,823 - models.train - INFO - Transformer model is available
2025-06-09 16:44:09,823 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:44:09,823 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:44:09,823 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:44:09,830 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:44:10,167 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:44:10,167 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:44:10,167 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:44:10,167 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:44:10,167 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:44:10,167 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:44:10,167 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:44:10,167 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:44:10,173 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:44:10,173 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:44:10,381 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:44:10,385 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:44:10,768 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:44:11,398 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:44:11,398 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:44:12,548 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:44:12,561 - app.utils.memory_management - INFO - Memory before cleanup: 425.19 MB
2025-06-09 16:44:12,741 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:44:12,745 - app.utils.memory_management - INFO - Memory after cleanup: 425.55 MB (freed -0.36 MB)
2025-06-09 16:44:18,129 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:44:18,173 - app.utils.memory_management - INFO - Memory before cleanup: 428.84 MB
2025-06-09 16:44:18,359 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:44:18,360 - app.utils.memory_management - INFO - Memory after cleanup: 428.84 MB (freed 0.00 MB)
2025-06-09 16:44:20,189 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:44:20,807 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: 'PredictiveAnalytics' object has no attribute 'generate_enhanced_predictions'
2025-06-09 16:44:20,824 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:44:20,824 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:44:20,996 - app.utils.memory_management - INFO - Memory before cleanup: 433.09 MB
2025-06-09 16:44:21,225 - app.utils.memory_management - INFO - Garbage collection: collected 404 objects
2025-06-09 16:44:21,228 - app.utils.memory_management - INFO - Memory after cleanup: 433.12 MB (freed -0.04 MB)
2025-06-09 16:47:02,594 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:47:02,601 - app - INFO - Memory management utilities loaded
2025-06-09 16:47:02,604 - app - INFO - Error handling utilities loaded
2025-06-09 16:47:02,606 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:47:02,607 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:47:02,607 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:47:02,607 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:47:23,780 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:47:25,997 - app - INFO - Memory management utilities loaded
2025-06-09 16:47:25,999 - app - INFO - Error handling utilities loaded
2025-06-09 16:47:26,001 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:47:26,001 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:47:26,001 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:47:26,001 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:47:26,005 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:47:26,006 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:47:26,009 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:47:26,010 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:47:26,011 - app - INFO - Applied NumPy fix
2025-06-09 16:47:26,015 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:47:26,017 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:47:26,019 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:47:26,019 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:47:26,019 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:47:26,020 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:47:26,020 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:47:26,020 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:47:30,551 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:47:30,551 - app - INFO - Applied TensorFlow fix
2025-06-09 16:47:30,554 - app.config - INFO - Configuration initialized
2025-06-09 16:47:30,560 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:47:30,570 - models.train - INFO - TensorFlow test successful
2025-06-09 16:47:31,128 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:47:31,128 - models.train - INFO - Transformer model is available
2025-06-09 16:47:31,128 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:47:31,130 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:47:31,130 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:47:31,134 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:47:31,479 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:47:31,479 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:47:31,479 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:47:31,479 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:47:31,479 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:47:31,480 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:47:31,480 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:47:31,480 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:47:31,480 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:47:31,480 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:47:31,587 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:47:31,590 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:47:31,973 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:47:32,627 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:47:32,629 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:47:33,790 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:47:33,804 - app.utils.memory_management - INFO - Memory before cleanup: 425.12 MB
2025-06-09 16:47:33,987 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:47:33,988 - app.utils.memory_management - INFO - Memory after cleanup: 425.12 MB (freed -0.00 MB)
2025-06-09 16:47:35,990 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:47:36,032 - app.utils.memory_management - INFO - Memory before cleanup: 429.08 MB
2025-06-09 16:47:36,208 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:47:36,208 - app.utils.memory_management - INFO - Memory after cleanup: 429.08 MB (freed 0.00 MB)
2025-06-09 16:47:37,714 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:47:38,326 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:38,327 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:38,338 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:38,346 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:38,350 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:38,522 - app.utils.memory_management - INFO - Memory before cleanup: 433.48 MB
2025-06-09 16:47:38,715 - app.utils.memory_management - INFO - Garbage collection: collected 404 objects
2025-06-09 16:47:38,719 - app.utils.memory_management - INFO - Memory after cleanup: 433.52 MB (freed -0.04 MB)
2025-06-09 16:47:45,225 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:47:45,798 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:45,798 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:45,820 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:45,828 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:45,828 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:47:45,901 - app.utils.memory_management - INFO - Memory before cleanup: 436.08 MB
2025-06-09 16:47:46,070 - app.utils.memory_management - INFO - Garbage collection: collected 691 objects
2025-06-09 16:47:46,071 - app.utils.memory_management - INFO - Memory after cleanup: 436.08 MB (freed 0.00 MB)
2025-06-09 16:48:29,281 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:48:29,931 - app - INFO - Memory management utilities loaded
2025-06-09 16:48:29,931 - app - INFO - Error handling utilities loaded
2025-06-09 16:48:29,931 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:48:29,938 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:48:29,938 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:48:29,938 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:48:29,938 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:48:29,941 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:48:29,941 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:48:29,941 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:48:29,941 - app - INFO - Applied NumPy fix
2025-06-09 16:48:29,943 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:48:29,943 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:48:29,943 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:48:29,943 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:48:29,944 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:48:29,944 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:48:29,944 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:48:29,944 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:48:34,415 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:48:34,415 - app - INFO - Applied TensorFlow fix
2025-06-09 16:48:35,635 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:48:35,635 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:48:35,642 - app - INFO - Cleaning up resources...
2025-06-09 16:48:35,644 - app.utils.memory_management - INFO - Memory before cleanup: 352.85 MB
2025-06-09 16:48:35,765 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:48:35,767 - app.utils.memory_management - INFO - Memory after cleanup: 353.14 MB (freed -0.29 MB)
2025-06-09 16:48:35,767 - app - INFO - Application shutdown complete
2025-06-09 16:50:21,532 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:50:22,198 - app - INFO - Memory management utilities loaded
2025-06-09 16:50:22,198 - app - INFO - Error handling utilities loaded
2025-06-09 16:50:22,198 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:50:22,198 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:50:22,198 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:50:22,198 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:50:22,198 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:50:22,205 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:50:22,205 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:50:22,205 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:50:22,205 - app - INFO - Applied NumPy fix
2025-06-09 16:50:22,207 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:50:22,207 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:50:22,207 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:50:22,207 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:50:22,208 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:50:22,208 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:50:22,208 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:50:22,208 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:50:26,771 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:50:26,771 - app - INFO - Applied TensorFlow fix
2025-06-09 16:50:27,741 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 16:50:27,916 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:50:27,916 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:50:27,939 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:50:27,941 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 16:50:27,942 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 16:50:27,949 - app - INFO - Cleaning up resources...
2025-06-09 16:50:27,953 - app.utils.memory_management - INFO - Memory before cleanup: 352.91 MB
2025-06-09 16:50:28,070 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:50:28,070 - app.utils.memory_management - INFO - Memory after cleanup: 353.26 MB (freed -0.35 MB)
2025-06-09 16:50:28,070 - app - INFO - Application shutdown complete
2025-06-09 16:51:00,669 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:51:00,675 - app - INFO - Memory management utilities loaded
2025-06-09 16:51:00,677 - app - INFO - Error handling utilities loaded
2025-06-09 16:51:00,679 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:51:00,680 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:51:00,680 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:51:00,681 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:51:22,497 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:51:24,164 - app - INFO - Memory management utilities loaded
2025-06-09 16:51:24,171 - app - INFO - Error handling utilities loaded
2025-06-09 16:51:24,198 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:51:24,203 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:51:24,204 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:51:24,205 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:51:24,207 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:51:24,208 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:51:24,209 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:51:24,210 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:51:24,210 - app - INFO - Applied NumPy fix
2025-06-09 16:51:24,213 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:51:24,213 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:51:24,214 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:51:24,215 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:51:24,216 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:51:24,216 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:51:24,217 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:51:24,217 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:51:28,635 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:51:28,635 - app - INFO - Applied TensorFlow fix
2025-06-09 16:51:28,638 - app.config - INFO - Configuration initialized
2025-06-09 16:51:28,643 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:51:28,652 - models.train - INFO - TensorFlow test successful
2025-06-09 16:51:29,220 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:51:29,220 - models.train - INFO - Transformer model is available
2025-06-09 16:51:29,221 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:51:29,222 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:51:29,222 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:51:29,225 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:51:29,569 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:51:29,570 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:51:29,570 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:51:29,570 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:51:29,570 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:51:29,570 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:51:29,571 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:51:29,571 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:51:29,571 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:51:29,571 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:51:29,680 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:51:29,683 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:51:30,144 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:51:30,886 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:51:30,887 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:51:31,973 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:51:31,987 - app.utils.memory_management - INFO - Memory before cleanup: 425.58 MB
2025-06-09 16:51:32,170 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:51:32,171 - app.utils.memory_management - INFO - Memory after cleanup: 425.59 MB (freed -0.01 MB)
2025-06-09 16:51:34,737 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:51:34,797 - app.utils.memory_management - INFO - Memory before cleanup: 429.53 MB
2025-06-09 16:51:35,015 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:51:35,016 - app.utils.memory_management - INFO - Memory after cleanup: 429.53 MB (freed 0.00 MB)
2025-06-09 16:51:36,728 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:51:37,369 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 16:51:37,384 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:51:37,384 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:51:37,401 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:51:37,412 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:51:37,412 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 16:51:37,589 - app.utils.memory_management - INFO - Memory before cleanup: 433.75 MB
2025-06-09 16:51:37,791 - app.utils.memory_management - INFO - Garbage collection: collected 404 objects
2025-06-09 16:51:37,793 - app.utils.memory_management - INFO - Memory after cleanup: 433.79 MB (freed -0.04 MB)
2025-06-09 16:56:52,027 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:56:52,035 - app - INFO - Memory management utilities loaded
2025-06-09 16:56:52,037 - app - INFO - Error handling utilities loaded
2025-06-09 16:56:52,040 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:56:52,042 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:56:52,043 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:56:52,046 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:57:09,780 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 16:57:11,524 - app - INFO - Memory management utilities loaded
2025-06-09 16:57:11,530 - app - INFO - Error handling utilities loaded
2025-06-09 16:57:11,532 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 16:57:11,534 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 16:57:11,534 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 16:57:11,536 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 16:57:11,536 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 16:57:11,538 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 16:57:11,538 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 16:57:11,538 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 16:57:11,540 - app - INFO - Applied NumPy fix
2025-06-09 16:57:11,542 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:57:11,542 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:57:11,542 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:57:11,542 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 16:57:11,542 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:57:11,544 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:57:11,546 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:57:11,546 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 16:57:16,081 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 16:57:16,082 - app - INFO - Applied TensorFlow fix
2025-06-09 16:57:16,085 - app.config - INFO - Configuration initialized
2025-06-09 16:57:16,090 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 16:57:16,103 - models.train - INFO - TensorFlow test successful
2025-06-09 16:57:16,689 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 16:57:16,690 - models.train - INFO - Transformer model is available
2025-06-09 16:57:16,690 - models.train - INFO - Using TensorFlow-based models
2025-06-09 16:57:16,692 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 16:57:16,692 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 16:57:16,695 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 16:57:17,116 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:57:17,117 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 16:57:17,117 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 16:57:17,117 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 16:57:17,117 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 16:57:17,117 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 16:57:17,117 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 16:57:17,118 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 16:57:17,118 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 16:57:17,118 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 16:57:17,226 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 16:57:17,229 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:57:17,642 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 16:57:18,359 - app.utils.session_state - INFO - Initializing session state
2025-06-09 16:57:18,360 - app.utils.session_state - INFO - Session state initialized
2025-06-09 16:57:19,743 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 16:57:19,761 - app.utils.memory_management - INFO - Memory before cleanup: 425.54 MB
2025-06-09 16:57:19,948 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:57:19,950 - app.utils.memory_management - INFO - Memory after cleanup: 425.55 MB (freed -0.00 MB)
2025-06-09 16:57:22,815 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:57:22,868 - app.utils.memory_management - INFO - Memory before cleanup: 428.86 MB
2025-06-09 16:57:23,053 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 16:57:23,055 - app.utils.memory_management - INFO - Memory after cleanup: 428.86 MB (freed 0.00 MB)
2025-06-09 16:57:25,991 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 16:57:26,643 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 16:57:26,678 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 16:57:26,692 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'PredictiveAnalytics' object has no attribute '_detect_market_regime'
2025-06-09 16:57:26,700 - app.utils.memory_management - INFO - Memory before cleanup: 432.15 MB
2025-06-09 16:57:26,861 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-09 16:57:26,861 - app.utils.memory_management - INFO - Memory after cleanup: 432.19 MB (freed -0.04 MB)
2025-06-09 17:14:01,358 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 17:14:01,366 - app - INFO - Memory management utilities loaded
2025-06-09 17:14:01,368 - app - INFO - Error handling utilities loaded
2025-06-09 17:14:01,372 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 17:14:01,372 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 17:14:01,373 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 17:14:01,373 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 17:18:12,580 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 17:18:14,679 - app - INFO - Memory management utilities loaded
2025-06-09 17:18:14,685 - app - INFO - Error handling utilities loaded
2025-06-09 17:18:14,688 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 17:18:14,693 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 17:18:14,695 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 17:18:14,696 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 17:18:14,701 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 17:18:14,701 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 17:18:14,703 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 17:18:14,705 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 17:18:14,706 - app - INFO - Applied NumPy fix
2025-06-09 17:18:14,708 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 17:18:14,709 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 17:18:14,711 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 17:18:14,712 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 17:18:14,713 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 17:18:14,715 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 17:18:14,715 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 17:18:14,717 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 17:18:20,165 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 17:18:20,166 - app - INFO - Applied TensorFlow fix
2025-06-09 17:18:20,170 - app.config - INFO - Configuration initialized
2025-06-09 17:18:20,176 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 17:18:20,185 - models.train - INFO - TensorFlow test successful
2025-06-09 17:18:20,837 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 17:18:20,837 - models.train - INFO - Transformer model is available
2025-06-09 17:18:20,838 - models.train - INFO - Using TensorFlow-based models
2025-06-09 17:18:20,840 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 17:18:20,840 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 17:18:20,844 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 17:18:21,218 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 17:18:21,218 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 17:18:21,218 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 17:18:21,219 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 17:18:21,220 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 17:18:21,221 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 17:18:21,221 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 17:18:21,221 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 17:18:21,221 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 17:18:21,222 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 17:18:21,352 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 17:18:21,356 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 17:18:21,800 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 17:18:22,610 - app.utils.session_state - INFO - Initializing session state
2025-06-09 17:18:22,611 - app.utils.session_state - INFO - Session state initialized
2025-06-09 17:18:23,815 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 17:18:23,831 - app.utils.memory_management - INFO - Memory before cleanup: 426.38 MB
2025-06-09 17:18:24,026 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 17:18:24,028 - app.utils.memory_management - INFO - Memory after cleanup: 426.40 MB (freed -0.02 MB)
2025-06-09 17:18:28,155 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 17:18:28,194 - app.utils.memory_management - INFO - Memory before cleanup: 429.86 MB
2025-06-09 17:18:28,379 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 17:18:28,380 - app.utils.memory_management - INFO - Memory after cleanup: 429.87 MB (freed -0.00 MB)
2025-06-09 17:18:30,974 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 17:18:31,666 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 17:18:31,687 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 17:18:31,701 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 17:18:31,701 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 17:18:31,709 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 17:18:31,737 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 17:18:31,746 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 17:18:31,746 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 17:18:31,771 - app.utils.session_state - ERROR - Error tracked: app_crash - unsupported format string passed to NoneType.__format__
2025-06-09 17:18:31,773 - app - ERROR - Application crashed: unsupported format string passed to NoneType.__format__
2025-06-09 17:18:31,775 - app.utils.memory_management - INFO - Memory before cleanup: 432.80 MB
2025-06-09 17:18:31,957 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-09 17:18:31,957 - app.utils.memory_management - INFO - Memory after cleanup: 432.84 MB (freed -0.04 MB)
2025-06-09 17:18:31,958 - app.utils.memory_management - INFO - Memory before cleanup: 432.84 MB
2025-06-09 17:18:32,139 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-09 17:18:32,139 - app.utils.memory_management - INFO - Memory after cleanup: 432.84 MB (freed 0.00 MB)
2025-06-09 21:29:19,949 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 21:29:25,736 - app - INFO - Memory management utilities loaded
2025-06-09 21:29:25,738 - app - INFO - Error handling utilities loaded
2025-06-09 21:29:25,766 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 21:29:25,768 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 21:29:25,768 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 21:29:25,769 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 21:29:25,778 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 21:29:25,780 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 21:29:25,780 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 21:29:25,781 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 21:29:25,781 - app - INFO - Applied NumPy fix
2025-06-09 21:29:25,798 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 21:29:25,800 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 21:29:25,800 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 21:29:25,803 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 21:29:25,804 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 21:29:25,804 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 21:29:25,804 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 21:29:25,805 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 21:29:49,798 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 21:29:49,798 - app - INFO - Applied TensorFlow fix
2025-06-09 21:29:49,824 - app.config - INFO - Configuration initialized
2025-06-09 21:29:49,841 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 21:29:49,852 - models.train - INFO - TensorFlow test successful
2025-06-09 21:29:54,771 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 21:29:54,772 - models.train - INFO - Transformer model is available
2025-06-09 21:29:54,772 - models.train - INFO - Using TensorFlow-based models
2025-06-09 21:29:54,779 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 21:29:54,780 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 21:29:54,813 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 21:29:56,520 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 21:29:56,521 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 21:29:56,521 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 21:29:56,521 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 21:29:56,521 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 21:29:56,521 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 21:29:56,521 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 21:29:56,522 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 21:29:56,522 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 21:29:56,522 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 21:29:56,985 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 21:29:57,001 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:29:57,921 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 21:30:01,097 - app.utils.session_state - INFO - Initializing session state
2025-06-09 21:30:01,099 - app.utils.session_state - INFO - Session state initialized
2025-06-09 21:30:02,285 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 21:30:02,304 - app.utils.memory_management - INFO - Memory before cleanup: 426.33 MB
2025-06-09 21:30:02,482 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 21:30:02,482 - app.utils.memory_management - INFO - Memory after cleanup: 426.34 MB (freed -0.00 MB)
2025-06-09 21:30:08,740 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:30:08,806 - app.utils.memory_management - INFO - Memory before cleanup: 429.33 MB
2025-06-09 21:30:09,057 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 21:30:09,059 - app.utils.memory_management - INFO - Memory after cleanup: 429.39 MB (freed -0.06 MB)
2025-06-09 21:30:13,781 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:30:14,466 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 21:30:14,487 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 21:30:14,497 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:30:14,499 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:30:14,508 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:30:14,527 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 21:30:14,537 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:30:14,537 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:30:14,555 - app.utils.session_state - ERROR - Error tracked: app_crash - unsupported format string passed to NoneType.__format__
2025-06-09 21:30:14,558 - app - ERROR - Application crashed: unsupported format string passed to NoneType.__format__
2025-06-09 21:30:14,560 - app.utils.memory_management - INFO - Memory before cleanup: 432.63 MB
2025-06-09 21:30:14,789 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-09 21:30:14,798 - app.utils.memory_management - INFO - Memory after cleanup: 432.67 MB (freed -0.04 MB)
2025-06-09 21:30:14,802 - app.utils.memory_management - INFO - Memory before cleanup: 432.67 MB
2025-06-09 21:30:14,990 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-09 21:30:14,990 - app.utils.memory_management - INFO - Memory after cleanup: 432.62 MB (freed 0.05 MB)
2025-06-09 21:36:00,163 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:36:00,176 - app.utils.session_state - INFO - Initializing session state
2025-06-09 21:36:00,181 - app.utils.session_state - INFO - Session state initialized
2025-06-09 21:36:00,204 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 21:36:00,219 - app.utils.memory_management - INFO - Memory before cleanup: 432.54 MB
2025-06-09 21:36:00,399 - app.utils.memory_management - INFO - Garbage collection: collected 215 objects
2025-06-09 21:36:00,401 - app.utils.memory_management - INFO - Memory after cleanup: 432.54 MB (freed 0.00 MB)
2025-06-09 21:36:04,761 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:36:04,804 - app.utils.memory_management - INFO - Memory before cleanup: 433.43 MB
2025-06-09 21:36:05,010 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 21:36:05,012 - app.utils.memory_management - INFO - Memory after cleanup: 433.43 MB (freed 0.00 MB)
2025-06-09 21:36:07,541 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:36:08,151 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 21:36:08,168 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 21:36:08,180 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:08,181 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:08,195 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:08,210 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 21:36:08,227 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:08,227 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:08,243 - app.utils.session_state - ERROR - Error tracked: app_crash - unsupported format string passed to NoneType.__format__
2025-06-09 21:36:08,244 - app - ERROR - Application crashed: unsupported format string passed to NoneType.__format__
2025-06-09 21:36:08,252 - app.utils.memory_management - INFO - Memory before cleanup: 434.12 MB
2025-06-09 21:36:08,442 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-09 21:36:08,443 - app.utils.memory_management - INFO - Memory after cleanup: 434.12 MB (freed 0.00 MB)
2025-06-09 21:36:08,444 - app.utils.memory_management - INFO - Memory before cleanup: 434.12 MB
2025-06-09 21:36:08,620 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-09 21:36:08,620 - app.utils.memory_management - INFO - Memory after cleanup: 434.12 MB (freed 0.00 MB)
2025-06-09 21:36:26,853 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:36:26,860 - app.utils.session_state - INFO - Initializing session state
2025-06-09 21:36:26,866 - app.utils.session_state - INFO - Session state initialized
2025-06-09 21:36:26,890 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 21:36:26,911 - app.utils.memory_management - INFO - Memory before cleanup: 433.98 MB
2025-06-09 21:36:27,159 - app.utils.memory_management - INFO - Garbage collection: collected 217 objects
2025-06-09 21:36:27,160 - app.utils.memory_management - INFO - Memory after cleanup: 433.98 MB (freed 0.00 MB)
2025-06-09 21:36:30,230 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:36:30,275 - app.utils.memory_management - INFO - Memory before cleanup: 434.16 MB
2025-06-09 21:36:30,475 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 21:36:30,476 - app.utils.memory_management - INFO - Memory after cleanup: 434.16 MB (freed 0.00 MB)
2025-06-09 21:36:32,348 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:36:33,136 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 21:36:33,150 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 21:36:33,163 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:33,165 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:33,172 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:33,185 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 21:36:33,198 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:33,198 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:36:33,217 - app.utils.session_state - ERROR - Error tracked: app_crash - unsupported format string passed to NoneType.__format__
2025-06-09 21:36:33,219 - app - ERROR - Application crashed: unsupported format string passed to NoneType.__format__
2025-06-09 21:36:33,221 - app.utils.memory_management - INFO - Memory before cleanup: 434.23 MB
2025-06-09 21:36:33,400 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-09 21:36:33,402 - app.utils.memory_management - INFO - Memory after cleanup: 434.23 MB (freed 0.00 MB)
2025-06-09 21:36:33,402 - app.utils.memory_management - INFO - Memory before cleanup: 434.23 MB
2025-06-09 21:36:33,596 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-09 21:36:33,596 - app.utils.memory_management - INFO - Memory after cleanup: 434.23 MB (freed 0.00 MB)
2025-06-09 21:40:27,705 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 21:40:27,711 - app - INFO - Memory management utilities loaded
2025-06-09 21:40:27,713 - app - INFO - Error handling utilities loaded
2025-06-09 21:40:27,714 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 21:40:27,716 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 21:40:27,717 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 21:40:27,718 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 21:40:55,060 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 21:41:00,366 - app - INFO - Memory management utilities loaded
2025-06-09 21:41:00,368 - app - INFO - Error handling utilities loaded
2025-06-09 21:41:00,370 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 21:41:00,371 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 21:41:00,371 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 21:41:00,372 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 21:41:00,372 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 21:41:00,373 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 21:41:00,373 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 21:41:00,373 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 21:41:00,374 - app - INFO - Applied NumPy fix
2025-06-09 21:41:00,386 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 21:41:00,386 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 21:41:00,387 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 21:41:00,387 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 21:41:00,387 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 21:41:00,387 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 21:41:00,387 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 21:41:00,388 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 21:41:29,255 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 21:41:29,256 - app - INFO - Applied TensorFlow fix
2025-06-09 21:41:29,280 - app.config - INFO - Configuration initialized
2025-06-09 21:41:29,304 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 21:41:29,314 - models.train - INFO - TensorFlow test successful
2025-06-09 21:41:33,770 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 21:41:33,771 - models.train - INFO - Transformer model is available
2025-06-09 21:41:33,771 - models.train - INFO - Using TensorFlow-based models
2025-06-09 21:41:33,776 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 21:41:33,776 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 21:41:33,801 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 21:41:35,701 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 21:41:35,701 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 21:41:35,701 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 21:41:35,702 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 21:41:35,702 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 21:41:35,703 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 21:41:35,703 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 21:41:35,703 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 21:41:35,704 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 21:41:35,704 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 21:41:36,058 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 21:41:36,073 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:41:36,073 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:41:36,952 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 21:41:39,690 - app.utils.session_state - INFO - Initializing session state
2025-06-09 21:41:39,693 - app.utils.session_state - INFO - Session state initialized
2025-06-09 21:41:39,697 - app.utils.memory_management - INFO - Memory before cleanup: 424.25 MB
2025-06-09 21:41:39,865 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 21:41:39,866 - app.utils.memory_management - INFO - Memory after cleanup: 424.27 MB (freed -0.02 MB)
2025-06-09 21:41:41,334 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 21:41:41,347 - app.utils.memory_management - INFO - Memory before cleanup: 427.67 MB
2025-06-09 21:41:41,576 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 21:41:41,577 - app.utils.memory_management - INFO - Memory after cleanup: 427.67 MB (freed 0.00 MB)
2025-06-09 21:41:44,728 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:41:44,845 - app.utils.memory_management - INFO - Memory before cleanup: 430.53 MB
2025-06-09 21:41:45,101 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 21:41:45,105 - app.utils.memory_management - INFO - Memory after cleanup: 430.57 MB (freed -0.04 MB)
2025-06-09 21:41:46,522 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 21:41:47,364 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 21:41:47,388 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 21:41:47,400 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:41:47,400 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:41:47,414 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:41:47,426 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 21:41:47,465 - app.utils.session_state - ERROR - Error tracked: app_crash - unsupported format string passed to NoneType.__format__
2025-06-09 21:41:47,466 - app - ERROR - Application crashed: unsupported format string passed to NoneType.__format__
2025-06-09 21:41:47,467 - app.utils.memory_management - INFO - Memory before cleanup: 433.72 MB
2025-06-09 21:41:47,688 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-09 21:41:47,689 - app.utils.memory_management - INFO - Memory after cleanup: 433.72 MB (freed -0.00 MB)
2025-06-09 21:41:47,690 - app.utils.memory_management - INFO - Memory before cleanup: 433.72 MB
2025-06-09 21:41:47,850 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-09 21:41:47,851 - app.utils.memory_management - INFO - Memory after cleanup: 433.72 MB (freed 0.00 MB)
2025-06-09 21:56:31,346 - app - INFO - Cleaning up resources...
2025-06-09 21:56:31,347 - app.utils.memory_management - INFO - Memory before cleanup: 433.50 MB
2025-06-09 21:56:31,510 - app.utils.memory_management - INFO - Garbage collection: collected 446 objects
2025-06-09 21:56:31,511 - app.utils.memory_management - INFO - Memory after cleanup: 433.50 MB (freed 0.00 MB)
2025-06-09 21:56:31,511 - app - INFO - Application shutdown complete
2025-06-09 22:43:53,498 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 22:43:55,150 - app - INFO - Memory management utilities loaded
2025-06-09 22:43:55,153 - app - INFO - Error handling utilities loaded
2025-06-09 22:43:55,155 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 22:43:55,157 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 22:43:55,157 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 22:43:55,157 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 22:43:55,157 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 22:43:55,159 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 22:43:55,163 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 22:43:55,163 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 22:43:55,165 - app - INFO - Applied NumPy fix
2025-06-09 22:43:55,169 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 22:43:55,173 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 22:43:55,175 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 22:43:55,177 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 22:43:55,179 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 22:43:55,182 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 22:43:55,184 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 22:43:55,184 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 22:44:05,810 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 22:44:05,810 - app - INFO - Applied TensorFlow fix
2025-06-09 22:44:05,814 - app.config - INFO - Configuration initialized
2025-06-09 22:44:05,816 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 22:44:05,826 - models.train - INFO - TensorFlow test successful
2025-06-09 22:44:06,580 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 22:44:06,580 - models.train - INFO - Transformer model is available
2025-06-09 22:44:06,580 - models.train - INFO - Using TensorFlow-based models
2025-06-09 22:44:06,580 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 22:44:06,580 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 22:44:06,580 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 22:44:06,961 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 22:44:06,961 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 22:44:06,961 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 22:44:06,961 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 22:44:06,961 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 22:44:06,961 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 22:44:06,961 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 22:44:06,961 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 22:44:06,961 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 22:44:06,961 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 22:44:07,057 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 22:44:07,059 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:44:07,404 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 22:44:08,033 - app.utils.session_state - INFO - Initializing session state
2025-06-09 22:44:08,033 - app.utils.session_state - INFO - Session state initialized
2025-06-09 22:44:09,390 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 22:44:09,403 - app.utils.memory_management - INFO - Memory before cleanup: 425.31 MB
2025-06-09 22:44:09,575 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 22:44:09,575 - app.utils.memory_management - INFO - Memory after cleanup: 425.31 MB (freed -0.00 MB)
2025-06-09 22:44:23,396 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:44:23,437 - app.utils.memory_management - INFO - Memory before cleanup: 429.40 MB
2025-06-09 22:44:23,616 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 22:44:23,617 - app.utils.memory_management - INFO - Memory after cleanup: 429.40 MB (freed 0.00 MB)
2025-06-09 22:44:25,445 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:44:26,045 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 22:44:26,069 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 22:44:26,080 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:44:26,085 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:44:26,097 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:44:26,111 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:44:26,138 - app.utils.session_state - ERROR - Error tracked: app_crash - unsupported format string passed to NoneType.__format__
2025-06-09 22:44:26,139 - app - ERROR - Application crashed: unsupported format string passed to NoneType.__format__
2025-06-09 22:44:26,140 - app.utils.memory_management - INFO - Memory before cleanup: 432.41 MB
2025-06-09 22:44:26,357 - app.utils.memory_management - INFO - Garbage collection: collected 203 objects
2025-06-09 22:44:26,358 - app.utils.memory_management - INFO - Memory after cleanup: 432.45 MB (freed -0.04 MB)
2025-06-09 22:44:26,359 - app.utils.memory_management - INFO - Memory before cleanup: 432.45 MB
2025-06-09 22:44:26,526 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-09 22:44:26,526 - app.utils.memory_management - INFO - Memory after cleanup: 432.45 MB (freed 0.00 MB)
2025-06-09 22:51:49,775 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 22:51:51,281 - app - INFO - Memory management utilities loaded
2025-06-09 22:51:51,282 - app - INFO - Error handling utilities loaded
2025-06-09 22:51:51,284 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 22:51:51,284 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 22:51:51,284 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 22:51:51,286 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 22:51:51,286 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 22:51:51,286 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 22:51:51,286 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 22:51:51,288 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 22:51:51,289 - app - INFO - Applied NumPy fix
2025-06-09 22:51:51,290 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 22:51:51,290 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 22:51:51,291 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 22:51:51,291 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 22:51:51,291 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 22:51:51,291 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 22:51:51,292 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 22:51:51,292 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 22:51:55,332 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 22:51:55,334 - app - INFO - Applied TensorFlow fix
2025-06-09 22:51:55,336 - app.config - INFO - Configuration initialized
2025-06-09 22:51:55,340 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 22:51:55,353 - models.train - INFO - TensorFlow test successful
2025-06-09 22:51:55,828 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 22:51:55,828 - models.train - INFO - Transformer model is available
2025-06-09 22:51:55,828 - models.train - INFO - Using TensorFlow-based models
2025-06-09 22:51:55,830 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 22:51:55,830 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 22:51:55,832 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 22:51:56,155 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 22:51:56,155 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 22:51:56,157 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 22:51:56,157 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 22:51:56,157 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 22:51:56,157 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 22:51:56,157 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 22:51:56,157 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 22:51:56,157 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 22:51:56,157 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 22:51:56,241 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 22:51:56,243 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:51:56,573 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 22:51:57,110 - app.utils.session_state - INFO - Initializing session state
2025-06-09 22:51:57,112 - app.utils.session_state - INFO - Session state initialized
2025-06-09 22:51:58,481 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 22:51:58,516 - app.utils.memory_management - INFO - Memory before cleanup: 425.86 MB
2025-06-09 22:51:58,693 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 22:51:58,693 - app.utils.memory_management - INFO - Memory after cleanup: 425.93 MB (freed -0.07 MB)
2025-06-09 22:52:05,408 - app - INFO - Cleaning up resources...
2025-06-09 22:52:05,408 - app.utils.memory_management - INFO - Memory before cleanup: 432.20 MB
2025-06-09 22:52:05,553 - app.utils.memory_management - INFO - Garbage collection: collected 286 objects
2025-06-09 22:52:05,553 - app.utils.memory_management - INFO - Memory after cleanup: 432.20 MB (freed 0.00 MB)
2025-06-09 22:52:05,553 - app - INFO - Application shutdown complete
2025-06-09 22:52:13,084 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:52:13,156 - app.utils.memory_management - INFO - Memory before cleanup: 429.15 MB
2025-06-09 22:52:13,398 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 22:52:13,399 - app.utils.memory_management - INFO - Memory after cleanup: 429.15 MB (freed 0.00 MB)
2025-06-09 22:52:15,344 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:52:15,977 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 22:52:15,995 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 22:52:16,012 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:52:16,012 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:52:16,025 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:52:16,034 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:52:16,306 - app.utils.memory_management - INFO - Memory before cleanup: 433.61 MB
2025-06-09 22:52:16,484 - app.utils.memory_management - INFO - Garbage collection: collected 404 objects
2025-06-09 22:52:16,490 - app.utils.memory_management - INFO - Memory after cleanup: 433.65 MB (freed -0.04 MB)
2025-06-09 22:52:22,770 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:52:22,775 - app.utils.session_state - INFO - Initializing session state
2025-06-09 22:52:22,791 - app.utils.session_state - INFO - Session state initialized
2025-06-09 22:52:22,811 - app.utils.memory_management - INFO - Memory before cleanup: 434.95 MB
2025-06-09 22:52:23,000 - app.utils.memory_management - INFO - Garbage collection: collected 241 objects
2025-06-09 22:52:23,001 - app.utils.memory_management - INFO - Memory after cleanup: 434.95 MB (freed 0.00 MB)
2025-06-09 22:52:27,667 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:52:27,714 - app.utils.memory_management - INFO - Memory before cleanup: 434.98 MB
2025-06-09 22:52:27,999 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-09 22:52:27,999 - app.utils.memory_management - INFO - Memory after cleanup: 434.98 MB (freed 0.00 MB)
2025-06-09 22:52:30,614 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 22:52:31,293 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 22:52:31,311 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 22:52:31,320 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:52:31,320 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:52:31,323 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:52:31,339 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 22:52:31,394 - app.utils.memory_management - INFO - Memory before cleanup: 436.04 MB
2025-06-09 22:52:31,562 - app.utils.memory_management - INFO - Garbage collection: collected 703 objects
2025-06-09 22:52:31,564 - app.utils.memory_management - INFO - Memory after cleanup: 436.04 MB (freed 0.00 MB)
2025-06-09 23:06:45,862 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:06:45,866 - app - INFO - Memory management utilities loaded
2025-06-09 23:06:45,868 - app - INFO - Error handling utilities loaded
2025-06-09 23:06:45,869 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:06:45,869 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:06:45,870 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:06:45,870 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:07:31,799 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:07:33,290 - app - INFO - Memory management utilities loaded
2025-06-09 23:07:33,294 - app - INFO - Error handling utilities loaded
2025-06-09 23:07:33,297 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:07:33,299 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:07:33,301 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:07:33,303 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:07:33,309 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:07:33,311 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:07:33,311 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:07:33,313 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:07:33,321 - app - INFO - Applied NumPy fix
2025-06-09 23:07:33,327 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:07:33,328 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:07:33,328 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:07:33,329 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:07:33,329 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:07:33,329 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:07:33,330 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:07:33,330 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:07:37,151 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:07:37,152 - app - INFO - Applied TensorFlow fix
2025-06-09 23:07:37,154 - app.config - INFO - Configuration initialized
2025-06-09 23:07:37,159 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:07:37,170 - models.train - INFO - TensorFlow test successful
2025-06-09 23:07:37,647 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:07:37,648 - models.train - INFO - Transformer model is available
2025-06-09 23:07:37,648 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:07:37,649 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:07:37,650 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:07:37,652 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:07:37,950 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:07:37,951 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:07:37,951 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:07:37,951 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:07:37,951 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:07:37,951 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:07:37,952 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:07:37,952 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:07:37,952 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:07:37,952 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:07:38,045 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:07:38,048 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:07:38,357 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:07:38,847 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:07:38,848 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:07:40,050 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:07:40,070 - app.utils.memory_management - INFO - Memory before cleanup: 424.68 MB
2025-06-09 23:07:40,339 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:07:40,340 - app.utils.memory_management - INFO - Memory after cleanup: 425.03 MB (freed -0.36 MB)
2025-06-09 23:07:43,335 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:07:43,390 - app.utils.memory_management - INFO - Memory before cleanup: 428.21 MB
2025-06-09 23:07:43,581 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:07:43,582 - app.utils.memory_management - INFO - Memory after cleanup: 428.21 MB (freed 0.00 MB)
2025-06-09 23:07:50,301 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:07:50,947 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:07:50,969 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 23:07:50,994 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:07:50,994 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:07:51,006 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:07:51,032 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:07:51,189 - app.utils.memory_management - INFO - Memory before cleanup: 432.73 MB
2025-06-09 23:07:51,371 - app.utils.memory_management - INFO - Garbage collection: collected 405 objects
2025-06-09 23:07:51,371 - app.utils.memory_management - INFO - Memory after cleanup: 432.77 MB (freed -0.04 MB)
2025-06-09 23:14:18,585 - app - INFO - Cleaning up resources...
2025-06-09 23:14:18,585 - app.utils.memory_management - INFO - Memory before cleanup: 434.22 MB
2025-06-09 23:14:18,742 - app.utils.memory_management - INFO - Garbage collection: collected 314 objects
2025-06-09 23:14:18,742 - app.utils.memory_management - INFO - Memory after cleanup: 434.22 MB (freed 0.00 MB)
2025-06-09 23:14:18,742 - app - INFO - Application shutdown complete
2025-06-09 23:14:26,548 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:14:28,362 - app - INFO - Memory management utilities loaded
2025-06-09 23:14:28,366 - app - INFO - Error handling utilities loaded
2025-06-09 23:14:28,370 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:14:28,373 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:14:28,378 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:14:28,381 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:14:28,384 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:14:28,386 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:14:28,388 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:14:28,389 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:14:28,391 - app - INFO - Applied NumPy fix
2025-06-09 23:14:28,392 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:14:28,398 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:14:28,400 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:14:28,403 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:14:28,405 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:14:28,407 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:14:28,407 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:14:28,408 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:14:32,380 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:14:32,381 - app - INFO - Applied TensorFlow fix
2025-06-09 23:14:32,383 - app.config - INFO - Configuration initialized
2025-06-09 23:14:32,387 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:14:32,399 - models.train - INFO - TensorFlow test successful
2025-06-09 23:14:32,895 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:14:32,896 - models.train - INFO - Transformer model is available
2025-06-09 23:14:32,896 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:14:32,898 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:14:32,899 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:14:32,901 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:14:33,226 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:14:33,226 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:14:33,226 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:14:33,226 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:14:33,226 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:14:33,227 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:14:33,227 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:14:33,227 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:14:33,227 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:14:33,228 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:14:33,310 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:14:33,316 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:14:33,685 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:14:34,303 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:14:34,309 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:14:35,502 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:14:35,516 - app.utils.memory_management - INFO - Memory before cleanup: 425.31 MB
2025-06-09 23:14:35,704 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:14:35,705 - app.utils.memory_management - INFO - Memory after cleanup: 425.31 MB (freed -0.00 MB)
2025-06-09 23:14:37,741 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:14:37,792 - app.utils.memory_management - INFO - Memory before cleanup: 429.16 MB
2025-06-09 23:14:37,978 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:14:37,980 - app.utils.memory_management - INFO - Memory after cleanup: 429.16 MB (freed 0.00 MB)
2025-06-09 23:14:44,268 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:14:45,005 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:14:45,014 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 23:14:45,031 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:14:45,033 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:14:45,039 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:14:45,070 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:14:45,257 - app.utils.memory_management - INFO - Memory before cleanup: 433.86 MB
2025-06-09 23:14:45,457 - app.utils.memory_management - INFO - Garbage collection: collected 405 objects
2025-06-09 23:14:45,458 - app.utils.memory_management - INFO - Memory after cleanup: 433.89 MB (freed -0.04 MB)
2025-06-09 23:14:55,246 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:14:55,281 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:14:55,287 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:14:55,333 - app.utils.memory_management - INFO - Memory before cleanup: 435.67 MB
2025-06-09 23:14:55,604 - app.utils.memory_management - INFO - Garbage collection: collected 242 objects
2025-06-09 23:14:55,606 - app.utils.memory_management - INFO - Memory after cleanup: 435.67 MB (freed 0.00 MB)
2025-06-09 23:14:59,559 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:14:59,614 - app.utils.memory_management - INFO - Memory before cleanup: 436.01 MB
2025-06-09 23:14:59,815 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-09 23:14:59,816 - app.utils.memory_management - INFO - Memory after cleanup: 436.01 MB (freed 0.00 MB)
2025-06-09 23:16:24,678 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:16:24,682 - app - INFO - Memory management utilities loaded
2025-06-09 23:16:24,683 - app - INFO - Error handling utilities loaded
2025-06-09 23:16:24,684 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:16:24,684 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:16:24,684 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:16:24,684 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:16:56,683 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:16:58,133 - app - INFO - Memory management utilities loaded
2025-06-09 23:16:58,135 - app - INFO - Error handling utilities loaded
2025-06-09 23:16:58,136 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:16:58,144 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:16:58,158 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:16:58,162 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:16:58,164 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:16:58,166 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:16:58,169 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:16:58,169 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:16:58,171 - app - INFO - Applied NumPy fix
2025-06-09 23:16:58,172 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:16:58,174 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:16:58,174 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:16:58,176 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:16:58,176 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:16:58,178 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:16:58,180 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:16:58,180 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:17:02,070 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:17:02,071 - app - INFO - Applied TensorFlow fix
2025-06-09 23:17:02,073 - app.config - INFO - Configuration initialized
2025-06-09 23:17:02,077 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:17:02,087 - models.train - INFO - TensorFlow test successful
2025-06-09 23:17:02,542 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:17:02,543 - models.train - INFO - Transformer model is available
2025-06-09 23:17:02,543 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:17:02,545 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:17:02,545 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:17:02,547 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:17:02,840 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:17:02,843 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:17:02,844 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:17:02,844 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:17:02,844 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:17:02,844 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:17:02,845 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:17:02,845 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:17:02,845 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:17:02,845 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:17:02,937 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:17:02,940 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:17:03,246 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:17:03,746 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:17:03,748 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:17:05,014 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:17:05,029 - app.utils.memory_management - INFO - Memory before cleanup: 425.74 MB
2025-06-09 23:17:05,204 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:17:05,205 - app.utils.memory_management - INFO - Memory after cleanup: 426.08 MB (freed -0.34 MB)
2025-06-09 23:17:08,531 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:17:08,588 - app.utils.memory_management - INFO - Memory before cleanup: 429.50 MB
2025-06-09 23:17:08,759 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:17:08,760 - app.utils.memory_management - INFO - Memory after cleanup: 429.50 MB (freed 0.00 MB)
2025-06-09 23:17:34,394 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:17:35,084 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:17:35,104 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 23:17:35,116 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:17:35,117 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:17:35,136 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:17:35,148 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:17:35,290 - app.utils.memory_management - INFO - Memory before cleanup: 434.28 MB
2025-06-09 23:17:35,471 - app.utils.memory_management - INFO - Garbage collection: collected 413 objects
2025-06-09 23:17:35,472 - app.utils.memory_management - INFO - Memory after cleanup: 434.32 MB (freed -0.04 MB)
2025-06-09 23:20:08,805 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:20:08,810 - app - INFO - Memory management utilities loaded
2025-06-09 23:20:08,811 - app - INFO - Error handling utilities loaded
2025-06-09 23:20:08,813 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:20:08,813 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:20:08,813 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:20:08,814 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:20:31,156 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:20:32,579 - app - INFO - Memory management utilities loaded
2025-06-09 23:20:32,587 - app - INFO - Error handling utilities loaded
2025-06-09 23:20:32,591 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:20:32,595 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:20:32,598 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:20:32,599 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:20:32,601 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:20:32,603 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:20:32,603 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:20:32,603 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:20:32,604 - app - INFO - Applied NumPy fix
2025-06-09 23:20:32,605 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:20:32,608 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:20:32,612 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:20:32,616 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:20:32,617 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:20:32,617 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:20:32,618 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:20:32,619 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:20:36,172 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:20:36,172 - app - INFO - Applied TensorFlow fix
2025-06-09 23:20:36,175 - app.config - INFO - Configuration initialized
2025-06-09 23:20:36,178 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:20:36,189 - models.train - INFO - TensorFlow test successful
2025-06-09 23:20:36,645 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:20:36,646 - models.train - INFO - Transformer model is available
2025-06-09 23:20:36,646 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:20:36,647 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:20:36,648 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:20:36,650 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:20:36,950 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:20:36,954 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:20:36,955 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:20:36,956 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:20:36,956 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:20:36,956 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:20:36,957 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:20:36,957 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:20:36,957 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:20:36,957 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:20:37,040 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:20:37,042 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:20:37,042 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:20:37,348 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:20:37,857 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:20:37,861 - app.utils.memory_management - INFO - Memory before cleanup: 423.03 MB
2025-06-09 23:20:37,862 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:20:38,035 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:20:38,037 - app.utils.memory_management - INFO - Memory after cleanup: 423.07 MB (freed -0.04 MB)
2025-06-09 23:20:39,425 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:20:39,438 - app.utils.memory_management - INFO - Memory before cleanup: 426.75 MB
2025-06-09 23:20:39,633 - app.utils.memory_management - INFO - Garbage collection: collected 69 objects
2025-06-09 23:20:39,635 - app.utils.memory_management - INFO - Memory after cleanup: 426.75 MB (freed -0.00 MB)
2025-06-09 23:20:41,918 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:20:41,975 - app.utils.memory_management - INFO - Memory before cleanup: 429.44 MB
2025-06-09 23:20:42,166 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-09 23:20:42,166 - app.utils.memory_management - INFO - Memory after cleanup: 429.48 MB (freed -0.04 MB)
2025-06-09 23:23:51,116 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:23:51,121 - app - INFO - Memory management utilities loaded
2025-06-09 23:23:51,123 - app - INFO - Error handling utilities loaded
2025-06-09 23:23:51,125 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:23:51,126 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:23:51,126 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:23:51,127 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:24:12,069 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:24:13,481 - app - INFO - Memory management utilities loaded
2025-06-09 23:24:13,486 - app - INFO - Error handling utilities loaded
2025-06-09 23:24:13,488 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:24:13,490 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:24:13,498 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:24:13,501 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:24:13,503 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:24:13,507 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:24:13,514 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:24:13,521 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:24:13,523 - app - INFO - Applied NumPy fix
2025-06-09 23:24:13,529 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:24:13,533 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:24:13,535 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:24:13,538 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:24:13,541 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:24:13,544 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:24:13,546 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:24:13,549 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:24:17,345 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:24:17,345 - app - INFO - Applied TensorFlow fix
2025-06-09 23:24:17,349 - app.config - INFO - Configuration initialized
2025-06-09 23:24:17,353 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:24:17,365 - models.train - INFO - TensorFlow test successful
2025-06-09 23:24:17,832 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:24:17,833 - models.train - INFO - Transformer model is available
2025-06-09 23:24:17,833 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:24:17,834 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:24:17,835 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:24:17,837 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:24:18,322 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:24:18,324 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:24:18,324 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:24:18,326 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:24:18,326 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:24:18,328 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:24:18,332 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:24:18,334 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:24:18,337 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:24:18,337 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:24:18,462 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:24:18,468 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:24:18,468 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:24:18,848 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:24:19,943 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:24:19,947 - app.utils.memory_management - INFO - Memory before cleanup: 424.00 MB
2025-06-09 23:24:19,948 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:24:20,135 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:24:20,139 - app.utils.memory_management - INFO - Memory after cleanup: 424.05 MB (freed -0.05 MB)
2025-06-09 23:24:21,587 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:24:21,596 - app.utils.memory_management - INFO - Memory before cleanup: 427.86 MB
2025-06-09 23:24:21,822 - app.utils.memory_management - INFO - Garbage collection: collected 69 objects
2025-06-09 23:24:21,822 - app.utils.memory_management - INFO - Memory after cleanup: 427.86 MB (freed 0.00 MB)
2025-06-09 23:24:25,872 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:24:25,920 - app.utils.memory_management - INFO - Memory before cleanup: 430.69 MB
2025-06-09 23:24:26,118 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-09 23:24:26,118 - app.utils.memory_management - INFO - Memory after cleanup: 430.73 MB (freed -0.04 MB)
2025-06-09 23:26:30,041 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:26:30,154 - app.utils.memory_management - INFO - Memory before cleanup: 430.63 MB
2025-06-09 23:26:30,364 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:26:30,365 - app.utils.memory_management - INFO - Memory after cleanup: 430.63 MB (freed 0.00 MB)
2025-06-09 23:26:35,758 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:26:35,820 - app.utils.memory_management - INFO - Memory before cleanup: 430.63 MB
2025-06-09 23:26:36,039 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:26:36,039 - app.utils.memory_management - INFO - Memory after cleanup: 430.63 MB (freed 0.00 MB)
2025-06-09 23:26:38,708 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:26:39,287 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:26:41,450 - app.components.predictive_analytics - ERROR - Error in model training: 'rf_price'
2025-06-09 23:26:41,467 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 23:26:41,504 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: 'PredictionResult' object has no attribute 'current_price'
2025-06-09 23:26:41,671 - app.utils.memory_management - INFO - Memory before cleanup: 436.63 MB
2025-06-09 23:26:41,861 - app.utils.memory_management - INFO - Garbage collection: collected 412 objects
2025-06-09 23:26:41,865 - app.utils.memory_management - INFO - Memory after cleanup: 436.63 MB (freed 0.00 MB)
2025-06-09 23:27:22,566 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:27:22,642 - app.utils.memory_management - INFO - Memory before cleanup: 437.90 MB
2025-06-09 23:27:22,830 - app.utils.memory_management - INFO - Garbage collection: collected 841 objects
2025-06-09 23:27:22,831 - app.utils.memory_management - INFO - Memory after cleanup: 437.90 MB (freed 0.00 MB)
2025-06-09 23:27:24,274 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:27:24,814 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:27:24,833 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 23:27:24,843 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:27:24,851 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-09 23:27:24,912 - app.utils.memory_management - INFO - Memory before cleanup: 439.14 MB
2025-06-09 23:27:25,076 - app.utils.memory_management - INFO - Garbage collection: collected 746 objects
2025-06-09 23:27:25,078 - app.utils.memory_management - INFO - Memory after cleanup: 439.14 MB (freed 0.00 MB)
2025-06-09 23:27:41,119 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:27:41,215 - app.utils.memory_management - INFO - Memory before cleanup: 439.14 MB
2025-06-09 23:27:41,440 - app.utils.memory_management - INFO - Garbage collection: collected 751 objects
2025-06-09 23:27:41,440 - app.utils.memory_management - INFO - Memory after cleanup: 439.14 MB (freed 0.00 MB)
2025-06-09 23:27:44,154 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:27:44,727 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:27:46,871 - app.components.predictive_analytics - ERROR - Error in model training: 'rf_price'
2025-06-09 23:27:46,905 - app.components.predictive_analytics - WARNING - Auto-training failed: Found input variables with inconsistent numbers of samples: [1, 200]
2025-06-09 23:27:46,935 - app.components.predictive_analytics - WARNING - Model ensemble prediction failed: 'PredictionResult' object has no attribute 'current_price'
2025-06-09 23:27:46,992 - app.utils.memory_management - INFO - Memory before cleanup: 439.20 MB
2025-06-09 23:27:47,146 - app.utils.memory_management - INFO - Garbage collection: collected 818 objects
2025-06-09 23:27:47,146 - app.utils.memory_management - INFO - Memory after cleanup: 439.20 MB (freed 0.00 MB)
2025-06-09 23:33:37,668 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:33:37,675 - app - INFO - Memory management utilities loaded
2025-06-09 23:33:37,677 - app - INFO - Error handling utilities loaded
2025-06-09 23:33:37,680 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:33:37,682 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:33:37,682 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:33:37,683 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:33:55,708 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:33:57,177 - app - INFO - Memory management utilities loaded
2025-06-09 23:33:57,179 - app - INFO - Error handling utilities loaded
2025-06-09 23:33:57,179 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:33:57,181 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:33:57,181 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:33:57,181 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:33:57,181 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:33:57,181 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:33:57,184 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:33:57,184 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:33:57,184 - app - INFO - Applied NumPy fix
2025-06-09 23:33:57,184 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:33:57,184 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:33:57,186 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:33:57,186 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:33:57,186 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:33:57,186 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:33:57,186 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:33:57,186 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:34:01,384 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:34:01,384 - app - INFO - Applied TensorFlow fix
2025-06-09 23:34:01,386 - app.config - INFO - Configuration initialized
2025-06-09 23:34:01,390 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:34:01,403 - models.train - INFO - TensorFlow test successful
2025-06-09 23:34:01,866 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:34:01,866 - models.train - INFO - Transformer model is available
2025-06-09 23:34:01,866 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:34:01,868 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:34:01,868 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:34:01,870 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:34:02,175 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:34:02,177 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:34:02,177 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:34:02,177 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:34:02,177 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:34:02,177 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:34:02,177 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:34:02,177 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:34:02,177 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:34:02,177 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:34:02,263 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:34:02,265 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:34:02,265 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:34:02,585 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:34:03,105 - app.utils.memory_management - INFO - Memory before cleanup: 423.54 MB
2025-06-09 23:34:03,106 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:34:03,293 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:34:03,295 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:34:03,295 - app.utils.memory_management - INFO - Memory after cleanup: 423.55 MB (freed -0.01 MB)
2025-06-09 23:34:04,670 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:34:04,684 - app.utils.memory_management - INFO - Memory before cleanup: 426.67 MB
2025-06-09 23:34:04,854 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:34:04,855 - app.utils.memory_management - INFO - Memory after cleanup: 426.67 MB (freed 0.00 MB)
2025-06-09 23:34:10,939 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:34:10,989 - app.utils.memory_management - INFO - Memory before cleanup: 429.30 MB
2025-06-09 23:34:11,164 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 23:34:11,165 - app.utils.memory_management - INFO - Memory after cleanup: 429.34 MB (freed -0.04 MB)
2025-06-09 23:34:15,034 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:34:15,083 - app.utils.memory_management - INFO - Memory before cleanup: 429.35 MB
2025-06-09 23:34:15,285 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:34:15,285 - app.utils.memory_management - INFO - Memory after cleanup: 429.35 MB (freed 0.00 MB)
2025-06-09 23:34:17,287 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:34:17,851 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:34:17,868 - app.components.predictive_analytics - WARNING - Auto-training failed: 'Close'
2025-06-09 23:34:17,868 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:34:17,868 - app.components.predictive_analytics - ERROR - Error generating base predictions: No base predictions generated
2025-06-09 23:34:17,868 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: 'PredictiveAnalytics' object has no attribute 'generate_fallback_predictions'
2025-06-09 23:34:17,868 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: 'PredictiveAnalytics' object has no attribute 'generate_fallback_predictions'
2025-06-09 23:34:17,871 - app.components.predictive_analytics - WARNING - Auto-training failed: 'Close'
2025-06-09 23:34:17,871 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:34:17,886 - app.utils.memory_management - INFO - Memory before cleanup: 432.44 MB
2025-06-09 23:34:18,073 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:34:18,073 - app.utils.memory_management - INFO - Memory after cleanup: 432.44 MB (freed 0.00 MB)
2025-06-09 23:38:07,530 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:38:07,535 - app - INFO - Memory management utilities loaded
2025-06-09 23:38:07,537 - app - INFO - Error handling utilities loaded
2025-06-09 23:38:07,538 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:38:07,539 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:38:07,539 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:38:07,540 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:38:26,681 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:38:28,018 - app - INFO - Memory management utilities loaded
2025-06-09 23:38:28,023 - app - INFO - Error handling utilities loaded
2025-06-09 23:38:28,024 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:38:28,027 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:38:28,029 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:38:28,029 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:38:28,032 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:38:28,034 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:38:28,036 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:38:28,038 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:38:28,039 - app - INFO - Applied NumPy fix
2025-06-09 23:38:28,045 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:38:28,048 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:38:28,048 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:38:28,050 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:38:28,050 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:38:28,050 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:38:28,054 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:38:28,056 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:38:32,306 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:38:32,306 - app - INFO - Applied TensorFlow fix
2025-06-09 23:38:32,309 - app.config - INFO - Configuration initialized
2025-06-09 23:38:32,314 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:38:32,327 - models.train - INFO - TensorFlow test successful
2025-06-09 23:38:32,769 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:38:32,770 - models.train - INFO - Transformer model is available
2025-06-09 23:38:32,770 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:38:32,771 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:38:32,772 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:38:32,776 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:38:33,077 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:38:33,077 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:38:33,078 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:38:33,078 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:38:33,078 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:38:33,078 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:38:33,078 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:38:33,078 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:38:33,079 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:38:33,079 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:38:33,158 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:38:33,161 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:38:33,161 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:38:33,483 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:38:33,989 - app.utils.memory_management - INFO - Memory before cleanup: 423.54 MB
2025-06-09 23:38:33,989 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:38:34,171 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:38:34,173 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:38:34,174 - app.utils.memory_management - INFO - Memory after cleanup: 423.55 MB (freed -0.02 MB)
2025-06-09 23:38:35,501 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:38:35,515 - app.utils.memory_management - INFO - Memory before cleanup: 426.75 MB
2025-06-09 23:38:35,711 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:38:35,712 - app.utils.memory_management - INFO - Memory after cleanup: 426.75 MB (freed 0.00 MB)
2025-06-09 23:38:39,375 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:38:39,420 - app.utils.memory_management - INFO - Memory before cleanup: 429.90 MB
2025-06-09 23:38:39,598 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 23:38:39,599 - app.utils.memory_management - INFO - Memory after cleanup: 429.94 MB (freed -0.04 MB)
2025-06-09 23:38:43,325 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:38:43,372 - app.utils.memory_management - INFO - Memory before cleanup: 429.94 MB
2025-06-09 23:38:43,575 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:38:43,577 - app.utils.memory_management - INFO - Memory after cleanup: 429.94 MB (freed 0.00 MB)
2025-06-09 23:38:45,412 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:38:45,998 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:38:46,023 - app.components.predictive_analytics - WARNING - Auto-training failed: 'Close'
2025-06-09 23:38:46,024 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:38:46,024 - app.components.predictive_analytics - ERROR - Error generating base predictions: No base predictions generated
2025-06-09 23:38:46,025 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: 'Close'
2025-06-09 23:38:46,025 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: 'Close'
2025-06-09 23:38:46,026 - app.components.predictive_analytics - WARNING - Auto-training failed: 'Close'
2025-06-09 23:38:46,026 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:38:46,040 - app.utils.memory_management - INFO - Memory before cleanup: 432.75 MB
2025-06-09 23:38:46,234 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:38:46,234 - app.utils.memory_management - INFO - Memory after cleanup: 432.75 MB (freed 0.00 MB)
2025-06-09 23:41:27,316 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:41:27,320 - app - INFO - Memory management utilities loaded
2025-06-09 23:41:27,322 - app - INFO - Error handling utilities loaded
2025-06-09 23:41:27,324 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:41:27,325 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:41:27,326 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:41:27,326 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:41:40,139 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:41:41,480 - app - INFO - Memory management utilities loaded
2025-06-09 23:41:41,490 - app - INFO - Error handling utilities loaded
2025-06-09 23:41:41,500 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:41:41,506 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:41:41,515 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:41:41,521 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:41:41,523 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:41:41,523 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:41:41,525 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:41:41,525 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:41:41,527 - app - INFO - Applied NumPy fix
2025-06-09 23:41:41,529 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:41:41,529 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:41:41,529 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:41:41,531 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:41:41,531 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:41:41,534 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:41:41,539 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:41:41,542 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:41:45,204 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:41:45,204 - app - INFO - Applied TensorFlow fix
2025-06-09 23:41:45,206 - app.config - INFO - Configuration initialized
2025-06-09 23:41:45,211 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:41:45,220 - models.train - INFO - TensorFlow test successful
2025-06-09 23:41:45,660 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:41:45,661 - models.train - INFO - Transformer model is available
2025-06-09 23:41:45,661 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:41:45,661 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:41:45,661 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:41:45,663 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:41:45,955 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:41:45,955 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:41:45,956 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:41:45,956 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:41:45,956 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:41:45,956 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:41:45,956 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:41:45,956 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:41:45,956 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:41:45,957 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:41:46,037 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:41:46,039 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:41:46,356 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:41:46,842 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:41:46,843 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:41:48,049 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:41:48,068 - app.utils.memory_management - INFO - Memory before cleanup: 426.36 MB
2025-06-09 23:41:48,291 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:41:48,293 - app.utils.memory_management - INFO - Memory after cleanup: 426.37 MB (freed -0.00 MB)
2025-06-09 23:41:51,042 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:41:51,089 - app.utils.memory_management - INFO - Memory before cleanup: 429.13 MB
2025-06-09 23:41:51,271 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:41:51,272 - app.utils.memory_management - INFO - Memory after cleanup: 429.13 MB (freed 0.00 MB)
2025-06-09 23:41:55,510 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:41:55,576 - app.utils.memory_management - INFO - Memory before cleanup: 429.39 MB
2025-06-09 23:41:55,812 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:41:55,812 - app.utils.memory_management - INFO - Memory after cleanup: 429.43 MB (freed -0.04 MB)
2025-06-09 23:41:57,365 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:41:57,949 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:41:58,054 - app.components.predictive_analytics - WARNING - Only 0 training samples generated, need at least 20
2025-06-09 23:41:58,054 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:41:58,054 - app.components.predictive_analytics - ERROR - Error generating base predictions: No base predictions generated
2025-06-09 23:41:58,057 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: 'Close'
2025-06-09 23:41:58,057 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: 'Close'
2025-06-09 23:41:58,146 - app.components.predictive_analytics - WARNING - Only 0 training samples generated, need at least 20
2025-06-09 23:41:58,146 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:41:58,153 - app.utils.memory_management - INFO - Memory before cleanup: 432.66 MB
2025-06-09 23:41:58,295 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:41:58,295 - app.utils.memory_management - INFO - Memory after cleanup: 432.66 MB (freed 0.00 MB)
2025-06-09 23:42:18,627 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:42:18,668 - app.utils.memory_management - INFO - Memory before cleanup: 432.65 MB
2025-06-09 23:42:18,911 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-06-09 23:42:18,911 - app.utils.memory_management - INFO - Memory after cleanup: 432.65 MB (freed 0.00 MB)
2025-06-09 23:42:20,502 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:42:21,080 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:42:21,151 - app.components.predictive_analytics - WARNING - Only 0 training samples generated, need at least 20
2025-06-09 23:42:21,153 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:42:21,153 - app.components.predictive_analytics - ERROR - Error generating base predictions: No base predictions generated
2025-06-09 23:42:21,153 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: 'Close'
2025-06-09 23:42:21,153 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: 'Close'
2025-06-09 23:42:21,224 - app.components.predictive_analytics - WARNING - Only 0 training samples generated, need at least 20
2025-06-09 23:42:21,226 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:42:21,232 - app.utils.memory_management - INFO - Memory before cleanup: 433.44 MB
2025-06-09 23:42:21,373 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:42:21,373 - app.utils.memory_management - INFO - Memory after cleanup: 433.44 MB (freed 0.00 MB)
2025-06-09 23:42:40,138 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:42:40,712 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:42:40,786 - app.components.predictive_analytics - WARNING - Only 0 training samples generated, need at least 20
2025-06-09 23:42:40,786 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:42:40,786 - app.components.predictive_analytics - ERROR - Error generating base predictions: No base predictions generated
2025-06-09 23:42:40,786 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: 'Close'
2025-06-09 23:42:40,788 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: 'Close'
2025-06-09 23:42:40,882 - app.components.predictive_analytics - WARNING - Only 0 training samples generated, need at least 20
2025-06-09 23:42:40,882 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:42:40,882 - app.utils.memory_management - INFO - Memory before cleanup: 433.79 MB
2025-06-09 23:42:41,026 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-06-09 23:42:41,026 - app.utils.memory_management - INFO - Memory after cleanup: 433.79 MB (freed 0.00 MB)
2025-06-09 23:44:16,347 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:44:16,401 - app.utils.memory_management - INFO - Memory before cleanup: 433.71 MB
2025-06-09 23:44:16,586 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-06-09 23:44:16,588 - app.utils.memory_management - INFO - Memory after cleanup: 433.71 MB (freed 0.00 MB)
2025-06-09 23:44:18,499 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:44:19,067 - app.components.predictive_analytics - INFO - Models initialized successfully
2025-06-09 23:44:19,171 - app.components.predictive_analytics - WARNING - Only 0 training samples generated, need at least 20
2025-06-09 23:44:19,172 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:44:19,172 - app.components.predictive_analytics - ERROR - Error generating base predictions: No base predictions generated
2025-06-09 23:44:19,173 - app.components.predictive_analytics - ERROR - Error in enhanced predictions: 'Close'
2025-06-09 23:44:19,173 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: 'Close'
2025-06-09 23:44:19,250 - app.components.predictive_analytics - WARNING - Only 0 training samples generated, need at least 20
2025-06-09 23:44:19,250 - app.components.predictive_analytics - ERROR - Error in prediction generation: 'Close'
2025-06-09 23:44:19,250 - app.utils.memory_management - INFO - Memory before cleanup: 434.63 MB
2025-06-09 23:44:19,399 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:44:19,399 - app.utils.memory_management - INFO - Memory after cleanup: 434.63 MB (freed 0.00 MB)
2025-06-09 23:49:45,972 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:49:47,238 - app - INFO - Memory management utilities loaded
2025-06-09 23:49:47,240 - app - INFO - Error handling utilities loaded
2025-06-09 23:49:47,241 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:49:47,242 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:49:47,242 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:49:47,243 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-09 23:49:47,244 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-09 23:49:47,245 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-09 23:49:47,246 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-09 23:49:47,247 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-09 23:49:47,247 - app - INFO - Applied NumPy fix
2025-06-09 23:49:47,249 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:49:47,250 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:49:47,250 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:49:47,250 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-09 23:49:47,250 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:49:47,250 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:49:47,251 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:49:47,252 - app - INFO - Applied NumPy BitGenerator fix
2025-06-09 23:49:51,483 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-09 23:49:51,483 - app - INFO - Applied TensorFlow fix
2025-06-09 23:49:51,485 - app.config - INFO - Configuration initialized
2025-06-09 23:49:51,489 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-09 23:49:51,498 - models.train - INFO - TensorFlow test successful
2025-06-09 23:49:52,055 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-09 23:49:52,055 - models.train - INFO - Transformer model is available
2025-06-09 23:49:52,056 - models.train - INFO - Using TensorFlow-based models
2025-06-09 23:49:52,057 - models.predict - INFO - Transformer model is available for predictions
2025-06-09 23:49:52,057 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-09 23:49:52,060 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-09 23:49:52,395 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:49:52,396 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-09 23:49:52,396 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-09 23:49:52,396 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-09 23:49:52,396 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-09 23:49:52,397 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-09 23:49:52,397 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-09 23:49:52,397 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-09 23:49:52,397 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-09 23:49:52,397 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-09 23:49:52,502 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-09 23:49:52,505 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:49:52,902 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-09 23:49:53,527 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:49:53,529 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:49:54,963 - app - INFO - Found 8 stock files in data/stocks
2025-06-09 23:49:54,979 - app.utils.memory_management - INFO - Memory before cleanup: 426.39 MB
2025-06-09 23:49:55,154 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-09 23:49:55,154 - app.utils.memory_management - INFO - Memory after cleanup: 426.40 MB (freed -0.02 MB)
2025-06-09 23:49:57,889 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:49:57,939 - app.utils.memory_management - INFO - Memory before cleanup: 429.62 MB
2025-06-09 23:49:58,127 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:49:58,128 - app.utils.memory_management - INFO - Memory after cleanup: 429.62 MB (freed 0.00 MB)
2025-06-09 23:50:02,864 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:50:02,917 - app.utils.memory_management - INFO - Memory before cleanup: 429.79 MB
2025-06-09 23:50:03,095 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-06-09 23:50:03,095 - app.utils.memory_management - INFO - Memory after cleanup: 429.82 MB (freed -0.04 MB)
2025-06-09 23:50:04,905 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:50:05,496 - models.train - INFO - Starting training from CSV: data/stocks/COMI.csv
2025-06-09 23:50:05,496 - models.train - INFO - Symbol: COMI, Model type: ensemble
2025-06-09 23:50:05,496 - models.train - INFO - Horizons: [1440, 10080] (in minutes)
2025-06-09 23:50:05,501 - models.train - INFO - Loading data from data/stocks/COMI.csv...
2025-06-09 23:50:05,513 - models.train - INFO - Data loaded. Shape: (585, 36)
2025-06-09 23:50:05,513 - models.train - INFO - Converting Date column to datetime...
2025-06-09 23:50:05,526 - models.train - INFO - Starting model training...
2025-06-09 23:50:05,527 - models.train - INFO - Training models for symbol: COMI
2025-06-09 23:50:05,527 - models.train - INFO - Model type: ensemble
2025-06-09 23:50:05,528 - models.train - INFO - Horizons: [1440, 10080]
2025-06-09 23:50:05,528 - models.train - INFO - Sequence length: 60
2025-06-09 23:50:05,528 - models.train - INFO - Epochs: 50
2025-06-09 23:50:05,528 - models.train - INFO - Batch size: 32
2025-06-09 23:50:05,529 - models.train - INFO - Data shape: (585, 36)
2025-06-09 23:50:05,529 - models.train - INFO - Preparing features...
2025-06-09 23:50:05,560 - models.train - INFO - Features prepared. Shape: (585, 36)
2025-06-09 23:50:05,560 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-06-09 23:50:05,561 - models.train - INFO - Training model for 1440 minutes horizon
2025-06-09 23:50:05,561 - models.train - INFO - Data shape before processing: (585, 36)
2025-06-09 23:50:05,568 - models.train - INFO - Detected data frequency: daily
2025-06-09 23:50:05,568 - models.train - INFO - Minimum required rows: 70
2025-06-09 23:50:05,568 - models.train - INFO - Creating target variable for horizon 1440 minutes...
2025-06-09 23:50:05,570 - models.train - INFO - Converting 1440 minutes to approximately 1 days for target shifting
2025-06-09 23:50:05,570 - models.train - INFO - Created target variable. Valid targets: 584/585
2025-06-09 23:50:05,572 - models.train - INFO - Dropping rows with NaN targets...
2025-06-09 23:50:05,582 - models.train - INFO - Data shape after dropping NaN rows: (584, 37)
2025-06-09 23:50:05,582 - models.train - INFO - Preprocessing data...
2025-06-09 23:50:05,856 - app.utils.data_processing - INFO - Created sequences: X shape=(524, 60, 7), memory=0.84 MB; y shape=(524,), memory=0.00 MB
2025-06-09 23:50:05,994 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.13 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.14s
2025-06-09 23:50:05,995 - models.train - INFO - Saving scaler for horizon 1440...
2025-06-09 23:50:06,130 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-09 23:50:06,130 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_ensemble_scaler_1440min.joblib (0.52 KB)
2025-06-09 23:50:06,130 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_ensemble_scaler1440min.joblib (0.52 KB)
2025-06-09 23:50:06,436 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.03 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.16s
2025-06-09 23:50:06,436 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_1440_scaler.pkl', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler1440min.joblib']
2025-06-09 23:50:06,438 - models.train - INFO - Splitting data into training and validation sets...
2025-06-09 23:50:06,438 - models.train - INFO - Training data shape: (419, 60, 7)
2025-06-09 23:50:06,438 - models.train - INFO - Initializing model for horizon 1440...
2025-06-09 23:50:06,438 - models.train - INFO - Using robust ensemble model for 1440 minutes horizon
2025-06-09 23:50:06,697 - models.hybrid_model - INFO - XGBoost is available
2025-06-09 23:50:06,697 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-06-09 23:50:06,697 - models.train - INFO - Added rf as base model for ensemble
2025-06-09 23:50:06,697 - models.train - INFO - Added gb as base model for ensemble
2025-06-09 23:50:06,697 - models.train - INFO - Added lr as base model for ensemble
2025-06-09 23:50:06,697 - models.train - INFO - Created robust ensemble with 3 base models
2025-06-09 23:50:06,697 - models.train - INFO - Training model for horizon 1440...
2025-06-09 23:50:06,697 - models.train - INFO - Training machine learning model (ensemble) for horizon 1440. This may be quick as ML models don't use epochs like neural networks.
2025-06-09 23:50:06,697 - models.robust_ensemble - INFO - Fitting base model 1/3
2025-06-09 23:50:14,529 - models.robust_ensemble - INFO - Fitting base model 2/3
2025-06-09 23:50:15,677 - app - INFO - Using TensorFlow-based LSTM model
2025-06-09 23:50:15,684 - app.utils.session_state - INFO - Initializing session state
2025-06-09 23:50:15,686 - app.utils.session_state - INFO - Session state initialized
2025-06-09 23:50:15,708 - app.utils.memory_management - INFO - Memory before cleanup: 440.35 MB
2025-06-09 23:50:15,950 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-09 23:50:15,952 - app.utils.memory_management - INFO - Memory after cleanup: 440.35 MB (freed 0.00 MB)
2025-06-09 23:50:19,810 - models.robust_ensemble - INFO - Fitting base model 3/3
2025-06-09 23:50:20,045 - models.train - INFO - Machine learning model (ensemble) training completed for horizon 1440
2025-06-09 23:50:20,045 - models.train - INFO - Saving model for horizon 1440...
2025-06-09 23:50:20,099 - models.robust_ensemble - INFO - Saved ensemble model to saved_models\COMI_ensemble_1440min.joblib
2025-06-09 23:50:20,101 - models.train - INFO - Model saved for horizon 1440
2025-06-09 23:50:20,101 - models.train - INFO - Model for 1440 days horizon trained and saved successfully
2025-06-09 23:50:20,101 - models.train - INFO - Training model for 10080 minutes horizon
2025-06-09 23:50:20,101 - models.train - INFO - Data shape before processing: (585, 37)
2025-06-09 23:50:20,101 - models.train - INFO - Detected data frequency: daily
2025-06-09 23:50:20,104 - models.train - INFO - Minimum required rows: 70
2025-06-09 23:50:20,104 - models.train - INFO - Creating target variable for horizon 10080 minutes...
2025-06-09 23:50:20,104 - models.train - INFO - Converting 10080 minutes to approximately 7 days for target shifting
2025-06-09 23:50:20,104 - models.train - INFO - Created target variable. Valid targets: 578/585
2025-06-09 23:50:20,104 - models.train - INFO - Dropping rows with NaN targets...
2025-06-09 23:50:20,108 - models.train - INFO - Data shape after dropping NaN rows: (578, 38)
2025-06-09 23:50:20,110 - models.train - INFO - Preprocessing data...
2025-06-09 23:50:20,377 - app.utils.data_processing - INFO - Created sequences: X shape=(518, 60, 7), memory=0.83 MB; y shape=(518,), memory=0.00 MB
2025-06-09 23:50:20,514 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.13s
2025-06-09 23:50:20,514 - models.train - INFO - Saving scaler for horizon 10080...
2025-06-09 23:50:20,643 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_10080_scaler.pkl (0.52 KB)
2025-06-09 23:50:20,643 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_ensemble_scaler_10080min.joblib (0.52 KB)
2025-06-09 23:50:20,643 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_ensemble_scaler10080min.joblib (0.52 KB)
2025-06-09 23:50:20,910 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.04 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.14s
2025-06-09 23:50:20,910 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_10080_scaler.pkl', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler10080min.joblib']
2025-06-09 23:50:20,910 - models.train - INFO - Splitting data into training and validation sets...
2025-06-09 23:50:20,910 - models.train - INFO - Training data shape: (414, 60, 7)
2025-06-09 23:50:20,910 - models.train - INFO - Initializing model for horizon 10080...
2025-06-09 23:50:20,910 - models.train - INFO - Using robust ensemble model for 10080 minutes horizon
2025-06-09 23:50:20,910 - models.train - INFO - Added rf as base model for ensemble
2025-06-09 23:50:20,910 - models.train - INFO - Added gb as base model for ensemble
2025-06-09 23:50:20,910 - models.train - INFO - Added lr as base model for ensemble
2025-06-09 23:50:20,910 - models.train - INFO - Created robust ensemble with 3 base models
2025-06-09 23:50:20,910 - models.train - INFO - Training model for horizon 10080...
2025-06-09 23:50:20,910 - models.train - INFO - Training machine learning model (ensemble) for horizon 10080. This may be quick as ML models don't use epochs like neural networks.
2025-06-09 23:50:20,910 - models.robust_ensemble - INFO - Fitting base model 1/3
2025-06-09 23:50:28,822 - models.robust_ensemble - INFO - Fitting base model 2/3
2025-06-09 23:50:32,816 - models.robust_ensemble - INFO - Fitting base model 3/3
2025-06-09 23:50:32,904 - models.train - INFO - Machine learning model (ensemble) training completed for horizon 10080
2025-06-09 23:50:32,904 - models.train - INFO - Saving model for horizon 10080...
2025-06-09 23:50:32,952 - models.robust_ensemble - INFO - Saved ensemble model to saved_models\COMI_ensemble_10080min.joblib
2025-06-09 23:50:32,952 - models.train - INFO - Model saved for horizon 10080
2025-06-09 23:50:32,952 - models.train - INFO - Model for 10080 days horizon trained and saved successfully
2025-06-09 23:50:32,952 - models.train - INFO - Successfully trained 2 models
2025-06-09 23:50:32,958 - models.ensemble_predict - ERROR - Error making ensemble predictions: 'Close'
2025-06-09 23:50:32,958 - app.pages.buy_sell_recommendations - ERROR - Error generating predictions: local variable 'current_price' referenced before assignment
2025-06-09 23:50:33,182 - app.utils.memory_management - INFO - Memory before cleanup: 448.05 MB
2025-06-09 23:50:33,483 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-09 23:50:33,485 - app.utils.memory_management - INFO - Memory after cleanup: 448.05 MB (freed 0.00 MB)
2025-06-09 23:51:56,886 - app - INFO - Cleaning up resources...
2025-06-09 23:51:56,887 - app.utils.memory_management - INFO - Memory before cleanup: 430.71 MB
2025-06-09 23:51:57,036 - app.utils.memory_management - INFO - Garbage collection: collected 286 objects
2025-06-09 23:51:57,036 - app.utils.memory_management - INFO - Memory after cleanup: 430.71 MB (freed -0.00 MB)
2025-06-09 23:51:57,036 - app - INFO - Application shutdown complete
2025-06-09 23:59:07,238 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-09 23:59:07,243 - app - INFO - Memory management utilities loaded
2025-06-09 23:59:07,245 - app - INFO - Error handling utilities loaded
2025-06-09 23:59:07,247 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-09 23:59:07,248 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-09 23:59:07,249 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-09 23:59:07,250 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
