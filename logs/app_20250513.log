2025-05-13 00:04:33,070 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 00:04:34,801 - app - INFO - Memory management utilities loaded
2025-05-13 00:04:34,803 - app - INFO - Error handling utilities loaded
2025-05-13 00:04:34,804 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 00:04:34,805 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 00:04:34,806 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 00:04:34,806 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 00:04:34,806 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 00:04:34,807 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 00:04:34,807 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 00:04:34,807 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 00:04:34,808 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 00:04:34,808 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 00:04:34,809 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 00:04:34,809 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 00:04:34,809 - app - INFO - Applied NumPy fix
2025-05-13 00:04:34,811 - app.config - INFO - Configuration initialized
2025-05-13 00:04:38,657 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 00:04:38,687 - models.train - INFO - TensorFlow test successful
2025-05-13 00:04:39,381 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 00:04:39,381 - models.train - INFO - Transformer model is available
2025-05-13 00:04:39,382 - models.train - INFO - Using TensorFlow-based models
2025-05-13 00:04:39,383 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 00:04:39,383 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 00:04:39,383 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 00:04:39,383 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 00:04:39,881 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 00:04:40,164 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 00:04:40,186 - app.utils.session_state - INFO - Initializing session state
2025-05-13 00:04:40,187 - app.utils.session_state - INFO - Initializing session state
2025-05-13 00:04:40,188 - app.utils.session_state - INFO - Session state initialized
2025-05-13 00:04:40,189 - app.utils.session_state - INFO - Session state initialized
2025-05-13 00:04:40,229 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 00:04:40,262 - app.utils.memory_management - INFO - Memory before cleanup: 416.98 MB
2025-05-13 00:04:40,456 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 00:04:40,456 - app.utils.memory_management - INFO - Memory after cleanup: 417.47 MB (freed -0.49 MB)
2025-05-13 00:04:42,600 - app.utils.memory_management - INFO - Memory before cleanup: 422.61 MB
2025-05-13 00:04:42,763 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 00:04:42,764 - app.utils.memory_management - INFO - Memory after cleanup: 422.61 MB (freed 0.00 MB)
2025-05-13 00:04:57,443 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 00:04:57,460 - app.utils.memory_management - INFO - Memory before cleanup: 423.68 MB
2025-05-13 00:04:57,584 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-13 00:04:57,585 - app.utils.memory_management - INFO - Memory after cleanup: 423.72 MB (freed -0.04 MB)
2025-05-13 00:04:58,707 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 00:04:58,739 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-13 00:04:58,740 - app - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-13 00:04:58,740 - app - INFO - Data shape: (567, 36)
2025-05-13 00:04:58,740 - app - INFO - File COMI contains 2025 data
2025-05-13 00:04:58,761 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-13 00:04:58,761 - app - INFO - Features shape: (567, 36)
2025-05-13 00:04:58,773 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 00:04:58,773 - app - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-13 00:04:58,773 - app - INFO - Data shape: (567, 36)
2025-05-13 00:04:58,773 - app - INFO - File COMI contains 2025 data
2025-05-13 00:04:58,801 - app.utils.memory_management - INFO - Memory before cleanup: 429.00 MB
2025-05-13 00:04:58,917 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-13 00:04:58,917 - app.utils.memory_management - INFO - Memory after cleanup: 429.00 MB (freed 0.00 MB)
2025-05-13 00:05:01,216 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 00:05:01,227 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 00:05:01,243 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 00:05:01,244 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-13 00:05:01,244 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-13 00:05:01,244 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 00:05:01,249 - app.utils.memory_management - INFO - Memory before cleanup: 425.36 MB
2025-05-13 00:05:01,363 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-05-13 00:05:01,363 - app.utils.memory_management - INFO - Memory after cleanup: 425.36 MB (freed 0.00 MB)
2025-05-13 00:09:51,425 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 00:09:52,588 - app - INFO - Memory management utilities loaded
2025-05-13 00:09:52,590 - app - INFO - Error handling utilities loaded
2025-05-13 00:09:52,590 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 00:09:52,590 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 00:09:52,590 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 00:09:52,590 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 00:09:52,590 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 00:09:52,590 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 00:09:52,590 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 00:09:52,590 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 00:09:52,590 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 00:09:52,590 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 00:09:52,590 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 00:09:52,590 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 00:09:52,595 - app - INFO - Applied NumPy fix
2025-05-13 00:09:52,597 - app.config - INFO - Configuration initialized
2025-05-13 00:09:55,835 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 00:09:55,861 - models.train - INFO - TensorFlow test successful
2025-05-13 00:09:56,351 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 00:09:56,358 - models.train - INFO - Transformer model is available
2025-05-13 00:09:56,358 - models.train - INFO - Using TensorFlow-based models
2025-05-13 00:09:56,358 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 00:09:56,358 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 00:09:56,358 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 00:09:56,786 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 00:09:56,950 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 00:09:56,971 - app.utils.session_state - INFO - Initializing session state
2025-05-13 00:09:56,972 - app.utils.session_state - INFO - Session state initialized
2025-05-13 00:09:57,819 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 00:09:57,825 - app.utils.memory_management - INFO - Memory before cleanup: 419.22 MB
2025-05-13 00:09:57,938 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 00:09:57,938 - app.utils.memory_management - INFO - Memory after cleanup: 419.23 MB (freed -0.00 MB)
2025-05-13 00:10:00,142 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 00:10:00,146 - app.utils.session_state - INFO - Initializing session state
2025-05-13 00:10:00,147 - app.utils.session_state - INFO - Session state initialized
2025-05-13 00:10:00,157 - app.utils.memory_management - INFO - Memory before cleanup: 422.36 MB
2025-05-13 00:10:00,269 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 00:10:00,270 - app.utils.memory_management - INFO - Memory after cleanup: 422.36 MB (freed -0.00 MB)
2025-05-13 00:10:05,265 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 00:10:05,276 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 00:10:05,302 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 00:10:05,302 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-13 00:10:05,302 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-13 00:10:05,302 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 00:10:05,317 - app.utils.memory_management - INFO - Memory before cleanup: 427.13 MB
2025-05-13 00:10:05,474 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-13 00:10:05,474 - app.utils.memory_management - INFO - Memory after cleanup: 427.17 MB (freed -0.04 MB)
2025-05-13 00:13:43,293 - app - INFO - Cleaning up resources...
2025-05-13 00:13:43,293 - app.utils.memory_management - INFO - Memory before cleanup: 421.42 MB
2025-05-13 00:13:43,412 - app.utils.memory_management - INFO - Garbage collection: collected 434 objects
2025-05-13 00:13:43,412 - app.utils.memory_management - INFO - Memory after cleanup: 421.42 MB (freed 0.00 MB)
2025-05-13 00:13:43,412 - app - INFO - Application shutdown complete
2025-05-13 13:15:20,508 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 13:15:23,267 - app - INFO - Memory management utilities loaded
2025-05-13 13:15:23,269 - app - INFO - Error handling utilities loaded
2025-05-13 13:15:23,270 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 13:15:23,271 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 13:15:23,271 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 13:15:23,271 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 13:15:23,272 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 13:15:23,272 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 13:15:23,272 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 13:15:23,273 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 13:15:23,274 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 13:15:23,274 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 13:15:23,274 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 13:15:23,275 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 13:15:23,275 - app - INFO - Applied NumPy fix
2025-05-13 13:15:23,277 - app.config - INFO - Configuration initialized
2025-05-13 13:15:33,918 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 13:15:34,185 - models.train - INFO - TensorFlow test successful
2025-05-13 13:15:36,078 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 13:15:36,078 - models.train - INFO - Transformer model is available
2025-05-13 13:15:36,078 - models.train - INFO - Using TensorFlow-based models
2025-05-13 13:15:36,080 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 13:15:36,080 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 13:15:36,080 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:15:36,542 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 13:15:36,827 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 13:15:36,837 - app.utils.session_state - INFO - Initializing session state
2025-05-13 13:15:36,838 - app.utils.session_state - INFO - Session state initialized
2025-05-13 13:15:37,733 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 13:15:37,749 - app.utils.memory_management - INFO - Memory before cleanup: 416.16 MB
2025-05-13 13:15:37,882 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 13:15:37,882 - app.utils.memory_management - INFO - Memory after cleanup: 416.54 MB (freed -0.38 MB)
2025-05-13 13:15:57,800 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:15:57,819 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:15:57,909 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.08 seconds
2025-05-13 13:15:57,911 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-13 13:15:57,912 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-13 13:15:57,913 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 13:15:57,981 - app.utils.memory_management - INFO - Memory before cleanup: 417.95 MB
2025-05-13 13:15:58,090 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 13:15:58,090 - app.utils.memory_management - INFO - Memory after cleanup: 417.95 MB (freed 0.00 MB)
2025-05-13 13:30:38,886 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:30:38,902 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 13:30:38,910 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:30:38,937 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 13:30:38,938 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-13 13:30:38,938 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-13 13:30:38,938 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 13:30:38,947 - app.utils.memory_management - INFO - Memory before cleanup: 418.76 MB
2025-05-13 13:30:39,096 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-13 13:30:39,097 - app.utils.memory_management - INFO - Memory after cleanup: 418.80 MB (freed -0.04 MB)
2025-05-13 13:30:44,489 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:30:44,499 - app.utils.memory_management - INFO - Memory before cleanup: 418.77 MB
2025-05-13 13:30:44,610 - app.utils.memory_management - INFO - Garbage collection: collected 196 objects
2025-05-13 13:30:44,610 - app.utils.memory_management - INFO - Memory after cleanup: 418.77 MB (freed 0.00 MB)
2025-05-13 13:30:54,165 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:30:54,177 - app.utils.memory_management - INFO - Memory before cleanup: 418.77 MB
2025-05-13 13:30:54,293 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-13 13:30:54,293 - app.utils.memory_management - INFO - Memory after cleanup: 418.77 MB (freed 0.00 MB)
2025-05-13 13:30:55,977 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:30:56,014 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-13 13:30:56,014 - app - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-13 13:30:56,015 - app - INFO - Data shape: (567, 36)
2025-05-13 13:30:56,015 - app - INFO - File COMI contains 2025 data
2025-05-13 13:30:56,037 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-13 13:30:56,038 - app - INFO - Features shape: (567, 36)
2025-05-13 13:30:56,051 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 13:30:56,052 - app - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-13 13:30:56,052 - app - INFO - Data shape: (567, 36)
2025-05-13 13:30:56,053 - app - INFO - File COMI contains 2025 data
2025-05-13 13:30:56,069 - app.utils.memory_management - INFO - Memory before cleanup: 420.49 MB
2025-05-13 13:30:56,163 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-13 13:30:56,163 - app.utils.memory_management - INFO - Memory after cleanup: 420.49 MB (freed 0.00 MB)
2025-05-13 13:30:58,912 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:30:59,128 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-13 13:30:59,498 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:30:59,520 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.02 seconds
2025-05-13 13:30:59,521 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-13 13:30:59,521 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-13 13:30:59,521 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-13 13:31:00,093 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.60 seconds
2025-05-13 13:31:00,300 - app.utils.memory_management - INFO - Memory before cleanup: 440.23 MB
2025-05-13 13:31:00,412 - app.utils.memory_management - INFO - Garbage collection: collected 4371 objects
2025-05-13 13:31:00,412 - app.utils.memory_management - INFO - Memory after cleanup: 440.23 MB (freed 0.00 MB)
2025-05-13 13:31:15,317 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:31:15,560 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-13 13:31:15,740 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:31:15,748 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-13 13:31:15,748 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-13 13:31:15,749 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-13 13:31:15,750 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-13 13:31:15,928 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.19 seconds
2025-05-13 13:31:16,101 - app.utils.memory_management - INFO - Memory before cleanup: 442.32 MB
2025-05-13 13:31:16,208 - app.utils.memory_management - INFO - Garbage collection: collected 4135 objects
2025-05-13 13:31:16,208 - app.utils.memory_management - INFO - Memory after cleanup: 442.32 MB (freed 0.00 MB)
2025-05-13 13:31:58,503 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:31:58,901 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:31:58,909 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-13 13:31:58,911 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-13 13:31:58,912 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-13 13:31:58,912 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-13 13:31:59,146 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.25 seconds
2025-05-13 13:31:59,349 - app.utils.memory_management - INFO - Memory before cleanup: 442.66 MB
2025-05-13 13:31:59,472 - app.utils.memory_management - INFO - Garbage collection: collected 3296 objects
2025-05-13 13:31:59,473 - app.utils.memory_management - INFO - Memory after cleanup: 442.66 MB (freed 0.00 MB)
2025-05-13 13:33:17,387 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:33:17,432 - app.utils.memory_management - INFO - Memory before cleanup: 442.64 MB
2025-05-13 13:33:17,553 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-05-13 13:33:17,554 - app.utils.memory_management - INFO - Memory after cleanup: 442.64 MB (freed 0.00 MB)
2025-05-13 13:41:52,846 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:41:52,869 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 13:41:52,915 - app - INFO - File COMI contains 2025 data
2025-05-13 13:41:52,921 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:41:52,921 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:41:52,924 - app.utils.memory_management - INFO - Memory before cleanup: 442.77 MB
2025-05-13 13:41:53,054 - app.utils.memory_management - INFO - Garbage collection: collected 210 objects
2025-05-13 13:41:53,055 - app.utils.memory_management - INFO - Memory after cleanup: 442.77 MB (freed 0.00 MB)
2025-05-13 13:42:13,958 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:42:13,998 - app - INFO - File COMI contains 2025 data
2025-05-13 13:42:14,011 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:42:14,013 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:42:14,015 - app.utils.memory_management - INFO - Memory before cleanup: 442.93 MB
2025-05-13 13:42:14,134 - app.utils.memory_management - INFO - Garbage collection: collected 228 objects
2025-05-13 13:42:14,135 - app.utils.memory_management - INFO - Memory after cleanup: 442.93 MB (freed 0.00 MB)
2025-05-13 13:42:24,263 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:42:24,321 - app - INFO - File COMI contains 2025 data
2025-05-13 13:42:24,327 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:42:24,327 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:42:24,330 - app.utils.memory_management - INFO - Memory before cleanup: 442.96 MB
2025-05-13 13:42:24,443 - app.utils.memory_management - INFO - Garbage collection: collected 228 objects
2025-05-13 13:42:24,444 - app.utils.memory_management - INFO - Memory after cleanup: 442.96 MB (freed 0.00 MB)
2025-05-13 13:42:25,413 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:42:25,462 - app - INFO - File COMI contains 2025 data
2025-05-13 13:42:25,466 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:42:25,467 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:42:25,469 - app.utils.memory_management - INFO - Memory before cleanup: 442.96 MB
2025-05-13 13:42:25,576 - app.utils.memory_management - INFO - Garbage collection: collected 250 objects
2025-05-13 13:42:25,576 - app.utils.memory_management - INFO - Memory after cleanup: 442.96 MB (freed 0.00 MB)
2025-05-13 13:42:35,067 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:42:35,145 - app - INFO - File COMI contains 2025 data
2025-05-13 13:42:35,170 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:42:35,171 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:42:35,173 - app.utils.memory_management - INFO - Memory before cleanup: 442.99 MB
2025-05-13 13:42:35,282 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-05-13 13:42:35,282 - app.utils.memory_management - INFO - Memory after cleanup: 442.99 MB (freed 0.00 MB)
2025-05-13 13:42:48,188 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:42:48,213 - app.utils.memory_management - INFO - Memory before cleanup: 442.98 MB
2025-05-13 13:42:48,351 - app.utils.memory_management - INFO - Garbage collection: collected 206 objects
2025-05-13 13:42:48,351 - app.utils.memory_management - INFO - Memory after cleanup: 442.98 MB (freed 0.00 MB)
2025-05-13 13:42:52,138 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:42:52,161 - app.utils.memory_management - INFO - Memory before cleanup: 442.98 MB
2025-05-13 13:42:52,292 - app.utils.memory_management - INFO - Garbage collection: collected 191 objects
2025-05-13 13:42:52,292 - app.utils.memory_management - INFO - Memory after cleanup: 442.98 MB (freed 0.00 MB)
2025-05-13 13:42:55,942 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:42:55,973 - app.utils.memory_management - INFO - Memory before cleanup: 443.00 MB
2025-05-13 13:42:56,092 - app.utils.memory_management - INFO - Garbage collection: collected 191 objects
2025-05-13 13:42:56,092 - app.utils.memory_management - INFO - Memory after cleanup: 443.00 MB (freed 0.00 MB)
2025-05-13 13:43:12,915 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:43:13,183 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-13 13:43:13,340 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:43:13,346 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-13 13:43:13,347 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-13 13:43:13,347 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-13 13:43:13,348 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-13 13:43:13,534 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.20 seconds
2025-05-13 13:43:13,757 - app.utils.memory_management - INFO - Memory before cleanup: 443.59 MB
2025-05-13 13:43:13,866 - app.utils.memory_management - INFO - Garbage collection: collected 3073 objects
2025-05-13 13:43:13,867 - app.utils.memory_management - INFO - Memory after cleanup: 443.59 MB (freed 0.00 MB)
2025-05-13 13:43:18,790 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:43:19,037 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-13 13:43:19,183 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:43:19,188 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-13 13:43:19,190 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-13 13:43:19,191 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-13 13:43:19,192 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-13 13:43:19,369 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.19 seconds
2025-05-13 13:43:19,556 - app.utils.memory_management - INFO - Memory before cleanup: 444.01 MB
2025-05-13 13:43:19,676 - app.utils.memory_management - INFO - Garbage collection: collected 3422 objects
2025-05-13 13:43:19,676 - app.utils.memory_management - INFO - Memory after cleanup: 444.01 MB (freed 0.00 MB)
2025-05-13 13:43:20,393 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:43:20,409 - app.utils.memory_management - INFO - Memory before cleanup: 443.96 MB
2025-05-13 13:43:20,522 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-05-13 13:43:20,522 - app.utils.memory_management - INFO - Memory after cleanup: 443.96 MB (freed 0.00 MB)
2025-05-13 13:43:31,060 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:43:31,098 - app - INFO - File COMI contains 2025 data
2025-05-13 13:43:31,102 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:43:31,103 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:43:31,107 - app.utils.memory_management - INFO - Memory before cleanup: 443.97 MB
2025-05-13 13:43:31,270 - app.utils.memory_management - INFO - Garbage collection: collected 210 objects
2025-05-13 13:43:31,275 - app.utils.memory_management - INFO - Memory after cleanup: 443.97 MB (freed 0.00 MB)
2025-05-13 13:44:13,735 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:44:13,820 - app - INFO - File COMI contains 2025 data
2025-05-13 13:44:13,847 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:44:13,848 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:44:13,851 - app.utils.memory_management - INFO - Memory before cleanup: 443.98 MB
2025-05-13 13:44:13,976 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-13 13:44:13,976 - app.utils.memory_management - INFO - Memory after cleanup: 443.98 MB (freed 0.00 MB)
2025-05-13 13:44:57,212 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:44:57,342 - app - INFO - File COMI contains 2025 data
2025-05-13 13:44:57,389 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:44:57,390 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:44:57,394 - app.utils.memory_management - INFO - Memory before cleanup: 443.99 MB
2025-05-13 13:44:57,524 - app.utils.memory_management - INFO - Garbage collection: collected 229 objects
2025-05-13 13:44:57,524 - app.utils.memory_management - INFO - Memory after cleanup: 443.99 MB (freed 0.00 MB)
2025-05-13 13:44:59,541 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:44:59,616 - app - INFO - File COMI contains 2025 data
2025-05-13 13:44:59,641 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:44:59,642 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:44:59,644 - app.utils.memory_management - INFO - Memory before cleanup: 443.99 MB
2025-05-13 13:44:59,752 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-05-13 13:44:59,752 - app.utils.memory_management - INFO - Memory after cleanup: 443.99 MB (freed 0.00 MB)
2025-05-13 13:45:01,316 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:45:01,392 - app - INFO - File COMI contains 2025 data
2025-05-13 13:45:01,415 - app - INFO - Saved data to data/stocks\COMI.csv
2025-05-13 13:45:01,415 - app - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:45:01,417 - app.utils.memory_management - INFO - Memory before cleanup: 443.98 MB
2025-05-13 13:45:01,544 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-05-13 13:45:01,544 - app.utils.memory_management - INFO - Memory after cleanup: 443.98 MB (freed 0.00 MB)
2025-05-13 13:45:40,509 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:45:40,550 - app.utils.memory_management - INFO - Memory before cleanup: 443.98 MB
2025-05-13 13:45:40,676 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-05-13 13:45:40,687 - app.utils.memory_management - INFO - Memory after cleanup: 443.98 MB (freed 0.00 MB)
2025-05-13 13:45:43,675 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:45:43,699 - app.utils.memory_management - INFO - Memory before cleanup: 443.98 MB
2025-05-13 13:45:43,803 - app.utils.memory_management - INFO - Garbage collection: collected 191 objects
2025-05-13 13:45:43,803 - app.utils.memory_management - INFO - Memory after cleanup: 443.98 MB (freed 0.00 MB)
2025-05-13 13:45:44,726 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:45:44,837 - app.utils.memory_management - INFO - Memory before cleanup: 444.82 MB
2025-05-13 13:45:44,937 - app.utils.memory_management - INFO - Garbage collection: collected 338 objects
2025-05-13 13:45:44,953 - app.utils.memory_management - INFO - Memory after cleanup: 444.82 MB (freed 0.00 MB)
2025-05-13 13:46:29,943 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:46:30,037 - app.utils.memory_management - INFO - Memory before cleanup: 445.75 MB
2025-05-13 13:46:30,162 - app.utils.memory_management - INFO - Garbage collection: collected 338 objects
2025-05-13 13:46:30,163 - app.utils.memory_management - INFO - Memory after cleanup: 445.75 MB (freed 0.00 MB)
2025-05-13 13:46:31,490 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:46:31,717 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-13 13:46:31,872 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:46:31,879 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-13 13:46:31,880 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-13 13:46:31,881 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-13 13:46:31,881 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-13 13:46:32,073 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.20 seconds
2025-05-13 13:46:32,287 - app.utils.memory_management - INFO - Memory before cleanup: 445.79 MB
2025-05-13 13:46:32,405 - app.utils.memory_management - INFO - Garbage collection: collected 3495 objects
2025-05-13 13:46:32,406 - app.utils.memory_management - INFO - Memory after cleanup: 445.79 MB (freed 0.00 MB)
2025-05-13 13:46:48,231 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:46:48,498 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-13 13:46:48,789 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:46:48,813 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 13:46:48,819 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:46:48,822 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 13:46:48,823 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 13:46:49,043 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.26 seconds
2025-05-13 13:46:49,300 - app.utils.memory_management - INFO - Memory before cleanup: 446.53 MB
2025-05-13 13:46:49,425 - app.utils.memory_management - INFO - Garbage collection: collected 3439 objects
2025-05-13 13:46:49,425 - app.utils.memory_management - INFO - Memory after cleanup: 446.53 MB (freed 0.00 MB)
2025-05-13 13:47:08,227 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:47:08,234 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 13:47:08,475 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-13 13:47:08,651 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:47:08,669 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 13:47:08,670 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:47:08,670 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 13:47:08,671 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 13:47:08,857 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.21 seconds
2025-05-13 13:47:09,044 - app.utils.memory_management - INFO - Memory before cleanup: 446.53 MB
2025-05-13 13:47:09,152 - app.utils.memory_management - INFO - Garbage collection: collected 2793 objects
2025-05-13 13:47:09,153 - app.utils.memory_management - INFO - Memory after cleanup: 446.53 MB (freed 0.00 MB)
2025-05-13 13:47:34,321 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:47:34,341 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-13 13:47:34,349 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-13 13:47:34,349 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-13 13:47:34,353 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-13 13:47:34,357 - app.utils.memory_management - INFO - Memory before cleanup: 446.49 MB
2025-05-13 13:47:34,468 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-05-13 13:47:34,468 - app.utils.memory_management - INFO - Memory after cleanup: 446.49 MB (freed 0.00 MB)
2025-05-13 13:47:55,926 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:47:55,948 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-13 13:47:55,953 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-13 13:47:55,953 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-13 13:47:55,956 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-13 13:47:55,963 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 13:47:58,460 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 13:47:58,460 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 13:47:58,468 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.82
2025-05-13 13:47:58,576 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 13:47:58,576 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 13:47:58,577 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 13:48:01,746 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 13:48:03,871 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 13:48:03,880 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 13:48:03,880 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 13:48:03,881 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 13:48:03,881 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 13:48:03,881 - app.utils.error_handling - INFO - fetch_price executed in 5.30 seconds
2025-05-13 13:48:03,883 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 13:48:06,000 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 13:48:06,117 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 13:48:06,227 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 13:48:06,234 - models.predict - INFO - Loading lstm model for COMI with horizon 5
2025-05-13 13:48:06,234 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_5min.keras
2025-05-13 13:48:07,567 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-05-13 13:48:09,400 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-13 13:48:09,400 - models.predict - INFO - Prediction for 5 minutes horizon: 80.82773821353912
2025-05-13 13:48:09,400 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-13 13:48:09,554 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-13 13:48:09,684 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 13:48:09,699 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-13 13:48:09,700 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-13 13:48:09,700 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-13 13:48:09,700 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-13 13:48:09,900 - models.hybrid_model - INFO - XGBoost is available
2025-05-13 13:48:09,900 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-13 13:48:09,900 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-05-13 13:48:09,900 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_15min.joblib, searching for alternatives...
2025-05-13 13:48:09,900 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-13 13:48:09,900 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 13:48:09,900 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 13:48:09,900 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 13:48:09,900 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-13 13:48:09,900 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 13:48:09,900 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 13:48:09,900 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 13:48:09,900 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-13 13:48:09,900 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 13:48:09,900 - models.predict - INFO - Prediction for 15 minutes horizon: 80.0
2025-05-13 13:48:09,900 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-13 13:48:10,083 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-13 13:48:10,217 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 13:48:10,217 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-13 13:48:10,217 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-13 13:48:11,056 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-13 13:48:12,067 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-13 13:48:12,067 - models.predict - INFO - Prediction for 30 minutes horizon: 72.85751612186431
2025-05-13 13:48:12,067 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-13 13:48:12,235 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-13 13:48:12,368 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 13:48:12,368 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-13 13:48:12,368 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-13 13:48:12,368 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-13 13:48:12,368 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-13 13:48:12,368 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-13 13:48:12,368 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-13 13:48:12,368 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-13 13:48:12,368 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-13 13:48:12,368 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-13 13:48:12,368 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 13:48:12,368 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-13 13:48:12,368 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 13:48:12,383 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-13 13:48:12,383 - models.predict - ERROR - Error in prediction for horizon 60: invalid index to scalar variable.
2025-05-13 13:48:12,383 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-13 13:48:12,383 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 13:48:12,383 - models.predict - INFO - Prediction for 60 minutes horizon: 80.0
2025-05-13 13:48:12,410 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 5 minutes
2025-05-13 13:48:12,423 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 15 minutes
2025-05-13 13:48:12,440 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-13 13:48:12,449 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-13 13:48:12,466 - app.utils.memory_management - INFO - Memory before cleanup: 497.12 MB
2025-05-13 13:48:12,623 - app.utils.memory_management - INFO - Garbage collection: collected 10657 objects
2025-05-13 13:48:12,624 - app.utils.memory_management - INFO - Memory after cleanup: 476.11 MB (freed 21.00 MB)
2025-05-13 13:48:44,669 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:48:44,707 - app.utils.memory_management - INFO - Memory before cleanup: 477.73 MB
2025-05-13 13:48:44,884 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-05-13 13:48:44,885 - app.utils.memory_management - INFO - Memory after cleanup: 477.73 MB (freed 0.00 MB)
2025-05-13 13:48:49,017 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:48:49,034 - app.utils.memory_management - INFO - Memory before cleanup: 477.72 MB
2025-05-13 13:48:49,132 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-13 13:48:49,148 - app.utils.memory_management - INFO - Memory after cleanup: 477.72 MB (freed 0.00 MB)
2025-05-13 13:48:51,932 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:48:51,952 - app.utils.memory_management - INFO - Memory before cleanup: 477.73 MB
2025-05-13 13:48:52,066 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-13 13:48:52,066 - app.utils.memory_management - INFO - Memory after cleanup: 477.73 MB (freed 0.00 MB)
2025-05-13 13:48:57,363 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:48:57,391 - app.utils.memory_management - INFO - Memory before cleanup: 477.72 MB
2025-05-13 13:48:57,515 - app.utils.memory_management - INFO - Garbage collection: collected 199 objects
2025-05-13 13:48:57,515 - app.utils.memory_management - INFO - Memory after cleanup: 477.72 MB (freed 0.00 MB)
2025-05-13 13:48:58,208 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:48:58,225 - app.utils.memory_management - INFO - Memory before cleanup: 477.73 MB
2025-05-13 13:48:58,332 - app.utils.memory_management - INFO - Garbage collection: collected 199 objects
2025-05-13 13:48:58,332 - app.utils.memory_management - INFO - Memory after cleanup: 477.73 MB (freed 0.00 MB)
2025-05-13 13:49:02,908 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:49:02,949 - app.utils.memory_management - INFO - Memory before cleanup: 477.73 MB
2025-05-13 13:49:03,065 - app.utils.memory_management - INFO - Garbage collection: collected 199 objects
2025-05-13 13:49:03,065 - app.utils.memory_management - INFO - Memory after cleanup: 477.73 MB (freed 0.00 MB)
2025-05-13 13:49:19,172 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:49:19,192 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 13:49:20,335 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 13:49:20,336 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 13:49:20,351 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.02
2025-05-13 13:49:20,352 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 13:49:20,352 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 13:49:20,353 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 13:49:25,870 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 13:49:28,000 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 13:49:28,009 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 13:49:28,009 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 13:49:28,009 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 13:49:28,010 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 13:49:28,010 - app.utils.error_handling - INFO - fetch_price executed in 7.66 seconds
2025-05-13 13:49:28,033 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-13 13:49:28,150 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-13 13:49:28,266 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 13:49:28,268 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-13 13:49:28,269 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-13 13:49:28,269 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-13 13:49:28,269 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-13 13:49:28,270 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-05-13 13:49:28,271 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_15min.joblib, searching for alternatives...
2025-05-13 13:49:28,272 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-13 13:49:28,272 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 13:49:28,273 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 13:49:28,274 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 13:49:28,274 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-13 13:49:28,274 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 13:49:28,275 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 13:49:28,275 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 13:49:28,275 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-13 13:49:28,277 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 13:49:28,277 - models.predict - INFO - Prediction for 15 minutes horizon: 80.0
2025-05-13 13:49:28,299 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-13 13:49:28,412 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-13 13:49:28,532 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 13:49:28,533 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-13 13:49:28,534 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-13 13:49:29,111 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-13 13:49:29,968 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-13 13:49:29,968 - models.predict - INFO - Prediction for 30 minutes horizon: 72.85751612186431
2025-05-13 13:49:29,991 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-13 13:49:30,131 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-13 13:49:30,231 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 13:49:30,248 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-13 13:49:30,248 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-13 13:49:30,248 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-13 13:49:30,248 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-13 13:49:30,248 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-13 13:49:30,248 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-13 13:49:30,248 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-13 13:49:30,248 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-13 13:49:30,248 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-13 13:49:30,248 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 13:49:30,248 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-13 13:49:30,248 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 13:49:30,248 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-13 13:49:30,248 - models.predict - ERROR - Error in prediction for horizon 60: invalid index to scalar variable.
2025-05-13 13:49:30,248 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-13 13:49:30,248 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 13:49:30,248 - models.predict - INFO - Prediction for 60 minutes horizon: 80.0
2025-05-13 13:49:30,271 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 15 minutes
2025-05-13 13:49:30,286 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-13 13:49:30,294 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-13 13:49:30,324 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 13:49:32,434 - app.utils.memory_management - INFO - Memory before cleanup: 486.21 MB
2025-05-13 13:49:32,556 - app.utils.memory_management - INFO - Garbage collection: collected 330 objects
2025-05-13 13:49:32,556 - app.utils.memory_management - INFO - Memory after cleanup: 486.21 MB (freed 0.00 MB)
2025-05-13 13:49:45,728 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:49:45,747 - app.utils.memory_management - INFO - Memory before cleanup: 476.30 MB
2025-05-13 13:49:45,865 - app.utils.memory_management - INFO - Garbage collection: collected 210 objects
2025-05-13 13:49:45,865 - app.utils.memory_management - INFO - Memory after cleanup: 476.30 MB (freed 0.00 MB)
2025-05-13 13:49:47,115 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:49:47,167 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-13 13:49:47,305 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-13 13:49:47,423 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 13:49:47,427 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-13 13:49:47,427 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-13 13:49:47,427 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-13 13:49:47,427 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-13 13:49:47,427 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-13 13:49:47,427 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-13 13:49:47,431 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-13 13:49:47,431 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-13 13:49:47,432 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-13 13:49:47,433 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 13:49:47,433 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-13 13:49:47,433 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 13:49:47,434 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-13 13:49:47,434 - models.predict - ERROR - Error in prediction for horizon 60: invalid index to scalar variable.
2025-05-13 13:49:47,435 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-13 13:49:47,435 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 13:49:47,435 - models.predict - INFO - Prediction for 60 minutes horizon: 80.0
2025-05-13 13:49:47,534 - app.utils.memory_management - INFO - Memory before cleanup: 477.32 MB
2025-05-13 13:49:47,647 - app.utils.memory_management - INFO - Garbage collection: collected 1067 objects
2025-05-13 13:49:47,647 - app.utils.memory_management - INFO - Memory after cleanup: 477.30 MB (freed 0.02 MB)
2025-05-13 13:50:07,085 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:50:07,117 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 3.2 hours of target)
2025-05-13 13:50:07,120 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 3.2 hours of target)
2025-05-13 13:50:07,140 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 2.2 hours of target)
2025-05-13 13:50:07,151 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.2 hours of target)
2025-05-13 13:50:07,162 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 1.3 hours of target)
2025-05-13 13:50:07,176 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 1.3 hours of target)
2025-05-13 13:50:07,187 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 1.3 hours of target)
2025-05-13 13:50:07,198 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 1.3 hours of target)
2025-05-13 13:50:07,199 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.0 hours of target)
2025-05-13 13:50:07,218 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.2 hours of target)
2025-05-13 13:50:07,263 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.9 hours of target)
2025-05-13 13:50:07,289 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.0 hours of target)
2025-05-13 13:50:07,303 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.2 hours of target)
2025-05-13 13:50:07,318 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.9 hours of target)
2025-05-13 13:50:07,328 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.1 hours of target)
2025-05-13 13:50:07,338 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.2 hours of target)
2025-05-13 13:50:07,347 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.0 hours of target)
2025-05-13 13:50:07,347 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.1 hours of target)
2025-05-13 13:50:07,365 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.3 hours of target)
2025-05-13 13:50:07,365 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.1 hours of target)
2025-05-13 13:50:07,388 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 10.1 hours of target)
2025-05-13 13:50:07,397 - app.components.performance_metrics - INFO - Target time 2025-05-13 10:04:34.514509 is in the future compared to latest data 2025-05-13 00:00:00
2025-05-13 13:50:07,406 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.5 hours of target)
2025-05-13 13:50:07,413 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.7 hours of target)
2025-05-13 13:50:07,413 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.4 hours of target)
2025-05-13 13:50:07,430 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.5 hours of target)
2025-05-13 13:50:07,430 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.7 hours of target)
2025-05-13 13:50:07,446 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.0 hours of target)
2025-05-13 13:50:07,463 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.5 hours of target)
2025-05-13 13:50:07,463 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.6 hours of target)
2025-05-13 13:50:07,480 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.7 hours of target)
2025-05-13 13:50:07,480 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.0 hours of target)
2025-05-13 13:50:07,496 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.5 hours of target)
2025-05-13 13:50:07,513 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.6 hours of target)
2025-05-13 13:50:07,513 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.8 hours of target)
2025-05-13 13:50:07,530 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.0 hours of target)
2025-05-13 13:50:07,530 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.5 hours of target)
2025-05-13 13:50:07,546 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.8 hours of target)
2025-05-13 13:50:07,546 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.9 hours of target)
2025-05-13 13:50:07,563 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.2 hours of target)
2025-05-13 13:50:07,580 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.7 hours of target)
2025-05-13 13:50:07,580 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.8 hours of target)
2025-05-13 13:50:07,596 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.0 hours of target)
2025-05-13 13:50:07,596 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.2 hours of target)
2025-05-13 13:50:07,613 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.7 hours of target)
2025-05-13 13:50:07,630 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.9 hours of target)
2025-05-13 13:50:07,630 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.0 hours of target)
2025-05-13 13:50:07,646 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.3 hours of target)
2025-05-13 13:50:07,646 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.8 hours of target)
2025-05-13 13:50:07,666 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 10.9 hours of target)
2025-05-13 13:50:07,666 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.1 hours of target)
2025-05-13 13:50:07,680 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.3 hours of target)
2025-05-13 13:50:07,697 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.8 hours of target)
2025-05-13 13:50:07,697 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.1 hours of target)
2025-05-13 13:50:07,714 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.3 hours of target)
2025-05-13 13:50:07,714 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.5 hours of target)
2025-05-13 13:50:07,731 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 12.0 hours of target)
2025-05-13 13:50:07,747 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.1 hours of target)
2025-05-13 13:50:07,747 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.3 hours of target)
2025-05-13 13:50:07,764 - app.components.performance_metrics - INFO - Found price 78.6 for COMI at 2025-05-11 00:00:00 (within 11.5 hours of target)
2025-05-13 13:50:07,764 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 12.0 hours of target)
2025-05-13 13:50:07,780 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.1 hours of target)
2025-05-13 13:50:07,797 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.0 hours of target)
2025-05-13 13:50:07,797 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 10.7 hours of target)
2025-05-13 13:50:07,814 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 10.2 hours of target)
2025-05-13 13:50:07,814 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.1 hours of target)
2025-05-13 13:50:07,831 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.0 hours of target)
2025-05-13 13:50:07,848 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 10.2 hours of target)
2025-05-13 13:50:07,848 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 7.0 hours of target)
2025-05-13 13:50:07,864 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 6.8 hours of target)
2025-05-13 13:50:07,864 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 6.6 hours of target)
2025-05-13 13:50:07,880 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 6.1 hours of target)
2025-05-13 13:50:07,897 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 7.1 hours of target)
2025-05-13 13:50:07,897 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 4.7 hours of target)
2025-05-13 13:50:07,914 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 4.6 hours of target)
2025-05-13 13:50:07,914 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 4.3 hours of target)
2025-05-13 13:50:07,930 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 3.8 hours of target)
2025-05-13 13:50:07,947 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 3.0 hours of target)
2025-05-13 13:50:07,947 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 2.9 hours of target)
2025-05-13 13:50:07,964 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 2.6 hours of target)
2025-05-13 13:50:07,964 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 2.1 hours of target)
2025-05-13 13:50:07,980 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 2.9 hours of target)
2025-05-13 13:50:07,997 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 2.1 hours of target)
2025-05-13 13:50:07,997 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 2.6 hours of target)
2025-05-13 13:50:08,014 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.6 hours of target)
2025-05-13 13:50:08,014 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.8 hours of target)
2025-05-13 13:50:08,030 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 12.0 hours of target)
2025-05-13 13:50:08,047 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 11.5 hours of target)
2025-05-13 13:50:08,047 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.7 hours of target)
2025-05-13 13:50:08,064 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.8 hours of target)
2025-05-13 13:50:08,064 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 11.4 hours of target)
2025-05-13 13:50:08,081 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.7 hours of target)
2025-05-13 13:50:08,098 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.9 hours of target)
2025-05-13 13:50:08,098 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 11.9 hours of target)
2025-05-13 13:50:08,114 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 11.4 hours of target)
2025-05-13 13:50:08,132 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.8 hours of target)
2025-05-13 13:50:08,141 - app.components.performance_metrics - INFO - Found price 79.45 for COMI at 2025-05-12 00:00:00 (within 11.9 hours of target)
2025-05-13 13:50:08,152 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 11.8 hours of target)
2025-05-13 13:50:08,160 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 11.3 hours of target)
2025-05-13 13:50:08,164 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 6.8 hours of target)
2025-05-13 13:50:08,181 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 6.6 hours of target)
2025-05-13 13:50:08,181 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 6.4 hours of target)
2025-05-13 13:50:08,198 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 5.9 hours of target)
2025-05-13 13:50:08,214 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 6.8 hours of target)
2025-05-13 13:50:08,214 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 6.6 hours of target)
2025-05-13 13:50:08,231 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 6.3 hours of target)
2025-05-13 13:50:08,247 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 5.8 hours of target)
2025-05-13 13:50:08,247 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 3.1 hours of target)
2025-05-13 13:50:08,264 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 3.0 hours of target)
2025-05-13 13:50:08,282 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.7 hours of target)
2025-05-13 13:50:08,299 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.2 hours of target)
2025-05-13 13:50:08,315 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.9 hours of target)
2025-05-13 13:50:08,315 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.7 hours of target)
2025-05-13 13:50:08,331 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.5 hours of target)
2025-05-13 13:50:08,348 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.0 hours of target)
2025-05-13 13:50:08,348 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.8 hours of target)
2025-05-13 13:50:08,364 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.7 hours of target)
2025-05-13 13:50:08,381 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 2.4 hours of target)
2025-05-13 13:50:08,382 - app.components.performance_metrics - INFO - Found price 80.0 for COMI at 2025-05-13 00:00:00 (within 1.9 hours of target)
2025-05-13 13:50:08,414 - app.components.performance_metrics - INFO - Updated performance metrics for 6 model-symbol combinations
2025-05-13 13:50:08,561 - app.utils.memory_management - INFO - Memory before cleanup: 482.02 MB
2025-05-13 13:50:08,679 - app.utils.memory_management - INFO - Garbage collection: collected 1565 objects
2025-05-13 13:50:08,679 - app.utils.memory_management - INFO - Memory after cleanup: 482.02 MB (freed 0.00 MB)
2025-05-13 13:50:29,024 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:50:29,056 - app.components.performance_metrics - INFO - Target time 2025-05-13 10:04:34.514509 is in the future compared to latest data 2025-05-13 00:00:00
2025-05-13 13:50:29,167 - app.utils.memory_management - INFO - Memory before cleanup: 482.41 MB
2025-05-13 13:50:29,294 - app.utils.memory_management - INFO - Garbage collection: collected 1507 objects
2025-05-13 13:50:29,294 - app.utils.memory_management - INFO - Memory after cleanup: 482.41 MB (freed 0.00 MB)
2025-05-13 13:50:59,976 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:00,018 - app.utils.memory_management - INFO - Memory before cleanup: 482.39 MB
2025-05-13 13:51:00,164 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-13 13:51:00,165 - app.utils.memory_management - INFO - Memory after cleanup: 482.39 MB (freed 0.00 MB)
2025-05-13 13:51:01,197 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:01,216 - app.utils.memory_management - INFO - Memory before cleanup: 482.39 MB
2025-05-13 13:51:01,329 - app.utils.memory_management - INFO - Garbage collection: collected 197 objects
2025-05-13 13:51:01,329 - app.utils.memory_management - INFO - Memory after cleanup: 482.39 MB (freed 0.00 MB)
2025-05-13 13:51:02,351 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:02,378 - app.utils.memory_management - INFO - Memory before cleanup: 482.39 MB
2025-05-13 13:51:02,486 - app.utils.memory_management - INFO - Garbage collection: collected 196 objects
2025-05-13 13:51:02,486 - app.utils.memory_management - INFO - Memory after cleanup: 482.39 MB (freed 0.00 MB)
2025-05-13 13:51:04,085 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:04,109 - app.utils.memory_management - INFO - Memory before cleanup: 482.39 MB
2025-05-13 13:51:04,212 - app.utils.memory_management - INFO - Garbage collection: collected 209 objects
2025-05-13 13:51:04,227 - app.utils.memory_management - INFO - Memory after cleanup: 482.39 MB (freed 0.00 MB)
2025-05-13 13:51:05,220 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:05,268 - app.utils.memory_management - INFO - Memory before cleanup: 482.39 MB
2025-05-13 13:51:05,373 - app.utils.memory_management - INFO - Garbage collection: collected 364 objects
2025-05-13 13:51:05,373 - app.utils.memory_management - INFO - Memory after cleanup: 482.39 MB (freed 0.00 MB)
2025-05-13 13:51:07,271 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:07,303 - app.utils.memory_management - INFO - Memory before cleanup: 482.40 MB
2025-05-13 13:51:07,428 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-05-13 13:51:07,428 - app.utils.memory_management - INFO - Memory after cleanup: 482.40 MB (freed 0.00 MB)
2025-05-13 13:51:08,332 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:08,335 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 13:51:09,530 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 13:51:09,534 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 13:51:11,710 - app.utils.memory_management - INFO - Memory before cleanup: 482.45 MB
2025-05-13 13:51:11,836 - app.utils.memory_management - INFO - Garbage collection: collected 248 objects
2025-05-13 13:51:11,836 - app.utils.memory_management - INFO - Memory after cleanup: 482.45 MB (freed 0.00 MB)
2025-05-13 13:51:14,060 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:14,069 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 13:51:14,883 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 13:51:14,900 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 13:51:14,917 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 13:51:14,918 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 13:51:14,919 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 13:51:14,920 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 13:51:14,930 - app.utils.memory_management - INFO - Memory before cleanup: 482.60 MB
2025-05-13 13:51:15,073 - app.utils.memory_management - INFO - Garbage collection: collected 191 objects
2025-05-13 13:51:15,073 - app.utils.memory_management - INFO - Memory after cleanup: 482.60 MB (freed 0.00 MB)
2025-05-13 13:51:15,228 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 13:51:15,229 - app.utils.memory_management - INFO - Memory before cleanup: 482.58 MB
2025-05-13 13:51:15,386 - app.utils.memory_management - INFO - Garbage collection: collected 192 objects
2025-05-13 13:51:15,387 - app.utils.memory_management - INFO - Memory after cleanup: 482.58 MB (freed 0.00 MB)
2025-05-13 13:51:15,387 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 14:17:18,688 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:17:19,654 - app - INFO - Memory management utilities loaded
2025-05-13 14:17:19,654 - app - INFO - Error handling utilities loaded
2025-05-13 14:17:19,654 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 14:17:19,654 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 14:17:19,654 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 14:17:19,654 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 14:17:19,654 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 14:17:19,654 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 14:17:19,654 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 14:17:19,654 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:17:19,654 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:17:19,654 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 14:17:19,662 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 14:17:19,662 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:17:19,662 - app - INFO - Applied NumPy fix
2025-05-13 14:17:19,664 - app.config - INFO - Configuration initialized
2025-05-13 14:17:23,046 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:17:23,065 - models.train - INFO - TensorFlow test successful
2025-05-13 14:17:23,604 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:17:23,604 - models.train - INFO - Transformer model is available
2025-05-13 14:17:23,605 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:17:23,606 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:17:23,606 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:17:23,606 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:17:24,013 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:17:24,172 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:17:24,183 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:17:24,185 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:17:25,050 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:17:25,057 - app.utils.memory_management - INFO - Memory before cleanup: 416.52 MB
2025-05-13 14:17:25,155 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:17:25,155 - app.utils.memory_management - INFO - Memory after cleanup: 416.89 MB (freed -0.37 MB)
2025-05-13 14:17:31,229 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:17:31,245 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:17:31,248 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:17:31,273 - app.utils.memory_management - INFO - Memory before cleanup: 420.12 MB
2025-05-13 14:17:31,397 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:17:31,398 - app.utils.memory_management - INFO - Memory after cleanup: 420.12 MB (freed 0.00 MB)
2025-05-13 14:17:35,830 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:17:35,844 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 14:17:37,003 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 14:17:37,009 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 14:17:39,371 - app.utils.memory_management - INFO - Memory before cleanup: 421.87 MB
2025-05-13 14:17:39,473 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-13 14:17:39,473 - app.utils.memory_management - INFO - Memory after cleanup: 421.91 MB (freed -0.04 MB)
2025-05-13 14:18:09,048 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:18:09,966 - app - INFO - Memory management utilities loaded
2025-05-13 14:18:09,966 - app - INFO - Error handling utilities loaded
2025-05-13 14:18:09,966 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 14:18:09,966 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 14:18:09,966 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 14:18:09,966 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 14:18:09,966 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 14:18:09,966 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 14:18:09,966 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 14:18:09,966 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:18:09,981 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:18:09,981 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 14:18:09,981 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 14:18:09,981 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:18:09,981 - app - INFO - Applied NumPy fix
2025-05-13 14:18:09,984 - app.config - INFO - Configuration initialized
2025-05-13 14:18:12,587 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:18:12,603 - models.train - INFO - TensorFlow test successful
2025-05-13 14:18:13,090 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:18:13,091 - models.train - INFO - Transformer model is available
2025-05-13 14:18:13,091 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:18:13,092 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:18:13,092 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:18:13,093 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:18:13,582 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:18:13,805 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:18:13,814 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:18:13,815 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:18:14,510 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:18:14,543 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:18:14,551 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:18:14,561 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:18:14,572 - app.utils.memory_management - INFO - Memory before cleanup: 415.67 MB
2025-05-13 14:18:14,690 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:18:14,691 - app.utils.memory_management - INFO - Memory after cleanup: 416.05 MB (freed -0.38 MB)
2025-05-13 14:18:15,823 - app.utils.memory_management - INFO - Memory before cleanup: 419.34 MB
2025-05-13 14:18:15,940 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:18:15,941 - app.utils.memory_management - INFO - Memory after cleanup: 419.34 MB (freed 0.00 MB)
2025-05-13 14:23:45,125 - app - INFO - Cleaning up resources...
2025-05-13 14:23:45,126 - app.utils.memory_management - INFO - Memory before cleanup: 482.66 MB
2025-05-13 14:23:45,353 - app.utils.memory_management - INFO - Garbage collection: collected 257 objects
2025-05-13 14:23:45,356 - app.utils.memory_management - INFO - Memory after cleanup: 482.66 MB (freed 0.00 MB)
2025-05-13 14:23:45,356 - app - INFO - Application shutdown complete
2025-05-13 14:30:11,755 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:30:12,785 - app - INFO - Memory management utilities loaded
2025-05-13 14:30:12,785 - app - INFO - Error handling utilities loaded
2025-05-13 14:30:12,785 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 14:30:12,785 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 14:30:12,785 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 14:30:12,785 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 14:30:12,798 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 14:30:12,798 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 14:30:12,798 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 14:30:12,798 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:30:12,798 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:30:12,798 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 14:30:12,798 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 14:30:12,798 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:30:12,801 - app - INFO - Applied NumPy fix
2025-05-13 14:30:12,803 - app.config - INFO - Configuration initialized
2025-05-13 14:30:15,818 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:30:15,847 - models.train - INFO - TensorFlow test successful
2025-05-13 14:30:16,314 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:30:16,315 - models.train - INFO - Transformer model is available
2025-05-13 14:30:16,315 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:30:16,316 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:30:16,316 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:30:16,316 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:30:16,685 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:30:16,818 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:30:16,837 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:30:16,838 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:30:17,669 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:30:17,669 - app.utils.memory_management - INFO - Memory before cleanup: 416.14 MB
2025-05-13 14:30:17,785 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:30:17,785 - app.utils.memory_management - INFO - Memory after cleanup: 416.14 MB (freed -0.01 MB)
2025-05-13 14:30:31,748 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:30:31,758 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:30:31,781 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:30:31,783 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:30:31,785 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:30:31,787 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:30:31,787 - app.utils.memory_management - INFO - Memory before cleanup: 421.88 MB
2025-05-13 14:30:31,885 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:30:31,885 - app.utils.memory_management - INFO - Memory after cleanup: 421.88 MB (freed 0.00 MB)
2025-05-13 14:32:12,031 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:32:12,977 - app - INFO - Memory management utilities loaded
2025-05-13 14:32:12,982 - app - INFO - Error handling utilities loaded
2025-05-13 14:32:12,984 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 14:32:12,986 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 14:32:12,986 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 14:32:12,986 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 14:32:12,986 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 14:32:12,987 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 14:32:12,987 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 14:32:12,987 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:32:12,988 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:32:12,988 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 14:32:12,988 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 14:32:12,988 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:32:12,989 - app - INFO - Applied NumPy fix
2025-05-13 14:32:12,990 - app.config - INFO - Configuration initialized
2025-05-13 14:32:15,435 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:32:15,451 - models.train - INFO - TensorFlow test successful
2025-05-13 14:32:15,924 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:32:15,924 - models.train - INFO - Transformer model is available
2025-05-13 14:32:15,925 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:32:15,926 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:32:15,926 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:32:15,926 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:32:16,284 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:32:16,409 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:32:16,419 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:32:16,420 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:32:17,187 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:32:17,194 - app.utils.memory_management - INFO - Memory before cleanup: 415.54 MB
2025-05-13 14:32:17,302 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:32:17,302 - app.utils.memory_management - INFO - Memory after cleanup: 415.90 MB (freed -0.36 MB)
2025-05-13 14:32:21,301 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:32:21,304 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:32:21,336 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:32:21,336 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:32:21,336 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:32:21,336 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:32:21,336 - app.utils.memory_management - INFO - Memory before cleanup: 421.23 MB
2025-05-13 14:32:21,454 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:32:21,454 - app.utils.memory_management - INFO - Memory after cleanup: 421.23 MB (freed 0.00 MB)
2025-05-13 14:33:19,200 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:33:19,224 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:33:19,252 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:33:19,253 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:33:19,253 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:33:19,254 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:33:19,257 - app.utils.memory_management - INFO - Memory before cleanup: 416.56 MB
2025-05-13 14:33:19,388 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:33:19,389 - app.utils.memory_management - INFO - Memory after cleanup: 416.60 MB (freed -0.04 MB)
2025-05-13 14:33:20,948 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:33:20,953 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:33:20,978 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:33:20,979 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:33:20,980 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:33:20,981 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:33:20,982 - app.utils.memory_management - INFO - Memory before cleanup: 416.63 MB
2025-05-13 14:33:21,081 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:33:21,081 - app.utils.memory_management - INFO - Memory after cleanup: 416.63 MB (freed 0.00 MB)
2025-05-13 14:33:39,815 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:33:39,834 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:33:39,851 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:33:39,852 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:33:39,852 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:33:39,852 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:33:39,967 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 14:33:41,078 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 14:33:41,079 - scrapers.price_scraper - INFO - Created TradingView widget price data structure for EGX:COMI
2025-05-13 14:33:41,081 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 14:33:43,200 - app.utils.performance - WARNING - enhanced_prediction failed after 0.0000s: predict_future_prices() got an unexpected keyword argument 'live_data'
2025-05-13 14:33:43,204 - app.components.tradingview_predictions - ERROR - Error generating predictions: predict_future_prices() got an unexpected keyword argument 'live_data'
2025-05-13 14:33:43,208 - app.utils.memory_management - INFO - Memory before cleanup: 417.06 MB
2025-05-13 14:33:43,312 - app.utils.memory_management - INFO - Garbage collection: collected 227 objects
2025-05-13 14:33:43,312 - app.utils.memory_management - INFO - Memory after cleanup: 417.06 MB (freed 0.00 MB)
2025-05-13 14:37:11,512 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:37:12,391 - app - INFO - Memory management utilities loaded
2025-05-13 14:37:12,391 - app - INFO - Error handling utilities loaded
2025-05-13 14:37:12,391 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 14:37:12,391 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 14:37:12,391 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 14:37:12,391 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 14:37:12,391 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 14:37:12,391 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 14:37:12,391 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 14:37:12,391 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:37:12,391 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:37:12,391 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 14:37:12,391 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 14:37:12,391 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:37:12,391 - app - INFO - Applied NumPy fix
2025-05-13 14:37:12,405 - app.config - INFO - Configuration initialized
2025-05-13 14:37:15,028 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:37:15,044 - models.train - INFO - TensorFlow test successful
2025-05-13 14:37:15,636 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:37:15,636 - models.train - INFO - Transformer model is available
2025-05-13 14:37:15,636 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:37:15,638 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:37:15,638 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:37:15,638 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:37:16,109 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:37:16,264 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:37:16,279 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:37:16,280 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:37:16,453 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:37:16,458 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:37:16,459 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:37:16,473 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:37:16,504 - app.utils.memory_management - INFO - Memory before cleanup: 414.18 MB
2025-05-13 14:37:16,735 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:37:16,735 - app.utils.memory_management - INFO - Memory after cleanup: 414.62 MB (freed -0.44 MB)
2025-05-13 14:37:18,348 - app.utils.memory_management - INFO - Memory before cleanup: 420.14 MB
2025-05-13 14:37:18,441 - app.utils.memory_management - INFO - Garbage collection: collected 108 objects
2025-05-13 14:37:18,441 - app.utils.memory_management - INFO - Memory after cleanup: 420.14 MB (freed 0.00 MB)
2025-05-13 14:37:21,414 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:37:21,429 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:37:21,451 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:37:21,451 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:37:21,451 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:37:21,451 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:37:21,459 - app.utils.memory_management - INFO - Memory before cleanup: 422.88 MB
2025-05-13 14:37:21,610 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-13 14:37:21,610 - app.utils.memory_management - INFO - Memory after cleanup: 422.91 MB (freed -0.04 MB)
2025-05-13 14:37:35,367 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:37:35,382 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:37:35,399 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:37:35,400 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:37:35,400 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:37:35,401 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:37:35,403 - app.utils.memory_management - INFO - Memory before cleanup: 424.20 MB
2025-05-13 14:37:35,524 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:37:35,541 - app.utils.memory_management - INFO - Memory after cleanup: 424.20 MB (freed 0.00 MB)
2025-05-13 14:37:36,442 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:37:36,456 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:37:36,474 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:37:36,475 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:37:36,477 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:37:36,477 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:37:36,477 - app.utils.memory_management - INFO - Memory before cleanup: 424.24 MB
2025-05-13 14:37:36,590 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:37:36,590 - app.utils.memory_management - INFO - Memory after cleanup: 424.24 MB (freed 0.00 MB)
2025-05-13 14:37:46,162 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:37:46,175 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:37:46,198 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:37:46,200 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:37:46,200 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:37:46,201 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:37:46,326 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 14:37:47,479 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 14:37:47,479 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 14:37:47,487 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.19
2025-05-13 14:37:47,537 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 14:37:47,538 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 14:37:47,538 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 14:37:51,665 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 14:37:53,800 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 14:37:53,810 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 14:37:53,811 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 14:37:53,811 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 14:37:53,811 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 14:37:53,811 - app.utils.error_handling - INFO - fetch_price executed in 6.27 seconds
2025-05-13 14:37:53,812 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 14:38:29,740 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:38:29,743 - app - INFO - Memory management utilities loaded
2025-05-13 14:38:29,744 - app - INFO - Error handling utilities loaded
2025-05-13 14:38:29,744 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 14:38:29,744 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 14:38:29,745 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 14:38:29,745 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:38:29,745 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:38:29,746 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 14:38:29,746 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:38:29,746 - app - INFO - Applied NumPy fix
2025-05-13 14:38:29,748 - app.config - INFO - Configuration initialized
2025-05-13 14:38:29,750 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:38:29,751 - models.train - INFO - TensorFlow test successful
2025-05-13 14:38:29,753 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:38:29,753 - models.train - INFO - Transformer model is available
2025-05-13 14:38:29,753 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:38:29,757 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:38:29,757 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:38:29,757 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:38:29,776 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:38:29,780 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:38:29,789 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:38:29,790 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:38:29,797 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:38:29,799 - app.utils.memory_management - INFO - Memory before cleanup: 420.14 MB
2025-05-13 14:38:29,911 - app.utils.memory_management - INFO - Garbage collection: collected 207 objects
2025-05-13 14:38:29,911 - app.utils.memory_management - INFO - Memory after cleanup: 420.14 MB (freed 0.00 MB)
2025-05-13 14:38:34,825 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:38:34,837 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:38:34,852 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:38:34,852 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:38:34,853 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:38:34,854 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:38:34,856 - app.utils.memory_management - INFO - Memory before cleanup: 420.34 MB
2025-05-13 14:38:34,962 - app.utils.memory_management - INFO - Garbage collection: collected 196 objects
2025-05-13 14:38:34,972 - app.utils.memory_management - INFO - Memory after cleanup: 420.34 MB (freed 0.00 MB)
2025-05-13 14:38:46,331 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:38:46,345 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:38:46,360 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:38:46,361 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:38:46,361 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:38:46,362 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:38:46,363 - app.utils.memory_management - INFO - Memory before cleanup: 420.32 MB
2025-05-13 14:38:46,455 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:38:46,455 - app.utils.memory_management - INFO - Memory after cleanup: 420.32 MB (freed 0.00 MB)
2025-05-13 14:38:47,606 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:38:47,606 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:38:47,635 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:38:47,636 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:38:47,636 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:38:47,637 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:38:47,642 - app.utils.memory_management - INFO - Memory before cleanup: 420.33 MB
2025-05-13 14:38:47,750 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:38:47,750 - app.utils.memory_management - INFO - Memory after cleanup: 420.33 MB (freed 0.00 MB)
2025-05-13 14:38:51,440 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:38:51,456 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:38:51,473 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:38:51,473 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:38:51,473 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:38:51,474 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:38:51,589 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 14:38:52,694 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 14:38:52,695 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 14:38:52,706 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.25
2025-05-13 14:38:52,760 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 14:38:52,761 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 14:38:52,761 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 14:38:56,762 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 14:38:58,892 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 14:38:58,901 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 14:38:58,901 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 14:38:58,901 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 14:38:58,901 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 14:38:58,902 - app.utils.error_handling - INFO - fetch_price executed in 6.14 seconds
2025-05-13 14:38:58,902 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 14:39:01,022 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-05-13 14:39:01,130 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.94 KB)
2025-05-13 14:39:01,238 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 14:39:01,238 - models.predict - INFO - Using scikit-learn ensemble model for 1440 minutes horizon
2025-05-13 14:39:01,255 - models.hybrid_model - INFO - XGBoost is available
2025-05-13 14:39:01,255 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-13 14:39:01,255 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-05-13 14:39:01,255 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_1440min.joblib
2025-05-13 14:39:01,255 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_1440min.joblib
2025-05-13 14:39:01,422 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-05-13 14:39:01,437 - models.sklearn_model - INFO - Using hybrid model predict method for ensemble
2025-05-13 14:39:01,461 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-13 14:39:01,462 - models.predict - INFO - Prediction for 1440 minutes horizon: 80.98030215938235
2025-05-13 14:39:01,462 - models.predict - INFO - Making predictions for 2880 minutes horizon
2025-05-13 14:39:01,557 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_2880_scaler.pkl (0.94 KB)
2025-05-13 14:39:01,657 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 14:39:01,659 - models.predict - INFO - Using scikit-learn ensemble model for 2880 minutes horizon
2025-05-13 14:39:01,660 - models.predict - INFO - Loading ensemble model for COMI with horizon 2880
2025-05-13 14:39:01,660 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_2880min.joblib
2025-05-13 14:39:01,660 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_2880min.joblib
2025-05-13 14:39:01,790 - models.predict - INFO - Successfully loaded model for COMI with horizon 2880
2025-05-13 14:39:01,805 - models.sklearn_model - INFO - Using hybrid model predict method for ensemble
2025-05-13 14:39:01,815 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-13 14:39:01,816 - models.predict - INFO - Prediction for 2880 minutes horizon: 81.06078611576908
2025-05-13 14:39:01,893 - app.utils.memory_management - INFO - Memory before cleanup: 432.35 MB
2025-05-13 14:39:02,017 - app.utils.memory_management - INFO - Garbage collection: collected 278 objects
2025-05-13 14:39:02,018 - app.utils.memory_management - INFO - Memory after cleanup: 432.35 MB (freed 0.00 MB)
2025-05-13 14:48:54,902 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:48:55,789 - app - INFO - Memory management utilities loaded
2025-05-13 14:48:55,789 - app - INFO - Error handling utilities loaded
2025-05-13 14:48:55,789 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 14:48:55,789 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 14:48:55,789 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 14:48:55,789 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 14:48:55,789 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 14:48:55,789 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 14:48:55,789 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 14:48:55,789 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:48:55,789 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:48:55,789 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 14:48:55,789 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 14:48:55,789 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:48:55,789 - app - INFO - Applied NumPy fix
2025-05-13 14:48:55,804 - app.config - INFO - Configuration initialized
2025-05-13 14:48:58,474 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:48:58,491 - models.train - INFO - TensorFlow test successful
2025-05-13 14:48:59,190 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:48:59,190 - models.train - INFO - Transformer model is available
2025-05-13 14:48:59,190 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:48:59,191 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:48:59,192 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:48:59,192 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:48:59,615 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:48:59,787 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:48:59,814 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:48:59,832 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:48:59,833 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:48:59,834 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:48:59,835 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:48:59,846 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:48:59,854 - app.utils.memory_management - INFO - Memory before cleanup: 413.34 MB
2025-05-13 14:49:00,011 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:49:00,013 - app.utils.memory_management - INFO - Memory after cleanup: 413.71 MB (freed -0.37 MB)
2025-05-13 14:49:01,756 - app.utils.memory_management - INFO - Memory before cleanup: 419.36 MB
2025-05-13 14:49:01,869 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:49:01,870 - app.utils.memory_management - INFO - Memory after cleanup: 419.36 MB (freed 0.00 MB)
2025-05-13 14:49:20,281 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:49:20,300 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:49:20,324 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:49:20,324 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:49:20,324 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:49:20,324 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:49:20,328 - app.utils.memory_management - INFO - Memory before cleanup: 422.71 MB
2025-05-13 14:49:20,439 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-13 14:49:20,440 - app.utils.memory_management - INFO - Memory after cleanup: 422.75 MB (freed -0.04 MB)
2025-05-13 14:49:32,126 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:49:32,141 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:49:32,160 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:49:32,161 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:49:32,161 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:49:32,162 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:49:32,163 - app.utils.memory_management - INFO - Memory before cleanup: 418.19 MB
2025-05-13 14:49:32,284 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:49:32,288 - app.utils.memory_management - INFO - Memory after cleanup: 418.19 MB (freed 0.00 MB)
2025-05-13 14:49:33,294 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:49:33,372 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:49:33,440 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:49:33,440 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:49:33,440 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:49:33,440 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:49:33,440 - app.utils.memory_management - INFO - Memory before cleanup: 418.19 MB
2025-05-13 14:49:33,594 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:49:33,594 - app.utils.memory_management - INFO - Memory after cleanup: 418.19 MB (freed 0.00 MB)
2025-05-13 14:49:34,481 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:49:34,496 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:49:34,514 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:49:34,515 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:49:34,515 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:49:34,516 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:49:34,518 - app.utils.memory_management - INFO - Memory before cleanup: 418.18 MB
2025-05-13 14:49:34,634 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:49:34,634 - app.utils.memory_management - INFO - Memory after cleanup: 418.18 MB (freed 0.00 MB)
2025-05-13 14:49:37,108 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:49:37,121 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:49:37,137 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:49:37,139 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:49:37,140 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:49:37,140 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:49:37,142 - app.utils.memory_management - INFO - Memory before cleanup: 418.18 MB
2025-05-13 14:49:37,256 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:49:37,256 - app.utils.memory_management - INFO - Memory after cleanup: 418.18 MB (freed 0.00 MB)
2025-05-13 14:49:40,106 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:49:40,131 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:49:40,161 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:49:40,162 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:49:40,163 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:49:40,163 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:49:40,310 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 14:49:41,987 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 14:49:41,987 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 14:49:41,996 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.31
2025-05-13 14:49:42,043 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 14:49:42,044 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 14:49:42,044 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 14:49:44,420 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 14:49:46,542 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 14:49:46,554 - scrapers.price_scraper - INFO - Successfully extracted price from header: 4.903
2025-05-13 14:49:46,555 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 4.903
2025-05-13 14:49:46,555 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 14:49:46,555 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 14:49:46,556 - app.utils.error_handling - INFO - fetch_price executed in 4.51 seconds
2025-05-13 14:49:46,557 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 14:52:26,682 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:52:26,685 - app - INFO - Memory management utilities loaded
2025-05-13 14:52:26,686 - app - INFO - Error handling utilities loaded
2025-05-13 14:52:26,686 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 14:52:26,686 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 14:52:26,687 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 14:52:26,687 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:52:26,687 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:52:26,688 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 14:52:26,688 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:52:26,688 - app - INFO - Applied NumPy fix
2025-05-13 14:52:26,689 - app.config - INFO - Configuration initialized
2025-05-13 14:52:26,691 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:52:26,692 - models.train - INFO - TensorFlow test successful
2025-05-13 14:52:26,693 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:52:26,694 - models.train - INFO - Transformer model is available
2025-05-13 14:52:26,694 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:52:26,695 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:52:26,695 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:52:26,695 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:52:26,709 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:52:26,718 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:52:26,726 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:52:26,727 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:52:26,732 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:52:26,736 - app.utils.memory_management - INFO - Memory before cleanup: 434.75 MB
2025-05-13 14:52:26,853 - app.utils.memory_management - INFO - Garbage collection: collected 283 objects
2025-05-13 14:52:26,853 - app.utils.memory_management - INFO - Memory after cleanup: 434.75 MB (freed 0.00 MB)
2025-05-13 14:52:31,426 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:52:31,437 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:52:31,452 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:52:31,453 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:52:31,453 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:52:31,455 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:52:31,457 - app.utils.memory_management - INFO - Memory before cleanup: 435.05 MB
2025-05-13 14:52:31,555 - app.utils.memory_management - INFO - Garbage collection: collected 196 objects
2025-05-13 14:52:31,555 - app.utils.memory_management - INFO - Memory after cleanup: 435.05 MB (freed 0.00 MB)
2025-05-13 14:52:42,161 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:52:42,176 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:52:42,194 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:52:42,195 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:52:42,196 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:52:42,196 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:52:42,201 - app.utils.memory_management - INFO - Memory before cleanup: 435.05 MB
2025-05-13 14:52:42,308 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:52:42,308 - app.utils.memory_management - INFO - Memory after cleanup: 435.05 MB (freed 0.00 MB)
2025-05-13 14:52:56,906 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:52:56,918 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:52:56,935 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:52:56,935 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:52:56,935 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:52:56,936 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:52:56,937 - app.utils.memory_management - INFO - Memory before cleanup: 435.04 MB
2025-05-13 14:52:57,034 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:52:57,034 - app.utils.memory_management - INFO - Memory after cleanup: 435.04 MB (freed 0.00 MB)
2025-05-13 14:53:03,151 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:53:03,151 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:53:03,178 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:53:03,179 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:53:03,180 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:53:03,180 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:53:03,182 - app.utils.memory_management - INFO - Memory before cleanup: 435.05 MB
2025-05-13 14:53:03,284 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:53:03,284 - app.utils.memory_management - INFO - Memory after cleanup: 435.05 MB (freed 0.00 MB)
2025-05-13 14:53:12,345 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:53:12,355 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:53:12,371 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:53:12,371 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:53:12,372 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:53:12,372 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:53:12,374 - app.utils.memory_management - INFO - Memory before cleanup: 435.05 MB
2025-05-13 14:53:12,483 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:53:12,483 - app.utils.memory_management - INFO - Memory after cleanup: 435.05 MB (freed 0.00 MB)
2025-05-13 14:53:13,517 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:53:13,527 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:53:13,543 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:53:13,544 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:53:13,544 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:53:13,545 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:53:13,546 - app.utils.memory_management - INFO - Memory before cleanup: 435.04 MB
2025-05-13 14:53:13,650 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:53:13,650 - app.utils.memory_management - INFO - Memory after cleanup: 435.04 MB (freed 0.00 MB)
2025-05-13 14:53:14,968 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:53:14,978 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:53:14,995 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:53:14,995 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:53:14,996 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:53:14,996 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:53:14,998 - app.utils.memory_management - INFO - Memory before cleanup: 435.06 MB
2025-05-13 14:53:15,099 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:53:15,099 - app.utils.memory_management - INFO - Memory after cleanup: 435.06 MB (freed 0.00 MB)
2025-05-13 14:53:16,249 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:53:16,251 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:53:16,276 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:53:16,276 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:53:16,277 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:53:16,277 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:53:16,279 - app.utils.memory_management - INFO - Memory before cleanup: 435.06 MB
2025-05-13 14:53:16,383 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:53:16,383 - app.utils.memory_management - INFO - Memory after cleanup: 435.06 MB (freed 0.00 MB)
2025-05-13 14:53:19,704 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:53:19,718 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:53:19,735 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:53:19,735 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:53:19,736 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:53:19,736 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:53:19,867 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 14:53:20,948 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 14:53:20,948 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 14:53:20,965 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.92
2025-05-13 14:53:20,965 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 14:53:20,966 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 14:53:20,966 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 14:53:25,350 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 14:53:27,490 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 14:53:27,498 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 14:53:27,498 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 14:53:27,498 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 14:53:27,498 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 14:53:27,499 - app.utils.error_handling - INFO - fetch_price executed in 6.53 seconds
2025-05-13 14:53:27,500 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 14:53:29,615 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 14:53:29,716 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 14:53:29,831 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 14:53:29,833 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 14:53:29,833 - models.hybrid_model - INFO - XGBoost is available
2025-05-13 14:53:29,833 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-13 14:53:29,833 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 14:53:29,833 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 14:53:29,833 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 14:53:29,916 - models.predict - ERROR - Error making predictions: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-13 14:53:29,917 - app.components.tradingview_predictions - ERROR - Error generating predictions: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-13 14:53:29,921 - app.utils.memory_management - INFO - Memory before cleanup: 436.68 MB
2025-05-13 14:53:30,016 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:53:30,016 - app.utils.memory_management - INFO - Memory after cleanup: 436.68 MB (freed 0.00 MB)
2025-05-13 14:57:54,834 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:57:55,723 - app - INFO - Memory management utilities loaded
2025-05-13 14:57:55,723 - app - INFO - Error handling utilities loaded
2025-05-13 14:57:55,739 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 14:57:55,741 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 14:57:55,741 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 14:57:55,741 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 14:57:55,743 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 14:57:55,743 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 14:57:55,743 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 14:57:55,744 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:57:55,744 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:57:55,744 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 14:57:55,745 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 14:57:55,745 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:57:55,746 - app - INFO - Applied NumPy fix
2025-05-13 14:57:55,749 - app.config - INFO - Configuration initialized
2025-05-13 14:57:58,589 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:57:58,623 - models.train - INFO - TensorFlow test successful
2025-05-13 14:57:59,237 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:57:59,238 - models.train - INFO - Transformer model is available
2025-05-13 14:57:59,238 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:57:59,239 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:57:59,239 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:57:59,239 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:57:59,479 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:57:59,710 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:57:59,886 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:57:59,904 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:57:59,905 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:57:59,916 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:57:59,921 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:57:59,941 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:57:59,961 - app.utils.memory_management - INFO - Memory before cleanup: 413.23 MB
2025-05-13 14:58:00,104 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 14:58:00,105 - app.utils.memory_management - INFO - Memory after cleanup: 413.36 MB (freed -0.13 MB)
2025-05-13 14:58:01,899 - app.utils.memory_management - INFO - Memory before cleanup: 419.44 MB
2025-05-13 14:58:01,997 - app.utils.memory_management - INFO - Garbage collection: collected 108 objects
2025-05-13 14:58:01,998 - app.utils.memory_management - INFO - Memory after cleanup: 419.44 MB (freed 0.00 MB)
2025-05-13 14:58:02,940 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:58:02,953 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:58:02,982 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-13 14:58:02,983 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:58:02,983 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:58:02,984 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:58:02,987 - app.utils.memory_management - INFO - Memory before cleanup: 422.23 MB
2025-05-13 14:58:03,108 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-13 14:58:03,109 - app.utils.memory_management - INFO - Memory after cleanup: 422.27 MB (freed -0.04 MB)
2025-05-13 14:58:11,855 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:58:11,868 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:58:11,934 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:58:11,937 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:58:11,937 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:58:11,938 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:58:11,940 - app.utils.memory_management - INFO - Memory before cleanup: 423.07 MB
2025-05-13 14:58:12,076 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:58:12,076 - app.utils.memory_management - INFO - Memory after cleanup: 423.07 MB (freed 0.00 MB)
2025-05-13 14:58:12,685 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:58:12,698 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:58:12,714 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:58:12,715 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:58:12,715 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:58:12,716 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:58:12,718 - app.utils.memory_management - INFO - Memory before cleanup: 423.07 MB
2025-05-13 14:58:12,826 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:58:12,826 - app.utils.memory_management - INFO - Memory after cleanup: 423.07 MB (freed 0.00 MB)
2025-05-13 14:58:13,825 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:58:13,839 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:58:13,856 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:58:13,857 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:58:13,858 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:58:13,859 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:58:13,863 - app.utils.memory_management - INFO - Memory before cleanup: 423.08 MB
2025-05-13 14:58:13,960 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:58:13,960 - app.utils.memory_management - INFO - Memory after cleanup: 423.08 MB (freed 0.00 MB)
2025-05-13 14:58:16,310 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:58:16,321 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:58:16,341 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:58:16,342 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:58:16,342 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:58:16,343 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:58:16,344 - app.utils.memory_management - INFO - Memory before cleanup: 423.10 MB
2025-05-13 14:58:16,442 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 14:58:16,442 - app.utils.memory_management - INFO - Memory after cleanup: 423.10 MB (freed 0.00 MB)
2025-05-13 14:58:18,576 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:58:18,593 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:58:18,613 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:58:18,614 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:58:18,615 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:58:18,615 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:58:18,727 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 14:58:19,857 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 14:58:19,857 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 14:58:19,865 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.04
2025-05-13 14:58:19,913 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 14:58:19,914 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 14:58:19,914 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 14:58:23,345 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 14:58:25,476 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 14:58:25,484 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 14:58:25,485 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 14:58:25,485 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 14:58:25,485 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 14:58:25,485 - app.utils.error_handling - INFO - fetch_price executed in 5.57 seconds
2025-05-13 14:58:25,486 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 14:58:27,593 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 14:58:27,593 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 14:58:27,593 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 14:58:27,593 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:58:27,594 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:58:27,595 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 14:58:27,595 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:58:27,596 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 14:58:27,617 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 14:58:27,742 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 14:58:27,859 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 14:58:27,859 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 14:58:27,887 - models.hybrid_model - INFO - XGBoost is available
2025-05-13 14:58:27,887 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-13 14:58:27,888 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 14:58:27,888 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 14:58:27,889 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 14:58:27,918 - models.predict - ERROR - Error making predictions: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-13 14:58:27,918 - app.components.tradingview_predictions - ERROR - BitGenerator error in prediction: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-13 14:58:27,919 - app.components.tradingview_predictions - INFO - Applied BitGenerator fix, trying prediction again
2025-05-13 14:58:27,936 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 14:58:28,040 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 14:58:28,152 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 14:58:28,154 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 14:58:28,154 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 14:58:28,155 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 14:58:28,155 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 14:58:28,188 - models.predict - ERROR - Error making predictions: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 14:58:28,188 - app.components.tradingview_predictions - ERROR - Still failed after BitGenerator fix: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 14:58:28,188 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-13 14:58:28,189 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 80.0
2025-05-13 14:58:28,189 - app.components.tradingview_predictions - INFO - Fallback prediction for 5 minutes: 80.00227847831636
2025-05-13 14:58:28,189 - app.components.tradingview_predictions - INFO - Fallback prediction for 15 minutes: 80.00402708051757
2025-05-13 14:58:28,190 - app.components.tradingview_predictions - INFO - Fallback prediction for 30 minutes: 79.98654216713496
2025-05-13 14:58:28,190 - app.components.tradingview_predictions - INFO - Fallback prediction for 60 minutes: 79.99933956953143
2025-05-13 14:58:28,259 - app.utils.memory_management - INFO - Memory before cleanup: 425.89 MB
2025-05-13 14:58:28,358 - app.utils.memory_management - INFO - Garbage collection: collected 366 objects
2025-05-13 14:58:28,358 - app.utils.memory_management - INFO - Memory after cleanup: 425.90 MB (freed -0.01 MB)
2025-05-13 14:59:37,884 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 14:59:37,888 - app - INFO - Memory management utilities loaded
2025-05-13 14:59:37,888 - app - INFO - Error handling utilities loaded
2025-05-13 14:59:37,889 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 14:59:37,889 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 14:59:37,890 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 14:59:37,890 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 14:59:37,891 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 14:59:37,891 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 14:59:37,891 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 14:59:37,892 - app - INFO - Applied NumPy fix
2025-05-13 14:59:37,893 - app.config - INFO - Configuration initialized
2025-05-13 14:59:37,895 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 14:59:37,896 - models.train - INFO - TensorFlow test successful
2025-05-13 14:59:37,898 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 14:59:37,898 - models.train - INFO - Transformer model is available
2025-05-13 14:59:37,899 - models.train - INFO - Using TensorFlow-based models
2025-05-13 14:59:37,900 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 14:59:37,900 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 14:59:37,900 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:59:37,921 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 14:59:37,925 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 14:59:37,933 - app.utils.session_state - INFO - Initializing session state
2025-05-13 14:59:37,934 - app.utils.session_state - INFO - Session state initialized
2025-05-13 14:59:37,941 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 14:59:37,945 - app.utils.memory_management - INFO - Memory before cleanup: 437.77 MB
2025-05-13 14:59:38,063 - app.utils.memory_management - INFO - Garbage collection: collected 276 objects
2025-05-13 14:59:38,063 - app.utils.memory_management - INFO - Memory after cleanup: 437.77 MB (freed 0.00 MB)
2025-05-13 14:59:42,222 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 14:59:42,226 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 14:59:42,251 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 14:59:42,252 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 14:59:42,252 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 14:59:42,252 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 14:59:42,256 - app.utils.memory_management - INFO - Memory before cleanup: 438.62 MB
2025-05-13 14:59:42,356 - app.utils.memory_management - INFO - Garbage collection: collected 196 objects
2025-05-13 14:59:42,373 - app.utils.memory_management - INFO - Memory after cleanup: 438.62 MB (freed 0.00 MB)
2025-05-13 15:00:15,254 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:00:15,273 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:00:15,296 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:00:15,297 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:00:15,297 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:00:15,297 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:00:15,300 - app.utils.memory_management - INFO - Memory before cleanup: 438.62 MB
2025-05-13 15:00:15,438 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:00:15,438 - app.utils.memory_management - INFO - Memory after cleanup: 438.62 MB (freed 0.00 MB)
2025-05-13 15:00:23,719 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:00:23,731 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:00:23,748 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:00:23,748 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:00:23,749 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:00:23,749 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:00:23,751 - app.utils.memory_management - INFO - Memory before cleanup: 438.62 MB
2025-05-13 15:00:23,866 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:00:23,881 - app.utils.memory_management - INFO - Memory after cleanup: 438.62 MB (freed 0.00 MB)
2025-05-13 15:00:24,714 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:00:24,725 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:00:24,741 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:00:24,741 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:00:24,742 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:00:24,742 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:00:24,743 - app.utils.memory_management - INFO - Memory before cleanup: 438.62 MB
2025-05-13 15:00:24,854 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:00:24,855 - app.utils.memory_management - INFO - Memory after cleanup: 438.62 MB (freed 0.00 MB)
2025-05-13 15:00:25,890 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:00:25,901 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:00:25,918 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:00:25,919 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:00:25,920 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:00:25,921 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:00:25,924 - app.utils.memory_management - INFO - Memory before cleanup: 438.61 MB
2025-05-13 15:00:26,022 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:00:26,022 - app.utils.memory_management - INFO - Memory after cleanup: 438.61 MB (freed 0.00 MB)
2025-05-13 15:00:29,942 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:00:29,956 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:00:29,971 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:00:29,972 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:00:29,972 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:00:29,973 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:00:30,092 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 15:00:31,205 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 15:00:31,206 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 15:00:31,225 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.37
2025-05-13 15:00:31,225 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 15:00:31,226 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 15:00:31,226 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 15:00:34,747 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 15:00:36,874 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 15:00:36,883 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 15:00:36,883 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 15:00:36,883 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 15:00:36,883 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 15:00:36,884 - app.utils.error_handling - INFO - fetch_price executed in 5.66 seconds
2025-05-13 15:00:36,884 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 15:00:39,009 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:00:39,010 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:00:39,011 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:00:39,012 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:00:39,015 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 15:00:39,016 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:00:39,016 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:00:39,016 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 15:00:39,032 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:00:39,139 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:00:39,254 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:00:39,256 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 15:00:39,256 - models.hybrid_model - INFO - XGBoost is available
2025-05-13 15:00:39,256 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-13 15:00:39,256 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 15:00:39,256 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:00:39,256 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:00:39,289 - models.predict - ERROR - Error making predictions: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-13 15:00:39,289 - app.components.tradingview_predictions - ERROR - BitGenerator error in prediction: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-13 15:00:39,289 - app.components.tradingview_predictions - INFO - Applied BitGenerator fix, trying prediction again
2025-05-13 15:00:39,304 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:00:39,405 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:00:39,523 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:00:39,524 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 15:00:39,525 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 15:00:39,525 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:00:39,525 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:00:39,556 - models.predict - ERROR - Error making predictions: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:00:39,556 - app.components.tradingview_predictions - ERROR - Still failed after BitGenerator fix: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:00:39,556 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-13 15:00:39,557 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 80.0
2025-05-13 15:00:39,557 - app.components.tradingview_predictions - INFO - Fallback prediction for 5 minutes: 80.00502255704811
2025-05-13 15:00:39,557 - app.components.tradingview_predictions - INFO - Fallback prediction for 15 minutes: 79.99850984929869
2025-05-13 15:00:39,557 - app.components.tradingview_predictions - INFO - Fallback prediction for 30 minutes: 80.00698385700177
2025-05-13 15:00:39,558 - app.components.tradingview_predictions - INFO - Fallback prediction for 60 minutes: 80.05076637704425
2025-05-13 15:00:39,581 - app.utils.memory_management - INFO - Memory before cleanup: 435.57 MB
2025-05-13 15:00:39,688 - app.utils.memory_management - INFO - Garbage collection: collected 379 objects
2025-05-13 15:00:39,688 - app.utils.memory_management - INFO - Memory after cleanup: 435.60 MB (freed -0.02 MB)
2025-05-13 15:01:21,923 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:01:21,947 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:01:21,975 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:01:21,975 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:01:21,975 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:01:21,975 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:01:21,975 - app.utils.memory_management - INFO - Memory before cleanup: 438.00 MB
2025-05-13 15:01:22,159 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-13 15:01:22,160 - app.utils.memory_management - INFO - Memory after cleanup: 438.00 MB (freed 0.00 MB)
2025-05-13 15:01:39,654 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:01:39,670 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:01:39,687 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:01:39,688 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:01:39,688 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:01:39,689 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:01:39,690 - app.utils.memory_management - INFO - Memory before cleanup: 438.20 MB
2025-05-13 15:01:39,804 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:01:39,805 - app.utils.memory_management - INFO - Memory after cleanup: 438.20 MB (freed 0.00 MB)
2025-05-13 15:02:07,772 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:02:07,782 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:02:07,801 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:02:07,802 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:02:07,802 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:02:07,802 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:02:07,804 - app.utils.memory_management - INFO - Memory before cleanup: 438.20 MB
2025-05-13 15:02:07,919 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:02:07,920 - app.utils.memory_management - INFO - Memory after cleanup: 438.20 MB (freed 0.00 MB)
2025-05-13 15:02:16,289 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:02:16,297 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:02:16,313 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:02:16,314 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:02:16,314 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:02:16,315 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:02:16,317 - app.utils.memory_management - INFO - Memory before cleanup: 438.20 MB
2025-05-13 15:02:16,481 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:02:16,482 - app.utils.memory_management - INFO - Memory after cleanup: 438.20 MB (freed 0.00 MB)
2025-05-13 15:02:41,704 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:02:41,713 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:02:41,728 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:02:41,729 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:02:41,729 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:02:41,730 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:02:41,731 - app.utils.memory_management - INFO - Memory before cleanup: 438.21 MB
2025-05-13 15:02:41,835 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:02:41,835 - app.utils.memory_management - INFO - Memory after cleanup: 438.21 MB (freed 0.00 MB)
2025-05-13 15:02:44,340 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:02:44,350 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:02:44,367 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:02:44,367 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:02:44,368 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:02:44,368 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:02:44,370 - app.utils.memory_management - INFO - Memory before cleanup: 438.20 MB
2025-05-13 15:02:44,470 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:02:44,485 - app.utils.memory_management - INFO - Memory after cleanup: 438.20 MB (freed 0.00 MB)
2025-05-13 15:03:11,128 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:03:11,139 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:03:11,154 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:03:11,155 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:03:11,155 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:03:11,156 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:03:11,157 - app.utils.memory_management - INFO - Memory before cleanup: 438.20 MB
2025-05-13 15:03:11,284 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:03:11,285 - app.utils.memory_management - INFO - Memory after cleanup: 438.20 MB (freed 0.00 MB)
2025-05-13 15:03:15,292 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:03:15,303 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:03:15,317 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:03:15,317 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:03:15,317 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:03:15,317 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:03:15,317 - app.utils.memory_management - INFO - Memory before cleanup: 438.24 MB
2025-05-13 15:03:15,440 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:03:15,442 - app.utils.memory_management - INFO - Memory after cleanup: 438.24 MB (freed 0.00 MB)
2025-05-13 15:03:17,546 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:03:17,559 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:03:17,574 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:03:17,575 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:03:17,575 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:03:17,576 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:03:17,577 - app.utils.memory_management - INFO - Memory before cleanup: 438.24 MB
2025-05-13 15:03:17,689 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:03:17,689 - app.utils.memory_management - INFO - Memory after cleanup: 438.24 MB (freed 0.00 MB)
2025-05-13 15:03:21,998 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:03:22,011 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:03:22,028 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:03:22,030 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:03:22,030 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:03:22,030 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:03:22,034 - app.utils.memory_management - INFO - Memory before cleanup: 438.24 MB
2025-05-13 15:03:22,151 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:03:22,172 - app.utils.memory_management - INFO - Memory after cleanup: 438.24 MB (freed 0.00 MB)
2025-05-13 15:03:26,575 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:03:26,588 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:03:26,605 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:03:26,606 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:03:26,607 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:03:26,607 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:03:26,724 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 15:03:27,865 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 15:03:27,866 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 15:03:27,874 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.85
2025-05-13 15:03:27,874 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 15:03:27,874 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 15:03:27,874 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 15:03:31,817 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 15:03:33,935 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 15:03:33,944 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 15:03:33,944 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 15:03:33,944 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 15:03:33,945 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 15:03:33,945 - app.utils.error_handling - INFO - fetch_price executed in 6.07 seconds
2025-05-13 15:03:33,946 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 15:03:36,054 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:03:36,055 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:03:36,056 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:03:36,057 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:03:36,057 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: 'MockMT19937' object has no attribute 'capsule'
2025-05-13 15:03:36,059 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-13 15:03:36,060 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:03:36,061 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:03:36,061 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 15:03:36,076 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:03:36,183 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:03:36,294 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:03:36,296 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 15:03:36,296 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 15:03:36,297 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:03:36,297 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:03:36,328 - models.predict - ERROR - Error making predictions: <class 'app.utils.numpy_fix.fix_numpy_imports.<locals>.FixedMT19937'> is not a known BitGenerator module.
2025-05-13 15:03:36,328 - app.components.tradingview_predictions - ERROR - BitGenerator error in prediction: <class 'app.utils.numpy_fix.fix_numpy_imports.<locals>.FixedMT19937'> is not a known BitGenerator module.
2025-05-13 15:03:36,328 - app.components.tradingview_predictions - INFO - Applied BitGenerator fix, trying prediction again
2025-05-13 15:03:36,335 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:03:36,450 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:03:36,561 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:03:36,563 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 15:03:36,563 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 15:03:36,563 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:03:36,564 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:03:36,594 - models.predict - ERROR - Error making predictions: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:03:36,594 - app.components.tradingview_predictions - ERROR - Still failed after BitGenerator fix: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:03:36,594 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-13 15:03:36,595 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 80.0
2025-05-13 15:03:36,595 - app.components.tradingview_predictions - INFO - Fallback prediction for 5 minutes: 80.00463719796568
2025-05-13 15:03:36,595 - app.components.tradingview_predictions - INFO - Fallback prediction for 15 minutes: 79.9946228382304
2025-05-13 15:03:36,595 - app.components.tradingview_predictions - INFO - Fallback prediction for 30 minutes: 80.02308897235756
2025-05-13 15:03:36,596 - app.components.tradingview_predictions - INFO - Fallback prediction for 60 minutes: 79.9547303652734
2025-05-13 15:03:36,620 - app.utils.memory_management - INFO - Memory before cleanup: 436.32 MB
2025-05-13 15:03:36,722 - app.utils.memory_management - INFO - Garbage collection: collected 405 objects
2025-05-13 15:03:36,723 - app.utils.memory_management - INFO - Memory after cleanup: 436.32 MB (freed 0.00 MB)
2025-05-13 15:04:13,350 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:04:13,378 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:04:13,398 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:04:13,399 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:04:13,400 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:04:13,400 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:04:13,402 - app.utils.memory_management - INFO - Memory before cleanup: 438.56 MB
2025-05-13 15:04:13,518 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-13 15:04:13,518 - app.utils.memory_management - INFO - Memory after cleanup: 438.56 MB (freed 0.00 MB)
2025-05-13 15:04:16,501 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:04:16,519 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:04:16,533 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:04:16,533 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:04:16,534 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:04:16,534 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:04:16,655 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 15:04:17,777 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 15:04:17,778 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 15:04:17,785 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.13
2025-05-13 15:04:17,786 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 15:04:17,786 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 15:04:17,786 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 15:04:20,986 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 15:04:23,118 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 15:04:23,127 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 15:04:23,127 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 15:04:23,128 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 15:04:23,128 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 15:04:23,128 - app.utils.error_handling - INFO - fetch_price executed in 5.34 seconds
2025-05-13 15:04:23,129 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 15:04:25,232 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:04:25,232 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:04:25,232 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:04:25,233 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:04:25,233 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: 'MockMT19937' object has no attribute 'capsule'
2025-05-13 15:04:25,233 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-13 15:04:25,233 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:04:25,234 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:04:25,234 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 15:04:25,251 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:04:25,349 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:04:25,466 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:04:25,466 - models.predict - INFO - Loading lstm model for COMI with horizon 5
2025-05-13 15:04:25,466 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_5min.keras
2025-05-13 15:04:26,051 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-05-13 15:04:26,947 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-13 15:04:26,948 - models.predict - INFO - Prediction for 5 minutes horizon: 80.92113251686095
2025-05-13 15:04:26,948 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-13 15:04:27,066 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-13 15:04:27,166 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:04:27,182 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-13 15:04:27,183 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-13 15:04:27,183 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-13 15:04:27,184 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-13 15:04:27,184 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-05-13 15:04:27,184 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_15min.joblib, searching for alternatives...
2025-05-13 15:04:27,185 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-13 15:04:27,186 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 15:04:27,186 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 15:04:27,187 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 15:04:27,188 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-13 15:04:27,188 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:04:27,188 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 15:04:27,189 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 15:04:27,190 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-13 15:04:27,191 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 15:04:27,191 - models.predict - INFO - Prediction for 15 minutes horizon: 80.0
2025-05-13 15:04:27,191 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-13 15:04:27,322 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-13 15:04:27,431 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:04:27,432 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-13 15:04:27,432 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-13 15:04:27,982 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-13 15:04:28,934 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-13 15:04:28,936 - models.predict - INFO - Prediction for 30 minutes horizon: 73.08833165168761
2025-05-13 15:04:28,936 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-13 15:04:29,068 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-13 15:04:29,227 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:04:29,229 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-13 15:04:29,229 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-13 15:04:29,229 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-13 15:04:29,229 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-13 15:04:29,230 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-13 15:04:29,230 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-13 15:04:29,231 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-13 15:04:29,231 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-13 15:04:29,232 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-13 15:04:29,233 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 15:04:29,233 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-13 15:04:29,234 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:04:29,235 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-13 15:04:29,235 - models.predict - ERROR - Error in prediction for horizon 60: invalid index to scalar variable.
2025-05-13 15:04:29,236 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-13 15:04:29,237 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 15:04:29,237 - models.predict - INFO - Prediction for 60 minutes horizon: 80.0
2025-05-13 15:04:29,262 - app.utils.memory_management - INFO - Memory before cleanup: 482.40 MB
2025-05-13 15:04:29,471 - app.utils.memory_management - INFO - Garbage collection: collected 10381 objects
2025-05-13 15:04:29,471 - app.utils.memory_management - INFO - Memory after cleanup: 462.09 MB (freed 20.31 MB)
2025-05-13 15:04:49,273 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:04:49,296 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 15:04:49,305 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:04:49,329 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:04:49,330 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:04:49,330 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:04:49,331 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:04:49,332 - app.utils.memory_management - INFO - Memory before cleanup: 465.32 MB
2025-05-13 15:04:49,446 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-13 15:04:49,446 - app.utils.memory_management - INFO - Memory after cleanup: 465.32 MB (freed 0.00 MB)
2025-05-13 15:04:54,119 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:04:54,136 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:04:54,153 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:04:54,154 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:04:54,154 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:04:54,155 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:04:54,281 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 15:04:55,586 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 15:04:55,587 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 15:04:55,612 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.88
2025-05-13 15:04:55,613 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 15:04:55,614 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 15:04:55,615 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 15:05:00,762 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 15:05:02,910 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 15:05:02,918 - scrapers.price_scraper - INFO - Successfully extracted price from header: 4.903
2025-05-13 15:05:02,918 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 4.903
2025-05-13 15:05:02,918 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 15:05:02,919 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 15:05:02,919 - app.utils.error_handling - INFO - fetch_price executed in 7.31 seconds
2025-05-13 15:05:02,920 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 15:05:05,067 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:05:05,070 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:05:05,071 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:05:05,072 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:05:05,073 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: Not implemented in base BitGenerator
2025-05-13 15:05:05,074 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-13 15:05:05,075 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:05:05,075 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:05:05,075 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 15:05:05,091 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:05:05,199 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:05:05,307 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:05:05,307 - models.predict - INFO - Loading bilstm model for COMI with horizon 5
2025-05-13 15:05:05,307 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_5min.keras or saved_models\COMI_bilstm_5min.h5
2025-05-13 15:05:05,307 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_bilstm_5min.keras or saved_models\COMI_bilstm_5min.h5
2025-05-13 15:05:05,307 - models.predict - WARNING - Attempting to create a fallback model for bilstm
2025-05-13 15:05:05,307 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_5min.joblib
2025-05-13 15:05:05,307 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_5min.joblib, searching for alternatives...
2025-05-13 15:05:05,307 - models.sklearn_model - INFO - Found 49 potential model files: ['COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_enhanced_ensemble_scaler25min.joblib', 'COMI_enhanced_ensemble_scaler_25min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler_25min.joblib']
2025-05-13 15:05:05,314 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_25min.joblib
2025-05-13 15:05:05,314 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_25min.joblib
2025-05-13 15:05:05,315 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 15:05:05,315 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-13 15:05:05,315 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:05:05,315 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-13 15:05:05,315 - models.predict - ERROR - Error in prediction for horizon 5: invalid index to scalar variable.
2025-05-13 15:05:05,315 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-13 15:05:05,315 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 15:05:05,315 - models.predict - INFO - Prediction for 5 minutes horizon: 80.0
2025-05-13 15:05:05,315 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-13 15:05:05,430 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-13 15:05:05,542 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:05:05,544 - models.predict - INFO - Loading bilstm model for COMI with horizon 15
2025-05-13 15:05:05,545 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_15min.keras or saved_models\COMI_bilstm_15min.h5
2025-05-13 15:05:05,545 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_bilstm_15min.keras or saved_models\COMI_bilstm_15min.h5
2025-05-13 15:05:05,545 - models.predict - WARNING - Attempting to create a fallback model for bilstm
2025-05-13 15:05:05,546 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-05-13 15:05:05,546 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_15min.joblib, searching for alternatives...
2025-05-13 15:05:05,548 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-13 15:05:05,548 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 15:05:05,548 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 15:05:05,550 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 15:05:05,550 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-13 15:05:05,550 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:05:05,551 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 15:05:05,551 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 15:05:05,551 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-13 15:05:05,552 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 15:05:05,553 - models.predict - INFO - Prediction for 15 minutes horizon: 80.0
2025-05-13 15:05:05,553 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-13 15:05:05,663 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-13 15:05:05,776 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:05:05,778 - models.predict - INFO - Loading bilstm model for COMI with horizon 30
2025-05-13 15:05:05,778 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_30min.keras
2025-05-13 15:05:06,888 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-13 15:05:08,496 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-13 15:05:08,498 - models.predict - INFO - Prediction for 30 minutes horizon: 81.53831295967102
2025-05-13 15:05:08,498 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-13 15:05:08,644 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-13 15:05:08,775 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:05:08,777 - models.predict - INFO - Loading bilstm model for COMI with horizon 60
2025-05-13 15:05:08,777 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_60min.keras or saved_models\COMI_bilstm_60min.h5
2025-05-13 15:05:08,778 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_bilstm_60min.keras or saved_models\COMI_bilstm_60min.h5
2025-05-13 15:05:08,778 - models.predict - WARNING - Attempting to create a fallback model for bilstm
2025-05-13 15:05:08,778 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-13 15:05:08,778 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-13 15:05:08,779 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-13 15:05:08,782 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-13 15:05:08,782 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-13 15:05:08,782 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-13 15:05:08,782 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-13 15:05:08,782 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:05:08,782 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-13 15:05:08,782 - models.predict - ERROR - Error in prediction for horizon 60: invalid index to scalar variable.
2025-05-13 15:05:08,782 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-13 15:05:08,782 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 15:05:08,782 - models.predict - INFO - Prediction for 60 minutes horizon: 80.0
2025-05-13 15:05:08,818 - app.utils.memory_management - INFO - Memory before cleanup: 520.05 MB
2025-05-13 15:05:09,058 - app.utils.memory_management - INFO - Garbage collection: collected 20183 objects
2025-05-13 15:05:09,077 - app.utils.memory_management - INFO - Memory after cleanup: 473.16 MB (freed 46.89 MB)
2025-05-13 15:05:36,777 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:05:36,803 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:05:36,832 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:05:36,833 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:05:36,833 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:05:36,834 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:05:36,835 - app.utils.memory_management - INFO - Memory before cleanup: 476.10 MB
2025-05-13 15:05:36,958 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-13 15:05:36,959 - app.utils.memory_management - INFO - Memory after cleanup: 476.10 MB (freed 0.00 MB)
2025-05-13 15:05:40,882 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:05:40,882 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:05:40,911 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:05:40,912 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:05:40,912 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:05:40,913 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:05:41,031 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 15:05:42,144 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 15:05:42,144 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 15:05:42,152 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.13
2025-05-13 15:05:42,152 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 15:05:42,153 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 15:05:42,153 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 15:05:46,023 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 15:05:48,173 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 15:05:48,187 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 15:05:48,187 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 15:05:48,188 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 15:05:48,188 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 15:05:48,188 - app.utils.error_handling - INFO - fetch_price executed in 6.04 seconds
2025-05-13 15:05:48,189 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 15:05:50,297 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:05:50,299 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:05:50,300 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:05:50,300 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:05:50,301 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: Not implemented in base BitGenerator
2025-05-13 15:05:50,302 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-13 15:05:50,302 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:05:50,303 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:05:50,304 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 15:05:50,324 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:05:50,437 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:05:50,549 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:05:50,551 - models.predict - INFO - Using scikit-learn rf model for 5 minutes horizon
2025-05-13 15:05:50,552 - models.predict - INFO - Loading rf model for COMI with horizon 5
2025-05-13 15:05:50,552 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_5min.joblib
2025-05-13 15:05:50,552 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_5min.joblib
2025-05-13 15:05:50,633 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-05-13 15:05:50,634 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-13 15:05:50,634 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:05:50,642 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-13 15:05:50,643 - models.predict - INFO - Prediction for 5 minutes horizon: 80.23850000000002
2025-05-13 15:05:50,643 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-13 15:05:50,752 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-13 15:05:50,862 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:05:50,864 - models.predict - INFO - Using scikit-learn rf model for 15 minutes horizon
2025-05-13 15:05:50,865 - models.predict - INFO - Loading rf model for COMI with horizon 15
2025-05-13 15:05:50,865 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_15min.joblib
2025-05-13 15:05:50,865 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_15min.joblib, searching for alternatives...
2025-05-13 15:05:50,866 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-13 15:05:50,867 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 15:05:50,867 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-13 15:05:50,868 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-13 15:05:50,868 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-13 15:05:50,869 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:05:50,869 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 15:05:50,869 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-13 15:05:50,870 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-13 15:05:50,870 - models.predict - INFO - Using fallback price due to error: 80.0
2025-05-13 15:05:50,870 - models.predict - INFO - Prediction for 15 minutes horizon: 80.0
2025-05-13 15:05:50,871 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-13 15:05:50,980 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-13 15:05:51,092 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:05:51,094 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-05-13 15:05:51,094 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-05-13 15:05:51,094 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-05-13 15:05:51,095 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-05-13 15:05:51,162 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-13 15:05:51,163 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-13 15:05:51,163 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:05:51,173 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-13 15:05:51,174 - models.predict - INFO - Prediction for 30 minutes horizon: 79.97360000000002
2025-05-13 15:05:51,174 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-13 15:05:51,282 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-13 15:05:51,392 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:05:51,394 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-13 15:05:51,395 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-13 15:05:51,395 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-13 15:05:51,395 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-13 15:05:51,397 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-13 15:05:51,398 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-13 15:05:51,398 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-13 15:05:51,484 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-13 15:05:51,485 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-13 15:05:51,485 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-13 15:05:51,494 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-13 15:05:51,494 - models.predict - INFO - Prediction for 60 minutes horizon: 79.91710000000002
2025-05-13 15:05:51,518 - app.utils.memory_management - INFO - Memory before cleanup: 481.70 MB
2025-05-13 15:05:51,618 - app.utils.memory_management - INFO - Garbage collection: collected 426 objects
2025-05-13 15:05:51,618 - app.utils.memory_management - INFO - Memory after cleanup: 481.70 MB (freed 0.00 MB)
2025-05-13 15:06:15,143 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:06:15,163 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:06:15,188 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:06:15,189 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:06:15,189 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:06:15,190 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:06:15,192 - app.utils.memory_management - INFO - Memory before cleanup: 481.65 MB
2025-05-13 15:06:15,319 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-13 15:06:15,320 - app.utils.memory_management - INFO - Memory after cleanup: 481.65 MB (freed 0.00 MB)
2025-05-13 15:07:23,688 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:07:23,712 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:07:23,736 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:07:23,737 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:07:23,737 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:07:23,738 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:07:23,741 - app.utils.memory_management - INFO - Memory before cleanup: 481.66 MB
2025-05-13 15:07:23,862 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:07:23,862 - app.utils.memory_management - INFO - Memory after cleanup: 481.66 MB (freed 0.00 MB)
2025-05-13 15:07:25,715 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:07:25,727 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:07:25,745 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:07:25,746 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:07:25,746 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:07:25,746 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:07:25,865 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 15:07:27,064 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 15:07:27,065 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 15:07:27,080 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.17
2025-05-13 15:07:27,081 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 15:07:27,081 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 15:07:27,082 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 15:07:37,825 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 15:07:39,961 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 15:07:39,961 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 15:07:39,961 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 15:07:39,961 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 15:07:39,961 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 15:07:39,961 - app.utils.error_handling - INFO - fetch_price executed in 12.88 seconds
2025-05-13 15:07:39,961 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 15:07:42,130 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:07:42,131 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:07:42,131 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:07:42,132 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:07:42,132 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: Not implemented in base BitGenerator
2025-05-13 15:07:42,133 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-13 15:07:42,133 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:07:42,134 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:07:42,134 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 15:07:42,154 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:07:42,273 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:07:42,377 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:07:42,377 - models.predict - INFO - Using scikit-learn gb model for 5 minutes horizon
2025-05-13 15:07:42,377 - models.predict - INFO - Loading gb model for COMI with horizon 5
2025-05-13 15:07:42,377 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_5min.joblib
2025-05-13 15:07:42,377 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_5min.joblib
2025-05-13 15:07:42,377 - models.predict - ERROR - Error making predictions: <class 'app.utils.numpy_fix.fix_numpy_imports.<locals>.FixedMT19937'> is not a known BitGenerator module.
2025-05-13 15:07:42,377 - app.components.tradingview_predictions - ERROR - BitGenerator error in prediction: <class 'app.utils.numpy_fix.fix_numpy_imports.<locals>.FixedMT19937'> is not a known BitGenerator module.
2025-05-13 15:07:42,377 - app.components.tradingview_predictions - INFO - Applied BitGenerator fix, trying prediction again
2025-05-13 15:07:42,408 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:07:42,516 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:07:42,624 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:07:42,626 - models.predict - INFO - Using scikit-learn gb model for 5 minutes horizon
2025-05-13 15:07:42,626 - models.predict - INFO - Loading gb model for COMI with horizon 5
2025-05-13 15:07:42,627 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_5min.joblib
2025-05-13 15:07:42,627 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_5min.joblib
2025-05-13 15:07:42,628 - models.predict - ERROR - Error making predictions: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:07:42,628 - app.components.tradingview_predictions - ERROR - Still failed after BitGenerator fix: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:07:42,628 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-13 15:07:42,629 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 80.0
2025-05-13 15:07:42,629 - app.components.tradingview_predictions - INFO - Fallback prediction for 5 minutes: 80.00511040041877
2025-05-13 15:07:42,629 - app.components.tradingview_predictions - INFO - Fallback prediction for 15 minutes: 80.00633167454934
2025-05-13 15:07:42,630 - app.components.tradingview_predictions - INFO - Fallback prediction for 30 minutes: 80.03180810285062
2025-05-13 15:07:42,630 - app.components.tradingview_predictions - INFO - Fallback prediction for 60 minutes: 79.99321198013666
2025-05-13 15:07:42,655 - app.utils.memory_management - INFO - Memory before cleanup: 481.71 MB
2025-05-13 15:07:42,761 - app.utils.memory_management - INFO - Garbage collection: collected 529 objects
2025-05-13 15:07:42,762 - app.utils.memory_management - INFO - Memory after cleanup: 481.71 MB (freed 0.00 MB)
2025-05-13 15:08:14,885 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:08:14,907 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:08:14,930 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:08:14,930 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:08:14,930 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:08:14,930 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:08:14,930 - app.utils.memory_management - INFO - Memory before cleanup: 481.66 MB
2025-05-13 15:08:15,062 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-05-13 15:08:15,063 - app.utils.memory_management - INFO - Memory after cleanup: 481.66 MB (freed 0.00 MB)
2025-05-13 15:08:22,961 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:08:22,976 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:08:22,993 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:08:22,993 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:08:22,993 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:08:22,993 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:08:22,993 - app.utils.memory_management - INFO - Memory before cleanup: 481.66 MB
2025-05-13 15:08:23,133 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:08:23,134 - app.utils.memory_management - INFO - Memory after cleanup: 481.66 MB (freed 0.00 MB)
2025-05-13 15:08:52,402 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:08:52,415 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:08:52,430 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:08:52,431 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:08:52,431 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:08:52,431 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:08:52,433 - app.utils.memory_management - INFO - Memory before cleanup: 481.65 MB
2025-05-13 15:08:52,578 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:08:52,578 - app.utils.memory_management - INFO - Memory after cleanup: 481.65 MB (freed 0.00 MB)
2025-05-13 15:08:56,255 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:08:56,264 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:08:56,281 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:08:56,282 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:08:56,282 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:08:56,283 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:08:56,285 - app.utils.memory_management - INFO - Memory before cleanup: 481.66 MB
2025-05-13 15:08:56,409 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:08:56,409 - app.utils.memory_management - INFO - Memory after cleanup: 481.66 MB (freed 0.00 MB)
2025-05-13 15:09:05,580 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:09:05,593 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:09:05,608 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:09:05,609 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:09:05,609 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:09:05,610 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:09:05,611 - app.utils.memory_management - INFO - Memory before cleanup: 481.66 MB
2025-05-13 15:09:05,726 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:09:05,726 - app.utils.memory_management - INFO - Memory after cleanup: 481.66 MB (freed 0.00 MB)
2025-05-13 15:11:54,289 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:11:54,314 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 15:11:54,323 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:11:54,347 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:11:54,348 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:11:54,348 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:11:54,349 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:11:54,350 - app.utils.memory_management - INFO - Memory before cleanup: 481.70 MB
2025-05-13 15:11:54,489 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:11:54,489 - app.utils.memory_management - INFO - Memory after cleanup: 481.70 MB (freed 0.00 MB)
2025-05-13 15:14:32,247 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 15:14:33,163 - app - INFO - Memory management utilities loaded
2025-05-13 15:14:33,163 - app - INFO - Error handling utilities loaded
2025-05-13 15:14:33,163 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-13 15:14:33,163 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-13 15:14:33,163 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-13 15:14:33,163 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-13 15:14:33,163 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-13 15:14:33,163 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-13 15:14:33,163 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-13 15:14:33,163 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:14:33,163 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 15:14:33,163 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-13 15:14:33,163 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-13 15:14:33,163 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:14:33,163 - app - INFO - Applied NumPy fix
2025-05-13 15:14:33,171 - app.config - INFO - Configuration initialized
2025-05-13 15:14:36,115 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 15:14:36,135 - models.train - INFO - TensorFlow test successful
2025-05-13 15:14:36,712 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 15:14:36,713 - models.train - INFO - Transformer model is available
2025-05-13 15:14:36,713 - models.train - INFO - Using TensorFlow-based models
2025-05-13 15:14:36,714 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 15:14:36,714 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 15:14:36,714 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:14:36,918 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:14:37,174 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 15:14:37,313 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 15:14:37,328 - app.utils.session_state - INFO - Initializing session state
2025-05-13 15:14:37,329 - app.utils.session_state - INFO - Session state initialized
2025-05-13 15:14:37,334 - app.utils.session_state - INFO - Initializing session state
2025-05-13 15:14:37,336 - app.utils.session_state - INFO - Session state initialized
2025-05-13 15:14:37,342 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 15:14:37,348 - app.utils.memory_management - INFO - Memory before cleanup: 413.98 MB
2025-05-13 15:14:37,469 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 15:14:37,475 - app.utils.memory_management - INFO - Memory after cleanup: 414.00 MB (freed -0.02 MB)
2025-05-13 15:14:39,285 - app.utils.memory_management - INFO - Memory before cleanup: 418.86 MB
2025-05-13 15:14:39,417 - app.utils.memory_management - INFO - Garbage collection: collected 108 objects
2025-05-13 15:14:39,417 - app.utils.memory_management - INFO - Memory after cleanup: 418.86 MB (freed -0.00 MB)
2025-05-13 15:14:40,321 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:14:40,333 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:14:40,359 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-13 15:14:40,360 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:14:40,360 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:14:40,360 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:14:40,372 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:14:40,373 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:14:40,373 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:14:40,373 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:14:40,376 - app.utils.memory_management - INFO - Memory before cleanup: 422.56 MB
2025-05-13 15:14:40,491 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-13 15:14:40,491 - app.utils.memory_management - INFO - Memory after cleanup: 422.60 MB (freed -0.04 MB)
2025-05-13 15:14:48,601 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:14:48,615 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:14:48,631 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:14:48,633 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:14:48,634 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:14:48,635 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:14:48,636 - app.utils.memory_management - INFO - Memory before cleanup: 422.64 MB
2025-05-13 15:14:48,750 - app.utils.memory_management - INFO - Garbage collection: collected 196 objects
2025-05-13 15:14:48,750 - app.utils.memory_management - INFO - Memory after cleanup: 422.64 MB (freed 0.00 MB)
2025-05-13 15:14:49,489 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:14:49,507 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:14:49,523 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:14:49,524 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:14:49,524 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:14:49,525 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:14:49,528 - app.utils.memory_management - INFO - Memory before cleanup: 422.66 MB
2025-05-13 15:14:49,634 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:14:49,634 - app.utils.memory_management - INFO - Memory after cleanup: 422.66 MB (freed 0.00 MB)
2025-05-13 15:14:50,796 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:14:50,809 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:14:50,826 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:14:50,827 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:14:50,827 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:14:50,827 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:14:50,831 - app.utils.memory_management - INFO - Memory before cleanup: 422.66 MB
2025-05-13 15:14:50,943 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:14:50,943 - app.utils.memory_management - INFO - Memory after cleanup: 422.66 MB (freed 0.00 MB)
2025-05-13 15:14:52,153 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:14:52,167 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:14:52,185 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:14:52,186 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:14:52,188 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:14:52,188 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:14:52,188 - app.utils.memory_management - INFO - Memory before cleanup: 422.66 MB
2025-05-13 15:14:52,300 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:14:52,300 - app.utils.memory_management - INFO - Memory after cleanup: 422.66 MB (freed 0.00 MB)
2025-05-13 15:14:54,706 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:14:54,719 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:14:54,737 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:14:54,737 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:14:54,737 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:14:54,738 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:14:54,853 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 15:14:56,043 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 15:14:56,043 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 15:14:56,051 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.64
2025-05-13 15:14:56,098 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 15:14:56,098 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 15:14:56,098 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 15:15:00,174 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 15:15:02,286 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 15:15:02,295 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 15:15:02,296 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 15:15:02,296 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 15:15:02,296 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 15:15:02,296 - app.utils.error_handling - INFO - fetch_price executed in 6.20 seconds
2025-05-13 15:15:02,297 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 15:15:04,399 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:15:04,400 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:15:04,400 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:15:04,400 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:15:04,401 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-13 15:15:04,401 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:15:04,401 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:15:04,401 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 15:15:04,418 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:15:04,520 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:15:04,619 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:15:04,620 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 15:15:04,633 - models.hybrid_model - INFO - XGBoost is available
2025-05-13 15:15:04,633 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-13 15:15:04,633 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 15:15:04,633 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:15:04,633 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:15:04,671 - models.predict - ERROR - Error making predictions: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-13 15:15:04,671 - app.components.tradingview_predictions - ERROR - BitGenerator error in prediction: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-13 15:15:04,672 - app.components.tradingview_predictions - INFO - Applied BitGenerator fix, trying prediction again
2025-05-13 15:15:04,690 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:15:04,792 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:15:04,903 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:15:04,905 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 15:15:04,905 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 15:15:04,906 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:15:04,906 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:15:04,938 - models.predict - ERROR - Error making predictions: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:15:04,938 - app.components.tradingview_predictions - ERROR - Still failed after BitGenerator fix: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:15:04,938 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-13 15:15:04,939 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 80.0
2025-05-13 15:15:04,939 - app.components.tradingview_predictions - INFO - Fallback prediction for 5 minutes: 80.00013098794632
2025-05-13 15:15:04,939 - app.components.tradingview_predictions - INFO - Fallback prediction for 15 minutes: 80.00994036962899
2025-05-13 15:15:04,939 - app.components.tradingview_predictions - INFO - Fallback prediction for 30 minutes: 80.0223324458525
2025-05-13 15:15:04,939 - app.components.tradingview_predictions - INFO - Fallback prediction for 60 minutes: 80.06307536101659
2025-05-13 15:15:05,021 - app.utils.memory_management - INFO - Memory before cleanup: 425.76 MB
2025-05-13 15:15:05,143 - app.utils.memory_management - INFO - Garbage collection: collected 366 objects
2025-05-13 15:15:05,144 - app.utils.memory_management - INFO - Memory after cleanup: 425.76 MB (freed 0.00 MB)
2025-05-13 15:15:27,071 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:15:27,101 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:15:27,128 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:15:27,136 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:15:27,136 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:15:27,137 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:15:27,139 - app.utils.memory_management - INFO - Memory before cleanup: 428.67 MB
2025-05-13 15:15:27,295 - app.utils.memory_management - INFO - Garbage collection: collected 217 objects
2025-05-13 15:15:27,296 - app.utils.memory_management - INFO - Memory after cleanup: 428.67 MB (freed 0.00 MB)
2025-05-13 15:16:06,945 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-13 15:16:06,948 - app - INFO - Memory management utilities loaded
2025-05-13 15:16:06,949 - app - INFO - Error handling utilities loaded
2025-05-13 15:16:06,950 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:16:06,950 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:16:06,950 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:16:06,951 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:16:06,951 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: 'MockMT19937' object has no attribute 'capsule'
2025-05-13 15:16:06,951 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-13 15:16:06,951 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:16:06,951 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:16:06,952 - app - INFO - Applied NumPy fix
2025-05-13 15:16:06,953 - app.config - INFO - Configuration initialized
2025-05-13 15:16:06,956 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-13 15:16:06,957 - models.train - INFO - TensorFlow test successful
2025-05-13 15:16:06,958 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-13 15:16:06,959 - models.train - INFO - Transformer model is available
2025-05-13 15:16:06,959 - models.train - INFO - Using TensorFlow-based models
2025-05-13 15:16:06,961 - models.predict - INFO - Transformer model is available for predictions
2025-05-13 15:16:06,961 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-13 15:16:06,961 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:16:06,988 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-13 15:16:06,994 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-13 15:16:07,003 - app.utils.session_state - INFO - Initializing session state
2025-05-13 15:16:07,004 - app.utils.session_state - INFO - Session state initialized
2025-05-13 15:16:07,011 - app.utils.memory_management - INFO - Memory before cleanup: 483.61 MB
2025-05-13 15:16:07,132 - app.utils.memory_management - INFO - Garbage collection: collected 270 objects
2025-05-13 15:16:07,132 - app.utils.memory_management - INFO - Memory after cleanup: 483.61 MB (freed 0.00 MB)
2025-05-13 15:16:10,779 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:16:10,789 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:16:10,805 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:16:10,805 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:16:10,806 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:16:10,807 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:16:10,814 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:16:10,814 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:16:10,814 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:16:10,814 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:16:10,814 - app.utils.memory_management - INFO - Memory before cleanup: 483.82 MB
2025-05-13 15:16:10,933 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-13 15:16:10,933 - app.utils.memory_management - INFO - Memory after cleanup: 483.82 MB (freed 0.00 MB)
2025-05-13 15:16:33,620 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:16:33,634 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:16:33,650 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:16:33,650 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:16:33,651 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:16:33,651 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:16:33,653 - app.utils.memory_management - INFO - Memory before cleanup: 483.81 MB
2025-05-13 15:16:33,773 - app.utils.memory_management - INFO - Garbage collection: collected 196 objects
2025-05-13 15:16:33,773 - app.utils.memory_management - INFO - Memory after cleanup: 483.81 MB (freed 0.00 MB)
2025-05-13 15:16:34,531 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:16:34,544 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:16:34,561 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:16:34,562 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:16:34,562 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:16:34,563 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:16:34,565 - app.utils.memory_management - INFO - Memory before cleanup: 483.82 MB
2025-05-13 15:16:34,681 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:16:34,681 - app.utils.memory_management - INFO - Memory after cleanup: 483.82 MB (freed 0.00 MB)
2025-05-13 15:16:36,026 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:16:36,036 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:16:36,053 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:16:36,054 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:16:36,054 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:16:36,054 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:16:36,056 - app.utils.memory_management - INFO - Memory before cleanup: 483.82 MB
2025-05-13 15:16:36,164 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:16:36,164 - app.utils.memory_management - INFO - Memory after cleanup: 483.82 MB (freed 0.00 MB)
2025-05-13 15:16:37,498 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:16:37,513 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:16:37,530 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:16:37,532 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:16:37,532 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:16:37,532 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:16:37,532 - app.utils.memory_management - INFO - Memory before cleanup: 483.81 MB
2025-05-13 15:16:37,631 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:16:37,631 - app.utils.memory_management - INFO - Memory after cleanup: 483.81 MB (freed 0.00 MB)
2025-05-13 15:16:39,815 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:16:39,815 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:16:39,845 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:16:39,846 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:16:39,846 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:16:39,847 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:16:39,973 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-13 15:16:41,057 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-13 15:16:41,058 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-13 15:16:41,069 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 79.85
2025-05-13 15:16:41,069 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-13 15:16:41,069 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-13 15:16:41,070 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-13 15:16:44,763 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-13 15:16:46,890 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-13 15:16:46,899 - scrapers.price_scraper - INFO - Successfully extracted price from header: 80.0
2025-05-13 15:16:46,899 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 80.0
2025-05-13 15:16:46,899 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-13 15:16:46,899 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-13 15:16:46,899 - app.utils.error_handling - INFO - fetch_price executed in 5.83 seconds
2025-05-13 15:16:46,900 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-13 15:16:48,997 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-13 15:16:48,998 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-13 15:16:48,998 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-13 15:16:48,998 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-13 15:16:48,999 - app.utils.numpy_fix - WARNING - MT19937 exists but is not properly registered: Not implemented in base BitGenerator
2025-05-13 15:16:48,999 - app.utils.numpy_fix - INFO - Successfully fixed MT19937 BitGenerator registration
2025-05-13 15:16:48,999 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-13 15:16:49,000 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-13 15:16:49,000 - app.components.tradingview_predictions - INFO - Applied NumPy fix before generating predictions
2025-05-13 15:16:49,021 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:16:49,130 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:16:49,246 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:16:49,248 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 15:16:49,248 - models.hybrid_model - INFO - XGBoost is available
2025-05-13 15:16:49,248 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-13 15:16:49,248 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 15:16:49,248 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:16:49,248 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:16:49,280 - models.predict - ERROR - Error making predictions: <class 'app.utils.numpy_fix.fix_numpy_imports.<locals>.FixedMT19937'> is not a known BitGenerator module.
2025-05-13 15:16:49,280 - app.components.tradingview_predictions - ERROR - BitGenerator error in prediction: <class 'app.utils.numpy_fix.fix_numpy_imports.<locals>.FixedMT19937'> is not a known BitGenerator module.
2025-05-13 15:16:49,280 - app.components.tradingview_predictions - INFO - Applied BitGenerator fix, trying prediction again
2025-05-13 15:16:49,297 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-13 15:16:49,403 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-13 15:16:49,517 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-13 15:16:49,519 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-13 15:16:49,519 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-13 15:16:49,519 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:16:49,520 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-13 15:16:49,551 - models.predict - ERROR - Error making predictions: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:16:49,552 - app.components.tradingview_predictions - ERROR - Still failed after BitGenerator fix: <class 'app.components.tradingview_predictions.generate_predictions.<locals>.MockMT19937'> is not a known BitGenerator module.
2025-05-13 15:16:49,552 - app.components.tradingview_predictions - WARNING - Creating fallback predictions
2025-05-13 15:16:49,552 - app.components.tradingview_predictions - INFO - Creating fallback predictions from current price: 80.0
2025-05-13 15:16:49,552 - app.components.tradingview_predictions - INFO - Fallback prediction for 5 minutes: 80.00501207995457
2025-05-13 15:16:49,553 - app.components.tradingview_predictions - INFO - Fallback prediction for 15 minutes: 79.98858856056644
2025-05-13 15:16:49,553 - app.components.tradingview_predictions - INFO - Fallback prediction for 30 minutes: 79.99223183619804
2025-05-13 15:16:49,553 - app.components.tradingview_predictions - INFO - Fallback prediction for 60 minutes: 80.04452758765551
2025-05-13 15:16:49,577 - app.utils.memory_management - INFO - Memory before cleanup: 472.06 MB
2025-05-13 15:16:49,680 - app.utils.memory_management - INFO - Garbage collection: collected 423 objects
2025-05-13 15:16:49,680 - app.utils.memory_management - INFO - Memory after cleanup: 472.06 MB (freed 0.00 MB)
2025-05-13 15:16:57,784 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:16:57,802 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 15:16:57,806 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:16:57,824 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:16:57,824 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:16:57,825 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:16:57,825 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:16:57,827 - app.utils.memory_management - INFO - Memory before cleanup: 475.72 MB
2025-05-13 15:16:57,940 - app.utils.memory_management - INFO - Garbage collection: collected 217 objects
2025-05-13 15:16:57,940 - app.utils.memory_management - INFO - Memory after cleanup: 475.72 MB (freed 0.00 MB)
2025-05-13 15:17:29,057 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:17:29,083 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:17:29,103 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:17:29,103 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:17:29,103 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:17:29,103 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:17:29,103 - app.utils.memory_management - INFO - Memory before cleanup: 475.71 MB
2025-05-13 15:17:29,240 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-05-13 15:17:29,240 - app.utils.memory_management - INFO - Memory after cleanup: 475.71 MB (freed 0.00 MB)
2025-05-13 15:17:33,350 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:17:33,359 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:17:33,376 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:17:33,377 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:17:33,377 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:17:33,379 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:17:33,380 - app.utils.memory_management - INFO - Memory before cleanup: 475.71 MB
2025-05-13 15:17:33,513 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:17:33,513 - app.utils.memory_management - INFO - Memory after cleanup: 475.64 MB (freed 0.07 MB)
2025-05-13 15:17:34,920 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:17:34,931 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:17:34,946 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:17:34,947 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:17:34,947 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:17:34,948 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:17:34,950 - app.utils.memory_management - INFO - Memory before cleanup: 475.64 MB
2025-05-13 15:17:35,081 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:17:35,082 - app.utils.memory_management - INFO - Memory after cleanup: 475.64 MB (freed 0.00 MB)
2025-05-13 15:18:34,521 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:18:34,544 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:18:34,566 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:18:34,568 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:18:34,569 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:18:34,570 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:18:34,573 - app.utils.memory_management - INFO - Memory before cleanup: 475.64 MB
2025-05-13 15:18:34,715 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:18:34,716 - app.utils.memory_management - INFO - Memory after cleanup: 475.64 MB (freed 0.00 MB)
2025-05-13 15:19:31,864 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:19:31,888 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:19:31,911 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:19:31,912 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:19:31,912 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:19:31,914 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:19:31,916 - app.utils.memory_management - INFO - Memory before cleanup: 475.64 MB
2025-05-13 15:19:32,056 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:19:32,060 - app.utils.memory_management - INFO - Memory after cleanup: 475.64 MB (freed 0.00 MB)
2025-05-13 15:19:36,481 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:19:36,490 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:19:36,506 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:19:36,508 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:19:36,509 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:19:36,509 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:19:36,509 - app.utils.memory_management - INFO - Memory before cleanup: 475.64 MB
2025-05-13 15:19:36,646 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:19:36,647 - app.utils.memory_management - INFO - Memory after cleanup: 475.64 MB (freed 0.00 MB)
2025-05-13 15:19:39,166 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:19:39,179 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:19:39,193 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:19:39,195 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:19:39,195 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:19:39,196 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:19:39,197 - app.utils.memory_management - INFO - Memory before cleanup: 475.64 MB
2025-05-13 15:19:39,309 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:19:39,309 - app.utils.memory_management - INFO - Memory after cleanup: 475.64 MB (freed 0.00 MB)
2025-05-13 15:20:10,362 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:20:10,383 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:20:10,410 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:20:10,411 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:20:10,411 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:20:10,411 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:20:10,413 - app.utils.memory_management - INFO - Memory before cleanup: 475.64 MB
2025-05-13 15:20:10,545 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:20:10,546 - app.utils.memory_management - INFO - Memory after cleanup: 475.64 MB (freed 0.00 MB)
2025-05-13 15:22:44,716 - app - INFO - Using TensorFlow-based LSTM model
2025-05-13 15:22:44,741 - app - INFO - Found 8 stock files in data/stocks
2025-05-13 15:22:44,750 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-13 15:22:44,772 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-13 15:22:44,773 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-13
2025-05-13 15:22:44,773 - app.utils.common - INFO - Data shape: (569, 38)
2025-05-13 15:22:44,774 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-13 15:22:44,776 - app.utils.memory_management - INFO - Memory before cleanup: 475.64 MB
2025-05-13 15:22:44,905 - app.utils.memory_management - INFO - Garbage collection: collected 195 objects
2025-05-13 15:22:44,905 - app.utils.memory_management - INFO - Memory after cleanup: 475.64 MB (freed 0.00 MB)
2025-05-13 15:32:48,805 - app - INFO - Cleaning up resources...
2025-05-13 15:32:48,806 - app.utils.memory_management - INFO - Memory before cleanup: 475.38 MB
2025-05-13 15:32:48,923 - app.utils.memory_management - INFO - Garbage collection: collected 577 objects
2025-05-13 15:32:48,923 - app.utils.memory_management - INFO - Memory after cleanup: 475.38 MB (freed 0.00 MB)
2025-05-13 15:32:48,923 - app - INFO - Application shutdown complete
2025-05-13 15:32:48,924 - app - INFO - Cleaning up resources...
2025-05-13 15:32:48,924 - app.utils.memory_management - INFO - Memory before cleanup: 475.38 MB
2025-05-13 15:32:49,034 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 15:32:49,034 - app.utils.memory_management - INFO - Memory after cleanup: 475.38 MB (freed 0.00 MB)
2025-05-13 15:32:49,034 - app - INFO - Application shutdown complete
2025-05-13 15:32:49,035 - app - INFO - Cleaning up resources...
2025-05-13 15:32:49,035 - app.utils.memory_management - INFO - Memory before cleanup: 475.38 MB
2025-05-13 15:32:49,144 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 15:32:49,145 - app.utils.memory_management - INFO - Memory after cleanup: 475.38 MB (freed 0.00 MB)
2025-05-13 15:32:49,145 - app - INFO - Application shutdown complete
2025-05-13 15:32:49,145 - app - INFO - Cleaning up resources...
2025-05-13 15:32:49,145 - app.utils.memory_management - INFO - Memory before cleanup: 475.38 MB
2025-05-13 15:32:49,255 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-13 15:32:49,255 - app.utils.memory_management - INFO - Memory after cleanup: 475.38 MB (freed 0.00 MB)
2025-05-13 15:32:49,256 - app - INFO - Application shutdown complete
2025-05-13 15:32:49,256 - app - INFO - Cleaning up resources...
2025-05-13 15:32:49,256 - app.utils.memory_management - INFO - Memory before cleanup: 475.38 MB
2025-05-13 15:32:49,366 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-05-13 15:32:49,367 - app.utils.memory_management - INFO - Memory after cleanup: 475.38 MB (freed 0.00 MB)
2025-05-13 15:32:49,367 - app - INFO - Application shutdown complete
