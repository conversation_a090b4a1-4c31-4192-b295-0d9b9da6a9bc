2025-06-28 23:23:54,764 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-28 23:23:57,328 - app - INFO - Memory management utilities loaded
2025-06-28 23:23:57,332 - app - INFO - Error handling utilities loaded
2025-06-28 23:23:57,336 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-28 23:23:57,339 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-28 23:23:57,341 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-28 23:23:57,341 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-28 23:23:57,351 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-28 23:23:57,363 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-28 23:23:57,364 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-28 23:23:57,365 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-28 23:23:57,365 - app - INFO - Applied NumPy fix
2025-06-28 23:23:57,366 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-28 23:23:57,367 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-28 23:23:57,368 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-28 23:23:57,369 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-28 23:23:57,370 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-28 23:23:57,372 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-28 23:23:57,373 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-28 23:23:57,374 - app - INFO - Applied NumPy BitGenerator fix
2025-06-28 23:24:14,728 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-28 23:24:14,728 - app - INFO - Applied TensorFlow fix
2025-06-28 23:24:14,736 - app.config - INFO - Configuration initialized
2025-06-28 23:24:14,745 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-28 23:24:15,045 - models.train - INFO - TensorFlow test successful
2025-06-28 23:24:19,684 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-28 23:24:19,684 - models.train - INFO - Transformer model is available
2025-06-28 23:24:19,684 - models.train - INFO - Using TensorFlow-based models
2025-06-28 23:24:19,694 - models.predict - INFO - Transformer model is available for predictions
2025-06-28 23:24:19,694 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-28 23:24:19,700 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-28 23:24:21,522 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-28 23:24:21,523 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-28 23:24:21,523 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-28 23:24:21,523 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-28 23:24:21,523 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-28 23:24:21,523 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-28 23:24:21,524 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-28 23:24:21,524 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-28 23:24:21,524 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-28 23:24:21,524 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-28 23:24:21,875 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-28 23:24:21,879 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:24:22,421 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-28 23:24:25,072 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-28 23:24:25,349 - app.utils.session_state - INFO - Initializing session state
2025-06-28 23:24:25,351 - app.utils.session_state - INFO - Session state initialized
2025-06-28 23:24:26,588 - app - INFO - Found 14 stock files in data/stocks
2025-06-28 23:24:26,604 - app.utils.memory_management - INFO - Memory before cleanup: 429.34 MB
2025-06-28 23:24:26,781 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-28 23:24:26,781 - app.utils.memory_management - INFO - Memory after cleanup: 429.34 MB (freed -0.00 MB)
2025-06-28 23:24:54,108 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:24:54,140 - app.utils.memory_management - INFO - Memory before cleanup: 433.20 MB
2025-06-28 23:24:54,384 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-28 23:24:54,411 - app.utils.memory_management - INFO - Memory after cleanup: 433.20 MB (freed 0.00 MB)
2025-06-28 23:24:55,422 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:24:55,565 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.08 seconds
2025-06-28 23:24:55,567 - app - INFO - Date range: 2022-08-09 to 2025-06-23
2025-06-28 23:24:55,568 - app - INFO - Data shape: (750, 36)
2025-06-28 23:24:55,569 - app - INFO - File COMI contains 2025 data
2025-06-28 23:24:55,609 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-28 23:24:55,609 - app - INFO - Features shape: (750, 36)
2025-06-28 23:24:55,631 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-28 23:24:55,632 - app - INFO - Date range: 2022-08-09 to 2025-06-23
2025-06-28 23:24:55,633 - app - INFO - Data shape: (750, 36)
2025-06-28 23:24:55,633 - app - INFO - File COMI contains 2025 data
2025-06-28 23:24:55,636 - app.utils.memory_management - INFO - Memory before cleanup: 437.84 MB
2025-06-28 23:24:55,799 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-28 23:24:55,799 - app.utils.memory_management - INFO - Memory after cleanup: 437.88 MB (freed -0.04 MB)
2025-06-28 23:24:55,991 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:24:56,079 - app.utils.memory_management - INFO - Memory before cleanup: 438.91 MB
2025-06-28 23:24:56,270 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-06-28 23:24:56,271 - app.utils.memory_management - INFO - Memory after cleanup: 438.89 MB (freed 0.02 MB)
2025-06-28 23:25:17,301 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:25:17,329 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-28 23:25:17,342 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-28 23:25:17,481 - app.utils.memory_management - INFO - Memory before cleanup: 439.43 MB
2025-06-28 23:25:17,705 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-28 23:25:17,708 - app.utils.memory_management - INFO - Memory after cleanup: 439.43 MB (freed 0.00 MB)
2025-06-28 23:25:24,966 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:25:24,991 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-28 23:25:25,009 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-28 23:25:25,122 - app.utils.memory_management - INFO - Memory before cleanup: 439.60 MB
2025-06-28 23:25:25,340 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-06-28 23:25:25,341 - app.utils.memory_management - INFO - Memory after cleanup: 439.60 MB (freed 0.00 MB)
2025-06-28 23:25:34,463 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:25:34,492 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-28 23:25:34,500 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-28 23:25:34,539 - app.utils.historical_data_downloader - INFO - Starting historical data generation for COMI (3 years)
2025-06-28 23:25:34,539 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-28 23:25:34,545 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-28 23:25:34,545 - app.utils.historical_data_downloader - INFO - Fetching current live data for COMI...
2025-06-28 23:25:34,546 - app.utils.historical_data_downloader - INFO - Downloading historical data for COMI with intervals: ['1D', '1W']
2025-06-28 23:25:34,546 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-28 23:25:34,546 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-COMI'], 'intervals': ['1D', '1W']}
2025-06-28 23:25:52,150 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-06-28 23:25:52,150 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-COMI': [{'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 75176.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': 4235.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 72734.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 80978.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 80265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80665.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 80770.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80474.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 80570.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 80130.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 79914.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 79761.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 78963.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 79222.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 80326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80465.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 80773.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 82066.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 58540.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': -290.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 68206.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 81010.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 81017.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80410.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 79757.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80157.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 79621.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 79156.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 80980.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 73509.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 76174.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 62422.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 56579.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78450.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 79830.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 81417.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]}, 'message': 'Successfully scraped 1 pairs'}
2025-06-28 23:25:52,152 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-COMI']
2025-06-28 23:25:52,152 - app.utils.historical_data_downloader - INFO - Found data for EGX-COMI: [{'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 75176.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': 4235.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 72734.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 80978.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 80265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80665.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 80770.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80474.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 80570.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 80130.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 79914.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 79761.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 78963.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 79222.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 80326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80465.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 80773.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 82066.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 58540.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': -290.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 68206.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 81010.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 81017.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80410.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 79757.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80157.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 79621.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 79156.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 80980.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 73509.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 76174.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 62422.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 56579.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78450.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 79830.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 81417.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-06-28 23:25:52,152 - app.utils.historical_data_downloader - INFO - Processing 2 data points for COMI
2025-06-28 23:25:52,152 - app.utils.historical_data_downloader - INFO - Raw data structure: [{'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 75176.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': 4235.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 72734.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 80978.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 80265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80665.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 80770.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80474.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 80570.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 80130.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 79914.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 79761.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 78963.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 79222.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 80326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80465.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 80773.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 82066.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 58540.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': -290.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 68206.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 81010.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 81017.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80410.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 79757.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80157.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 79621.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 79156.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 80980.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 73509.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 76174.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 62422.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 56579.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78450.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 79830.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 81417.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-06-28 23:25:52,152 - app.utils.historical_data_downloader - INFO - First item structure: {'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 75176.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': 4235.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 72734.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 80978.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 80265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80665.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 80770.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80474.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 80570.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 80130.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 79914.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 79761.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 78963.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 79222.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 80326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80465.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 80773.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 82066.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}
2025-06-28 23:25:52,155 - app.utils.historical_data_downloader - INFO - Processing interval 1D: pair=EGX-COMI, price=83900.0
2025-06-28 23:25:52,155 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 83900.0 -> 83.9 EGP
2025-06-28 23:25:52,155 - app.utils.historical_data_downloader - INFO - Successfully processed 1D: price=83.9 EGP
2025-06-28 23:25:52,155 - app.utils.historical_data_downloader - INFO - Processing interval 1W: pair=EGX-COMI, price=83900.0
2025-06-28 23:25:52,155 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 83900.0 -> 83.9 EGP
2025-06-28 23:25:52,155 - app.utils.historical_data_downloader - INFO - Successfully processed 1W: price=83.9 EGP
2025-06-28 23:25:52,158 - app.utils.historical_data_downloader - INFO - Successfully processed 2 intervals for COMI
2025-06-28 23:25:52,158 - app.utils.historical_data_downloader - INFO - Successfully processed historical data for COMI
2025-06-28 23:25:52,158 - app.utils.historical_data_downloader - INFO - Successfully retrieved current data for COMI: {'1D': {'symbol': 'COMI', 'interval': '1D', 'current_price': 83.9, 'timestamp': datetime.datetime(2025, 6, 28, 23, 25, 52, 155613), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 75176.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': 4235.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 72734.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 80978.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 80265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80665.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 80770.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80474.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 80570.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 80130.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 79914.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 79761.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 78963.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 79222.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 80326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80465.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 80773.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 82066.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 75176.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': 4235.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 72734.0, 'action': 'Buy'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 80978.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 80265.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80665.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 80770.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80474.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 80570.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 80130.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 79914.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 79761.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 78963.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 79222.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 80326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 80465.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 80773.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 82066.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}}, '1W': {'symbol': 'COMI', 'interval': '1W', 'current_price': 83.9, 'timestamp': datetime.datetime(2025, 6, 28, 23, 25, 52, 155613), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 58540.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': -290.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 68206.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 81010.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 81017.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80410.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 79757.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80157.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 79621.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 79156.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 80980.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 73509.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 76174.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 62422.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 56579.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78450.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 79830.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 81417.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 83900.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Relative Strength Index (14)', 'value': 61012.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic %K (14, 3, 3)', 'value': 78637.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Commodity Channel Index (20)', 'value': 162338.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Average Directional Index (14)', 'value': 19681.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Awesome Oscillator', 'value': -207.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Momentum (10)', 'value': 1350.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'MACD Level (12, 26)', 'value': 326.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 58540.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Williams Percent Range (14)', 'value': 0.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Bull Bear Power', 'value': -290.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 68206.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (10)', 'value': 81010.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (10)', 'value': 81017.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (20)', 'value': 80410.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (20)', 'value': 79757.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (30)', 'value': 80157.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (30)', 'value': 79621.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (50)', 'value': 79156.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (50)', 'value': 80980.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (100)', 'value': 73509.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (100)', 'value': 76174.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Exponential Moving Average (200)', 'value': 62422.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Simple Moving Average (200)', 'value': 56579.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 78450.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Volume Weighted Moving Average (20)', 'value': 79830.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'name': 'Hull Moving Average (9)', 'value': 81417.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '28/06/2025 23:25', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}}}
2025-06-28 23:25:52,160 - app.utils.historical_data_downloader - INFO - Found valid price from 1D: 83.9 EGP
2025-06-28 23:25:52,160 - app.utils.historical_data_downloader - INFO - Using current price for COMI: 83.9 EGP
2025-06-28 23:25:52,160 - app.utils.historical_data_downloader - INFO - Generating 3 years of synthetic historical data...
2025-06-28 23:25:52,160 - app.utils.historical_data_downloader - INFO - Generating 3 years of synthetic data for COMI
2025-06-28 23:25:52,211 - app.utils.historical_data_downloader - INFO - Generated 750 days of synthetic historical data for COMI
2025-06-28 23:25:52,213 - app.utils.historical_data_downloader - INFO - Generated 750 days of synthetic data
2025-06-28 23:25:52,213 - app.utils.historical_data_downloader - INFO - Saving data to: data\stocks\COMI.csv
2025-06-28 23:25:52,230 - app.utils.historical_data_downloader - INFO - Saved 750 days of historical data to data\stocks\COMI.csv
2025-06-28 23:25:52,230 - app.utils.historical_data_downloader - INFO - CSV file size: 33889 bytes
2025-06-28 23:25:52,245 - app.utils.historical_data_downloader - INFO - Verification: CSV contains 750 rows
2025-06-28 23:25:52,247 - app.utils.historical_data_downloader - INFO - Successfully completed historical data generation for COMI
2025-06-28 23:25:52,331 - app.utils.memory_management - INFO - Memory before cleanup: 440.61 MB
2025-06-28 23:25:52,527 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-06-28 23:25:52,529 - app.utils.memory_management - INFO - Memory after cleanup: 440.61 MB (freed 0.00 MB)
2025-06-28 23:26:16,314 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:26:16,387 - app.utils.memory_management - INFO - Memory before cleanup: 440.75 MB
2025-06-28 23:26:16,616 - app.utils.memory_management - INFO - Garbage collection: collected 260 objects
2025-06-28 23:26:16,619 - app.utils.memory_management - INFO - Memory after cleanup: 440.75 MB (freed 0.00 MB)
2025-06-28 23:26:25,692 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:26:25,749 - app - INFO - File COMI contains 2025 data
2025-06-28 23:26:25,758 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-28 23:26:25,759 - app - INFO - Date range: 2022-08-15 to 2025-06-27
2025-06-28 23:26:25,763 - app.utils.memory_management - INFO - Memory before cleanup: 440.82 MB
2025-06-28 23:26:25,967 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-28 23:26:25,967 - app.utils.memory_management - INFO - Memory after cleanup: 440.82 MB (freed 0.00 MB)
2025-06-28 23:26:40,681 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:26:40,763 - app - INFO - File COMI contains 2025 data
2025-06-28 23:26:40,772 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-28 23:26:40,774 - app - INFO - Date range: 2022-08-15 to 2025-06-27
2025-06-28 23:26:40,777 - app.utils.memory_management - INFO - Memory before cleanup: 440.99 MB
2025-06-28 23:26:41,006 - app.utils.memory_management - INFO - Garbage collection: collected 251 objects
2025-06-28 23:26:41,008 - app.utils.memory_management - INFO - Memory after cleanup: 440.99 MB (freed 0.00 MB)
2025-06-28 23:27:04,160 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:27:04,238 - app - INFO - File COMI contains 2025 data
2025-06-28 23:27:04,245 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-28 23:27:04,246 - app - INFO - Date range: 2022-08-15 to 2025-06-27
2025-06-28 23:27:04,248 - app.utils.memory_management - INFO - Memory before cleanup: 441.05 MB
2025-06-28 23:27:04,460 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-06-28 23:27:04,462 - app.utils.memory_management - INFO - Memory after cleanup: 441.05 MB (freed 0.00 MB)
2025-06-28 23:27:05,301 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:27:05,381 - app - INFO - File COMI contains 2025 data
2025-06-28 23:27:05,397 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-28 23:27:05,400 - app - INFO - Date range: 2022-08-15 to 2025-06-27
2025-06-28 23:27:05,407 - app.utils.memory_management - INFO - Memory before cleanup: 441.05 MB
2025-06-28 23:27:05,607 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-28 23:27:05,607 - app.utils.memory_management - INFO - Memory after cleanup: 441.05 MB (freed 0.00 MB)
2025-06-28 23:27:08,080 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:27:08,226 - app - INFO - File COMI contains 2025 data
2025-06-28 23:27:08,264 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-28 23:27:08,265 - app - INFO - Date range: 2022-08-15 to 2025-06-27
2025-06-28 23:27:08,267 - app.utils.memory_management - INFO - Memory before cleanup: 441.54 MB
2025-06-28 23:27:08,451 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-28 23:27:08,451 - app.utils.memory_management - INFO - Memory after cleanup: 441.54 MB (freed 0.00 MB)
2025-06-28 23:27:11,092 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:27:11,253 - app - INFO - File COMI contains 2025 data
2025-06-28 23:27:11,305 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-28 23:27:11,306 - app - INFO - Date range: 2022-08-15 to 2025-06-27
2025-06-28 23:27:11,307 - app.utils.memory_management - INFO - Memory before cleanup: 441.61 MB
2025-06-28 23:27:11,489 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-28 23:27:11,490 - app.utils.memory_management - INFO - Memory after cleanup: 441.61 MB (freed 0.00 MB)
2025-06-28 23:27:18,015 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:27:18,180 - app.utils.memory_management - INFO - Memory before cleanup: 441.60 MB
2025-06-28 23:27:18,485 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-06-28 23:27:18,489 - app.utils.memory_management - INFO - Memory after cleanup: 441.60 MB (freed 0.00 MB)
2025-06-28 23:27:46,209 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:27:46,275 - app.utils.memory_management - INFO - Memory before cleanup: 441.60 MB
2025-06-28 23:27:46,474 - app.utils.memory_management - INFO - Garbage collection: collected 200 objects
2025-06-28 23:27:46,477 - app.utils.memory_management - INFO - Memory after cleanup: 441.60 MB (freed 0.00 MB)
2025-06-28 23:27:47,183 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:27:47,250 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-28 23:27:47,257 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.314
2025-06-28 23:27:47,407 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-28 23:27:47,498 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-28 23:27:47,499 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-28 23:27:47,499 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-28 23:27:47,499 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (53.1)
2025-06-28 23:27:47,499 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-28 23:27:54,108 - app.utils.ai_pattern_recognition - INFO - Using live price 48.89 EGP from API for ABUK
2025-06-28 23:27:54,108 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-28 23:27:54,108 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-28 23:27:54,108 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-28 23:27:54,108 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-28 23:27:54,162 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-28 23:27:54,162 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 48.2 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 48.194755183600435, 'bearish_score': 0}
2025-06-28 23:27:54,163 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-06-28 23:27:55,495 - app.utils.memory_management - INFO - Memory before cleanup: 460.03 MB
2025-06-28 23:27:55,676 - app.utils.memory_management - INFO - Garbage collection: collected 992 objects
2025-06-28 23:27:55,678 - app.utils.memory_management - INFO - Memory after cleanup: 460.03 MB (freed 0.00 MB)
2025-06-28 23:28:14,034 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:28:14,099 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-28 23:28:14,141 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.063
2025-06-28 23:28:14,250 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-28 23:28:14,395 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-28 23:28:14,398 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-28 23:28:14,398 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-28 23:28:14,398 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (59.9)
2025-06-28 23:28:14,398 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-28 23:28:45,381 - app.utils.ai_pattern_recognition - INFO - Using CSV price 83.90 EGP for COMI
2025-06-28 23:28:45,381 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for COMI
2025-06-28 23:28:45,383 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-28 23:28:45,399 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='HOLD', volatility_factor=0.050
2025-06-28 23:28:45,399 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'HOLD', 'confidence': 0.5, 'reasoning': 'Mixed AI signals suggest maintaining current position (Bullish: 16.0, Bearish: 17.5)', 'risk_level': 'Moderate', 'bullish_score': 16.0, 'bearish_score': 17.5}
2025-06-28 23:28:45,399 - app.pages.advanced_ai_features - INFO - Using HOLD logic: entry=83.90, stop=79.70, target=92.29
2025-06-28 23:28:45,486 - app.utils.memory_management - INFO - Memory before cleanup: 466.20 MB
2025-06-28 23:28:45,600 - app.utils.memory_management - INFO - Garbage collection: collected 1707 objects
2025-06-28 23:28:45,600 - app.utils.memory_management - INFO - Memory after cleanup: 466.20 MB (freed 0.00 MB)
2025-06-28 23:29:48,822 - app - INFO - Using TensorFlow-based LSTM model
2025-06-28 23:29:48,866 - app - INFO - Found 14 stock files in data/stocks
2025-06-28 23:29:48,909 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-28 23:29:54,136 - app.utils.ai_pattern_recognition - INFO - Using live price 83.90 EGP from API for COMI
2025-06-28 23:29:54,136 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-28 23:29:54,136 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-28 23:29:54,139 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-28 23:29:54,141 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-28 23:29:54,178 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-28 23:29:54,188 - app.utils.ai_pattern_recognition - INFO - API not available, using CSV price 83.90 EGP for None
2025-06-28 23:29:54,188 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for None
2025-06-28 23:29:54,192 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-28 23:29:54,218 - app.utils.memory_management - INFO - Memory before cleanup: 466.47 MB
2025-06-28 23:29:54,447 - app.utils.memory_management - INFO - Garbage collection: collected 254 objects
2025-06-28 23:29:54,448 - app.utils.memory_management - INFO - Memory after cleanup: 466.47 MB (freed 0.00 MB)
