2025-05-29 12:07:32,246 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-05-29 12:07:38,306 - app - INFO - Memory management utilities loaded
2025-05-29 12:07:38,319 - app - INFO - Error handling utilities loaded
2025-05-29 12:07:38,325 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-29 12:07:38,326 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-29 12:07:38,326 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-29 12:07:38,326 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-29 12:07:38,339 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-29 12:07:38,353 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-29 12:07:38,356 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-29 12:07:38,359 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-29 12:07:38,359 - app - INFO - Applied NumPy fix
2025-05-29 12:07:38,364 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-29 12:07:38,369 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-29 12:07:38,370 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-29 12:07:38,371 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-29 12:07:38,371 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-29 12:07:38,371 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-29 12:07:38,371 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-29 12:07:38,372 - app - INFO - Applied NumPy BitGenerator fix
2025-05-29 12:07:57,402 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-05-29 12:07:57,410 - app - INFO - Applied TensorFlow fix
2025-05-29 12:07:57,424 - app.config - INFO - Configuration initialized
2025-05-29 12:07:57,440 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-29 12:07:57,736 - models.train - INFO - TensorFlow test successful
2025-05-29 12:08:02,019 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-29 12:08:02,019 - models.train - INFO - Transformer model is available
2025-05-29 12:08:02,019 - models.train - INFO - Using TensorFlow-based models
2025-05-29 12:08:02,031 - models.predict - INFO - Transformer model is available for predictions
2025-05-29 12:08:02,031 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-29 12:08:02,059 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-05-29 12:08:03,677 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-29 12:08:03,677 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-29 12:08:03,677 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-29 12:08:04,038 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-05-29 12:08:04,084 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:05,005 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-29 12:08:08,330 - app.services.llm_service - INFO - llama_cpp is available
2025-05-29 12:08:08,389 - app.utils.session_state - INFO - Initializing session state
2025-05-29 12:08:08,389 - app.utils.session_state - INFO - Session state initialized
2025-05-29 12:08:09,557 - app - INFO - Found 8 stock files in data/stocks
2025-05-29 12:08:09,569 - app.utils.memory_management - INFO - Memory before cleanup: 430.14 MB
2025-05-29 12:08:09,726 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:08:09,727 - app.utils.memory_management - INFO - Memory after cleanup: 430.54 MB (freed -0.40 MB)
2025-05-29 12:08:20,194 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:20,230 - app.utils.memory_management - INFO - Memory before cleanup: 434.28 MB
2025-05-29 12:08:20,396 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:08:20,397 - app.utils.memory_management - INFO - Memory after cleanup: 434.28 MB (freed 0.00 MB)
2025-05-29 12:08:21,427 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:21,570 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.12 seconds
2025-05-29 12:08:21,585 - app - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:08:21,585 - app - INFO - Data shape: (579, 36)
2025-05-29 12:08:21,585 - app - INFO - File COMI contains 2025 data
2025-05-29 12:08:21,633 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-05-29 12:08:21,633 - app - INFO - Features shape: (579, 36)
2025-05-29 12:08:21,647 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:08:21,648 - app - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:08:21,648 - app - INFO - Data shape: (579, 36)
2025-05-29 12:08:21,648 - app - INFO - File COMI contains 2025 data
2025-05-29 12:08:21,650 - app.utils.memory_management - INFO - Memory before cleanup: 438.27 MB
2025-05-29 12:08:21,760 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-05-29 12:08:21,760 - app.utils.memory_management - INFO - Memory after cleanup: 438.31 MB (freed -0.04 MB)
2025-05-29 12:08:21,899 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:22,004 - app.utils.memory_management - INFO - Memory before cleanup: 439.38 MB
2025-05-29 12:08:22,146 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-05-29 12:08:22,147 - app.utils.memory_management - INFO - Memory after cleanup: 439.36 MB (freed 0.02 MB)
2025-05-29 12:08:44,819 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:44,863 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:44,867 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:44,868 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:44,876 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,877 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:44,877 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:08:44,878 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:08:44,884 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:44,885 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:44,889 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,893 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:08:44,953 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-29 12:08:44,954 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:08:44,954 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:44,954 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:44,961 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:08:44,975 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,976 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:08:44,976 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-29 12:08:44,976 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:44,977 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:44,977 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:44,982 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:44,983 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:44,984 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:08:45,002 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:08:45,007 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:45,008 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:45,013 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,031 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:08:45,033 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:08:45,033 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:08:45,033 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:08:45,033 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:08:45,034 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:08:45,035 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:08:45,035 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:08:45,035 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:08:45,035 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:08:45,036 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:08:45,037 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:08:45,037 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:08:45,038 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:08:45,038 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:08:45,286 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:08:45,327 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:45,327 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:45,330 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:45,338 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,338 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:45,338 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:08:45,342 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:45,342 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:45,353 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,361 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:08:45,363 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:08:45,364 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:45,369 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:08:45,378 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,379 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:08:45,379 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:08:45,380 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:45,380 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:08:45,390 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,391 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:08:45,392 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:45,392 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:45,393 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:45,406 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:45,421 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:45,467 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:45,474 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:45,476 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:45,476 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:45,476 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:45,487 - app.utils.memory_management - INFO - Memory before cleanup: 440.03 MB
2025-05-29 12:08:45,611 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-05-29 12:08:45,612 - app.utils.memory_management - INFO - Memory after cleanup: 440.03 MB (freed 0.00 MB)
2025-05-29 12:08:55,651 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:55,688 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:08:55,701 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:55,701 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:55,714 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,721 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:08:55,721 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:08:55,722 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:08:55,722 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:08:55,723 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:08:55,723 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:08:55,726 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:08:55,727 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:08:55,728 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:08:55,728 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:08:55,862 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:08:55,888 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:55,888 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:55,889 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:55,894 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,894 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:55,895 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:08:55,906 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:55,939 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:08:55,957 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,966 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:08:55,968 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:08:55,968 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:55,968 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:08:55,972 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,983 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:08:55,984 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:08:55,988 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:55,989 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:08:55,998 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:55,998 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:08:55,999 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:56,000 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:56,002 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:08:56,015 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:08:56,015 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:08:56,022 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:56,034 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:08:56,036 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:08:56,037 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:08:56,037 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:08:56,051 - app.utils.memory_management - INFO - Memory before cleanup: 440.09 MB
2025-05-29 12:08:56,177 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:08:56,178 - app.utils.memory_management - INFO - Memory after cleanup: 440.09 MB (freed 0.00 MB)
2025-05-29 12:08:57,616 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:08:57,648 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:08:57,662 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:08:58,910 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:08:58,910 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:08:58,918 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.73
2025-05-29 12:09:01,532 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:09:01,532 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:09:01,532 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:09:32,324 - app.utils.retry - WARNING - Attempt 1 failed, retrying in 1.82s: Message: timeout: Timed out receiving message from renderer: 30.000
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF7249F5335+78597]
	GetHandleVerifier [0x00007FF7249F5390+78688]
	(No symbol) [0x00007FF7247A91AA]
	(No symbol) [0x00007FF7247965AC]
	(No symbol) [0x00007FF72479629A]
	(No symbol) [0x00007FF724793F4A]
	(No symbol) [0x00007FF7247948FF]
	(No symbol) [0x00007FF7247A34FE]
	(No symbol) [0x00007FF7247B9931]
	(No symbol) [0x00007FF7247C08DA]
	(No symbol) [0x00007FF72479506D]
	(No symbol) [0x00007FF7247B9665]
	(No symbol) [0x00007FF72484F194]
	(No symbol) [0x00007FF724826EC3]
	(No symbol) [0x00007FF7247F03F8]
	(No symbol) [0x00007FF7247F1163]
	GetHandleVerifier [0x00007FF724C9EEED+2870973]
	GetHandleVerifier [0x00007FF724C99698+2848360]
	GetHandleVerifier [0x00007FF724CB6973+2967875]
	GetHandleVerifier [0x00007FF724A1017A+188746]
	GetHandleVerifier [0x00007FF724A1845F+222255]
	GetHandleVerifier [0x00007FF7249FD2B4+111236]
	GetHandleVerifier [0x00007FF7249FD462+111666]
	GetHandleVerifier [0x00007FF7249E3589+5465]
	BaseThreadInitThunk [0x00007FFF0932E8D7+23]
	RtlUserThreadStart [0x00007FFF0AC9C5DC+44]

2025-05-29 12:09:34,165 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:09:36,550 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:09:38,738 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:09:38,746 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.3
2025-05-29 12:09:38,747 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.3
2025-05-29 12:09:38,747 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:09:38,747 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:09:38,747 - app.utils.error_handling - INFO - fetch_price executed in 37.21 seconds
2025-05-29 12:09:38,747 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:09:40,979 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-29 12:09:41,103 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.53 KB)
2025-05-29 12:09:41,220 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:09:41,225 - models.predict - INFO - Using RobustEnsembleModel for 4 minutes horizon
2025-05-29 12:09:41,334 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_4min.joblib
2025-05-29 12:09:41,334 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 4
2025-05-29 12:09:41,334 - models.predict - INFO - Loading ensemble model for COMI with horizon 4
2025-05-29 12:09:41,336 - models.predict - INFO - Ensemble model already loaded
2025-05-29 12:09:41,353 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:09:41,353 - models.predict - INFO - Current price: 81.3, Predicted scaled value: 0.750632905440788
2025-05-29 12:09:41,353 - models.predict - INFO - Prediction for 4 minutes horizon: 82.83468137890404
2025-05-29 12:09:41,688 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:09:41,699 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:09:41,701 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:09:41,709 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,711 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:09:41,713 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:09:41,713 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:09:41,713 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:09:41,713 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:09:41,715 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:09:41,715 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:09:41,715 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:09:41,715 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:09:41,715 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:09:41,839 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:09:41,864 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:09:41,864 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:09:41,864 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:09:41,871 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,871 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:09:41,872 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:09:41,876 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:09:41,877 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:09:41,882 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,888 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:09:41,889 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:09:41,890 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:09:41,891 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:09:41,896 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,896 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:09:41,896 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:09:41,897 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:09:41,898 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:09:41,902 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,903 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:09:41,904 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:09:41,905 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:09:41,906 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:09:41,911 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:09:41,913 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:09:41,917 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:09:41,925 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:09:41,925 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:09:41,925 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:09:41,929 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:09:41,933 - app.utils.memory_management - INFO - Memory before cleanup: 444.67 MB
2025-05-29 12:09:42,041 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:09:42,041 - app.utils.memory_management - INFO - Memory after cleanup: 444.67 MB (freed 0.00 MB)
2025-05-29 12:10:42,981 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:43,052 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:43,058 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:43,059 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:43,066 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,073 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:43,075 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:43,075 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:43,075 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:43,075 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:43,077 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:43,079 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:43,080 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:43,081 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:43,081 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:43,352 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:43,399 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:43,400 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:43,400 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:43,410 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,413 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:43,413 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:43,421 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:43,421 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:43,426 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,433 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:43,434 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:43,435 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:43,436 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:43,442 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,443 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:43,445 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:43,447 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:43,448 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:43,459 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,459 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:43,459 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:43,461 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:43,461 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:43,470 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,470 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:43,476 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:43,484 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:43,484 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:43,484 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:43,486 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:43,494 - app.utils.memory_management - INFO - Memory before cleanup: 446.01 MB
2025-05-29 12:10:43,636 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:43,636 - app.utils.memory_management - INFO - Memory after cleanup: 446.01 MB (freed 0.00 MB)
2025-05-29 12:10:43,829 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:43,872 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:43,883 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:43,884 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:43,895 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:43,905 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:43,905 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:43,907 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:43,907 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:43,907 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:43,908 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:43,909 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:43,909 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:43,910 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:43,910 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:44,080 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:44,116 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:44,117 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:44,117 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:44,123 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,124 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:44,124 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:44,130 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:44,131 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:44,141 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,147 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:44,147 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:44,148 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:44,148 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:44,151 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:44,160 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:44,168 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:44,168 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:44,172 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:44,181 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:44,181 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:44,181 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:44,181 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:44,191 - app.utils.memory_management - INFO - Memory before cleanup: 446.02 MB
2025-05-29 12:10:44,319 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:44,319 - app.utils.memory_management - INFO - Memory after cleanup: 446.02 MB (freed 0.00 MB)
2025-05-29 12:10:44,944 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:45,034 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:45,039 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:45,040 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:45,054 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,062 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:45,066 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:45,066 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:45,067 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:45,069 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:45,074 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:45,082 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:45,083 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:45,083 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:45,083 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:45,255 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:45,316 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:45,316 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:45,318 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:45,323 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,323 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:45,323 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:45,334 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:45,334 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:45,340 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,359 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:45,361 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:45,362 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:45,368 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:45,374 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,376 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:45,377 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:45,377 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:45,378 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:45,390 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,391 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:45,391 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:45,391 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:45,392 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:45,400 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:45,401 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:45,406 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:45,413 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:45,415 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:45,417 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:45,418 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:45,432 - app.utils.memory_management - INFO - Memory before cleanup: 446.03 MB
2025-05-29 12:10:45,599 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:45,599 - app.utils.memory_management - INFO - Memory after cleanup: 446.03 MB (freed 0.00 MB)
2025-05-29 12:10:46,185 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:46,274 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:46,284 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:46,286 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:46,292 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,296 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:46,297 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:46,297 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:46,297 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:46,299 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:46,300 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:46,302 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:46,303 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:46,303 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:46,303 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:46,455 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:46,484 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:46,485 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:46,486 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:46,491 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,492 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:46,492 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:46,496 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:46,497 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:46,502 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,507 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:46,507 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:46,507 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:46,508 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:46,518 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,519 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:46,519 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:46,520 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:46,520 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:46,525 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,526 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:46,526 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:46,526 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:46,527 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:46,531 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:46,531 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:46,538 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:46,544 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:46,545 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:46,546 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:46,546 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:46,556 - app.utils.memory_management - INFO - Memory before cleanup: 446.04 MB
2025-05-29 12:10:46,691 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:46,691 - app.utils.memory_management - INFO - Memory after cleanup: 446.02 MB (freed 0.02 MB)
2025-05-29 12:10:54,290 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:54,354 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:54,362 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:54,363 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:54,367 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,374 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:54,374 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:54,375 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:54,376 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:54,376 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:54,377 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:54,379 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:54,379 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:54,379 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:54,380 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:54,518 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:54,544 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:54,544 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:54,545 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:54,552 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,553 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:54,553 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:54,561 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:54,562 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:54,567 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,580 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:54,580 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:54,580 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:54,580 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:54,594 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,595 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:54,595 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:54,595 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:54,595 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:54,602 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,602 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:54,603 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:54,603 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:54,603 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:54,612 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:54,612 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:54,617 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:54,623 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:54,623 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:54,624 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:54,624 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:54,629 - app.utils.memory_management - INFO - Memory before cleanup: 446.03 MB
2025-05-29 12:10:54,758 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:54,759 - app.utils.memory_management - INFO - Memory after cleanup: 446.03 MB (freed 0.00 MB)
2025-05-29 12:10:55,111 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:55,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:55,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:55,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:55,156 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,161 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:55,162 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:55,162 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:55,163 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:55,163 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:55,164 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:55,165 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:55,167 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:55,169 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:55,170 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:55,303 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:55,336 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:55,336 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:55,336 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:55,344 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,344 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:55,345 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:55,350 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:55,350 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:55,355 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,365 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:55,366 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:55,366 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:55,367 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:55,383 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,385 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:55,385 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:55,387 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:55,387 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:55,394 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,396 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:55,396 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:55,396 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:55,396 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:55,401 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:55,401 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:55,411 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:55,417 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:55,417 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:55,418 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:55,418 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:55,427 - app.utils.memory_management - INFO - Memory before cleanup: 446.03 MB
2025-05-29 12:10:55,565 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:55,566 - app.utils.memory_management - INFO - Memory after cleanup: 446.03 MB (freed 0.00 MB)
2025-05-29 12:10:57,094 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:57,131 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:57,140 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:57,141 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:57,150 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,156 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:57,159 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:57,160 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:57,160 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:57,161 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:57,162 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:57,164 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:57,164 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:57,164 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:57,164 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:57,302 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:57,335 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:57,336 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:57,336 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:57,344 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,345 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:57,345 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:57,353 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:57,353 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:57,358 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,365 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:57,365 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:57,366 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:57,366 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:57,371 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,371 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:57,372 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:57,372 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:57,372 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:57,382 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,382 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:57,383 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:57,383 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:57,384 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:57,391 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:57,392 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:57,395 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:57,400 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:57,400 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:57,400 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:57,400 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:57,417 - app.utils.memory_management - INFO - Memory before cleanup: 446.05 MB
2025-05-29 12:10:57,551 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:57,551 - app.utils.memory_management - INFO - Memory after cleanup: 446.05 MB (freed 0.00 MB)
2025-05-29 12:10:58,967 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:10:59,010 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:10:59,015 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:59,015 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:59,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:10:59,034 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:10:59,034 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:10:59,170 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:10:59,199 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:59,200 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:59,200 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:59,208 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,209 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:59,209 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:10:59,213 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:59,213 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:10:59,213 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,222 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:10:59,222 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:10:59,222 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:59,222 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:10:59,235 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,235 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:10:59,236 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:10:59,236 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:59,236 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:10:59,240 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,241 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:10:59,241 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:10:59,242 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:59,242 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:10:59,248 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:10:59,248 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:10:59,253 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:59,258 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:10:59,258 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:10:59,258 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:10:59,258 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:10:59,269 - app.utils.memory_management - INFO - Memory before cleanup: 446.11 MB
2025-05-29 12:10:59,391 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:10:59,392 - app.utils.memory_management - INFO - Memory after cleanup: 446.11 MB (freed 0.00 MB)
2025-05-29 12:11:06,768 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:11:06,805 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:11:06,814 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:11:06,815 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:11:06,822 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:11:06,827 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:11:06,827 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:11:06,982 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:11:06,995 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:11:08,174 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:11:08,174 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:11:08,190 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.65
2025-05-29 12:11:08,190 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:11:08,190 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:11:08,190 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:11:33,851 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:11:35,973 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:11:35,984 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.31
2025-05-29 12:11:35,984 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.31
2025-05-29 12:11:35,985 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:11:35,985 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:11:35,985 - app.utils.error_handling - INFO - fetch_price executed in 27.79 seconds
2025-05-29 12:11:35,987 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:11:35,988 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:11:35,988 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:11:35,988 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:11:35,989 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:11:35,990 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:11:35,991 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:11:35,991 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:11:35,992 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:11:36,143 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:36,317 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:36,431 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:36,433 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-29 12:11:36,433 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-29 12:11:38,006 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:40,573 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-29 12:11:40,575 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7009795904159546
2025-05-29 12:11:40,575 - models.predict - INFO - Prediction for 30 minutes horizon: 80.11367986759097
2025-05-29 12:11:40,599 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:40,763 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:40,875 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.18 MB, VMS: 0.21 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:40,877 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-05-29 12:11:41,100 - models.hybrid_model - INFO - XGBoost is available
2025-05-29 12:11:41,100 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-29 12:11:41,113 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-05-29 12:11:41,114 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-05-29 12:11:41,114 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-05-29 12:11:41,196 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:41,197 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:11:41,197 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:41,203 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:41,204 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7304014849662781
2025-05-29 12:11:41,204 - models.predict - INFO - Prediction for 30 minutes horizon: 81.72599959885332
2025-05-29 12:11:41,225 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:41,346 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:41,498 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:41,500 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-05-29 12:11:41,501 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-05-29 12:11:41,503 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-05-29 12:11:41,503 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-05-29 12:11:41,512 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:41,531 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:11:41,532 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:41,533 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:41,533 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7324837511963868
2025-05-29 12:11:41,534 - models.predict - INFO - Prediction for 30 minutes horizon: 81.84010778188697
2025-05-29 12:11:41,571 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:41,828 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:42,009 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:42,012 - models.predict - INFO - Using scikit-learn svr model for 30 minutes horizon
2025-05-29 12:11:42,013 - models.predict - INFO - Loading svr model for COMI with horizon 30
2025-05-29 12:11:42,013 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_30min.joblib
2025-05-29 12:11:42,013 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_30min.joblib
2025-05-29 12:11:42,015 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:42,016 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:11:42,016 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:42,026 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:42,027 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.49390464032770726
2025-05-29 12:11:42,030 - models.predict - INFO - Prediction for 30 minutes horizon: 68.76597323685753
2025-05-29 12:11:42,063 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:42,200 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:42,347 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:42,350 - models.predict - INFO - Using scikit-learn lr model for 30 minutes horizon
2025-05-29 12:11:42,350 - models.predict - INFO - Loading lr model for COMI with horizon 30
2025-05-29 12:11:42,351 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_30min.joblib
2025-05-29 12:11:42,352 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_30min.joblib
2025-05-29 12:11:42,356 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:42,357 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:11:42,357 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:42,359 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:42,359 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.734099911823467
2025-05-29 12:11:42,361 - models.predict - INFO - Prediction for 30 minutes horizon: 81.92867337930198
2025-05-29 12:11:42,364 - models.predict - INFO - Current price for COMI: 81.31
2025-05-29 12:11:42,365 - models.predict - INFO - Prophet prediction for 30 minutes: 81.28613431803268
2025-05-29 12:11:42,409 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-29 12:11:42,669 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-05-29 12:11:42,917 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-29 12:11:42,919 - models.predict - INFO - Using scikit-learn hybrid model for 30 minutes horizon
2025-05-29 12:11:42,922 - models.predict - INFO - Loading hybrid model for COMI with horizon 30
2025-05-29 12:11:42,925 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_30min.joblib
2025-05-29 12:11:42,927 - models.sklearn_model - INFO - Found hybrid model at saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-29 12:11:42,927 - models.sklearn_model - INFO - Loading model from saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-29 12:11:43,040 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-29 12:11:43,042 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:11:43,044 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:43,052 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:43,059 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7304014849662781
2025-05-29 12:11:43,064 - models.predict - INFO - Prediction for 30 minutes horizon: 81.72599959885332
2025-05-29 12:11:43,108 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:43,277 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:43,417 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:43,418 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:11:43,419 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:11:43,419 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:11:43,420 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:11:43,447 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:43,600 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:43,743 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:43,745 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:11:43,746 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:11:43,746 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:11:43,747 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:11:43,748 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:43,750 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:11:43,751 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:11:43,842 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:11:43,877 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:43,878 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:11:43,878 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:43,884 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:43,884 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7209689927101135
2025-05-29 12:11:43,884 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:11:43,913 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:44,093 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:44,290 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-29 12:11:44,291 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:11:44,292 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:11:44,292 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:11:44,293 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:11:44,295 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:44,296 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:11:44,296 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:11:44,302 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:11:44,312 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:44,331 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:11:44,331 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:44,332 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:44,333 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7194341839453234
2025-05-29 12:11:44,334 - models.predict - INFO - Prediction for 60 minutes horizon: 81.12499328020373
2025-05-29 12:11:44,367 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:44,504 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:44,630 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:44,632 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-29 12:11:44,632 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-29 12:11:44,633 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-29 12:11:44,633 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-29 12:11:44,635 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:44,635 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-29 12:11:44,636 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:11:44,637 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:11:44,640 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:44,640 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:11:44,640 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:44,641 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:44,641 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.49872770271068445
2025-05-29 12:11:44,642 - models.predict - INFO - Prediction for 60 minutes horizon: 69.0302781085455
2025-05-29 12:11:44,667 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:44,802 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:44,945 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:44,947 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:11:44,948 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:11:44,948 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:11:44,949 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:11:44,951 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:44,951 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:11:44,951 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:11:44,952 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:11:44,952 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:44,953 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:11:44,953 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:11:44,953 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:44,953 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7427804284288911
2025-05-29 12:11:44,954 - models.predict - INFO - Prediction for 60 minutes horizon: 82.40436747790324
2025-05-29 12:11:44,958 - models.predict - INFO - Current price for COMI: 81.31
2025-05-29 12:11:44,958 - models.predict - INFO - Prophet prediction for 60 minutes: 81.44004793329107
2025-05-29 12:11:44,983 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:11:45,116 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:11:45,264 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:11:45,266 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-29 12:11:45,266 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-29 12:11:45,266 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-29 12:11:45,267 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-29 12:11:45,269 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:11:45,269 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:11:45,270 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:11:45,344 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:11:45,373 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:11:45,374 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:11:45,379 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:11:45,380 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7209689927101135
2025-05-29 12:11:45,381 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:11:45,441 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-29 12:11:45,480 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-29 12:11:45,576 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:11:48,006 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:11:48,006 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:11:48,006 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:11:48,024 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,024 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:11:48,024 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:11:48,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:11:48,032 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:11:48,035 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,043 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:11:48,043 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:11:48,043 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:11:48,045 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:11:48,051 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:11:48,059 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:11:48,065 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:11:48,067 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:11:48,067 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:11:48,077 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:11:48,078 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:11:48,078 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:11:48,078 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:11:48,089 - app.utils.memory_management - INFO - Memory before cleanup: 479.90 MB
2025-05-29 12:11:48,268 - app.utils.memory_management - INFO - Garbage collection: collected 604 objects
2025-05-29 12:11:48,268 - app.utils.memory_management - INFO - Memory after cleanup: 479.90 MB (freed 0.00 MB)
2025-05-29 12:12:30,603 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:12:30,726 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:12:30,740 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:30,742 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:12:30,762 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:30,771 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:12:30,772 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:12:30,773 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:12:30,773 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:12:30,773 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:12:30,775 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:12:30,777 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:12:30,777 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:12:30,777 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:12:30,777 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:12:30,952 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:12:30,992 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:12:30,992 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:30,992 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:12:31,001 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,001 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:12:31,001 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:12:31,007 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:31,007 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:12:31,013 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,026 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:12:31,026 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:12:31,027 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:12:31,027 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:12:31,039 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,040 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:12:31,040 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:12:31,040 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:12:31,041 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:12:31,046 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,047 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:12:31,047 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:12:31,048 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:31,048 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:12:31,057 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:31,057 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:12:31,063 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:31,072 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:31,073 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:12:31,073 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:12:31,073 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:31,081 - app.utils.memory_management - INFO - Memory before cleanup: 480.34 MB
2025-05-29 12:12:31,231 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:12:31,232 - app.utils.memory_management - INFO - Memory after cleanup: 480.34 MB (freed 0.00 MB)
2025-05-29 12:12:31,984 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:12:32,041 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:12:32,060 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:32,067 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:12:32,078 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:32,097 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:12:32,097 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:12:32,104 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:12:32,106 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:12:32,106 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:12:32,107 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:12:32,108 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:12:32,109 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:12:32,109 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:12:32,109 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:12:32,253 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:12:32,297 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:32,442 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:32,573 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:32,575 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:12:32,575 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:12:32,575 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:12:32,576 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:12:32,599 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:32,716 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:32,830 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:32,830 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:12:32,830 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:12:32,833 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:12:32,833 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:12:32,835 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:32,835 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:12:32,835 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:12:32,865 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:12:32,898 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:32,898 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:12:32,898 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:12:32,902 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:32,902 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7209689927101135
2025-05-29 12:12:32,902 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:12:32,927 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:33,044 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:33,152 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:33,152 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:12:33,152 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:12:33,152 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:12:33,152 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:12:33,163 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:33,163 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:12:33,163 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:12:33,171 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:12:33,171 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:33,186 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:12:33,186 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:12:33,186 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:33,186 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7194341839453234
2025-05-29 12:12:33,186 - models.predict - INFO - Prediction for 60 minutes horizon: 81.12499328020373
2025-05-29 12:12:33,211 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:33,321 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:33,436 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:33,436 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-29 12:12:33,436 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-29 12:12:33,436 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-29 12:12:33,436 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-29 12:12:33,449 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:33,449 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-29 12:12:33,449 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:12:33,449 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:12:33,453 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:33,453 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:12:33,453 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:12:33,453 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:33,453 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.49872770271068445
2025-05-29 12:12:33,453 - models.predict - INFO - Prediction for 60 minutes horizon: 69.0302781085455
2025-05-29 12:12:33,469 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:33,600 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:33,714 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:33,714 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:12:33,714 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:12:33,714 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:12:33,714 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:12:33,719 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:33,719 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:12:33,719 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:12:33,719 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:33,719 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7427804284288911
2025-05-29 12:12:33,719 - models.predict - INFO - Prediction for 60 minutes horizon: 82.40436747790324
2025-05-29 12:12:33,725 - models.predict - INFO - Current price for COMI: 81.31
2025-05-29 12:12:33,725 - models.predict - INFO - Prophet prediction for 60 minutes: 81.39339142882824
2025-05-29 12:12:33,741 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:12:33,863 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:12:33,975 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:12:33,975 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-29 12:12:33,975 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-29 12:12:33,975 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-29 12:12:33,975 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-29 12:12:33,980 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:12:33,980 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:12:33,980 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:12:34,010 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:12:34,036 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:12:34,036 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:12:34,044 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:12:34,044 - models.predict - INFO - Current price: 81.31, Predicted scaled value: 0.7209689927101135
2025-05-29 12:12:34,044 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:12:34,216 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:12:34,216 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:34,216 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:12:34,240 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,240 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:12:34,241 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:12:34,245 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:34,246 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:12:34,256 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,261 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:12:34,261 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:12:34,261 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:12:34,262 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:12:34,266 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,266 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:12:34,267 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:12:34,267 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:12:34,268 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:12:34,274 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,274 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:12:34,275 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:12:34,275 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:34,275 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:12:34,280 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:12:34,280 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:12:34,284 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:34,293 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:12:34,293 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:12:34,294 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:12:34,294 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:12:34,304 - app.utils.memory_management - INFO - Memory before cleanup: 481.02 MB
2025-05-29 12:12:34,434 - app.utils.memory_management - INFO - Garbage collection: collected 1067 objects
2025-05-29 12:12:34,434 - app.utils.memory_management - INFO - Memory after cleanup: 481.02 MB (freed 0.00 MB)
2025-05-29 12:13:25,813 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:13:25,830 - app - INFO - Found 8 stock files in data/stocks
2025-05-29 12:13:25,865 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:13:25,876 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:13:25,876 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:13:25,882 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:25,889 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:13:25,889 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:13:25,890 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:13:25,890 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:13:25,890 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:13:25,891 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:13:25,893 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:13:25,893 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:13:25,893 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:13:25,893 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:13:26,115 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:13:26,145 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:13:26,147 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:13:26,147 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:13:26,155 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:26,155 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:13:26,157 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:13:26,161 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:13:26,161 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:13:26,167 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:26,176 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:13:26,177 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:13:26,180 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:13:26,180 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:13:26,189 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:26,190 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:13:26,190 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:13:26,191 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:13:26,191 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:13:26,196 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:26,197 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:13:26,197 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:13:26,198 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:13:26,199 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:13:26,207 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:26,208 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:13:26,213 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:13:26,218 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:13:26,219 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:13:26,221 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:13:26,223 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:13:26,230 - app.utils.memory_management - INFO - Memory before cleanup: 482.56 MB
2025-05-29 12:13:26,371 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:13:26,372 - app.utils.memory_management - INFO - Memory after cleanup: 482.55 MB (freed 0.02 MB)
2025-05-29 12:13:44,695 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:13:44,756 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:13:44,775 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:13:44,777 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:13:44,783 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:44,797 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:13:44,797 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:13:44,798 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:13:44,799 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:13:44,799 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:13:44,800 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:13:44,801 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:13:44,801 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:13:44,802 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:13:44,802 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:13:44,939 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:13:44,959 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:13:44,959 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:13:44,960 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:13:44,966 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:44,966 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:13:44,967 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:13:44,971 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:13:44,972 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:13:44,976 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:44,981 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:13:44,981 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:13:44,981 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:13:44,981 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:13:44,991 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:44,991 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:13:44,991 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:13:44,992 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:13:44,992 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:13:44,996 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:44,996 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:13:44,997 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:13:44,997 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:13:44,997 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:13:45,001 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:13:45,002 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:13:45,007 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:13:45,012 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:13:45,013 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:13:45,013 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:13:45,013 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:13:45,020 - app.utils.memory_management - INFO - Memory before cleanup: 482.55 MB
2025-05-29 12:13:45,138 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:13:45,139 - app.utils.memory_management - INFO - Memory after cleanup: 482.55 MB (freed 0.00 MB)
2025-05-29 12:14:21,618 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:14:21,655 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:14:21,657 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:14:21,657 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:14:21,663 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:21,665 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:14:21,665 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:14:21,665 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:14:21,671 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:14:21,671 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:14:21,679 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:21,683 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:14:21,691 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-29 12:14:21,691 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:14:21,691 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:14:21,693 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:14:21,697 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:21,698 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:14:21,698 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-29 12:14:21,699 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:14:21,699 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:14:21,699 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:14:21,707 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:21,707 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:14:21,709 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-29 12:14:21,709 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:14:21,709 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:14:21,709 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:14:21,715 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:21,717 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:14:21,717 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:14:21,750 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:14:21,761 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:14:21,762 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:14:21,776 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:21,783 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:14:21,783 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:14:21,784 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:14:21,784 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:14:21,784 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:14:21,785 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:14:21,786 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:14:21,787 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:14:21,787 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:14:21,788 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:14:22,003 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:14:22,132 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:14:23,185 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:14:23,185 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:14:23,195 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.4
2025-05-29 12:14:23,195 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:14:23,195 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:14:23,195 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:14:26,447 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:14:28,583 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:14:28,593 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.3
2025-05-29 12:14:28,593 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.3
2025-05-29 12:14:28,593 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:14:28,594 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:14:28,594 - app.utils.error_handling - INFO - fetch_price executed in 5.40 seconds
2025-05-29 12:14:28,595 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:14:30,719 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:30,720 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-05-29 12:14:30,720 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-05-29 12:14:30,720 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-29 12:14:30,837 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:14:30,946 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:14:31,060 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:14:31,062 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:14:31,062 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:14:31,062 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:14:31,062 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:14:31,063 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:14:31,064 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:14:31,064 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:14:31,082 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:14:31,121 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:14:31,122 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:14:31,122 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:14:31,127 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:14:31,128 - models.predict - INFO - Current price: 80.34, Predicted scaled value: 0.7001149833202363
2025-05-29 12:14:31,128 - models.predict - INFO - Prediction for 60 minutes horizon: 80.06630108594895
2025-05-29 12:14:31,187 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:14:31,282 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:14:31,396 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-29 12:14:31,396 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:14:31,396 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:14:31,396 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:14:31,396 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:14:31,396 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:14:31,396 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:14:31,396 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:14:31,410 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:14:31,418 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:14:31,435 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:14:31,435 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:14:31,436 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:14:31,436 - models.predict - INFO - Current price: 80.34, Predicted scaled value: 0.69406540113695
2025-05-29 12:14:31,437 - models.predict - INFO - Prediction for 60 minutes horizon: 79.73478398230486
2025-05-29 12:14:31,495 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:14:31,618 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:14:31,739 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:14:31,741 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:14:31,741 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:14:31,741 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:14:31,741 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:14:31,741 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 79.90054253412691
2025-05-29 12:14:31,741 - app.models.predict - INFO - Prediction completed in 1.02 seconds
2025-05-29 12:14:31,741 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-05-29 12:14:31,741 - app.models.predict - INFO - Using specified model type: lstm
2025-05-29 12:14:31,768 - app.models.predict - INFO - Prediction completed in 0.03 seconds
2025-05-29 12:14:31,768 - app.models.predict - INFO - Using specified model type: bilstm
2025-05-29 12:14:31,818 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:14:31,942 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:14:32,051 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:14:32,052 - models.predict - INFO - Loading bilstm model for COMI with horizon 60
2025-05-29 12:14:32,053 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_60min.keras or saved_models\COMI_bilstm_60min.h5
2025-05-29 12:14:32,053 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 60: Model not found at saved_models\COMI_bilstm_60min.keras or saved_models\COMI_bilstm_60min.h5
2025-05-29 12:14:32,053 - models.predict - INFO - Skipping bilstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:14:32,054 - app.models.predict - INFO - Prediction completed in 0.29 seconds
2025-05-29 12:14:32,054 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-05-29 12:14:32,119 - app.components.enhanced_prediction - INFO - Trend analysis for COMI: down (strength: 0.10)
2025-05-29 12:14:32,134 - app.utils.performance - WARNING - No performance data found for COMI
2025-05-29 12:14:32,193 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:14:32,193 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:14:32,193 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:14:32,204 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:32,205 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:14:32,205 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:14:32,209 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:14:32,210 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:14:32,214 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:32,219 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:14:32,219 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:14:32,220 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:14:32,220 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:14:32,225 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:32,225 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:14:32,225 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:14:32,225 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:14:32,225 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:14:32,231 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:32,231 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:14:32,231 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:14:32,231 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:14:32,231 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:14:32,234 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:14:32,234 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:14:32,234 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:14:32,234 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:14:32,234 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:14:32,249 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:14:32,250 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:14:32,256 - app.utils.memory_management - INFO - Memory before cleanup: 485.37 MB
2025-05-29 12:14:32,377 - app.utils.memory_management - INFO - Garbage collection: collected 711 objects
2025-05-29 12:14:32,377 - app.utils.memory_management - INFO - Memory after cleanup: 485.37 MB (freed 0.00 MB)
2025-05-29 12:15:34,751 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:15:34,808 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:15:34,818 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:15:34,820 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:15:34,827 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:34,833 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:15:34,836 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:15:34,836 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:15:34,838 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:15:34,839 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:15:34,840 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:15:34,841 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:15:34,842 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:15:34,844 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:15:34,846 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:15:35,109 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:15:35,139 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:15:35,140 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:15:35,141 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:15:35,146 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,148 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:15:35,148 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:15:35,153 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:15:35,153 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:15:35,161 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,173 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:15:35,173 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:15:35,174 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:15:35,174 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:15:35,181 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,182 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:15:35,183 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:15:35,183 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:15:35,183 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:15:35,197 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,198 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:15:35,200 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:15:35,201 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:15:35,202 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:15:35,215 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,218 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:15:35,237 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,246 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,255 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,262 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,268 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:35,292 - app.utils.memory_management - INFO - Memory before cleanup: 485.54 MB
2025-05-29 12:15:35,449 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:15:35,450 - app.utils.memory_management - INFO - Memory after cleanup: 485.54 MB (freed 0.00 MB)
2025-05-29 12:15:45,490 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:15:45,527 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:15:45,541 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:15:45,543 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:15:45,547 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,553 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:15:45,553 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:15:45,555 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:15:45,555 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:15:45,557 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:15:45,557 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:15:45,559 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:15:45,559 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:15:45,559 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:15:45,559 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:15:45,679 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:15:45,701 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:15:45,701 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:15:45,701 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:15:45,710 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,710 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:15:45,710 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:15:45,716 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:15:45,716 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:15:45,721 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,723 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:15:45,723 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:15:45,723 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:15:45,723 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:15:45,730 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,730 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:15:45,730 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:15:45,730 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:15:45,730 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:15:45,735 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,737 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:15:45,737 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:15:45,738 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:15:45,738 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:15:45,743 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,743 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:15:45,752 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,757 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,761 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,766 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,773 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:15:45,776 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:15:47,285 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:15:47,285 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:15:47,291 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.57
2025-05-29 12:15:47,291 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:15:47,291 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:15:47,291 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:15:50,317 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:15:52,452 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:15:52,462 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.4
2025-05-29 12:15:52,462 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.4
2025-05-29 12:15:52,462 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:15:52,463 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:15:52,463 - app.utils.error_handling - INFO - fetch_price executed in 5.17 seconds
2025-05-29 12:15:52,463 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:15:54,598 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:15:54,713 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:15:54,823 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:15:54,823 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:15:54,823 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:15:54,825 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:15:54,825 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:15:54,825 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:15:54,825 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:15:54,825 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:15:54,847 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:15:54,865 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:15:54,865 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:15:54,865 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:15:54,882 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:15:54,882 - models.predict - INFO - Current price: 81.4, Predicted scaled value: 0.7209689927101135
2025-05-29 12:15:54,882 - models.predict - INFO - Prediction for 60 minutes horizon: 81.20910080051422
2025-05-29 12:15:54,882 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:15:56,011 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:15:56,011 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:15:56,020 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.05
2025-05-29 12:15:56,020 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:15:56,021 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:15:56,021 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:15:58,963 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:16:01,092 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:16:01,100 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.4
2025-05-29 12:16:01,100 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.4
2025-05-29 12:16:01,101 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:16:01,101 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:16:01,101 - app.utils.error_handling - INFO - fetch_price executed in 5.08 seconds
2025-05-29 12:16:01,101 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:16:03,276 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:16:03,388 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:16:03,491 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:16:03,491 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:16:03,491 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:16:03,491 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:16:03,491 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:16:03,491 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:16:04,585 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:16:04,585 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:16:04,592 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.62
2025-05-29 12:16:04,592 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:16:04,593 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:16:04,593 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:16:07,514 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:16:09,650 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:16:09,659 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.4
2025-05-29 12:16:09,659 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.4
2025-05-29 12:16:09,659 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:16:09,659 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:16:09,660 - app.utils.error_handling - INFO - fetch_price executed in 5.07 seconds
2025-05-29 12:16:09,660 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:16:11,822 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:16:11,946 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:16:12,045 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:16:12,045 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:16:12,045 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:16:12,045 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:16:12,051 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:16:12,051 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:16:12,051 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:16:12,051 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:16:12,057 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:16:12,061 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:16:12,077 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:16:12,077 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:16:12,077 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:16:12,077 - models.predict - INFO - Current price: 81.4, Predicted scaled value: 0.7194341839453234
2025-05-29 12:16:12,077 - models.predict - INFO - Prediction for 60 minutes horizon: 81.12499328020373
2025-05-29 12:16:12,080 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:16:13,222 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:16:13,222 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:16:13,229 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.28
2025-05-29 12:16:13,230 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:16:13,230 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:16:13,231 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:16:16,295 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:16:18,409 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:16:18,418 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.4
2025-05-29 12:16:18,418 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.4
2025-05-29 12:16:18,418 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:16:18,419 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:16:18,419 - app.utils.error_handling - INFO - fetch_price executed in 5.19 seconds
2025-05-29 12:16:18,419 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:16:20,574 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:16:20,671 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:16:20,787 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:16:20,787 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:16:20,787 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:16:20,787 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:16:20,787 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:16:20,787 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:16:20,787 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:16:20,787 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:16:20,787 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:16:20,787 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:16:20,787 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:16:20,787 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:16:20,787 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:16:20,787 - models.predict - INFO - Current price: 81.4, Predicted scaled value: 0.7437255508108338
2025-05-29 12:16:20,787 - models.predict - INFO - Prediction for 60 minutes horizon: 82.45616018443368
2025-05-29 12:16:20,801 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:16:21,935 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:16:21,936 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:16:21,941 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.31
2025-05-29 12:16:21,941 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:16:21,941 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:16:21,941 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:16:24,698 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:16:26,863 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:16:26,874 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.4
2025-05-29 12:16:26,875 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.4
2025-05-29 12:16:26,875 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:16:26,875 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:16:26,875 - app.utils.error_handling - INFO - fetch_price executed in 4.93 seconds
2025-05-29 12:16:26,875 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:16:29,092 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:16:29,195 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:16:29,306 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:16:29,307 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-05-29 12:16:29,307 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_60min.joblib
2025-05-29 12:16:29,307 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 60
2025-05-29 12:16:29,308 - models.predict - INFO - Created fallback ensemble model with base price: 81.4
2025-05-29 12:16:29,308 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-29 12:16:29,308 - models.predict - INFO - Ensemble model already loaded
2025-05-29 12:16:29,323 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-05-29 12:16:29,323 - models.predict - INFO - Current price: 81.4, Predicted scaled value: 80.02648223873632
2025-05-29 12:16:29,323 - models.predict - WARNING - Prediction 4427.151226682749 is too far from current price 81.4, using fallback
2025-05-29 12:16:29,324 - models.predict - INFO - Prediction for 60 minutes horizon: 81.57403782781209
2025-05-29 12:16:29,330 - app.utils.memory_management - INFO - Memory before cleanup: 485.77 MB
2025-05-29 12:16:29,438 - app.utils.memory_management - INFO - Garbage collection: collected 9 objects
2025-05-29 12:16:29,438 - app.utils.memory_management - INFO - Memory after cleanup: 485.77 MB (freed 0.00 MB)
2025-05-29 12:29:02,569 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:29:02,615 - app - INFO - Found 8 stock files in data/stocks
2025-05-29 12:29:02,633 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:29:02,635 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:29:02,635 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:29:02,643 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:02,648 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:29:02,649 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:29:02,650 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:29:02,663 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:29:02,663 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:29:02,668 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:02,674 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:29:02,684 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-29 12:29:02,685 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:29:02,686 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:29:02,686 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:29:02,692 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:02,692 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:29:02,694 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-29 12:29:02,694 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:29:02,694 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:29:02,694 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:29:02,701 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:02,702 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:29:02,703 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-29 12:29:02,704 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:29:02,705 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:29:02,705 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:29:02,711 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:02,715 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:29:02,715 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:29:02,772 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:29:02,805 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:29:02,807 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:29:02,816 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:02,832 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:29:02,838 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:29:02,839 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:29:02,839 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:29:02,840 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:29:02,841 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:29:02,844 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:29:02,845 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:29:02,845 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:29:02,845 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:29:03,018 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:29:03,049 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:29:03,051 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:29:03,051 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:29:03,056 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,056 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:29:03,058 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:29:03,063 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:29:03,065 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:29:03,077 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,083 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:29:03,084 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:29:03,085 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:29:03,087 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:29:03,095 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,097 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:29:03,101 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:29:03,105 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:29:03,109 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:29:03,117 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,121 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:29:03,124 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:29:03,130 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:29:03,130 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:29:03,138 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,140 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:29:03,154 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,161 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,165 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,171 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,180 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:03,196 - app.utils.memory_management - INFO - Memory before cleanup: 483.66 MB
2025-05-29 12:29:03,395 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:29:03,396 - app.utils.memory_management - INFO - Memory after cleanup: 483.66 MB (freed 0.00 MB)
2025-05-29 12:29:06,620 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:29:06,661 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:06,661 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:29:07,835 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:29:07,836 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:29:07,846 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.74
2025-05-29 12:29:07,849 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:29:07,849 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:29:07,849 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:29:11,070 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:29:13,206 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:29:13,217 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.5
2025-05-29 12:29:13,217 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.5
2025-05-29 12:29:13,218 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:29:13,218 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:29:13,218 - app.utils.error_handling - INFO - fetch_price executed in 5.37 seconds
2025-05-29 12:29:13,219 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:29:15,358 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:29:15,479 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:29:15,581 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:29:15,581 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-05-29 12:29:15,597 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_60min.joblib
2025-05-29 12:29:15,597 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 60
2025-05-29 12:29:15,597 - models.predict - INFO - Created fallback ensemble model with base price: 81.5
2025-05-29 12:29:15,597 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-29 12:29:15,597 - models.predict - INFO - Ensemble model already loaded
2025-05-29 12:29:15,612 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-05-29 12:29:15,612 - models.predict - INFO - Current price: 81.5, Predicted scaled value: 81.70377406211756
2025-05-29 12:29:15,612 - models.predict - WARNING - Prediction 4519.066818604042 is too far from current price 81.5, using fallback
2025-05-29 12:29:15,612 - models.predict - INFO - Prediction for 60 minutes horizon: 81.65243980999877
2025-05-29 12:29:15,634 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:29:15,638 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:29:15,638 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:29:15,649 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,654 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:29:15,655 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:29:15,655 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:29:15,656 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:29:15,657 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:29:15,658 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:29:15,659 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:29:15,659 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:29:15,659 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:29:15,659 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:29:15,784 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:29:15,806 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:29:15,806 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:29:15,807 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:29:15,813 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,814 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:29:15,814 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:29:15,825 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:29:15,825 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:29:15,830 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,834 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:29:15,834 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:29:15,835 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:29:15,835 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:29:15,840 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,840 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:29:15,840 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:29:15,841 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:29:15,841 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:29:15,845 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,845 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:29:15,845 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:29:15,846 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:29:15,846 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:29:15,851 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,851 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:29:15,853 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,853 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,873 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,878 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,882 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:29:15,890 - app.utils.memory_management - INFO - Memory before cleanup: 484.54 MB
2025-05-29 12:29:16,042 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:29:16,042 - app.utils.memory_management - INFO - Memory after cleanup: 484.54 MB (freed 0.00 MB)
2025-05-29 12:30:52,656 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:30:52,727 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:30:52,741 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:30:52,747 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:52,753 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:30:52,754 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:30:52,754 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:30:52,755 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:30:52,755 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:30:52,756 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:30:52,758 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:30:52,758 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:30:52,759 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:30:52,759 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:30:52,979 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:30:53,011 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:30:53,011 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:30:53,012 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:30:53,020 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,022 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:30:53,023 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:30:53,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:30:53,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:30:53,034 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,040 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:30:53,041 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:30:53,043 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:30:53,043 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:30:53,053 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,054 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:30:53,054 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:30:53,055 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:30:53,055 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:30:53,059 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,060 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:30:53,060 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:30:53,061 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:30:53,061 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:30:53,069 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,070 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:30:53,082 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,089 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,093 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,097 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,101 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:53,109 - app.utils.memory_management - INFO - Memory before cleanup: 484.49 MB
2025-05-29 12:30:53,229 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:30:53,229 - app.utils.memory_management - INFO - Memory after cleanup: 484.49 MB (freed 0.00 MB)
2025-05-29 12:30:54,437 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:30:54,470 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-29 12:30:54,475 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,478 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:30:54,479 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:30:54,479 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:30:54,479 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:30:54,479 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:30:54,480 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:30:54,482 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:30:54,483 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:30:54,484 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:30:54,485 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:30:54,606 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:30:54,626 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:30:54,628 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:30:54,628 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:30:54,640 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,640 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:30:54,641 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:30:54,645 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:30:54,646 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:30:54,650 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,654 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:30:54,655 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:30:54,655 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:30:54,655 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:30:54,662 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,663 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:30:54,664 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:30:54,664 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:30:54,665 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:30:54,665 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,674 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:30:54,674 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:30:54,674 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:30:54,674 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:30:54,674 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,674 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:30:54,683 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,693 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,700 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,703 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,708 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:30:54,714 - app.utils.memory_management - INFO - Memory before cleanup: 484.50 MB
2025-05-29 12:30:54,832 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:30:54,833 - app.utils.memory_management - INFO - Memory after cleanup: 484.50 MB (freed 0.00 MB)
2025-05-29 12:31:00,110 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:31:00,141 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,146 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:31:00,146 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:31:00,147 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:31:00,148 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:31:00,148 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:31:00,149 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:31:00,151 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:31:00,151 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:31:00,151 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:31:00,151 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:31:00,284 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:31:00,311 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:00,311 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:00,312 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:00,319 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,322 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:00,323 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:31:00,327 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:31:00,327 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:31:00,331 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,336 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:31:00,339 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:31:00,339 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:31:00,341 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:31:00,341 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,341 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:31:00,341 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:31:00,341 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:31:00,351 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:31:00,356 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,357 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:31:00,357 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:00,357 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:00,358 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:00,362 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,362 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:00,373 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,379 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,384 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,389 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,396 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:00,410 - app.utils.memory_management - INFO - Memory before cleanup: 484.50 MB
2025-05-29 12:31:00,533 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:31:00,533 - app.utils.memory_management - INFO - Memory after cleanup: 484.50 MB (freed 0.00 MB)
2025-05-29 12:31:16,494 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:31:16,530 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,535 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:31:16,535 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:31:16,536 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:31:16,537 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:31:16,537 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:31:16,539 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:31:16,539 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:31:16,539 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:31:16,539 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:31:16,539 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:31:16,666 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:31:16,688 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:16,688 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:16,688 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:16,696 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,696 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:16,697 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:31:16,705 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:31:16,705 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:31:16,709 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,717 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:31:16,718 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:31:16,719 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:16,721 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:16,739 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,740 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:16,749 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,755 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,758 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,758 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,758 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:16,778 - app.utils.memory_management - INFO - Memory before cleanup: 484.51 MB
2025-05-29 12:31:16,896 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:31:16,896 - app.utils.memory_management - INFO - Memory after cleanup: 484.51 MB (freed 0.00 MB)
2025-05-29 12:31:22,960 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:31:23,001 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,005 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:31:23,005 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:31:23,006 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:31:23,006 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:31:23,008 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:31:23,009 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:31:23,010 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:31:23,010 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:31:23,012 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:31:23,012 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:31:23,143 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:31:23,160 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:23,160 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:23,160 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:23,179 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,179 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:23,180 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:31:23,184 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:31:23,184 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:31:23,186 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,194 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:31:23,195 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:31:23,195 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:31:23,196 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:31:23,200 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,200 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:31:23,200 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:31:23,200 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:31:23,201 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:31:23,209 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,211 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:31:23,211 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:23,212 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:23,212 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:23,217 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,217 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:23,228 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,234 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,234 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,243 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,243 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,243 - app.utils.memory_management - INFO - Memory before cleanup: 484.52 MB
2025-05-29 12:31:23,425 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:31:23,425 - app.utils.memory_management - INFO - Memory after cleanup: 484.52 MB (freed 0.00 MB)
2025-05-29 12:31:23,944 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:31:23,983 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:23,987 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:31:23,988 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:31:23,989 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:31:23,990 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:31:23,991 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:31:23,992 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:31:23,993 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:31:23,993 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:31:23,995 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:31:23,996 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:31:24,121 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:31:24,142 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:24,142 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:24,144 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:24,150 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,151 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:24,151 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:31:24,158 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:31:24,158 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:31:24,163 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:31:24,166 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:31:24,182 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,182 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:31:24,183 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:24,184 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:24,184 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:24,191 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,191 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:24,202 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,206 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,210 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,210 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,219 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,226 - app.utils.memory_management - INFO - Memory before cleanup: 484.53 MB
2025-05-29 12:31:24,381 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:31:24,381 - app.utils.memory_management - INFO - Memory after cleanup: 484.53 MB (freed 0.00 MB)
2025-05-29 12:31:24,735 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:31:24,774 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,780 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:31:24,780 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:31:24,781 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:31:24,781 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:31:24,781 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:31:24,782 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:31:24,784 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:31:24,784 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:31:24,784 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:31:24,787 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:31:24,909 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:31:24,928 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:24,928 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:24,928 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:24,928 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,928 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:24,928 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:31:24,928 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:31:24,944 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:31:24,956 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,961 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:31:24,962 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:31:24,963 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:31:24,963 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:31:24,968 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,968 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:31:24,968 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:31:24,968 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:31:24,970 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:31:24,972 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,972 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:31:24,972 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:24,972 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:24,972 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:24,981 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,981 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:24,990 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:24,995 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:25,000 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:25,004 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:25,006 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:25,018 - app.utils.memory_management - INFO - Memory before cleanup: 484.53 MB
2025-05-29 12:31:25,166 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:31:25,166 - app.utils.memory_management - INFO - Memory after cleanup: 484.53 MB (freed 0.00 MB)
2025-05-29 12:31:25,891 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:31:25,931 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:25,937 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:31:25,937 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:31:25,937 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:31:25,938 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:31:25,938 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:31:25,939 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:31:25,940 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:31:25,941 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:31:25,941 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:31:25,941 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:31:26,063 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:31:26,082 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:26,084 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:26,084 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:26,092 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,092 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:26,092 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:31:26,099 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:31:26,099 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:31:26,100 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,107 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:31:26,107 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:31:26,107 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:31:26,107 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:31:26,114 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,115 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:31:26,115 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:31:26,115 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:31:26,116 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:31:26,125 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,126 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:31:26,126 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:26,127 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:26,127 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:26,133 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,134 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:26,138 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,148 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,154 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,154 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,161 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:26,169 - app.utils.memory_management - INFO - Memory before cleanup: 484.54 MB
2025-05-29 12:31:26,282 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:31:26,282 - app.utils.memory_management - INFO - Memory after cleanup: 484.54 MB (freed 0.00 MB)
2025-05-29 12:31:35,509 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:31:35,544 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:35,554 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:31:36,703 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:31:36,703 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:31:36,717 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.28
2025-05-29 12:31:36,718 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:31:36,718 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:31:36,718 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:31:41,785 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:31:43,941 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:31:43,954 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.6
2025-05-29 12:31:43,954 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.6
2025-05-29 12:31:43,954 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:31:43,955 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:31:43,955 - app.utils.error_handling - INFO - fetch_price executed in 7.24 seconds
2025-05-29 12:31:43,957 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:31:46,119 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:31:46,235 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:31:46,349 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:31:46,351 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:31:46,351 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:31:46,351 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:31:46,351 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:31:46,373 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:31:46,495 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:31:46,605 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:31:46,605 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:31:46,605 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:31:46,605 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:31:46,605 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:31:46,605 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:31:46,605 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:31:46,605 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:31:46,638 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:31:46,655 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:31:46,665 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:31:46,666 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:31:46,670 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:31:46,671 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.732374107837677
2025-05-29 12:31:46,672 - models.predict - INFO - Prediction for 60 minutes horizon: 81.8341011095047
2025-05-29 12:31:46,688 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:31:46,799 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:31:46,916 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:31:46,916 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:31:46,921 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:31:46,921 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:31:46,921 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:31:46,921 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:31:46,921 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:31:46,923 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:31:46,927 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:31:46,933 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:31:46,948 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:31:46,949 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:31:46,949 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:31:46,949 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.7375842923169725
2025-05-29 12:31:46,949 - models.predict - INFO - Prediction for 60 minutes horizon: 82.1196192189701
2025-05-29 12:31:46,973 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:31:47,084 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:31:47,183 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:31:47,198 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-29 12:31:47,199 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-29 12:31:47,199 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-29 12:31:47,200 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-29 12:31:47,200 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:31:47,200 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-29 12:31:47,200 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:31:47,216 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:31:47,216 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:31:47,216 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:31:47,216 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:31:47,216 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:31:47,216 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.49873361807365774
2025-05-29 12:31:47,216 - models.predict - INFO - Prediction for 60 minutes horizon: 69.03060227043645
2025-05-29 12:31:47,245 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:31:47,350 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:31:47,466 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:31:47,466 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:31:47,466 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:31:47,466 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:31:47,466 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:31:47,466 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:31:47,466 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:31:47,466 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:31:47,466 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:31:47,482 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:31:47,482 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:31:47,483 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:31:47,483 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:31:47,483 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.7458258227707061
2025-05-29 12:31:47,483 - models.predict - INFO - Prediction for 60 minutes horizon: 82.57125508783469
2025-05-29 12:31:47,483 - models.predict - INFO - Current price for COMI: 81.6
2025-05-29 12:31:47,483 - models.predict - INFO - Prophet prediction for 60 minutes: 81.57279792978795
2025-05-29 12:31:47,509 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:31:47,631 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:31:47,745 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:31:47,745 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-29 12:31:47,745 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-29 12:31:47,745 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-29 12:31:47,745 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-29 12:31:47,745 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:31:47,750 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:31:47,750 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:31:47,790 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:31:47,816 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:31:47,817 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:31:47,821 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:31:47,821 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.732374107837677
2025-05-29 12:31:47,822 - models.predict - INFO - Prediction for 60 minutes horizon: 81.8341011095047
2025-05-29 12:31:47,823 - app.components.advanced_prediction - WARNING - No prediction found for horizon 60 (minutes)
2025-05-29 12:31:47,840 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-29 12:31:47,858 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:31:47,858 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:31:47,858 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:31:47,859 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:31:47,860 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:31:47,860 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:31:47,862 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:31:47,862 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:31:47,862 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:31:47,862 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:31:47,980 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:31:48,001 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:48,002 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:48,002 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:48,010 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,011 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:48,012 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:31:48,019 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:31:48,019 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:31:48,023 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,027 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:31:48,028 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:31:48,028 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:31:48,028 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:31:48,033 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,034 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:31:48,035 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:31:48,035 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:31:48,035 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:31:48,040 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,040 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:31:48,040 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:31:48,040 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:31:48,040 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:31:48,044 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,044 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:31:48,055 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,059 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,064 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,069 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,073 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:31:48,080 - app.utils.memory_management - INFO - Memory before cleanup: 484.71 MB
2025-05-29 12:31:48,208 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:31:48,229 - app.utils.memory_management - INFO - Memory after cleanup: 484.71 MB (freed 0.00 MB)
2025-05-29 12:33:01,422 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:33:01,503 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,510 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:33:01,512 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:33:01,512 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:33:01,513 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:33:01,514 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:33:01,515 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:33:01,516 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:33:01,517 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:33:01,517 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:33:01,518 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:33:01,734 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:33:01,768 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:33:01,769 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:33:01,769 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:33:01,777 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,778 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:33:01,780 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:33:01,785 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:33:01,785 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:33:01,800 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,810 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:33:01,811 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:33:01,812 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:33:01,812 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:33:01,817 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,817 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:33:01,818 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:33:01,818 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:33:01,818 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:33:01,824 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,825 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:33:01,825 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:33:01,826 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:33:01,826 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:33:01,830 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,831 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:33:01,840 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,840 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,853 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,853 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,853 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:01,853 - app.utils.memory_management - INFO - Memory before cleanup: 484.66 MB
2025-05-29 12:33:01,992 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:33:01,993 - app.utils.memory_management - INFO - Memory after cleanup: 484.66 MB (freed 0.00 MB)
2025-05-29 12:33:17,654 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:33:17,687 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:17,698 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:33:17,698 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:33:17,698 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:33:17,698 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:33:17,698 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:33:17,698 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:33:17,698 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:33:17,698 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:33:17,703 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:33:17,703 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:33:17,828 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:33:17,839 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:33:18,954 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:33:18,955 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:33:18,979 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.21
2025-05-29 12:33:18,979 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:33:18,980 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:33:18,980 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:33:22,206 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:33:24,380 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:33:24,388 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.6
2025-05-29 12:33:24,388 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.6
2025-05-29 12:33:24,389 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:33:24,389 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:33:24,389 - app.utils.error_handling - INFO - fetch_price executed in 5.41 seconds
2025-05-29 12:33:24,392 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:33:24,392 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:33:24,393 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:33:24,394 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:33:24,395 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:33:24,396 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:33:24,397 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:33:24,397 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:33:24,397 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:33:24,539 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:33:24,652 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:33:24,774 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:33:24,775 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:33:24,775 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:33:24,775 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:33:24,776 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:33:24,800 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:33:24,940 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:33:25,057 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:33:25,059 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:33:25,059 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:33:25,060 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:33:25,060 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:33:25,061 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:33:25,062 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:33:25,062 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:33:25,089 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:33:25,115 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:33:25,116 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:33:25,116 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:33:25,120 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:33:25,120 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.732374107837677
2025-05-29 12:33:25,121 - models.predict - INFO - Prediction for 60 minutes horizon: 81.8341011095047
2025-05-29 12:33:25,140 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:33:25,253 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:33:25,369 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:33:25,371 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:33:25,371 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:33:25,371 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:33:25,372 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:33:25,373 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:33:25,373 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:33:25,373 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:33:25,381 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:33:25,385 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:33:25,401 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:33:25,401 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:33:25,402 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:33:25,402 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.7375842923169725
2025-05-29 12:33:25,403 - models.predict - INFO - Prediction for 60 minutes horizon: 82.1196192189701
2025-05-29 12:33:25,422 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:33:25,537 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:33:25,653 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:33:25,654 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-29 12:33:25,654 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-29 12:33:25,654 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-29 12:33:25,655 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-29 12:33:25,656 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:33:25,656 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-29 12:33:25,656 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:33:25,657 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:33:25,658 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:33:25,659 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:33:25,659 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:33:25,659 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:33:25,660 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.49873361807365774
2025-05-29 12:33:25,660 - models.predict - INFO - Prediction for 60 minutes horizon: 69.03060227043645
2025-05-29 12:33:25,683 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:33:25,796 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:33:25,912 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:33:25,913 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:33:25,914 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:33:25,914 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:33:25,915 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:33:25,915 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:33:25,917 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:33:25,917 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:33:25,917 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:33:25,918 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:33:25,919 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:33:25,919 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:33:25,919 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:33:25,920 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.7458258227707061
2025-05-29 12:33:25,920 - models.predict - INFO - Prediction for 60 minutes horizon: 82.57125508783469
2025-05-29 12:33:25,922 - models.predict - INFO - Current price for COMI: 81.6
2025-05-29 12:33:25,922 - models.predict - INFO - Prophet prediction for 60 minutes: 81.5715231282193
2025-05-29 12:33:25,942 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:33:26,062 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:33:26,176 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:33:26,178 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-29 12:33:26,178 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-29 12:33:26,178 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-29 12:33:26,179 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-29 12:33:26,180 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:33:26,180 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:33:26,180 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:33:26,207 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:33:26,235 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:33:26,235 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:33:26,239 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:33:26,239 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.732374107837677
2025-05-29 12:33:26,239 - models.predict - INFO - Prediction for 60 minutes horizon: 81.8341011095047
2025-05-29 12:33:26,253 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-29 12:33:26,272 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:33:28,411 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:33:28,411 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:33:28,411 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:33:28,432 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,432 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:33:28,432 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:33:28,432 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:33:28,432 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:33:28,443 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:33:28,446 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:33:28,459 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,459 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:33:28,459 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:33:28,460 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:33:28,460 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:33:28,464 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,464 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:33:28,464 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,481 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,485 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,489 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,493 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:33:28,500 - app.utils.memory_management - INFO - Memory before cleanup: 469.39 MB
2025-05-29 12:33:28,609 - app.utils.memory_management - INFO - Garbage collection: collected 644 objects
2025-05-29 12:33:28,609 - app.utils.memory_management - INFO - Memory after cleanup: 469.39 MB (freed 0.00 MB)
2025-05-29 12:35:23,199 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:35:23,237 - app - INFO - Found 8 stock files in data/stocks
2025-05-29 12:35:23,254 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:35:23,254 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:35:23,254 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:35:23,261 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:23,261 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:35:23,261 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:35:23,263 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:35:23,267 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:35:23,269 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:35:23,275 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:23,283 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:35:23,288 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-29 12:35:23,289 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:35:23,290 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:35:23,291 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:35:23,298 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:23,299 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:35:23,299 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-29 12:35:23,300 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:35:23,300 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:35:23,301 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:35:23,305 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:23,306 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:35:23,307 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-29 12:35:23,310 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:35:23,311 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:35:23,312 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:35:23,320 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:23,320 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:35:23,321 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:35:23,361 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:23,371 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:35:23,372 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:35:23,373 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:35:23,374 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:35:23,374 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:35:23,378 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:35:23,380 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:35:23,380 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:35:23,380 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:35:23,381 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:35:23,556 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:35:23,597 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:35:23,753 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:35:23,870 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: -0.02 MB, VMS: -0.04 MB, Percent: -0.00%, Execution time: 0.00s
2025-05-29 12:35:23,872 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:35:23,872 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:35:23,872 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:35:23,873 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:35:23,894 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:35:24,003 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:35:24,115 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:35:24,117 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:35:24,118 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:35:24,118 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:35:24,118 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:35:24,120 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:35:24,120 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:35:24,120 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:35:24,149 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:35:24,173 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:35:24,173 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:35:24,174 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:35:24,178 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:35:24,180 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.7347171747684479
2025-05-29 12:35:24,180 - models.predict - INFO - Prediction for 60 minutes horizon: 81.96250117731094
2025-05-29 12:35:24,201 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:35:24,313 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:35:24,426 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:35:24,426 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:35:24,426 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:35:24,426 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:35:24,426 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:35:24,426 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:35:24,426 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:35:24,426 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:35:24,426 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:35:24,441 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:35:24,445 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:35:24,445 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:35:24,445 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:35:24,445 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.7316217933320386
2025-05-29 12:35:24,458 - models.predict - INFO - Prediction for 60 minutes horizon: 81.79287427459572
2025-05-29 12:35:24,478 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:35:24,588 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:35:24,692 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:35:24,692 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-29 12:35:24,692 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-29 12:35:24,692 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-29 12:35:24,692 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-29 12:35:24,692 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:35:24,692 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-29 12:35:24,692 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:35:24,708 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:35:24,708 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:35:24,708 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:35:24,708 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:35:24,708 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:35:24,708 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.4954982985870937
2025-05-29 12:35:24,708 - models.predict - INFO - Prediction for 60 minutes horizon: 68.85330676257273
2025-05-29 12:35:24,724 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:35:24,837 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:35:24,940 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:35:24,940 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:35:24,940 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:35:24,940 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:35:24,940 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:35:24,940 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:35:24,940 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:35:24,940 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:35:24,940 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:35:24,940 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:35:24,940 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:35:24,940 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:35:24,940 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:35:24,940 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.7118602418609664
2025-05-29 12:35:24,940 - models.predict - INFO - Prediction for 60 minutes horizon: 80.70994125398096
2025-05-29 12:35:24,955 - models.predict - INFO - Current price for COMI: 81.6
2025-05-29 12:35:24,955 - models.predict - INFO - Prophet prediction for 60 minutes: 81.69750289007897
2025-05-29 12:35:24,972 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:35:25,079 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:35:25,189 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:35:25,189 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-29 12:35:25,189 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-29 12:35:25,189 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-29 12:35:25,189 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-29 12:35:25,189 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:35:25,189 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:35:25,189 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:35:25,205 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:35:25,243 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:35:25,243 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:35:25,243 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:35:25,243 - models.predict - INFO - Current price: 81.6, Predicted scaled value: 0.7347171747684479
2025-05-29 12:35:25,243 - models.predict - INFO - Prediction for 60 minutes horizon: 81.96250117731094
2025-05-29 12:35:25,294 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:35:25,294 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:35:25,294 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:35:25,309 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,309 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:35:25,311 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:35:25,315 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:35:25,315 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:35:25,320 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,323 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:35:25,325 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:35:25,325 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:35:25,325 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:35:25,327 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:35:25,341 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,341 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:35:25,345 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,345 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,357 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,364 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,365 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:25,365 - app.utils.memory_management - INFO - Memory before cleanup: 469.37 MB
2025-05-29 12:35:25,497 - app.utils.memory_management - INFO - Garbage collection: collected 507 objects
2025-05-29 12:35:25,498 - app.utils.memory_management - INFO - Memory after cleanup: 469.37 MB (freed 0.00 MB)
2025-05-29 12:35:29,512 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:35:29,566 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:29,574 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:35:30,771 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:35:30,771 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:35:30,779 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.74
2025-05-29 12:35:30,779 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:35:30,780 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:35:30,780 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:35:34,306 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:35:36,445 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:35:36,454 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.7
2025-05-29 12:35:36,454 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.7
2025-05-29 12:35:36,455 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:35:36,455 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:35:36,455 - app.utils.error_handling - INFO - fetch_price executed in 5.68 seconds
2025-05-29 12:35:36,455 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:35:38,594 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:35:38,706 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:35:38,824 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:35:38,824 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-05-29 12:35:38,824 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_60min.joblib
2025-05-29 12:35:38,824 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 60
2025-05-29 12:35:38,824 - models.predict - INFO - Created fallback ensemble model with base price: 81.7
2025-05-29 12:35:38,824 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-29 12:35:38,824 - models.predict - INFO - Ensemble model already loaded
2025-05-29 12:35:38,840 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-05-29 12:35:38,840 - models.predict - INFO - Current price: 81.7, Predicted scaled value: 82.15548866210248
2025-05-29 12:35:38,840 - models.predict - WARNING - Prediction 4543.820778683215 is too far from current price 81.7, using fallback
2025-05-29 12:35:38,840 - models.predict - INFO - Prediction for 60 minutes horizon: 82.15226927427186
2025-05-29 12:35:38,874 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:38,885 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:35:38,885 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:35:38,885 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:35:38,885 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:35:38,885 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:35:38,885 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:35:38,885 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:35:38,885 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:35:38,889 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:35:38,890 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:35:39,007 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:35:39,026 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:35:39,027 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:35:39,027 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:35:39,033 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,033 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:35:39,034 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:35:39,044 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:35:39,045 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:35:39,049 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,054 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:35:39,054 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:35:39,054 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:35:39,055 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:35:39,059 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,059 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:35:39,060 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:35:39,060 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:35:39,060 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:35:39,064 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,064 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:35:39,065 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:35:39,065 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:35:39,065 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:35:39,070 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,070 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:35:39,080 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,084 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,088 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,095 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,099 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:35:39,105 - app.utils.memory_management - INFO - Memory before cleanup: 469.41 MB
2025-05-29 12:35:39,229 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:35:39,229 - app.utils.memory_management - INFO - Memory after cleanup: 469.41 MB (freed 0.00 MB)
2025-05-29 12:36:02,610 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:36:02,680 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,690 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:36:02,690 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:36:02,692 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:36:02,692 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:36:02,692 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:36:02,694 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:36:02,694 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:36:02,696 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:36:02,696 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:36:02,698 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:36:02,852 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:36:02,884 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:36:02,885 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:36:02,885 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:36:02,893 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,894 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:36:02,894 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:36:02,900 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:36:02,902 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:36:02,906 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,911 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:36:02,913 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:36:02,914 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:36:02,914 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:36:02,926 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,927 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:36:02,927 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:36:02,927 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:36:02,928 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:36:02,933 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,934 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:36:02,934 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:36:02,935 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:36:02,935 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:36:02,939 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,939 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:36:02,950 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,954 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,958 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,964 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,969 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:02,976 - app.utils.memory_management - INFO - Memory before cleanup: 469.36 MB
2025-05-29 12:36:03,091 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:36:03,129 - app.utils.memory_management - INFO - Memory after cleanup: 469.36 MB (freed 0.00 MB)
2025-05-29 12:36:03,522 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:36:03,562 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,567 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:36:03,568 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:36:03,568 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:36:03,568 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:36:03,569 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:36:03,569 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:36:03,571 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:36:03,571 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:36:03,572 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:36:03,572 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:36:03,692 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:36:03,713 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:36:03,713 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:36:03,715 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:36:03,723 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,724 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:36:03,725 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:36:03,729 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:36:03,729 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:36:03,734 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,738 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:36:03,738 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:36:03,738 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:36:03,740 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:36:03,740 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,740 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:36:03,740 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:36:03,740 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:36:03,740 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:36:03,756 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,757 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:36:03,757 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:36:03,758 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:36:03,758 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:36:03,764 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,765 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:36:03,775 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,779 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,784 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,788 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,793 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:03,800 - app.utils.memory_management - INFO - Memory before cleanup: 469.37 MB
2025-05-29 12:36:03,922 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:36:03,923 - app.utils.memory_management - INFO - Memory after cleanup: 469.37 MB (freed 0.00 MB)
2025-05-29 12:36:07,139 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:36:07,174 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:07,181 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:36:07,181 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:36:07,181 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:36:07,181 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:36:07,183 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:36:07,184 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:36:07,185 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:36:07,186 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:36:07,186 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:36:07,186 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:36:07,306 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:36:07,435 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:36:08,571 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:36:08,572 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:36:08,587 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.63
2025-05-29 12:36:08,588 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:36:08,588 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:36:08,588 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:36:11,768 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:36:13,895 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:36:13,906 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.62
2025-05-29 12:36:13,907 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.62
2025-05-29 12:36:13,907 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:36:13,908 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:36:13,908 - app.utils.error_handling - INFO - fetch_price executed in 5.32 seconds
2025-05-29 12:36:13,910 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:36:16,048 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,049 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-05-29 12:36:16,049 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-05-29 12:36:16,049 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-05-29 12:36:16,145 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 79.90054253412691
2025-05-29 12:36:16,148 - app.models.predict - INFO - Prediction completed in 0.10 seconds
2025-05-29 12:36:16,148 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-05-29 12:36:16,148 - app.models.predict - INFO - Using specified model type: lstm
2025-05-29 12:36:16,209 - app.models.predict - INFO - Prediction completed in 0.06 seconds
2025-05-29 12:36:16,209 - app.models.predict - INFO - Using specified model type: bilstm
2025-05-29 12:36:16,242 - app.models.predict - INFO - Prediction completed in 0.03 seconds
2025-05-29 12:36:16,242 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-05-29 12:36:16,242 - app.components.enhanced_prediction - INFO - Trend analysis for COMI: down (strength: 0.10)
2025-05-29 12:36:16,260 - app.utils.performance - WARNING - No performance data found for COMI
2025-05-29 12:36:16,307 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:36:16,310 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:36:16,310 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:36:16,310 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,310 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:36:16,310 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:36:16,321 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:36:16,321 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:36:16,321 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:36:16,326 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:36:16,343 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,344 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:36:16,344 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:36:16,346 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:36:16,346 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:36:16,350 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,350 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:36:16,360 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,365 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,369 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,373 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,378 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:36:16,383 - app.utils.memory_management - INFO - Memory before cleanup: 469.44 MB
2025-05-29 12:36:16,502 - app.utils.memory_management - INFO - Garbage collection: collected 960 objects
2025-05-29 12:36:16,502 - app.utils.memory_management - INFO - Memory after cleanup: 469.44 MB (freed 0.00 MB)
2025-05-29 12:39:13,154 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:39:13,225 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,235 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:39:13,238 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:39:13,241 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:39:13,242 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:39:13,242 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:39:13,243 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:39:13,247 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:39:13,247 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:39:13,249 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:39:13,253 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:39:13,413 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:39:13,442 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:39:13,442 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:39:13,442 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:39:13,450 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,451 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:39:13,451 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:39:13,456 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:39:13,456 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:39:13,461 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,472 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:39:13,473 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:39:13,474 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:39:13,475 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:39:13,482 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,482 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:39:13,483 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:39:13,483 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:39:13,484 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:39:13,488 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,488 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:39:13,488 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:39:13,488 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:39:13,488 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:39:13,494 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,496 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:39:13,506 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,511 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,515 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,519 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,523 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:39:13,527 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:39:14,661 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:39:14,662 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:39:14,670 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.69
2025-05-29 12:39:14,670 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:39:14,671 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:39:14,671 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:39:18,669 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:39:20,812 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:39:20,821 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.8
2025-05-29 12:39:20,821 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.8
2025-05-29 12:39:20,821 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:39:20,821 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:39:20,823 - app.utils.error_handling - INFO - fetch_price executed in 6.15 seconds
2025-05-29 12:39:20,823 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:39:22,977 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:39:23,092 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:39:23,206 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:39:23,206 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:39:23,206 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:39:23,206 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:39:23,206 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:39:23,206 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:39:23,206 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:39:23,206 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:39:23,243 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:39:23,261 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:39:23,261 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:39:23,261 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:39:23,275 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:39:23,275 - models.predict - INFO - Current price: 81.8, Predicted scaled value: 0.7387335979938507
2025-05-29 12:39:23,275 - models.predict - INFO - Prediction for 60 minutes horizon: 82.18260117006301
2025-05-29 12:39:23,275 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:39:24,355 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:39:24,356 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:39:24,369 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.34
2025-05-29 12:39:24,370 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:39:24,370 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:39:24,370 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:39:29,803 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:39:31,939 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:39:31,948 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.8
2025-05-29 12:39:31,949 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.8
2025-05-29 12:39:31,949 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:39:31,949 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:39:31,950 - app.utils.error_handling - INFO - fetch_price executed in 7.58 seconds
2025-05-29 12:39:31,950 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:39:34,103 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:39:34,222 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:39:34,335 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:39:34,336 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:39:34,337 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:39:34,337 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:39:34,337 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:39:34,339 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:39:35,469 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:39:35,470 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:39:35,484 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.69
2025-05-29 12:39:35,486 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:39:35,487 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:39:35,487 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:39:38,457 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:39:40,611 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:39:40,618 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.79
2025-05-29 12:39:40,619 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.79
2025-05-29 12:39:40,619 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:39:40,619 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:39:40,619 - app.utils.error_handling - INFO - fetch_price executed in 5.13 seconds
2025-05-29 12:39:40,620 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:39:43,848 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:39:43,963 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:39:44,079 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:39:44,079 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:39:44,079 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:39:44,079 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:39:44,079 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:39:44,079 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:39:44,079 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:39:44,079 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:39:44,079 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:39:44,095 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:39:44,111 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:39:44,112 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:39:44,113 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:39:44,113 - models.predict - INFO - Current price: 81.79, Predicted scaled value: 0.7375842923169725
2025-05-29 12:39:44,113 - models.predict - INFO - Prediction for 60 minutes horizon: 82.1196192189701
2025-05-29 12:39:44,113 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:39:45,187 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:39:45,188 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:39:45,200 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.63
2025-05-29 12:39:45,201 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:39:45,201 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:39:45,201 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:39:48,520 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:39:50,638 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:39:50,647 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.79
2025-05-29 12:39:50,647 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.79
2025-05-29 12:39:50,647 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:39:50,648 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:39:50,648 - app.utils.error_handling - INFO - fetch_price executed in 5.45 seconds
2025-05-29 12:39:50,648 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:39:52,809 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:39:52,918 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:39:53,038 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:39:53,038 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:39:53,038 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:39:53,038 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:39:53,038 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:39:53,038 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:39:53,038 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:39:53,038 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:39:53,038 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:39:53,038 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:39:53,038 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:39:53,038 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:39:53,038 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:39:53,038 - models.predict - INFO - Current price: 81.79, Predicted scaled value: 0.7478210811325854
2025-05-29 12:39:53,038 - models.predict - INFO - Prediction for 60 minutes horizon: 82.68059524606568
2025-05-29 12:39:53,054 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:39:54,114 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:39:54,114 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:39:54,128 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.41
2025-05-29 12:39:54,129 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:39:54,129 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:39:54,129 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:39:59,268 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:40:01,467 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:40:01,478 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.79
2025-05-29 12:40:01,478 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.79
2025-05-29 12:40:01,478 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:40:01,478 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:40:01,478 - app.utils.error_handling - INFO - fetch_price executed in 7.35 seconds
2025-05-29 12:40:01,479 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:40:03,645 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:40:03,763 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:40:03,881 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:40:03,881 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-05-29 12:40:03,881 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_60min.joblib
2025-05-29 12:40:03,881 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 60
2025-05-29 12:40:03,881 - models.predict - INFO - Created fallback ensemble model with base price: 81.79
2025-05-29 12:40:03,881 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-29 12:40:03,881 - models.predict - INFO - Ensemble model already loaded
2025-05-29 12:40:03,897 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-05-29 12:40:03,897 - models.predict - INFO - Current price: 81.79, Predicted scaled value: 81.05856308543274
2025-05-29 12:40:03,897 - models.predict - WARNING - Prediction 4483.709257081713 is too far from current price 81.79, using fallback
2025-05-29 12:40:03,897 - models.predict - INFO - Prediction for 60 minutes horizon: 82.9930634730477
2025-05-29 12:40:03,916 - app.utils.memory_management - INFO - Memory before cleanup: 449.57 MB
2025-05-29 12:40:04,013 - app.utils.memory_management - INFO - Garbage collection: collected 9 objects
2025-05-29 12:40:04,013 - app.utils.memory_management - INFO - Memory after cleanup: 449.57 MB (freed 0.00 MB)
2025-05-29 12:46:36,075 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:46:36,278 - app - INFO - Found 8 stock files in data/stocks
2025-05-29 12:46:36,330 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:46:36,330 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:46:36,330 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:46:36,337 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:36,338 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:46:36,338 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:46:36,338 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:46:36,348 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:46:36,349 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:46:36,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:36,362 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:46:36,368 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-29 12:46:36,369 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:46:36,369 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:46:36,369 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:46:36,376 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:36,376 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:46:36,377 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-29 12:46:36,377 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:46:36,378 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:46:36,378 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:46:36,382 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:36,383 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:46:36,383 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-29 12:46:36,384 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:46:36,385 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:46:36,385 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:46:36,393 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:36,394 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:46:36,397 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:46:36,421 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:36,435 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 12:46:37,822 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 12:46:37,822 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 12:46:37,844 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.66
2025-05-29 12:46:37,845 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 12:46:37,845 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 12:46:37,845 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 12:46:43,473 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 12:46:45,592 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 12:46:45,600 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.64
2025-05-29 12:46:45,602 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.64
2025-05-29 12:46:45,602 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 12:46:45,602 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 12:46:45,603 - app.utils.error_handling - INFO - fetch_price executed in 7.76 seconds
2025-05-29 12:46:45,606 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 12:46:47,947 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:46:48,114 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:46:48,246 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-29 12:46:48,246 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 12:46:48,246 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:46:48,246 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 12:46:48,246 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 12:46:48,296 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:46:48,421 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:46:48,534 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:46:48,536 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 12:46:48,536 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 12:46:48,536 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 12:46:48,536 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 12:46:48,537 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:46:48,537 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 12:46:48,538 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:46:48,571 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 12:46:48,596 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:46:48,596 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:46:48,596 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:46:48,601 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:46:48,601 - models.predict - INFO - Current price: 81.64, Predicted scaled value: 0.7368741083145142
2025-05-29 12:46:48,603 - models.predict - INFO - Prediction for 60 minutes horizon: 82.08070113563538
2025-05-29 12:46:48,628 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:46:48,772 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:46:48,895 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:46:48,896 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 12:46:48,896 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 12:46:48,896 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 12:46:48,897 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 12:46:48,898 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:46:48,898 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 12:46:48,898 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:46:48,916 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 12:46:48,920 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:46:48,937 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 12:46:48,937 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:46:48,937 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:46:48,937 - models.predict - INFO - Current price: 81.64, Predicted scaled value: 0.7375842923169725
2025-05-29 12:46:48,937 - models.predict - INFO - Prediction for 60 minutes horizon: 82.1196192189701
2025-05-29 12:46:48,965 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:46:49,092 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:46:49,210 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:46:49,210 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-29 12:46:49,210 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-29 12:46:49,210 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-29 12:46:49,214 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-29 12:46:49,214 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:46:49,214 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-29 12:46:49,214 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:46:49,224 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-29 12:46:49,225 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:46:49,225 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-29 12:46:49,226 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:46:49,226 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:46:49,226 - models.predict - INFO - Current price: 81.64, Predicted scaled value: 0.49873443214127017
2025-05-29 12:46:49,228 - models.predict - INFO - Prediction for 60 minutes horizon: 69.03064688134161
2025-05-29 12:46:49,251 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:46:49,378 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:46:49,489 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:46:49,491 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-29 12:46:49,491 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-29 12:46:49,491 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-29 12:46:49,491 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-29 12:46:49,492 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:46:49,492 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-29 12:46:49,493 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:46:49,499 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-29 12:46:49,500 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:46:49,500 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-29 12:46:49,500 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 12:46:49,500 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:46:49,501 - models.predict - INFO - Current price: 81.64, Predicted scaled value: 0.7462458771626808
2025-05-29 12:46:49,501 - models.predict - INFO - Prediction for 60 minutes horizon: 82.59427406851489
2025-05-29 12:46:49,503 - models.predict - INFO - Current price for COMI: 81.64
2025-05-29 12:46:49,504 - models.predict - INFO - Prophet prediction for 60 minutes: 81.73896517552038
2025-05-29 12:46:49,527 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 12:46:49,637 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 12:46:49,757 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 12:46:49,759 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-29 12:46:49,759 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-29 12:46:49,759 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-29 12:46:49,759 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-29 12:46:49,761 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 12:46:49,761 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:46:49,761 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:46:49,794 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-29 12:46:49,814 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 12:46:49,814 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-29 12:46:49,814 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 12:46:49,814 - models.predict - INFO - Current price: 81.64, Predicted scaled value: 0.7368741083145142
2025-05-29 12:46:49,814 - models.predict - INFO - Prediction for 60 minutes horizon: 82.08070113563538
2025-05-29 12:46:49,814 - app.components.advanced_prediction - WARNING - No prediction found for horizon 60 (minutes)
2025-05-29 12:46:49,846 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-29 12:46:49,867 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:46:49,867 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:46:49,867 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:46:49,869 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:46:49,869 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:46:49,871 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:46:49,871 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:46:49,873 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:46:49,873 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:46:49,873 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:46:49,999 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:46:50,031 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:46:50,031 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:46:50,031 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:46:50,040 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,041 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:46:50,041 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:46:50,045 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:46:50,046 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:46:50,051 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,057 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:46:50,058 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:46:50,059 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:46:50,059 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:46:50,063 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,064 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:46:50,064 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:46:50,065 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:46:50,065 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:46:50,070 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,071 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:46:50,071 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:46:50,072 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:46:50,072 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:46:50,077 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,077 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:46:50,105 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,111 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,128 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,132 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,138 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:46:50,145 - app.utils.memory_management - INFO - Memory before cleanup: 434.98 MB
2025-05-29 12:46:50,281 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:46:50,281 - app.utils.memory_management - INFO - Memory after cleanup: 434.98 MB (freed 0.00 MB)
2025-05-29 12:52:11,571 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:52:11,609 - app - INFO - Found 8 stock files in data/stocks
2025-05-29 12:52:11,623 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:52:11,623 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:52:11,625 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:52:11,629 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:11,631 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:52:11,631 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:52:11,631 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:52:11,637 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:52:11,637 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:52:11,643 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:11,649 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:52:11,655 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-05-29 12:52:11,655 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:52:11,655 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:52:11,657 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:52:11,668 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:11,670 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:52:11,672 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-05-29 12:52:11,672 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:52:11,672 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:52:11,672 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:52:11,688 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:11,695 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:52:11,695 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-05-29 12:52:11,696 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:52:11,697 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:52:11,697 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:52:11,703 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:11,704 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:52:11,704 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-05-29 12:52:11,741 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:11,751 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-05-29 12:52:11,752 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-05-29 12:52:11,752 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-05-29 12:52:11,753 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-05-29 12:52:11,753 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-05-29 12:52:11,754 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-05-29 12:52:11,755 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-05-29 12:52:11,756 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-05-29 12:52:11,757 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-05-29 12:52:11,757 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-05-29 12:52:11,969 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-05-29 12:52:12,000 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:52:12,000 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:52:12,000 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:52:12,000 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,010 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:52:12,010 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-29 12:52:12,017 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-29 12:52:12,017 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-29 12:52:12,028 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,033 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-05-29 12:52:12,033 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-05-29 12:52:12,033 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-05-29 12:52:12,033 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-05-29 12:52:12,040 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,040 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-05-29 12:52:12,040 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-05-29 12:52:12,040 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-05-29 12:52:12,040 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-05-29 12:52:12,044 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,044 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-05-29 12:52:12,044 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-29 12:52:12,044 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-05-29 12:52:12,044 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-29 12:52:12,049 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,049 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-05-29 12:52:12,059 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,059 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,059 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,075 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,075 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-05-29 12:52:12,075 - app.utils.memory_management - INFO - Memory before cleanup: 425.67 MB
2025-05-29 12:52:12,207 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-29 12:52:12,207 - app.utils.memory_management - INFO - Memory after cleanup: 425.66 MB (freed 0.02 MB)
2025-05-29 12:52:12,331 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:52:12,410 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-25 00:00:00 (within 2.2 hours of target)
2025-05-29 12:52:12,420 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-05-29 12:52:12,431 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-05-29 12:52:12,443 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-05-29 12:52:12,456 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-25 00:00:00 (within 7.4 hours of target)
2025-05-29 12:52:12,466 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-25 00:00:00 (within 7.2 hours of target)
2025-05-29 12:52:12,483 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-25 00:00:00 (within 7.0 hours of target)
2025-05-29 12:52:12,483 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-25 00:00:00 (within 6.5 hours of target)
2025-05-29 12:52:12,501 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-25 00:00:00 (within 7.2 hours of target)
2025-05-29 12:52:12,518 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-25 00:00:00 (within 6.9 hours of target)
2025-05-29 12:52:12,529 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-25 00:00:00 (within 6.4 hours of target)
2025-05-29 12:52:12,541 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 11.5 hours of target)
2025-05-29 12:52:12,553 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 11.3 hours of target)
2025-05-29 12:52:12,564 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 11.1 hours of target)
2025-05-29 12:52:12,574 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 10.6 hours of target)
2025-05-29 12:52:12,589 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 11.3 hours of target)
2025-05-29 12:52:12,601 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 11.0 hours of target)
2025-05-29 12:52:12,606 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 10.5 hours of target)
2025-05-29 12:52:12,621 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.4 hours of target)
2025-05-29 12:52:12,621 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.3 hours of target)
2025-05-29 12:52:12,645 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.0 hours of target)
2025-05-29 12:52:12,653 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 8.5 hours of target)
2025-05-29 12:52:12,669 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.3 hours of target)
2025-05-29 12:52:12,679 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 8.5 hours of target)
2025-05-29 12:52:12,693 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.0 hours of target)
2025-05-29 12:52:12,701 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.4 hours of target)
2025-05-29 12:52:12,715 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.2 hours of target)
2025-05-29 12:52:12,715 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.0 hours of target)
2025-05-29 12:52:12,731 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 8.5 hours of target)
2025-05-29 12:52:12,748 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.2 hours of target)
2025-05-29 12:52:12,748 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 8.5 hours of target)
2025-05-29 12:52:12,762 - app.components.performance_metrics - INFO - Found price 79.9 for COMI at 2025-05-26 00:00:00 (within 9.0 hours of target)
2025-05-29 12:52:12,780 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 11.9 hours of target)
2025-05-29 12:52:12,783 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 11.7 hours of target)
2025-05-29 12:52:12,793 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 11.4 hours of target)
2025-05-29 12:52:12,809 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 10.9 hours of target)
2025-05-29 12:52:12,809 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 11.6 hours of target)
2025-05-29 12:52:12,825 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 11.4 hours of target)
2025-05-29 12:52:12,841 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 10.9 hours of target)
2025-05-29 12:52:12,843 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.9 hours of target)
2025-05-29 12:52:12,862 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.7 hours of target)
2025-05-29 12:52:12,866 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.5 hours of target)
2025-05-29 12:52:12,882 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.0 hours of target)
2025-05-29 12:52:12,882 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.9 hours of target)
2025-05-29 12:52:12,901 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.7 hours of target)
2025-05-29 12:52:12,914 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.5 hours of target)
2025-05-29 12:52:12,917 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.0 hours of target)
2025-05-29 12:52:12,932 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.9 hours of target)
2025-05-29 12:52:12,946 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.7 hours of target)
2025-05-29 12:52:12,946 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.4 hours of target)
2025-05-29 12:52:12,962 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 0.9 hours of target)
2025-05-29 12:52:12,967 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.7 hours of target)
2025-05-29 12:52:12,977 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.4 hours of target)
2025-05-29 12:52:12,997 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 0.9 hours of target)
2025-05-29 12:52:12,997 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.4 hours of target)
2025-05-29 12:52:13,017 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.2 hours of target)
2025-05-29 12:52:13,017 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 1.0 hours of target)
2025-05-29 12:52:13,040 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 0.5 hours of target)
2025-05-29 12:52:13,051 - app.components.performance_metrics - INFO - Found price 80.34 for COMI at 2025-05-27 00:00:00 (within 0.5 hours of target)
2025-05-29 12:52:13,053 - app.components.performance_metrics - INFO - Target time 2025-05-27 13:50:15.628261 is in the future compared to latest data 2025-05-27 00:00:00
2025-05-29 12:52:13,068 - app.components.performance_metrics - INFO - Target time 2025-05-27 14:00:15.628261 is in the future compared to latest data 2025-05-27 00:00:00
2025-05-29 12:52:13,068 - app.components.performance_metrics - INFO - Target time 2025-05-27 14:15:15.628261 is in the future compared to latest data 2025-05-27 00:00:00
2025-05-29 12:52:13,083 - app.components.performance_metrics - INFO - Target time 2025-05-27 14:45:15.628261 is in the future compared to latest data 2025-05-27 00:00:00
2025-05-29 12:52:13,099 - app.components.performance_metrics - INFO - Target time 2025-05-27 14:17:18.814352 is in the future compared to latest data 2025-05-27 00:00:00
2025-05-29 12:52:13,099 - app.components.performance_metrics - INFO - Target time 2025-05-27 14:47:18.814352 is in the future compared to latest data 2025-05-27 00:00:00
2025-05-29 12:52:13,118 - app.components.performance_metrics - INFO - Target time 2025-05-27 15:32:19.031735 is in the future compared to latest data 2025-05-27 00:00:00
2025-05-29 12:52:13,118 - app.components.performance_metrics - INFO - Target time 2025-05-29 12:41:45.387724 is in the future compared to latest data 2025-05-27 00:00:00
2025-05-29 12:52:13,178 - app.components.performance_metrics - INFO - Updated performance metrics for 7 model-symbol combinations
2025-05-29 12:52:13,374 - app.utils.memory_management - INFO - Memory before cleanup: 426.16 MB
2025-05-29 12:52:13,483 - app.utils.memory_management - INFO - Garbage collection: collected 1518 objects
2025-05-29 12:52:13,483 - app.utils.memory_management - INFO - Memory after cleanup: 426.16 MB (freed 0.00 MB)
2025-05-29 12:53:06,937 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:53:06,990 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:53:07,012 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:53:07,013 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:53:07,014 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:53:09,113 - app.utils.memory_management - INFO - Memory before cleanup: 437.39 MB
2025-05-29 12:53:09,236 - app.utils.memory_management - INFO - Garbage collection: collected 2534 objects
2025-05-29 12:53:09,236 - app.utils.memory_management - INFO - Memory after cleanup: 437.39 MB (freed 0.00 MB)
2025-05-29 12:54:50,735 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:54:50,788 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:54:50,789 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:54:50,789 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:54:50,790 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:54:51,113 - app.utils.memory_management - INFO - Memory before cleanup: 436.62 MB
2025-05-29 12:54:51,252 - app.utils.memory_management - INFO - Garbage collection: collected 2392 objects
2025-05-29 12:54:51,252 - app.utils.memory_management - INFO - Memory after cleanup: 436.62 MB (freed 0.00 MB)
2025-05-29 12:54:51,694 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:54:51,703 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:54:51,746 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-29 12:54:51,746 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:54:51,747 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:54:51,749 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:54:51,778 - app.utils.memory_management - INFO - Memory before cleanup: 436.61 MB
2025-05-29 12:54:51,906 - app.utils.memory_management - INFO - Garbage collection: collected 234 objects
2025-05-29 12:54:51,907 - app.utils.memory_management - INFO - Memory after cleanup: 436.61 MB (freed 0.00 MB)
2025-05-29 12:56:22,062 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:56:22,167 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:56:22,201 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-29 12:56:22,202 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:56:22,202 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:56:22,202 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:56:22,267 - app.utils.memory_management - INFO - Memory before cleanup: 436.73 MB
2025-05-29 12:56:22,543 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-05-29 12:56:22,547 - app.utils.memory_management - INFO - Memory after cleanup: 436.73 MB (freed 0.00 MB)
2025-05-29 12:56:29,701 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:56:29,724 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:56:29,746 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:56:29,747 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:56:29,747 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:56:29,748 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:56:29,788 - app.utils.memory_management - INFO - Memory before cleanup: 436.81 MB
2025-05-29 12:56:29,915 - app.utils.memory_management - INFO - Garbage collection: collected 284 objects
2025-05-29 12:56:29,915 - app.utils.memory_management - INFO - Memory after cleanup: 436.81 MB (freed 0.00 MB)
2025-05-29 12:56:46,692 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:56:46,718 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:56:46,743 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-29 12:56:46,743 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:56:46,743 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:56:46,743 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:56:46,771 - app.utils.memory_management - INFO - Memory before cleanup: 436.81 MB
2025-05-29 12:56:46,938 - app.utils.memory_management - INFO - Garbage collection: collected 285 objects
2025-05-29 12:56:46,939 - app.utils.memory_management - INFO - Memory after cleanup: 436.81 MB (freed 0.00 MB)
2025-05-29 12:57:23,455 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:57:23,489 - app - INFO - Found 8 stock files in data/stocks
2025-05-29 12:57:23,526 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:57:23,559 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:57:23,561 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:57:23,563 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:57:23,568 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:57:23,625 - app.utils.memory_management - INFO - Memory before cleanup: 436.83 MB
2025-05-29 12:57:23,797 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-29 12:57:23,799 - app.utils.memory_management - INFO - Memory after cleanup: 436.83 MB (freed 0.00 MB)
2025-05-29 12:57:56,601 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:57:56,655 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:57:56,683 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:57:56,685 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:57:56,686 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:57:56,690 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:57:56,797 - app.utils.memory_management - INFO - Memory before cleanup: 436.81 MB
2025-05-29 12:57:56,983 - app.utils.memory_management - INFO - Garbage collection: collected 284 objects
2025-05-29 12:57:56,983 - app.utils.memory_management - INFO - Memory after cleanup: 436.81 MB (freed 0.00 MB)
2025-05-29 12:58:17,732 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:58:17,761 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:58:17,849 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-29 12:58:17,850 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:58:17,851 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:58:17,851 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:58:17,892 - app.utils.memory_management - INFO - Memory before cleanup: 436.81 MB
2025-05-29 12:58:18,021 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-29 12:58:18,021 - app.utils.memory_management - INFO - Memory after cleanup: 436.81 MB (freed 0.00 MB)
2025-05-29 12:58:20,636 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:58:20,683 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:58:20,714 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:58:20,715 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:58:20,715 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:58:20,715 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:58:20,744 - app.utils.memory_management - INFO - Memory before cleanup: 434.66 MB
2025-05-29 12:58:20,922 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-29 12:58:20,922 - app.utils.memory_management - INFO - Memory after cleanup: 434.66 MB (freed 0.00 MB)
2025-05-29 12:58:22,015 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:58:22,045 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:58:22,087 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-29 12:58:22,089 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:58:22,091 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:58:22,091 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:58:22,126 - app.utils.memory_management - INFO - Memory before cleanup: 434.72 MB
2025-05-29 12:58:22,316 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-29 12:58:22,318 - app.utils.memory_management - INFO - Memory after cleanup: 434.72 MB (freed 0.00 MB)
2025-05-29 12:59:26,293 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:59:26,370 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:59:26,399 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:59:26,399 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:59:26,400 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:59:26,401 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:59:26,446 - app.utils.memory_management - INFO - Memory before cleanup: 434.72 MB
2025-05-29 12:59:26,632 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-29 12:59:26,680 - app.utils.memory_management - INFO - Memory after cleanup: 434.72 MB (freed 0.00 MB)
2025-05-29 12:59:35,848 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 12:59:35,874 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 12:59:35,898 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 12:59:35,898 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 12:59:35,898 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 12:59:35,898 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 12:59:35,934 - app.utils.memory_management - INFO - Memory before cleanup: 434.74 MB
2025-05-29 12:59:36,065 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-29 12:59:36,065 - app.utils.memory_management - INFO - Memory after cleanup: 434.74 MB (freed 0.00 MB)
2025-05-29 13:00:13,750 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:00:13,815 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 13:00:13,851 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-29 13:00:13,854 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 13:00:13,854 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 13:00:13,856 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 13:00:14,075 - app.utils.memory_management - INFO - Memory before cleanup: 434.75 MB
2025-05-29 13:00:14,410 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-29 13:00:14,412 - app.utils.memory_management - INFO - Memory after cleanup: 434.75 MB (freed 0.00 MB)
2025-05-29 13:00:23,011 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:00:23,063 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 13:00:23,099 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-29 13:00:23,100 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 13:00:23,102 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 13:00:23,104 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 13:00:23,170 - app.utils.memory_management - INFO - Memory before cleanup: 434.75 MB
2025-05-29 13:00:23,512 - app.utils.memory_management - INFO - Garbage collection: collected 282 objects
2025-05-29 13:00:23,513 - app.utils.memory_management - INFO - Memory after cleanup: 434.75 MB (freed 0.00 MB)
2025-05-29 13:00:43,055 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:00:43,084 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 13:00:43,107 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 13:00:43,108 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 13:00:43,108 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 13:00:43,109 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 13:00:43,151 - app.utils.memory_management - INFO - Memory before cleanup: 434.75 MB
2025-05-29 13:00:43,351 - app.utils.memory_management - INFO - Garbage collection: collected 282 objects
2025-05-29 13:00:43,352 - app.utils.memory_management - INFO - Memory after cleanup: 434.75 MB (freed 0.00 MB)
2025-05-29 13:01:12,306 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:01:12,330 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 13:01:12,357 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 13:01:12,358 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 13:01:12,358 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 13:01:12,359 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 13:01:12,384 - app.utils.memory_management - INFO - Memory before cleanup: 434.78 MB
2025-05-29 13:01:12,540 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-05-29 13:01:12,540 - app.utils.memory_management - INFO - Memory after cleanup: 434.78 MB (freed 0.00 MB)
2025-05-29 13:01:17,167 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:01:17,190 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 13:01:17,216 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-29 13:01:17,217 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 13:01:17,218 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 13:01:17,218 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 13:01:17,253 - app.utils.memory_management - INFO - Memory before cleanup: 434.78 MB
2025-05-29 13:01:17,409 - app.utils.memory_management - INFO - Garbage collection: collected 290 objects
2025-05-29 13:01:17,412 - app.utils.memory_management - INFO - Memory after cleanup: 434.78 MB (freed 0.00 MB)
2025-05-29 13:01:24,901 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:01:24,924 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-29 13:01:24,932 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-29 13:01:24,932 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-27
2025-05-29 13:01:24,932 - app.utils.common - INFO - Data shape: (579, 36)
2025-05-29 13:01:24,932 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-29 13:01:25,069 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-29 13:01:26,676 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-29 13:01:26,677 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-29 13:01:26,694 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 80.3
2025-05-29 13:01:26,694 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-29 13:01:26,694 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-29 13:01:26,694 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-29 13:01:29,848 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-29 13:01:32,033 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-29 13:01:32,045 - scrapers.price_scraper - INFO - Successfully extracted price from header: 81.52
2025-05-29 13:01:32,045 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 81.52
2025-05-29 13:01:32,046 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-29 13:01:32,046 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-29 13:01:32,046 - app.utils.error_handling - INFO - fetch_price executed in 5.35 seconds
2025-05-29 13:01:32,047 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-29 13:01:34,170 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 13:01:34,291 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 13:01:34,424 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-05-29 13:01:34,426 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-29 13:01:34,426 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 13:01:34,426 - models.predict - WARNING - Model file not found or import error for lstm with horizon 60: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-29 13:01:34,426 - models.predict - INFO - Skipping lstm model for horizon 60 - model not trained for this horizon
2025-05-29 13:01:34,426 - app.components.tradingview_predictions - WARNING - No valid predictions generated for LSTM - skipping this model
2025-05-29 13:01:34,446 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 13:01:34,556 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 13:01:34,674 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 13:01:34,675 - models.predict - INFO - Loading bilstm model for COMI with horizon 60
2025-05-29 13:01:34,677 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_60min.keras or saved_models\COMI_bilstm_60min.h5
2025-05-29 13:01:34,677 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 60: Model not found at saved_models\COMI_bilstm_60min.keras or saved_models\COMI_bilstm_60min.h5
2025-05-29 13:01:34,677 - models.predict - INFO - Skipping bilstm model for horizon 60 - model not trained for this horizon
2025-05-29 13:01:34,677 - app.components.tradingview_predictions - WARNING - No valid predictions generated for BiLSTM - skipping this model
2025-05-29 13:01:34,693 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 13:01:34,812 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 13:01:34,921 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 13:01:34,921 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-29 13:01:34,921 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-29 13:01:34,921 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-29 13:01:34,921 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-29 13:01:34,921 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 13:01:34,921 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-29 13:01:34,921 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 13:01:34,986 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-29 13:01:35,012 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 13:01:35,012 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-29 13:01:35,013 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 13:01:35,017 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 13:01:35,017 - models.predict - INFO - Current price: 80.34, Predicted scaled value: 0.7001149833202363
2025-05-29 13:01:35,018 - models.predict - INFO - Prediction for 60 minutes horizon: 80.06630108594895
2025-05-29 13:01:35,018 - app.components.tradingview_predictions - INFO - Successfully generated 1 predictions for RandomForest
2025-05-29 13:01:35,035 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 13:01:35,137 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 13:01:35,257 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 13:01:35,257 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-05-29 13:01:35,257 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-05-29 13:01:35,257 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-05-29 13:01:35,257 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_60min.joblib, searching for alternatives...
2025-05-29 13:01:35,257 - models.sklearn_model - INFO - Found 30 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib']
2025-05-29 13:01:35,257 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_gb_120960min.joblib
2025-05-29 13:01:35,257 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 13:01:35,273 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_120960min.joblib
2025-05-29 13:01:35,273 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-29 13:01:35,302 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-05-29 13:01:35,302 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-29 13:01:35,302 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-29 13:01:35,302 - models.predict - INFO - Current price: 80.34, Predicted scaled value: 0.69406540113695
2025-05-29 13:01:35,305 - models.predict - INFO - Prediction for 60 minutes horizon: 79.73478398230486
2025-05-29 13:01:35,305 - app.components.tradingview_predictions - INFO - Successfully generated 1 predictions for GradientBoosting
2025-05-29 13:01:35,305 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-29 13:01:35,430 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-29 13:01:35,542 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-29 13:01:35,542 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-05-29 13:01:35,542 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_60min.joblib
2025-05-29 13:01:35,542 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 60
2025-05-29 13:01:35,542 - models.predict - INFO - Created fallback ensemble model with base price: 80.34
2025-05-29 13:01:35,542 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-29 13:01:35,542 - models.predict - INFO - Ensemble model already loaded
2025-05-29 13:01:35,569 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-05-29 13:01:35,569 - models.predict - INFO - Current price: 80.34, Predicted scaled value: 82.24774912398713
2025-05-29 13:01:35,569 - models.predict - WARNING - Prediction 4548.876651994495 is too far from current price 80.34, using fallback
2025-05-29 13:01:35,569 - models.predict - INFO - Prediction for 60 minutes horizon: 79.80786804399462
2025-05-29 13:01:35,569 - app.components.tradingview_predictions - INFO - Successfully generated 1 predictions for Ensemble
2025-05-29 13:01:35,660 - app.utils.memory_management - INFO - Memory before cleanup: 439.85 MB
2025-05-29 13:01:35,786 - app.utils.memory_management - INFO - Garbage collection: collected 259 objects
2025-05-29 13:01:35,787 - app.utils.memory_management - INFO - Memory after cleanup: 439.85 MB (freed 0.00 MB)
2025-05-29 13:02:02,581 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:02:02,659 - app.utils.memory_management - INFO - Memory before cleanup: 439.88 MB
2025-05-29 13:02:02,882 - app.utils.memory_management - INFO - Garbage collection: collected 322 objects
2025-05-29 13:02:02,883 - app.utils.memory_management - INFO - Memory after cleanup: 439.88 MB (freed 0.00 MB)
2025-05-29 13:02:04,450 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:02:04,478 - app.utils.memory_management - INFO - Memory before cleanup: 439.91 MB
2025-05-29 13:02:04,588 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-05-29 13:02:04,588 - app.utils.memory_management - INFO - Memory after cleanup: 439.91 MB (freed 0.00 MB)
2025-05-29 13:02:05,705 - app - INFO - Using TensorFlow-based LSTM model
2025-05-29 13:02:05,973 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-05-29 13:02:06,234 - app.utils.session_state - ERROR - Error tracked: app_crash - 'Date'
2025-05-29 13:02:06,234 - app - ERROR - Application crashed: 'Date'
2025-05-29 13:02:06,236 - app.utils.memory_management - INFO - Memory before cleanup: 440.38 MB
2025-05-29 13:02:06,353 - app.utils.memory_management - INFO - Garbage collection: collected 200 objects
2025-05-29 13:02:06,354 - app.utils.memory_management - INFO - Memory after cleanup: 440.38 MB (freed 0.00 MB)
2025-05-29 13:02:06,354 - app.utils.memory_management - INFO - Memory before cleanup: 440.38 MB
2025-05-29 13:02:06,507 - app.utils.memory_management - INFO - Garbage collection: collected 399 objects
2025-05-29 13:02:06,507 - app.utils.memory_management - INFO - Memory after cleanup: 440.38 MB (freed 0.00 MB)
