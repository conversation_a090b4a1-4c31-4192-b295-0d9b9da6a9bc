2025-05-11 19:03:02,017 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 19:03:02,040 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 19:03:02,040 - app - WARNING - Memory management utilities not available
2025-05-11 19:03:57,198 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 19:03:57,199 - app - WARNING - Memory management utilities not available
2025-05-11 19:03:57,201 - app - INFO - Error handling utilities loaded
2025-05-11 19:03:57,209 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-11 19:03:57,209 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-11 19:03:57,211 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-11 19:03:57,211 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-11 19:03:57,211 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-11 19:03:57,211 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-11 19:03:57,212 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-11 19:03:57,212 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 19:03:57,213 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 19:03:57,213 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 19:03:57,214 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 19:03:57,214 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 19:03:57,214 - app - INFO - Applied NumPy fix
2025-05-11 19:03:57,218 - app.utils.session_state - WARNING - Memory management utilities not available in session_state module
2025-05-11 19:03:57,223 - app.config - INFO - Configuration initialized
2025-05-11 19:04:00,212 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 19:04:00,235 - models.train - INFO - TensorFlow test successful
2025-05-11 19:04:00,627 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 19:04:00,627 - models.train - INFO - Transformer model is available
2025-05-11 19:04:00,627 - models.train - INFO - Using TensorFlow-based models
2025-05-11 19:04:00,627 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 19:04:00,627 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 19:04:00,627 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:05:11,365 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:06:21,141 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 19:06:21,141 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 19:06:21,141 - app - WARNING - Memory management utilities not available
2025-05-11 19:06:21,150 - app - INFO - Error handling utilities loaded
2025-05-11 19:06:21,152 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 19:06:21,153 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 19:06:21,155 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 19:06:21,155 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 19:06:21,156 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 19:06:21,156 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 19:06:21,157 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 19:06:21,157 - app - INFO - Applied NumPy fix
2025-05-11 19:06:21,158 - app.utils.session_state - WARNING - Memory management utilities not available in session_state module
2025-05-11 19:06:21,172 - app.config - INFO - Configuration initialized
2025-05-11 19:06:21,176 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 19:06:21,179 - models.train - INFO - TensorFlow test successful
2025-05-11 19:06:21,183 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 19:06:21,183 - models.train - INFO - Transformer model is available
2025-05-11 19:06:21,185 - models.train - INFO - Using TensorFlow-based models
2025-05-11 19:06:21,186 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 19:06:21,186 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 19:06:21,187 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:07:29,218 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 19:07:33,277 - app - WARNING - Memory management utilities not available
2025-05-11 19:07:33,282 - app - INFO - Error handling utilities loaded
2025-05-11 19:07:33,287 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 19:07:33,287 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 19:07:33,287 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 19:07:33,287 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 19:07:33,289 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 19:07:33,289 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 19:07:33,289 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 19:07:33,290 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 19:07:33,290 - app - INFO - Applied NumPy fix
2025-05-11 19:07:33,294 - app.utils.session_state - WARNING - Memory management utilities not available in session_state module
2025-05-11 19:07:33,301 - app.config - INFO - Configuration initialized
2025-05-11 19:07:33,313 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 19:07:41,026 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 19:07:41,026 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 19:07:41,182 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 19:07:41,183 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 19:07:41,183 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 19:07:41,183 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 19:07:41,184 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 19:07:42,140 - app.utils.state_manager - WARNING - Memory management utilities not available in state_manager module
2025-05-11 19:07:42,151 - app.utils.session_state - INFO - Initializing session state
2025-05-11 19:07:42,157 - app.utils.session_state - INFO - Session state initialized
2025-05-11 19:07:42,694 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 19:07:43,195 - app - INFO - Cleaning up resources...
2025-05-11 19:07:43,195 - app - INFO - Application shutdown complete
2025-05-11 19:07:54,833 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 19:07:54,836 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 19:07:54,836 - app - WARNING - Memory management utilities not available
2025-05-11 19:07:54,837 - app - INFO - Error handling utilities loaded
2025-05-11 19:07:54,838 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 19:07:54,838 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 19:07:54,838 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 19:07:54,838 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 19:07:54,839 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 19:07:54,839 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 19:07:54,839 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 19:07:54,839 - app - INFO - Applied NumPy fix
2025-05-11 19:07:54,840 - app.utils.session_state - WARNING - Memory management utilities not available in session_state module
2025-05-11 19:07:54,846 - app.config - INFO - Configuration initialized
2025-05-11 19:07:54,849 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 19:07:54,850 - models.train - INFO - TensorFlow test successful
2025-05-11 19:07:54,851 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 19:07:54,851 - models.train - INFO - Transformer model is available
2025-05-11 19:07:54,851 - models.train - INFO - Using TensorFlow-based models
2025-05-11 19:07:54,852 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 19:07:54,852 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 19:07:54,852 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:07:54,876 - app.utils.state_manager - WARNING - Memory management utilities not available in state_manager module
2025-05-11 19:07:54,883 - app.utils.session_state - INFO - Initializing session state
2025-05-11 19:07:54,884 - app.utils.session_state - INFO - Session state initialized
2025-05-11 19:07:54,888 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 19:08:19,656 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:08:21,027 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:08:21,065 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-11 19:08:21,065 - app - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 19:08:21,065 - app - INFO - Data shape: (567, 36)
2025-05-11 19:08:21,065 - app - INFO - File COMI contains 2025 data
2025-05-11 19:08:21,092 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-11 19:08:21,094 - app - INFO - Features shape: (567, 36)
2025-05-11 19:08:21,096 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-11 19:08:21,096 - app - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 19:08:21,096 - app - INFO - Data shape: (567, 36)
2025-05-11 19:08:21,096 - app - INFO - File COMI contains 2025 data
2025-05-11 19:08:54,877 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:09:05,077 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:09:05,096 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 19:09:06,195 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 19:09:06,195 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-11 19:09:06,206 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 78.25
2025-05-11 19:09:06,206 - scrapers.price_scraper - INFO - Generated sample price for COMI: 80.07
2025-05-11 19:09:06,207 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-11 19:09:06,207 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-11 19:09:06,207 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-11 19:09:10,718 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-11 19:09:12,866 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-11 19:09:12,875 - scrapers.price_scraper - INFO - Successfully extracted price from header: 78.6
2025-05-11 19:09:12,875 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 78.6
2025-05-11 19:09:12,875 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-11 19:09:12,875 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-11 19:09:12,876 - app.utils.error_handling - INFO - fetch_price executed in 6.67 seconds
2025-05-11 19:09:12,880 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 19:09:52,476 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:09:52,499 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-11 19:09:52,504 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-11 19:09:52,505 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-11 19:09:52,510 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-11 19:10:07,359 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:10:07,379 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-11 19:10:07,385 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-11 19:10:07,386 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-11 19:10:07,389 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-11 19:10:07,396 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 19:10:08,553 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 19:10:08,554 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-11 19:10:08,561 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 78.68
2025-05-11 19:10:08,562 - scrapers.price_scraper - INFO - Generated sample price for COMI: 79.68
2025-05-11 19:10:08,562 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-11 19:10:08,562 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-11 19:10:08,563 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-11 19:10:12,251 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-11 19:10:14,399 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-11 19:10:14,407 - scrapers.price_scraper - INFO - Successfully extracted price from header: 78.6
2025-05-11 19:10:14,408 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 78.6
2025-05-11 19:10:14,408 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-11 19:10:14,408 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-11 19:10:14,408 - app.utils.error_handling - INFO - fetch_price executed in 5.85 seconds
2025-05-11 19:10:14,412 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 19:10:16,545 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-11 19:10:16,658 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 19:10:16,658 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-11 19:10:16,775 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 19:10:16,775 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 19:10:16,775 - models.predict - INFO - Loading lstm model for COMI with horizon 5
2025-05-11 19:10:16,775 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_5min.keras
2025-05-11 19:10:17,443 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-05-11 19:10:18,359 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-11 19:10:18,359 - models.predict - INFO - Prediction for 5 minutes horizon: 80.3061143875122
2025-05-11 19:10:18,359 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-11 19:10:18,476 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 19:10:18,476 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-11 19:10:18,609 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 19:10:18,609 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 19:10:18,609 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-11 19:10:18,609 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-11 19:10:18,609 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-11 19:10:18,609 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-11 19:10:18,743 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 19:10:18,743 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 19:10:18,793 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-05-11 19:10:18,793 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_15min.joblib, searching for alternatives...
2025-05-11 19:10:18,793 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-11 19:10:18,793 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 19:10:18,793 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 19:10:18,793 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-11 19:10:18,793 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-11 19:10:18,793 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 19:10:18,793 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 19:10:18,793 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 19:10:18,793 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 19:10:18,793 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 19:10:18,793 - models.predict - INFO - Prediction for 15 minutes horizon: 78.6
2025-05-11 19:10:18,793 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 19:10:18,942 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 19:10:18,942 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 19:10:19,059 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 19:10:19,059 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 19:10:19,059 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-11 19:10:19,059 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-11 19:10:19,642 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 19:10:20,576 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-11 19:10:20,576 - models.predict - INFO - Prediction for 30 minutes horizon: 72.64745488166808
2025-05-11 19:10:20,576 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 19:10:20,695 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 19:10:20,695 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 19:10:20,828 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 19:10:20,828 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-11 19:10:20,828 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-11 19:10:20,828 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-11 19:10:20,828 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-11 19:10:20,828 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-11 19:10:20,828 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-11 19:10:20,828 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-11 19:10:20,828 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 19:10:20,828 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-11 19:10:20,828 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-11 19:10:20,828 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-11 19:10:20,828 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-11 19:10:20,828 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 19:10:20,845 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-11 19:10:20,845 - models.predict - ERROR - Error in prediction for horizon 60: invalid index to scalar variable.
2025-05-11 19:10:20,845 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-11 19:10:20,845 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 19:10:20,845 - models.predict - INFO - Prediction for 60 minutes horizon: 78.6
2025-05-11 19:10:20,866 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 5 minutes
2025-05-11 19:10:20,881 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 15 minutes
2025-05-11 19:10:20,894 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-11 19:10:20,909 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-11 19:10:46,480 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:10:53,375 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:10:55,541 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:10:55,578 - app.components.performance_metrics - INFO - Target time 2025-05-11 09:59:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,591 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:09:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,602 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,614 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:01:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,624 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:11:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,631 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,643 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:03:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,652 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:13:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,658 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,658 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:08:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,679 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:18:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,679 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:03:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,691 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:29:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,702 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:39:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,714 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:24:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,722 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:32:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,730 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:42:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,739 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:57:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,741 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:27:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,741 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:33:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,758 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:43:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,758 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,774 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:28:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,774 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:36:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,791 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,802 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,808 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,808 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,825 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,825 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:11:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,841 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:41:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,841 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:48:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,863 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,871 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:13:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,879 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:43:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,887 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:51:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,891 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,908 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,908 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:46:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,928 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,937 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:04:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,943 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:19:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,943 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:49:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,958 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:05:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,958 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:15:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,975 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:30:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,975 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:00:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:55,993 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:06:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,002 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,043 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,059 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:01:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,059 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:51:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,076 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:01:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,076 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:16:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,093 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:46:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,102 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:52:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,109 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:02:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,109 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:47:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,126 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:01:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,126 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:11:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,142 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:26:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:10:56,142 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:56:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,549 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:11:21,581 - app.components.performance_metrics - INFO - Target time 2025-05-11 09:59:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,591 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:09:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,600 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,609 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:01:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,617 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:11:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,624 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,624 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:03:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,643 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:13:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,649 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,658 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:08:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,658 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:18:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,681 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:03:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,688 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:29:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,697 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:39:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,705 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:24:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,709 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:32:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,709 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:42:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,725 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:57:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,725 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:27:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,741 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:33:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,749 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:43:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,758 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,775 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:28:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,775 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:36:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,791 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,791 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,808 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,808 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,824 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,824 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:11:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,845 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:41:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,855 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:48:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,864 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,873 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:13:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,875 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:43:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,875 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:51:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,891 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,908 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,908 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:46:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,925 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,934 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:04:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,942 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:19:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,950 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:49:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,958 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:05:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,958 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:15:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,975 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:30:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,975 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:00:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,991 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:06:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:21,991 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,009 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,009 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:01:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,025 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:51:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,025 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:01:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,041 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:16:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,056 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:46:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,057 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:52:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,074 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:02:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,075 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:47:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,092 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:01:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,105 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:11:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,114 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:26:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:22,124 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:56:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 19:11:38,624 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:11:46,107 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:11:47,174 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:11:47,174 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 19:11:47,200 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-11 19:11:47,201 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-11 19:11:47,201 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-11 19:11:47,201 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-11 19:11:47,730 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.56 seconds
2025-05-11 19:11:56,000 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:11:56,011 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 19:11:56,025 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 19:11:56,026 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 19:11:56,026 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-11 19:11:56,026 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-11 19:11:56,261 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.25 seconds
2025-05-11 19:51:16,113 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:51:16,135 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 19:51:16,141 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-11 19:51:16,141 - app.utils.backtesting - INFO - Historical data shape: (567, 36)
2025-05-11 19:51:16,142 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-11 19:51:16,143 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-11 00:00:00
2025-05-11 19:51:21,284 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:51:21,299 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-11 19:51:21,299 - app.utils.backtesting - INFO - Historical data shape: (567, 36)
2025-05-11 19:51:21,299 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-11 19:51:21,299 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-11 00:00:00
2025-05-11 19:51:22,568 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 19:51:22,585 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 19:51:23,794 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 19:51:23,811 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-05-11 19:51:23,811 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-05-11 19:51:23,812 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-05-11 19:51:23,812 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-05-11 19:51:23,815 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-11 19:51:23,816 - app.utils.error_handling - INFO - live_trading_component executed in 1.25 seconds
2025-05-11 20:39:16,411 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 20:39:16,427 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 20:39:16,427 - app - INFO - Memory management utilities loaded
2025-05-11 20:39:16,435 - app - INFO - Error handling utilities loaded
2025-05-11 20:39:16,436 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 20:39:16,436 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 20:39:16,436 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 20:39:16,436 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 20:39:16,437 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 20:39:16,437 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 20:39:16,437 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 20:39:16,437 - app - INFO - Applied NumPy fix
2025-05-11 20:39:16,439 - app.config - INFO - Configuration initialized
2025-05-11 20:39:16,442 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 20:39:16,443 - models.train - INFO - TensorFlow test successful
2025-05-11 20:39:16,445 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 20:39:16,446 - models.train - INFO - Transformer model is available
2025-05-11 20:39:16,446 - models.train - INFO - Using TensorFlow-based models
2025-05-11 20:39:16,447 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 20:39:16,447 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 20:39:16,448 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:47:13,458 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 20:47:15,784 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 20:47:15,784 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 20:47:15,784 - app - INFO - Memory management utilities loaded
2025-05-11 20:47:15,799 - app - INFO - Error handling utilities loaded
2025-05-11 20:47:15,800 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 20:47:15,801 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 20:47:15,801 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 20:47:15,801 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 20:47:15,802 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 20:47:15,802 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 20:47:15,802 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 20:47:15,802 - app - INFO - Applied NumPy fix
2025-05-11 20:47:15,804 - app.config - INFO - Configuration initialized
2025-05-11 20:47:15,809 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 20:47:15,810 - models.train - INFO - TensorFlow test successful
2025-05-11 20:47:15,812 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 20:47:15,813 - models.train - INFO - Transformer model is available
2025-05-11 20:47:15,813 - models.train - INFO - Using TensorFlow-based models
2025-05-11 20:47:15,814 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 20:47:15,815 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 20:47:15,815 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:49:13,487 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:49:13,487 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 20:49:13,504 - app.utils.session_state - INFO - Initializing session state
2025-05-11 20:49:13,506 - app.utils.session_state - INFO - Session state initialized
2025-05-11 20:49:13,512 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 20:49:13,515 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:49:13,516 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:49:13,651 - app.utils.memory_management - INFO - Garbage collection: collected 13890 objects
2025-05-11 20:49:13,651 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:49:13,651 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:50:13,690 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:50:13,722 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:13,724 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:50:13,868 - app.utils.memory_management - INFO - Garbage collection: collected 231 objects
2025-05-11 20:50:13,871 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:13,871 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:50:14,905 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:50:14,950 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-11 20:50:14,951 - app - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 20:50:14,951 - app - INFO - Data shape: (567, 36)
2025-05-11 20:50:14,951 - app - INFO - File COMI contains 2025 data
2025-05-11 20:50:14,969 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-11 20:50:14,969 - app - INFO - Features shape: (567, 36)
2025-05-11 20:50:14,973 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-11 20:50:14,973 - app - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 20:50:14,973 - app - INFO - Data shape: (567, 36)
2025-05-11 20:50:14,973 - app - INFO - File COMI contains 2025 data
2025-05-11 20:50:15,000 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:15,000 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:50:15,131 - app.utils.memory_management - INFO - Garbage collection: collected 210 objects
2025-05-11 20:50:15,131 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:15,131 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:50:32,755 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:50:32,772 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 20:50:32,793 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-11 20:50:32,793 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-11 20:50:32,793 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-11 20:50:32,793 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-11 20:50:32,971 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.20 seconds
2025-05-11 20:50:33,159 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:33,160 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:50:33,288 - app.utils.memory_management - INFO - Garbage collection: collected 2015 objects
2025-05-11 20:50:33,288 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:33,289 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:50:40,115 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:50:40,127 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 20:50:40,139 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 20:50:40,140 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 20:50:40,140 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-11 20:50:40,141 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-11 20:50:40,338 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.21 seconds
2025-05-11 20:50:40,533 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:40,533 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:50:40,664 - app.utils.memory_management - INFO - Garbage collection: collected 2155 objects
2025-05-11 20:50:40,664 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:40,665 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:50:52,325 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:50:52,349 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:52,349 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:50:52,502 - app.utils.memory_management - INFO - Garbage collection: collected 309 objects
2025-05-11 20:50:52,502 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:52,502 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:50:56,871 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:50:56,904 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:56,904 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:50:57,039 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-05-11 20:50:57,040 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:57,040 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:50:58,004 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:50:58,124 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:58,124 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:50:58,261 - app.utils.memory_management - INFO - Garbage collection: collected 451 objects
2025-05-11 20:50:58,261 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:50:58,261 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:51:16,420 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:51:16,498 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:16,498 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:51:16,626 - app.utils.memory_management - INFO - Garbage collection: collected 451 objects
2025-05-11 20:51:16,627 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:16,627 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:51:17,454 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:51:17,454 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-11 20:51:17,454 - app.utils.backtesting - INFO - Historical data shape: (567, 36)
2025-05-11 20:51:17,454 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-11 20:51:17,454 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-11 00:00:00
2025-05-11 20:51:17,479 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:17,479 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:51:17,608 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-05-11 20:51:17,608 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:17,608 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:51:25,953 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:51:25,953 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-05-11 20:51:25,953 - app.utils.backtesting - INFO - Historical data shape: (567, 36)
2025-05-11 20:51:25,953 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-05-11 20:51:25,953 - app.utils.backtesting - INFO - Historical data date range: 2023-01-02 00:00:00 to 2025-05-11 00:00:00
2025-05-11 20:51:25,972 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:25,973 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:51:26,110 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-11 20:51:26,110 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:26,110 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:51:27,020 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:51:27,037 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:27,037 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:51:27,170 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-11 20:51:27,170 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:27,177 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:51:31,520 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:51:31,556 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:31,556 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:51:31,684 - app.utils.memory_management - INFO - Garbage collection: collected 229 objects
2025-05-11 20:51:31,685 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:31,685 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:51:32,686 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:51:32,703 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:32,703 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:51:32,849 - app.utils.memory_management - INFO - Garbage collection: collected 228 objects
2025-05-11 20:51:32,849 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:32,850 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:51:39,486 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:51:39,528 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:39,528 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:51:39,653 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-05-11 20:51:39,653 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:39,653 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:51:40,952 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:51:40,974 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-05-11 20:51:40,974 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-05-11 20:51:40,974 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-05-11 20:51:40,974 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-05-11 20:51:40,974 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:40,974 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:51:41,103 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-05-11 20:51:41,103 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:51:41,103 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:52:00,286 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:52:00,303 - app.utils.data_processing - INFO - Found hybrid model for COMI with 5 minutes horizon
2025-05-11 20:52:00,323 - app.utils.data_processing - INFO - Using model trained for 4 minutes instead of 15 minutes
2025-05-11 20:52:00,324 - app.utils.data_processing - INFO - Found hybrid model for COMI with 30 minutes horizon
2025-05-11 20:52:00,327 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon using glob
2025-05-11 20:52:00,329 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:00,329 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:52:00,462 - app.utils.memory_management - INFO - Garbage collection: collected 227 objects
2025-05-11 20:52:00,462 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:00,462 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:52:02,589 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:52:02,613 - app.utils.data_processing - INFO - Found hybrid model for COMI with 5 minutes horizon
2025-05-11 20:52:02,622 - app.utils.data_processing - INFO - Using model trained for 4 minutes instead of 15 minutes
2025-05-11 20:52:02,623 - app.utils.data_processing - INFO - Found hybrid model for COMI with 30 minutes horizon
2025-05-11 20:52:02,626 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon using glob
2025-05-11 20:52:02,639 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 20:52:03,795 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 20:52:03,795 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-11 20:52:03,802 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 78.74
2025-05-11 20:52:03,803 - scrapers.price_scraper - INFO - Generated sample price for COMI: 79.34
2025-05-11 20:52:03,803 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-11 20:52:03,803 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-11 20:52:03,804 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-11 20:52:09,977 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-11 20:52:12,113 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-11 20:52:12,123 - scrapers.price_scraper - INFO - Successfully extracted price from header: 78.6
2025-05-11 20:52:12,123 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 78.6
2025-05-11 20:52:12,123 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-11 20:52:12,123 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-11 20:52:12,124 - app.utils.error_handling - INFO - fetch_price executed in 8.32 seconds
2025-05-11 20:52:12,126 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 20:52:14,255 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-11 20:52:14,369 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:14,369 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-11 20:52:14,520 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:14,520 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-05-11 20:52:14,520 - models.predict - INFO - Using scikit-learn hybrid model for 5 minutes horizon
2025-05-11 20:52:14,520 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 20:52:14,520 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 20:52:14,520 - models.predict - INFO - Loading hybrid model for COMI with horizon 5
2025-05-11 20:52:14,520 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_5min.joblib
2025-05-11 20:52:14,520 - models.sklearn_model - INFO - Found hybrid model at saved_models\COMI_arima_ml_rf_5min.joblib
2025-05-11 20:52:14,520 - models.sklearn_model - INFO - Loading model from saved_models\COMI_arima_ml_rf_5min.joblib
2025-05-11 20:52:14,553 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-05-11 20:52:14,553 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-11 20:52:14,553 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:52:14,570 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:52:14,570 - models.predict - INFO - Prediction for 5 minutes horizon: 78.64390000000003
2025-05-11 20:52:14,570 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-11 20:52:14,686 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:14,686 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-11 20:52:14,820 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:14,820 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-05-11 20:52:14,820 - models.predict - INFO - Using scikit-learn hybrid model for 15 minutes horizon
2025-05-11 20:52:14,820 - models.predict - INFO - Loading hybrid model for COMI with horizon 15
2025-05-11 20:52:14,820 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_15min.joblib
2025-05-11 20:52:14,820 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_15min.joblib, searching for alternatives...
2025-05-11 20:52:14,820 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-11 20:52:14,820 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:52:14,820 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:52:14,836 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-11 20:52:14,836 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-11 20:52:14,836 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:52:14,836 - models.sklearn_model - ERROR - Error in hybrid model prediction: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:52:14,836 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:52:14,836 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:52:14,836 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 413, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 418, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 20:52:14,836 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:52:14,836 - models.predict - INFO - Prediction for 15 minutes horizon: 78.6
2025-05-11 20:52:14,836 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 20:52:14,970 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:14,970 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 20:52:15,119 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:15,119 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-05-11 20:52:15,119 - models.predict - INFO - Using scikit-learn hybrid model for 30 minutes horizon
2025-05-11 20:52:15,119 - models.predict - INFO - Loading hybrid model for COMI with horizon 30
2025-05-11 20:52:15,119 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_30min.joblib
2025-05-11 20:52:15,119 - models.sklearn_model - INFO - Found hybrid model at saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-11 20:52:15,119 - models.sklearn_model - INFO - Loading model from saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-11 20:52:15,153 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 20:52:15,153 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-11 20:52:15,153 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:52:15,169 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:52:15,169 - models.predict - INFO - Prediction for 30 minutes horizon: 78.71330000000003
2025-05-11 20:52:15,169 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:52:15,303 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:15,303 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:52:15,438 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:15,438 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:52:15,438 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-11 20:52:15,438 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-11 20:52:15,438 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-11 20:52:15,438 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-11 20:52:15,438 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:52:15,438 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-11 20:52:15,438 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-11 20:52:15,469 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:52:15,469 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-11 20:52:15,487 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:52:15,487 - models.predict - INFO - Prediction for 60 minutes horizon: 78.67880000000002
2025-05-11 20:52:15,503 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 5 minutes
2025-05-11 20:52:15,517 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 15 minutes
2025-05-11 20:52:15,526 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-11 20:52:15,536 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-11 20:52:15,561 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:15,561 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:52:15,693 - app.utils.memory_management - INFO - Garbage collection: collected 726 objects
2025-05-11 20:52:15,694 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:15,694 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:52:36,180 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:52:36,202 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:36,203 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:52:36,319 - app.utils.memory_management - INFO - Garbage collection: collected 242 objects
2025-05-11 20:52:36,319 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:36,334 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:52:52,335 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:52:52,359 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:52,359 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:52:52,490 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-11 20:52:52,490 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:52,490 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:52:59,805 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:52:59,819 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:59,819 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:52:59,955 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-11 20:52:59,956 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:52:59,956 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:01,252 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:01,269 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:01,269 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:01,408 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-11 20:53:01,413 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:01,413 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:02,462 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:02,478 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:02,478 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:02,616 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-11 20:53:02,616 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:02,616 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:04,824 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:04,855 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:04,856 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:04,982 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-11 20:53:04,983 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:04,985 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:05,668 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:05,685 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:05,685 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:05,818 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-11 20:53:05,818 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:05,818 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:21,681 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:21,704 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:21,704 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:21,818 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-11 20:53:21,818 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:21,818 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:27,619 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:27,638 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:27,638 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:27,779 - app.utils.memory_management - INFO - Garbage collection: collected 231 objects
2025-05-11 20:53:27,780 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:27,780 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:32,251 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:32,268 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:32,268 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:32,401 - app.utils.memory_management - INFO - Garbage collection: collected 231 objects
2025-05-11 20:53:32,402 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:32,402 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:33,702 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:33,721 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:33,721 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:33,851 - app.utils.memory_management - INFO - Garbage collection: collected 231 objects
2025-05-11 20:53:33,851 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:33,851 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:37,818 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:37,834 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:37,834 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:53:37,969 - app.utils.memory_management - INFO - Garbage collection: collected 231 objects
2025-05-11 20:53:37,969 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:37,969 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:53:44,852 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:53:44,880 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 20:53:46,045 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 20:53:46,046 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-11 20:53:46,062 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 78.96
2025-05-11 20:53:46,063 - scrapers.price_scraper - INFO - Generated sample price for COMI: 79.05
2025-05-11 20:53:46,063 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-11 20:53:46,063 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-11 20:53:46,064 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-11 20:53:49,406 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-11 20:53:51,550 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-11 20:53:51,559 - scrapers.price_scraper - INFO - Successfully extracted price from header: 78.6
2025-05-11 20:53:51,559 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 78.6
2025-05-11 20:53:51,559 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-11 20:53:51,559 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-11 20:53:51,560 - app.utils.error_handling - INFO - fetch_price executed in 5.50 seconds
2025-05-11 20:53:51,581 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-11 20:53:51,713 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:51,714 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-11 20:53:51,848 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:51,848 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:51,850 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-05-11 20:53:51,850 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-11 20:53:51,851 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_15min.keras or saved_models\COMI_lstm_15min.h5
2025-05-11 20:53:51,851 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-11 20:53:51,851 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-05-11 20:53:51,851 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_15min.joblib, searching for alternatives...
2025-05-11 20:53:51,852 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-11 20:53:51,853 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:51,853 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:51,854 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-11 20:53:51,854 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-11 20:53:51,854 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:51,855 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:51,855 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:51,855 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 20:53:51,856 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:53:51,856 - models.predict - INFO - Prediction for 15 minutes horizon: 78.6
2025-05-11 20:53:51,875 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-11 20:53:51,999 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:51,999 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-11 20:53:52,130 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:52,131 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:52,133 - models.predict - INFO - Using scikit-learn rf model for 15 minutes horizon
2025-05-11 20:53:52,133 - models.predict - INFO - Loading rf model for COMI with horizon 15
2025-05-11 20:53:52,133 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_15min.joblib
2025-05-11 20:53:52,134 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_15min.joblib, searching for alternatives...
2025-05-11 20:53:52,136 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-11 20:53:52,136 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:52,137 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:52,138 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-11 20:53:52,138 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-11 20:53:52,139 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:52,139 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:52,139 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:52,140 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 20:53:52,141 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:53:52,141 - models.predict - INFO - Prediction for 15 minutes horizon: 78.6
2025-05-11 20:53:52,164 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-11 20:53:52,291 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:52,292 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-11 20:53:52,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:52,421 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:52,422 - models.predict - INFO - Using scikit-learn svr model for 15 minutes horizon
2025-05-11 20:53:52,423 - models.predict - INFO - Loading svr model for COMI with horizon 15
2025-05-11 20:53:52,423 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_15min.joblib
2025-05-11 20:53:52,423 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_15min.joblib, searching for alternatives...
2025-05-11 20:53:52,424 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-11 20:53:52,425 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:52,425 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:52,426 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-11 20:53:52,426 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-11 20:53:52,426 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:52,426 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:52,427 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:52,427 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 20:53:52,428 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:53:52,428 - models.predict - INFO - Prediction for 15 minutes horizon: 78.6
2025-05-11 20:53:52,448 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-11 20:53:52,577 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:52,577 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-11 20:53:52,711 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:52,712 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:52,713 - models.predict - INFO - Using scikit-learn lr model for 15 minutes horizon
2025-05-11 20:53:52,714 - models.predict - INFO - Loading lr model for COMI with horizon 15
2025-05-11 20:53:52,714 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-05-11 20:53:52,714 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_15min.joblib, searching for alternatives...
2025-05-11 20:53:52,715 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-11 20:53:52,716 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:52,716 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:52,717 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-11 20:53:52,717 - models.sklearn_model - WARNING - Prediction with original shape failed: 'MinMaxScaler' object has no attribute 'predict'. Trying with prepared data.
2025-05-11 20:53:52,719 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:52,719 - models.sklearn_model - ERROR - All prediction attempts failed: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:52,719 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:52,720 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 488, in predict
    return self.model.predict(X)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 494, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 20:53:52,721 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:53:52,721 - models.predict - INFO - Prediction for 15 minutes horizon: 78.6
2025-05-11 20:53:52,723 - models.predict - INFO - Current price for COMI: 78.6
2025-05-11 20:53:52,723 - models.predict - INFO - Prophet prediction for 15 minutes: 78.59268153120432
2025-05-11 20:53:52,742 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-11 20:53:52,868 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:52,868 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-11 20:53:52,998 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:52,999 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:53,001 - models.predict - INFO - Using scikit-learn ensemble model for 15 minutes horizon
2025-05-11 20:53:53,002 - models.predict - INFO - Loading ensemble model for COMI with horizon 15
2025-05-11 20:53:53,002 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_15min.joblib
2025-05-11 20:53:53,002 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_ensemble_15min.joblib, searching for alternatives...
2025-05-11 20:53:53,003 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-11 20:53:53,004 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:53,004 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:53,005 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-11 20:53:53,036 - models.sklearn_model - INFO - Using hybrid model predict method for ensemble
2025-05-11 20:53:53,036 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:53,036 - models.sklearn_model - ERROR - Error in hybrid model prediction: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:53,036 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:53,037 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:53,037 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 413, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 418, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 20:53:53,038 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:53:53,038 - models.predict - INFO - Prediction for 15 minutes horizon: 78.6
2025-05-11 20:53:53,056 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-05-11 20:53:53,184 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:53,185 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.94 KB)
2025-05-11 20:53:53,319 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:53,320 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:53,322 - models.predict - INFO - Using scikit-learn hybrid model for 15 minutes horizon
2025-05-11 20:53:53,322 - models.predict - INFO - Loading hybrid model for COMI with horizon 15
2025-05-11 20:53:53,322 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_15min.joblib
2025-05-11 20:53:53,323 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_15min.joblib, searching for alternatives...
2025-05-11 20:53:53,324 - models.sklearn_model - INFO - Found 2 potential model files: ['COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler_15min.joblib']
2025-05-11 20:53:53,324 - models.sklearn_model - WARNING - Using alternative model file: saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:53,325 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lstm_scaler15min.joblib
2025-05-11 20:53:53,326 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-05-11 20:53:53,326 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-11 20:53:53,326 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:53,327 - models.sklearn_model - ERROR - Error in hybrid model prediction: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:53,327 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:53,327 - models.predict - ERROR - Error in prediction for horizon 15: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 20:53:53,328 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 413, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 418, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 20:53:53,329 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:53:53,329 - models.predict - INFO - Prediction for 15 minutes horizon: 78.6
2025-05-11 20:53:53,354 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:53:53,481 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:53,482 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:53:53,622 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:53,622 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:53,624 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-11 20:53:53,624 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-11 20:53:53,624 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-11 20:53:53,625 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-11 20:53:53,625 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-11 20:53:53,625 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-11 20:53:53,626 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:53:53,627 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-11 20:53:53,628 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-11 20:53:53,635 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-11 20:53:53,636 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-11 20:53:53,636 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:53,637 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-11 20:53:53,637 - models.predict - ERROR - Error in prediction for horizon 60: invalid index to scalar variable.
2025-05-11 20:53:53,637 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-11 20:53:53,638 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:53:53,638 - models.predict - INFO - Prediction for 60 minutes horizon: 78.6
2025-05-11 20:53:53,658 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:53:53,788 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:53,789 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:53:53,922 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:53,922 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:53,924 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-11 20:53:53,925 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-11 20:53:53,925 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-11 20:53:53,925 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-11 20:53:53,927 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:53:53,928 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-11 20:53:53,928 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-11 20:53:53,967 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:53:53,967 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-11 20:53:53,968 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:53,977 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:53,977 - models.predict - INFO - Prediction for 60 minutes horizon: 78.67880000000002
2025-05-11 20:53:53,996 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:53:54,121 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:54,122 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:53:54,257 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:54,258 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:54,259 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-11 20:53:54,260 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-11 20:53:54,260 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-11 20:53:54,260 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-11 20:53:54,261 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:53:54,263 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-11 20:53:54,263 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-11 20:53:54,273 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:53:54,274 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-11 20:53:54,274 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:54,275 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:54,276 - models.predict - INFO - Prediction for 60 minutes horizon: 68.90783548260816
2025-05-11 20:53:54,295 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:53:54,423 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:54,423 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:53:54,550 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:54,551 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:54,552 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-11 20:53:54,553 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-11 20:53:54,553 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-11 20:53:54,553 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-11 20:53:54,555 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:53:54,556 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-11 20:53:54,556 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-11 20:53:54,557 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:53:54,558 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-11 20:53:54,558 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:54,559 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:54,559 - models.predict - INFO - Prediction for 60 minutes horizon: 82.4337148521041
2025-05-11 20:53:54,561 - models.predict - INFO - Current price for COMI: 78.6
2025-05-11 20:53:54,562 - models.predict - INFO - Prophet prediction for 60 minutes: 78.59467916462741
2025-05-11 20:53:54,583 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:53:54,711 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:54,712 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:53:54,852 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:54,852 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:54,854 - models.predict - INFO - Using scikit-learn ensemble model for 60 minutes horizon
2025-05-11 20:53:54,854 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-11 20:53:54,855 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_60min.joblib
2025-05-11 20:53:54,855 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_ensemble_60min.joblib, searching for alternatives...
2025-05-11 20:53:54,856 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:53:54,857 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_ensemble_120960min.joblib
2025-05-11 20:53:54,857 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_120960min.joblib
2025-05-11 20:53:54,971 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:53:54,986 - models.sklearn_model - INFO - Using hybrid model predict method for ensemble
2025-05-11 20:53:55,018 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:55,018 - models.predict - INFO - Prediction for 60 minutes horizon: 79.57993944670866
2025-05-11 20:53:55,036 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:53:55,155 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:55,156 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:53:55,289 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:55,289 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:55,291 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-11 20:53:55,291 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-11 20:53:55,291 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-11 20:53:55,292 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-11 20:53:55,293 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:53:55,294 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-11 20:53:55,295 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-11 20:53:55,323 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:53:55,323 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-11 20:53:55,332 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:55,332 - models.predict - INFO - Prediction for 60 minutes horizon: 78.67880000000002
2025-05-11 20:53:55,352 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 20:53:55,480 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:55,480 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 20:53:55,616 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:55,616 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:55,618 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-05-11 20:53:55,619 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-05-11 20:53:56,184 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 20:53:57,119 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-11 20:53:57,119 - models.predict - INFO - Prediction for 30 minutes horizon: 72.6224151611328
2025-05-11 20:53:57,134 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 20:53:57,284 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:57,284 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 20:53:57,419 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:57,419 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:57,419 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-05-11 20:53:57,419 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-05-11 20:53:57,419 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-05-11 20:53:57,419 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-05-11 20:53:57,451 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 20:53:57,451 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-11 20:53:57,451 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:57,468 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:57,468 - models.predict - INFO - Prediction for 30 minutes horizon: 78.71330000000003
2025-05-11 20:53:57,484 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 20:53:57,619 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:57,619 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 20:53:57,734 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:57,734 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:57,734 - models.predict - INFO - Using scikit-learn svr model for 30 minutes horizon
2025-05-11 20:53:57,734 - models.predict - INFO - Loading svr model for COMI with horizon 30
2025-05-11 20:53:57,734 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_30min.joblib
2025-05-11 20:53:57,734 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_30min.joblib
2025-05-11 20:53:57,751 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 20:53:57,751 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-11 20:53:57,751 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:57,751 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:57,751 - models.predict - INFO - Prediction for 30 minutes horizon: 68.48789895837452
2025-05-11 20:53:57,768 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 20:53:57,901 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:57,901 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 20:53:58,019 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:58,019 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:58,019 - models.predict - INFO - Using scikit-learn lr model for 30 minutes horizon
2025-05-11 20:53:58,033 - models.predict - INFO - Loading lr model for COMI with horizon 30
2025-05-11 20:53:58,033 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_30min.joblib
2025-05-11 20:53:58,034 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_30min.joblib
2025-05-11 20:53:58,034 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 20:53:58,034 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-05-11 20:53:58,034 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:58,034 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:58,034 - models.predict - INFO - Prediction for 30 minutes horizon: 79.69787000044474
2025-05-11 20:53:58,034 - models.predict - INFO - Current price for COMI: 78.6
2025-05-11 20:53:58,034 - models.predict - INFO - Prophet prediction for 30 minutes: 78.6424735381193
2025-05-11 20:53:58,051 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 20:53:58,184 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:58,184 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 20:53:58,319 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:58,319 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:53:58,319 - models.predict - INFO - Using scikit-learn ensemble model for 30 minutes horizon
2025-05-11 20:53:58,319 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-05-11 20:53:58,319 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_30min.joblib
2025-05-11 20:53:58,319 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_30min.joblib
2025-05-11 20:53:58,351 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 20:53:58,368 - models.sklearn_model - INFO - Using hybrid model predict method for ensemble
2025-05-11 20:53:58,384 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:58,384 - models.predict - INFO - Prediction for 30 minutes horizon: 80.62443255257428
2025-05-11 20:53:58,401 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 20:53:58,520 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:58,520 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 20:53:58,668 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:53:58,668 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-11 20:53:58,668 - models.predict - INFO - Using scikit-learn hybrid model for 30 minutes horizon
2025-05-11 20:53:58,668 - models.predict - INFO - Loading hybrid model for COMI with horizon 30
2025-05-11 20:53:58,668 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_30min.joblib
2025-05-11 20:53:58,668 - models.sklearn_model - INFO - Found hybrid model at saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-11 20:53:58,668 - models.sklearn_model - INFO - Loading model from saved_models\COMI_arima_ml_rf_30min.joblib
2025-05-11 20:53:58,701 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 20:53:58,701 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-11 20:53:58,701 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:53:58,701 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:53:58,701 - models.predict - INFO - Prediction for 30 minutes horizon: 78.71330000000003
2025-05-11 20:53:58,723 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 15 minutes
2025-05-11 20:53:58,737 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 60 minutes
2025-05-11 20:53:58,751 - app.components.performance_metrics - INFO - Saved prediction record for COMI with horizon 30 minutes
2025-05-11 20:53:58,779 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 20:54:00,901 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:00,902 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:54:01,034 - app.utils.memory_management - INFO - Garbage collection: collected 872 objects
2025-05-11 20:54:01,034 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:01,035 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:54:24,503 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:54:24,503 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 20:54:24,528 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:24,528 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:54:24,664 - app.utils.memory_management - INFO - Garbage collection: collected 238 objects
2025-05-11 20:54:24,665 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:24,665 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:54:25,701 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:54:25,748 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:54:25,867 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:25,882 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:54:26,013 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:26,014 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:54:26,017 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-05-11 20:54:26,018 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-11 20:54:26,019 - models.predict - ERROR - Model file not found or import error: Model not found at saved_models\COMI_lstm_60min.keras or saved_models\COMI_lstm_60min.h5
2025-05-11 20:54:26,019 - models.predict - WARNING - Attempting to create a fallback model for lstm
2025-05-11 20:54:26,019 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-11 20:54:26,020 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-11 20:54:26,021 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:54:26,022 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-11 20:54:26,022 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-11 20:54:26,023 - models.predict - INFO - Successfully loaded fallback linear regression model
2025-05-11 20:54:26,023 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-11 20:54:26,024 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:54:26,024 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1,)
2025-05-11 20:54:26,024 - models.predict - ERROR - Error in prediction for horizon 60: invalid index to scalar variable.
2025-05-11 20:54:26,025 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 491, in predict_future_prices
    pred_scaled = prediction[0][0]
IndexError: invalid index to scalar variable.

2025-05-11 20:54:26,025 - models.predict - INFO - Using fallback price due to error: 78.6
2025-05-11 20:54:26,025 - models.predict - INFO - Prediction for 60 minutes horizon: 78.6
2025-05-11 20:54:26,047 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:54:26,168 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:26,168 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:54:26,301 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:26,301 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:54:26,301 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-05-11 20:54:26,301 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-05-11 20:54:26,301 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-05-11 20:54:26,301 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_60min.joblib, searching for alternatives...
2025-05-11 20:54:26,316 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:54:26,317 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_rf_120960min.joblib
2025-05-11 20:54:26,317 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_120960min.joblib
2025-05-11 20:54:26,334 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:54:26,334 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-05-11 20:54:26,334 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:54:26,350 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:54:26,350 - models.predict - INFO - Prediction for 60 minutes horizon: 78.78420000000001
2025-05-11 20:54:26,367 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:54:26,484 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:26,499 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:54:26,634 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:26,634 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:54:26,634 - models.predict - INFO - Using scikit-learn svr model for 60 minutes horizon
2025-05-11 20:54:26,634 - models.predict - INFO - Loading svr model for COMI with horizon 60
2025-05-11 20:54:26,634 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_svr_60min.joblib
2025-05-11 20:54:26,634 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_svr_60min.joblib, searching for alternatives...
2025-05-11 20:54:26,634 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:54:26,634 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_svr_120960min.joblib
2025-05-11 20:54:26,634 - models.sklearn_model - INFO - Loading model from saved_models\COMI_svr_120960min.joblib
2025-05-11 20:54:26,634 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:54:26,634 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. SVR expected <= 2.. Trying with prepared data.
2025-05-11 20:54:26,650 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:54:26,651 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:54:26,651 - models.predict - INFO - Prediction for 60 minutes horizon: 68.90777645810715
2025-05-11 20:54:26,667 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:54:26,800 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:26,800 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:54:26,934 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:26,934 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:54:26,934 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-05-11 20:54:26,934 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-05-11 20:54:26,934 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-05-11 20:54:26,934 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_lr_60min.joblib, searching for alternatives...
2025-05-11 20:54:26,934 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:54:26,934 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_lr_120960min.joblib
2025-05-11 20:54:26,934 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_120960min.joblib
2025-05-11 20:54:26,934 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:54:26,934 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. LinearRegression expected <= 2.. Trying with prepared data.
2025-05-11 20:54:26,934 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 20:54:26,934 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:54:26,934 - models.predict - INFO - Prediction for 60 minutes horizon: 79.54160207413196
2025-05-11 20:54:26,934 - models.predict - INFO - Current price for COMI: 78.6
2025-05-11 20:54:26,934 - models.predict - INFO - Prophet prediction for 60 minutes: 78.73147440373914
2025-05-11 20:54:26,968 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:54:27,099 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:27,099 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:54:27,234 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:27,234 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:54:27,234 - models.predict - INFO - Using scikit-learn ensemble model for 60 minutes horizon
2025-05-11 20:54:27,234 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-05-11 20:54:27,234 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_60min.joblib
2025-05-11 20:54:27,234 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_ensemble_60min.joblib, searching for alternatives...
2025-05-11 20:54:27,234 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:54:27,234 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_ensemble_120960min.joblib
2025-05-11 20:54:27,234 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_120960min.joblib
2025-05-11 20:54:27,267 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:54:27,284 - models.sklearn_model - INFO - Using hybrid model predict method for ensemble
2025-05-11 20:54:27,301 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:54:27,301 - models.predict - INFO - Prediction for 60 minutes horizon: 78.938018689091
2025-05-11 20:54:27,317 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-05-11 20:54:27,434 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:27,434 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.94 KB)
2025-05-11 20:54:27,567 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:27,567 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:54:27,567 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-05-11 20:54:27,567 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-05-11 20:54:27,567 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-05-11 20:54:27,567 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_hybrid_60min.joblib, searching for alternatives...
2025-05-11 20:54:27,584 - models.sklearn_model - INFO - Found 62 potential model files: ['COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_20160min.joblib']
2025-05-11 20:54:27,584 - models.sklearn_model - INFO - Found matching model file: saved_models\COMI_hybrid_120960min.joblib
2025-05-11 20:54:27,584 - models.sklearn_model - INFO - Loading model from saved_models\COMI_hybrid_120960min.joblib
2025-05-11 20:54:27,617 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-05-11 20:54:27,617 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-05-11 20:54:27,617 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 20:54:27,617 - models.predict - INFO - Prediction for 60 minutes horizon: 78.78420000000001
2025-05-11 20:54:27,670 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:27,670 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:54:27,799 - app.utils.memory_management - INFO - Garbage collection: collected 1060 objects
2025-05-11 20:54:27,800 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:54:27,801 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:55:07,336 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:55:07,356 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:55:07,356 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:55:07,506 - app.utils.memory_management - INFO - Garbage collection: collected 241 objects
2025-05-11 20:55:07,507 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:55:07,507 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:55:07,800 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:55:07,839 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:49:00.975097 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,849 - app.components.performance_metrics - INFO - Target time 2025-05-11 09:59:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,858 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:09:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,888 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,913 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:01:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,922 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:11:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,932 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,941 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:03:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,951 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:13:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,959 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,966 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:08:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,966 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:18:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,984 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:03:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:07,992 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:29:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,000 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:39:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,008 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:24:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,017 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:32:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,025 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:42:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,032 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:57:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,032 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:27:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,049 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:33:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,057 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:43:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,066 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,066 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:28:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,082 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:36:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,082 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,098 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,099 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,099 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,116 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,116 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:11:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,132 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:41:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,148 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:48:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,149 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,165 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:13:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,166 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:43:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,181 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:51:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,183 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,199 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,207 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:46:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,216 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,223 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:04:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,231 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:19:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,233 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:49:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,233 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:05:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,249 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:15:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,258 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:30:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,266 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:00:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,266 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:06:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,283 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,300 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,300 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:01:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,323 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:51:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,332 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:01:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,340 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:16:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,350 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:46:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,358 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:52:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,367 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:02:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,367 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:47:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,383 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:01:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,387 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:11:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,402 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:26:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,402 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:56:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,417 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:15:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,417 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:25:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,433 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:40:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,438 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:10:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:08,529 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:55:08,529 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:55:08,670 - app.utils.memory_management - INFO - Garbage collection: collected 1284 objects
2025-05-11 20:55:08,670 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:55:08,671 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:55:24,750 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:55:24,779 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:49:00.975097 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,790 - app.components.performance_metrics - INFO - Target time 2025-05-11 09:59:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,800 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:09:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,810 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,819 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:01:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,829 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:11:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,833 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,833 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:03:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,849 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:13:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,849 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,865 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:08:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,883 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:18:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,883 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:03:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,899 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:29:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,899 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:39:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,916 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:24:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,916 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:32:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,933 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:42:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,933 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:57:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,949 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:27:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,949 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:33:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,969 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:43:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,969 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,983 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:28:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:24,983 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:36:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,003 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,011 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,020 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,028 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,032 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,032 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:11:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,049 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:41:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,049 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:48:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,070 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,078 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:13:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,086 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:43:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,094 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:51:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,099 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,099 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,116 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:46:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,116 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,136 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:04:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,144 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:19:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,154 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:49:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,162 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:05:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,166 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:15:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,166 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:30:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,183 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:00:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,183 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:06:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,199 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,199 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,216 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:01:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,216 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:51:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,233 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:01:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,233 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:16:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,250 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:46:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,250 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:52:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,266 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:02:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,266 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:47:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,283 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:01:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,298 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:11:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,299 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:26:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,316 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:56:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,316 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:15:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,334 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:25:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,334 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:40:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,350 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:10:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:55:25,442 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:55:25,443 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:55:25,571 - app.utils.memory_management - INFO - Garbage collection: collected 1409 objects
2025-05-11 20:55:25,571 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:55:25,571 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:56:20,317 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:56:20,354 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:49:00.975097 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,363 - app.components.performance_metrics - INFO - Target time 2025-05-11 09:59:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,370 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:09:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,382 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,382 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:01:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,398 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:11:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,398 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,415 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:03:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,432 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:13:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,432 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,448 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:08:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,448 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:18:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,465 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:03:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,465 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:29:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,482 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:39:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,482 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:24:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,500 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:32:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,500 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:42:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,520 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:57:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,527 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:27:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,534 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:33:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,534 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:43:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,554 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,562 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:28:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,566 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:36:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,566 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,587 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,595 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,604 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,612 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,615 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:11:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,615 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:41:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,631 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:48:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,648 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,649 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:13:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,665 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:43:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,673 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:51:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,682 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,690 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,698 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:46:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,698 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,715 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:04:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,715 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:19:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,731 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:49:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,732 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:05:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,748 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:15:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,748 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:30:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,764 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:00:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,765 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:06:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,782 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,782 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,799 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:01:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,799 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:51:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,816 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:01:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,816 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:16:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,832 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:46:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,832 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:52:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,849 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:02:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,849 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:47:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,866 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:01:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,866 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:11:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,883 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:26:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,883 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:56:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,899 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:15:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,899 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:25:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,919 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:40:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:20,932 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:10:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 20:56:21,031 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:56:21,032 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:56:21,199 - app.utils.memory_management - INFO - Garbage collection: collected 1374 objects
2025-05-11 20:56:21,199 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:56:21,199 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:57:26,245 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:57:26,263 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-05-11 20:57:26,266 - app.utils.data_processing - INFO - Found lstm model for COMI with 69 minutes horizon
2025-05-11 20:57:26,268 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:26,268 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:57:26,413 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-11 20:57:26,413 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:26,413 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:57:35,082 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:57:35,097 - app.utils.data_processing - INFO - Found bilstm model for COMI with 4 minutes horizon
2025-05-11 20:57:35,104 - app.utils.data_processing - INFO - Found bilstm model for COMI with 69 minutes horizon
2025-05-11 20:57:35,106 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:35,107 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:57:35,241 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-11 20:57:35,241 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:35,242 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:57:38,299 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:57:38,314 - app.utils.data_processing - INFO - Found bilstm model for COMI with 4 minutes horizon
2025-05-11 20:57:38,314 - app.utils.data_processing - INFO - Found bilstm model for COMI with 69 minutes horizon
2025-05-11 20:57:38,314 - app.utils.data_processing - INFO - Found bilstm model for COMI with 30 minutes horizon
2025-05-11 20:57:38,314 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:38,314 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:57:38,450 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-11 20:57:38,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:38,451 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:57:40,998 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:57:41,014 - app.utils.data_processing - INFO - Found bilstm model for COMI with 4 minutes horizon
2025-05-11 20:57:41,015 - app.utils.data_processing - INFO - Found bilstm model for COMI with 69 minutes horizon
2025-05-11 20:57:41,015 - app.utils.data_processing - INFO - Found bilstm model for COMI with 30 minutes horizon
2025-05-11 20:57:41,045 - models.predict - INFO - Making predictions for 4 minutes horizon
2025-05-11 20:57:41,180 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:41,180 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4_scaler.pkl (0.94 KB)
2025-05-11 20:57:41,314 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:41,314 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-11 20:57:41,329 - models.predict - INFO - Loading bilstm model for COMI with horizon 4
2025-05-11 20:57:41,329 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_4min.keras
2025-05-11 20:57:42,448 - models.predict - INFO - Successfully loaded model for COMI with horizon 4
2025-05-11 20:57:44,081 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-11 20:57:44,081 - models.predict - INFO - Prediction for 4 minutes horizon: 69.16465055942535
2025-05-11 20:57:44,081 - models.predict - INFO - Making predictions for 69 minutes horizon
2025-05-11 20:57:44,247 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:44,247 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_69_scaler.pkl (0.94 KB)
2025-05-11 20:57:44,397 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:44,397 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:57:44,397 - models.predict - INFO - Loading bilstm model for COMI with horizon 69
2025-05-11 20:57:44,397 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_69min.keras
2025-05-11 20:57:45,514 - models.predict - INFO - Successfully loaded model for COMI with horizon 69
2025-05-11 20:57:47,130 - tensorflow - WARNING - 5 out of the last 5 calls to <function Model.make_predict_function.<locals>.predict_function at 0x000001E1FE927910> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 20:57:47,130 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-11 20:57:47,146 - models.predict - INFO - Prediction for 69 minutes horizon: 69.29225645065307
2025-05-11 20:57:47,147 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-05-11 20:57:47,347 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:47,347 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.94 KB)
2025-05-11 20:57:47,481 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:47,481 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-11 20:57:47,481 - models.predict - INFO - Loading bilstm model for COMI with horizon 30
2025-05-11 20:57:47,481 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_30min.keras
2025-05-11 20:57:48,598 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-05-11 20:57:50,214 - tensorflow - WARNING - 6 out of the last 6 calls to <function Model.make_predict_function.<locals>.predict_function at 0x000001E1FED360E0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-05-11 20:57:50,214 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-05-11 20:57:50,214 - models.predict - INFO - Prediction for 30 minutes horizon: 67.54607564210892
2025-05-11 20:57:50,247 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:50,247 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:57:50,499 - app.utils.memory_management - INFO - Garbage collection: collected 60093 objects
2025-05-11 20:57:50,499 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:57:50,499 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:58:30,416 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:58:30,437 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:30,437 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:58:30,579 - app.utils.memory_management - INFO - Garbage collection: collected 224 objects
2025-05-11 20:58:30,579 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:30,583 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:58:32,595 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:58:32,612 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:32,612 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:58:32,755 - app.utils.memory_management - INFO - Garbage collection: collected 210 objects
2025-05-11 20:58:32,756 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:32,756 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:58:33,829 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:58:33,829 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 20:58:33,854 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-11 20:58:33,855 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-11 20:58:33,855 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-11 20:58:33,855 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-11 20:58:34,033 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.20 seconds
2025-05-11 20:58:34,211 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:34,212 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:58:34,337 - app.utils.memory_management - INFO - Garbage collection: collected 2267 objects
2025-05-11 20:58:34,337 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:34,338 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:58:48,667 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:58:48,676 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 20:58:48,697 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-11 20:58:48,697 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 20:58:48,697 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-11 20:58:48,697 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-11 20:58:48,920 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.24 seconds
2025-05-11 20:58:49,098 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:49,099 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:58:49,226 - app.utils.memory_management - INFO - Garbage collection: collected 2156 objects
2025-05-11 20:58:49,226 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:49,227 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:58:58,766 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:58:58,778 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 20:58:58,790 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 20:58:58,791 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 20:58:58,791 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-11 20:58:58,792 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-11 20:58:59,039 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.26 seconds
2025-05-11 20:58:59,229 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:59,229 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:58:59,359 - app.utils.memory_management - INFO - Garbage collection: collected 2464 objects
2025-05-11 20:58:59,360 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:58:59,360 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:59:02,542 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:59:02,555 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 20:59:02,557 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:59:02,558 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:59:02,696 - app.utils.memory_management - INFO - Garbage collection: collected 315 objects
2025-05-11 20:59:02,700 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:59:02,700 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:59:03,001 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:59:03,014 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 20:59:03,025 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 20:59:03,026 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 20:59:03,026 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-11 20:59:03,026 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-11 20:59:03,228 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.22 seconds
2025-05-11 20:59:03,419 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:59:03,419 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:59:03,545 - app.utils.memory_management - INFO - Garbage collection: collected 1526 objects
2025-05-11 20:59:03,545 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:59:03,545 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 20:59:08,598 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 20:59:08,607 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-11 20:59:08,618 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 20:59:08,619 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 20:59:08,619 - app.utils.common - INFO - Data shape: (567, 36)
2025-05-11 20:59:08,619 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-11 20:59:08,853 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.25 seconds
2025-05-11 20:59:09,028 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:59:09,028 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 20:59:09,160 - app.utils.memory_management - INFO - Garbage collection: collected 2077 objects
2025-05-11 20:59:09,160 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 20:59:09,160 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:01:00,596 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:01:00,632 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 21:01:00,662 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:49:00.975097 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,672 - app.components.performance_metrics - INFO - Target time 2025-05-11 09:59:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,684 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:09:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,697 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,706 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:01:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,716 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:11:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,727 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,736 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:03:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,746 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:13:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,756 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,765 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:08:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,773 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:18:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,783 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:03:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,791 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:29:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,800 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:39:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,808 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:24:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,818 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:32:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,827 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:42:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,835 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:57:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,845 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:27:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,854 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:33:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,862 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:43:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,870 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,879 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:28:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,888 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:36:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,896 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,905 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,913 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,922 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,930 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,939 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:11:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,947 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:41:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,955 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:48:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,964 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,972 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:13:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,981 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:43:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,990 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:51:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:00,998 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,006 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,014 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:46:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,023 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,031 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:04:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,040 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:19:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,048 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:49:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,057 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:05:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,066 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:15:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,074 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:30:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,083 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:00:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,091 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:06:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,100 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,108 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,118 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:01:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,126 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:51:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,134 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:01:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,143 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:16:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,152 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:46:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,160 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:52:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,168 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:02:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,177 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:47:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,185 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:01:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,193 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:11:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,201 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:26:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,210 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:56:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,219 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:15:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,227 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:25:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,235 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:40:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,244 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:10:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,253 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:57:15.487345 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:01,347 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:01:01,347 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:01:01,471 - app.utils.memory_management - INFO - Garbage collection: collected 1506 objects
2025-05-11 21:01:01,471 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:01:01,487 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:01:12,819 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:01:12,847 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:49:00.975097 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,860 - app.components.performance_metrics - INFO - Target time 2025-05-11 09:59:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,870 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:09:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,879 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:09.053978 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,887 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:01:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,893 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:11:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,893 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:58.895566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,916 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:03:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,926 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:13:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,926 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:49.743276 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,943 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:08:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,943 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:18:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,960 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:03:01.661739 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,960 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:29:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,978 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:39:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,978 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:24:04.469422 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,993 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:32:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:12,993 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:42:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,012 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:57:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,022 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:27:11.296940 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,027 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:33:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,027 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:43:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,049 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,058 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:28:44.109776 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,066 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:36:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,075 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,076 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,092 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:04.327966 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,093 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:46:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,110 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:56:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,119 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:11:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,129 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:41:29.623566 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,138 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:48:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,144 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:58:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,144 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:13:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,160 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:43:06.600056 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,160 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:51:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,177 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:01:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,177 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,200 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:46:05.698040 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,208 - app.components.performance_metrics - INFO - Target time 2025-05-11 10:54:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,210 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:04:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,225 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:19:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,227 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:49:49.748273 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,243 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:05:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,244 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:15:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,260 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:30:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,260 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:00:34.451465 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,277 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:06:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,277 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:16:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,294 - app.components.performance_metrics - INFO - Target time 2025-05-11 11:31:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,294 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:01:50.625659 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,310 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:51:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,310 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:01:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,327 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:16:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,327 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:46:13.237265 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,344 - app.components.performance_metrics - INFO - Target time 2025-05-11 12:52:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,344 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:02:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,361 - app.components.performance_metrics - INFO - Target time 2025-05-11 13:47:47.715562 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,361 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:01:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,377 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:11:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,393 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:26:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,394 - app.components.performance_metrics - INFO - Target time 2025-05-11 17:56:01.112766 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,411 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:15:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,411 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:25:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,427 - app.components.performance_metrics - INFO - Target time 2025-05-11 19:40:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,427 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:10:20.845824 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,444 - app.components.performance_metrics - INFO - Target time 2025-05-11 20:57:15.487345 is in the future compared to latest data 2025-05-11 00:00:00
2025-05-11 21:01:13,537 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:01:13,538 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:01:13,662 - app.utils.memory_management - INFO - Garbage collection: collected 1416 objects
2025-05-11 21:01:13,673 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:01:13,674 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:53:17,011 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 21:53:17,029 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 21:53:17,029 - app - INFO - Memory management utilities loaded
2025-05-11 21:53:17,030 - app - INFO - Error handling utilities loaded
2025-05-11 21:53:17,031 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 21:53:17,031 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 21:53:17,031 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 21:53:17,031 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 21:53:17,032 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 21:53:17,032 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 21:53:17,032 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 21:53:17,032 - app - INFO - Applied NumPy fix
2025-05-11 21:53:17,034 - app.config - INFO - Configuration initialized
2025-05-11 21:53:17,040 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 21:53:17,040 - models.train - INFO - TensorFlow test successful
2025-05-11 21:53:17,042 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 21:53:17,043 - models.train - INFO - Transformer model is available
2025-05-11 21:53:17,043 - models.train - INFO - Using TensorFlow-based models
2025-05-11 21:53:17,044 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 21:53:17,044 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 21:53:17,044 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:53:17,079 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 21:53:17,094 - app.utils.session_state - INFO - Initializing session state
2025-05-11 21:53:17,095 - app.utils.session_state - INFO - Session state initialized
2025-05-11 21:53:17,103 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 21:53:17,108 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:53:17,108 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:53:17,263 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-05-11 21:53:17,263 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:53:17,263 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:53:21,787 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:53:21,801 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:53:21,802 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:53:21,936 - app.utils.memory_management - INFO - Garbage collection: collected 234 objects
2025-05-11 21:53:21,937 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:53:21,938 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:00,052 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:54:00,081 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 21:54:00,082 - app.components.chat - INFO - Query: 'what is the close price of COMI stock?', Intent: price_query, Entities: {'symbols': ['COMI']}
2025-05-11 21:54:00,095 - app.utils.state_manager - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 21:54:00,095 - app.utils.state_manager - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 21:54:00,096 - app.utils.state_manager - INFO - Data shape: (567, 36)
2025-05-11 21:54:00,096 - app.utils.state_manager - INFO - File COMI contains 2025 data
2025-05-11 21:54:00,137 - app.utils.session_state - ERROR - Error tracked: app_crash - `st.session_state.user_message` cannot be modified after the widget with key `user_message` is instantiated.
2025-05-11 21:54:00,137 - app - ERROR - Application crashed: `st.session_state.user_message` cannot be modified after the widget with key `user_message` is instantiated.
2025-05-11 21:54:00,137 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:00,137 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:00,269 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-11 21:54:00,269 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:00,269 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:00,269 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:00,269 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:00,402 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 21:54:00,402 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:00,402 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:06,636 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:54:06,654 - app.components.chat - INFO - Query: 'what is the close price of COMI stock?', Intent: price_query, Entities: {'symbols': ['COMI']}
2025-05-11 21:54:06,654 - app.utils.state_manager - INFO - Using cached data for COMI from session state
2025-05-11 21:54:06,658 - app.utils.session_state - ERROR - Error tracked: app_crash - `st.session_state.user_message` cannot be modified after the widget with key `user_message` is instantiated.
2025-05-11 21:54:06,658 - app - ERROR - Application crashed: `st.session_state.user_message` cannot be modified after the widget with key `user_message` is instantiated.
2025-05-11 21:54:06,659 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:06,659 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:06,788 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-11 21:54:06,789 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:06,790 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:06,791 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:06,791 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:06,919 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 21:54:06,919 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:06,919 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:38,810 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:54:38,813 - app.utils.session_state - INFO - Initializing session state
2025-05-11 21:54:38,814 - app.utils.session_state - INFO - Session state initialized
2025-05-11 21:54:38,825 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:38,825 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:38,956 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-05-11 21:54:38,957 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:38,957 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:54,168 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:54:54,184 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:54,184 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:54,326 - app.utils.memory_management - INFO - Garbage collection: collected 233 objects
2025-05-11 21:54:54,326 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:54,327 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:58,618 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:54:58,634 - app.utils.session_state - INFO - Initializing session state
2025-05-11 21:54:58,651 - app.utils.session_state - INFO - Session state initialized
2025-05-11 21:54:58,666 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:58,684 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:58,833 - app.utils.memory_management - INFO - Garbage collection: collected 345 objects
2025-05-11 21:54:58,850 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:58,866 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:58,901 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:54:59,068 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 21:54:59,068 - app.components.chat - INFO - Query: 'what is the close price of COMI stock?', Intent: price_query, Entities: {'symbols': ['COMI']}
2025-05-11 21:54:59,075 - app.utils.state_manager - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 21:54:59,075 - app.utils.state_manager - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 21:54:59,075 - app.utils.state_manager - INFO - Data shape: (567, 36)
2025-05-11 21:54:59,075 - app.utils.state_manager - INFO - File COMI contains 2025 data
2025-05-11 21:54:59,085 - app.utils.session_state - ERROR - Error tracked: app_crash - `st.session_state.user_message` cannot be modified after the widget with key `user_message` is instantiated.
2025-05-11 21:54:59,085 - app - ERROR - Application crashed: `st.session_state.user_message` cannot be modified after the widget with key `user_message` is instantiated.
2025-05-11 21:54:59,086 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:59,086 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:59,202 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-05-11 21:54:59,202 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:59,202 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:54:59,202 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:59,202 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:54:59,350 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 21:54:59,351 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:54:59,351 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:57:19,332 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 21:57:19,355 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 21:57:19,355 - app - INFO - Memory management utilities loaded
2025-05-11 21:57:19,356 - app - INFO - Error handling utilities loaded
2025-05-11 21:57:19,357 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 21:57:19,357 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 21:57:19,358 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 21:57:19,358 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 21:57:19,358 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 21:57:19,358 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 21:57:19,359 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 21:57:19,359 - app - INFO - Applied NumPy fix
2025-05-11 21:57:19,360 - app.config - INFO - Configuration initialized
2025-05-11 21:57:19,363 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 21:57:19,364 - models.train - INFO - TensorFlow test successful
2025-05-11 21:57:19,366 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 21:57:19,367 - models.train - INFO - Transformer model is available
2025-05-11 21:57:19,367 - models.train - INFO - Using TensorFlow-based models
2025-05-11 21:57:19,368 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 21:57:19,369 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 21:57:19,369 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:57:19,393 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 21:57:19,406 - app.utils.session_state - INFO - Initializing session state
2025-05-11 21:57:19,407 - app.utils.session_state - INFO - Session state initialized
2025-05-11 21:57:19,415 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:57:19,416 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:57:19,554 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-05-11 21:57:19,555 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:57:19,555 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:57:22,365 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:57:22,380 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:57:22,382 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:57:22,514 - app.utils.memory_management - INFO - Garbage collection: collected 233 objects
2025-05-11 21:57:22,514 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:57:22,514 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:57:47,348 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:57:47,375 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 21:57:47,375 - app.components.chat - INFO - Query: 'what is current price of COMI?', Intent: price_query, Entities: {'symbols': ['COMI']}
2025-05-11 21:57:47,381 - app.utils.state_manager - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 21:57:47,381 - app.utils.state_manager - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 21:57:47,381 - app.utils.state_manager - INFO - Data shape: (567, 36)
2025-05-11 21:57:47,381 - app.utils.state_manager - INFO - File COMI contains 2025 data
2025-05-11 21:57:47,381 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:57:47,381 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:57:47,514 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-05-11 21:57:47,514 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:57:47,526 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:57:47,798 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:57:47,816 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:57:47,816 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:57:47,948 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 21:57:47,948 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:57:47,948 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:58:32,997 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:58:33,015 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 21:58:33,032 - app.components.chat - INFO - Query: 'what is the closed price of SWDY?', Intent: price_query, Entities: {'symbols': ['SWDY']}
2025-05-11 21:58:33,033 - app.utils.state_manager - INFO - Loaded stock data for SWDY from data/stocks\SWDY.csv in 0.00 seconds
2025-05-11 21:58:33,033 - app.utils.state_manager - INFO - Date range: 2023-12-31 to 2025-04-08
2025-05-11 21:58:33,033 - app.utils.state_manager - INFO - Data shape: (307, 6)
2025-05-11 21:58:33,033 - app.utils.state_manager - INFO - File SWDY contains 2025 data
2025-05-11 21:58:33,033 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:58:33,033 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:58:33,180 - app.utils.memory_management - INFO - Garbage collection: collected 263 objects
2025-05-11 21:58:33,180 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:58:33,180 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 21:58:33,447 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 21:58:33,482 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:58:33,482 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 21:58:33,652 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 21:58:33,653 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 21:58:33,653 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:07:12,990 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:07:13,001 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 22:07:13,001 - app - INFO - Memory management utilities loaded
2025-05-11 22:07:13,002 - app - INFO - Error handling utilities loaded
2025-05-11 22:07:13,003 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:07:13,004 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:07:13,004 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:07:13,004 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:07:13,005 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:07:13,005 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 22:07:13,005 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:07:13,005 - app - INFO - Applied NumPy fix
2025-05-11 22:07:13,007 - app.config - INFO - Configuration initialized
2025-05-11 22:07:13,011 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 22:07:13,012 - models.train - INFO - TensorFlow test successful
2025-05-11 22:07:13,013 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 22:07:13,014 - models.train - INFO - Transformer model is available
2025-05-11 22:07:13,014 - models.train - INFO - Using TensorFlow-based models
2025-05-11 22:07:13,015 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 22:07:13,016 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 22:07:13,016 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:07:13,045 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:07:13,062 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:07:13,063 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:07:13,068 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:07:13,075 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:07:13,076 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:07:13,231 - app.utils.memory_management - INFO - Garbage collection: collected 292 objects
2025-05-11 22:07:13,231 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:07:13,231 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:07:16,454 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:07:16,472 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:07:16,474 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:07:16,607 - app.utils.memory_management - INFO - Garbage collection: collected 234 objects
2025-05-11 22:07:16,608 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:07:16,609 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:07:43,371 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:07:43,400 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 22:07:43,401 - app.components.chat - INFO - Query: 'can you get some news about COMI', Intent: news_query, Entities: {'symbols': ['COMI']}
2025-05-11 22:07:46,275 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:07:46,275 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:07:46,410 - app.utils.memory_management - INFO - Garbage collection: collected 339 objects
2025-05-11 22:07:46,411 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:07:46,412 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:07:46,692 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:07:46,702 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:07:46,702 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:07:46,835 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:07:46,837 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:07:46,837 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:12:56,703 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:12:56,711 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 22:12:56,711 - app - INFO - Memory management utilities loaded
2025-05-11 22:12:56,712 - app - INFO - Error handling utilities loaded
2025-05-11 22:12:56,714 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:12:56,714 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:12:56,714 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:12:56,714 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:12:56,715 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:12:56,715 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 22:12:56,715 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:12:56,715 - app - INFO - Applied NumPy fix
2025-05-11 22:12:56,717 - app.config - INFO - Configuration initialized
2025-05-11 22:12:56,720 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 22:12:56,721 - models.train - INFO - TensorFlow test successful
2025-05-11 22:12:56,723 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 22:12:56,723 - models.train - INFO - Transformer model is available
2025-05-11 22:12:56,724 - models.train - INFO - Using TensorFlow-based models
2025-05-11 22:12:56,725 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 22:12:56,725 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 22:12:56,726 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:12:56,754 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:12:56,770 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:12:56,772 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:12:56,779 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:12:56,786 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:12:56,787 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:12:56,941 - app.utils.memory_management - INFO - Garbage collection: collected 317 objects
2025-05-11 22:12:56,941 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:12:56,942 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:12:59,481 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:12:59,513 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:12:59,514 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:12:59,652 - app.utils.memory_management - INFO - Garbage collection: collected 234 objects
2025-05-11 22:12:59,653 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:12:59,653 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:13:37,131 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:13:37,167 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 22:13:37,167 - app.components.chat - INFO - Query: 'what is the latest news about COMI?', Intent: news_query, Entities: {'symbols': ['COMI']}
2025-05-11 22:13:39,950 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:13:39,950 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:13:40,093 - app.utils.memory_management - INFO - Garbage collection: collected 343 objects
2025-05-11 22:13:40,093 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:13:40,094 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:13:40,380 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:13:40,413 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:13:40,414 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:13:40,588 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:13:40,588 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:13:40,589 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:14:43,531 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:14:43,567 - app.components.chat - INFO - Query: 'can predict COMI price?', Intent: price_query, Entities: {'symbols': ['COMI']}
2025-05-11 22:14:43,568 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:14:44,716 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:14:44,718 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-11 22:14:44,749 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 78.88
2025-05-11 22:14:44,750 - scrapers.price_scraper - INFO - Generated sample price for COMI: 79.03
2025-05-11 22:14:44,751 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-11 22:14:44,751 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-11 22:14:44,751 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-11 22:14:50,872 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-11 22:14:53,021 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-11 22:14:53,033 - scrapers.price_scraper - INFO - Successfully extracted price from header: 78.6
2025-05-11 22:14:53,033 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 78.6
2025-05-11 22:14:53,034 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-11 22:14:53,034 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-11 22:14:53,034 - app.utils.error_handling - INFO - fetch_price executed in 8.28 seconds
2025-05-11 22:14:53,035 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:14:56,848 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:14:56,848 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:14:56,986 - app.utils.memory_management - INFO - Garbage collection: collected 299 objects
2025-05-11 22:14:56,986 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:14:56,986 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:14:57,283 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:14:57,302 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:14:57,302 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:14:57,435 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:14:57,437 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:14:57,437 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:16:07,026 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:16:07,047 - app.components.chat - INFO - Query: 'check the Market information and trends for COMI', Intent: market_query, Entities: {'symbols': ['COMI']}
2025-05-11 22:16:10,312 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:16:10,312 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:16:10,496 - app.utils.memory_management - INFO - Garbage collection: collected 6569 objects
2025-05-11 22:16:10,496 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:16:10,496 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:16:10,778 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:16:10,798 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:16:10,798 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:16:10,931 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:16:10,933 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:16:10,933 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:18:05,076 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:18:05,076 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:18:05,102 - app.components.chat - INFO - Query: 'Forecast future stock price for COMI', Intent: price_query, Entities: {'symbols': ['COMI']}
2025-05-11 22:18:05,102 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:18:06,292 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:18:06,292 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-11 22:18:06,300 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 78.75
2025-05-11 22:18:06,300 - scrapers.price_scraper - INFO - Generated sample price for COMI: 80.1
2025-05-11 22:18:06,301 - scrapers.price_scraper - INFO - Getting price for COMI from tradingview
2025-05-11 22:18:06,301 - scrapers.price_scraper - INFO - Getting delayed price for COMI (not logged in or real-time failed)
2025-05-11 22:18:06,301 - scrapers.price_scraper - INFO - Navigating to https://www.tradingview.com/symbols/EGX-COMI/
2025-05-11 22:18:10,469 - scrapers.price_scraper - INFO - Saved screenshot of COMI page to COMI_page.png
2025-05-11 22:18:12,599 - scrapers.price_scraper - INFO - Saved screenshot to COMI_price_extraction.png
2025-05-11 22:18:12,608 - scrapers.price_scraper - INFO - Successfully extracted price from header: 78.6
2025-05-11 22:18:12,609 - scrapers.price_scraper - INFO - Successfully extracted price from header for COMI: 78.6
2025-05-11 22:18:12,609 - scrapers.price_scraper - INFO - Successfully retrieved delayed price for COMI
2025-05-11 22:18:12,610 - scrapers.price_scraper - INFO - Successfully got price for COMI from tradingview
2025-05-11 22:18:12,610 - app.utils.error_handling - INFO - fetch_price executed in 6.31 seconds
2025-05-11 22:18:12,611 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:18:14,726 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:18:14,727 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:18:14,878 - app.utils.memory_management - INFO - Garbage collection: collected 303 objects
2025-05-11 22:18:14,878 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:18:14,879 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:18:15,175 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:18:15,198 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:18:15,198 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:18:15,333 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:18:15,333 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:18:15,335 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:25:39,199 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:25:40,270 - app - INFO - Memory management utilities loaded
2025-05-11 22:25:40,271 - app - INFO - Error handling utilities loaded
2025-05-11 22:25:40,272 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:25:40,272 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:25:40,273 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:25:40,273 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:25:40,273 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:25:40,274 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 22:25:40,274 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 22:25:40,274 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:25:40,274 - app - INFO - Applied NumPy fix
2025-05-11 22:25:40,277 - app.config - INFO - Configuration initialized
2025-05-11 22:25:40,283 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 22:25:41,756 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 22:25:41,756 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 22:25:41,824 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:25:41,826 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 22:25:41,826 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 22:25:41,826 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:25:41,826 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:25:42,604 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:25:42,627 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:25:42,628 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:25:43,173 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:25:43,178 - app.utils.memory_management - INFO - Memory before cleanup: 248.70 MB
2025-05-11 22:25:43,252 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 22:25:43,252 - app.utils.memory_management - INFO - Memory after cleanup: 248.70 MB (freed -0.00 MB)
2025-05-11 22:28:29,351 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:28:30,607 - app - INFO - Memory management utilities loaded
2025-05-11 22:28:30,608 - app - INFO - Error handling utilities loaded
2025-05-11 22:28:30,609 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:28:30,609 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:28:30,609 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:28:30,609 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:28:30,610 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:28:30,610 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 22:28:30,610 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 22:28:30,610 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:28:30,610 - app - INFO - Applied NumPy fix
2025-05-11 22:28:30,612 - app.config - INFO - Configuration initialized
2025-05-11 22:28:30,619 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 22:28:31,888 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 22:28:31,889 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 22:28:31,955 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:28:31,956 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 22:28:31,956 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 22:28:31,956 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:28:31,956 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:28:32,651 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:28:32,668 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:28:32,669 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:28:33,149 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:28:33,154 - app.utils.memory_management - INFO - Memory before cleanup: 246.82 MB
2025-05-11 22:28:33,230 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 22:28:33,230 - app.utils.memory_management - INFO - Memory after cleanup: 247.17 MB (freed -0.35 MB)
2025-05-11 22:29:43,959 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:29:43,975 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 22:29:43,975 - app - INFO - Memory management utilities loaded
2025-05-11 22:29:43,975 - app - INFO - Error handling utilities loaded
2025-05-11 22:29:43,975 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-11 22:29:43,975 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-11 22:29:43,975 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-11 22:29:43,987 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-11 22:29:43,988 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-11 22:29:43,988 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-11 22:29:43,988 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-11 22:29:43,990 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:29:43,990 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:29:43,991 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 22:29:43,991 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 22:29:43,991 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:29:43,991 - app - INFO - Applied NumPy fix
2025-05-11 22:29:43,994 - app.config - INFO - Configuration initialized
2025-05-11 22:29:52,464 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 22:29:52,489 - models.train - INFO - TensorFlow test successful
2025-05-11 22:29:55,111 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 22:29:55,111 - models.train - INFO - Transformer model is available
2025-05-11 22:29:55,111 - models.train - INFO - Using TensorFlow-based models
2025-05-11 22:29:55,113 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 22:29:55,113 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 22:29:55,113 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:29:56,506 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:29:56,525 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:29:56,526 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:29:57,417 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:29:57,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:29:57,420 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:29:57,532 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 22:29:57,533 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:29:57,534 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:30:01,063 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:30:01,086 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:30:01,086 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:30:01,206 - app.utils.memory_management - INFO - Garbage collection: collected 6 objects
2025-05-11 22:30:01,209 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:30:01,210 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:30:46,962 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:30:46,989 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 22:30:46,989 - app.components.chat - INFO - Query: 'Forecast future stock price of COMI', Intent: price_query, Entities: {'symbols': ['COMI']}
2025-05-11 22:30:46,989 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:30:48,147 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:30:48,148 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-11 22:30:48,176 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 78.97
2025-05-11 22:30:50,494 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:30:52,597 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:30:52,599 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:30:52,724 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-05-11 22:30:52,724 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:30:52,724 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:30:52,978 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:30:53,005 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:30:53,006 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:30:53,117 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:30:53,117 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:30:53,118 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:31:51,712 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:31:51,740 - app.components.chat - INFO - Query: 'Find the latest news about COMI', Intent: news_query, Entities: {'symbols': ['COMI']}
2025-05-11 22:31:53,846 - app.utils.web_search - WARNING - Error searching https://www.reuters.com/search/news?blob=COMI+stock+news+EGX+Egypt: 404 Client Error: Not Found for url: https://www.reuters.com/search/news/?blob=COMI+stock+news+EGX+Egypt
2025-05-11 22:31:56,094 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:31:56,094 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:31:56,211 - app.utils.memory_management - INFO - Garbage collection: collected 17150 objects
2025-05-11 22:31:56,211 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:31:56,211 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:31:56,464 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:31:56,475 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:31:56,476 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:31:56,586 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:31:56,586 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:31:56,587 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:32:53,227 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:32:53,248 - app.components.chat - INFO - Query: 'Predict COMI for next week', Intent: prediction_query, Entities: {'symbols': ['COMI'], 'time_horizon': 'week'}
2025-05-11 22:32:53,263 - app.utils.state_manager - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-11 22:32:53,264 - app.utils.state_manager - INFO - Date range: 2023-01-02 to 2025-05-11
2025-05-11 22:32:53,265 - app.utils.state_manager - INFO - Data shape: (567, 36)
2025-05-11 22:32:53,265 - app.utils.state_manager - INFO - File COMI contains 2025 data
2025-05-11 22:32:53,266 - app.models.predict - INFO - Using adaptive model selection for COMI
2025-05-11 22:32:53,266 - app.models.adaptive - INFO - No performance data for COMI with 5min horizon, using default model: ensemble
2025-05-11 22:32:53,267 - app.models.predict - INFO - Selected best model for COMI with 5min horizon: ensemble
2025-05-11 22:32:53,337 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-05-11 22:32:53,460 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:32:53,460 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.94 KB)
2025-05-11 22:32:53,577 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:32:53,577 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-11 22:32:53,577 - models.predict - INFO - Using scikit-learn ensemble model for 5 minutes horizon
2025-05-11 22:32:54,379 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 22:32:54,379 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 22:32:54,445 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-05-11 22:32:54,445 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_5min.joblib
2025-05-11 22:32:54,445 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_5min.joblib
2025-05-11 22:32:54,498 - models.predict - ERROR - Error making predictions: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-11 22:32:54,498 - app.utils.performance - WARNING - enhanced_prediction failed after 1.2315s: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-11 22:32:54,499 - app.components.chat - ERROR - Error generating prediction for COMI: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
2025-05-11 22:32:54,499 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:32:54,499 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:32:54,611 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 22:32:54,611 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:32:54,611 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:32:54,876 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:32:54,889 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:32:54,889 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:32:55,002 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:32:55,002 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:32:55,003 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:33:37,708 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:33:37,733 - app.components.chat - INFO - Query: 'How is the Egyptian market doing today?', Intent: explanation_query, Entities: {}
2025-05-11 22:33:37,734 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:33:37,734 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:33:37,878 - app.utils.memory_management - INFO - Garbage collection: collected 277 objects
2025-05-11 22:33:37,879 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:33:37,879 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:33:38,127 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:33:38,160 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:33:38,160 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:33:38,276 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:33:38,286 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:33:38,286 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:34:07,709 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:34:07,727 - app.components.chat - INFO - Query: 'Any news about COMI?', Intent: news_query, Entities: {'symbols': ['COMI']}
2025-05-11 22:34:09,994 - app.utils.web_search - WARNING - Error searching https://www.reuters.com/search/news?blob=COMI+stock+news+EGX+Egypt: 404 Client Error: Not Found for url: https://www.reuters.com/search/news/?blob=COMI+stock+news+EGX+Egypt
2025-05-11 22:34:11,964 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:11,964 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:34:12,094 - app.utils.memory_management - INFO - Garbage collection: collected 17156 objects
2025-05-11 22:34:12,095 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:12,095 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:34:12,341 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:34:12,375 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:12,376 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:34:12,492 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:34:12,492 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:12,494 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:34:49,309 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:34:49,333 - app.components.chat - INFO - Query: 'Compare COMI and SWDY', Intent: comparison_query, Entities: {'symbols': ['COMI', 'SWDY']}
2025-05-11 22:34:49,334 - app.utils.state_manager - INFO - Using cached data for COMI from session state
2025-05-11 22:34:49,339 - app.utils.state_manager - INFO - Loaded stock data for SWDY from data/stocks\SWDY.csv in 0.01 seconds
2025-05-11 22:34:49,341 - app.utils.state_manager - INFO - Date range: 2023-12-31 to 2025-04-08
2025-05-11 22:34:49,341 - app.utils.state_manager - INFO - Data shape: (307, 6)
2025-05-11 22:34:49,342 - app.utils.state_manager - INFO - File SWDY contains 2025 data
2025-05-11 22:34:49,342 - app.models.predict - INFO - Using adaptive model selection for COMI
2025-05-11 22:34:49,343 - app.models.adaptive - INFO - No performance data for COMI with 1min horizon, using default model: ensemble
2025-05-11 22:34:49,343 - app.models.predict - INFO - Selected best model for COMI with 1min horizon: ensemble
2025-05-11 22:34:49,414 - models.predict - INFO - Making predictions for 1 minutes horizon
2025-05-11 22:34:49,541 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:49,541 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1_scaler.pkl (0.94 KB)
2025-05-11 22:34:49,676 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:49,676 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-05-11 22:34:49,676 - models.predict - INFO - Using scikit-learn ensemble model for 1 minutes horizon
2025-05-11 22:34:49,676 - models.predict - INFO - Loading ensemble model for COMI with horizon 1
2025-05-11 22:34:49,676 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_ensemble_1min.joblib
2025-05-11 22:34:49,676 - models.sklearn_model - INFO - Loading model from saved_models\COMI_ensemble_1min.joblib
2025-05-11 22:34:49,709 - models.predict - INFO - Successfully loaded model for COMI with horizon 1
2025-05-11 22:34:49,726 - models.sklearn_model - INFO - Using hybrid model predict method for ensemble
2025-05-11 22:34:49,726 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 22:34:49,743 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-05-11 22:34:49,743 - models.predict - INFO - Prediction for 1 minutes horizon: 79.32600000000002
2025-05-11 22:34:49,743 - app.models.predict - INFO - Prediction completed in 0.40 seconds
2025-05-11 22:34:49,743 - app.models.predict - INFO - Using adaptive model selection for SWDY
2025-05-11 22:34:49,743 - app.models.adaptive - INFO - No performance data for SWDY with 1min horizon, using default model: ensemble
2025-05-11 22:34:49,743 - app.models.predict - INFO - Selected best model for SWDY with 1min horizon: ensemble
2025-05-11 22:34:49,776 - models.predict - INFO - Making predictions for 1 minutes horizon
2025-05-11 22:34:49,891 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:49,891 - app.utils.data_processing - WARNING - No exact scaler match found for SWDY with horizon 1. Trying to find any scaler...
2025-05-11 22:34:49,891 - app.utils.data_processing - WARNING - No scaler found for SWDY. Creating a new MinMaxScaler as fallback.
2025-05-11 22:34:50,025 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:50,025 - app.utils.data_processing - INFO - Found data file in subdirectory: data\stocks\SWDY.csv
2025-05-11 22:34:50,142 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:50,158 - app.utils.data_processing - INFO - Optimizing memory usage for DataFrame with 307 rows
2025-05-11 22:34:50,158 - app.utils.memory_management - INFO - DataFrame memory usage before optimization: 0.01 MB
2025-05-11 22:34:50,158 - app.utils.memory_management - INFO - DataFrame memory usage after optimization: 0.01 MB
2025-05-11 22:34:50,158 - app.utils.memory_management - INFO - Memory usage reduced by 0.00%
2025-05-11 22:34:50,158 - app.utils.data_processing - INFO - DataFrame memory usage: 0.01 MB
2025-05-11 22:34:50,291 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:50,291 - app.utils.memory_management - INFO - Memory usage for load_csv_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-05-11 22:34:50,408 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:50,408 - app.utils.memory_management - INFO - Memory usage for get_original_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.27s
2025-05-11 22:34:50,424 - app.utils.data_processing - INFO - Fitted new scaler with 307 samples from SWDY data
2025-05-11 22:34:50,542 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:50,556 - app.utils.data_processing - INFO - Saved scaler to saved_models\SWDY_1_scaler.pkl (0.53 KB)
2025-05-11 22:34:50,558 - app.utils.data_processing - INFO - Saved scaler to saved_models\SWDY_ensemble_scaler_1min.joblib (0.53 KB)
2025-05-11 22:34:50,558 - app.utils.data_processing - INFO - Saved scaler to saved_models\SWDY_ensemble_scaler1min.joblib (0.53 KB)
2025-05-11 22:34:50,808 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:50,808 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.13s
2025-05-11 22:34:51,043 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:51,043 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 1.03s
2025-05-11 22:34:51,043 - models.predict - INFO - Using scikit-learn ensemble model for 1 minutes horizon
2025-05-11 22:34:51,043 - models.predict - INFO - Loading ensemble model for SWDY with horizon 1
2025-05-11 22:34:51,043 - models.sklearn_model - INFO - Attempting to load model from saved_models\SWDY_ensemble_1min.joblib
2025-05-11 22:34:51,043 - models.sklearn_model - WARNING - Model not found at saved_models\SWDY_ensemble_1min.joblib, searching for alternatives...
2025-05-11 22:34:51,043 - models.sklearn_model - INFO - Found 2 potential model files: ['SWDY_ensemble_scaler1min.joblib', 'SWDY_ensemble_scaler_1min.joblib']
2025-05-11 22:34:51,043 - models.sklearn_model - INFO - Found matching model file: saved_models\SWDY_ensemble_scaler1min.joblib
2025-05-11 22:34:51,043 - models.sklearn_model - INFO - Loading model from saved_models\SWDY_ensemble_scaler1min.joblib
2025-05-11 22:34:51,043 - models.predict - INFO - Successfully loaded model for SWDY with horizon 1
2025-05-11 22:34:51,059 - models.sklearn_model - INFO - Using hybrid model predict method for ensemble
2025-05-11 22:34:51,059 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 22:34:51,059 - models.sklearn_model - ERROR - Error in hybrid model prediction: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 22:34:51,059 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-05-11 22:34:51,059 - models.predict - ERROR - Error in prediction for horizon 1: 'MinMaxScaler' object has no attribute 'predict'
2025-05-11 22:34:51,059 - models.predict - ERROR - Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 413, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\AI Stocks Bot\models\predict.py", line 411, in predict_future_prices
    prediction = model.predict(X_pred)
  File "D:\AI Stocks Bot\models\sklearn_model.py", line 418, in predict
    return self.model.predict(X_2d)
AttributeError: 'MinMaxScaler' object has no attribute 'predict'

2025-05-11 22:34:51,059 - models.predict - INFO - Using fallback price due to error: 80.98
2025-05-11 22:34:51,059 - models.predict - INFO - Prediction for 1 minutes horizon: 80.98
2025-05-11 22:34:51,074 - app.models.predict - INFO - Prediction completed in 1.33 seconds
2025-05-11 22:34:51,074 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:51,074 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:34:51,191 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 22:34:51,191 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:51,191 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:34:51,456 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:34:51,476 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:51,477 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:34:51,594 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-11 22:34:51,594 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:34:51,594 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:39:57,930 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:39:58,917 - app - INFO - Memory management utilities loaded
2025-05-11 22:39:58,918 - app - INFO - Error handling utilities loaded
2025-05-11 22:39:58,920 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:39:58,920 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:39:58,920 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:39:58,920 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:39:58,921 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:39:58,921 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 22:39:58,922 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 22:39:58,922 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:39:58,922 - app - INFO - Applied NumPy fix
2025-05-11 22:39:58,924 - app.config - INFO - Configuration initialized
2025-05-11 22:39:58,929 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 22:40:00,074 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 22:40:00,074 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 22:40:00,139 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:40:00,140 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 22:40:00,140 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 22:40:00,140 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:40:00,141 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:40:00,851 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:40:00,866 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:40:00,867 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:40:01,350 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:40:01,356 - app.utils.memory_management - INFO - Memory before cleanup: 246.75 MB
2025-05-11 22:40:01,450 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 22:40:01,450 - app.utils.memory_management - INFO - Memory after cleanup: 247.11 MB (freed -0.36 MB)
2025-05-11 22:40:05,118 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:40:05,141 - app.utils.session_state - ERROR - Error tracked: app_crash - No secrets found. Valid paths for a secrets.toml file or secret directories are: C:\Users\<USER>\.streamlit\secrets.toml, D:\AI Stocks Bot\.streamlit\secrets.toml
2025-05-11 22:40:05,141 - app - ERROR - Application crashed: No secrets found. Valid paths for a secrets.toml file or secret directories are: C:\Users\<USER>\.streamlit\secrets.toml, D:\AI Stocks Bot\.streamlit\secrets.toml
2025-05-11 22:40:05,142 - app.utils.memory_management - INFO - Memory before cleanup: 247.38 MB
2025-05-11 22:40:05,217 - app.utils.memory_management - INFO - Garbage collection: collected 100 objects
2025-05-11 22:40:05,218 - app.utils.memory_management - INFO - Memory after cleanup: 247.38 MB (freed 0.00 MB)
2025-05-11 22:40:05,218 - app.utils.memory_management - INFO - Memory before cleanup: 247.38 MB
2025-05-11 22:40:05,294 - app.utils.memory_management - INFO - Garbage collection: collected 19 objects
2025-05-11 22:40:05,295 - app.utils.memory_management - INFO - Memory after cleanup: 247.38 MB (freed 0.00 MB)
2025-05-11 22:41:29,236 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:41:30,226 - app - INFO - Memory management utilities loaded
2025-05-11 22:41:30,228 - app - INFO - Error handling utilities loaded
2025-05-11 22:41:30,229 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:41:30,229 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:41:30,229 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:41:30,229 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:41:30,229 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:41:30,230 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 22:41:30,230 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 22:41:30,230 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:41:30,230 - app - INFO - Applied NumPy fix
2025-05-11 22:41:30,232 - app.config - INFO - Configuration initialized
2025-05-11 22:41:30,238 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 22:41:31,402 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 22:41:31,402 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 22:41:31,465 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:41:31,466 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 22:41:31,466 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 22:41:31,466 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:41:31,467 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:41:32,174 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:41:32,190 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:41:32,191 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:41:32,687 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:41:32,698 - app.utils.memory_management - INFO - Memory before cleanup: 247.72 MB
2025-05-11 22:41:32,781 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 22:41:32,796 - app.utils.memory_management - INFO - Memory after cleanup: 247.73 MB (freed -0.00 MB)
2025-05-11 22:42:09,798 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:42:09,804 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 22:42:09,804 - app - INFO - Memory management utilities loaded
2025-05-11 22:42:09,806 - app - INFO - Error handling utilities loaded
2025-05-11 22:42:09,807 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:42:09,807 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:42:09,807 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:42:09,807 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:42:09,808 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:42:09,808 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 22:42:09,808 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:42:09,809 - app - INFO - Applied NumPy fix
2025-05-11 22:42:09,810 - app.config - INFO - Configuration initialized
2025-05-11 22:42:09,813 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 22:42:09,814 - models.train - INFO - TensorFlow test successful
2025-05-11 22:42:09,818 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 22:42:09,819 - models.train - INFO - Transformer model is available
2025-05-11 22:42:09,819 - models.train - INFO - Using TensorFlow-based models
2025-05-11 22:42:09,820 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 22:42:09,821 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 22:42:09,822 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:42:09,852 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:42:09,865 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:42:09,867 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:42:09,873 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:42:09,877 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:42:09,877 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:42:10,011 - app.utils.memory_management - INFO - Garbage collection: collected 277 objects
2025-05-11 22:42:10,011 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:42:10,011 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:42:13,121 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:42:13,140 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:42:14,285 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:42:14,288 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:42:16,417 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:42:16,418 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:42:16,538 - app.utils.memory_management - INFO - Garbage collection: collected 266 objects
2025-05-11 22:42:16,539 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:42:16,539 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:43:41,165 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:43:41,182 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:43:42,255 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:43:42,996 - app.components.ai_chat - ERROR - Error calling LLM API: 401 Client Error: Unauthorized for url: https://api.openai.com/v1/chat/completions
2025-05-11 22:43:42,998 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:43:42,999 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:43:43,118 - app.utils.memory_management - INFO - Garbage collection: collected 226 objects
2025-05-11 22:43:43,118 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:43:43,119 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:43:43,119 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:43:45,464 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:43:45,481 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:43:46,652 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:43:46,656 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:43:50,447 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:43:50,448 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:43:50,577 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 22:43:50,577 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:43:50,577 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:47:25,801 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:47:25,801 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:47:25,801 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:47:25,801 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:47:25,816 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:47:25,816 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:47:25,942 - app.utils.memory_management - INFO - Garbage collection: collected 269 objects
2025-05-11 22:47:25,942 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:47:25,942 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:47:30,277 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:47:30,281 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:47:31,434 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:47:31,442 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:47:33,643 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:47:33,644 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:47:33,762 - app.utils.memory_management - INFO - Garbage collection: collected 266 objects
2025-05-11 22:47:33,763 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:47:33,764 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:48:27,902 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:48:27,920 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:48:29,120 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:48:30,063 - app.components.ai_chat - ERROR - Error calling LLM API: 429 Client Error: Too Many Requests for url: https://api.openai.com/v1/chat/completions
2025-05-11 22:48:30,063 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:48:30,063 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:48:30,191 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-11 22:48:30,192 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:48:30,192 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:48:30,192 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:48:32,542 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:48:32,559 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:48:33,697 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:48:33,700 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:48:35,843 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:48:35,843 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:48:35,974 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 22:48:35,975 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:48:35,976 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:54:47,199 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:54:48,247 - app - INFO - Memory management utilities loaded
2025-05-11 22:54:48,249 - app - INFO - Error handling utilities loaded
2025-05-11 22:54:48,249 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:54:48,249 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:54:48,250 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:54:48,250 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:54:48,250 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:54:48,250 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 22:54:48,251 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 22:54:48,251 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:54:48,251 - app - INFO - Applied NumPy fix
2025-05-11 22:54:48,254 - app.config - INFO - Configuration initialized
2025-05-11 22:54:48,259 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 22:54:49,465 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 22:54:49,466 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 22:54:49,530 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:54:49,531 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 22:54:49,531 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 22:54:49,532 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:54:49,532 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:54:50,350 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:54:50,364 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:54:50,365 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:54:50,901 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:54:50,908 - app.utils.memory_management - INFO - Memory before cleanup: 247.09 MB
2025-05-11 22:54:50,984 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 22:54:50,984 - app.utils.memory_management - INFO - Memory after cleanup: 247.10 MB (freed -0.00 MB)
2025-05-11 22:56:09,525 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:56:10,536 - app - INFO - Memory management utilities loaded
2025-05-11 22:56:10,537 - app - INFO - Error handling utilities loaded
2025-05-11 22:56:10,538 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:56:10,538 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:56:10,538 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:56:10,538 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:56:10,539 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:56:10,539 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 22:56:10,539 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 22:56:10,539 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:56:10,540 - app - INFO - Applied NumPy fix
2025-05-11 22:56:10,541 - app.config - INFO - Configuration initialized
2025-05-11 22:56:10,546 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 22:56:11,824 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 22:56:11,824 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 22:56:11,887 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:56:11,888 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 22:56:11,888 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 22:56:11,888 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 22:56:11,889 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:56:12,585 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:56:12,601 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:56:12,602 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:56:13,110 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:56:13,116 - app.utils.memory_management - INFO - Memory before cleanup: 247.10 MB
2025-05-11 22:56:13,195 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 22:56:13,196 - app.utils.memory_management - INFO - Memory after cleanup: 247.11 MB (freed -0.00 MB)
2025-05-11 22:56:19,085 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:56:19,095 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:56:20,263 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:56:20,266 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:56:21,794 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:56:21,796 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:56:21,797 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:56:21,803 - app.utils.memory_management - INFO - Memory before cleanup: 248.76 MB
2025-05-11 22:56:21,878 - app.utils.memory_management - INFO - Garbage collection: collected 142 objects
2025-05-11 22:56:21,878 - app.utils.memory_management - INFO - Memory after cleanup: 248.76 MB (freed -0.00 MB)
2025-05-11 22:56:22,435 - app.utils.memory_management - INFO - Memory before cleanup: 248.73 MB
2025-05-11 22:56:22,535 - app.utils.memory_management - INFO - Garbage collection: collected 120 objects
2025-05-11 22:56:22,536 - app.utils.memory_management - INFO - Memory after cleanup: 248.73 MB (freed 0.00 MB)
2025-05-11 22:56:25,694 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 22:56:25,705 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:56:57,637 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 22:56:57,642 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 22:56:57,642 - app - INFO - Memory management utilities loaded
2025-05-11 22:56:57,643 - app - INFO - Error handling utilities loaded
2025-05-11 22:56:57,645 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 22:56:57,645 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 22:56:57,645 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 22:56:57,646 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 22:56:57,646 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 22:56:57,646 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 22:56:57,647 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 22:56:57,647 - app - INFO - Applied NumPy fix
2025-05-11 22:56:57,649 - app.config - INFO - Configuration initialized
2025-05-11 22:56:57,655 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 22:56:57,657 - models.train - INFO - TensorFlow test successful
2025-05-11 22:56:57,659 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 22:56:57,659 - models.train - INFO - Transformer model is available
2025-05-11 22:56:57,659 - models.train - INFO - Using TensorFlow-based models
2025-05-11 22:56:57,661 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 22:56:57,661 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 22:56:57,661 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:56:57,691 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 22:56:57,706 - app.utils.session_state - INFO - Initializing session state
2025-05-11 22:56:57,707 - app.utils.session_state - INFO - Session state initialized
2025-05-11 22:56:57,713 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 22:56:57,717 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:56:57,718 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:56:57,854 - app.utils.memory_management - INFO - Garbage collection: collected 268 objects
2025-05-11 22:56:57,855 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:56:57,855 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:57:01,817 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:57:01,839 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:57:02,964 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:57:02,968 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:57:05,156 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:57:05,157 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:57:05,300 - app.utils.memory_management - INFO - Garbage collection: collected 266 objects
2025-05-11 22:57:05,300 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:57:05,300 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:57:34,271 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:57:34,302 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:57:35,455 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:57:35,466 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 22:57:36,068 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 22:57:36,068 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:57:36,068 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:57:36,202 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-11 22:57:36,203 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:57:36,203 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 22:57:36,203 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:57:38,582 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 22:57:38,599 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 22:57:39,776 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 22:57:39,793 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 22:57:41,967 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:57:41,968 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 22:57:42,083 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 22:57:42,083 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 22:57:42,083 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:01:17,202 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:01:17,206 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:01:17,207 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:01:17,216 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:01:17,216 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:01:17,348 - app.utils.memory_management - INFO - Garbage collection: collected 268 objects
2025-05-11 23:01:17,348 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:01:17,348 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:01:19,990 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:01:19,996 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:01:21,116 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:01:21,119 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:01:23,279 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:01:23,279 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:01:23,399 - app.utils.memory_management - INFO - Garbage collection: collected 265 objects
2025-05-11 23:01:23,399 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:01:23,399 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:03:11,610 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:03:11,627 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:03:11,636 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:03:12,754 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:03:12,759 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:03:14,948 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:03:14,948 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:03:15,083 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-05-11 23:03:15,083 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:03:15,084 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:03:27,680 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:03:27,685 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:03:28,824 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:03:28,833 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:03:28,833 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:03:28,963 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-11 23:03:28,964 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:03:28,964 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:03:28,964 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:03:33,009 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:03:33,019 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:03:34,059 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:03:34,063 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:03:37,966 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:03:37,968 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:03:38,094 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 23:03:38,095 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:03:38,095 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:03:47,059 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:03:47,077 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:03:48,286 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:03:48,298 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:03:50,482 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:03:50,484 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:03:50,614 - app.utils.memory_management - INFO - Garbage collection: collected 300 objects
2025-05-11 23:03:50,614 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:03:50,615 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:05:30,979 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:05:30,984 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 23:05:30,984 - app - INFO - Memory management utilities loaded
2025-05-11 23:05:30,986 - app - INFO - Error handling utilities loaded
2025-05-11 23:05:30,987 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:05:30,987 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:05:30,987 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:05:30,987 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:05:30,988 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:05:30,988 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 23:05:30,988 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:05:30,988 - app - INFO - Applied NumPy fix
2025-05-11 23:05:30,991 - app.config - INFO - Configuration initialized
2025-05-11 23:05:30,996 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 23:05:30,997 - models.train - INFO - TensorFlow test successful
2025-05-11 23:05:30,999 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 23:05:30,999 - models.train - INFO - Transformer model is available
2025-05-11 23:05:30,999 - models.train - INFO - Using TensorFlow-based models
2025-05-11 23:05:31,001 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 23:05:31,001 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 23:05:31,001 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:05:31,027 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:05:31,045 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:05:31,046 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:05:31,054 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:05:31,055 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:05:31,181 - app.utils.memory_management - INFO - Garbage collection: collected 227 objects
2025-05-11 23:05:31,182 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:05:31,182 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:05:35,191 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:05:35,213 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:05:36,331 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:05:36,336 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:05:38,574 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:05:38,574 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:05:38,696 - app.utils.memory_management - INFO - Garbage collection: collected 265 objects
2025-05-11 23:05:38,696 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:05:38,696 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:05:57,515 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:05:58,557 - app - INFO - Memory management utilities loaded
2025-05-11 23:05:58,559 - app - INFO - Error handling utilities loaded
2025-05-11 23:05:58,560 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:05:58,560 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:05:58,560 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:05:58,560 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:05:58,561 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:05:58,561 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 23:05:58,561 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 23:05:58,561 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:05:58,562 - app - INFO - Applied NumPy fix
2025-05-11 23:05:58,563 - app.config - INFO - Configuration initialized
2025-05-11 23:05:58,569 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 23:05:59,867 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 23:05:59,867 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 23:05:59,936 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:05:59,937 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 23:05:59,937 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 23:05:59,937 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:05:59,938 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:06:00,645 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:06:00,660 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:06:00,661 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:06:01,144 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:06:01,150 - app.utils.memory_management - INFO - Memory before cleanup: 247.43 MB
2025-05-11 23:06:01,230 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 23:06:01,230 - app.utils.memory_management - INFO - Memory after cleanup: 247.43 MB (freed -0.00 MB)
2025-05-11 23:06:05,037 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:06:05,040 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:06:05,042 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:06:05,048 - app.utils.memory_management - INFO - Memory before cleanup: 247.93 MB
2025-05-11 23:06:05,130 - app.utils.memory_management - INFO - Garbage collection: collected 100 objects
2025-05-11 23:06:05,130 - app.utils.memory_management - INFO - Memory after cleanup: 247.93 MB (freed 0.00 MB)
2025-05-11 23:06:07,883 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:06:07,894 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:06:09,115 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:06:09,127 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:06:12,148 - app.utils.memory_management - INFO - Memory before cleanup: 248.96 MB
2025-05-11 23:06:12,215 - app.utils.memory_management - INFO - Garbage collection: collected 120 objects
2025-05-11 23:06:12,215 - app.utils.memory_management - INFO - Memory after cleanup: 248.96 MB (freed 0.00 MB)
2025-05-11 23:06:54,861 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:06:56,232 - app - INFO - Memory management utilities loaded
2025-05-11 23:06:56,234 - app - INFO - Error handling utilities loaded
2025-05-11 23:06:56,235 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:06:56,236 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:06:56,236 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:06:56,236 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:06:56,236 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:06:56,237 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 23:06:56,237 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 23:06:56,237 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:06:56,238 - app - INFO - Applied NumPy fix
2025-05-11 23:06:56,243 - app.config - INFO - Configuration initialized
2025-05-11 23:06:56,258 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 23:06:57,727 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 23:06:57,728 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 23:06:57,795 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:06:57,796 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 23:06:57,796 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 23:06:57,797 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:06:57,797 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:06:57,797 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:06:58,598 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:06:58,618 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:06:58,620 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:06:58,621 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:06:58,622 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:06:58,676 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:06:58,684 - app.utils.memory_management - INFO - Memory before cleanup: 247.74 MB
2025-05-11 23:06:58,793 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 23:06:58,794 - app.utils.memory_management - INFO - Memory after cleanup: 247.74 MB (freed -0.00 MB)
2025-05-11 23:06:59,659 - app.utils.memory_management - INFO - Memory before cleanup: 248.25 MB
2025-05-11 23:06:59,726 - app.utils.memory_management - INFO - Garbage collection: collected 100 objects
2025-05-11 23:06:59,726 - app.utils.memory_management - INFO - Memory after cleanup: 248.25 MB (freed 0.00 MB)
2025-05-11 23:07:07,662 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:07:07,669 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:07:07,670 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:07:08,855 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:07:08,865 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:07:11,910 - app.utils.memory_management - INFO - Memory before cleanup: 249.23 MB
2025-05-11 23:07:11,982 - app.utils.memory_management - INFO - Garbage collection: collected 120 objects
2025-05-11 23:07:11,983 - app.utils.memory_management - INFO - Memory after cleanup: 249.23 MB (freed 0.00 MB)
2025-05-11 23:07:33,006 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:07:33,011 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 23:07:33,012 - app - INFO - Memory management utilities loaded
2025-05-11 23:07:33,013 - app - INFO - Error handling utilities loaded
2025-05-11 23:07:33,014 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:07:33,014 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:07:33,014 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:07:33,015 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:07:33,015 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:07:33,015 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 23:07:33,015 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:07:33,015 - app - INFO - Applied NumPy fix
2025-05-11 23:07:33,018 - app.config - INFO - Configuration initialized
2025-05-11 23:07:33,025 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 23:07:33,026 - models.train - INFO - TensorFlow test successful
2025-05-11 23:07:33,028 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 23:07:33,028 - models.train - INFO - Transformer model is available
2025-05-11 23:07:33,028 - models.train - INFO - Using TensorFlow-based models
2025-05-11 23:07:33,030 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 23:07:33,030 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 23:07:33,030 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:07:33,055 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:07:33,068 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:07:33,069 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:07:33,080 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:07:33,080 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:07:33,215 - app.utils.memory_management - INFO - Garbage collection: collected 226 objects
2025-05-11 23:07:33,216 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:07:33,216 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:07:36,722 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:07:36,722 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:07:36,735 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:07:37,936 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:07:37,950 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:07:41,760 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:07:41,760 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:07:41,886 - app.utils.memory_management - INFO - Garbage collection: collected 265 objects
2025-05-11 23:07:41,886 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:07:41,886 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:08:05,388 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:08:05,388 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:08:05,388 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:08:06,617 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:08:06,631 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 23:08:06,633 - app.components.claude_chat - INFO - Calling Claude API with model: claude-3-sonnet-20240229
2025-05-11 23:08:07,223 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 23:08:07,223 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:08:07,223 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:08:07,378 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-05-11 23:08:07,378 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:08:07,379 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:08:07,379 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:08:09,738 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:08:09,738 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:08:09,738 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:08:10,913 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:08:10,918 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:08:13,054 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:08:13,055 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:08:13,183 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 23:08:13,183 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:08:13,183 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:09:55,569 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:09:55,585 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:09:55,592 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:09:55,592 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:09:56,679 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:09:56,686 - app.components.claude_chat - INFO - Calling Claude API with model: claude-3-sonnet-20240229
2025-05-11 23:09:57,252 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 23:09:57,255 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:09:57,255 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:09:57,401 - app.utils.memory_management - INFO - Garbage collection: collected 268 objects
2025-05-11 23:09:57,401 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:09:57,402 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:09:57,402 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:09:59,803 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:09:59,815 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:09:59,816 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:10:00,959 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:10:00,976 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:10:03,136 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:10:03,136 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:10:03,258 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 23:10:03,258 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:10:03,258 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:12:11,402 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:12:12,723 - app - INFO - Memory management utilities loaded
2025-05-11 23:12:12,725 - app - INFO - Error handling utilities loaded
2025-05-11 23:12:12,726 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:12:12,726 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:12:12,726 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:12:12,726 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:12:12,727 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:12:12,727 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 23:12:12,727 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 23:12:12,728 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:12:12,728 - app - INFO - Applied NumPy fix
2025-05-11 23:12:12,732 - app.config - INFO - Configuration initialized
2025-05-11 23:12:12,739 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 23:12:14,286 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 23:12:14,286 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 23:12:14,356 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:12:14,358 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 23:12:14,358 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 23:12:14,359 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:12:14,359 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:12:14,359 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:12:15,177 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:12:15,194 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:12:15,195 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:12:15,197 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:12:15,198 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:12:15,246 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:12:15,255 - app.utils.memory_management - INFO - Memory before cleanup: 248.09 MB
2025-05-11 23:12:15,345 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 23:12:15,346 - app.utils.memory_management - INFO - Memory after cleanup: 248.09 MB (freed -0.00 MB)
2025-05-11 23:12:16,134 - app.utils.memory_management - INFO - Memory before cleanup: 248.56 MB
2025-05-11 23:12:16,202 - app.utils.memory_management - INFO - Garbage collection: collected 100 objects
2025-05-11 23:12:16,203 - app.utils.memory_management - INFO - Memory after cleanup: 248.56 MB (freed 0.00 MB)
2025-05-11 23:12:19,016 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:12:19,032 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:12:19,034 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:12:20,245 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:12:20,247 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:12:23,283 - app.utils.memory_management - INFO - Memory before cleanup: 249.14 MB
2025-05-11 23:12:23,356 - app.utils.memory_management - INFO - Garbage collection: collected 120 objects
2025-05-11 23:12:23,356 - app.utils.memory_management - INFO - Memory after cleanup: 249.14 MB (freed 0.00 MB)
2025-05-11 23:13:36,293 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:13:37,304 - app - INFO - Memory management utilities loaded
2025-05-11 23:13:37,305 - app - INFO - Error handling utilities loaded
2025-05-11 23:13:37,306 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:13:37,306 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:13:37,306 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:13:37,306 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:13:37,307 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:13:37,307 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 23:13:37,307 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 23:13:37,307 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:13:37,307 - app - INFO - Applied NumPy fix
2025-05-11 23:13:37,310 - app.config - INFO - Configuration initialized
2025-05-11 23:13:37,319 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 23:13:38,596 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 23:13:38,596 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 23:13:38,673 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:13:38,675 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 23:13:38,676 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 23:13:38,676 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:13:38,676 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:13:39,881 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:13:39,903 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:13:39,904 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:13:40,272 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:13:40,277 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:13:40,282 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:13:40,295 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:13:40,304 - app.utils.memory_management - INFO - Memory before cleanup: 247.60 MB
2025-05-11 23:13:40,375 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 23:13:40,375 - app.utils.memory_management - INFO - Memory after cleanup: 247.60 MB (freed -0.00 MB)
2025-05-11 23:13:40,959 - app.utils.memory_management - INFO - Memory before cleanup: 248.34 MB
2025-05-11 23:13:41,030 - app.utils.memory_management - INFO - Garbage collection: collected 100 objects
2025-05-11 23:13:41,031 - app.utils.memory_management - INFO - Memory after cleanup: 248.34 MB (freed 0.00 MB)
2025-05-11 23:13:47,534 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:13:47,544 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:13:47,545 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:13:48,730 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:13:48,738 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:13:52,335 - app.utils.memory_management - INFO - Memory before cleanup: 248.82 MB
2025-05-11 23:13:52,404 - app.utils.memory_management - INFO - Garbage collection: collected 139 objects
2025-05-11 23:13:52,405 - app.utils.memory_management - INFO - Memory after cleanup: 248.82 MB (freed 0.00 MB)
2025-05-11 23:13:59,256 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:13:59,262 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:13:59,263 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:14:00,402 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:14:00,406 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 23:14:00,407 - app.components.claude_chat - INFO - Calling Claude API with model: claude-3-sonnet-20240229
2025-05-11 23:14:00,407 - app.components.claude_chat - INFO - Sending request to Claude API with headers: dict_keys(['Content-Type', 'Authorization', 'anthropic-version'])
2025-05-11 23:14:01,143 - app.components.claude_chat - INFO - Claude API response status: 401
2025-05-11 23:14:01,144 - app.components.claude_chat - ERROR - Claude API error: 401 - {"type":"error","error":{"type":"authentication_error","message":"Invalid bearer token"}}
2025-05-11 23:14:01,144 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 23:14:01,148 - app.utils.session_state - ERROR - Error tracked: app_crash - module 'streamlit' has no attribute 'experimental_rerun'
2025-05-11 23:14:01,148 - app - ERROR - Application crashed: module 'streamlit' has no attribute 'experimental_rerun'
2025-05-11 23:14:01,149 - app.utils.memory_management - INFO - Memory before cleanup: 249.88 MB
2025-05-11 23:14:01,225 - app.utils.memory_management - INFO - Garbage collection: collected 101 objects
2025-05-11 23:14:01,225 - app.utils.memory_management - INFO - Memory after cleanup: 249.88 MB (freed -0.00 MB)
2025-05-11 23:14:01,225 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:15:13,987 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:15:15,015 - app - INFO - Memory management utilities loaded
2025-05-11 23:15:15,017 - app - INFO - Error handling utilities loaded
2025-05-11 23:15:15,018 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:15:15,018 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:15:15,018 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:15:15,019 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:15:15,019 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:15:15,020 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-11 23:15:15,020 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-11 23:15:15,020 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:15:15,020 - app - INFO - Applied NumPy fix
2025-05-11 23:15:15,024 - app.config - INFO - Configuration initialized
2025-05-11 23:15:15,030 - models.train - WARNING - TensorFlow import error: No module named 'tensorflow'
2025-05-11 23:15:16,428 - models.hybrid_model - INFO - XGBoost is available
2025-05-11 23:15:16,431 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-05-11 23:15:16,515 - models.train - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:15:16,517 - models.predict - WARNING - TensorFlow not working correctly: No module named 'tensorflow'
2025-05-11 23:15:16,518 - models.predict - WARNING - TensorFlow error: TensorFlow not working correctly
2025-05-11 23:15:16,518 - models.predict - INFO - TensorFlow not available. Using scikit-learn based model instead
2025-05-11 23:15:16,518 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:15:17,200 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:15:17,308 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:15:17,324 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:15:17,326 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:15:17,327 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:15:17,328 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:15:17,377 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:15:17,386 - app.utils.memory_management - INFO - Memory before cleanup: 247.48 MB
2025-05-11 23:15:17,470 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-05-11 23:15:17,470 - app.utils.memory_management - INFO - Memory after cleanup: 247.48 MB (freed -0.00 MB)
2025-05-11 23:15:18,350 - app.utils.memory_management - INFO - Memory before cleanup: 248.10 MB
2025-05-11 23:15:18,431 - app.utils.memory_management - INFO - Garbage collection: collected 100 objects
2025-05-11 23:15:18,432 - app.utils.memory_management - INFO - Memory after cleanup: 248.10 MB (freed 0.00 MB)
2025-05-11 23:15:22,161 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:15:22,174 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:15:22,175 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:15:23,249 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:15:23,253 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:15:26,280 - app.utils.memory_management - INFO - Memory before cleanup: 248.47 MB
2025-05-11 23:15:26,352 - app.utils.memory_management - INFO - Garbage collection: collected 139 objects
2025-05-11 23:15:26,352 - app.utils.memory_management - INFO - Memory after cleanup: 248.47 MB (freed 0.00 MB)
2025-05-11 23:15:29,190 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:15:29,196 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:15:29,197 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:15:30,358 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:15:30,373 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 23:15:30,375 - app.components.claude_chat - INFO - Calling Claude API with model: claude-3-sonnet-20240229
2025-05-11 23:15:30,376 - app.components.claude_chat - INFO - Sending request to Claude API with headers: dict_keys(['Content-Type', 'Authorization', 'anthropic-version'])
2025-05-11 23:15:31,265 - app.components.claude_chat - INFO - Claude API response status: 401
2025-05-11 23:15:31,265 - app.components.claude_chat - ERROR - Claude API error: 401 - {"type":"error","error":{"type":"authentication_error","message":"Invalid bearer token"}}
2025-05-11 23:15:31,266 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 23:15:31,266 - app.components.claude_chat - WARNING - Authentication error with Claude API, using fallback
2025-05-11 23:15:33,609 - app.utils.web_search - WARNING - Error searching https://www.reuters.com/search/news?blob=COMI+stock+news+EGX+Egypt: 404 Client Error: Not Found for url: https://www.reuters.com/search/news/?blob=COMI+stock+news+EGX+Egypt
2025-05-11 23:15:35,598 - app.utils.memory_management - INFO - Memory before cleanup: 252.27 MB
2025-05-11 23:15:35,682 - app.utils.memory_management - INFO - Garbage collection: collected 14539 objects
2025-05-11 23:15:35,683 - app.utils.memory_management - INFO - Memory after cleanup: 252.27 MB (freed 0.00 MB)
2025-05-11 23:15:35,683 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:15:37,818 - app - INFO - Using scikit-learn based model (TensorFlow not available)
2025-05-11 23:15:37,824 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:15:37,825 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:15:38,907 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:15:38,910 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:18:03,011 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:18:03,018 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 23:18:03,018 - app - INFO - Memory management utilities loaded
2025-05-11 23:18:03,019 - app - INFO - Error handling utilities loaded
2025-05-11 23:18:03,020 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:18:03,021 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:18:03,021 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:18:03,021 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:18:03,022 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:18:03,023 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 23:18:03,024 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:18:03,024 - app - INFO - Applied NumPy fix
2025-05-11 23:18:03,028 - app.config - INFO - Configuration initialized
2025-05-11 23:18:03,033 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 23:18:03,033 - models.train - INFO - TensorFlow test successful
2025-05-11 23:18:03,035 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 23:18:03,035 - models.train - INFO - Transformer model is available
2025-05-11 23:18:03,036 - models.train - INFO - Using TensorFlow-based models
2025-05-11 23:18:03,037 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 23:18:03,037 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 23:18:03,037 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:18:03,068 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:18:03,082 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:18:03,084 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:18:03,089 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:18:03,095 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:18:03,095 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:18:03,238 - app.utils.memory_management - INFO - Garbage collection: collected 270 objects
2025-05-11 23:18:03,240 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:18:03,245 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:18:05,877 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:18:05,899 - app.components.claude_chat - WARNING - API key doesn't appear to be a valid Claude API key: sk-ant-api...
2025-05-11 23:18:05,903 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:18:07,052 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:18:07,058 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:18:10,895 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:18:10,896 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:18:11,020 - app.utils.memory_management - INFO - Garbage collection: collected 266 objects
2025-05-11 23:18:11,020 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:18:11,020 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:33:34,010 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:33:34,027 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:33:34,028 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:33:34,035 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:33:34,038 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:33:34,038 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:33:34,173 - app.utils.memory_management - INFO - Garbage collection: collected 226 objects
2025-05-11 23:33:34,173 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:33:34,174 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:33:37,510 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:33:37,510 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:33:37,510 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:33:38,712 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:33:38,718 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:33:40,876 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:33:40,877 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:33:40,998 - app.utils.memory_management - INFO - Garbage collection: collected 266 objects
2025-05-11 23:33:40,998 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:33:40,999 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:34:04,160 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:34:04,177 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:34:04,185 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:34:05,340 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:34:05,346 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 23:34:05,347 - app.components.claude_chat - INFO - Calling Claude API with model: claude-3-sonnet-20240229
2025-05-11 23:34:05,347 - app.components.claude_chat - INFO - Sending request to Claude API with headers: dict_keys(['Content-Type', 'Authorization', 'anthropic-version'])
2025-05-11 23:34:05,943 - app.components.claude_chat - INFO - Claude API response status: 401
2025-05-11 23:34:05,943 - app.components.claude_chat - ERROR - Claude API error: 401 - {"type":"error","error":{"type":"authentication_error","message":"Invalid bearer token"}}
2025-05-11 23:34:05,943 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 23:34:05,943 - app.components.claude_chat - WARNING - Authentication error with Claude API, using fallback
2025-05-11 23:34:08,460 - app.utils.web_search - WARNING - Error searching https://www.reuters.com/search/news?blob=COMI+stock+news+EGX+Egypt: 404 Client Error: Not Found for url: https://www.reuters.com/search/news/?blob=COMI+stock+news+EGX+Egypt
2025-05-11 23:34:10,512 - app.utils.session_state - ERROR - Error tracked: app_crash - module 'streamlit' has no attribute 'rerun'
2025-05-11 23:34:10,513 - app - ERROR - Application crashed: module 'streamlit' has no attribute 'rerun'
2025-05-11 23:34:10,514 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:34:10,514 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:34:10,642 - app.utils.memory_management - INFO - Garbage collection: collected 17118 objects
2025-05-11 23:34:10,643 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:34:10,643 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:34:10,644 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:34:12,759 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:34:12,759 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:34:12,881 - app.utils.memory_management - INFO - Garbage collection: collected 32 objects
2025-05-11 23:34:12,881 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:34:12,882 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:36:37,620 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-11 23:36:37,627 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-05-11 23:36:37,627 - app - INFO - Memory management utilities loaded
2025-05-11 23:36:37,629 - app - INFO - Error handling utilities loaded
2025-05-11 23:36:37,630 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-11 23:36:37,630 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-11 23:36:37,630 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-11 23:36:37,630 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-11 23:36:37,631 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-11 23:36:37,631 - app.utils.numpy_fix - INFO - numpy.random._bit_generator module exists, no fix needed
2025-05-11 23:36:37,631 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-11 23:36:37,631 - app - INFO - Applied NumPy fix
2025-05-11 23:36:37,634 - app.config - INFO - Configuration initialized
2025-05-11 23:36:37,640 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-11 23:36:37,641 - models.train - INFO - TensorFlow test successful
2025-05-11 23:36:37,643 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-11 23:36:37,643 - models.train - INFO - Transformer model is available
2025-05-11 23:36:37,643 - models.train - INFO - Using TensorFlow-based models
2025-05-11 23:36:37,645 - models.predict - INFO - Transformer model is available for predictions
2025-05-11 23:36:37,646 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-11 23:36:37,646 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:36:37,671 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-11 23:36:37,685 - app.utils.session_state - INFO - Initializing session state
2025-05-11 23:36:37,686 - app.utils.session_state - INFO - Session state initialized
2025-05-11 23:36:37,696 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:36:37,696 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:36:37,832 - app.utils.memory_management - INFO - Garbage collection: collected 218 objects
2025-05-11 23:36:37,833 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:36:37,833 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:36:41,073 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:36:41,073 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:36:41,073 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:36:42,270 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:36:42,278 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:36:45,294 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:36:45,294 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:36:45,420 - app.utils.memory_management - INFO - Garbage collection: collected 265 objects
2025-05-11 23:36:45,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:36:45,421 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:37:09,456 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:37:09,473 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:37:09,473 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:37:10,649 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:37:10,654 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-05-11 23:37:10,655 - app.components.claude_chat - INFO - Calling Claude API with model: claude-3-sonnet-20240229
2025-05-11 23:37:10,656 - app.components.claude_chat - INFO - Sending request to Claude API with headers: dict_keys(['Content-Type', 'Authorization', 'anthropic-version'])
2025-05-11 23:37:11,406 - app.components.claude_chat - INFO - Claude API response status: 401
2025-05-11 23:37:11,406 - app.components.claude_chat - ERROR - Claude API error: 401 - {"type":"error","error":{"type":"authentication_error","message":"Invalid bearer token"}}
2025-05-11 23:37:11,406 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 23:37:11,406 - app.components.claude_chat - WARNING - Authentication error with Claude API, using fallback
2025-05-11 23:37:13,656 - app.utils.web_search - WARNING - Error searching https://www.reuters.com/search/news?blob=COMI+stock+news+EGX+Egypt: 404 Client Error: Not Found for url: https://www.reuters.com/search/news/?blob=COMI+stock+news+EGX+Egypt
2025-05-11 23:37:15,596 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:37:15,596 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:37:15,729 - app.utils.memory_management - INFO - Garbage collection: collected 20645 objects
2025-05-11 23:37:15,729 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:37:15,729 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:37:15,730 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:37:18,122 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:37:18,151 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:37:18,153 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:37:19,281 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:37:19,294 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:37:21,473 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:37:21,473 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:37:21,599 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 23:37:21,601 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:37:21,601 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:39:15,437 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:39:15,462 - app - INFO - Found 8 stock files in data/stocks
2025-05-11 23:39:15,468 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:39:15,469 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:39:16,515 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:39:16,519 - app.components.claude_chat - INFO - Calling Claude API with model: claude-3-sonnet-20240229
2025-05-11 23:39:16,520 - app.components.claude_chat - INFO - Sending request to Claude API with headers: dict_keys(['Content-Type', 'Authorization', 'anthropic-version'])
2025-05-11 23:39:16,937 - app.components.claude_chat - INFO - Claude API response status: 401
2025-05-11 23:39:16,937 - app.components.claude_chat - ERROR - Claude API error: 401 - {"type":"error","error":{"type":"authentication_error","message":"Invalid bearer token"}}
2025-05-11 23:39:16,937 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 23:39:16,937 - app.components.claude_chat - WARNING - Authentication error with Claude API, using fallback
2025-05-11 23:39:16,937 - scrapers.price_scraper - WARNING - Generating sample price for COMI
2025-05-11 23:39:16,975 - scrapers.price_scraper - INFO - Generated sample price based on historical data: 78.28
2025-05-11 23:39:16,975 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:39:16,992 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:39:17,132 - app.utils.memory_management - INFO - Garbage collection: collected 268 objects
2025-05-11 23:39:17,133 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:39:17,133 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:39:17,133 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:39:19,502 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:39:19,503 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:39:19,503 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:39:20,659 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:39:20,664 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:39:24,510 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:39:24,511 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:39:24,644 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 23:39:24,644 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:39:24,645 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:40:42,203 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:40:42,203 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:40:42,203 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:40:43,416 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:40:43,432 - app.components.claude_chat - INFO - Calling Claude API with model: claude-3-sonnet-20240229
2025-05-11 23:40:43,434 - app.components.claude_chat - INFO - Sending request to Claude API with headers: dict_keys(['Content-Type', 'Authorization', 'anthropic-version'])
2025-05-11 23:40:44,353 - app.components.claude_chat - INFO - Claude API response status: 401
2025-05-11 23:40:44,353 - app.components.claude_chat - ERROR - Claude API error: 401 - {"type":"error","error":{"type":"authentication_error","message":"Invalid bearer token"}}
2025-05-11 23:40:44,353 - app.components.claude_chat - ERROR - Error calling Claude API: 401 Client Error: Unauthorized for url: https://api.anthropic.com/v1/messages
2025-05-11 23:40:44,353 - app.components.claude_chat - WARNING - Authentication error with Claude API, using fallback
2025-05-11 23:40:44,353 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:40:44,353 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:40:44,501 - app.utils.memory_management - INFO - Garbage collection: collected 270 objects
2025-05-11 23:40:44,501 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:40:44,501 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:40:44,501 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:40:46,868 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:40:46,868 - app.components.claude_chat - INFO - Valid Claude API key found
2025-05-11 23:40:46,868 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:40:48,047 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:40:48,069 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:40:50,252 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:40:50,252 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:40:50,375 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 23:40:50,376 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:40:50,376 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:41:23,601 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:41:23,613 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:41:24,709 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:41:24,715 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:41:28,374 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:41:28,376 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:41:28,498 - app.utils.memory_management - INFO - Garbage collection: collected 304 objects
2025-05-11 23:41:28,499 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:41:28,499 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:41:30,134 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:41:30,158 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:41:31,292 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:41:32,452 - app.components.ai_chat - ERROR - Error calling LLM API: 401 Client Error: Unauthorized for url: https://api.openai.com/v1/chat/completions
2025-05-11 23:41:32,452 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:41:32,452 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:41:32,579 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-05-11 23:41:32,580 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:41:32,580 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:41:32,580 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:41:34,936 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:41:34,947 - scrapers.price_scraper - INFO - Found ChromeDriver at D:\AI Stocks Bot\scrapers\chromedriver.exe
2025-05-11 23:41:36,197 - scrapers.price_scraper - INFO - Selenium driver initialized successfully
2025-05-11 23:41:36,219 - scrapers.price_scraper - INFO - Closing WebDriver
2025-05-11 23:41:38,355 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:41:38,356 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:41:38,497 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-05-11 23:41:38,498 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:41:38,498 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:41:57,351 - app - INFO - Using TensorFlow-based LSTM model
2025-05-11 23:41:57,383 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:41:57,384 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:41:57,515 - app.utils.memory_management - INFO - Garbage collection: collected 274 objects
2025-05-11 23:41:57,515 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:41:57,515 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:42:10,484 - app - INFO - Cleaning up resources...
2025-05-11 23:42:10,486 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,486 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:42:10,617 - app.utils.memory_management - INFO - Garbage collection: collected 311 objects
2025-05-11 23:42:10,617 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,617 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:42:10,617 - app - INFO - Application shutdown complete
2025-05-11 23:42:10,617 - app - INFO - Cleaning up resources...
2025-05-11 23:42:10,617 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,617 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:42:10,733 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 23:42:10,733 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,733 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:42:10,733 - app - INFO - Application shutdown complete
2025-05-11 23:42:10,733 - app - INFO - Cleaning up resources...
2025-05-11 23:42:10,733 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,733 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:42:10,850 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 23:42:10,850 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,850 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:42:10,850 - app - INFO - Application shutdown complete
2025-05-11 23:42:10,850 - app - INFO - Cleaning up resources...
2025-05-11 23:42:10,850 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,850 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:42:10,981 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 23:42:10,981 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,982 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:42:10,982 - app - INFO - Application shutdown complete
2025-05-11 23:42:10,982 - app - INFO - Cleaning up resources...
2025-05-11 23:42:10,983 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:10,983 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:42:11,100 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 23:42:11,100 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:11,100 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:42:11,100 - app - INFO - Application shutdown complete
2025-05-11 23:42:11,100 - app - INFO - Cleaning up resources...
2025-05-11 23:42:11,100 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:11,100 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:42:11,217 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-11 23:42:11,217 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:11,217 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:42:11,217 - app - INFO - Application shutdown complete
2025-05-11 23:42:11,217 - app - INFO - Cleaning up resources...
2025-05-11 23:42:11,217 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:11,217 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-05-11 23:42:11,333 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-05-11 23:42:11,333 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-05-11 23:42:11,345 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-05-11 23:42:11,345 - app - INFO - Application shutdown complete
