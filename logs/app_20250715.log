2025-07-15 11:58:34,463 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-15 11:58:37,498 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 11:58:37,499 - app - INFO - Memory management utilities loaded
2025-07-15 11:58:37,503 - app - INFO - Error handling utilities loaded
2025-07-15 11:58:37,510 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 11:58:37,511 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 11:58:37,511 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 11:58:37,511 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 11:58:37,515 - app.utils.numpy_fix - INFO - <PERSON><PERSON>19937 is properly registered as a BitGenerator
2025-07-15 11:58:37,516 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 11:58:37,516 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 11:58:37,516 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 11:58:37,644 - app - INFO - Applied NumPy fix
2025-07-15 11:58:37,647 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 11:58:37,648 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 11:58:37,648 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 11:58:37,648 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 11:58:37,648 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 11:58:37,648 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 11:58:37,648 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 11:58:37,649 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 11:58:57,227 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 11:58:57,227 - app - INFO - Applied TensorFlow fix
2025-07-15 11:58:57,231 - app.config - INFO - Configuration initialized
2025-07-15 11:58:57,243 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-15 11:58:57,577 - models.train - INFO - TensorFlow test successful
2025-07-15 11:58:57,717 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 11:58:57,717 - models.train - INFO - Transformer model is available
2025-07-15 11:58:57,717 - models.train - INFO - Using TensorFlow-based models
2025-07-15 11:58:57,727 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 11:58:57,727 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 11:58:57,731 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 11:58:59,702 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 11:58:59,702 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 11:58:59,702 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 11:58:59,702 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 11:58:59,702 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 11:58:59,703 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 11:58:59,703 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 11:58:59,703 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 11:58:59,703 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 11:58:59,703 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 11:58:59,947 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 11:58:59,950 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 11:59:00,458 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-15 11:59:02,707 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-15 11:59:02,707 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-15 11:59:03,044 - app.utils.session_state - INFO - Initializing session state
2025-07-15 11:59:03,046 - app.utils.session_state - INFO - Session state initialized
2025-07-15 11:59:03,735 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 11:59:03,744 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 11:59:03,744 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 11:59:03,959 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 11:59:03,959 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 11:59:03,960 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 11:59:45,461 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 11:59:45,516 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 11:59:45,574 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.05 seconds
2025-07-15 11:59:45,579 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 11:59:45,579 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 11:59:45,580 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 11:59:46,041 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.53 seconds
2025-07-15 11:59:46,043 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 11:59:46,044 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 11:59:46,275 - app.utils.memory_management - INFO - Garbage collection: collected 1579 objects
2025-07-15 11:59:46,276 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 11:59:46,276 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 11:59:56,148 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 11:59:56,181 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 11:59:56,204 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 11:59:56,205 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 11:59:56,205 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 11:59:56,206 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 11:59:56,553 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.37 seconds
2025-07-15 11:59:56,555 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 11:59:56,555 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 11:59:56,745 - app.utils.memory_management - INFO - Garbage collection: collected 3047 objects
2025-07-15 11:59:56,745 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 11:59:56,746 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:02,168 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:02,259 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 12:00:02,310 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.04 seconds
2025-07-15 12:00:02,314 - app.utils.common - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 12:00:02,314 - app.utils.common - INFO - Data shape: (1250, 36)
2025-07-15 12:00:02,314 - app.utils.common - INFO - File COMI contains 2025 data
2025-07-15 12:00:02,614 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.36 seconds
2025-07-15 12:00:02,616 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:02,616 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:02,889 - app.utils.memory_management - INFO - Garbage collection: collected 3176 objects
2025-07-15 12:00:02,890 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:02,890 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:07,060 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:07,106 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-15 12:00:07,107 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 12:00:07,107 - app - INFO - Data shape: (1250, 36)
2025-07-15 12:00:07,107 - app - INFO - File COMI contains 2025 data
2025-07-15 12:00:07,140 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-07-15 12:00:07,140 - app - INFO - Features shape: (1250, 36)
2025-07-15 12:00:07,166 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-15 12:00:07,167 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 12:00:07,167 - app - INFO - Data shape: (1250, 36)
2025-07-15 12:00:07,167 - app - INFO - File COMI contains 2025 data
2025-07-15 12:00:07,175 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:07,176 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:07,401 - app.utils.memory_management - INFO - Garbage collection: collected 1250 objects
2025-07-15 12:00:07,401 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:07,401 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:07,790 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:08,191 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 12:00:08,504 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 12:00:08,519 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 12:00:08,521 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 12:00:08,521 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 12:00:08,522 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 12:00:08,872 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.37 seconds
2025-07-15 12:00:09,275 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:09,276 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:09,541 - app.utils.memory_management - INFO - Garbage collection: collected 2466 objects
2025-07-15 12:00:09,542 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:09,545 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:21,133 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:21,172 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-15 12:00:21,198 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-15 12:00:21,292 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:21,293 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:22,055 - app.utils.memory_management - INFO - Garbage collection: collected 3724 objects
2025-07-15 12:00:22,058 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:22,058 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:27,594 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:27,608 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-15 12:00:27,623 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-15 12:00:27,752 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:27,753 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:27,945 - app.utils.memory_management - INFO - Garbage collection: collected 2136 objects
2025-07-15 12:00:27,946 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:27,947 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:28,723 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:28,746 - app.utils.state_manager - INFO - Found 14 stock files in data/stocks
2025-07-15 12:00:29,703 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:29,704 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:29,898 - app.utils.memory_management - INFO - Garbage collection: collected 1954 objects
2025-07-15 12:00:29,898 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:29,900 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:32,280 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:32,716 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:32,717 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:32,941 - app.utils.memory_management - INFO - Garbage collection: collected 1606 objects
2025-07-15 12:00:32,941 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:32,943 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:33,361 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:33,400 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-07-15 12:00:33,592 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:00:33,592 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:00:33,598 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:00:33,598 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:00:33,605 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:00:33,608 - app.utils.error_handling - INFO - live_trading_component executed in 0.22 seconds
2025-07-15 12:00:33,609 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:33,609 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:33,846 - app.utils.memory_management - INFO - Garbage collection: collected 1532 objects
2025-07-15 12:00:33,846 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:33,846 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:37,568 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:37,671 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:00:37,671 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:00:37,672 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:00:37,672 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:00:37,679 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:00:37,682 - app.utils.error_handling - INFO - live_trading_component executed in 0.07 seconds
2025-07-15 12:00:37,683 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:37,683 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:37,883 - app.utils.memory_management - INFO - Garbage collection: collected 1144 objects
2025-07-15 12:00:37,885 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:37,886 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:00:41,740 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:00:51,436 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:00:51,437 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:00:51,437 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:00:51,438 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:00:51,446 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:00:51,449 - app.utils.error_handling - INFO - live_trading_component executed in 9.69 seconds
2025-07-15 12:00:51,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:51,451 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:00:51,672 - app.utils.memory_management - INFO - Garbage collection: collected 1135 objects
2025-07-15 12:00:51,673 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:00:51,674 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:01:18,221 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:01:18,261 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:01:18,262 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:18,262 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:01:18,262 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:18,269 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:01:18,272 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 12:01:18,274 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-15 12:01:18,285 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:01:18,286 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:01:18,293 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:18,298 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:01:18,301 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-15 12:01:18,309 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:01:18,310 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:01:18,310 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:01:18,310 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:18,311 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:01:18,312 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-15 12:01:18,319 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:01:18,320 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:01:18,320 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:01:18,320 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:18,321 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:01:18,321 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-15 12:01:18,331 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:01:18,332 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:18,332 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:01:18,332 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:18,332 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:01:18,333 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 12:01:18,351 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:18,352 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:18,352 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:18,359 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:18,360 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:18,370 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:01:18,370 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:18,372 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:01:18,373 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:18,374 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:01:18,375 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:01:18,381 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:01:18,382 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:01:18,394 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:18,397 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:01:18,398 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:01:18,398 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:01:18,399 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:01:18,399 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:18,399 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:01:18,399 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:01:18,400 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:01:18,400 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:01:18,400 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:18,400 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:01:18,400 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:01:18,400 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:18,401 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:01:18,401 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:18,401 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:01:18,408 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:18,413 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:01:18,414 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:01:18,414 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:01:18,414 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:18,424 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:01:18,424 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:01:18,633 - app.utils.memory_management - INFO - Garbage collection: collected 1258 objects
2025-07-15 12:01:18,634 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:01:18,634 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:01:26,866 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:01:26,912 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:26,913 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:26,913 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:26,924 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:26,925 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:26,939 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:01:26,939 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:26,940 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:01:26,940 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:26,940 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:01:26,941 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:01:26,947 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:01:26,948 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:01:26,950 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:26,958 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:01:26,959 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:01:26,960 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:01:26,961 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:01:26,962 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:26,962 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:01:26,963 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:01:26,963 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:01:26,964 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:01:26,965 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:26,966 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:01:26,966 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:01:26,966 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:26,966 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:01:26,967 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:26,968 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:01:26,979 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:26,987 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:01:26,988 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:01:26,989 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:01:26,990 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:27,009 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:01:27,009 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:01:27,234 - app.utils.memory_management - INFO - Garbage collection: collected 1621 objects
2025-07-15 12:01:27,234 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:01:27,234 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:01:30,742 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:01:30,789 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:30,796 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:30,798 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:30,798 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:30,799 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:30,801 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-15 12:01:30,803 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:30,804 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:30,806 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:30,811 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:30,811 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:30,812 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-15 12:01:30,812 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:36,119 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-15 12:01:36,172 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:01:36,375 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:01:36,375 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:01:36,579 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:01:36,579 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:01:36,583 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 12:01:36,800 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 12:01:36,800 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 12:01:36,800 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 12:01:36,800 - models.predict - INFO - Ensemble model already loaded
2025-07-15 12:01:36,862 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 12:01:36,863 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4147952911752378
2025-07-15 12:01:36,863 - models.predict - INFO - Prediction for 60 minutes horizon: 85.29112933637401
2025-07-15 12:01:36,874 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:36,874 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:36,874 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:36,879 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:36,879 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:36,923 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:36,924 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:36,924 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:36,938 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:36,939 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:36,959 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:01:36,961 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:36,962 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:01:36,963 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:36,964 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:01:36,966 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:01:36,969 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:01:36,973 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:01:36,978 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:36,982 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:01:36,983 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:01:36,983 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:01:36,984 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:01:36,984 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:36,984 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:01:36,985 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:01:36,985 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:01:36,987 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:01:36,987 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:36,987 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:01:36,989 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:01:36,989 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:36,990 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:01:36,990 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:36,990 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:01:36,999 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:37,006 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:01:37,007 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:01:37,008 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:01:37,009 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:01:37,020 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:01:37,021 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:01:37,293 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-07-15 12:01:37,293 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:01:37,294 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:01:57,991 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:01:58,037 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:58,038 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:01:58,038 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:01:58,042 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:01:58,044 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:01:58,052 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:20,050 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-15 12:02:20,087 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:02:20,296 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:20,297 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:02:20,500 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:20,501 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:02:20,502 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 12:02:20,648 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 12:02:20,648 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 12:02:20,648 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 12:02:20,648 - models.predict - INFO - Ensemble model already loaded
2025-07-15 12:02:20,676 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 12:02:20,676 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4147952911752378
2025-07-15 12:02:20,677 - models.predict - INFO - Prediction for 60 minutes horizon: 85.29112933637401
2025-07-15 12:02:20,699 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:20,700 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:20,701 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:20,701 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:20,702 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:20,703 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:02:20,712 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:02:20,712 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:02:20,716 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:20,720 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:02:20,721 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:02:20,721 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:02:20,721 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:02:20,722 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:20,722 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:02:20,723 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:02:20,724 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:02:20,725 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:02:20,727 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:20,728 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:02:20,729 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:20,730 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:20,730 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:20,730 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:20,730 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:20,738 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:20,741 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:02:20,741 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:02:20,741 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:02:20,741 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:20,758 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:20,758 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:02:20,965 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 12:02:20,965 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:20,966 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:02:34,815 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:02:34,864 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:34,865 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:34,865 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:34,868 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:34,869 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:34,876 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:34,876 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:34,876 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:34,877 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:34,878 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:34,878 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:02:34,888 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:02:34,889 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:02:34,897 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:34,905 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:02:34,906 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:02:34,907 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:02:34,908 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:02:34,909 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:34,910 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:02:34,911 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:02:34,912 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:02:34,913 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:02:34,914 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:34,914 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:02:34,915 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:34,917 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:34,918 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:34,918 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:34,919 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:34,931 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:34,937 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:02:34,938 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:02:34,939 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:02:34,940 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:34,953 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:34,954 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:02:35,169 - app.utils.memory_management - INFO - Garbage collection: collected 1880 objects
2025-07-15 12:02:35,170 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:35,171 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:02:37,551 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:02:37,600 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:37,600 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:37,601 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:37,606 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:37,608 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:37,662 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:02:37,868 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:37,870 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:02:38,103 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:38,105 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:02:38,108 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 12:02:38,197 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 12:02:38,198 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 12:02:38,198 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 12:02:38,198 - models.predict - INFO - Ensemble model already loaded
2025-07-15 12:02:38,233 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 12:02:38,234 - models.predict - INFO - Current price: 87.0, Predicted scaled value: 0.4236584450257059
2025-07-15 12:02:38,234 - models.predict - INFO - Prediction for 60 minutes horizon: 86.09094039037306
2025-07-15 12:02:38,247 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:38,249 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:38,249 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:38,249 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:38,250 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:38,250 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:02:38,254 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:02:38,256 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:02:38,259 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:38,265 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:02:38,265 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:02:38,266 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:02:38,266 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:02:38,266 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:38,266 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:02:38,267 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:02:38,267 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:02:38,268 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:02:38,268 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:38,268 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:02:38,269 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:38,269 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:38,269 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:38,269 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:38,270 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:38,280 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:38,285 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:02:38,285 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:02:38,287 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:02:38,287 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:38,310 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:38,313 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:02:38,552 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 12:02:38,552 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:38,552 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:02:47,913 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:02:47,965 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:47,966 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:47,967 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:47,975 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:47,976 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:47,992 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:47,993 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:47,993 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:47,993 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:47,993 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:47,994 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:02:48,003 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:02:48,003 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:02:48,011 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:48,016 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:02:48,018 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:02:48,018 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:02:48,019 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:02:48,019 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:48,020 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:02:48,021 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:02:48,023 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:02:48,025 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:02:48,026 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:48,026 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:02:48,026 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:48,026 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:48,026 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:48,026 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:48,027 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:48,033 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:48,037 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:48,037 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:48,038 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:48,039 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:48,052 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:48,053 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:02:48,262 - app.utils.memory_management - INFO - Garbage collection: collected 1664 objects
2025-07-15 12:02:48,263 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:48,263 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:02:51,406 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:02:51,456 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:51,456 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:51,456 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:51,461 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:51,465 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:51,482 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:51,483 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:51,483 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:51,484 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:51,484 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:51,485 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:02:51,489 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:02:51,490 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:02:51,492 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:51,498 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:02:51,500 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:02:51,501 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:02:51,502 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:02:51,503 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:51,503 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:02:51,504 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:02:51,504 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:02:51,504 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:02:51,504 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:51,504 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:02:51,504 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:02:51,507 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:02:51,508 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:02:51,508 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:51,509 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:02:51,519 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:51,529 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:02:51,530 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:02:51,530 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:02:51,531 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:51,534 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:02:57,177 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=True
2025-07-15 12:02:57,212 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:02:57,425 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:57,425 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:02:57,627 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:02:57,627 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:02:57,629 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-15 12:02:57,632 - models.hybrid_model - INFO - XGBoost is available
2025-07-15 12:02:57,633 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-15 12:02:57,635 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-15 12:02:57,635 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-15 12:02:57,635 - models.predict - ERROR - Error making predictions: local variable 'model_path' referenced before assignment
2025-07-15 12:02:57,635 - models.predict - ERROR - Error making predictions from live data: local variable 'model_path' referenced before assignment
2025-07-15 12:02:57,635 - app.pages.predictions_consolidated - ERROR - Unified prediction engine error: local variable 'model_path' referenced before assignment
2025-07-15 12:02:57,642 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:03:02,211 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=True
2025-07-15 12:03:02,247 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:03:02,454 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:02,454 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:03:02,656 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:02,657 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:03:02,659 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-15 12:03:02,659 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-15 12:03:03,252 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:03:04,928 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-15 12:03:04,929 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4584633708000183
2025-07-15 12:03:04,929 - models.predict - INFO - Prediction for 60 minutes horizon: 89.23173709068544
2025-07-15 12:03:04,931 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 89.23 vs current 86.30 (3.4% change, limit: 2.0%). Applying correction.
2025-07-15 12:03:04,931 - app.pages.predictions_consolidated - INFO - Corrected prediction: 89.23 -> 87.51 (change: 1.4%)
2025-07-15 12:03:04,931 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 89.23 -> 87.51 for 60min
2025-07-15 12:03:04,931 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:03:09,618 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-15 12:03:09,652 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:03:09,872 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:09,872 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:03:10,073 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:10,073 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:03:10,076 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-15 12:03:10,077 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-15 12:03:10,078 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-15 12:03:10,078 - models.predict - ERROR - Error making predictions: local variable 'model_path' referenced before assignment
2025-07-15 12:03:10,079 - models.predict - ERROR - Error making predictions from live data: local variable 'model_path' referenced before assignment
2025-07-15 12:03:10,079 - app.pages.predictions_consolidated - ERROR - Unified prediction engine error: local variable 'model_path' referenced before assignment
2025-07-15 12:03:10,080 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:03:14,826 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-15 12:03:14,855 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:03:15,061 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:15,062 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:03:15,271 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:15,272 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:03:15,274 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-15 12:03:15,274 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-15 12:03:15,274 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-15 12:03:15,274 - models.predict - ERROR - Error making predictions: local variable 'model_path' referenced before assignment
2025-07-15 12:03:15,274 - models.predict - ERROR - Error making predictions from live data: local variable 'model_path' referenced before assignment
2025-07-15 12:03:15,275 - app.pages.predictions_consolidated - ERROR - Unified prediction engine error: local variable 'model_path' referenced before assignment
2025-07-15 12:03:15,276 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:03:19,951 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-15 12:03:19,994 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:03:20,204 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:20,204 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:03:20,414 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:20,414 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:03:20,416 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 12:03:20,550 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 12:03:20,550 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 12:03:20,550 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 12:03:20,550 - models.predict - INFO - Ensemble model already loaded
2025-07-15 12:03:20,579 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 12:03:20,579 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4147952911752378
2025-07-15 12:03:20,579 - models.predict - INFO - Prediction for 60 minutes horizon: 85.29112933637401
2025-07-15 12:03:20,612 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:20,613 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:03:20,842 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 12:03:20,843 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:03:20,843 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:13:26,698 - app - INFO - Cleaning up resources...
2025-07-15 12:13:26,701 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:13:26,702 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:13:27,316 - app.utils.memory_management - INFO - Garbage collection: collected 2347 objects
2025-07-15 12:13:27,316 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:13:27,316 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:13:27,316 - app - INFO - Application shutdown complete
2025-07-15 12:13:36,705 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 12:13:38,976 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 12:13:38,976 - app - INFO - Memory management utilities loaded
2025-07-15 12:13:38,978 - app - INFO - Error handling utilities loaded
2025-07-15 12:13:38,979 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 12:13:38,980 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 12:13:38,981 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 12:13:38,981 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 12:13:38,982 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 12:13:38,982 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 12:13:38,982 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 12:13:38,983 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 12:13:38,983 - app - INFO - Applied NumPy fix
2025-07-15 12:13:38,983 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 12:13:38,984 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 12:13:38,984 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 12:13:38,984 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 12:13:38,985 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 12:13:38,985 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 12:13:38,985 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 12:13:38,985 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 12:13:47,797 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 12:13:47,798 - app - INFO - Applied TensorFlow fix
2025-07-15 12:13:47,800 - app.config - INFO - Configuration initialized
2025-07-15 12:13:47,805 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-15 12:13:47,824 - models.train - INFO - TensorFlow test successful
2025-07-15 12:13:47,870 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 12:13:47,870 - models.train - INFO - Transformer model is available
2025-07-15 12:13:47,870 - models.train - INFO - Using TensorFlow-based models
2025-07-15 12:13:47,872 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 12:13:47,872 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 12:13:47,875 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 12:13:48,462 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 12:13:48,462 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 12:13:48,463 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 12:13:48,463 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 12:13:48,463 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 12:13:48,463 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 12:13:48,463 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 12:13:48,463 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 12:13:48,463 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 12:13:48,464 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 12:13:48,617 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 12:13:48,619 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:13:49,129 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-15 12:13:50,189 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-15 12:13:50,190 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-15 12:13:50,492 - app.utils.session_state - INFO - Initializing session state
2025-07-15 12:13:50,494 - app.utils.session_state - INFO - Session state initialized
2025-07-15 12:13:51,170 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 12:13:51,186 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:13:51,186 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:13:51,427 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 12:13:51,427 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:13:51,428 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:00,387 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:00,413 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:00,414 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:00,626 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 12:14:00,627 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:00,628 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:01,789 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:01,873 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.04 seconds
2025-07-15 12:14:01,875 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 12:14:01,875 - app - INFO - Data shape: (1250, 36)
2025-07-15 12:14:01,875 - app - INFO - File COMI contains 2025 data
2025-07-15 12:14:01,906 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-07-15 12:14:01,906 - app - INFO - Features shape: (1250, 36)
2025-07-15 12:14:01,928 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-15 12:14:01,929 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 12:14:01,929 - app - INFO - Data shape: (1250, 36)
2025-07-15 12:14:01,929 - app - INFO - File COMI contains 2025 data
2025-07-15 12:14:01,932 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:01,933 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:02,136 - app.utils.memory_management - INFO - Garbage collection: collected 730 objects
2025-07-15 12:14:02,136 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:02,137 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:02,815 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:03,077 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:03,078 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:03,307 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-15 12:14:03,307 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:03,308 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:05,839 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:05,910 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:14:05,911 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:05,912 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:05,913 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:05,914 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:05,914 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 12:14:05,915 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-15 12:14:05,925 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:05,926 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:14:05,930 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:05,937 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:14:05,950 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-15 12:14:05,959 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:14:05,960 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:05,960 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:14:05,960 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:05,961 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:14:05,961 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-15 12:14:05,972 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:14:05,972 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:05,973 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:14:05,973 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:05,973 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:14:05,973 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-15 12:14:05,978 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:14:05,979 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:05,979 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:05,979 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:05,980 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:05,980 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 12:14:06,011 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:06,012 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:06,012 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:06,022 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:06,022 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:06,035 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:14:06,036 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:06,037 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:06,038 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:06,038 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:06,039 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:14:06,046 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:06,047 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:14:06,055 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:06,063 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:14:06,064 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:14:06,064 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:06,064 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:14:06,065 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:06,065 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:14:06,065 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:14:06,067 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:06,067 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:14:06,067 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:06,067 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:14:06,069 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:14:06,071 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:06,072 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:06,072 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:06,072 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:06,080 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:06,083 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:06,084 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:06,084 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:06,084 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:06,104 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:06,105 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:06,308 - app.utils.memory_management - INFO - Garbage collection: collected 878 objects
2025-07-15 12:14:06,309 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:06,310 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:14,965 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:15,064 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:15,065 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:15,065 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:15,072 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:15,072 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:15,082 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:14:15,083 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:15,085 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:15,085 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:15,086 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:15,087 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:14:15,095 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:15,096 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:14:15,099 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:15,104 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:14:15,104 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:14:15,104 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:15,104 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:14:15,105 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:15,105 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:14:15,105 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:14:15,105 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:15,105 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:14:15,106 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:15,106 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:14:15,106 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:14:15,107 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:15,107 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:15,107 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:15,107 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:15,155 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:15,162 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:15,164 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:15,165 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:15,166 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:15,178 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:15,179 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:15,392 - app.utils.memory_management - INFO - Garbage collection: collected 1554 objects
2025-07-15 12:14:15,393 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:15,394 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:17,394 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:17,457 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:17,463 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:17,465 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:17,466 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:17,467 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:17,467 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-15 12:14:17,468 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:17,472 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:17,472 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:17,479 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:17,480 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:17,481 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-15 12:14:17,481 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:27,207 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-15 12:14:27,266 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:14:27,489 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:27,490 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:14:27,678 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:27,678 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:14:27,680 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 12:14:28,007 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 12:14:28,008 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 12:14:28,008 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 12:14:28,008 - models.predict - INFO - Ensemble model already loaded
2025-07-15 12:14:28,052 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 12:14:28,052 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4147952911752378
2025-07-15 12:14:28,052 - models.predict - INFO - Prediction for 60 minutes horizon: 85.29112933637401
2025-07-15 12:14:28,060 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:28,060 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:28,061 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:28,066 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:28,067 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:28,216 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:28,217 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:28,218 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:28,225 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:28,225 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:28,239 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:14:28,240 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:28,240 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:28,241 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:28,242 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:28,243 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:14:28,262 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:28,262 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:14:28,266 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:28,269 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:14:28,269 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:14:28,270 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:28,270 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:14:28,270 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:28,270 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:14:28,270 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:14:28,270 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:28,270 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:14:28,271 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:28,271 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:14:28,271 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:14:28,271 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:28,271 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:28,271 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:28,271 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:28,310 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:28,318 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:28,318 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:28,319 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:28,319 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:28,342 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:28,344 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:28,541 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-07-15 12:14:28,541 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:28,542 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:36,605 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:36,618 - app.utils.session_state - INFO - Initializing session state
2025-07-15 12:14:36,620 - app.utils.session_state - INFO - Session state initialized
2025-07-15 12:14:36,650 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:36,651 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:36,865 - app.utils.memory_management - INFO - Garbage collection: collected 2006 objects
2025-07-15 12:14:36,866 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:36,867 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:50,550 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:50,573 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:50,573 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:50,862 - app.utils.memory_management - INFO - Garbage collection: collected 866 objects
2025-07-15 12:14:50,862 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:50,863 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:51,631 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:51,658 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:51,658 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:52,015 - app.utils.memory_management - INFO - Garbage collection: collected 626 objects
2025-07-15 12:14:52,016 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:52,017 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:52,686 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:52,832 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:52,832 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:14:53,125 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-15 12:14:53,127 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:53,127 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:14:59,620 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:14:59,684 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:59,684 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:59,684 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:59,697 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:59,697 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:59,716 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:14:59,716 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:59,717 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:59,717 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:59,717 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:59,717 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:14:59,721 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:59,721 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:14:59,729 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:14:59,739 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:14:59,740 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:14:59,740 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:59,740 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:14:59,741 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:14:59,742 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:14:59,742 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:14:59,742 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:59,742 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:14:59,742 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:14:59,748 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:14:59,749 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:14:59,750 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:59,751 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:14:59,751 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:14:59,751 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:14:59,763 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:59,791 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:14:59,792 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:14:59,793 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:14:59,793 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:14:59,811 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:14:59,811 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:15:00,027 - app.utils.memory_management - INFO - Garbage collection: collected 878 objects
2025-07-15 12:15:00,028 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:00,029 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:15:08,975 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:15:09,022 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:09,024 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:09,024 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:15:09,032 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:15:09,033 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:15:09,046 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:15:09,046 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:15:09,047 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:15:09,047 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:09,047 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:15:09,047 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:15:09,051 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:15:09,051 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:15:09,057 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:15:09,063 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:15:09,063 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:15:09,064 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:15:09,065 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:15:09,065 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:15:09,066 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:15:09,069 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:15:09,070 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:15:09,070 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:15:09,071 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:15:09,071 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:15:09,071 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:15:09,072 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:15:09,073 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:15:09,073 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:09,077 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:15:09,090 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:09,107 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:15:09,107 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:15:09,108 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:15:09,108 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:09,125 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:09,126 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:15:09,353 - app.utils.memory_management - INFO - Garbage collection: collected 1515 objects
2025-07-15 12:15:09,354 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:09,355 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:15:14,428 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:15:14,494 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:14,495 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:14,495 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:15:14,498 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:15:14,498 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:15:14,515 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:15:14,515 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:15:14,516 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:15:14,516 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:14,516 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:15:14,516 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:15:14,523 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:15:14,525 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:15:14,528 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:15:14,531 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:15:14,532 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:15:14,532 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:15:14,532 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:15:14,532 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:15:14,533 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:15:14,533 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:15:14,533 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:15:14,533 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:15:14,534 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:15:14,534 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:15:14,534 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:15:14,534 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:15:14,535 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:15:14,535 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:14,535 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:15:14,543 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:14,551 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:15:14,551 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:15:14,552 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:15:14,552 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:14,559 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:21,463 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=True
2025-07-15 12:15:21,507 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:15:21,700 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:21,701 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:15:21,893 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:21,893 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:15:21,895 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-15 12:15:21,923 - models.hybrid_model - INFO - XGBoost is available
2025-07-15 12:15:21,924 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-15 12:15:21,925 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-15 12:15:21,925 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-15 12:15:21,926 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:15:21,926 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 12:15:21,937 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 12:15:21,937 - models.predict - INFO - Using fallback price due to error: 86.3
2025-07-15 12:15:21,938 - models.predict - INFO - Prediction for 60 minutes horizon: 86.3
2025-07-15 12:15:21,958 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:15:25,819 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=False
2025-07-15 12:15:25,865 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:15:26,084 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:26,085 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:15:26,275 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:26,275 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:15:26,277 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-15 12:15:26,277 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-15 12:15:26,489 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:15:27,026 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-15 12:15:27,026 - models.predict - INFO - Current price: 87.0, Predicted scaled value: 0.3832293748855591
2025-07-15 12:15:27,026 - models.predict - INFO - Prediction for 60 minutes horizon: 82.44262087042202
2025-07-15 12:15:27,027 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 82.44 vs current 87.00 (5.2% change, limit: 2.0%). Applying correction.
2025-07-15 12:15:27,027 - app.pages.predictions_consolidated - INFO - Corrected prediction: 82.44 -> 85.78 (change: -1.4%)
2025-07-15 12:15:27,028 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 82.44 -> 85.78 for 60min
2025-07-15 12:15:27,028 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:15:32,175 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-15 12:15:32,223 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:15:32,434 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:32,434 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:15:32,624 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:32,625 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:15:32,626 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-15 12:15:32,626 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-15 12:15:32,626 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-15 12:15:32,627 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:15:32,646 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 12:15:32,646 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 12:15:32,646 - models.predict - INFO - Using fallback price due to error: 86.3
2025-07-15 12:15:32,646 - models.predict - INFO - Prediction for 60 minutes horizon: 86.3
2025-07-15 12:15:32,648 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:15:37,531 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-15 12:15:37,598 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:15:38,031 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:38,032 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:15:38,226 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:38,227 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:15:38,228 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-15 12:15:38,228 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-15 12:15:38,228 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-15 12:15:38,228 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:15:38,229 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 12:15:38,229 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 12:15:38,229 - models.predict - INFO - Using fallback price due to error: 86.3
2025-07-15 12:15:38,229 - models.predict - INFO - Prediction for 60 minutes horizon: 86.3
2025-07-15 12:15:38,230 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:15:43,168 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-15 12:15:43,208 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:15:43,408 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:43,408 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:15:43,599 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:43,599 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:15:43,601 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 12:15:43,725 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 12:15:43,725 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 12:15:43,725 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 12:15:43,725 - models.predict - INFO - Ensemble model already loaded
2025-07-15 12:15:43,755 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 12:15:43,755 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4147952911752378
2025-07-15 12:15:43,756 - models.predict - INFO - Prediction for 60 minutes horizon: 85.29112933637401
2025-07-15 12:15:43,828 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:43,829 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:15:44,055 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 12:15:44,056 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:15:44,056 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:16:51,017 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:16:51,060 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-07-15 12:16:51,084 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:16:51,087 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:16:51,088 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:16:51,089 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:16:51,096 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:16:51,099 - app.utils.error_handling - INFO - live_trading_component executed in 0.05 seconds
2025-07-15 12:16:51,100 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:16:51,101 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:16:51,334 - app.utils.memory_management - INFO - Garbage collection: collected 2188 objects
2025-07-15 12:16:51,335 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:16:51,335 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:16:54,167 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:17:00,973 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:17:00,977 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:17:00,977 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:17:00,977 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:17:00,983 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:17:00,988 - app.utils.error_handling - INFO - live_trading_component executed in 6.79 seconds
2025-07-15 12:17:00,988 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:17:00,988 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:17:01,210 - app.utils.memory_management - INFO - Garbage collection: collected 1139 objects
2025-07-15 12:17:01,211 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:17:01,212 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:18:08,451 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:18:08,510 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:18:08,511 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:18:08,511 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:18:08,512 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:18:08,517 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:18:08,542 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:18:08,544 - app.utils.error_handling - INFO - live_trading_component executed in 0.08 seconds
2025-07-15 12:18:08,544 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:18:08,545 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:18:08,747 - app.utils.memory_management - INFO - Garbage collection: collected 1259 objects
2025-07-15 12:18:08,748 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:18:08,749 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:18:11,597 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:18:11,656 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:18:11,657 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:18:11,657 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:18:11,658 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:18:11,669 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:18:11,674 - app.utils.error_handling - INFO - live_trading_component executed in 0.05 seconds
2025-07-15 12:18:11,676 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:18:11,676 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:18:11,880 - app.utils.memory_management - INFO - Garbage collection: collected 1162 objects
2025-07-15 12:18:11,881 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:18:11,882 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:18:14,794 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:18:14,850 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:18:14,851 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:18:14,851 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:18:14,851 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:18:14,859 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:18:14,911 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:18:15,118 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:18:15,125 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:18:15,320 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:18:15,321 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-15 12:18:15,328 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-15 12:18:15,329 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-15 12:18:15,592 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:18:16,139 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-15 12:18:16,140 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4584633708000183
2025-07-15 12:18:16,140 - models.predict - INFO - Prediction for 60 minutes horizon: 89.23173709068544
2025-07-15 12:18:16,199 - app.utils.error_handling - INFO - live_trading_component executed in 1.39 seconds
2025-07-15 12:18:16,199 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:18:16,199 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:18:16,431 - app.utils.memory_management - INFO - Garbage collection: collected 17191 objects
2025-07-15 12:18:16,431 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:18:16,431 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:19:16,445 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:19:16,473 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 12:19:24,794 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:19:24,795 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:19:24,796 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:19:24,796 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:19:24,810 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:19:24,814 - app.utils.error_handling - INFO - live_trading_component executed in 8.33 seconds
2025-07-15 12:19:24,815 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:19:24,816 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:19:25,051 - app.utils.memory_management - INFO - Garbage collection: collected 1282 objects
2025-07-15 12:19:25,052 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:19:25,052 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:24:54,052 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:24:54,075 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 12:24:54,120 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:24:54,121 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:24:54,121 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:24:54,122 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:24:54,123 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:24:54,123 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 12:24:54,123 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-15 12:24:54,133 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:24:54,133 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:24:54,140 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:24:54,146 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:24:54,151 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-15 12:24:54,157 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:24:54,157 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:24:54,157 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:24:54,158 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:24:54,158 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:24:54,158 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-15 12:24:54,164 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:24:54,165 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:24:54,165 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:24:54,165 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:24:54,166 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:24:54,166 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-15 12:24:54,173 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 12:24:54,174 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:24:54,174 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:24:54,174 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:24:54,174 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:24:54,174 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 12:24:54,195 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:24:54,195 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:24:54,195 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:24:54,203 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:24:54,204 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:24:54,215 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:24:54,216 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:24:54,216 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:24:54,216 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:24:54,217 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:24:54,217 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:24:54,219 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:24:54,220 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:24:54,223 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:24:54,226 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:24:54,227 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:24:54,227 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:24:54,227 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:24:54,228 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:24:54,228 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:24:54,229 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:24:54,230 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:24:54,230 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:24:54,231 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:24:54,232 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:24:54,232 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:24:54,233 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:24:54,234 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:24:54,234 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:24:54,235 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:24:54,243 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:24:54,246 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:24:54,246 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:24:54,246 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:24:54,247 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:24:54,256 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:24:54,258 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:24:54,522 - app.utils.memory_management - INFO - Garbage collection: collected 1297 objects
2025-07-15 12:24:54,523 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:24:54,523 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:25:06,033 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:25:06,083 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:06,084 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:06,085 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:06,092 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:06,093 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:06,109 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:06,110 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:25:06,110 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:25:06,111 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:06,111 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:25:06,111 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:25:06,123 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:25:06,124 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:25:06,132 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:06,137 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:25:06,138 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:25:06,138 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:25:06,138 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:25:06,139 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:06,139 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:25:06,140 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:25:06,140 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:25:06,141 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:25:06,141 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:06,141 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:25:06,141 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:06,142 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:25:06,142 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:25:06,142 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:06,142 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:25:06,150 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:06,157 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:06,157 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:06,158 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:06,159 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:06,175 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:06,176 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:25:06,401 - app.utils.memory_management - INFO - Garbage collection: collected 1659 objects
2025-07-15 12:25:06,402 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:06,403 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:25:09,131 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:25:09,176 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:09,177 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:25:09,178 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:09,179 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:25:09,179 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:25:09,186 - app.utils.data_processing - INFO - Found hybrid model for COMI with 5 minutes horizon
2025-07-15 12:25:09,187 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:09,187 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:09,188 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:25:09,188 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:25:09,188 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:25:09,188 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-15 12:25:09,188 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:15,412 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=5, model=ensemble, live_data=True
2025-07-15 12:25:15,454 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-15 12:25:15,645 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:15,650 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-15 12:25:15,844 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:15,844 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-15 12:25:15,846 - models.predict - INFO - Using RobustEnsembleModel for 5 minutes horizon
2025-07-15 12:25:15,977 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_5min.joblib
2025-07-15 12:25:15,977 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 5
2025-07-15 12:25:15,977 - models.predict - INFO - Loading ensemble model for COMI with horizon 5
2025-07-15 12:25:15,977 - models.predict - INFO - Ensemble model already loaded
2025-07-15 12:25:16,004 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 12:25:16,004 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4147952911752378
2025-07-15 12:25:16,005 - models.predict - INFO - Prediction for 5 minutes horizon: 85.29112933637401
2025-07-15 12:25:16,011 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:16,012 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:16,012 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:25:16,012 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:25:16,013 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:25:16,050 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:16,051 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:16,051 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:16,060 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:16,060 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:16,077 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:16,077 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:25:16,078 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:25:16,079 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:16,079 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:25:16,080 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:25:16,087 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:25:16,090 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:25:16,096 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:16,103 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:25:16,104 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:25:16,104 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:25:16,104 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:25:16,104 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:16,104 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:25:16,104 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:25:16,105 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:25:16,105 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:25:16,106 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:16,106 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:25:16,106 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:16,107 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:25:16,107 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:25:16,107 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:16,107 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:25:16,114 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:16,123 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:16,123 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:16,124 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:16,125 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:16,142 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:16,142 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:25:16,365 - app.utils.memory_management - INFO - Garbage collection: collected 220 objects
2025-07-15 12:25:16,367 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:16,368 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:25:19,470 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:25:19,523 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:19,523 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:19,523 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:19,532 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:19,533 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:19,555 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:19,555 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:25:19,556 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:25:19,556 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:19,556 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:25:19,556 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 12:25:19,563 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 12:25:19,564 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 12:25:19,568 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:19,578 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 12:25:19,582 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 12:25:19,582 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 12:25:19,583 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 12:25:19,584 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:19,585 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 12:25:19,586 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 12:25:19,586 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 12:25:19,587 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 12:25:19,588 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:19,589 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 12:25:19,590 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 12:25:19,590 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 12:25:19,590 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 12:25:19,591 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:19,591 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 12:25:19,597 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:19,600 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:19,600 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:19,601 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:19,602 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:19,607 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:24,323 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=True
2025-07-15 12:25:24,353 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:25:24,546 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:24,547 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:25:24,738 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:24,738 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:25:24,740 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-15 12:25:24,740 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-15 12:25:24,740 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-15 12:25:24,740 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:25:24,741 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 12:25:24,741 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 12:25:24,741 - models.predict - INFO - Using fallback price due to error: 86.3
2025-07-15 12:25:24,741 - models.predict - INFO - Prediction for 60 minutes horizon: 86.3
2025-07-15 12:25:24,748 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 12:25:30,119 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=True
2025-07-15 12:25:30,173 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:25:30,377 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:30,378 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:25:30,570 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:30,571 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:25:30,572 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-15 12:25:30,572 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-15 12:25:30,769 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:25:31,270 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-15 12:25:31,271 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4584633708000183
2025-07-15 12:25:31,271 - models.predict - INFO - Prediction for 60 minutes horizon: 89.23173709068544
2025-07-15 12:25:31,272 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 89.23 vs current 86.30 (3.4% change, limit: 2.0%). Applying correction.
2025-07-15 12:25:31,272 - app.pages.predictions_consolidated - INFO - Corrected prediction: 89.23 -> 87.51 (change: 1.4%)
2025-07-15 12:25:31,272 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 89.23 -> 87.51 for 60min
2025-07-15 12:25:31,273 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 12:25:36,358 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-15 12:25:36,419 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:25:36,641 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:36,641 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:25:36,831 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:36,831 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:25:36,833 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-15 12:25:36,833 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-15 12:25:36,834 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-15 12:25:36,834 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:25:36,854 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 12:25:36,854 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 12:25:36,854 - models.predict - INFO - Using fallback price due to error: 86.3
2025-07-15 12:25:36,854 - models.predict - INFO - Prediction for 60 minutes horizon: 86.3
2025-07-15 12:25:36,855 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 12:25:41,448 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-15 12:25:41,488 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:25:41,691 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:41,692 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:25:41,882 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:41,882 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:25:41,884 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-15 12:25:41,884 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-15 12:25:41,884 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-15 12:25:41,884 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 12:25:41,884 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 12:25:41,885 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 12:25:41,885 - models.predict - INFO - Using fallback price due to error: 86.3
2025-07-15 12:25:41,885 - models.predict - INFO - Prediction for 60 minutes horizon: 86.3
2025-07-15 12:25:41,887 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 12:25:46,738 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-15 12:25:46,769 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 12:25:46,976 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:46,976 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 12:25:47,179 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:47,179 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 12:25:47,181 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 12:25:47,271 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 12:25:47,271 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 12:25:47,271 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 12:25:47,272 - models.predict - INFO - Ensemble model already loaded
2025-07-15 12:25:47,299 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 12:25:47,299 - models.predict - INFO - Current price: 86.3, Predicted scaled value: 0.4147952911752378
2025-07-15 12:25:47,299 - models.predict - INFO - Prediction for 60 minutes horizon: 85.29112933637401
2025-07-15 12:25:47,346 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:47,346 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:25:47,562 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 12:25:47,562 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:25:47,563 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 12:26:01,661 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 12:26:02,028 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 12:26:02,388 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 12:26:02,402 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 12:26:02,403 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 12:26:02,403 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 12:26:02,404 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 12:26:02,813 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.43 seconds
2025-07-15 12:26:03,250 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:26:03,250 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 12:26:03,522 - app.utils.memory_management - INFO - Garbage collection: collected 5106 objects
2025-07-15 12:26:03,525 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 12:26:03,525 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:34:49,690 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:34:49,733 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 14:34:50,214 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 14:34:50,544 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 14:34:50,561 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 14:34:50,563 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 14:34:50,564 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 14:34:50,565 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 14:34:50,959 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.42 seconds
2025-07-15 14:34:51,332 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:34:51,332 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:34:52,235 - app.utils.memory_management - INFO - Garbage collection: collected 6106 objects
2025-07-15 14:34:52,236 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:34:52,237 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:34:53,412 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:34:53,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:34:53,451 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:34:53,718 - app.utils.memory_management - INFO - Garbage collection: collected 4310 objects
2025-07-15 14:34:53,719 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:34:53,720 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:34:55,811 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:34:55,839 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:34:55,840 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:34:56,063 - app.utils.memory_management - INFO - Garbage collection: collected 1317 objects
2025-07-15 14:34:56,064 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:34:56,065 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:34:58,758 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:34:58,799 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:34:58,799 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:34:59,010 - app.utils.memory_management - INFO - Garbage collection: collected 618 objects
2025-07-15 14:34:59,011 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:34:59,012 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:35:01,532 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:35:01,597 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:01,598 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:35:01,837 - app.utils.memory_management - INFO - Garbage collection: collected 911 objects
2025-07-15 14:35:01,839 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:01,840 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:35:02,644 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:35:02,980 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 14:35:03,262 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 14:35:03,278 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 14:35:03,279 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 14:35:03,279 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 14:35:03,281 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 14:35:03,560 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.30 seconds
2025-07-15 14:35:03,927 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:03,927 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:35:04,179 - app.utils.memory_management - INFO - Garbage collection: collected 3738 objects
2025-07-15 14:35:04,182 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:04,184 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:35:21,800 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:35:22,244 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 14:35:22,565 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 14:35:22,581 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 14:35:22,582 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 14:35:22,582 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 14:35:22,583 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 14:35:22,752 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:22,759 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:35:22,761 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:35:23,015 - app.utils.memory_management - INFO - Garbage collection: collected 4745 objects
2025-07-15 14:35:23,018 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:23,020 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:35:23,516 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 14:35:23,516 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 14:35:23,518 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 14:35:23,519 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 14:35:23,528 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 14:35:23,531 - app.utils.error_handling - INFO - live_trading_component executed in 0.51 seconds
2025-07-15 14:35:23,531 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:23,531 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:35:23,743 - app.utils.memory_management - INFO - Garbage collection: collected 4319 objects
2025-07-15 14:35:23,745 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:23,745 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:35:30,503 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:35:33,228 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 14:35:33,229 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 14:35:33,230 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 14:35:33,231 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 14:35:33,245 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 14:35:33,248 - app.utils.error_handling - INFO - live_trading_component executed in 2.71 seconds
2025-07-15 14:35:33,249 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:33,251 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:35:33,518 - app.utils.memory_management - INFO - Garbage collection: collected 1803 objects
2025-07-15 14:35:33,519 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:35:33,520 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:36:31,050 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:36:31,079 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:31,080 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:36:31,291 - app.utils.memory_management - INFO - Garbage collection: collected 1240 objects
2025-07-15 14:36:31,291 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:31,292 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:36:33,582 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:36:33,611 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:33,611 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:36:33,826 - app.utils.memory_management - INFO - Garbage collection: collected 669 objects
2025-07-15 14:36:33,827 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:33,829 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:36:35,530 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:36:35,895 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 14:36:36,181 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 14:36:36,195 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 14:36:36,196 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 14:36:36,196 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 14:36:36,196 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 14:36:36,479 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.30 seconds
2025-07-15 14:36:36,831 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:36,832 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:36:37,049 - app.utils.memory_management - INFO - Garbage collection: collected 3147 objects
2025-07-15 14:36:37,050 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:37,051 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:36:51,613 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:36:51,944 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 14:36:52,207 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 14:36:52,222 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 14:36:52,223 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 14:36:52,223 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 14:36:52,224 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 14:36:52,558 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.35 seconds
2025-07-15 14:36:52,980 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:52,982 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:36:53,209 - app.utils.memory_management - INFO - Garbage collection: collected 6431 objects
2025-07-15 14:36:53,209 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:53,210 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:36:53,646 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:36:53,788 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 14:36:53,788 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 14:36:53,788 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 14:36:53,788 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 14:36:53,794 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 14:36:53,796 - app.utils.error_handling - INFO - live_trading_component executed in 0.13 seconds
2025-07-15 14:36:53,796 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:53,796 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:36:54,048 - app.utils.memory_management - INFO - Garbage collection: collected 4320 objects
2025-07-15 14:36:54,051 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:36:54,053 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:36:58,939 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:37:24,631 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 14:37:24,633 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 14:37:24,634 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 14:37:24,634 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 14:37:24,641 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 14:37:24,644 - app.utils.error_handling - INFO - live_trading_component executed in 25.69 seconds
2025-07-15 14:37:24,645 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:24,645 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:37:24,870 - app.utils.memory_management - INFO - Garbage collection: collected 1809 objects
2025-07-15 14:37:24,871 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:24,871 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:37:32,938 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:37:32,995 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:32,995 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:37:33,203 - app.utils.memory_management - INFO - Garbage collection: collected 1258 objects
2025-07-15 14:37:33,204 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:33,204 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:37:37,481 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:37:37,532 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:37,532 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:37:37,735 - app.utils.memory_management - INFO - Garbage collection: collected 1155 objects
2025-07-15 14:37:37,736 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:37,737 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:37:38,336 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:37:38,441 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:38,442 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:37:38,641 - app.utils.memory_management - INFO - Garbage collection: collected 1054 objects
2025-07-15 14:37:38,641 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:38,642 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:37:46,914 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:37:47,010 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:47,011 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:37:47,213 - app.utils.memory_management - INFO - Garbage collection: collected 1517 objects
2025-07-15 14:37:47,214 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:47,214 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:37:49,011 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:37:49,066 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-07-15 14:37:49,085 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.392
2025-07-15 14:37:49,097 - app.utils.ai_ensemble_optimizer - WARNING - No saved ensemble configuration found
2025-07-15 14:37:49,119 - app.utils.session_state - ERROR - Error tracked: app_crash - AIEnsembleOptimizer.optimize_ensemble() got an unexpected keyword argument 'trials'
2025-07-15 14:37:49,119 - app - ERROR - Application crashed: AIEnsembleOptimizer.optimize_ensemble() got an unexpected keyword argument 'trials'
2025-07-15 14:37:49,120 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:49,120 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:37:49,307 - app.utils.memory_management - INFO - Garbage collection: collected 1397 objects
2025-07-15 14:37:49,308 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:49,309 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:37:49,309 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:49,309 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:37:49,514 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-15 14:37:49,515 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:37:49,515 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:41:53,761 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 14:41:53,795 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 14:41:53,796 - app - INFO - Memory management utilities loaded
2025-07-15 14:41:53,798 - app - INFO - Error handling utilities loaded
2025-07-15 14:41:53,799 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 14:41:53,799 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 14:41:53,800 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 14:41:53,800 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 14:42:22,024 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 14:42:24,342 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 14:42:24,343 - app - INFO - Memory management utilities loaded
2025-07-15 14:42:24,344 - app - INFO - Error handling utilities loaded
2025-07-15 14:42:24,346 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 14:42:24,347 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 14:42:24,347 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 14:42:24,347 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 14:42:24,348 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 14:42:24,349 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 14:42:24,349 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 14:42:24,349 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 14:42:24,349 - app - INFO - Applied NumPy fix
2025-07-15 14:42:24,350 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 14:42:24,350 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 14:42:24,350 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 14:42:24,350 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 14:42:24,350 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 14:42:24,350 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 14:42:24,350 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 14:42:24,350 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 14:42:37,629 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 14:42:37,634 - app - INFO - Applied TensorFlow fix
2025-07-15 14:42:37,655 - app.config - INFO - Configuration initialized
2025-07-15 14:42:37,677 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-15 14:42:37,798 - models.train - INFO - TensorFlow test successful
2025-07-15 14:42:37,889 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 14:42:37,889 - models.train - INFO - Transformer model is available
2025-07-15 14:42:37,890 - models.train - INFO - Using TensorFlow-based models
2025-07-15 14:42:37,892 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 14:42:37,893 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 14:42:37,902 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 14:42:39,039 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 14:42:39,040 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 14:42:39,040 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 14:42:39,040 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 14:42:39,040 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 14:42:39,040 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 14:42:39,041 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 14:42:39,041 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 14:42:39,041 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 14:42:39,041 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 14:42:39,320 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 14:42:39,323 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:42:40,036 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-15 14:42:41,456 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-15 14:42:41,456 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-15 14:42:41,792 - app.utils.session_state - INFO - Initializing session state
2025-07-15 14:42:41,794 - app.utils.session_state - INFO - Session state initialized
2025-07-15 14:42:42,474 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 14:42:42,488 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:42:42,488 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:42:42,720 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 14:42:42,721 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:42:42,723 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:42:53,759 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:42:53,777 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:42:53,777 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:42:53,981 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 14:42:53,982 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:42:53,983 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:42:55,474 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:42:55,538 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-15 14:42:55,539 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 14:42:55,539 - app - INFO - Data shape: (1250, 36)
2025-07-15 14:42:55,539 - app - INFO - File COMI contains 2025 data
2025-07-15 14:42:55,584 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-07-15 14:42:55,584 - app - INFO - Features shape: (1250, 36)
2025-07-15 14:42:55,607 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-15 14:42:55,608 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 14:42:55,608 - app - INFO - Data shape: (1250, 36)
2025-07-15 14:42:55,608 - app - INFO - File COMI contains 2025 data
2025-07-15 14:42:55,611 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:42:55,611 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:42:55,806 - app.utils.memory_management - INFO - Garbage collection: collected 730 objects
2025-07-15 14:42:55,806 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:42:55,806 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:42:56,198 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:42:56,262 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:42:56,263 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:42:56,480 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-15 14:42:56,480 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:42:56,480 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:43:03,648 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:43:03,757 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-07-15 14:43:03,763 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.371
2025-07-15 14:43:03,777 - app.utils.ai_ensemble_optimizer - WARNING - No saved ensemble configuration found
2025-07-15 14:43:03,778 - app.utils.ai_ensemble_optimizer - INFO - Starting ensemble optimization with 50 trials
2025-07-15 14:43:03,783 - app.utils.ai_ensemble_optimizer - INFO - Training random_forest
2025-07-15 14:43:04,063 - app.utils.ai_ensemble_optimizer - INFO - Successfully trained random_forest
2025-07-15 14:43:04,063 - app.utils.ai_ensemble_optimizer - INFO - Training gradient_boosting
2025-07-15 14:43:04,235 - app.utils.ai_ensemble_optimizer - INFO - Successfully trained gradient_boosting
2025-07-15 14:43:04,235 - app.utils.ai_ensemble_optimizer - INFO - Training ridge_regression
2025-07-15 14:43:04,268 - app.utils.ai_ensemble_optimizer - INFO - Successfully trained ridge_regression
2025-07-15 14:43:04,268 - app.utils.ai_ensemble_optimizer - INFO - Training elastic_net
2025-07-15 14:43:04,274 - app.utils.ai_ensemble_optimizer - INFO - Successfully trained elastic_net
2025-07-15 14:43:04,274 - app.utils.ai_ensemble_optimizer - INFO - Training svr
2025-07-15 14:43:04,279 - app.utils.ai_ensemble_optimizer - INFO - Successfully trained svr
2025-07-15 14:43:04,279 - app.utils.ai_ensemble_optimizer - INFO - Training neural_network
2025-07-15 14:43:04,500 - app.utils.ai_ensemble_optimizer - INFO - Successfully trained neural_network
2025-07-15 14:43:06,980 - app.utils.ai_ensemble_optimizer - INFO - Optimization completed. Best MSE: 0.027431
2025-07-15 14:43:07,103 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration saved to models/ensemble/ensemble_config.joblib
2025-07-15 14:43:07,103 - app.utils.ai_ensemble_optimizer - INFO - Ensemble optimization completed successfully
2025-07-15 14:43:07,156 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-15 14:43:07,157 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-07-15 14:43:07,157 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-15 14:43:07,157 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: MEDIUM (48.1)
2025-07-15 14:43:07,158 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-15 14:43:40,256 - app.utils.ai_pattern_recognition - INFO - Using CSV price 49.90 EGP for ABUK
2025-07-15 14:43:40,256 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for ABUK
2025-07-15 14:43:40,259 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-15 14:43:40,292 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-07-15 14:43:40,292 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 49.9 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 49.87441178158255, 'bearish_score': 0}
2025-07-15 14:43:40,292 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-07-15 14:43:40,674 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:43:40,675 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:43:40,903 - app.utils.memory_management - INFO - Garbage collection: collected 1771 objects
2025-07-15 14:43:40,904 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:43:40,904 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:43:53,468 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:43:53,518 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-07-15 14:43:53,529 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.331
2025-07-15 14:43:53,593 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-15 14:43:53,649 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-15 14:43:53,650 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-07-15 14:43:53,650 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-15 14:43:53,651 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: MEDIUM (48.1)
2025-07-15 14:43:53,651 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-15 14:43:54,659 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:43:54,762 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:43:54,763 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:43:55,002 - app.utils.memory_management - INFO - Garbage collection: collected 1781 objects
2025-07-15 14:43:55,003 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:43:55,003 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:44:00,692 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:44:00,715 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:44:00,716 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:44:00,922 - app.utils.memory_management - INFO - Garbage collection: collected 702 objects
2025-07-15 14:44:00,922 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:44:00,924 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:44:03,252 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:44:03,279 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-07-15 14:44:03,336 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-11-05 to 2025-07-14
2025-07-15 14:44:03,336 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-07-15 14:44:25,574 - app.utils.ai_pattern_recognition - INFO - Using CSV price 49.90 EGP for ABUK
2025-07-15 14:44:25,575 - app.utils.ai_pattern_recognition - INFO - Using calculated pivot levels for ABUK
2025-07-15 14:44:25,577 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-15 14:44:25,579 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:44:25,579 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:44:25,764 - app.utils.memory_management - INFO - Garbage collection: collected 699 objects
2025-07-15 14:44:25,764 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:44:25,765 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:44:34,491 - app.pages.smc_analysis - INFO - ⚠️ API not available, using CSV price: 87.00 EGP for COMI
2025-07-15 14:44:35,464 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:44:35,465 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:44:35,671 - app.utils.memory_management - INFO - Garbage collection: collected 763 objects
2025-07-15 14:44:35,672 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:44:35,672 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:45:17,868 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:45:17,926 - app.components.performance_metrics - INFO - Found price 85.19 for COMI at 2025-07-07 00:00:00 (within 13.9 hours of target)
2025-07-15 14:45:17,943 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 29.5 hours
2025-07-15 14:45:17,965 - app.components.performance_metrics - INFO - Found price 87.53 for COMI at 2025-07-11 00:00:00 (within 5.5 hours of target)
2025-07-15 14:45:17,990 - app.components.performance_metrics - INFO - Found price 87.53 for COMI at 2025-07-11 00:00:00 (within 5.5 hours of target)
2025-07-15 14:45:17,995 - app.components.performance_metrics - INFO - Target time 2025-06-14 19:28:26.809508 is in the future compared to latest data 2025-06-13 00:00:00
2025-07-15 14:45:17,999 - app.components.performance_metrics - INFO - Target time 2025-06-14 19:29:49.739566 is in the future compared to latest data 2025-06-13 00:00:00
2025-07-15 14:45:18,078 - app.components.performance_metrics - INFO - Updated performance metrics for 9 model-symbol combinations
2025-07-15 14:45:18,532 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:18,532 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:45:18,754 - app.utils.memory_management - INFO - Garbage collection: collected 3809 objects
2025-07-15 14:45:18,755 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:18,755 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:45:27,798 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:45:27,857 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 29.5 hours
2025-07-15 14:45:27,861 - app.components.performance_metrics - INFO - Target time 2025-06-14 19:28:26.809508 is in the future compared to latest data 2025-06-13 00:00:00
2025-07-15 14:45:27,865 - app.components.performance_metrics - INFO - Target time 2025-06-14 19:29:49.739566 is in the future compared to latest data 2025-06-13 00:00:00
2025-07-15 14:45:28,239 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:28,243 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:45:28,537 - app.utils.memory_management - INFO - Garbage collection: collected 2274 objects
2025-07-15 14:45:28,539 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:28,539 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:45:28,950 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:45:28,972 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-07-15 14:45:28,973 - app.utils.backtesting - INFO - Historical data shape: (1250, 36)
2025-07-15 14:45:28,973 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-15 14:45:28,974 - app.utils.backtesting - INFO - Historical data date range: 2020-09-29 00:00:00 to 2025-07-14 00:00:00
2025-07-15 14:45:29,145 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:29,146 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:45:29,374 - app.utils.memory_management - INFO - Garbage collection: collected 763 objects
2025-07-15 14:45:29,375 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:29,376 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:45:36,677 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:45:36,697 - app.utils.backtesting - INFO - Starting backtesting component for COMI
2025-07-15 14:45:36,698 - app.utils.backtesting - INFO - Historical data shape: (1250, 36)
2025-07-15 14:45:36,698 - app.utils.backtesting - INFO - Historical data columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-15 14:45:36,700 - app.utils.backtesting - INFO - Historical data date range: 2020-09-29 00:00:00 to 2025-07-14 00:00:00
2025-07-15 14:45:36,733 - models.hybrid_model - INFO - XGBoost is available
2025-07-15 14:45:36,734 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-15 14:45:36,736 - app.utils.backtesting - INFO - Created model instance of type rf
2025-07-15 14:45:36,738 - app.utils.backtesting - INFO - Loading model for COMI with horizon 5
2025-07-15 14:45:36,738 - app.utils.backtesting - INFO - Looking in path: saved_models
2025-07-15 14:45:36,744 - app.utils.backtesting - INFO - Found matching model files: ['COMI_arima_ml_params_5min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_rf_15min.joblib', 'COMI_xgb_15min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_prophet_5min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_svr_15min.joblib', 'COMI_gb_15min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_gb_5min.joblib', 'COMI_lr_5min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_lr_15min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_svr_5min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_prophet_15min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_5min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_lstm_scaler5min.joblib']
2025-07-15 14:45:36,747 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_5min.joblib or saved_models/COMI_rf_5min.pkl
2025-07-15 14:45:36,749 - app.utils.backtesting - INFO - Model loaded successfully: NoneType
2025-07-15 14:45:36,751 - app.utils.backtesting - INFO - Starting backtest with test period: 30 days
2025-07-15 14:45:36,753 - app.utils.backtesting - INFO - Preparing features for backtesting...
2025-07-15 14:45:36,776 - app.utils.backtesting - INFO - Added technical indicators. New shape: (1250, 36)
2025-07-15 14:45:36,803 - app.utils.backtesting - INFO - Prepared features. Final shape: (1250, 36)
2025-07-15 14:45:36,805 - app.utils.backtesting - INFO - Starting backtesting with 30 days and 5 features
2025-07-15 14:45:36,805 - app.utils.backtesting - INFO - Model type detection: sklearn=False, hybrid=False
2025-07-15 14:45:36,808 - app.utils.backtesting - WARNING - 3D reshape failed for day 0, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,808 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 0: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,810 - app.utils.backtesting - WARNING - 3D reshape failed for day 1, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,811 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 1: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,813 - app.utils.backtesting - WARNING - 3D reshape failed for day 2, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,813 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 2: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,814 - app.utils.backtesting - WARNING - 3D reshape failed for day 3, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,815 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 3: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,817 - app.utils.backtesting - WARNING - 3D reshape failed for day 4, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,818 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 4: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,820 - app.utils.backtesting - WARNING - 3D reshape failed for day 5, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,821 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 5: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,827 - app.utils.backtesting - WARNING - 3D reshape failed for day 6, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,827 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 6: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,829 - app.utils.backtesting - WARNING - 3D reshape failed for day 7, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,830 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 7: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,832 - app.utils.backtesting - WARNING - 3D reshape failed for day 8, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,832 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 8: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,835 - app.utils.backtesting - WARNING - 3D reshape failed for day 9, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,835 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 9: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,841 - app.utils.backtesting - WARNING - 3D reshape failed for day 10, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,841 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 10: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,843 - app.utils.backtesting - WARNING - 3D reshape failed for day 11, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,845 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 11: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,847 - app.utils.backtesting - WARNING - 3D reshape failed for day 12, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,847 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 12: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,850 - app.utils.backtesting - WARNING - 3D reshape failed for day 13, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,850 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 13: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,853 - app.utils.backtesting - WARNING - 3D reshape failed for day 14, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,854 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 14: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,856 - app.utils.backtesting - WARNING - 3D reshape failed for day 15, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,856 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 15: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,858 - app.utils.backtesting - WARNING - 3D reshape failed for day 16, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,858 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 16: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,862 - app.utils.backtesting - WARNING - 3D reshape failed for day 17, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,862 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 17: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,864 - app.utils.backtesting - WARNING - 3D reshape failed for day 18, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,864 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 18: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,867 - app.utils.backtesting - WARNING - 3D reshape failed for day 19, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,867 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 19: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,869 - app.utils.backtesting - WARNING - 3D reshape failed for day 20, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,869 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 20: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,871 - app.utils.backtesting - WARNING - 3D reshape failed for day 21, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,872 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 21: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,874 - app.utils.backtesting - WARNING - 3D reshape failed for day 22, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,876 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 22: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,878 - app.utils.backtesting - WARNING - 3D reshape failed for day 23, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,878 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 23: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,880 - app.utils.backtesting - WARNING - 3D reshape failed for day 24, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,881 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 24: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,884 - app.utils.backtesting - WARNING - 3D reshape failed for day 25, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,884 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 25: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,887 - app.utils.backtesting - WARNING - 3D reshape failed for day 26, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,887 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 26: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,891 - app.utils.backtesting - WARNING - 3D reshape failed for day 27, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,891 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 27: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,894 - app.utils.backtesting - WARNING - 3D reshape failed for day 28, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,895 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 28: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,898 - app.utils.backtesting - WARNING - 3D reshape failed for day 29, trying 2D: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,898 - app.utils.backtesting - ERROR - Both 3D and 2D predictions failed for day 29: 'NoneType' object has no attribute 'predict'
2025-07-15 14:45:36,901 - app.utils.backtesting - ERROR - No valid predictions were made during backtesting
2025-07-15 14:45:36,902 - app.utils.backtesting - WARNING - Created 5 dummy results for visualization purposes
2025-07-15 14:45:36,903 - app.utils.backtesting - INFO - Backtest completed with 5 results
2025-07-15 14:45:36,956 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:36,956 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:45:37,178 - app.utils.memory_management - INFO - Garbage collection: collected 1178 objects
2025-07-15 14:45:37,178 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:37,179 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:45:43,968 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:45:44,015 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:44,016 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:45:44,229 - app.utils.memory_management - INFO - Garbage collection: collected 1189 objects
2025-07-15 14:45:44,230 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:44,230 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:45:49,151 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:45:49,190 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:49,190 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:45:49,411 - app.utils.memory_management - INFO - Garbage collection: collected 753 objects
2025-07-15 14:45:49,411 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:49,412 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:45:52,243 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:45:52,291 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:52,292 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:45:52,490 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-15 14:45:52,490 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:52,491 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:45:58,489 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:45:58,534 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:58,535 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:45:58,736 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-15 14:45:58,737 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:45:58,738 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:46:03,827 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:46:10,773 - app.pages.advanced_technical_analysis - INFO - Loaded 781 days of historical data for COMI
2025-07-15 14:46:10,854 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:10,855 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:46:11,058 - app.utils.memory_management - INFO - Garbage collection: collected 733 objects
2025-07-15 14:46:11,059 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:11,059 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:46:26,235 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:46:26,498 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:26,499 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:46:26,690 - app.utils.memory_management - INFO - Garbage collection: collected 2775 objects
2025-07-15 14:46:26,692 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:26,692 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:46:33,058 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:46:33,127 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 14:46:33,171 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-15 14:46:33,365 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:33,367 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-15 14:46:33,588 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:33,589 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 14:46:33,593 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-15 14:46:33,790 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-15 14:46:33,790 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-15 14:46:33,790 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-15 14:46:33,790 - models.predict - INFO - Ensemble model already loaded
2025-07-15 14:46:33,844 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 14:46:33,845 - models.predict - INFO - Current price: 87.0, Predicted scaled value: 0.4236584450257059
2025-07-15 14:46:33,845 - models.predict - INFO - Prediction for 30 minutes horizon: 86.09094039037306
2025-07-15 14:46:33,851 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 14:46:33,879 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 14:46:34,085 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:34,087 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 14:46:34,283 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:34,283 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 14:46:34,285 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 14:46:34,366 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 14:46:34,366 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 14:46:34,366 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 14:46:34,366 - models.predict - INFO - Ensemble model already loaded
2025-07-15 14:46:34,393 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 14:46:34,394 - models.predict - INFO - Current price: 87.0, Predicted scaled value: 0.4236584450257059
2025-07-15 14:46:34,394 - models.predict - INFO - Prediction for 60 minutes horizon: 86.09094039037306
2025-07-15 14:46:34,400 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-15 14:46:34,403 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-15 14:46:34,406 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-15 14:46:34,409 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-15 14:46:34,412 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-15 14:46:34,416 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-15 14:46:34,419 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-15 14:46:34,422 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-15 14:46:34,424 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-15 14:46:34,429 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-15 14:46:34,434 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-15 14:46:34,437 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-15 14:46:34,440 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-15 14:46:34,443 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-15 14:46:34,446 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-15 14:46:34,449 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-15 14:46:34,452 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-15 14:46:34,455 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-15 14:46:34,457 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 14:46:34,481 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-15 14:46:34,676 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:34,677 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-15 14:46:34,874 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:34,875 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 14:46:34,876 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-15 14:46:34,953 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-15 14:46:34,953 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-15 14:46:34,953 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-15 14:46:34,953 - models.predict - INFO - Ensemble model already loaded
2025-07-15 14:46:34,981 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 14:46:34,981 - models.predict - INFO - Current price: 87.0, Predicted scaled value: 0.4236584450257059
2025-07-15 14:46:34,982 - models.predict - INFO - Prediction for 1440 minutes horizon: 86.09094039037306
2025-07-15 14:46:35,198 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:35,198 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:46:35,392 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-15 14:46:35,393 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:35,394 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:46:52,608 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:46:52,641 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:52,642 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:46:52,849 - app.utils.memory_management - INFO - Garbage collection: collected 1412 objects
2025-07-15 14:46:52,850 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:52,851 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:46:55,585 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:46:55,622 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:55,622 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:46:55,821 - app.utils.memory_management - INFO - Garbage collection: collected 822 objects
2025-07-15 14:46:55,822 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:55,822 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:46:59,006 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:46:59,068 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 14:46:59,112 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-15 14:46:59,114 - app.utils.common - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 14:46:59,115 - app.utils.common - INFO - Data shape: (1250, 36)
2025-07-15 14:46:59,116 - app.utils.common - INFO - File COMI contains 2025 data
2025-07-15 14:46:59,173 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:59,173 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:46:59,366 - app.utils.memory_management - INFO - Garbage collection: collected 792 objects
2025-07-15 14:46:59,368 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:46:59,368 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:47:11,586 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:47:11,653 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 14:47:11,697 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-15 14:47:11,698 - app.utils.common - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 14:47:11,699 - app.utils.common - INFO - Data shape: (1250, 36)
2025-07-15 14:47:11,699 - app.utils.common - INFO - File COMI contains 2025 data
2025-07-15 14:47:11,735 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:47:11,735 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:47:11,952 - app.utils.memory_management - INFO - Garbage collection: collected 2724 objects
2025-07-15 14:47:11,952 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:47:11,953 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:47:13,642 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:47:13,703 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:47:13,703 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:47:13,904 - app.utils.memory_management - INFO - Garbage collection: collected 3048 objects
2025-07-15 14:47:13,905 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:47:13,906 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:47:30,774 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:47:30,830 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:47:30,830 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:47:31,041 - app.utils.memory_management - INFO - Garbage collection: collected 1233 objects
2025-07-15 14:47:31,041 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:47:31,042 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 14:47:31,635 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 14:47:31,972 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 14:47:32,235 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 14:47:32,247 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 14:47:32,248 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 14:47:32,248 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 14:47:32,249 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 14:47:32,503 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.27 seconds
2025-07-15 14:47:32,910 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:47:32,910 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 14:47:33,196 - app.utils.memory_management - INFO - Garbage collection: collected 3511 objects
2025-07-15 14:47:33,199 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 14:47:33,199 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:30:38,769 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:30:38,793 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 16:30:39,194 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 16:30:39,509 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 16:30:39,525 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 16:30:39,525 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 16:30:39,526 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 16:30:39,526 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 16:30:39,805 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.30 seconds
2025-07-15 16:30:40,210 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:40,211 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:30:40,449 - app.utils.memory_management - INFO - Garbage collection: collected 6337 objects
2025-07-15 16:30:40,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:40,451 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:30:41,367 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:30:41,395 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:41,395 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:30:41,650 - app.utils.memory_management - INFO - Garbage collection: collected 4310 objects
2025-07-15 16:30:41,652 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:41,652 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:30:46,275 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:30:46,314 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:46,314 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:30:46,533 - app.utils.memory_management - INFO - Garbage collection: collected 1317 objects
2025-07-15 16:30:46,534 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:46,535 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:30:47,371 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:30:47,423 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-15 16:30:47,440 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-15 16:30:47,510 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:47,511 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:30:47,731 - app.utils.memory_management - INFO - Garbage collection: collected 618 objects
2025-07-15 16:30:47,731 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:47,732 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:30:52,449 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:30:52,470 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-15 16:30:52,476 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-15 16:30:52,610 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:52,611 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:30:52,812 - app.utils.memory_management - INFO - Garbage collection: collected 1569 objects
2025-07-15 16:30:52,814 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:52,814 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:30:53,294 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:30:53,327 - app.utils.state_manager - INFO - Found 14 stock files in data/stocks
2025-07-15 16:30:54,458 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:54,459 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:30:54,673 - app.utils.memory_management - INFO - Garbage collection: collected 1779 objects
2025-07-15 16:30:54,674 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:54,675 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:30:57,612 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:30:58,098 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:58,098 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:30:58,324 - app.utils.memory_management - INFO - Garbage collection: collected 1649 objects
2025-07-15 16:30:58,325 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:58,326 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:30:58,374 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:30:58,814 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-07-15 16:30:58,832 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 16:30:58,834 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 16:30:58,835 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 16:30:58,835 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 16:30:58,845 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 16:30:58,847 - app.utils.error_handling - INFO - live_trading_component executed in 0.05 seconds
2025-07-15 16:30:58,849 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:58,849 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:30:59,069 - app.utils.memory_management - INFO - Garbage collection: collected 1531 objects
2025-07-15 16:30:59,070 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:30:59,071 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:31:02,353 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:31:43,921 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 16:31:43,932 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 16:31:43,933 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 16:31:43,933 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 16:31:43,949 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 16:31:43,954 - app.utils.error_handling - INFO - live_trading_component executed in 41.57 seconds
2025-07-15 16:31:43,955 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:31:43,956 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:31:44,270 - app.utils.memory_management - INFO - Garbage collection: collected 1144 objects
2025-07-15 16:31:44,271 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:31:44,271 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:32:41,441 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:32:41,453 - app.utils.session_state - INFO - Initializing session state
2025-07-15 16:32:41,455 - app.utils.session_state - INFO - Session state initialized
2025-07-15 16:32:41,487 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:32:41,487 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:32:41,686 - app.utils.memory_management - INFO - Garbage collection: collected 1240 objects
2025-07-15 16:32:41,687 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:32:41,688 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:32:46,412 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:32:46,430 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 16:32:46,447 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 16:32:46,448 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 16:32:46,448 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 16:32:46,449 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 16:32:46,484 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-07-15 16:32:46,509 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-15 16:32:46,513 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-15 16:32:46,521 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-15 16:32:46,524 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-15 16:32:46,533 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-15 16:32:46,536 - app.utils.error_handling - INFO - live_trading_component executed in 0.11 seconds
2025-07-15 16:32:46,536 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:32:46,537 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:32:46,767 - app.utils.memory_management - INFO - Garbage collection: collected 866 objects
2025-07-15 16:32:46,767 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:32:46,768 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:32:59,806 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:32:59,867 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-15 16:32:59,869 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-15 16:32:59,872 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-15 16:32:59,875 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-15 16:32:59,887 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-15 16:32:59,893 - app.utils.error_handling - INFO - live_trading_component executed in 0.07 seconds
2025-07-15 16:32:59,893 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:32:59,894 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:33:00,147 - app.utils.memory_management - INFO - Garbage collection: collected 1150 objects
2025-07-15 16:33:00,148 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:33:00,150 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:33:02,855 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:33:44,730 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-15 16:33:44,740 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-15 16:33:44,745 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-15 16:33:44,749 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-15 16:33:44,763 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-15 16:33:44,767 - app.utils.error_handling - INFO - live_trading_component executed in 41.89 seconds
2025-07-15 16:33:44,767 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:33:44,767 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:33:45,014 - app.utils.memory_management - INFO - Garbage collection: collected 1149 objects
2025-07-15 16:33:45,015 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:33:45,016 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:33:57,446 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:33:57,522 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-15 16:33:57,523 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 16:33:57,523 - app - INFO - Data shape: (1250, 36)
2025-07-15 16:33:57,523 - app - INFO - File COMI contains 2025 data
2025-07-15 16:33:57,556 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-07-15 16:33:57,557 - app - INFO - Features shape: (1250, 36)
2025-07-15 16:33:57,581 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-15 16:33:57,581 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 16:33:57,582 - app - INFO - Data shape: (1250, 36)
2025-07-15 16:33:57,582 - app - INFO - File COMI contains 2025 data
2025-07-15 16:33:57,584 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:33:57,585 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:33:57,797 - app.utils.memory_management - INFO - Garbage collection: collected 1254 objects
2025-07-15 16:33:57,798 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:33:57,799 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:33:58,288 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:33:58,367 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 16:33:58,368 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 16:33:58,368 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 16:33:58,368 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 16:33:58,377 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 16:33:58,402 - app.utils.error_handling - INFO - live_trading_component executed in 0.08 seconds
2025-07-15 16:33:58,405 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:33:58,406 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:33:58,652 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-15 16:33:58,653 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:33:58,653 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:34:03,784 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:34:34,645 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 16:34:34,652 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 16:34:34,652 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 16:34:34,653 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 16:34:34,667 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 16:34:34,671 - app.utils.error_handling - INFO - live_trading_component executed in 30.86 seconds
2025-07-15 16:34:34,671 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:34:34,671 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:34:35,066 - app.utils.memory_management - INFO - Garbage collection: collected 1077 objects
2025-07-15 16:34:35,066 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:34:35,067 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:49:22,966 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:49:23,014 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 16:49:23,029 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:49:23,030 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:49:23,776 - app.utils.memory_management - INFO - Garbage collection: collected 1259 objects
2025-07-15 16:49:23,777 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:49:23,778 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:49:31,265 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:49:31,300 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:49:31,301 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:49:31,519 - app.utils.memory_management - INFO - Garbage collection: collected 782 objects
2025-07-15 16:49:31,519 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:49:31,520 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:49:48,368 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:49:48,439 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 16:49:48,526 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.05 seconds
2025-07-15 16:49:48,527 - app.utils.common - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 16:49:48,528 - app.utils.common - INFO - Data shape: (1250, 36)
2025-07-15 16:49:48,528 - app.utils.common - INFO - File COMI contains 2025 data
2025-07-15 16:49:48,582 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:49:48,582 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:49:48,787 - app.utils.memory_management - INFO - Garbage collection: collected 776 objects
2025-07-15 16:49:48,790 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:49:48,790 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:51:39,724 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:51:39,783 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 16:51:39,836 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-15 16:51:39,837 - app.utils.common - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 16:51:39,837 - app.utils.common - INFO - Data shape: (1250, 36)
2025-07-15 16:51:39,838 - app.utils.common - INFO - File COMI contains 2025 data
2025-07-15 16:51:39,882 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:51:39,882 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:51:40,113 - app.utils.memory_management - INFO - Garbage collection: collected 2775 objects
2025-07-15 16:51:40,115 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:51:40,115 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:51:40,862 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:51:40,918 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-07-15 16:51:40,927 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.392
2025-07-15 16:51:41,073 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-15 16:51:41,154 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-15 16:51:41,155 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-07-15 16:51:41,155 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-15 16:51:41,155 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (53.8)
2025-07-15 16:51:41,156 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-15 16:51:54,479 - app.utils.ai_pattern_recognition - INFO - Using live price 50.00 EGP from API for ABUK
2025-07-15 16:51:54,480 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-07-15 16:51:54,480 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-07-15 16:51:54,481 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-07-15 16:51:54,484 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-15 16:51:54,514 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-07-15 16:51:54,515 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 50.7 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 50.69684046487143, 'bearish_score': 0}
2025-07-15 16:51:54,515 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-07-15 16:51:54,607 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:51:54,607 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:51:54,810 - app.utils.memory_management - INFO - Garbage collection: collected 3825 objects
2025-07-15 16:51:54,811 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:51:54,812 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 16:52:17,682 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 16:52:17,774 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-07-15 16:52:17,806 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: 0.132
2025-07-15 16:52:17,887 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-07-15 16:52:17,987 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-07-15 16:52:17,990 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.97%) exceeds limit (15.00%)
2025-07-15 16:52:17,990 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-07-15 16:52:17,991 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (54.5)
2025-07-15 16:52:17,991 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-07-15 16:52:26,173 - app.utils.ai_pattern_recognition - INFO - Using live price 86.30 EGP from API for COMI
2025-07-15 16:52:26,180 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-07-15 16:52:26,180 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-07-15 16:52:26,184 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-07-15 16:52:26,263 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='BUY', volatility_factor=0.050
2025-07-15 16:52:26,263 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'BUY', 'confidence': 0.5, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 33.5 vs 22.5)', 'risk_level': 'Low', 'bullish_score': 33.5, 'bearish_score': 22.5}
2025-07-15 16:52:26,264 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=86.56, stop=82.24, target=97.39
2025-07-15 16:52:26,403 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:52:26,404 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 16:52:26,624 - app.utils.memory_management - INFO - Garbage collection: collected 2837 objects
2025-07-15 16:52:26,625 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 16:52:26,626 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 20:59:37,122 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 20:59:37,191 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 20:59:37,427 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 20:59:37,431 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 20:59:37,431 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 20:59:37,431 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 20:59:37,439 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 20:59:37,444 - app.utils.error_handling - INFO - live_trading_component executed in 0.23 seconds
2025-07-15 20:59:37,465 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 20:59:37,466 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 20:59:38,920 - app.utils.memory_management - INFO - Garbage collection: collected 1747 objects
2025-07-15 20:59:38,921 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 20:59:38,922 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 20:59:44,425 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:00:18,039 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:00:18,040 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:00:18,040 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:00:18,040 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:00:18,044 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:00:18,047 - app.utils.error_handling - INFO - live_trading_component executed in 33.60 seconds
2025-07-15 21:00:18,048 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:00:18,050 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:00:18,294 - app.utils.memory_management - INFO - Garbage collection: collected 1158 objects
2025-07-15 21:00:18,294 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:00:18,295 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:02:20,305 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:02:20,314 - app.utils.session_state - INFO - Initializing session state
2025-07-15 21:02:20,316 - app.utils.session_state - INFO - Session state initialized
2025-07-15 21:02:20,354 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:20,355 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:02:20,580 - app.utils.memory_management - INFO - Garbage collection: collected 1240 objects
2025-07-15 21:02:20,580 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:20,580 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:02:24,669 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:02:24,704 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 21:02:24,712 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 21:02:24,713 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 21:02:24,713 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 21:02:24,713 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 21:02:24,728 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-07-15 21:02:24,755 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-15 21:02:24,758 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-15 21:02:24,765 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-15 21:02:24,769 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-15 21:02:24,783 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-15 21:02:24,786 - app.utils.error_handling - INFO - live_trading_component executed in 0.08 seconds
2025-07-15 21:02:24,787 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:24,788 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:02:25,044 - app.utils.memory_management - INFO - Garbage collection: collected 866 objects
2025-07-15 21:02:25,046 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:25,046 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:02:35,071 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:02:35,137 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using rf
2025-07-15 21:02:35,141 - app.utils.data_processing - INFO - No model found for SUGR with 15 minutes horizon using rf
2025-07-15 21:02:35,146 - app.utils.data_processing - INFO - No model found for SUGR with 30 minutes horizon using rf
2025-07-15 21:02:35,151 - app.utils.data_processing - INFO - No model found for SUGR with 60 minutes horizon using rf
2025-07-15 21:02:35,167 - app.utils.data_processing - INFO - No model found for SUGR with 5 minutes horizon using lstm
2025-07-15 21:02:35,170 - app.utils.error_handling - INFO - live_trading_component executed in 0.08 seconds
2025-07-15 21:02:35,170 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:35,170 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:02:35,454 - app.utils.memory_management - INFO - Garbage collection: collected 1150 objects
2025-07-15 21:02:35,454 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:35,456 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:02:36,437 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:02:36,498 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-15 21:02:36,499 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 21:02:36,499 - app - INFO - Data shape: (1250, 36)
2025-07-15 21:02:36,499 - app - INFO - File COMI contains 2025 data
2025-07-15 21:02:36,557 - app - INFO - Feature engineering for COMI completed in 0.06 seconds
2025-07-15 21:02:36,558 - app - INFO - Features shape: (1250, 36)
2025-07-15 21:02:36,587 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-15 21:02:36,587 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 21:02:36,588 - app - INFO - Data shape: (1250, 36)
2025-07-15 21:02:36,588 - app - INFO - File COMI contains 2025 data
2025-07-15 21:02:36,594 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:36,594 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:02:36,792 - app.utils.memory_management - INFO - Garbage collection: collected 1149 objects
2025-07-15 21:02:36,793 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:36,794 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:02:37,238 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:02:37,317 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:02:37,317 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:02:37,318 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:02:37,340 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:02:37,350 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:02:37,355 - app.utils.error_handling - INFO - live_trading_component executed in 0.09 seconds
2025-07-15 21:02:37,355 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:37,355 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:02:37,573 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-15 21:02:37,575 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:02:37,576 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:02:41,651 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:03:23,697 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:03:23,698 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:03:23,699 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:03:23,699 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:03:23,713 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:03:23,716 - app.utils.error_handling - INFO - live_trading_component executed in 42.04 seconds
2025-07-15 21:03:23,717 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:03:23,717 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:03:23,933 - app.utils.memory_management - INFO - Garbage collection: collected 1051 objects
2025-07-15 21:03:23,933 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:03:23,934 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:03:42,504 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:03:42,854 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 21:03:43,118 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 21:03:43,131 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.00 seconds
2025-07-15 21:03:43,132 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 21:03:43,132 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 21:03:43,133 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 21:03:43,380 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.26 seconds
2025-07-15 21:03:43,748 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:03:43,749 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:03:43,988 - app.utils.memory_management - INFO - Garbage collection: collected 3746 objects
2025-07-15 21:03:43,989 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:03:43,990 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:04:49,481 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:04:49,507 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 21:04:49,874 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-15 21:04:50,152 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-15 21:04:50,171 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-15 21:04:50,172 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-15 21:04:50,173 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-15 21:04:50,173 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-15 21:04:50,484 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.33 seconds
2025-07-15 21:04:50,977 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:04:50,977 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:04:51,380 - app.utils.memory_management - INFO - Garbage collection: collected 6204 objects
2025-07-15 21:04:51,381 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:04:51,383 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:04:55,565 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:04:55,609 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 21:04:55,610 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:04:55,610 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:04:55,610 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:04:55,610 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:04:55,611 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 21:04:55,612 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-15 21:04:55,617 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:04:55,617 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:04:55,627 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:04:55,632 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:04:55,641 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-15 21:04:55,653 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 21:04:55,654 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:04:55,655 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:04:55,656 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:04:55,657 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:04:55,658 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-15 21:04:55,665 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 21:04:55,666 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:04:55,666 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:04:55,666 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:04:55,667 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:04:55,667 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-15 21:04:55,694 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 21:04:55,695 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:04:55,695 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:04:55,695 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:04:55,696 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:04:55,696 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 21:04:55,718 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:04:55,721 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:04:55,723 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:04:55,734 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:04:55,734 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:04:55,748 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:04:55,749 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:04:55,749 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:04:55,750 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:04:55,750 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:04:55,750 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:04:55,753 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:04:55,753 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:04:55,756 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:04:55,758 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:04:55,759 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 21:04:55,760 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:04:55,760 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:04:55,761 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:04:55,761 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:04:55,762 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 21:04:55,763 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:04:55,765 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:04:55,766 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:04:55,766 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:04:55,767 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:04:55,768 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:04:55,768 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:04:55,769 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:04:55,770 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:04:55,779 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:04:55,784 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:04:55,785 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:04:55,785 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:04:55,785 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:04:55,795 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:04:55,796 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:04:56,008 - app.utils.memory_management - INFO - Garbage collection: collected 4310 objects
2025-07-15 21:04:56,009 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:04:56,009 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:05:03,466 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:05:03,516 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:03,517 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:03,517 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:05:03,525 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:05:03,525 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:05:03,540 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:05:03,540 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:05:03,541 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:05:03,541 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:03,545 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:05:03,545 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:05:03,549 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:05:03,549 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:05:03,557 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:05:03,617 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:05:03,617 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 21:05:03,617 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:05:03,617 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:05:03,617 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:05:03,618 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:05:03,618 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 21:05:03,618 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:05:03,618 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:05:03,618 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:05:03,621 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:05:03,621 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:05:03,621 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:05:03,622 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:05:03,622 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:03,622 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:05:03,631 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:05:03,636 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:05:03,636 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:05:03,636 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:05:03,637 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:05:03,652 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:05:03,653 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:05:03,865 - app.utils.memory_management - INFO - Garbage collection: collected 2136 objects
2025-07-15 21:05:03,866 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:05:03,867 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:05:08,124 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:05:08,178 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:08,188 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:05:08,188 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:08,188 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:05:08,189 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:05:08,191 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-15 21:05:08,192 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:08,192 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:08,192 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:05:08,196 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:05:08,196 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:05:08,197 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-15 21:05:08,197 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:46,870 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=False
2025-07-15 21:05:46,900 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 21:05:47,142 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:05:47,165 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 21:05:47,374 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:05:47,375 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-07-15 21:05:47,376 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 21:05:47,515 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 21:05:47,516 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 21:05:47,516 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 21:05:47,516 - models.predict - INFO - Ensemble model already loaded
2025-07-15 21:05:47,552 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 21:05:47,552 - models.predict - INFO - Current price: 87.0, Predicted scaled value: 0.4236584450257059
2025-07-15 21:05:47,553 - models.predict - INFO - Prediction for 60 minutes horizon: 86.09094039037306
2025-07-15 21:05:47,556 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 86.09 vs current 84.38 (2.0% change, limit: 2.0%). Applying correction.
2025-07-15 21:05:47,556 - app.pages.predictions_consolidated - INFO - Corrected prediction: 86.09 -> 85.56 (change: 1.4%)
2025-07-15 21:05:47,562 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:47,563 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:47,564 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:05:47,570 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:05:47,570 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:05:47,605 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:47,606 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:47,606 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:05:47,615 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:05:47,615 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:05:47,629 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:05:47,629 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:05:47,630 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:05:47,631 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:47,632 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:05:47,633 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:05:47,643 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:05:47,644 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:05:47,652 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:05:47,656 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:05:47,658 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 21:05:47,659 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:05:47,659 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:05:47,659 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:05:47,660 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:05:47,660 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 21:05:47,660 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:05:47,661 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:05:47,661 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:05:47,661 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:05:47,661 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:05:47,662 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:05:47,662 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:05:47,662 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:05:47,662 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:05:47,672 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:05:47,683 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:05:47,684 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:05:47,684 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:05:47,684 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:05:47,699 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:05:47,700 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:05:48,014 - app.utils.memory_management - INFO - Garbage collection: collected 224 objects
2025-07-15 21:05:48,014 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:05:48,015 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:06:24,559 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:06:24,607 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:06:24,608 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:06:24,609 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:06:24,609 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:06:24,613 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:06:24,616 - app.utils.error_handling - INFO - live_trading_component executed in 0.04 seconds
2025-07-15 21:06:24,616 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:06:24,616 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:06:24,883 - app.utils.memory_management - INFO - Garbage collection: collected 1993 objects
2025-07-15 21:06:24,883 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:06:24,884 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:06:33,320 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:07:13,516 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:07:13,518 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:07:13,519 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:07:13,520 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:07:13,526 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:07:13,529 - app.utils.error_handling - INFO - live_trading_component executed in 40.18 seconds
2025-07-15 21:07:13,530 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:07:13,530 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:07:13,794 - app.utils.memory_management - INFO - Garbage collection: collected 1147 objects
2025-07-15 21:07:13,796 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:07:13,797 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:07:36,494 - app - INFO - Cleaning up resources...
2025-07-15 21:07:36,495 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:07:36,495 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:07:36,700 - app.utils.memory_management - INFO - Garbage collection: collected 3279 objects
2025-07-15 21:07:36,700 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:07:36,700 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:07:36,700 - app - INFO - Application shutdown complete
2025-07-15 21:09:00,343 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 21:09:02,018 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 21:09:02,019 - app - INFO - Memory management utilities loaded
2025-07-15 21:09:02,022 - app - INFO - Error handling utilities loaded
2025-07-15 21:09:02,034 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 21:09:02,036 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 21:09:02,036 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 21:09:02,037 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 21:09:02,048 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 21:09:02,048 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 21:09:02,048 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 21:09:02,048 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 21:09:02,070 - app - INFO - Applied NumPy fix
2025-07-15 21:09:02,077 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:09:02,078 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:09:02,078 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:09:02,078 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 21:09:02,078 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:09:02,079 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:09:02,079 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:09:02,079 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 21:09:18,549 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 21:09:18,550 - app - INFO - Applied TensorFlow fix
2025-07-15 21:09:18,554 - app.config - INFO - Configuration initialized
2025-07-15 21:09:18,564 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-15 21:09:18,750 - models.train - INFO - TensorFlow test successful
2025-07-15 21:09:18,829 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 21:09:18,829 - models.train - INFO - Transformer model is available
2025-07-15 21:09:18,829 - models.train - INFO - Using TensorFlow-based models
2025-07-15 21:09:18,832 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 21:09:18,832 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 21:09:18,841 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 21:09:19,684 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:09:19,684 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:09:19,685 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:09:19,685 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:09:19,685 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:09:19,685 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 21:09:19,685 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 21:09:19,685 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:09:19,685 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:09:19,686 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:09:19,905 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 21:09:19,907 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:09:20,484 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-15 21:09:21,649 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-15 21:09:21,649 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-15 21:09:21,869 - app.utils.session_state - INFO - Initializing session state
2025-07-15 21:09:21,871 - app.utils.session_state - INFO - Session state initialized
2025-07-15 21:09:22,522 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 21:09:22,541 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:22,542 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:09:22,757 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 21:09:22,759 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:22,760 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:09:31,143 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:09:31,164 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:31,165 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:09:31,361 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 21:09:31,361 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:31,363 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:09:32,794 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:09:32,930 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.08 seconds
2025-07-15 21:09:32,933 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 21:09:32,934 - app - INFO - Data shape: (1250, 36)
2025-07-15 21:09:32,934 - app - INFO - File COMI contains 2025 data
2025-07-15 21:09:32,969 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-07-15 21:09:32,969 - app - INFO - Features shape: (1250, 36)
2025-07-15 21:09:32,990 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-15 21:09:32,991 - app - INFO - Date range: 2020-09-29 to 2025-07-14
2025-07-15 21:09:32,991 - app - INFO - Data shape: (1250, 36)
2025-07-15 21:09:32,991 - app - INFO - File COMI contains 2025 data
2025-07-15 21:09:32,995 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:32,996 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:09:33,179 - app.utils.memory_management - INFO - Garbage collection: collected 730 objects
2025-07-15 21:09:33,180 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:33,180 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:09:33,606 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:09:33,699 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:33,699 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:09:33,909 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-15 21:09:33,909 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:33,910 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:09:40,402 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:09:40,494 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-07-15 21:09:40,616 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:09:40,616 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:09:40,616 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:09:40,616 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:09:40,621 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:09:40,623 - app.utils.error_handling - INFO - live_trading_component executed in 0.17 seconds
2025-07-15 21:09:40,623 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:40,623 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:09:40,852 - app.utils.memory_management - INFO - Garbage collection: collected 878 objects
2025-07-15 21:09:40,852 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:09:40,853 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:09:45,856 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:10:26,151 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:10:26,158 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:10:26,158 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:10:26,159 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:10:26,162 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:10:26,167 - app.utils.error_handling - INFO - live_trading_component executed in 40.29 seconds
2025-07-15 21:10:26,167 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:10:26,168 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:10:26,386 - app.utils.memory_management - INFO - Garbage collection: collected 1087 objects
2025-07-15 21:10:26,386 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:10:26,387 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:16:03,770 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:16:03,786 - app - INFO - Found 14 stock files in data/stocks
2025-07-15 21:16:03,837 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:16:03,838 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:03,839 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:16:03,839 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:03,846 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:16:03,850 - app.utils.error_handling - INFO - live_trading_component executed in 0.06 seconds
2025-07-15 21:16:03,850 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:16:03,851 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:16:04,063 - app.utils.memory_management - INFO - Garbage collection: collected 1240 objects
2025-07-15 21:16:04,065 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:16:04,066 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:16:10,248 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:16:10,319 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:16:10,320 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:10,320 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:16:10,320 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:10,326 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:16:10,329 - app.utils.error_handling - INFO - live_trading_component executed in 0.06 seconds
2025-07-15 21:16:10,329 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:16:10,329 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:16:10,533 - app.utils.memory_management - INFO - Garbage collection: collected 1201 objects
2025-07-15 21:16:10,534 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:16:10,535 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:16:13,270 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:16:32,504 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:16:32,506 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:32,506 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:16:32,507 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:32,515 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:16:32,518 - app.utils.error_handling - INFO - live_trading_component executed in 19.23 seconds
2025-07-15 21:16:32,519 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:16:32,519 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:16:32,739 - app.utils.memory_management - INFO - Garbage collection: collected 1162 objects
2025-07-15 21:16:32,741 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:16:32,741 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:16:57,505 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:16:57,588 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 21:16:57,589 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:57,589 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:16:57,590 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:57,590 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:16:57,590 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 21:16:57,591 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-15 21:16:57,596 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:16:57,596 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:16:57,601 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:16:57,610 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:16:57,613 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-15 21:16:57,618 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 21:16:57,619 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:16:57,619 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:16:57,620 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:16:57,620 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:16:57,621 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-15 21:16:57,629 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 21:16:57,630 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:16:57,630 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:16:57,630 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:16:57,630 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:16:57,631 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-15 21:16:57,636 - app.utils.data_processing - INFO - Using model trained for 15 minutes instead of 4 minutes
2025-07-15 21:16:57,637 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:57,637 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:16:57,637 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:57,638 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:16:57,638 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-15 21:16:57,666 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:57,668 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:57,668 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:16:57,672 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:16:57,672 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:16:57,682 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:16:57,683 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:57,683 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:16:57,684 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:57,684 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:16:57,685 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:16:57,687 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:16:57,688 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:16:57,690 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:16:57,694 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:16:57,694 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 21:16:57,695 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:16:57,696 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:16:57,696 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:16:57,697 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:16:57,697 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 21:16:57,698 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:16:57,699 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:16:57,699 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:16:57,700 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:16:57,701 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:16:57,702 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:57,703 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:16:57,703 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:16:57,704 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:16:57,712 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:57,719 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:16:57,720 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:16:57,720 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:16:57,720 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:16:57,733 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:16:57,734 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:16:57,925 - app.utils.memory_management - INFO - Garbage collection: collected 1259 objects
2025-07-15 21:16:57,926 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:16:57,927 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:17:04,750 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:17:04,795 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:04,796 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:04,796 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:04,800 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:04,800 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:04,809 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:17:04,809 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:04,810 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:17:04,811 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:04,811 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:17:04,812 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:17:04,815 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:17:04,816 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:17:04,821 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:04,827 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:17:04,828 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 21:17:04,829 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:17:04,829 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:17:04,830 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:04,831 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:17:04,831 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 21:17:04,835 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:17:04,836 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:17:04,837 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:04,837 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:17:04,841 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:17:04,842 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:04,842 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:17:04,842 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:04,843 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:17:04,851 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:04,858 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:17:04,859 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:17:04,860 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:17:04,860 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:04,881 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:17:04,882 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:17:05,098 - app.utils.memory_management - INFO - Garbage collection: collected 1621 objects
2025-07-15 21:17:05,098 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:17:05,100 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:17:07,478 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:17:07,517 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:07,524 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:07,527 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:07,527 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:07,527 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:07,527 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-15 21:17:07,530 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:07,530 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:07,530 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:07,536 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:07,537 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:07,537 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-15 21:17:07,537 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:18,264 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-15 21:17:18,304 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 21:17:18,496 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:17:18,496 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 21:17:18,708 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:17:18,709 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-15 21:17:18,727 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 21:17:19,047 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 21:17:19,047 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 21:17:19,048 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 21:17:19,048 - models.predict - INFO - Ensemble model already loaded
2025-07-15 21:17:19,112 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 21:17:19,113 - models.predict - INFO - Current price: 89.0, Predicted scaled value: 0.4455067894536599
2025-07-15 21:17:19,113 - models.predict - INFO - Prediction for 60 minutes horizon: 88.06253511611881
2025-07-15 21:17:19,125 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:19,125 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:19,126 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:19,133 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:19,133 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:19,263 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:19,263 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:19,263 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:19,266 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:19,266 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:19,287 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:17:19,288 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:19,288 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:17:19,288 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:19,288 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:17:19,289 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:17:19,293 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:17:19,293 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:17:19,302 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:19,309 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:17:19,310 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 21:17:19,310 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:17:19,310 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:17:19,310 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:19,310 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:17:19,310 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 21:17:19,311 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:17:19,311 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:17:19,311 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:19,313 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:17:19,314 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:17:19,314 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:19,314 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:17:19,314 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:19,314 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:17:19,325 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:19,339 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:17:19,339 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:17:19,340 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:17:19,340 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:19,352 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:17:19,352 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:17:19,543 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 21:17:19,544 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:17:19,545 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:17:48,060 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:17:48,125 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:48,126 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:48,127 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:48,130 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:48,132 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:48,144 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:17:48,144 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:48,144 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:17:48,145 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:48,145 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:17:48,145 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:17:48,151 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:17:48,152 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:17:48,162 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:48,170 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:17:48,170 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 21:17:48,172 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:17:48,173 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:17:48,174 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:48,176 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:17:48,176 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 21:17:48,176 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:17:48,177 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:17:48,177 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:48,177 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:17:48,177 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:17:48,177 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:48,177 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:17:48,177 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:48,179 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:17:48,191 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:48,195 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:48,195 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:48,196 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:48,196 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:48,214 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:17:48,214 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:17:48,437 - app.utils.memory_management - INFO - Garbage collection: collected 2006 objects
2025-07-15 21:17:48,438 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:17:48,439 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:17:56,262 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:17:56,320 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:56,321 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:56,321 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:56,331 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:56,331 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:56,342 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:17:56,343 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:56,344 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:17:56,345 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:56,345 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:17:56,346 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-15 21:17:56,355 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-15 21:17:56,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-15 21:17:56,362 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:56,365 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-15 21:17:56,365 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-15 21:17:56,365 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-15 21:17:56,365 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-15 21:17:56,365 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:56,366 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-15 21:17:56,366 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-15 21:17:56,366 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-15 21:17:56,366 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-15 21:17:56,366 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:56,367 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-15 21:17:56,367 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-15 21:17:56,367 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-15 21:17:56,368 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-15 21:17:56,368 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:56,369 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-15 21:17:56,386 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:56,393 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:17:56,394 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:17:56,395 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:17:56,395 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:17:56,397 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:18:13,250 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=True
2025-07-15 21:18:13,296 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 21:18:13,511 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:18:13,512 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 21:18:13,711 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:18:13,711 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-15 21:18:13,713 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-15 21:18:13,745 - models.hybrid_model - INFO - XGBoost is available
2025-07-15 21:18:13,745 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-15 21:18:13,747 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-15 21:18:13,747 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-15 21:18:13,748 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 21:18:13,748 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 21:18:13,753 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 21:18:13,753 - models.predict - INFO - Using fallback price due to error: 89.0
2025-07-15 21:18:13,753 - models.predict - INFO - Prediction for 60 minutes horizon: 89.0
2025-07-15 21:18:13,776 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-15 21:18:38,821 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=True
2025-07-15 21:18:38,859 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 21:18:39,092 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:18:39,093 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 21:18:39,283 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:18:39,283 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 21:18:39,286 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-15 21:18:39,288 - models.lstm_model - INFO - Loading model from native Keras format: saved_models/COMI_lstm_60min.keras
2025-07-15 21:18:39,693 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 21:18:40,492 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-15 21:18:40,493 - models.predict - INFO - Current price: 89.0, Predicted scaled value: 0.4865618050098419
2025-07-15 21:18:40,494 - models.predict - INFO - Prediction for 60 minutes horizon: 91.76733995398166
2025-07-15 21:18:40,495 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 91.77 vs current 89.00 (3.1% change, limit: 2.0%). Applying correction.
2025-07-15 21:18:40,495 - app.pages.predictions_consolidated - INFO - Corrected prediction: 91.77 -> 90.25 (change: 1.4%)
2025-07-15 21:18:40,496 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 91.77 -> 90.25 for 60min
2025-07-15 21:18:40,496 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-15 21:19:04,194 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-15 21:19:04,230 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 21:19:04,445 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:19:04,445 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 21:19:04,645 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:19:04,645 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-15 21:19:04,647 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-15 21:19:04,647 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-15 21:19:04,647 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-15 21:19:04,647 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 21:19:04,666 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 21:19:04,666 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 21:19:04,666 - models.predict - INFO - Using fallback price due to error: 89.0
2025-07-15 21:19:04,666 - models.predict - INFO - Prediction for 60 minutes horizon: 89.0
2025-07-15 21:19:04,668 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-15 21:19:27,667 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-15 21:19:27,728 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 21:19:27,926 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:19:27,927 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 21:19:28,119 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:19:28,120 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 21:19:28,122 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-15 21:19:28,122 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-15 21:19:28,122 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-15 21:19:28,122 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-15 21:19:28,123 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-15 21:19:28,123 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 506, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-15 21:19:28,123 - models.predict - INFO - Using fallback price due to error: 89.0
2025-07-15 21:19:28,123 - models.predict - INFO - Prediction for 60 minutes horizon: 89.0
2025-07-15 21:19:28,125 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-15 21:19:44,432 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-15 21:19:44,464 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-15 21:19:44,672 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:19:44,672 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-15 21:19:44,872 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:19:44,872 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-15 21:19:44,874 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-15 21:19:45,003 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-15 21:19:45,009 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-15 21:19:45,009 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-15 21:19:45,009 - models.predict - INFO - Ensemble model already loaded
2025-07-15 21:19:45,037 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-15 21:19:45,038 - models.predict - INFO - Current price: 89.0, Predicted scaled value: 0.4455067894536599
2025-07-15 21:19:45,038 - models.predict - INFO - Prediction for 60 minutes horizon: 88.06253511611881
2025-07-15 21:19:45,041 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 89.00 vs current 87.00 (2.3% change, limit: 2.0%). Applying correction.
2025-07-15 21:19:45,042 - app.pages.predictions_consolidated - INFO - Corrected prediction: 89.00 -> 88.22 (change: 1.4%)
2025-07-15 21:19:45,042 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 90.25 vs current 87.00 (3.7% change, limit: 2.0%). Applying correction.
2025-07-15 21:19:45,042 - app.pages.predictions_consolidated - INFO - Corrected prediction: 90.25 -> 88.22 (change: 1.4%)
2025-07-15 21:19:45,042 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 89.00 vs current 87.00 (2.3% change, limit: 2.0%). Applying correction.
2025-07-15 21:19:45,044 - app.pages.predictions_consolidated - INFO - Corrected prediction: 89.00 -> 88.22 (change: 1.4%)
2025-07-15 21:19:45,044 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 89.00 vs current 87.00 (2.3% change, limit: 2.0%). Applying correction.
2025-07-15 21:19:45,044 - app.pages.predictions_consolidated - INFO - Corrected prediction: 89.00 -> 88.22 (change: 1.4%)
2025-07-15 21:19:45,099 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:19:45,101 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:19:45,321 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 21:19:45,322 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:19:45,323 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:49:34,698 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 21:49:36,930 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 21:49:36,930 - app - INFO - Memory management utilities loaded
2025-07-15 21:49:36,932 - app - INFO - Error handling utilities loaded
2025-07-15 21:49:36,934 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 21:49:36,935 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 21:49:36,935 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 21:49:36,935 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 21:49:36,937 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 21:49:36,937 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 21:49:36,937 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 21:49:36,937 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 21:49:36,938 - app - INFO - Applied NumPy fix
2025-07-15 21:49:36,938 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:49:36,939 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:49:36,939 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:49:36,939 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 21:49:36,939 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:49:36,940 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:49:36,940 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:49:36,940 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 21:49:46,775 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 21:49:46,776 - app - INFO - Applied TensorFlow fix
2025-07-15 21:49:46,779 - app.config - INFO - Configuration initialized
2025-07-15 21:49:46,786 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-15 21:49:46,807 - models.train - INFO - TensorFlow test successful
2025-07-15 21:49:46,840 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 21:49:46,840 - models.train - INFO - Transformer model is available
2025-07-15 21:49:46,840 - models.train - INFO - Using TensorFlow-based models
2025-07-15 21:49:46,842 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 21:49:46,842 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 21:49:46,848 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 21:49:47,177 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:49:47,177 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:49:47,177 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:49:47,177 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:49:47,178 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:49:47,178 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 21:49:47,178 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 21:49:47,178 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:49:47,178 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:49:47,178 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:49:47,308 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 21:49:47,311 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:49:47,826 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-15 21:49:48,625 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-15 21:49:48,625 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-15 21:49:54,106 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:51:44,635 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 21:51:46,481 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 21:51:46,482 - app - INFO - Memory management utilities loaded
2025-07-15 21:51:46,485 - app - INFO - Error handling utilities loaded
2025-07-15 21:51:46,486 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 21:51:46,487 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 21:51:46,487 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 21:51:46,487 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 21:51:46,496 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 21:51:46,496 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 21:51:46,497 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 21:51:46,497 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 21:51:46,497 - app - INFO - Applied NumPy fix
2025-07-15 21:51:46,498 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:51:46,498 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:51:46,498 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:51:46,498 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 21:51:46,498 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:51:46,499 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:51:46,499 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:51:46,499 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 21:51:56,856 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 21:51:56,861 - app - INFO - Applied TensorFlow fix
2025-07-15 21:51:56,863 - app.config - INFO - Configuration initialized
2025-07-15 21:51:56,884 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-15 21:51:56,907 - models.train - INFO - TensorFlow test successful
2025-07-15 21:51:56,949 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 21:51:56,950 - models.train - INFO - Transformer model is available
2025-07-15 21:51:56,950 - models.train - INFO - Using TensorFlow-based models
2025-07-15 21:51:56,951 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 21:51:56,951 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 21:51:56,964 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 21:51:57,589 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:51:57,589 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:51:57,589 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:51:57,589 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:51:57,589 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:51:57,589 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 21:51:57,590 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 21:51:57,590 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:51:57,590 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:51:57,590 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:51:57,830 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 21:51:57,833 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:51:57,834 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:51:58,552 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-15 21:51:59,844 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-15 21:51:59,844 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-15 21:53:18,452 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 21:53:20,659 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 21:53:20,660 - app - INFO - Memory management utilities loaded
2025-07-15 21:53:20,663 - app - INFO - Error handling utilities loaded
2025-07-15 21:53:20,663 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 21:53:20,664 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 21:53:20,664 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 21:53:20,664 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 21:53:20,665 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 21:53:20,665 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 21:53:20,666 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 21:53:20,666 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 21:53:20,666 - app - INFO - Applied NumPy fix
2025-07-15 21:53:20,667 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:53:20,667 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:53:20,667 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:53:20,667 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 21:53:20,668 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:53:20,669 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:53:20,670 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:53:20,670 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 21:53:30,423 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 21:53:30,423 - app - INFO - Applied TensorFlow fix
2025-07-15 21:53:31,126 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 21:53:31,644 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:53:31,644 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:53:31,644 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:53:31,645 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:53:31,645 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:53:31,645 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 21:53:31,645 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 21:53:31,645 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:53:31,645 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:53:31,645 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:53:31,774 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 21:53:31,836 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 21:53:31,836 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 21:53:31,836 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 21:53:32,518 - app - INFO - Cleaning up resources...
2025-07-15 21:53:32,518 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:53:32,519 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:53:32,685 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 21:53:32,685 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:53:32,685 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:53:32,685 - app - INFO - Application shutdown complete
2025-07-15 21:54:33,600 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 21:54:36,077 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 21:54:36,078 - app - INFO - Memory management utilities loaded
2025-07-15 21:54:36,081 - app - INFO - Error handling utilities loaded
2025-07-15 21:54:36,082 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 21:54:36,083 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 21:54:36,085 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 21:54:36,085 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 21:54:36,086 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 21:54:36,086 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 21:54:36,087 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 21:54:36,087 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 21:54:36,087 - app - INFO - Applied NumPy fix
2025-07-15 21:54:36,088 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:54:36,088 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:54:36,088 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:54:36,088 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 21:54:36,089 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:54:36,089 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:54:36,089 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:54:36,091 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 21:54:46,502 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 21:54:46,502 - app - INFO - Applied TensorFlow fix
2025-07-15 21:54:47,463 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 21:54:47,947 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:54:47,948 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:54:47,948 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:54:47,948 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:54:47,948 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:54:47,948 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 21:54:47,949 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 21:54:47,949 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:54:47,949 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:54:47,949 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:54:48,175 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 21:54:48,216 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 21:54:48,216 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 21:54:48,216 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 21:54:49,195 - app - INFO - Cleaning up resources...
2025-07-15 21:54:49,195 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:54:49,195 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 21:54:49,417 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-15 21:54:49,418 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 21:54:49,418 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 21:54:49,418 - app - INFO - Application shutdown complete
2025-07-15 21:55:48,796 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 21:55:53,779 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 21:55:53,780 - app - INFO - Memory management utilities loaded
2025-07-15 21:55:53,783 - app - INFO - Error handling utilities loaded
2025-07-15 21:55:53,787 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 21:55:53,789 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 21:55:53,789 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 21:55:53,789 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 21:55:53,790 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 21:55:53,790 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 21:55:53,791 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 21:55:53,791 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 21:55:53,791 - app - INFO - Applied NumPy fix
2025-07-15 21:55:53,794 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:55:53,794 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:55:53,795 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:55:53,795 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 21:55:53,795 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:55:53,796 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:55:53,796 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:55:53,796 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 21:56:11,943 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 21:56:11,944 - app - INFO - Applied TensorFlow fix
2025-07-15 21:56:11,953 - app.config - INFO - Configuration initialized
2025-07-15 21:56:11,980 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-15 21:56:12,003 - models.train - INFO - TensorFlow test successful
2025-07-15 21:56:12,088 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 21:56:12,089 - models.train - INFO - Transformer model is available
2025-07-15 21:56:12,089 - models.train - INFO - Using TensorFlow-based models
2025-07-15 21:56:12,096 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 21:56:12,096 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 21:56:12,104 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 21:56:12,990 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:56:12,991 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 21:56:12,991 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 21:56:12,991 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 21:56:12,991 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 21:56:12,991 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 21:56:12,991 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 21:56:12,991 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 21:56:12,992 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 21:56:12,992 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 21:56:13,424 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 21:56:13,431 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:56:13,432 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 21:56:14,531 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-15 21:56:17,319 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-15 21:56:17,319 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-15 22:02:45,397 - app - INFO - Cleaning up resources...
2025-07-15 22:02:45,401 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 22:02:45,402 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 22:02:46,604 - app.utils.memory_management - INFO - Garbage collection: collected 2281 objects
2025-07-15 22:02:46,612 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 22:02:46,612 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 22:02:46,613 - app - INFO - Application shutdown complete
2025-07-15 22:02:59,783 - app - INFO - Cleaning up resources...
2025-07-15 22:02:59,783 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 22:02:59,783 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 22:03:00,051 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-07-15 22:03:00,052 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 22:03:00,052 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 22:03:00,052 - app - INFO - Application shutdown complete
2025-07-15 22:03:35,979 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-15 22:03:38,427 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-15 22:03:38,428 - app - INFO - Memory management utilities loaded
2025-07-15 22:03:38,430 - app - INFO - Error handling utilities loaded
2025-07-15 22:03:38,431 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-15 22:03:38,432 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-15 22:03:38,432 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-15 22:03:38,433 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-15 22:03:38,434 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-15 22:03:38,435 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-15 22:03:38,435 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-15 22:03:38,435 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-15 22:03:38,436 - app - INFO - Applied NumPy fix
2025-07-15 22:03:38,437 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 22:03:38,437 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 22:03:38,437 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 22:03:38,437 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-15 22:03:38,438 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 22:03:38,438 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 22:03:38,439 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 22:03:38,440 - app - INFO - Applied NumPy BitGenerator fix
2025-07-15 22:03:48,942 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-15 22:03:48,942 - app - INFO - Applied TensorFlow fix
2025-07-15 22:03:48,945 - app.config - INFO - Configuration initialized
2025-07-15 22:03:48,951 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-15 22:03:48,982 - models.train - INFO - TensorFlow test successful
2025-07-15 22:03:49,018 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-15 22:03:49,019 - models.train - INFO - Transformer model is available
2025-07-15 22:03:49,019 - models.train - INFO - Using TensorFlow-based models
2025-07-15 22:03:49,021 - models.predict - INFO - Transformer model is available for predictions
2025-07-15 22:03:49,021 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-15 22:03:49,025 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-15 22:03:49,406 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 22:03:49,406 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-15 22:03:49,406 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-15 22:03:49,407 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-15 22:03:49,407 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-15 22:03:49,407 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-15 22:03:49,408 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-15 22:03:49,408 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-15 22:03:49,408 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-15 22:03:49,408 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-15 22:03:49,541 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-15 22:03:49,543 - app - INFO - Using TensorFlow-based LSTM model
2025-07-15 22:03:50,195 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-15 22:03:51,173 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-15 22:03:51,174 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-15 22:05:19,478 - app - INFO - Cleaning up resources...
2025-07-15 22:05:19,487 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 22:05:19,487 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-15 22:05:19,694 - app.utils.memory_management - INFO - Garbage collection: collected 66 objects
2025-07-15 22:05:19,694 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-15 22:05:19,695 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-15 22:05:19,695 - app - INFO - Application shutdown complete
