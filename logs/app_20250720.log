2025-07-20 08:19:33,428 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-20 08:19:37,073 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-20 08:19:37,073 - app - INFO - Memory management utilities loaded
2025-07-20 08:19:37,075 - app - INFO - Error handling utilities loaded
2025-07-20 08:19:37,076 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-20 08:19:37,076 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-20 08:19:37,077 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-20 08:19:37,077 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-20 08:19:37,077 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-20 08:19:37,077 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-20 08:19:37,078 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-20 08:19:37,078 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-20 08:19:37,078 - app - INFO - Applied NumPy fix
2025-07-20 08:19:37,082 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-20 08:19:37,082 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-20 08:19:37,082 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-20 08:19:37,082 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-20 08:19:37,082 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-20 08:19:37,082 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-20 08:19:37,083 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-20 08:19:37,083 - app - INFO - Applied NumPy BitGenerator fix
2025-07-20 08:20:22,008 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-20 08:20:22,009 - app - INFO - Applied TensorFlow fix
2025-07-20 08:20:22,011 - app.config - INFO - Configuration initialized
2025-07-20 08:20:22,017 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-20 08:20:22,041 - models.train - INFO - TensorFlow test successful
2025-07-20 08:20:22,228 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-20 08:20:22,228 - models.train - INFO - Transformer model is available
2025-07-20 08:20:22,228 - models.train - INFO - Using TensorFlow-based models
2025-07-20 08:20:22,230 - models.predict - INFO - Transformer model is available for predictions
2025-07-20 08:20:22,230 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-20 08:20:22,232 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-20 08:20:25,779 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-20 08:20:25,780 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-20 08:20:25,780 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-20 08:20:25,780 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-20 08:20:25,780 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-20 08:20:25,780 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-20 08:20:25,780 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-20 08:20:25,781 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-20 08:20:25,781 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-20 08:20:25,781 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-20 08:20:26,178 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-20 08:20:26,180 - app - INFO - Using TensorFlow-based LSTM model
2025-07-20 08:20:26,773 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-20 08:20:30,532 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-20 08:20:30,533 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-20 08:20:30,951 - app.utils.session_state - INFO - Initializing session state
2025-07-20 08:20:30,953 - app.utils.session_state - INFO - Session state initialized
2025-07-20 08:20:31,742 - app - INFO - Found 14 stock files in data/stocks
2025-07-20 08:20:31,758 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:20:31,759 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-20 08:20:32,007 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-20 08:20:32,008 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:20:32,008 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-20 08:20:46,950 - app - INFO - Using TensorFlow-based LSTM model
2025-07-20 08:20:46,990 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:20:46,991 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-20 08:20:47,318 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-20 08:20:47,319 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:20:47,321 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-20 08:20:49,189 - app - INFO - Using TensorFlow-based LSTM model
2025-07-20 08:20:49,343 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.13 seconds
2025-07-20 08:20:49,344 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-20 08:20:49,344 - app - INFO - Data shape: (1250, 36)
2025-07-20 08:20:49,344 - app - INFO - File COMI contains 2025 data
2025-07-20 08:20:49,381 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-07-20 08:20:49,381 - app - INFO - Features shape: (1250, 36)
2025-07-20 08:20:49,415 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.03 seconds
2025-07-20 08:20:49,416 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-20 08:20:49,417 - app - INFO - Data shape: (1250, 36)
2025-07-20 08:20:49,417 - app - INFO - File COMI contains 2025 data
2025-07-20 08:20:49,420 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:20:49,422 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-20 08:20:49,604 - app.utils.memory_management - INFO - Garbage collection: collected 730 objects
2025-07-20 08:20:49,605 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:20:49,606 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-20 08:20:50,071 - app - INFO - Using TensorFlow-based LSTM model
2025-07-20 08:20:50,222 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:20:50,222 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-20 08:20:50,483 - app.utils.memory_management - INFO - Garbage collection: collected 28 objects
2025-07-20 08:20:50,483 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:20:50,483 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-20 08:21:10,335 - app - INFO - Using TensorFlow-based LSTM model
2025-07-20 08:21:10,838 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets/Support/Resistance.json'
2025-07-20 08:21:11,685 - app.utils.common - INFO - Found 14 stock files in data/stocks
2025-07-20 08:21:11,705 - app.utils.common - INFO - Loaded stock data for SUGR from data/stocks/SUGR.csv in 0.01 seconds
2025-07-20 08:21:11,706 - app.utils.common - INFO - Date range: 2022-08-01 to 2025-06-13
2025-07-20 08:21:11,706 - app.utils.common - INFO - Data shape: (750, 6)
2025-07-20 08:21:11,706 - app.utils.common - INFO - File SUGR contains 2025 data
2025-07-20 08:21:12,201 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.52 seconds
2025-07-20 08:21:12,891 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:21:12,892 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-20 08:21:13,237 - app.utils.memory_management - INFO - Garbage collection: collected 3860 objects
2025-07-20 08:21:13,238 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-20 08:21:13,239 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
