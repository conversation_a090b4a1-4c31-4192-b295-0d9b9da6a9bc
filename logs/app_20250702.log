2025-07-02 04:37:21,273 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-02 04:37:24,461 - app - INFO - Memory management utilities loaded
2025-07-02 04:37:24,467 - app - INFO - Error handling utilities loaded
2025-07-02 04:37:24,469 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-02 04:37:24,469 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-02 04:37:24,471 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-02 04:37:24,471 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-02 04:37:24,485 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-02 04:37:24,487 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-02 04:37:24,487 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-02 04:37:24,489 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-02 04:37:24,489 - app - INFO - Applied NumPy fix
2025-07-02 04:37:24,491 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-02 04:37:24,491 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-02 04:37:24,491 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-02 04:37:24,491 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-02 04:37:24,493 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-02 04:37:24,493 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-02 04:37:24,493 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-02 04:37:24,494 - app - INFO - Applied NumPy BitGenerator fix
2025-07-02 04:37:40,279 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-07-02 04:37:40,280 - app - INFO - Applied TensorFlow fix
2025-07-02 04:37:40,283 - app.config - INFO - Configuration initialized
2025-07-02 04:37:40,289 - models.train - INFO - TensorFlow version: 2.9.1
2025-07-02 04:37:40,622 - models.train - INFO - TensorFlow test successful
2025-07-02 04:37:42,361 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-07-02 04:37:42,362 - models.train - INFO - Transformer model is available
2025-07-02 04:37:42,362 - models.train - INFO - Using TensorFlow-based models
2025-07-02 04:37:42,371 - models.predict - INFO - Transformer model is available for predictions
2025-07-02 04:37:42,371 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-02 04:37:42,376 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-02 04:37:43,060 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-02 04:37:43,061 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-02 04:37:43,063 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-02 04:37:43,063 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-02 04:37:43,063 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-02 04:37:43,064 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-02 04:37:43,065 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-02 04:37:43,066 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-02 04:37:43,067 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-02 04:37:43,067 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-02 04:37:43,261 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-02 04:37:43,264 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:37:43,718 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-02 04:37:44,917 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-02 04:37:45,180 - app.utils.session_state - INFO - Initializing session state
2025-07-02 04:37:45,182 - app.utils.session_state - INFO - Session state initialized
2025-07-02 04:37:46,442 - app - INFO - Found 14 stock files in data/stocks
2025-07-02 04:37:46,481 - app.utils.memory_management - INFO - Memory before cleanup: 427.99 MB
2025-07-02 04:37:46,715 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-02 04:37:46,717 - app.utils.memory_management - INFO - Memory after cleanup: 428.00 MB (freed -0.01 MB)
2025-07-02 04:37:51,887 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:37:51,946 - app.utils.memory_management - INFO - Memory before cleanup: 431.81 MB
2025-07-02 04:37:52,252 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-02 04:37:52,252 - app.utils.memory_management - INFO - Memory after cleanup: 431.81 MB (freed 0.00 MB)
2025-07-02 04:37:53,067 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:37:53,295 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.17 seconds
2025-07-02 04:37:53,297 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-07-02 04:37:53,298 - app - INFO - Data shape: (750, 36)
2025-07-02 04:37:53,299 - app - INFO - File COMI contains 2025 data
2025-07-02 04:37:53,347 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-07-02 04:37:53,347 - app - INFO - Features shape: (750, 36)
2025-07-02 04:37:53,377 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-07-02 04:37:53,378 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-07-02 04:37:53,380 - app - INFO - Data shape: (750, 36)
2025-07-02 04:37:53,381 - app - INFO - File COMI contains 2025 data
2025-07-02 04:37:53,385 - app.utils.memory_management - INFO - Memory before cleanup: 436.53 MB
2025-07-02 04:37:53,575 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-07-02 04:37:53,579 - app.utils.memory_management - INFO - Memory after cleanup: 436.57 MB (freed -0.04 MB)
2025-07-02 04:37:53,751 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:37:53,867 - app.utils.memory_management - INFO - Memory before cleanup: 437.61 MB
2025-07-02 04:37:54,100 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-07-02 04:37:54,100 - app.utils.memory_management - INFO - Memory after cleanup: 437.59 MB (freed 0.02 MB)
2025-07-02 04:38:01,013 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:38:02,714 - app.utils.memory_management - INFO - Memory before cleanup: 453.75 MB
2025-07-02 04:38:02,817 - app.utils.memory_management - INFO - Garbage collection: collected 1193 objects
2025-07-02 04:38:02,832 - app.utils.memory_management - INFO - Memory after cleanup: 453.75 MB (freed 0.00 MB)
2025-07-02 04:38:08,262 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:38:08,325 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 04:38:08,382 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-02 04:38:08,518 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-02 04:38:08,656 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.04 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-02 04:38:08,662 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-02 04:38:08,746 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_30min.joblib
2025-07-02 04:38:08,747 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-02 04:38:08,747 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-02 04:38:08,747 - models.predict - INFO - Ensemble model already loaded
2025-07-02 04:38:08,780 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:38:08,780 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9908505838908588
2025-07-02 04:38:08,781 - models.predict - INFO - Prediction for 30 minutes horizon: 83.6996708856103
2025-07-02 04:38:08,781 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 04:38:08,799 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 04:38:08,918 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 04:38:09,040 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: -0.02 MB, VMS: -0.04 MB, Percent: -0.00%, Execution time: 0.01s
2025-07-02 04:38:09,040 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-02 04:38:09,089 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-02 04:38:09,090 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-02 04:38:09,090 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-02 04:38:09,090 - models.predict - INFO - Ensemble model already loaded
2025-07-02 04:38:09,107 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:38:09,107 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9908505838908588
2025-07-02 04:38:09,107 - models.predict - INFO - Prediction for 60 minutes horizon: 83.6996708856103
2025-07-02 04:38:09,107 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,123 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,123 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,140 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,140 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,157 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,157 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,174 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,174 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:09,191 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-02 04:38:09,207 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-02 04:38:09,207 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-02 04:38:09,223 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-02 04:38:09,223 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-02 04:38:09,223 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-02 04:38:09,240 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-02 04:38:09,240 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-02 04:38:09,257 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-02 04:38:09,257 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 04:38:09,281 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-02 04:38:09,391 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-02 04:38:09,541 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-02 04:38:09,541 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-02 04:38:09,591 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_1440min.joblib
2025-07-02 04:38:09,591 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-02 04:38:09,591 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-02 04:38:09,591 - models.predict - INFO - Ensemble model already loaded
2025-07-02 04:38:09,626 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:38:09,626 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7530065618314385
2025-07-02 04:38:09,626 - models.predict - INFO - Prediction for 1440 minutes horizon: 82.9647577418431
2025-07-02 04:38:09,758 - app.utils.memory_management - INFO - Memory before cleanup: 464.12 MB
2025-07-02 04:38:09,874 - app.utils.memory_management - INFO - Garbage collection: collected 905 objects
2025-07-02 04:38:09,874 - app.utils.memory_management - INFO - Memory after cleanup: 464.12 MB (freed 0.00 MB)
2025-07-02 04:38:27,468 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:38:27,795 - app.utils.memory_management - INFO - Memory before cleanup: 464.16 MB
2025-07-02 04:38:28,118 - app.utils.memory_management - INFO - Garbage collection: collected 1290 objects
2025-07-02 04:38:28,121 - app.utils.memory_management - INFO - Memory after cleanup: 464.16 MB (freed 0.00 MB)
2025-07-02 04:38:29,803 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:38:29,886 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 04:38:29,935 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-02 04:38:30,181 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-02 04:38:30,338 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:38:30,340 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-02 04:38:30,341 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-07-02 04:38:31,945 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-02 04:38:34,084 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-02 04:38:34,084 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9345698356628418
2025-07-02 04:38:34,089 - models.predict - INFO - Prediction for 30 minutes horizon: 81.54468070295958
2025-07-02 04:38:34,099 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 04:38:34,127 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 04:38:34,314 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 04:38:34,482 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:38:34,482 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-02 04:38:34,482 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-02 04:38:35,334 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 04:38:36,578 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-02 04:38:36,592 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 1.351715326309204
2025-07-02 04:38:36,594 - models.predict - INFO - Prediction for 60 minutes horizon: 97.51718400796054
2025-07-02 04:38:36,596 - app.pages.market_overview_dashboard - WARNING - Unrealistic prediction for 2nd Day: 97.51718400796054 vs 84.3
2025-07-02 04:38:36,605 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 04:38:36,642 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 04:38:36,824 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 04:38:36,967 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:38:36,967 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-02 04:38:37,236 - models.hybrid_model - INFO - XGBoost is available
2025-07-02 04:38:37,236 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-02 04:38:37,236 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-02 04:38:37,236 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-02 04:38:37,236 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-02 04:38:37,279 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 04:38:37,279 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:38:37,279 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:38:37,288 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:38:37,288 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-02 04:38:37,288 - models.predict - INFO - Prediction for 60 minutes horizon: 74.11819835939582
2025-07-02 04:38:37,302 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:37,304 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:38:37,320 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-02 04:38:37,333 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-02 04:38:37,339 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 04:38:37,376 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-02 04:38:37,535 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-02 04:38:37,715 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:38:37,719 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-02 04:38:37,719 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-07-02 04:38:38,594 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-02 04:38:39,844 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-02 04:38:39,844 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7969218492507935
2025-07-02 04:38:39,844 - models.predict - INFO - Prediction for 1440 minutes horizon: 85.37131535794687
2025-07-02 04:38:40,045 - app.utils.memory_management - INFO - Memory before cleanup: 510.23 MB
2025-07-02 04:38:40,341 - app.utils.memory_management - INFO - Garbage collection: collected 14807 objects
2025-07-02 04:38:40,342 - app.utils.memory_management - INFO - Memory after cleanup: 488.14 MB (freed 22.09 MB)
2025-07-02 04:39:41,684 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:39:42,131 - app.utils.memory_management - INFO - Memory before cleanup: 490.94 MB
2025-07-02 04:39:42,391 - app.utils.memory_management - INFO - Garbage collection: collected 1290 objects
2025-07-02 04:39:42,393 - app.utils.memory_management - INFO - Memory after cleanup: 490.94 MB (freed 0.00 MB)
2025-07-02 04:39:44,837 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:39:44,919 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 04:39:44,969 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-02 04:39:45,258 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-02 04:39:45,413 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:39:45,415 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-02 04:39:45,415 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-02 04:39:45,416 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-07-02 04:39:45,416 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-07-02 04:39:45,495 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-02 04:39:45,501 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:39:45,503 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:39:45,509 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:39:45,509 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-02 04:39:45,511 - models.predict - INFO - Prediction for 30 minutes horizon: 74.11819835939582
2025-07-02 04:39:45,511 - app.pages.market_overview_dashboard - WARNING - Unrealistic prediction for Tomorrow: 74.11819835939582 vs 84.3
2025-07-02 04:39:45,513 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 04:39:45,561 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 04:39:45,712 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 04:39:45,867 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:39:45,867 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-02 04:39:45,867 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-02 04:39:45,867 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-02 04:39:45,867 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-02 04:39:45,904 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 04:39:45,904 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:39:45,904 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:39:45,904 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:39:45,904 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-02 04:39:45,904 - models.predict - INFO - Prediction for 60 minutes horizon: 74.11819835939582
2025-07-02 04:39:45,904 - app.pages.market_overview_dashboard - WARNING - Unrealistic prediction for 2nd Day: 74.11819835939582 vs 84.3
2025-07-02 04:39:45,928 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:39:45,938 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-02 04:39:45,938 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 04:39:45,970 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-02 04:39:46,118 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-02 04:39:46,277 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:39:46,277 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-07-02 04:39:46,277 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-07-02 04:39:46,283 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_1440min.joblib
2025-07-02 04:39:46,284 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_1440min.joblib
2025-07-02 04:39:46,329 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-02 04:39:46,331 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:39:46,331 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:39:46,338 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:39:46,340 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7814051270484924
2025-07-02 04:39:46,340 - models.predict - INFO - Prediction for 1440 minutes horizon: 84.5209990287759
2025-07-02 04:39:46,525 - app.utils.memory_management - INFO - Memory before cleanup: 496.13 MB
2025-07-02 04:39:46,768 - app.utils.memory_management - INFO - Garbage collection: collected 1073 objects
2025-07-02 04:39:46,768 - app.utils.memory_management - INFO - Memory after cleanup: 496.13 MB (freed 0.00 MB)
2025-07-02 04:40:06,085 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:40:06,387 - app.utils.memory_management - INFO - Memory before cleanup: 496.14 MB
2025-07-02 04:40:06,631 - app.utils.memory_management - INFO - Garbage collection: collected 1276 objects
2025-07-02 04:40:06,631 - app.utils.memory_management - INFO - Memory after cleanup: 496.14 MB (freed 0.00 MB)
2025-07-02 04:40:08,843 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:40:09,000 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 04:40:09,065 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-02 04:40:09,246 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-02 04:40:09,400 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:40:09,401 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-02 04:40:09,402 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-02 04:40:09,402 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-07-02 04:40:09,402 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-07-02 04:40:09,418 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-02 04:40:09,438 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:40:09,440 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:40:09,442 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:40:09,443 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7917314838161941
2025-07-02 04:40:09,444 - models.predict - INFO - Prediction for 30 minutes horizon: 76.07539936561052
2025-07-02 04:40:09,444 - app.pages.market_overview_dashboard - WARNING - Unrealistic prediction for Tomorrow: 76.07539936561052 vs 84.3
2025-07-02 04:40:09,444 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 04:40:09,479 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-02 04:40:09,621 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-02 04:40:09,797 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: -0.02 MB, VMS: -0.04 MB, Percent: -0.00%, Execution time: 0.00s
2025-07-02 04:40:09,797 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-02 04:40:09,797 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-02 04:40:09,797 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-07-02 04:40:09,797 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-07-02 04:40:09,833 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-02 04:40:09,833 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:40:09,833 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:40:09,839 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:40:09,840 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-02 04:40:09,841 - models.predict - INFO - Prediction for 30 minutes horizon: 74.11819835939582
2025-07-02 04:40:09,845 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 04:40:09,878 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 04:40:10,069 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 04:40:10,239 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:40:10,240 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-02 04:40:10,240 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-02 04:40:10,240 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-07-02 04:40:10,240 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-07-02 04:40:10,259 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 04:40:10,275 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:40:10,275 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:40:10,275 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:40:10,275 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7917314838161941
2025-07-02 04:40:10,275 - models.predict - INFO - Prediction for 60 minutes horizon: 76.07539936561052
2025-07-02 04:40:10,275 - app.pages.market_overview_dashboard - WARNING - Unrealistic prediction for 2nd Day: 76.07539936561052 vs 84.3
2025-07-02 04:40:10,275 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 04:40:10,311 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 04:40:10,466 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 04:40:10,632 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:40:10,634 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-02 04:40:10,634 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-02 04:40:10,634 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-02 04:40:10,634 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-02 04:40:10,659 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 04:40:10,659 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:40:10,659 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:40:10,671 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:40:10,675 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-02 04:40:10,676 - models.predict - INFO - Prediction for 60 minutes horizon: 74.11819835939582
2025-07-02 04:40:10,686 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:40:10,695 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 04:40:10,713 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-02 04:40:10,721 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-02 04:40:10,724 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 04:40:10,742 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-02 04:40:10,908 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-02 04:40:11,057 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 04:40:11,057 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-07-02 04:40:11,057 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-07-02 04:40:11,057 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_1440min.joblib
2025-07-02 04:40:11,057 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_1440min.joblib
2025-07-02 04:40:11,080 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-02 04:40:11,117 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-02 04:40:11,117 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 04:40:11,119 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 04:40:11,119 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7518808425780584
2025-07-02 04:40:11,121 - models.predict - INFO - Prediction for 1440 minutes horizon: 82.90306833020503
2025-07-02 04:40:11,349 - app.utils.memory_management - INFO - Memory before cleanup: 498.54 MB
2025-07-02 04:40:11,536 - app.utils.memory_management - INFO - Garbage collection: collected 1071 objects
2025-07-02 04:40:11,537 - app.utils.memory_management - INFO - Memory after cleanup: 498.54 MB (freed 0.00 MB)
2025-07-02 04:45:25,303 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 04:45:25,329 - app.utils.session_state - INFO - Initializing session state
2025-07-02 04:45:25,340 - app.utils.session_state - INFO - Session state initialized
2025-07-02 04:45:25,363 - app - INFO - Found 14 stock files in data/stocks
2025-07-02 04:45:25,384 - app.utils.memory_management - INFO - Memory before cleanup: 498.70 MB
2025-07-02 04:45:25,690 - app.utils.memory_management - INFO - Garbage collection: collected 234 objects
2025-07-02 04:45:25,691 - app.utils.memory_management - INFO - Memory after cleanup: 498.70 MB (freed 0.00 MB)
2025-07-02 14:52:08,167 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-07-02 14:52:12,229 - app - INFO - Memory management utilities loaded
2025-07-02 14:52:12,232 - app - INFO - Error handling utilities loaded
2025-07-02 14:52:12,234 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-02 14:52:12,234 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-02 14:52:12,234 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-02 14:52:12,234 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-02 14:52:12,254 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-02 14:52:12,256 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-02 14:52:12,257 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-02 14:52:12,257 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-02 14:52:12,258 - app - INFO - Applied NumPy fix
2025-07-02 14:52:12,261 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-02 14:52:12,261 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-02 14:52:12,261 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-02 14:52:12,263 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-02 14:52:12,263 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-02 14:52:12,267 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-02 14:52:12,267 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-02 14:52:12,267 - app - INFO - Applied NumPy BitGenerator fix
2025-07-02 14:52:33,495 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-07-02 14:52:33,495 - app - INFO - Applied TensorFlow fix
2025-07-02 14:52:33,499 - app.config - INFO - Configuration initialized
2025-07-02 14:52:33,506 - models.train - INFO - TensorFlow version: 2.9.1
2025-07-02 14:52:33,824 - models.train - INFO - TensorFlow test successful
2025-07-02 14:52:38,646 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-07-02 14:52:38,647 - models.train - INFO - Transformer model is available
2025-07-02 14:52:38,647 - models.train - INFO - Using TensorFlow-based models
2025-07-02 14:52:38,655 - models.predict - INFO - Transformer model is available for predictions
2025-07-02 14:52:38,656 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-02 14:52:38,663 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-02 14:52:41,127 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-02 14:52:41,127 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-02 14:52:41,127 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-02 14:52:41,127 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-02 14:52:41,128 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-02 14:52:41,128 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-02 14:52:41,128 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-02 14:52:41,141 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-02 14:52:41,141 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-02 14:52:41,141 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-02 14:52:41,516 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-02 14:52:41,535 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:52:42,123 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-02 14:52:44,679 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-02 14:52:44,995 - app.utils.session_state - INFO - Initializing session state
2025-07-02 14:52:44,997 - app.utils.session_state - INFO - Session state initialized
2025-07-02 14:52:46,196 - app - INFO - Found 14 stock files in data/stocks
2025-07-02 14:52:46,221 - app.utils.memory_management - INFO - Memory before cleanup: 429.68 MB
2025-07-02 14:52:46,438 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-02 14:52:46,438 - app.utils.memory_management - INFO - Memory after cleanup: 429.68 MB (freed -0.01 MB)
2025-07-02 14:53:02,467 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:53:02,577 - app.utils.memory_management - INFO - Memory before cleanup: 433.78 MB
2025-07-02 14:53:02,971 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-02 14:53:02,973 - app.utils.memory_management - INFO - Memory after cleanup: 433.78 MB (freed 0.00 MB)
2025-07-02 14:53:03,916 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:53:04,081 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.13 seconds
2025-07-02 14:53:04,082 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-07-02 14:53:04,083 - app - INFO - Data shape: (750, 36)
2025-07-02 14:53:04,083 - app - INFO - File COMI contains 2025 data
2025-07-02 14:53:04,144 - app - INFO - Feature engineering for COMI completed in 0.06 seconds
2025-07-02 14:53:04,146 - app - INFO - Features shape: (750, 36)
2025-07-02 14:53:04,189 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-07-02 14:53:04,190 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-07-02 14:53:04,192 - app - INFO - Data shape: (750, 36)
2025-07-02 14:53:04,192 - app - INFO - File COMI contains 2025 data
2025-07-02 14:53:04,198 - app.utils.memory_management - INFO - Memory before cleanup: 438.21 MB
2025-07-02 14:53:04,395 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-07-02 14:53:04,395 - app.utils.memory_management - INFO - Memory after cleanup: 438.25 MB (freed -0.04 MB)
2025-07-02 14:53:04,592 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:53:04,705 - app.utils.memory_management - INFO - Memory before cleanup: 439.26 MB
2025-07-02 14:53:04,908 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-07-02 14:53:04,910 - app.utils.memory_management - INFO - Memory after cleanup: 439.26 MB (freed -0.00 MB)
2025-07-02 14:53:32,607 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:53:35,203 - app.utils.memory_management - INFO - Memory before cleanup: 455.16 MB
2025-07-02 14:53:35,368 - app.utils.memory_management - INFO - Garbage collection: collected 1187 objects
2025-07-02 14:53:35,369 - app.utils.memory_management - INFO - Memory after cleanup: 455.16 MB (freed 0.00 MB)
2025-07-02 14:53:40,622 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:53:40,791 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 14:53:40,859 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-02 14:53:41,118 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-02 14:53:41,298 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-07-02 14:53:41,304 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-02 14:53:41,425 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_30min.joblib
2025-07-02 14:53:41,426 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-02 14:53:41,426 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-02 14:53:41,427 - models.predict - INFO - Ensemble model already loaded
2025-07-02 14:53:41,466 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 14:53:41,468 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9908505838908588
2025-07-02 14:53:41,469 - models.predict - INFO - Prediction for 30 minutes horizon: 83.6996708856103
2025-07-02 14:53:41,475 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 14:53:41,505 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 14:53:41,659 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 14:53:41,825 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 14:53:41,825 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-02 14:53:41,894 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-02 14:53:41,894 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-02 14:53:41,894 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-02 14:53:41,894 - models.predict - INFO - Ensemble model already loaded
2025-07-02 14:53:41,933 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 14:53:41,933 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9908505838908588
2025-07-02 14:53:41,933 - models.predict - INFO - Prediction for 60 minutes horizon: 83.6996708856103
2025-07-02 14:53:41,945 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:41,959 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:41,969 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:41,980 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:41,988 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:41,995 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:42,012 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:42,033 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:42,052 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:53:42,076 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-02 14:53:42,094 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-02 14:53:42,110 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-02 14:53:42,122 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-02 14:53:42,138 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-02 14:53:42,150 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-02 14:53:42,163 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-02 14:53:42,173 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-02 14:53:42,184 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-02 14:53:42,199 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 14:53:42,236 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-02 14:53:42,461 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-02 14:53:42,695 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-02 14:53:42,697 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-02 14:53:42,830 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_1440min.joblib
2025-07-02 14:53:42,832 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-02 14:53:42,836 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-02 14:53:42,836 - models.predict - INFO - Ensemble model already loaded
2025-07-02 14:53:42,879 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 14:53:42,881 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7530065618314385
2025-07-02 14:53:42,881 - models.predict - INFO - Prediction for 1440 minutes horizon: 82.9647577418431
2025-07-02 14:53:43,213 - app.utils.memory_management - INFO - Memory before cleanup: 464.89 MB
2025-07-02 14:53:43,535 - app.utils.memory_management - INFO - Garbage collection: collected 897 objects
2025-07-02 14:53:43,538 - app.utils.memory_management - INFO - Memory after cleanup: 464.89 MB (freed 0.00 MB)
2025-07-02 14:54:20,298 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:54:20,753 - app.utils.memory_management - INFO - Memory before cleanup: 465.19 MB
2025-07-02 14:54:21,062 - app.utils.memory_management - INFO - Garbage collection: collected 1282 objects
2025-07-02 14:54:21,064 - app.utils.memory_management - INFO - Memory after cleanup: 465.19 MB (freed 0.00 MB)
2025-07-02 14:54:23,382 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:54:23,480 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 14:54:23,530 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-02 14:54:23,738 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-02 14:54:23,887 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 14:54:23,889 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-02 14:54:23,889 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-07-02 14:54:25,447 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-02 14:54:27,522 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-02 14:54:27,522 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9345698356628418
2025-07-02 14:54:27,522 - models.predict - INFO - Prediction for 30 minutes horizon: 81.54468070295958
2025-07-02 14:54:27,539 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 14:54:27,574 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 14:54:27,767 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 14:54:27,917 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 14:54:27,920 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-02 14:54:27,920 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-02 14:54:28,782 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 14:54:30,339 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-02 14:54:30,341 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 1.351715326309204
2025-07-02 14:54:30,341 - models.predict - INFO - Prediction for 60 minutes horizon: 97.51718400796054
2025-07-02 14:54:30,341 - app.pages.market_overview_dashboard - WARNING - Unrealistic prediction for 2nd Day: 97.51718400796054 vs 84.3
2025-07-02 14:54:30,341 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 14:54:30,379 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 14:54:30,635 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 14:54:30,831 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-02 14:54:30,833 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-02 14:54:31,177 - models.hybrid_model - INFO - XGBoost is available
2025-07-02 14:54:31,177 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-02 14:54:31,198 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-02 14:54:31,198 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-02 14:54:31,198 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-02 14:54:31,247 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 14:54:31,247 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 14:54:31,247 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 14:54:31,256 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 14:54:31,257 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-02 14:54:31,257 - models.predict - INFO - Prediction for 60 minutes horizon: 74.11819835939582
2025-07-02 14:54:31,268 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:54:31,277 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-02 14:54:31,277 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-02 14:54:31,297 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-02 14:54:31,311 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 14:54:31,340 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-02 14:54:31,532 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-02 14:54:31,800 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 14:54:31,814 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-02 14:54:31,838 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-07-02 14:54:33,146 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-02 14:54:34,370 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-02 14:54:34,372 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7969218492507935
2025-07-02 14:54:34,372 - models.predict - INFO - Prediction for 1440 minutes horizon: 85.37131535794687
2025-07-02 14:54:34,639 - app.utils.memory_management - INFO - Memory before cleanup: 510.74 MB
2025-07-02 14:54:34,963 - app.utils.memory_management - INFO - Garbage collection: collected 14813 objects
2025-07-02 14:54:34,965 - app.utils.memory_management - INFO - Memory after cleanup: 489.37 MB (freed 21.38 MB)
2025-07-02 14:55:01,202 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:55:01,389 - app.utils.memory_management - INFO - Memory before cleanup: 490.78 MB
2025-07-02 14:55:01,757 - app.utils.memory_management - INFO - Garbage collection: collected 234 objects
2025-07-02 14:55:01,757 - app.utils.memory_management - INFO - Memory after cleanup: 490.78 MB (freed 0.00 MB)
2025-07-02 14:55:05,620 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:55:05,684 - app.utils.memory_management - INFO - Memory before cleanup: 490.98 MB
2025-07-02 14:55:05,909 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-07-02 14:55:05,921 - app.utils.memory_management - INFO - Memory after cleanup: 490.98 MB (freed 0.00 MB)
2025-07-02 14:55:07,060 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:55:07,129 - app.utils.memory_management - INFO - Memory before cleanup: 490.98 MB
2025-07-02 14:55:07,425 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-07-02 14:55:07,425 - app.utils.memory_management - INFO - Memory after cleanup: 490.98 MB (freed 0.00 MB)
2025-07-02 14:55:09,936 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 14:55:26,986 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-07-02 14:55:27,101 - app.utils.memory_management - INFO - Memory before cleanup: 494.63 MB
2025-07-02 14:55:27,314 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-07-02 14:55:27,314 - app.utils.memory_management - INFO - Memory after cleanup: 494.63 MB (freed 0.00 MB)
2025-07-02 15:07:05,781 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:07:05,880 - app - INFO - Found 14 stock files in data/stocks
2025-07-02 15:07:06,059 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-02 15:07:06,061 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:06,061 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:06,065 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:06,069 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:06,084 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-02 15:07:06,089 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-02 15:07:06,136 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:06,137 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:07:06,152 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:06,165 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:07:06,192 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-02 15:07:06,360 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-07-02 15:07:06,388 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:06,404 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:07:06,418 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:06,426 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:07:06,433 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-02 15:07:06,439 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-07-02 15:07:06,453 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:06,457 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:07:06,473 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:06,475 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:07:06,475 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-02 15:07:06,475 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-02 15:07:06,475 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:06,484 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:06,488 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:06,490 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:06,490 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-02 15:07:07,339 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:07,438 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:07,439 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:07,457 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:07,458 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:07,942 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:07:08,036 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:08,095 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:08,122 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:08,123 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:08,124 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:07:08,151 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:08,153 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:07:08,171 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:08,186 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:07:08,187 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:07:08,190 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:08,191 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:07:08,191 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:08,196 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:07:08,209 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:07:08,209 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:08,209 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:07:08,210 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:08,211 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:07:08,215 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:07:08,224 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:08,226 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:08,227 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:08,228 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:08,367 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:08,472 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:08,472 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:08,474 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:08,474 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:08,803 - app.utils.memory_management - INFO - Memory before cleanup: 488.03 MB
2025-07-02 15:07:09,061 - app.utils.memory_management - INFO - Garbage collection: collected 267 objects
2025-07-02 15:07:09,061 - app.utils.memory_management - INFO - Memory after cleanup: 488.03 MB (freed 0.00 MB)
2025-07-02 15:07:14,500 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:07:14,559 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:14,560 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:14,562 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:14,587 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:14,597 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:14,617 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:07:14,618 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:14,619 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:14,620 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:14,624 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:14,625 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:07:14,638 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:14,639 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:07:14,654 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:14,665 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:07:14,667 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:07:14,667 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:14,669 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:07:14,669 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:14,671 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:07:14,673 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:07:14,674 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:14,676 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:07:14,677 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:14,678 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:07:14,679 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:07:14,680 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:14,681 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:14,683 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:14,684 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:14,697 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:14,718 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:14,719 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:14,721 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:14,722 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:14,743 - app.utils.memory_management - INFO - Memory before cleanup: 488.05 MB
2025-07-02 15:07:14,956 - app.utils.memory_management - INFO - Garbage collection: collected 273 objects
2025-07-02 15:07:14,957 - app.utils.memory_management - INFO - Memory after cleanup: 488.05 MB (freed 0.00 MB)
2025-07-02 15:07:16,250 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:07:16,309 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:16,341 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:16,343 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:16,345 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:16,345 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:16,346 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-02 15:07:16,348 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:16,348 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:16,349 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:16,364 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:16,365 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:16,366 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-02 15:07:16,367 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:25,089 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-02 15:07:25,135 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:07:25,430 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:07:25,584 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-02 15:07:25,584 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-02 15:07:25,626 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-02 15:07:25,626 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-02 15:07:25,626 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-02 15:07:25,626 - models.predict - INFO - Ensemble model already loaded
2025-07-02 15:07:25,656 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 15:07:25,656 - models.predict - INFO - Current price: 84.44, Predicted scaled value: 0.944977081131115
2025-07-02 15:07:25,658 - models.predict - INFO - Prediction for 60 minutes horizon: 81.94317419351698
2025-07-02 15:07:25,658 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 81.94 vs current 84.44 (3.0% change, limit: 2.0%). Applying correction.
2025-07-02 15:07:25,658 - app.pages.predictions_consolidated - INFO - Corrected prediction: 81.94 -> 83.26 (change: -1.4%)
2025-07-02 15:07:25,658 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 81.94 -> 83.26 for 60min
2025-07-02 15:07:25,693 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:25,694 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:25,695 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:25,721 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:25,723 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:25,817 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:25,818 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:25,819 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:25,828 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:25,829 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:25,848 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:07:25,849 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:25,851 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:25,851 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:25,851 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:25,853 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:07:25,869 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:25,870 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:07:25,882 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:25,896 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:07:25,896 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:07:25,903 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:25,904 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:07:25,904 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:25,905 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:07:25,906 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:07:25,906 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:25,906 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:07:25,907 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:25,907 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:07:25,907 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:07:25,908 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:25,908 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:25,908 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:25,909 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:25,920 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:25,933 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:25,934 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:25,935 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:25,936 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:25,950 - app.utils.memory_management - INFO - Memory before cleanup: 490.53 MB
2025-07-02 15:07:26,166 - app.utils.memory_management - INFO - Garbage collection: collected 381 objects
2025-07-02 15:07:26,167 - app.utils.memory_management - INFO - Memory after cleanup: 490.53 MB (freed 0.00 MB)
2025-07-02 15:07:47,037 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:07:47,180 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:47,195 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:47,199 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:47,408 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:47,410 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:47,461 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:53,342 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-02 15:07:53,397 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:07:53,609 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:07:53,776 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 15:07:53,776 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-02 15:07:53,810 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-02 15:07:53,812 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-02 15:07:53,812 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-02 15:07:53,812 - models.predict - INFO - Ensemble model already loaded
2025-07-02 15:07:53,841 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 15:07:53,843 - models.predict - INFO - Current price: 84.44, Predicted scaled value: 0.944977081131115
2025-07-02 15:07:53,845 - models.predict - INFO - Prediction for 60 minutes horizon: 81.94317419351698
2025-07-02 15:07:53,864 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 81.94 vs current 84.44 (3.0% change, limit: 2.0%). Applying correction.
2025-07-02 15:07:53,868 - app.pages.predictions_consolidated - INFO - Corrected prediction: 81.94 -> 83.26 (change: -1.4%)
2025-07-02 15:07:53,868 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 81.94 -> 83.26 for 60min
2025-07-02 15:07:53,905 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:07:53,909 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:53,910 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:53,910 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:53,912 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:53,914 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:07:53,926 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:53,930 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:07:53,932 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:07:53,953 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:07:53,954 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:07:53,955 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:53,955 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:07:53,956 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:07:53,956 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:07:53,961 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:07:53,963 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:53,964 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:07:53,965 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:07:53,965 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:07:53,966 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:07:53,966 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:53,966 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:07:53,967 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:07:53,967 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:07:53,981 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:53,993 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:07:53,996 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:07:53,997 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:07:53,997 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:07:54,010 - app.utils.memory_management - INFO - Memory before cleanup: 492.35 MB
2025-07-02 15:07:54,192 - app.utils.memory_management - INFO - Garbage collection: collected 36 objects
2025-07-02 15:07:54,192 - app.utils.memory_management - INFO - Memory after cleanup: 492.35 MB (freed 0.00 MB)
2025-07-02 15:08:12,122 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:08:12,186 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:12,194 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:12,196 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:12,207 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:12,208 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:12,224 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:12,227 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:12,229 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:12,229 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:12,230 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:12,231 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:08:12,241 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:12,242 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:08:12,256 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:12,292 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:08:12,310 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:08:12,312 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:12,314 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:08:12,315 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:12,315 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:08:12,317 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:08:12,333 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:12,335 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:08:12,336 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:12,338 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:08:12,339 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:12,340 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:12,345 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:12,352 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:12,354 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:12,370 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:12,472 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:12,473 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:12,488 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:12,538 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:12,587 - app.utils.memory_management - INFO - Memory before cleanup: 492.35 MB
2025-07-02 15:08:12,766 - app.utils.memory_management - INFO - Garbage collection: collected 291 objects
2025-07-02 15:08:12,777 - app.utils.memory_management - INFO - Memory after cleanup: 492.35 MB (freed 0.00 MB)
2025-07-02 15:08:14,754 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:08:14,817 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:14,824 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:14,826 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:14,845 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:14,848 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:14,901 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:08:15,097 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:08:15,270 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 15:08:15,272 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-02 15:08:15,306 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-02 15:08:15,307 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-02 15:08:15,307 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-02 15:08:15,307 - models.predict - INFO - Ensemble model already loaded
2025-07-02 15:08:15,333 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 15:08:15,333 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9908505838908588
2025-07-02 15:08:15,334 - models.predict - INFO - Prediction for 60 minutes horizon: 83.6996708856103
2025-07-02 15:08:15,339 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:15,341 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:15,342 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:15,342 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:15,343 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:15,343 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:08:15,356 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:15,357 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:08:15,374 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:15,388 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:08:15,389 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:08:15,390 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:15,391 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:08:15,392 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:15,392 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:08:15,393 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:08:15,393 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:15,393 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:08:15,395 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:15,399 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:08:15,399 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:15,399 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:15,402 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:15,402 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:15,406 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:15,423 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:15,436 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:15,438 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:15,439 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:15,439 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:15,461 - app.utils.memory_management - INFO - Memory before cleanup: 493.00 MB
2025-07-02 15:08:15,664 - app.utils.memory_management - INFO - Garbage collection: collected 36 objects
2025-07-02 15:08:15,664 - app.utils.memory_management - INFO - Memory after cleanup: 493.00 MB (freed 0.00 MB)
2025-07-02 15:08:22,412 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:08:22,477 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:22,479 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:22,480 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:22,498 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:22,500 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:22,513 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:22,514 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:22,515 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:22,517 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:22,517 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:22,518 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:08:22,533 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:22,534 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:08:22,560 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:22,574 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:08:22,576 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:08:22,577 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:22,579 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:08:22,581 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:22,581 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:08:22,583 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:08:22,583 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:22,583 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:08:22,585 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:22,586 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:08:22,587 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:22,587 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:22,589 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:22,590 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:22,594 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:22,617 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:22,659 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:22,661 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:22,669 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:22,680 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:22,720 - app.utils.memory_management - INFO - Memory before cleanup: 493.00 MB
2025-07-02 15:08:22,904 - app.utils.memory_management - INFO - Garbage collection: collected 274 objects
2025-07-02 15:08:22,904 - app.utils.memory_management - INFO - Memory after cleanup: 493.00 MB (freed 0.00 MB)
2025-07-02 15:08:24,918 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:08:24,977 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:24,979 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:24,980 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:24,994 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:24,994 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:25,064 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:08:25,306 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:08:25,458 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 15:08:25,459 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-02 15:08:25,459 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-02 15:08:25,459 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-02 15:08:25,460 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-02 15:08:25,490 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 15:08:25,490 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 15:08:25,491 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 15:08:25,496 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 15:08:25,497 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-02 15:08:25,498 - models.predict - INFO - Prediction for 60 minutes horizon: 74.11819835939582
2025-07-02 15:08:25,502 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:25,502 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:25,503 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:25,503 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:25,508 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:25,508 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:08:25,530 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:25,531 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:08:25,545 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:25,555 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:08:25,555 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:08:25,555 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:25,557 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:08:25,557 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:25,557 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:08:25,560 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:08:25,560 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:25,560 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:08:25,562 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:25,562 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:08:25,564 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:25,564 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:25,564 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:25,566 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:25,568 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:25,582 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:25,597 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:25,598 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:25,599 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:25,599 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:25,614 - app.utils.memory_management - INFO - Memory before cleanup: 493.01 MB
2025-07-02 15:08:25,789 - app.utils.memory_management - INFO - Garbage collection: collected 27 objects
2025-07-02 15:08:25,789 - app.utils.memory_management - INFO - Memory after cleanup: 493.01 MB (freed 0.00 MB)
2025-07-02 15:08:33,489 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:08:33,563 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:33,566 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:33,571 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:33,588 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:33,600 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:33,726 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:33,744 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:33,755 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:33,760 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:33,761 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:33,761 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:08:33,771 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:33,774 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:08:33,790 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:33,804 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:08:33,806 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:08:33,807 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:33,807 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:08:33,807 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:33,808 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:08:33,808 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:08:33,809 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:33,809 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:08:33,810 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:33,810 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:08:33,811 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:33,812 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:33,813 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:33,813 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:33,813 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:33,825 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:33,838 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:33,841 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:33,843 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:33,844 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:33,873 - app.utils.memory_management - INFO - Memory before cleanup: 493.02 MB
2025-07-02 15:08:34,070 - app.utils.memory_management - INFO - Garbage collection: collected 274 objects
2025-07-02 15:08:34,070 - app.utils.memory_management - INFO - Memory after cleanup: 493.02 MB (freed 0.00 MB)
2025-07-02 15:08:35,923 - app - INFO - Using TensorFlow-based LSTM model
2025-07-02 15:08:35,984 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:35,984 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:35,986 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:36,008 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:36,008 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:36,022 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:36,023 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:36,026 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:36,027 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:36,028 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:36,029 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-02 15:08:36,043 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-02 15:08:36,043 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-02 15:08:36,074 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:36,096 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-02 15:08:36,096 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-02 15:08:36,097 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-02 15:08:36,098 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-02 15:08:36,099 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:36,099 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-02 15:08:36,100 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-02 15:08:36,100 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-02 15:08:36,102 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-02 15:08:36,104 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:36,104 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-02 15:08:36,105 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-02 15:08:36,105 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-02 15:08:36,105 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-02 15:08:36,106 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:36,106 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-02 15:08:36,112 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:36,125 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:36,137 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:36,140 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:08:36,140 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:36,146 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:08:42,624 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=True
2025-07-02 15:08:42,661 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:08:42,940 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:08:43,103 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 15:08:43,106 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-02 15:08:43,106 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-02 15:08:43,106 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-02 15:08:43,106 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-02 15:08:43,125 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 15:08:43,125 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-02 15:08:43,125 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 15:08:43,139 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 15:08:43,139 - models.predict - INFO - Current price: 84.44, Predicted scaled value: 0.7426455450057984
2025-07-02 15:08:43,139 - models.predict - INFO - Prediction for 60 minutes horizon: 74.1958984781305
2025-07-02 15:08:43,139 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 74.20 vs current 84.44 (12.1% change, limit: 2.0%). Applying correction.
2025-07-02 15:08:43,139 - app.pages.predictions_consolidated - INFO - Corrected prediction: 74.20 -> 83.26 (change: -1.4%)
2025-07-02 15:08:43,139 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 74.20 -> 83.26 for 60min
2025-07-02 15:08:43,165 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-02 15:08:48,318 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=True
2025-07-02 15:08:48,363 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:08:48,561 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:08:48,730 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 15:08:48,730 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-02 15:08:48,730 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-02 15:08:49,565 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 15:08:50,785 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-02 15:08:50,785 - models.predict - INFO - Current price: 84.44, Predicted scaled value: 1.3167986869812012
2025-07-02 15:08:50,785 - models.predict - INFO - Prediction for 60 minutes horizon: 96.18022568149776
2025-07-02 15:08:50,785 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 96.18 vs current 84.44 (13.9% change, limit: 2.0%). Applying correction.
2025-07-02 15:08:50,785 - app.pages.predictions_consolidated - INFO - Corrected prediction: 96.18 -> 85.62 (change: 1.4%)
2025-07-02 15:08:50,785 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 96.18 -> 85.62 for 60min
2025-07-02 15:08:50,785 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-02 15:08:56,608 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-02 15:08:56,655 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:08:56,869 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:08:57,036 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 15:08:57,036 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-02 15:08:57,040 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-02 15:08:57,040 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-07-02 15:08:57,040 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-07-02 15:08:57,077 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 15:08:57,097 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-02 15:08:57,097 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 15:08:57,097 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 15:08:57,097 - models.predict - INFO - Current price: 84.44, Predicted scaled value: 0.7869878003017466
2025-07-02 15:08:57,097 - models.predict - INFO - Prediction for 60 minutes horizon: 75.89376369577506
2025-07-02 15:08:57,097 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 75.89 vs current 84.44 (10.1% change, limit: 2.0%). Applying correction.
2025-07-02 15:08:57,107 - app.pages.predictions_consolidated - INFO - Corrected prediction: 75.89 -> 83.26 (change: -1.4%)
2025-07-02 15:08:57,109 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 75.89 -> 83.26 for 60min
2025-07-02 15:08:57,111 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-02 15:09:02,763 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-02 15:09:02,812 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:09:03,074 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:09:03,224 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-02 15:09:03,224 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-02 15:09:03,224 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-02 15:09:03,224 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-07-02 15:09:03,224 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_60min.joblib
2025-07-02 15:09:03,250 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-02 15:09:03,250 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-07-02 15:09:03,250 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-02 15:09:03,250 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 15:09:03,252 - models.predict - INFO - Current price: 84.44, Predicted scaled value: 1.3773752330115563
2025-07-02 15:09:03,252 - models.predict - INFO - Prediction for 60 minutes horizon: 98.49970198741724
2025-07-02 15:09:03,254 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 98.50 vs current 84.44 (16.7% change, limit: 2.0%). Applying correction.
2025-07-02 15:09:03,254 - app.pages.predictions_consolidated - INFO - Corrected prediction: 98.50 -> 85.62 (change: 1.4%)
2025-07-02 15:09:03,254 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 98.50 -> 85.62 for 60min
2025-07-02 15:09:03,254 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-02 15:09:08,271 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=True
2025-07-02 15:09:08,315 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-02 15:09:08,490 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-02 15:09:08,630 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-02 15:09:08,630 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-02 15:09:08,665 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-02 15:09:08,665 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-02 15:09:08,665 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-02 15:09:08,665 - models.predict - INFO - Ensemble model already loaded
2025-07-02 15:09:08,699 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-02 15:09:08,699 - models.predict - INFO - Current price: 84.44, Predicted scaled value: 0.944977081131115
2025-07-02 15:09:08,699 - models.predict - INFO - Prediction for 60 minutes horizon: 81.94317419351698
2025-07-02 15:09:08,704 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 81.94 vs current 84.44 (3.0% change, limit: 2.0%). Applying correction.
2025-07-02 15:09:08,704 - app.pages.predictions_consolidated - INFO - Corrected prediction: 81.94 -> 83.26 (change: -1.4%)
2025-07-02 15:09:08,704 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 81.94 -> 83.26 for 60min
2025-07-02 15:09:08,734 - app.utils.memory_management - INFO - Memory before cleanup: 498.88 MB
2025-07-02 15:09:08,930 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-02 15:09:08,930 - app.utils.memory_management - INFO - Memory after cleanup: 498.88 MB (freed 0.00 MB)
2025-07-02 21:02:20,773 - app - INFO - Cleaning up resources...
2025-07-02 21:02:20,775 - app.utils.memory_management - INFO - Memory before cleanup: 339.45 MB
2025-07-02 21:02:21,022 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-07-02 21:02:21,022 - app.utils.memory_management - INFO - Memory after cleanup: 440.73 MB (freed -101.29 MB)
2025-07-02 21:02:21,024 - app - INFO - Application shutdown complete
