2025-07-18 09:55:22,072 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-18 09:55:25,359 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-18 09:55:25,360 - app - INFO - Memory management utilities loaded
2025-07-18 09:55:25,361 - app - INFO - Error handling utilities loaded
2025-07-18 09:55:25,362 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-18 09:55:25,363 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-18 09:55:25,364 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-18 09:55:25,364 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-18 09:55:25,364 - app.utils.numpy_fix - INFO - <PERSON>T19937 is properly registered as a BitGenerator
2025-07-18 09:55:25,365 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-18 09:55:25,365 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-18 09:55:25,365 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-18 09:55:25,368 - app - INFO - Applied NumPy fix
2025-07-18 09:55:25,369 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-18 09:55:25,369 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-18 09:55:25,370 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-18 09:55:25,370 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-18 09:55:25,371 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-18 09:55:25,371 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-18 09:55:25,371 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-18 09:55:25,371 - app - INFO - Applied NumPy BitGenerator fix
2025-07-18 09:56:08,557 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-18 09:56:08,558 - app - INFO - Applied TensorFlow fix
2025-07-18 09:56:08,560 - app.config - INFO - Configuration initialized
2025-07-18 09:56:08,567 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-18 09:56:08,590 - models.train - INFO - TensorFlow test successful
2025-07-18 09:56:08,770 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-18 09:56:08,770 - models.train - INFO - Transformer model is available
2025-07-18 09:56:08,770 - models.train - INFO - Using TensorFlow-based models
2025-07-18 09:56:08,772 - models.predict - INFO - Transformer model is available for predictions
2025-07-18 09:56:08,772 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-18 09:56:08,775 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-18 09:56:12,466 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-18 09:56:12,466 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-18 09:56:12,468 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-18 09:56:12,468 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-18 09:56:12,468 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-18 09:56:12,910 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-18 09:56:12,912 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:56:13,556 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-18 09:56:16,603 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-18 09:56:16,603 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-18 09:56:16,874 - app.utils.session_state - INFO - Initializing session state
2025-07-18 09:56:16,876 - app.utils.session_state - INFO - Session state initialized
2025-07-18 09:56:17,547 - app - INFO - Found 14 stock files in data/stocks
2025-07-18 09:56:17,555 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:17,555 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:56:17,757 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-18 09:56:17,758 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:17,759 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:56:30,132 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:56:30,160 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-18 09:56:30,161 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-18 09:56:30,208 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:30,208 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:56:30,427 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-18 09:56:30,428 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:30,429 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:56:48,491 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:56:48,569 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:48,570 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:56:48,771 - app.utils.memory_management - INFO - Garbage collection: collected 1107 objects
2025-07-18 09:56:48,772 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:48,773 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:57:02,240 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:57:02,321 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:02,323 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:57:02,548 - app.utils.memory_management - INFO - Garbage collection: collected 1158 objects
2025-07-18 09:57:02,549 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:02,550 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:57:03,780 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:57:03,854 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.05 seconds
2025-07-18 09:57:03,855 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 09:57:03,856 - app - INFO - Data shape: (1250, 36)
2025-07-18 09:57:03,856 - app - INFO - File COMI contains 2025 data
2025-07-18 09:57:03,895 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-07-18 09:57:03,895 - app - INFO - Features shape: (1250, 36)
2025-07-18 09:57:03,918 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-18 09:57:03,918 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 09:57:03,918 - app - INFO - Data shape: (1250, 36)
2025-07-18 09:57:03,918 - app - INFO - File COMI contains 2025 data
2025-07-18 09:57:03,923 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:03,923 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:57:04,116 - app.utils.memory_management - INFO - Garbage collection: collected 1262 objects
2025-07-18 09:57:04,117 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:04,117 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:57:04,515 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:57:18,540 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:18,556 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:57:18,792 - app.utils.memory_management - INFO - Garbage collection: collected 956 objects
2025-07-18 09:57:18,793 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:18,794 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:57:21,046 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:57:26,715 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-18 09:57:26,748 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-18 09:57:26,969 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:26,970 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 09:57:27,165 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:27,165 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:57:27,182 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-18 09:57:27,693 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-18 09:57:27,693 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-18 09:57:27,693 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-18 09:57:27,694 - models.predict - INFO - Ensemble model already loaded
2025-07-18 09:57:27,725 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-18 09:57:27,725 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1591982737254
2025-07-18 09:57:27,726 - models.predict - WARNING - Prediction 30382.867968710583 is too far from current price 89980.0, using fallback
2025-07-18 09:57:27,726 - models.predict - INFO - Prediction for 30 minutes horizon: 91236.37756928915
2025-07-18 09:57:27,729 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-18 09:57:27,758 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-18 09:57:27,970 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:27,970 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 09:57:28,164 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:28,164 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:57:28,166 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-18 09:57:28,292 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-18 09:57:28,292 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-18 09:57:28,292 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-18 09:57:28,292 - models.predict - INFO - Ensemble model already loaded
2025-07-18 09:57:28,322 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-18 09:57:28,322 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1591982737254
2025-07-18 09:57:28,323 - models.predict - WARNING - Prediction 30382.867968710583 is too far from current price 89980.0, using fallback
2025-07-18 09:57:28,323 - models.predict - INFO - Prediction for 60 minutes horizon: 89041.6213836736
2025-07-18 09:57:28,330 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 09:57:28,333 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-18 09:57:28,337 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-18 09:57:28,340 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-18 09:57:28,343 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-18 09:57:28,347 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-18 09:57:28,351 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-18 09:57:28,354 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-18 09:57:28,357 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 09:57:28,362 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 09:57:28,366 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-18 09:57:28,369 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-18 09:57:28,372 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-18 09:57:28,376 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-18 09:57:28,380 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-18 09:57:28,383 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-18 09:57:28,387 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-18 09:57:28,391 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 09:57:28,392 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-18 09:57:28,416 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-18 09:57:28,612 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:28,613 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 09:57:28,807 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:28,807 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:57:28,809 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-18 09:57:28,891 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-18 09:57:28,891 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-18 09:57:28,891 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-18 09:57:28,891 - models.predict - INFO - Ensemble model already loaded
2025-07-18 09:57:28,920 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-18 09:57:28,920 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1591982737254
2025-07-18 09:57:28,920 - models.predict - WARNING - Prediction 30382.867968710583 is too far from current price 89980.0, using fallback
2025-07-18 09:57:28,920 - models.predict - INFO - Prediction for 1440 minutes horizon: 90194.01331544976
2025-07-18 09:57:29,295 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:29,296 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:57:29,504 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-18 09:57:29,504 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:29,504 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
