2025-07-18 09:55:22,072 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-18 09:55:25,359 - app.utils.memory_management - WARNING - psutil not available. Memory monitoring will be limited.
2025-07-18 09:55:25,360 - app - INFO - Memory management utilities loaded
2025-07-18 09:55:25,361 - app - INFO - Error handling utilities loaded
2025-07-18 09:55:25,362 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-18 09:55:25,363 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-18 09:55:25,364 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-18 09:55:25,364 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-18 09:55:25,364 - app.utils.numpy_fix - INFO - <PERSON>T19937 is properly registered as a BitGenerator
2025-07-18 09:55:25,365 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-18 09:55:25,365 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-18 09:55:25,365 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-18 09:55:25,368 - app - INFO - Applied NumPy fix
2025-07-18 09:55:25,369 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-18 09:55:25,369 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-18 09:55:25,370 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-18 09:55:25,370 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-18 09:55:25,371 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-18 09:55:25,371 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-18 09:55:25,371 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-18 09:55:25,371 - app - INFO - Applied NumPy BitGenerator fix
2025-07-18 09:56:08,557 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.16.2)
2025-07-18 09:56:08,558 - app - INFO - Applied TensorFlow fix
2025-07-18 09:56:08,560 - app.config - INFO - Configuration initialized
2025-07-18 09:56:08,567 - models.train - INFO - TensorFlow version: 2.16.2
2025-07-18 09:56:08,590 - models.train - INFO - TensorFlow test successful
2025-07-18 09:56:08,770 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.16.2
2025-07-18 09:56:08,770 - models.train - INFO - Transformer model is available
2025-07-18 09:56:08,770 - models.train - INFO - Using TensorFlow-based models
2025-07-18 09:56:08,772 - models.predict - INFO - Transformer model is available for predictions
2025-07-18 09:56:08,772 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-18 09:56:08,775 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-18 09:56:12,466 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-18 09:56:12,466 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-18 09:56:12,467 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-18 09:56:12,468 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-18 09:56:12,468 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-18 09:56:12,468 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-18 09:56:12,910 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-18 09:56:12,912 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:56:13,556 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-18 09:56:16,603 - app.pages.advanced_technical_analysis - INFO - 📊 Advanced Technical Analysis - Pure Technical Analysis Mode
2025-07-18 09:56:16,603 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-18 09:56:16,874 - app.utils.session_state - INFO - Initializing session state
2025-07-18 09:56:16,876 - app.utils.session_state - INFO - Session state initialized
2025-07-18 09:56:17,547 - app - INFO - Found 14 stock files in data/stocks
2025-07-18 09:56:17,555 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:17,555 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:56:17,757 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-18 09:56:17,758 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:17,759 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:56:30,132 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:56:30,160 - app.pages.market_overview_dashboard - INFO - Found 14 CSV files in data/stocks
2025-07-18 09:56:30,161 - app.pages.market_overview_dashboard - INFO - Loaded 14 stocks from data folder: ['SUGR', 'ABUK', 'ISPH', 'SWDY', 'EGX30', 'JUFO', 'TMGH', 'SKPC', 'COMI', 'QNBE', 'DOMT', 'ORWE', 'HRHO', 'ORHD']
2025-07-18 09:56:30,208 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:30,208 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:56:30,427 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-18 09:56:30,428 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:30,429 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:56:48,491 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:56:48,569 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:48,570 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:56:48,771 - app.utils.memory_management - INFO - Garbage collection: collected 1107 objects
2025-07-18 09:56:48,772 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:56:48,773 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:57:02,240 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:57:02,321 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:02,323 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:57:02,548 - app.utils.memory_management - INFO - Garbage collection: collected 1158 objects
2025-07-18 09:57:02,549 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:02,550 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:57:03,780 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:57:03,854 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.05 seconds
2025-07-18 09:57:03,855 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 09:57:03,856 - app - INFO - Data shape: (1250, 36)
2025-07-18 09:57:03,856 - app - INFO - File COMI contains 2025 data
2025-07-18 09:57:03,895 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-07-18 09:57:03,895 - app - INFO - Features shape: (1250, 36)
2025-07-18 09:57:03,918 - app - INFO - Loaded stock data for COMI from data/stocks/COMI.csv in 0.02 seconds
2025-07-18 09:57:03,918 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 09:57:03,918 - app - INFO - Data shape: (1250, 36)
2025-07-18 09:57:03,918 - app - INFO - File COMI contains 2025 data
2025-07-18 09:57:03,923 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:03,923 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:57:04,116 - app.utils.memory_management - INFO - Garbage collection: collected 1262 objects
2025-07-18 09:57:04,117 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:04,117 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:57:04,515 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:57:18,540 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:18,556 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:57:18,792 - app.utils.memory_management - INFO - Garbage collection: collected 956 objects
2025-07-18 09:57:18,793 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:18,794 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:57:21,046 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:57:26,715 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-18 09:57:26,748 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-18 09:57:26,969 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:26,970 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 09:57:27,165 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:27,165 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:57:27,182 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-18 09:57:27,693 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_30min.joblib
2025-07-18 09:57:27,693 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-18 09:57:27,693 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-18 09:57:27,694 - models.predict - INFO - Ensemble model already loaded
2025-07-18 09:57:27,725 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-18 09:57:27,725 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1591982737254
2025-07-18 09:57:27,726 - models.predict - WARNING - Prediction 30382.867968710583 is too far from current price 89980.0, using fallback
2025-07-18 09:57:27,726 - models.predict - INFO - Prediction for 30 minutes horizon: 91236.37756928915
2025-07-18 09:57:27,729 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-18 09:57:27,758 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-18 09:57:27,970 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:27,970 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 09:57:28,164 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:28,164 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:57:28,166 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-18 09:57:28,292 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_60min.joblib
2025-07-18 09:57:28,292 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-18 09:57:28,292 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-18 09:57:28,292 - models.predict - INFO - Ensemble model already loaded
2025-07-18 09:57:28,322 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-18 09:57:28,322 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1591982737254
2025-07-18 09:57:28,323 - models.predict - WARNING - Prediction 30382.867968710583 is too far from current price 89980.0, using fallback
2025-07-18 09:57:28,323 - models.predict - INFO - Prediction for 60 minutes horizon: 89041.6213836736
2025-07-18 09:57:28,330 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 09:57:28,333 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-18 09:57:28,337 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-18 09:57:28,340 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using svr
2025-07-18 09:57:28,343 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lstm
2025-07-18 09:57:28,347 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using bilstm
2025-07-18 09:57:28,351 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using prophet
2025-07-18 09:57:28,354 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using transformer
2025-07-18 09:57:28,357 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 09:57:28,362 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 09:57:28,366 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-18 09:57:28,369 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-18 09:57:28,372 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-18 09:57:28,376 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-18 09:57:28,380 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-18 09:57:28,383 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-18 09:57:28,387 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-18 09:57:28,391 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 09:57:28,392 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-18 09:57:28,416 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-18 09:57:28,612 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:28,613 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 09:57:28,807 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:28,807 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:57:28,809 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-18 09:57:28,891 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models/COMI_ensemble_1440min.joblib
2025-07-18 09:57:28,891 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-18 09:57:28,891 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-18 09:57:28,891 - models.predict - INFO - Ensemble model already loaded
2025-07-18 09:57:28,920 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-18 09:57:28,920 - models.predict - INFO - Current price: 89980.0, Predicted scaled value: 336.1591982737254
2025-07-18 09:57:28,920 - models.predict - WARNING - Prediction 30382.867968710583 is too far from current price 89980.0, using fallback
2025-07-18 09:57:28,920 - models.predict - INFO - Prediction for 1440 minutes horizon: 90194.01331544976
2025-07-18 09:57:29,295 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:29,296 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:57:29,504 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-18 09:57:29,504 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:57:29,504 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:58:42,685 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:58:49,772 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:58:53,171 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:58:53,171 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:58:53,426 - app.utils.memory_management - INFO - Garbage collection: collected 1756 objects
2025-07-18 09:58:53,427 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:58:53,427 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:58:57,498 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:58:57,498 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:58:57,695 - app.utils.memory_management - INFO - Garbage collection: collected 1880 objects
2025-07-18 09:58:57,695 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:58:57,695 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:59:00,690 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:59:04,458 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-18 09:59:04,505 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-18 09:59:04,707 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:04,708 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 09:59:04,892 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:04,892 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:59:04,894 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-18 09:59:04,898 - models.hybrid_model - INFO - XGBoost is available
2025-07-18 09:59:04,898 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-18 09:59:04,900 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-18 09:59:04,900 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_30min.joblib or saved_models/COMI_rf_30min.pkl
2025-07-18 09:59:04,900 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-18 09:59:04,900 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-18 09:59:04,903 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 09:59:04,904 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-18 09:59:04,904 - models.predict - INFO - Prediction for 30 minutes horizon: 89.98
2025-07-18 09:59:04,905 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-18 09:59:04,926 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-18 09:59:05,103 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:05,104 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 09:59:05,300 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:05,300 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:59:05,302 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-18 09:59:05,302 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-18 09:59:05,302 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-18 09:59:05,302 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-18 09:59:05,302 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-18 09:59:05,302 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 09:59:05,303 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-18 09:59:05,303 - models.predict - INFO - Prediction for 60 minutes horizon: 89.98
2025-07-18 09:59:05,309 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 09:59:05,312 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 09:59:05,313 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-18 09:59:05,332 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-18 09:59:05,512 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:05,513 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 09:59:05,699 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:05,699 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:59:05,701 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-07-18 09:59:05,701 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-07-18 09:59:05,701 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_1440min.joblib or saved_models/COMI_rf_1440min.pkl
2025-07-18 09:59:05,701 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-18 09:59:05,701 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-18 09:59:05,701 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 09:59:05,702 - models.predict - INFO - Using fallback price due to error: 89.98
2025-07-18 09:59:05,702 - models.predict - INFO - Prediction for 1440 minutes horizon: 89.98
2025-07-18 09:59:10,482 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:10,483 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:59:10,664 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-18 09:59:10,665 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:10,666 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:59:26,002 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:59:31,463 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:31,463 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:59:31,672 - app.utils.memory_management - INFO - Garbage collection: collected 2689 objects
2025-07-18 09:59:31,673 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:31,673 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:59:35,278 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:59:40,957 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-18 09:59:40,983 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-18 09:59:41,176 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:41,177 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 09:59:41,368 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:41,368 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:59:41,370 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-18 09:59:41,370 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-18 09:59:41,370 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_30min.joblib or saved_models/COMI_gb_30min.pkl
2025-07-18 09:59:41,371 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-18 09:59:41,391 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-18 09:59:41,391 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 09:59:41,392 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 09:59:41,392 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-18 09:59:41,392 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-18 09:59:41,412 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-18 09:59:41,595 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:41,595 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 09:59:41,785 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:41,786 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:59:41,787 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-18 09:59:41,787 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-18 09:59:41,788 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-18 09:59:41,788 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-18 09:59:41,809 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-18 09:59:41,810 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 09:59:41,810 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 09:59:41,811 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-18 09:59:41,814 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-18 09:59:41,818 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 09:59:41,822 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-18 09:59:41,827 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 09:59:41,828 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-18 09:59:41,848 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-18 09:59:42,038 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:42,039 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 09:59:42,240 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:42,240 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 09:59:42,242 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-07-18 09:59:42,243 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-07-18 09:59:42,243 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_1440min.joblib or saved_models/COMI_gb_1440min.pkl
2025-07-18 09:59:42,243 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-18 09:59:42,261 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-18 09:59:42,261 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 09:59:42,262 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 09:59:42,262 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-18 09:59:42,526 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:42,526 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:59:42,726 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-18 09:59:42,727 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:42,727 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:59:50,264 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 09:59:56,036 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:56,038 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 09:59:56,238 - app.utils.memory_management - INFO - Garbage collection: collected 2811 objects
2025-07-18 09:59:56,239 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 09:59:56,240 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 09:59:58,828 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:00:04,557 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-18 10:00:04,584 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-18 10:00:04,782 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:00:04,782 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 10:00:04,972 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:00:04,972 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:00:04,974 - models.predict - INFO - Using scikit-learn lr model for 30 minutes horizon
2025-07-18 10:00:04,974 - models.predict - INFO - Loading lr model for COMI with horizon 30
2025-07-18 10:00:04,975 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_30min.joblib or saved_models/COMI_lr_30min.pkl
2025-07-18 10:00:04,975 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-18 10:00:04,975 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-18 10:00:04,975 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:00:04,976 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:00:04,976 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-18 10:00:04,976 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-18 10:00:05,000 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-18 10:00:05,183 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:00:05,184 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 10:00:05,379 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:00:05,379 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:00:05,382 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-18 10:00:05,382 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-18 10:00:05,382 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-18 10:00:05,383 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-18 10:00:05,383 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-18 10:00:05,383 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:00:05,384 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:00:05,384 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-18 10:00:05,388 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-18 10:00:05,392 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 10:00:05,396 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-18 10:00:05,399 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 10:00:05,400 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-18 10:00:05,420 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-18 10:00:05,604 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:00:05,604 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 10:00:05,798 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:00:05,798 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:00:05,800 - models.predict - INFO - Using scikit-learn lr model for 1440 minutes horizon
2025-07-18 10:00:05,801 - models.predict - INFO - Loading lr model for COMI with horizon 1440
2025-07-18 10:00:05,801 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_1440min.joblib or saved_models/COMI_lr_1440min.pkl
2025-07-18 10:00:05,801 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-18 10:00:05,801 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-18 10:00:05,801 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:00:05,802 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:00:05,802 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-18 10:00:06,046 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:00:06,046 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:00:06,252 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-18 10:00:06,252 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:00:06,253 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:04:43,930 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:04:44,081 - app - INFO - Found 14 stock files in data/stocks
2025-07-18 10:04:44,090 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:04:44,091 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:04:44,291 - app.utils.memory_management - INFO - Garbage collection: collected 1769 objects
2025-07-18 10:04:44,292 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:04:44,292 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:04:51,780 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:04:51,814 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:04:51,815 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:04:52,017 - app.utils.memory_management - INFO - Garbage collection: collected 766 objects
2025-07-18 10:04:52,017 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:04:52,019 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:04:55,907 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:04:55,936 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:04:55,937 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:04:56,136 - app.utils.memory_management - INFO - Garbage collection: collected 709 objects
2025-07-18 10:04:56,138 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:04:56,139 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:04:58,124 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:04:58,155 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:04:58,156 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:04:58,387 - app.utils.memory_management - INFO - Garbage collection: collected 710 objects
2025-07-18 10:04:58,388 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:04:58,388 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:05:35,990 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:05:36,024 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:05:36,024 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:05:36,217 - app.utils.memory_management - INFO - Garbage collection: collected 710 objects
2025-07-18 10:05:36,218 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:05:36,218 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:05:55,125 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:05:55,164 - app - INFO - File COMI contains 2025 data
2025-07-18 10:05:55,241 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-18 10:05:55,242 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 10:05:55,245 - models.train - INFO - Starting training from CSV: data/stocks/COMI.csv
2025-07-18 10:05:55,246 - models.train - INFO - Symbol: COMI, Model type: rf
2025-07-18 10:05:55,246 - models.train - INFO - Horizons: [5, 15, 30, 60] (in minutes)
2025-07-18 10:05:55,246 - models.train - INFO - Loading data from data/stocks/COMI.csv...
2025-07-18 10:05:55,260 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-18 10:05:55,260 - models.train - INFO - Converting Date column to datetime...
2025-07-18 10:05:55,264 - models.train - INFO - Starting model training...
2025-07-18 10:05:55,264 - models.train - INFO - Training models for symbol: COMI
2025-07-18 10:05:55,265 - models.train - INFO - Model type: rf
2025-07-18 10:05:55,267 - models.train - INFO - Horizons: [5, 15, 30, 60]
2025-07-18 10:05:55,267 - models.train - INFO - Sequence length: 60
2025-07-18 10:05:55,268 - models.train - INFO - Epochs: 1
2025-07-18 10:05:55,268 - models.train - INFO - Batch size: 32
2025-07-18 10:05:55,268 - models.train - INFO - Data shape: (1250, 36)
2025-07-18 10:05:55,268 - models.train - INFO - Preparing features...
2025-07-18 10:05:55,301 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-18 10:05:55,301 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-18 10:05:55,302 - models.train - INFO - Training model for 5 minutes horizon
2025-07-18 10:05:55,302 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-18 10:05:55,306 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:05:55,306 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:05:55,307 - models.train - INFO - Creating target variable for horizon 5 minutes...
2025-07-18 10:05:55,307 - models.train - INFO - Converting 5 minutes to approximately 1 days for target shifting
2025-07-18 10:05:55,308 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:05:55,308 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:05:55,315 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-18 10:05:55,316 - models.train - INFO - Preprocessing data...
2025-07-18 10:05:55,630 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:05:55,630 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:05:55,859 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:05:56,048 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:05:56,048 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.23s
2025-07-18 10:05:56,049 - models.train - INFO - Saving scaler for horizon 5...
2025-07-18 10:05:56,236 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:05:56,241 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-18 10:05:56,244 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_5min.joblib (0.53 KB)
2025-07-18 10:05:56,247 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler5min.joblib (0.53 KB)
2025-07-18 10:05:56,636 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:05:56,659 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-18 10:05:56,659 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_5_scaler.pkl', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler5min.joblib']
2025-07-18 10:05:56,659 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:05:56,660 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:05:56,660 - models.train - INFO - Initializing model for horizon 5...
2025-07-18 10:05:56,660 - models.train - INFO - Using scikit-learn rf model for 5 days horizon
2025-07-18 10:05:56,660 - models.train - INFO - Training model for horizon 5...
2025-07-18 10:05:56,660 - models.train - INFO - Training machine learning model (rf) for horizon 5. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:05:56,660 - models.sklearn_model - INFO - Training rf model for 5 minutes horizon
2025-07-18 10:05:56,661 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:05:56,661 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:06:17,968 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:06:18,021 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_5min.joblib
2025-07-18 10:06:18,021 - models.train - INFO - Machine learning model (rf) training completed for horizon 5
2025-07-18 10:06:18,021 - models.train - INFO - Saving model for horizon 5...
2025-07-18 10:06:18,066 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_5min.joblib
2025-07-18 10:06:18,066 - models.train - INFO - Model saved for horizon 5
2025-07-18 10:06:18,067 - models.train - INFO - Model for 5 days horizon trained and saved successfully
2025-07-18 10:06:18,067 - models.train - INFO - Training model for 15 minutes horizon
2025-07-18 10:06:18,067 - models.train - INFO - Data shape before processing: (1250, 37)
2025-07-18 10:06:18,069 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:06:18,069 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:06:18,069 - models.train - INFO - Creating target variable for horizon 15 minutes...
2025-07-18 10:06:18,069 - models.train - INFO - Converting 15 minutes to approximately 1 days for target shifting
2025-07-18 10:06:18,070 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:06:18,070 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:06:18,071 - models.train - INFO - Data shape after dropping NaN rows: (1249, 38)
2025-07-18 10:06:18,071 - models.train - INFO - Preprocessing data...
2025-07-18 10:06:18,276 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:06:18,276 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:06:18,473 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:06:18,662 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:06:18,663 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:06:18,663 - models.train - INFO - Saving scaler for horizon 15...
2025-07-18 10:06:18,854 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:06:18,859 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-18 10:06:18,862 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_15min.joblib (0.53 KB)
2025-07-18 10:06:18,865 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler15min.joblib (0.53 KB)
2025-07-18 10:06:19,244 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:06:19,244 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:06:19,245 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_15_scaler.pkl', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler15min.joblib']
2025-07-18 10:06:19,245 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:06:19,245 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:06:19,245 - models.train - INFO - Initializing model for horizon 15...
2025-07-18 10:06:19,245 - models.train - INFO - Using scikit-learn rf model for 15 days horizon
2025-07-18 10:06:19,246 - models.train - INFO - Training model for horizon 15...
2025-07-18 10:06:19,246 - models.train - INFO - Training machine learning model (rf) for horizon 15. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:06:19,246 - models.sklearn_model - INFO - Training rf model for 15 minutes horizon
2025-07-18 10:06:19,246 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:06:19,246 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:06:39,076 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:06:39,119 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_15min.joblib
2025-07-18 10:06:39,119 - models.train - INFO - Machine learning model (rf) training completed for horizon 15
2025-07-18 10:06:39,119 - models.train - INFO - Saving model for horizon 15...
2025-07-18 10:06:39,158 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_15min.joblib
2025-07-18 10:06:39,158 - models.train - INFO - Model saved for horizon 15
2025-07-18 10:06:39,158 - models.train - INFO - Model for 15 days horizon trained and saved successfully
2025-07-18 10:06:39,158 - models.train - INFO - Training model for 30 minutes horizon
2025-07-18 10:06:39,159 - models.train - INFO - Data shape before processing: (1250, 38)
2025-07-18 10:06:39,160 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:06:39,160 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:06:39,160 - models.train - INFO - Creating target variable for horizon 30 minutes...
2025-07-18 10:06:39,160 - models.train - INFO - Converting 30 minutes to approximately 1 days for target shifting
2025-07-18 10:06:39,161 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:06:39,161 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:06:39,163 - models.train - INFO - Data shape after dropping NaN rows: (1249, 39)
2025-07-18 10:06:39,163 - models.train - INFO - Preprocessing data...
2025-07-18 10:06:39,362 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:06:39,362 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:06:39,620 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:06:39,952 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:06:39,952 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.26s
2025-07-18 10:06:39,952 - models.train - INFO - Saving scaler for horizon 30...
2025-07-18 10:06:40,141 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:06:40,143 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 10:06:40,145 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_30min.joblib (0.53 KB)
2025-07-18 10:06:40,148 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler30min.joblib (0.53 KB)
2025-07-18 10:06:40,527 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:06:40,528 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:06:40,528 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_30_scaler.pkl', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler30min.joblib']
2025-07-18 10:06:40,528 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:06:40,528 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:06:40,528 - models.train - INFO - Initializing model for horizon 30...
2025-07-18 10:06:40,529 - models.train - INFO - Using scikit-learn rf model for 30 days horizon
2025-07-18 10:06:40,529 - models.train - INFO - Training model for horizon 30...
2025-07-18 10:06:40,529 - models.train - INFO - Training machine learning model (rf) for horizon 30. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:06:40,529 - models.sklearn_model - INFO - Training rf model for 30 minutes horizon
2025-07-18 10:06:40,529 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:06:40,529 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:07:00,418 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:07:00,460 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_30min.joblib
2025-07-18 10:07:00,461 - models.train - INFO - Machine learning model (rf) training completed for horizon 30
2025-07-18 10:07:00,461 - models.train - INFO - Saving model for horizon 30...
2025-07-18 10:07:00,503 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_30min.joblib
2025-07-18 10:07:00,503 - models.train - INFO - Model saved for horizon 30
2025-07-18 10:07:00,503 - models.train - INFO - Model for 30 days horizon trained and saved successfully
2025-07-18 10:07:00,503 - models.train - INFO - Training model for 60 minutes horizon
2025-07-18 10:07:00,503 - models.train - INFO - Data shape before processing: (1250, 39)
2025-07-18 10:07:00,505 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:07:00,506 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:07:00,506 - models.train - INFO - Creating target variable for horizon 60 minutes...
2025-07-18 10:07:00,506 - models.train - INFO - Converting 60 minutes to approximately 1 days for target shifting
2025-07-18 10:07:00,506 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:07:00,506 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:07:00,508 - models.train - INFO - Data shape after dropping NaN rows: (1249, 40)
2025-07-18 10:07:00,508 - models.train - INFO - Preprocessing data...
2025-07-18 10:07:00,705 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:00,706 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:07:00,903 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:07:01,093 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:01,093 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:07:01,094 - models.train - INFO - Saving scaler for horizon 60...
2025-07-18 10:07:01,285 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:01,290 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 10:07:01,292 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_60min.joblib (0.53 KB)
2025-07-18 10:07:01,293 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler60min.joblib (0.53 KB)
2025-07-18 10:07:01,668 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:01,668 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:07:01,668 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_60_scaler.pkl', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler60min.joblib']
2025-07-18 10:07:01,669 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:07:01,669 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:07:01,669 - models.train - INFO - Initializing model for horizon 60...
2025-07-18 10:07:01,670 - models.train - INFO - Using scikit-learn rf model for 60 days horizon
2025-07-18 10:07:01,670 - models.train - INFO - Training model for horizon 60...
2025-07-18 10:07:01,670 - models.train - INFO - Training machine learning model (rf) for horizon 60. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:07:01,670 - models.sklearn_model - INFO - Training rf model for 60 minutes horizon
2025-07-18 10:07:01,670 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:07:01,671 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:07:21,621 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:07:21,662 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_60min.joblib
2025-07-18 10:07:21,662 - models.train - INFO - Machine learning model (rf) training completed for horizon 60
2025-07-18 10:07:21,662 - models.train - INFO - Saving model for horizon 60...
2025-07-18 10:07:21,702 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_60min.joblib
2025-07-18 10:07:21,702 - models.train - INFO - Model saved for horizon 60
2025-07-18 10:07:21,702 - models.train - INFO - Model for 60 days horizon trained and saved successfully
2025-07-18 10:07:21,702 - models.train - INFO - Successfully trained 4 models
2025-07-18 10:07:21,711 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:21,711 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:07:21,905 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-18 10:07:21,939 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:21,939 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:07:26,766 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:07:26,794 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:26,795 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:07:27,002 - app.utils.memory_management - INFO - Garbage collection: collected 802 objects
2025-07-18 10:07:27,002 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:27,003 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:07:30,111 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:07:30,153 - app - INFO - File COMI contains 2025 data
2025-07-18 10:07:30,228 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-18 10:07:30,231 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 10:07:30,233 - models.train - INFO - Starting training from CSV: data/stocks/COMI.csv
2025-07-18 10:07:30,233 - models.train - INFO - Symbol: COMI, Model type: rf
2025-07-18 10:07:30,233 - models.train - INFO - Horizons: [1440, 2880, 4320, 7200, 10080] (in minutes)
2025-07-18 10:07:30,233 - models.train - INFO - Loading data from data/stocks/COMI.csv...
2025-07-18 10:07:30,256 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-18 10:07:30,257 - models.train - INFO - Converting Date column to datetime...
2025-07-18 10:07:30,262 - models.train - INFO - Starting model training...
2025-07-18 10:07:30,262 - models.train - INFO - Training models for symbol: COMI
2025-07-18 10:07:30,263 - models.train - INFO - Model type: rf
2025-07-18 10:07:30,264 - models.train - INFO - Horizons: [1440, 2880, 4320, 7200, 10080]
2025-07-18 10:07:30,264 - models.train - INFO - Sequence length: 60
2025-07-18 10:07:30,265 - models.train - INFO - Epochs: 1
2025-07-18 10:07:30,266 - models.train - INFO - Batch size: 32
2025-07-18 10:07:30,266 - models.train - INFO - Data shape: (1250, 36)
2025-07-18 10:07:30,266 - models.train - INFO - Preparing features...
2025-07-18 10:07:30,297 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-18 10:07:30,297 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-18 10:07:30,298 - models.train - INFO - Training model for 1440 minutes horizon
2025-07-18 10:07:30,298 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-18 10:07:30,301 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:07:30,301 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:07:30,302 - models.train - INFO - Creating target variable for horizon 1440 minutes...
2025-07-18 10:07:30,304 - models.train - INFO - Converting 1440 minutes to approximately 1 days for target shifting
2025-07-18 10:07:30,305 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:07:30,305 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:07:30,308 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-18 10:07:30,308 - models.train - INFO - Preprocessing data...
2025-07-18 10:07:30,527 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:30,527 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:07:30,741 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:07:30,932 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:30,932 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-18 10:07:30,933 - models.train - INFO - Saving scaler for horizon 1440...
2025-07-18 10:07:31,119 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:31,121 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 10:07:31,123 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_1440min.joblib (0.53 KB)
2025-07-18 10:07:31,125 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler1440min.joblib (0.53 KB)
2025-07-18 10:07:31,511 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:31,512 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:07:31,512 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_1440_scaler.pkl', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler1440min.joblib']
2025-07-18 10:07:31,512 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:07:31,512 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:07:31,512 - models.train - INFO - Initializing model for horizon 1440...
2025-07-18 10:07:31,512 - models.train - INFO - Using scikit-learn rf model for 1440 days horizon
2025-07-18 10:07:31,512 - models.train - INFO - Training model for horizon 1440...
2025-07-18 10:07:31,512 - models.train - INFO - Training machine learning model (rf) for horizon 1440. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:07:31,512 - models.sklearn_model - INFO - Training rf model for 1440 minutes horizon
2025-07-18 10:07:31,512 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:07:31,513 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:07:52,272 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:07:52,316 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_1440min.joblib
2025-07-18 10:07:52,316 - models.train - INFO - Machine learning model (rf) training completed for horizon 1440
2025-07-18 10:07:52,317 - models.train - INFO - Saving model for horizon 1440...
2025-07-18 10:07:52,357 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_1440min.joblib
2025-07-18 10:07:52,357 - models.train - INFO - Model saved for horizon 1440
2025-07-18 10:07:52,357 - models.train - INFO - Model for 1440 days horizon trained and saved successfully
2025-07-18 10:07:52,357 - models.train - INFO - Training model for 2880 minutes horizon
2025-07-18 10:07:52,357 - models.train - INFO - Data shape before processing: (1250, 37)
2025-07-18 10:07:52,359 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:07:52,359 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:07:52,359 - models.train - INFO - Creating target variable for horizon 2880 minutes...
2025-07-18 10:07:52,359 - models.train - INFO - Converting 2880 minutes to approximately 2 days for target shifting
2025-07-18 10:07:52,360 - models.train - INFO - Created target variable. Valid targets: 1248/1250
2025-07-18 10:07:52,360 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:07:52,361 - models.train - INFO - Data shape after dropping NaN rows: (1248, 38)
2025-07-18 10:07:52,362 - models.train - INFO - Preprocessing data...
2025-07-18 10:07:52,560 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:52,560 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:07:52,757 - app.utils.data_processing - INFO - Created sequences: X shape=(1188, 60, 7), memory=1.90 MB; y shape=(1188,), memory=0.00 MB
2025-07-18 10:07:52,948 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:52,949 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:07:52,949 - models.train - INFO - Saving scaler for horizon 2880...
2025-07-18 10:07:53,141 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:53,145 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_2880_scaler.pkl (0.53 KB)
2025-07-18 10:07:53,146 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_2880min.joblib (0.53 KB)
2025-07-18 10:07:53,149 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler2880min.joblib (0.53 KB)
2025-07-18 10:07:53,531 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:07:53,531 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:07:53,531 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_2880_scaler.pkl', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler2880min.joblib']
2025-07-18 10:07:53,532 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:07:53,532 - models.train - INFO - Training data shape: (950, 60, 7)
2025-07-18 10:07:53,532 - models.train - INFO - Initializing model for horizon 2880...
2025-07-18 10:07:53,532 - models.train - INFO - Using scikit-learn rf model for 2880 days horizon
2025-07-18 10:07:53,533 - models.train - INFO - Training model for horizon 2880...
2025-07-18 10:07:53,533 - models.train - INFO - Training machine learning model (rf) for horizon 2880. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:07:53,533 - models.sklearn_model - INFO - Training rf model for 2880 minutes horizon
2025-07-18 10:07:53,533 - models.sklearn_model - INFO - Reshaping 3D input with shape (950, 60, 7) to 2D
2025-07-18 10:07:53,533 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:08:13,922 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:08:13,965 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_2880min.joblib
2025-07-18 10:08:13,965 - models.train - INFO - Machine learning model (rf) training completed for horizon 2880
2025-07-18 10:08:13,965 - models.train - INFO - Saving model for horizon 2880...
2025-07-18 10:08:14,005 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_2880min.joblib
2025-07-18 10:08:14,005 - models.train - INFO - Model saved for horizon 2880
2025-07-18 10:08:14,005 - models.train - INFO - Model for 2880 days horizon trained and saved successfully
2025-07-18 10:08:14,005 - models.train - INFO - Training model for 4320 minutes horizon
2025-07-18 10:08:14,005 - models.train - INFO - Data shape before processing: (1250, 38)
2025-07-18 10:08:14,007 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:08:14,007 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:08:14,007 - models.train - INFO - Creating target variable for horizon 4320 minutes...
2025-07-18 10:08:14,007 - models.train - INFO - Converting 4320 minutes to approximately 3 days for target shifting
2025-07-18 10:08:14,008 - models.train - INFO - Created target variable. Valid targets: 1247/1250
2025-07-18 10:08:14,008 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:08:14,010 - models.train - INFO - Data shape after dropping NaN rows: (1247, 39)
2025-07-18 10:08:14,010 - models.train - INFO - Preprocessing data...
2025-07-18 10:08:14,224 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:14,224 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:08:14,428 - app.utils.data_processing - INFO - Created sequences: X shape=(1187, 60, 7), memory=1.90 MB; y shape=(1187,), memory=0.00 MB
2025-07-18 10:08:14,775 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:14,775 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:08:14,776 - models.train - INFO - Saving scaler for horizon 4320...
2025-07-18 10:08:14,964 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:14,965 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_4320_scaler.pkl (0.53 KB)
2025-07-18 10:08:14,967 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_4320min.joblib (0.53 KB)
2025-07-18 10:08:14,968 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler4320min.joblib (0.53 KB)
2025-07-18 10:08:15,347 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:15,347 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-18 10:08:15,347 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_4320_scaler.pkl', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler4320min.joblib']
2025-07-18 10:08:15,347 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:08:15,348 - models.train - INFO - Training data shape: (949, 60, 7)
2025-07-18 10:08:15,348 - models.train - INFO - Initializing model for horizon 4320...
2025-07-18 10:08:15,348 - models.train - INFO - Using scikit-learn rf model for 4320 days horizon
2025-07-18 10:08:15,348 - models.train - INFO - Training model for horizon 4320...
2025-07-18 10:08:15,348 - models.train - INFO - Training machine learning model (rf) for horizon 4320. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:08:15,348 - models.sklearn_model - INFO - Training rf model for 4320 minutes horizon
2025-07-18 10:08:15,348 - models.sklearn_model - INFO - Reshaping 3D input with shape (949, 60, 7) to 2D
2025-07-18 10:08:15,348 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:08:36,906 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:08:36,949 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_4320min.joblib
2025-07-18 10:08:36,949 - models.train - INFO - Machine learning model (rf) training completed for horizon 4320
2025-07-18 10:08:36,949 - models.train - INFO - Saving model for horizon 4320...
2025-07-18 10:08:36,988 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_4320min.joblib
2025-07-18 10:08:36,988 - models.train - INFO - Model saved for horizon 4320
2025-07-18 10:08:36,988 - models.train - INFO - Model for 4320 days horizon trained and saved successfully
2025-07-18 10:08:36,988 - models.train - INFO - Training model for 7200 minutes horizon
2025-07-18 10:08:36,988 - models.train - INFO - Data shape before processing: (1250, 39)
2025-07-18 10:08:36,990 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:08:36,990 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:08:36,990 - models.train - INFO - Creating target variable for horizon 7200 minutes...
2025-07-18 10:08:36,990 - models.train - INFO - Converting 7200 minutes to approximately 5 days for target shifting
2025-07-18 10:08:36,991 - models.train - INFO - Created target variable. Valid targets: 1245/1250
2025-07-18 10:08:36,991 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:08:36,993 - models.train - INFO - Data shape after dropping NaN rows: (1245, 40)
2025-07-18 10:08:36,993 - models.train - INFO - Preprocessing data...
2025-07-18 10:08:37,200 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:37,200 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:08:37,397 - app.utils.data_processing - INFO - Created sequences: X shape=(1185, 60, 7), memory=1.90 MB; y shape=(1185,), memory=0.00 MB
2025-07-18 10:08:37,587 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:37,588 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:08:37,588 - models.train - INFO - Saving scaler for horizon 7200...
2025-07-18 10:08:37,779 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:37,783 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_7200_scaler.pkl (0.53 KB)
2025-07-18 10:08:37,785 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_7200min.joblib (0.53 KB)
2025-07-18 10:08:37,787 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler7200min.joblib (0.53 KB)
2025-07-18 10:08:38,165 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:38,165 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:08:38,165 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_7200_scaler.pkl', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler7200min.joblib']
2025-07-18 10:08:38,165 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:08:38,166 - models.train - INFO - Training data shape: (948, 60, 7)
2025-07-18 10:08:38,166 - models.train - INFO - Initializing model for horizon 7200...
2025-07-18 10:08:38,166 - models.train - INFO - Using scikit-learn rf model for 7200 days horizon
2025-07-18 10:08:38,166 - models.train - INFO - Training model for horizon 7200...
2025-07-18 10:08:38,166 - models.train - INFO - Training machine learning model (rf) for horizon 7200. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:08:38,166 - models.sklearn_model - INFO - Training rf model for 7200 minutes horizon
2025-07-18 10:08:38,166 - models.sklearn_model - INFO - Reshaping 3D input with shape (948, 60, 7) to 2D
2025-07-18 10:08:38,166 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:08:58,106 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:08:58,149 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_7200min.joblib
2025-07-18 10:08:58,150 - models.train - INFO - Machine learning model (rf) training completed for horizon 7200
2025-07-18 10:08:58,150 - models.train - INFO - Saving model for horizon 7200...
2025-07-18 10:08:58,188 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_7200min.joblib
2025-07-18 10:08:58,188 - models.train - INFO - Model saved for horizon 7200
2025-07-18 10:08:58,189 - models.train - INFO - Model for 7200 days horizon trained and saved successfully
2025-07-18 10:08:58,189 - models.train - INFO - Training model for 10080 minutes horizon
2025-07-18 10:08:58,189 - models.train - INFO - Data shape before processing: (1250, 40)
2025-07-18 10:08:58,191 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:08:58,191 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:08:58,191 - models.train - INFO - Creating target variable for horizon 10080 minutes...
2025-07-18 10:08:58,191 - models.train - INFO - Converting 10080 minutes to approximately 7 days for target shifting
2025-07-18 10:08:58,192 - models.train - INFO - Created target variable. Valid targets: 1243/1250
2025-07-18 10:08:58,192 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:08:58,194 - models.train - INFO - Data shape after dropping NaN rows: (1243, 41)
2025-07-18 10:08:58,194 - models.train - INFO - Preprocessing data...
2025-07-18 10:08:58,394 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:58,394 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:08:58,607 - app.utils.data_processing - INFO - Created sequences: X shape=(1183, 60, 7), memory=1.90 MB; y shape=(1183,), memory=0.00 MB
2025-07-18 10:08:58,953 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:58,953 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-18 10:08:58,954 - models.train - INFO - Saving scaler for horizon 10080...
2025-07-18 10:08:59,144 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:59,145 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_10080_scaler.pkl (0.54 KB)
2025-07-18 10:08:59,147 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_10080min.joblib (0.54 KB)
2025-07-18 10:08:59,148 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler10080min.joblib (0.54 KB)
2025-07-18 10:08:59,528 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:08:59,529 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-18 10:08:59,529 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_10080_scaler.pkl', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler10080min.joblib']
2025-07-18 10:08:59,529 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:08:59,530 - models.train - INFO - Training data shape: (946, 60, 7)
2025-07-18 10:08:59,530 - models.train - INFO - Initializing model for horizon 10080...
2025-07-18 10:08:59,530 - models.train - INFO - Using scikit-learn rf model for 10080 days horizon
2025-07-18 10:08:59,530 - models.train - INFO - Training model for horizon 10080...
2025-07-18 10:08:59,530 - models.train - INFO - Training machine learning model (rf) for horizon 10080. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:08:59,531 - models.sklearn_model - INFO - Training rf model for 10080 minutes horizon
2025-07-18 10:08:59,531 - models.sklearn_model - INFO - Reshaping 3D input with shape (946, 60, 7) to 2D
2025-07-18 10:08:59,531 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:09:19,781 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:09:19,825 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_10080min.joblib
2025-07-18 10:09:19,825 - models.train - INFO - Machine learning model (rf) training completed for horizon 10080
2025-07-18 10:09:19,825 - models.train - INFO - Saving model for horizon 10080...
2025-07-18 10:09:19,865 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_10080min.joblib
2025-07-18 10:09:19,865 - models.train - INFO - Model saved for horizon 10080
2025-07-18 10:09:19,865 - models.train - INFO - Model for 10080 days horizon trained and saved successfully
2025-07-18 10:09:19,866 - models.train - INFO - Successfully trained 5 models
2025-07-18 10:09:19,875 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:19,875 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:09:20,106 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-18 10:09:20,107 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:20,141 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:09:23,171 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:09:23,195 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:23,196 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:09:23,424 - app.utils.memory_management - INFO - Garbage collection: collected 802 objects
2025-07-18 10:09:23,425 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:23,426 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:09:28,068 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:09:28,113 - app - INFO - File COMI contains 2025 data
2025-07-18 10:09:28,207 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-18 10:09:28,208 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 10:09:28,209 - models.train - INFO - Starting training from CSV: data/stocks/COMI.csv
2025-07-18 10:09:28,210 - models.train - INFO - Symbol: COMI, Model type: rf
2025-07-18 10:09:28,214 - models.train - INFO - Horizons: [10080, 20160, 40320, 80640, 120960] (in minutes)
2025-07-18 10:09:28,214 - models.train - INFO - Loading data from data/stocks/COMI.csv...
2025-07-18 10:09:28,230 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-18 10:09:28,230 - models.train - INFO - Converting Date column to datetime...
2025-07-18 10:09:28,232 - models.train - INFO - Starting model training...
2025-07-18 10:09:28,233 - models.train - INFO - Training models for symbol: COMI
2025-07-18 10:09:28,233 - models.train - INFO - Model type: rf
2025-07-18 10:09:28,233 - models.train - INFO - Horizons: [10080, 20160, 40320, 80640, 120960]
2025-07-18 10:09:28,234 - models.train - INFO - Sequence length: 60
2025-07-18 10:09:28,234 - models.train - INFO - Epochs: 1
2025-07-18 10:09:28,234 - models.train - INFO - Batch size: 32
2025-07-18 10:09:28,234 - models.train - INFO - Data shape: (1250, 36)
2025-07-18 10:09:28,234 - models.train - INFO - Preparing features...
2025-07-18 10:09:28,264 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-18 10:09:28,264 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-18 10:09:28,265 - models.train - INFO - Training model for 10080 minutes horizon
2025-07-18 10:09:28,265 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-18 10:09:28,266 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:09:28,267 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:09:28,267 - models.train - INFO - Creating target variable for horizon 10080 minutes...
2025-07-18 10:09:28,267 - models.train - INFO - Converting 10080 minutes to approximately 7 days for target shifting
2025-07-18 10:09:28,268 - models.train - INFO - Created target variable. Valid targets: 1243/1250
2025-07-18 10:09:28,268 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:09:28,269 - models.train - INFO - Data shape after dropping NaN rows: (1243, 37)
2025-07-18 10:09:28,269 - models.train - INFO - Preprocessing data...
2025-07-18 10:09:28,477 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:28,477 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:09:28,694 - app.utils.data_processing - INFO - Created sequences: X shape=(1183, 60, 7), memory=1.90 MB; y shape=(1183,), memory=0.00 MB
2025-07-18 10:09:28,899 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:28,900 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.22s
2025-07-18 10:09:28,901 - models.train - INFO - Saving scaler for horizon 10080...
2025-07-18 10:09:29,098 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:29,101 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_10080_scaler.pkl (0.54 KB)
2025-07-18 10:09:29,102 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_10080min.joblib (0.54 KB)
2025-07-18 10:09:29,104 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler10080min.joblib (0.54 KB)
2025-07-18 10:09:29,524 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:29,525 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-18 10:09:29,525 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_10080_scaler.pkl', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler10080min.joblib']
2025-07-18 10:09:29,525 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:09:29,525 - models.train - INFO - Training data shape: (946, 60, 7)
2025-07-18 10:09:29,525 - models.train - INFO - Initializing model for horizon 10080...
2025-07-18 10:09:29,525 - models.train - INFO - Using scikit-learn rf model for 10080 days horizon
2025-07-18 10:09:29,525 - models.train - INFO - Training model for horizon 10080...
2025-07-18 10:09:29,525 - models.train - INFO - Training machine learning model (rf) for horizon 10080. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:09:29,526 - models.sklearn_model - INFO - Training rf model for 10080 minutes horizon
2025-07-18 10:09:29,526 - models.sklearn_model - INFO - Reshaping 3D input with shape (946, 60, 7) to 2D
2025-07-18 10:09:29,526 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:09:49,233 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:09:49,273 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_10080min.joblib
2025-07-18 10:09:49,273 - models.train - INFO - Machine learning model (rf) training completed for horizon 10080
2025-07-18 10:09:49,273 - models.train - INFO - Saving model for horizon 10080...
2025-07-18 10:09:49,312 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_10080min.joblib
2025-07-18 10:09:49,312 - models.train - INFO - Model saved for horizon 10080
2025-07-18 10:09:49,312 - models.train - INFO - Model for 10080 days horizon trained and saved successfully
2025-07-18 10:09:49,313 - models.train - INFO - Training model for 20160 minutes horizon
2025-07-18 10:09:49,313 - models.train - INFO - Data shape before processing: (1250, 37)
2025-07-18 10:09:49,314 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:09:49,314 - models.train - INFO - Minimum required rows: 75
2025-07-18 10:09:49,314 - models.train - INFO - Creating target variable for horizon 20160 minutes...
2025-07-18 10:09:49,315 - models.train - INFO - Converting 20160 minutes to approximately 14 days for target shifting
2025-07-18 10:09:49,315 - models.train - INFO - Created target variable. Valid targets: 1236/1250
2025-07-18 10:09:49,315 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:09:49,317 - models.train - INFO - Data shape after dropping NaN rows: (1236, 38)
2025-07-18 10:09:49,317 - models.train - INFO - Preprocessing data...
2025-07-18 10:09:49,516 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:49,516 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:09:49,721 - app.utils.data_processing - INFO - Created sequences: X shape=(1176, 60, 7), memory=1.88 MB; y shape=(1176,), memory=0.00 MB
2025-07-18 10:09:50,070 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:50,071 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-18 10:09:50,071 - models.train - INFO - Saving scaler for horizon 20160...
2025-07-18 10:09:50,260 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:50,263 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_20160_scaler.pkl (0.53 KB)
2025-07-18 10:09:50,264 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_20160min.joblib (0.53 KB)
2025-07-18 10:09:50,265 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler20160min.joblib (0.53 KB)
2025-07-18 10:09:50,643 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:09:50,643 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-18 10:09:50,643 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_20160_scaler.pkl', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler20160min.joblib']
2025-07-18 10:09:50,644 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:09:50,644 - models.train - INFO - Training data shape: (940, 60, 7)
2025-07-18 10:09:50,644 - models.train - INFO - Initializing model for horizon 20160...
2025-07-18 10:09:50,644 - models.train - INFO - Using scikit-learn rf model for 20160 days horizon
2025-07-18 10:09:50,644 - models.train - INFO - Training model for horizon 20160...
2025-07-18 10:09:50,644 - models.train - INFO - Training machine learning model (rf) for horizon 20160. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:09:50,644 - models.sklearn_model - INFO - Training rf model for 20160 minutes horizon
2025-07-18 10:09:50,644 - models.sklearn_model - INFO - Reshaping 3D input with shape (940, 60, 7) to 2D
2025-07-18 10:09:50,645 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:10:10,119 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:10:10,161 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_20160min.joblib
2025-07-18 10:10:10,161 - models.train - INFO - Machine learning model (rf) training completed for horizon 20160
2025-07-18 10:10:10,161 - models.train - INFO - Saving model for horizon 20160...
2025-07-18 10:10:10,200 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_20160min.joblib
2025-07-18 10:10:10,201 - models.train - INFO - Model saved for horizon 20160
2025-07-18 10:10:10,201 - models.train - INFO - Model for 20160 days horizon trained and saved successfully
2025-07-18 10:10:10,201 - models.train - INFO - Training model for 40320 minutes horizon
2025-07-18 10:10:10,201 - models.train - INFO - Data shape before processing: (1250, 38)
2025-07-18 10:10:10,203 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:10:10,203 - models.train - INFO - Minimum required rows: 89
2025-07-18 10:10:10,203 - models.train - INFO - Creating target variable for horizon 40320 minutes...
2025-07-18 10:10:10,203 - models.train - INFO - Converting 40320 minutes to approximately 28 days for target shifting
2025-07-18 10:10:10,203 - models.train - INFO - Created target variable. Valid targets: 1222/1250
2025-07-18 10:10:10,203 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:10:10,205 - models.train - INFO - Data shape after dropping NaN rows: (1222, 39)
2025-07-18 10:10:10,205 - models.train - INFO - Preprocessing data...
2025-07-18 10:10:10,400 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:10,401 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:10:10,597 - app.utils.data_processing - INFO - Created sequences: X shape=(1162, 60, 7), memory=1.86 MB; y shape=(1162,), memory=0.00 MB
2025-07-18 10:10:10,789 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:10,789 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:10:10,789 - models.train - INFO - Saving scaler for horizon 40320...
2025-07-18 10:10:10,981 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:10,985 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_40320_scaler.pkl (0.53 KB)
2025-07-18 10:10:10,987 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_40320min.joblib (0.53 KB)
2025-07-18 10:10:10,989 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler40320min.joblib (0.53 KB)
2025-07-18 10:10:11,366 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:11,366 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:10:11,367 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_40320_scaler.pkl', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler40320min.joblib']
2025-07-18 10:10:11,367 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:10:11,367 - models.train - INFO - Training data shape: (929, 60, 7)
2025-07-18 10:10:11,367 - models.train - INFO - Initializing model for horizon 40320...
2025-07-18 10:10:11,367 - models.train - INFO - Using scikit-learn rf model for 40320 days horizon
2025-07-18 10:10:11,367 - models.train - INFO - Training model for horizon 40320...
2025-07-18 10:10:11,367 - models.train - INFO - Training machine learning model (rf) for horizon 40320. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:10:11,367 - models.sklearn_model - INFO - Training rf model for 40320 minutes horizon
2025-07-18 10:10:11,367 - models.sklearn_model - INFO - Reshaping 3D input with shape (929, 60, 7) to 2D
2025-07-18 10:10:11,368 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:10:30,997 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:10:31,039 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_40320min.joblib
2025-07-18 10:10:31,040 - models.train - INFO - Machine learning model (rf) training completed for horizon 40320
2025-07-18 10:10:31,040 - models.train - INFO - Saving model for horizon 40320...
2025-07-18 10:10:31,080 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_40320min.joblib
2025-07-18 10:10:31,080 - models.train - INFO - Model saved for horizon 40320
2025-07-18 10:10:31,080 - models.train - INFO - Model for 40320 days horizon trained and saved successfully
2025-07-18 10:10:31,081 - models.train - INFO - Training model for 80640 minutes horizon
2025-07-18 10:10:31,081 - models.train - INFO - Data shape before processing: (1250, 39)
2025-07-18 10:10:31,082 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:10:31,083 - models.train - INFO - Minimum required rows: 117
2025-07-18 10:10:31,083 - models.train - INFO - Creating target variable for horizon 80640 minutes...
2025-07-18 10:10:31,083 - models.train - INFO - Converting 80640 minutes to approximately 56 days for target shifting
2025-07-18 10:10:31,083 - models.train - INFO - Created target variable. Valid targets: 1194/1250
2025-07-18 10:10:31,083 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:10:31,085 - models.train - INFO - Data shape after dropping NaN rows: (1194, 40)
2025-07-18 10:10:31,085 - models.train - INFO - Preprocessing data...
2025-07-18 10:10:31,280 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:31,280 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:10:31,481 - app.utils.data_processing - INFO - Created sequences: X shape=(1134, 60, 7), memory=1.82 MB; y shape=(1134,), memory=0.00 MB
2025-07-18 10:10:31,832 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:31,832 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:10:31,832 - models.train - INFO - Saving scaler for horizon 80640...
2025-07-18 10:10:32,021 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:32,023 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_80640_scaler.pkl (0.53 KB)
2025-07-18 10:10:32,025 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_80640min.joblib (0.53 KB)
2025-07-18 10:10:32,026 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler80640min.joblib (0.53 KB)
2025-07-18 10:10:32,405 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:32,406 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-18 10:10:32,406 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_80640_scaler.pkl', 'COMI_rf_scaler_80640min.joblib', 'COMI_rf_scaler80640min.joblib']
2025-07-18 10:10:32,406 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:10:32,406 - models.train - INFO - Training data shape: (907, 60, 7)
2025-07-18 10:10:32,406 - models.train - INFO - Initializing model for horizon 80640...
2025-07-18 10:10:32,406 - models.train - INFO - Using scikit-learn rf model for 80640 days horizon
2025-07-18 10:10:32,406 - models.train - INFO - Training model for horizon 80640...
2025-07-18 10:10:32,406 - models.train - INFO - Training machine learning model (rf) for horizon 80640. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:10:32,407 - models.sklearn_model - INFO - Training rf model for 80640 minutes horizon
2025-07-18 10:10:32,407 - models.sklearn_model - INFO - Reshaping 3D input with shape (907, 60, 7) to 2D
2025-07-18 10:10:32,407 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:10:52,035 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:10:52,078 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_80640min.joblib
2025-07-18 10:10:52,078 - models.train - INFO - Machine learning model (rf) training completed for horizon 80640
2025-07-18 10:10:52,078 - models.train - INFO - Saving model for horizon 80640...
2025-07-18 10:10:52,117 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_80640min.joblib
2025-07-18 10:10:52,118 - models.train - INFO - Model saved for horizon 80640
2025-07-18 10:10:52,118 - models.train - INFO - Model for 80640 days horizon trained and saved successfully
2025-07-18 10:10:52,118 - models.train - INFO - Training model for 120960 minutes horizon
2025-07-18 10:10:52,118 - models.train - INFO - Data shape before processing: (1250, 40)
2025-07-18 10:10:52,120 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:10:52,120 - models.train - INFO - Minimum required rows: 145
2025-07-18 10:10:52,120 - models.train - INFO - Creating target variable for horizon 120960 minutes...
2025-07-18 10:10:52,120 - models.train - INFO - Converting 120960 minutes to approximately 84 days for target shifting
2025-07-18 10:10:52,121 - models.train - INFO - Created target variable. Valid targets: 1166/1250
2025-07-18 10:10:52,121 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:10:52,122 - models.train - INFO - Data shape after dropping NaN rows: (1166, 41)
2025-07-18 10:10:52,122 - models.train - INFO - Preprocessing data...
2025-07-18 10:10:52,325 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:52,325 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:10:52,521 - app.utils.data_processing - INFO - Created sequences: X shape=(1106, 60, 7), memory=1.77 MB; y shape=(1106,), memory=0.00 MB
2025-07-18 10:10:52,712 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:52,713 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:10:52,713 - models.train - INFO - Saving scaler for horizon 120960...
2025-07-18 10:10:52,903 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:52,905 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_120960_scaler.pkl (0.53 KB)
2025-07-18 10:10:52,906 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler_120960min.joblib (0.53 KB)
2025-07-18 10:10:52,908 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_rf_scaler120960min.joblib (0.53 KB)
2025-07-18 10:10:53,284 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:10:53,284 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-18 10:10:53,284 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_120960_scaler.pkl', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler120960min.joblib']
2025-07-18 10:10:53,285 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:10:53,285 - models.train - INFO - Training data shape: (884, 60, 7)
2025-07-18 10:10:53,285 - models.train - INFO - Initializing model for horizon 120960...
2025-07-18 10:10:53,285 - models.train - INFO - Using scikit-learn rf model for 120960 days horizon
2025-07-18 10:10:53,285 - models.train - INFO - Training model for horizon 120960...
2025-07-18 10:10:53,285 - models.train - INFO - Training machine learning model (rf) for horizon 120960. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:10:53,285 - models.sklearn_model - INFO - Training rf model for 120960 minutes horizon
2025-07-18 10:10:53,285 - models.sklearn_model - INFO - Reshaping 3D input with shape (884, 60, 7) to 2D
2025-07-18 10:10:53,286 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-18 10:11:11,419 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-18 10:11:11,464 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_120960min.joblib
2025-07-18 10:11:11,464 - models.train - INFO - Machine learning model (rf) training completed for horizon 120960
2025-07-18 10:11:11,465 - models.train - INFO - Saving model for horizon 120960...
2025-07-18 10:11:11,503 - models.sklearn_model - INFO - Successfully saved rf model to COMI_rf_120960min.joblib
2025-07-18 10:11:11,503 - models.train - INFO - Model saved for horizon 120960
2025-07-18 10:11:11,504 - models.train - INFO - Model for 120960 days horizon trained and saved successfully
2025-07-18 10:11:11,504 - models.train - INFO - Successfully trained 5 models
2025-07-18 10:11:11,514 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:11,514 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:11:11,712 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-18 10:11:11,713 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:11,714 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:11:27,181 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:11:27,198 - app - INFO - Found 14 stock files in data/stocks
2025-07-18 10:11:27,217 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:27,217 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:11:27,462 - app.utils.memory_management - INFO - Garbage collection: collected 802 objects
2025-07-18 10:11:27,463 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:27,463 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:11:33,491 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:11:33,543 - app - INFO - File COMI contains 2025 data
2025-07-18 10:11:33,621 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-18 10:11:33,622 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 10:11:33,623 - models.train - INFO - Starting training from CSV: data/stocks/COMI.csv
2025-07-18 10:11:33,624 - models.train - INFO - Symbol: COMI, Model type: gb
2025-07-18 10:11:33,624 - models.train - INFO - Horizons: [10080, 20160, 40320, 80640, 120960] (in minutes)
2025-07-18 10:11:33,625 - models.train - INFO - Loading data from data/stocks/COMI.csv...
2025-07-18 10:11:33,658 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-18 10:11:33,658 - models.train - INFO - Converting Date column to datetime...
2025-07-18 10:11:33,660 - models.train - INFO - Starting model training...
2025-07-18 10:11:33,660 - models.train - INFO - Training models for symbol: COMI
2025-07-18 10:11:33,661 - models.train - INFO - Model type: gb
2025-07-18 10:11:33,661 - models.train - INFO - Horizons: [10080, 20160, 40320, 80640, 120960]
2025-07-18 10:11:33,661 - models.train - INFO - Sequence length: 60
2025-07-18 10:11:33,661 - models.train - INFO - Epochs: 1
2025-07-18 10:11:33,662 - models.train - INFO - Batch size: 32
2025-07-18 10:11:33,662 - models.train - INFO - Data shape: (1250, 36)
2025-07-18 10:11:33,662 - models.train - INFO - Preparing features...
2025-07-18 10:11:33,688 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-18 10:11:33,689 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-18 10:11:33,689 - models.train - INFO - Training model for 10080 minutes horizon
2025-07-18 10:11:33,689 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-18 10:11:33,693 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:11:33,693 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:11:33,694 - models.train - INFO - Creating target variable for horizon 10080 minutes...
2025-07-18 10:11:33,695 - models.train - INFO - Converting 10080 minutes to approximately 7 days for target shifting
2025-07-18 10:11:33,697 - models.train - INFO - Created target variable. Valid targets: 1243/1250
2025-07-18 10:11:33,698 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:11:33,699 - models.train - INFO - Data shape after dropping NaN rows: (1243, 37)
2025-07-18 10:11:33,699 - models.train - INFO - Preprocessing data...
2025-07-18 10:11:33,933 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:33,933 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:11:34,132 - app.utils.data_processing - INFO - Created sequences: X shape=(1183, 60, 7), memory=1.90 MB; y shape=(1183,), memory=0.00 MB
2025-07-18 10:11:34,336 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:34,337 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:11:34,337 - models.train - INFO - Saving scaler for horizon 10080...
2025-07-18 10:11:34,553 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:34,564 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_10080_scaler.pkl (0.54 KB)
2025-07-18 10:11:34,567 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_10080min.joblib (0.54 KB)
2025-07-18 10:11:34,568 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler10080min.joblib (0.54 KB)
2025-07-18 10:11:34,983 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:34,983 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.23s
2025-07-18 10:11:34,983 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_10080_scaler.pkl', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler10080min.joblib']
2025-07-18 10:11:34,984 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:11:34,984 - models.train - INFO - Training data shape: (946, 60, 7)
2025-07-18 10:11:34,984 - models.train - INFO - Initializing model for horizon 10080...
2025-07-18 10:11:34,984 - models.train - INFO - Using scikit-learn gb model for 10080 days horizon
2025-07-18 10:11:34,984 - models.train - INFO - Training model for horizon 10080...
2025-07-18 10:11:34,984 - models.train - INFO - Training machine learning model (gb) for horizon 10080. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:11:34,984 - models.sklearn_model - INFO - Training gb model for 10080 minutes horizon
2025-07-18 10:11:34,984 - models.sklearn_model - INFO - Reshaping 3D input with shape (946, 60, 7) to 2D
2025-07-18 10:11:34,984 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:11:39,870 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:11:39,878 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_10080min.joblib
2025-07-18 10:11:39,878 - models.train - INFO - Machine learning model (gb) training completed for horizon 10080
2025-07-18 10:11:39,878 - models.train - INFO - Saving model for horizon 10080...
2025-07-18 10:11:39,882 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_10080min.joblib
2025-07-18 10:11:39,882 - models.train - INFO - Model saved for horizon 10080
2025-07-18 10:11:39,882 - models.train - INFO - Model for 10080 days horizon trained and saved successfully
2025-07-18 10:11:39,882 - models.train - INFO - Training model for 20160 minutes horizon
2025-07-18 10:11:39,882 - models.train - INFO - Data shape before processing: (1250, 37)
2025-07-18 10:11:39,884 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:11:39,884 - models.train - INFO - Minimum required rows: 75
2025-07-18 10:11:39,884 - models.train - INFO - Creating target variable for horizon 20160 minutes...
2025-07-18 10:11:39,885 - models.train - INFO - Converting 20160 minutes to approximately 14 days for target shifting
2025-07-18 10:11:39,886 - models.train - INFO - Created target variable. Valid targets: 1236/1250
2025-07-18 10:11:39,886 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:11:39,888 - models.train - INFO - Data shape after dropping NaN rows: (1236, 38)
2025-07-18 10:11:39,888 - models.train - INFO - Preprocessing data...
2025-07-18 10:11:40,278 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:40,278 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:11:40,590 - app.utils.data_processing - INFO - Created sequences: X shape=(1176, 60, 7), memory=1.88 MB; y shape=(1176,), memory=0.00 MB
2025-07-18 10:11:40,887 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:40,887 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.31s
2025-07-18 10:11:40,888 - models.train - INFO - Saving scaler for horizon 20160...
2025-07-18 10:11:41,170 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:41,173 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_20160_scaler.pkl (0.53 KB)
2025-07-18 10:11:41,175 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_20160min.joblib (0.53 KB)
2025-07-18 10:11:41,183 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler20160min.joblib (0.53 KB)
2025-07-18 10:11:41,690 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:41,690 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.29s
2025-07-18 10:11:41,690 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_20160_scaler.pkl', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler20160min.joblib']
2025-07-18 10:11:41,690 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:11:41,690 - models.train - INFO - Training data shape: (940, 60, 7)
2025-07-18 10:11:41,691 - models.train - INFO - Initializing model for horizon 20160...
2025-07-18 10:11:41,691 - models.train - INFO - Using scikit-learn gb model for 20160 days horizon
2025-07-18 10:11:41,691 - models.train - INFO - Training model for horizon 20160...
2025-07-18 10:11:41,691 - models.train - INFO - Training machine learning model (gb) for horizon 20160. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:11:41,691 - models.sklearn_model - INFO - Training gb model for 20160 minutes horizon
2025-07-18 10:11:41,691 - models.sklearn_model - INFO - Reshaping 3D input with shape (940, 60, 7) to 2D
2025-07-18 10:11:41,691 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:11:48,745 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:11:48,749 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_20160min.joblib
2025-07-18 10:11:48,750 - models.train - INFO - Machine learning model (gb) training completed for horizon 20160
2025-07-18 10:11:48,751 - models.train - INFO - Saving model for horizon 20160...
2025-07-18 10:11:48,755 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_20160min.joblib
2025-07-18 10:11:48,755 - models.train - INFO - Model saved for horizon 20160
2025-07-18 10:11:48,756 - models.train - INFO - Model for 20160 days horizon trained and saved successfully
2025-07-18 10:11:48,757 - models.train - INFO - Training model for 40320 minutes horizon
2025-07-18 10:11:48,758 - models.train - INFO - Data shape before processing: (1250, 38)
2025-07-18 10:11:48,760 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:11:48,761 - models.train - INFO - Minimum required rows: 89
2025-07-18 10:11:48,761 - models.train - INFO - Creating target variable for horizon 40320 minutes...
2025-07-18 10:11:48,761 - models.train - INFO - Converting 40320 minutes to approximately 28 days for target shifting
2025-07-18 10:11:48,762 - models.train - INFO - Created target variable. Valid targets: 1222/1250
2025-07-18 10:11:48,762 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:11:48,765 - models.train - INFO - Data shape after dropping NaN rows: (1222, 39)
2025-07-18 10:11:48,765 - models.train - INFO - Preprocessing data...
2025-07-18 10:11:48,968 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:48,969 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:11:49,168 - app.utils.data_processing - INFO - Created sequences: X shape=(1162, 60, 7), memory=1.86 MB; y shape=(1162,), memory=0.00 MB
2025-07-18 10:11:49,381 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:49,381 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:11:49,382 - models.train - INFO - Saving scaler for horizon 40320...
2025-07-18 10:11:49,572 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:49,573 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_40320_scaler.pkl (0.53 KB)
2025-07-18 10:11:49,577 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_40320min.joblib (0.53 KB)
2025-07-18 10:11:49,579 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler40320min.joblib (0.53 KB)
2025-07-18 10:11:49,957 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:49,957 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:11:49,957 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_40320_scaler.pkl', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler40320min.joblib']
2025-07-18 10:11:49,957 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:11:49,957 - models.train - INFO - Training data shape: (929, 60, 7)
2025-07-18 10:11:49,957 - models.train - INFO - Initializing model for horizon 40320...
2025-07-18 10:11:49,957 - models.train - INFO - Using scikit-learn gb model for 40320 days horizon
2025-07-18 10:11:49,958 - models.train - INFO - Training model for horizon 40320...
2025-07-18 10:11:49,958 - models.train - INFO - Training machine learning model (gb) for horizon 40320. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:11:49,958 - models.sklearn_model - INFO - Training gb model for 40320 minutes horizon
2025-07-18 10:11:49,958 - models.sklearn_model - INFO - Reshaping 3D input with shape (929, 60, 7) to 2D
2025-07-18 10:11:49,958 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:11:54,630 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:11:54,633 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_40320min.joblib
2025-07-18 10:11:54,634 - models.train - INFO - Machine learning model (gb) training completed for horizon 40320
2025-07-18 10:11:54,634 - models.train - INFO - Saving model for horizon 40320...
2025-07-18 10:11:54,638 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_40320min.joblib
2025-07-18 10:11:54,638 - models.train - INFO - Model saved for horizon 40320
2025-07-18 10:11:54,639 - models.train - INFO - Model for 40320 days horizon trained and saved successfully
2025-07-18 10:11:54,639 - models.train - INFO - Training model for 80640 minutes horizon
2025-07-18 10:11:54,639 - models.train - INFO - Data shape before processing: (1250, 39)
2025-07-18 10:11:54,642 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:11:54,642 - models.train - INFO - Minimum required rows: 117
2025-07-18 10:11:54,642 - models.train - INFO - Creating target variable for horizon 80640 minutes...
2025-07-18 10:11:54,642 - models.train - INFO - Converting 80640 minutes to approximately 56 days for target shifting
2025-07-18 10:11:54,643 - models.train - INFO - Created target variable. Valid targets: 1194/1250
2025-07-18 10:11:54,643 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:11:54,646 - models.train - INFO - Data shape after dropping NaN rows: (1194, 40)
2025-07-18 10:11:54,646 - models.train - INFO - Preprocessing data...
2025-07-18 10:11:54,830 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:54,830 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:11:55,027 - app.utils.data_processing - INFO - Created sequences: X shape=(1134, 60, 7), memory=1.82 MB; y shape=(1134,), memory=0.00 MB
2025-07-18 10:11:55,217 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:55,217 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:11:55,218 - models.train - INFO - Saving scaler for horizon 80640...
2025-07-18 10:11:55,409 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:55,412 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_80640_scaler.pkl (0.53 KB)
2025-07-18 10:11:55,414 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_80640min.joblib (0.53 KB)
2025-07-18 10:11:55,417 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler80640min.joblib (0.53 KB)
2025-07-18 10:11:55,801 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:11:55,801 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:11:55,801 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_80640_scaler.pkl', 'COMI_gb_scaler_80640min.joblib', 'COMI_gb_scaler80640min.joblib']
2025-07-18 10:11:55,802 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:11:55,802 - models.train - INFO - Training data shape: (907, 60, 7)
2025-07-18 10:11:55,802 - models.train - INFO - Initializing model for horizon 80640...
2025-07-18 10:11:55,802 - models.train - INFO - Using scikit-learn gb model for 80640 days horizon
2025-07-18 10:11:55,803 - models.train - INFO - Training model for horizon 80640...
2025-07-18 10:11:55,803 - models.train - INFO - Training machine learning model (gb) for horizon 80640. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:11:55,803 - models.sklearn_model - INFO - Training gb model for 80640 minutes horizon
2025-07-18 10:11:55,804 - models.sklearn_model - INFO - Reshaping 3D input with shape (907, 60, 7) to 2D
2025-07-18 10:11:55,804 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:12:00,532 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:12:00,535 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_80640min.joblib
2025-07-18 10:12:00,535 - models.train - INFO - Machine learning model (gb) training completed for horizon 80640
2025-07-18 10:12:00,536 - models.train - INFO - Saving model for horizon 80640...
2025-07-18 10:12:00,540 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_80640min.joblib
2025-07-18 10:12:00,540 - models.train - INFO - Model saved for horizon 80640
2025-07-18 10:12:00,540 - models.train - INFO - Model for 80640 days horizon trained and saved successfully
2025-07-18 10:12:00,540 - models.train - INFO - Training model for 120960 minutes horizon
2025-07-18 10:12:00,540 - models.train - INFO - Data shape before processing: (1250, 40)
2025-07-18 10:12:00,542 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:12:00,542 - models.train - INFO - Minimum required rows: 145
2025-07-18 10:12:00,542 - models.train - INFO - Creating target variable for horizon 120960 minutes...
2025-07-18 10:12:00,543 - models.train - INFO - Converting 120960 minutes to approximately 84 days for target shifting
2025-07-18 10:12:00,543 - models.train - INFO - Created target variable. Valid targets: 1166/1250
2025-07-18 10:12:00,543 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:12:00,545 - models.train - INFO - Data shape after dropping NaN rows: (1166, 41)
2025-07-18 10:12:00,545 - models.train - INFO - Preprocessing data...
2025-07-18 10:12:00,732 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:00,732 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:12:00,930 - app.utils.data_processing - INFO - Created sequences: X shape=(1106, 60, 7), memory=1.77 MB; y shape=(1106,), memory=0.00 MB
2025-07-18 10:12:01,121 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:01,121 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:01,122 - models.train - INFO - Saving scaler for horizon 120960...
2025-07-18 10:12:01,313 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:01,316 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_120960_scaler.pkl (0.53 KB)
2025-07-18 10:12:01,318 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_120960min.joblib (0.53 KB)
2025-07-18 10:12:01,321 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler120960min.joblib (0.53 KB)
2025-07-18 10:12:01,703 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:01,703 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:01,704 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_120960_scaler.pkl', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler120960min.joblib']
2025-07-18 10:12:01,704 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:12:01,704 - models.train - INFO - Training data shape: (884, 60, 7)
2025-07-18 10:12:01,704 - models.train - INFO - Initializing model for horizon 120960...
2025-07-18 10:12:01,704 - models.train - INFO - Using scikit-learn gb model for 120960 days horizon
2025-07-18 10:12:01,704 - models.train - INFO - Training model for horizon 120960...
2025-07-18 10:12:01,705 - models.train - INFO - Training machine learning model (gb) for horizon 120960. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:12:01,705 - models.sklearn_model - INFO - Training gb model for 120960 minutes horizon
2025-07-18 10:12:01,705 - models.sklearn_model - INFO - Reshaping 3D input with shape (884, 60, 7) to 2D
2025-07-18 10:12:01,705 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:12:06,934 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:12:06,937 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_120960min.joblib
2025-07-18 10:12:06,937 - models.train - INFO - Machine learning model (gb) training completed for horizon 120960
2025-07-18 10:12:06,938 - models.train - INFO - Saving model for horizon 120960...
2025-07-18 10:12:06,942 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_120960min.joblib
2025-07-18 10:12:06,943 - models.train - INFO - Model saved for horizon 120960
2025-07-18 10:12:06,943 - models.train - INFO - Model for 120960 days horizon trained and saved successfully
2025-07-18 10:12:06,943 - models.train - INFO - Successfully trained 5 models
2025-07-18 10:12:06,947 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:06,949 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:12:07,150 - app.utils.memory_management - INFO - Garbage collection: collected 30 objects
2025-07-18 10:12:07,151 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:07,151 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:12:09,785 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:12:09,818 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:09,819 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:12:10,014 - app.utils.memory_management - INFO - Garbage collection: collected 802 objects
2025-07-18 10:12:10,015 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:10,015 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:12:12,920 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:12:12,952 - app - INFO - File COMI contains 2025 data
2025-07-18 10:12:13,031 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-18 10:12:13,033 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 10:12:13,035 - models.train - INFO - Starting training from CSV: data/stocks/COMI.csv
2025-07-18 10:12:13,035 - models.train - INFO - Symbol: COMI, Model type: gb
2025-07-18 10:12:13,035 - models.train - INFO - Horizons: [1440, 2880, 4320, 7200, 10080] (in minutes)
2025-07-18 10:12:13,035 - models.train - INFO - Loading data from data/stocks/COMI.csv...
2025-07-18 10:12:13,053 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-18 10:12:13,053 - models.train - INFO - Converting Date column to datetime...
2025-07-18 10:12:13,060 - models.train - INFO - Starting model training...
2025-07-18 10:12:13,061 - models.train - INFO - Training models for symbol: COMI
2025-07-18 10:12:13,061 - models.train - INFO - Model type: gb
2025-07-18 10:12:13,062 - models.train - INFO - Horizons: [1440, 2880, 4320, 7200, 10080]
2025-07-18 10:12:13,062 - models.train - INFO - Sequence length: 60
2025-07-18 10:12:13,062 - models.train - INFO - Epochs: 1
2025-07-18 10:12:13,062 - models.train - INFO - Batch size: 32
2025-07-18 10:12:13,063 - models.train - INFO - Data shape: (1250, 36)
2025-07-18 10:12:13,063 - models.train - INFO - Preparing features...
2025-07-18 10:12:13,097 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-18 10:12:13,097 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-18 10:12:13,098 - models.train - INFO - Training model for 1440 minutes horizon
2025-07-18 10:12:13,098 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-18 10:12:13,100 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:12:13,100 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:12:13,101 - models.train - INFO - Creating target variable for horizon 1440 minutes...
2025-07-18 10:12:13,101 - models.train - INFO - Converting 1440 minutes to approximately 1 days for target shifting
2025-07-18 10:12:13,102 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:12:13,103 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:12:13,105 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-18 10:12:13,106 - models.train - INFO - Preprocessing data...
2025-07-18 10:12:13,325 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:13,326 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:12:13,543 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:12:13,735 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:13,735 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.22s
2025-07-18 10:12:13,735 - models.train - INFO - Saving scaler for horizon 1440...
2025-07-18 10:12:13,927 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:13,931 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 10:12:13,935 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_1440min.joblib (0.53 KB)
2025-07-18 10:12:13,937 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler1440min.joblib (0.53 KB)
2025-07-18 10:12:14,321 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:14,321 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:14,321 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_1440_scaler.pkl', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler1440min.joblib']
2025-07-18 10:12:14,322 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:12:14,322 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:12:14,322 - models.train - INFO - Initializing model for horizon 1440...
2025-07-18 10:12:14,322 - models.train - INFO - Using scikit-learn gb model for 1440 days horizon
2025-07-18 10:12:14,322 - models.train - INFO - Training model for horizon 1440...
2025-07-18 10:12:14,322 - models.train - INFO - Training machine learning model (gb) for horizon 1440. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:12:14,323 - models.sklearn_model - INFO - Training gb model for 1440 minutes horizon
2025-07-18 10:12:14,323 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:12:14,323 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:12:19,149 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:12:19,153 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_1440min.joblib
2025-07-18 10:12:19,153 - models.train - INFO - Machine learning model (gb) training completed for horizon 1440
2025-07-18 10:12:19,154 - models.train - INFO - Saving model for horizon 1440...
2025-07-18 10:12:19,158 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_1440min.joblib
2025-07-18 10:12:19,159 - models.train - INFO - Model saved for horizon 1440
2025-07-18 10:12:19,159 - models.train - INFO - Model for 1440 days horizon trained and saved successfully
2025-07-18 10:12:19,159 - models.train - INFO - Training model for 2880 minutes horizon
2025-07-18 10:12:19,159 - models.train - INFO - Data shape before processing: (1250, 37)
2025-07-18 10:12:19,161 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:12:19,161 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:12:19,161 - models.train - INFO - Creating target variable for horizon 2880 minutes...
2025-07-18 10:12:19,161 - models.train - INFO - Converting 2880 minutes to approximately 2 days for target shifting
2025-07-18 10:12:19,162 - models.train - INFO - Created target variable. Valid targets: 1248/1250
2025-07-18 10:12:19,162 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:12:19,163 - models.train - INFO - Data shape after dropping NaN rows: (1248, 38)
2025-07-18 10:12:19,164 - models.train - INFO - Preprocessing data...
2025-07-18 10:12:19,354 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:19,355 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:12:19,556 - app.utils.data_processing - INFO - Created sequences: X shape=(1188, 60, 7), memory=1.90 MB; y shape=(1188,), memory=0.00 MB
2025-07-18 10:12:19,745 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:19,746 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:19,746 - models.train - INFO - Saving scaler for horizon 2880...
2025-07-18 10:12:19,937 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:19,938 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_2880_scaler.pkl (0.53 KB)
2025-07-18 10:12:19,940 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_2880min.joblib (0.53 KB)
2025-07-18 10:12:19,941 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler2880min.joblib (0.53 KB)
2025-07-18 10:12:20,325 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:20,325 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:20,325 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_2880_scaler.pkl', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler2880min.joblib']
2025-07-18 10:12:20,325 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:12:20,326 - models.train - INFO - Training data shape: (950, 60, 7)
2025-07-18 10:12:20,326 - models.train - INFO - Initializing model for horizon 2880...
2025-07-18 10:12:20,326 - models.train - INFO - Using scikit-learn gb model for 2880 days horizon
2025-07-18 10:12:20,326 - models.train - INFO - Training model for horizon 2880...
2025-07-18 10:12:20,326 - models.train - INFO - Training machine learning model (gb) for horizon 2880. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:12:20,326 - models.sklearn_model - INFO - Training gb model for 2880 minutes horizon
2025-07-18 10:12:20,326 - models.sklearn_model - INFO - Reshaping 3D input with shape (950, 60, 7) to 2D
2025-07-18 10:12:20,326 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:12:25,152 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:12:25,155 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_2880min.joblib
2025-07-18 10:12:25,155 - models.train - INFO - Machine learning model (gb) training completed for horizon 2880
2025-07-18 10:12:25,155 - models.train - INFO - Saving model for horizon 2880...
2025-07-18 10:12:25,160 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_2880min.joblib
2025-07-18 10:12:25,160 - models.train - INFO - Model saved for horizon 2880
2025-07-18 10:12:25,161 - models.train - INFO - Model for 2880 days horizon trained and saved successfully
2025-07-18 10:12:25,161 - models.train - INFO - Training model for 4320 minutes horizon
2025-07-18 10:12:25,161 - models.train - INFO - Data shape before processing: (1250, 38)
2025-07-18 10:12:25,163 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:12:25,163 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:12:25,163 - models.train - INFO - Creating target variable for horizon 4320 minutes...
2025-07-18 10:12:25,163 - models.train - INFO - Converting 4320 minutes to approximately 3 days for target shifting
2025-07-18 10:12:25,164 - models.train - INFO - Created target variable. Valid targets: 1247/1250
2025-07-18 10:12:25,164 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:12:25,167 - models.train - INFO - Data shape after dropping NaN rows: (1247, 39)
2025-07-18 10:12:25,167 - models.train - INFO - Preprocessing data...
2025-07-18 10:12:25,353 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:25,353 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:12:25,550 - app.utils.data_processing - INFO - Created sequences: X shape=(1187, 60, 7), memory=1.90 MB; y shape=(1187,), memory=0.00 MB
2025-07-18 10:12:25,740 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:25,740 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:25,740 - models.train - INFO - Saving scaler for horizon 4320...
2025-07-18 10:12:25,932 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:25,934 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_4320_scaler.pkl (0.53 KB)
2025-07-18 10:12:25,935 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_4320min.joblib (0.53 KB)
2025-07-18 10:12:25,937 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler4320min.joblib (0.53 KB)
2025-07-18 10:12:26,318 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:26,319 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:26,319 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_4320_scaler.pkl', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler4320min.joblib']
2025-07-18 10:12:26,319 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:12:26,320 - models.train - INFO - Training data shape: (949, 60, 7)
2025-07-18 10:12:26,320 - models.train - INFO - Initializing model for horizon 4320...
2025-07-18 10:12:26,320 - models.train - INFO - Using scikit-learn gb model for 4320 days horizon
2025-07-18 10:12:26,320 - models.train - INFO - Training model for horizon 4320...
2025-07-18 10:12:26,320 - models.train - INFO - Training machine learning model (gb) for horizon 4320. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:12:26,321 - models.sklearn_model - INFO - Training gb model for 4320 minutes horizon
2025-07-18 10:12:26,321 - models.sklearn_model - INFO - Reshaping 3D input with shape (949, 60, 7) to 2D
2025-07-18 10:12:26,321 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:12:31,215 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:12:31,221 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_4320min.joblib
2025-07-18 10:12:31,221 - models.train - INFO - Machine learning model (gb) training completed for horizon 4320
2025-07-18 10:12:31,221 - models.train - INFO - Saving model for horizon 4320...
2025-07-18 10:12:31,225 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_4320min.joblib
2025-07-18 10:12:31,225 - models.train - INFO - Model saved for horizon 4320
2025-07-18 10:12:31,225 - models.train - INFO - Model for 4320 days horizon trained and saved successfully
2025-07-18 10:12:31,225 - models.train - INFO - Training model for 7200 minutes horizon
2025-07-18 10:12:31,225 - models.train - INFO - Data shape before processing: (1250, 39)
2025-07-18 10:12:31,228 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:12:31,228 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:12:31,228 - models.train - INFO - Creating target variable for horizon 7200 minutes...
2025-07-18 10:12:31,228 - models.train - INFO - Converting 7200 minutes to approximately 5 days for target shifting
2025-07-18 10:12:31,229 - models.train - INFO - Created target variable. Valid targets: 1245/1250
2025-07-18 10:12:31,229 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:12:31,231 - models.train - INFO - Data shape after dropping NaN rows: (1245, 40)
2025-07-18 10:12:31,231 - models.train - INFO - Preprocessing data...
2025-07-18 10:12:31,418 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:31,419 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:12:31,615 - app.utils.data_processing - INFO - Created sequences: X shape=(1185, 60, 7), memory=1.90 MB; y shape=(1185,), memory=0.00 MB
2025-07-18 10:12:31,807 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:31,808 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:31,808 - models.train - INFO - Saving scaler for horizon 7200...
2025-07-18 10:12:31,999 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:32,001 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_7200_scaler.pkl (0.53 KB)
2025-07-18 10:12:32,002 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_7200min.joblib (0.53 KB)
2025-07-18 10:12:32,004 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler7200min.joblib (0.53 KB)
2025-07-18 10:12:32,385 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:32,385 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-18 10:12:32,385 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_7200_scaler.pkl', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler7200min.joblib']
2025-07-18 10:12:32,385 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:12:32,386 - models.train - INFO - Training data shape: (948, 60, 7)
2025-07-18 10:12:32,386 - models.train - INFO - Initializing model for horizon 7200...
2025-07-18 10:12:32,386 - models.train - INFO - Using scikit-learn gb model for 7200 days horizon
2025-07-18 10:12:32,386 - models.train - INFO - Training model for horizon 7200...
2025-07-18 10:12:32,386 - models.train - INFO - Training machine learning model (gb) for horizon 7200. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:12:32,386 - models.sklearn_model - INFO - Training gb model for 7200 minutes horizon
2025-07-18 10:12:32,386 - models.sklearn_model - INFO - Reshaping 3D input with shape (948, 60, 7) to 2D
2025-07-18 10:12:32,386 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:12:37,339 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:12:37,342 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_7200min.joblib
2025-07-18 10:12:37,342 - models.train - INFO - Machine learning model (gb) training completed for horizon 7200
2025-07-18 10:12:37,342 - models.train - INFO - Saving model for horizon 7200...
2025-07-18 10:12:37,347 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_7200min.joblib
2025-07-18 10:12:37,348 - models.train - INFO - Model saved for horizon 7200
2025-07-18 10:12:37,348 - models.train - INFO - Model for 7200 days horizon trained and saved successfully
2025-07-18 10:12:37,348 - models.train - INFO - Training model for 10080 minutes horizon
2025-07-18 10:12:37,348 - models.train - INFO - Data shape before processing: (1250, 40)
2025-07-18 10:12:37,350 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:12:37,351 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:12:37,351 - models.train - INFO - Creating target variable for horizon 10080 minutes...
2025-07-18 10:12:37,351 - models.train - INFO - Converting 10080 minutes to approximately 7 days for target shifting
2025-07-18 10:12:37,352 - models.train - INFO - Created target variable. Valid targets: 1243/1250
2025-07-18 10:12:37,352 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:12:37,354 - models.train - INFO - Data shape after dropping NaN rows: (1243, 41)
2025-07-18 10:12:37,354 - models.train - INFO - Preprocessing data...
2025-07-18 10:12:37,539 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:37,539 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:12:37,738 - app.utils.data_processing - INFO - Created sequences: X shape=(1183, 60, 7), memory=1.90 MB; y shape=(1183,), memory=0.00 MB
2025-07-18 10:12:37,930 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:37,930 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:37,930 - models.train - INFO - Saving scaler for horizon 10080...
2025-07-18 10:12:38,121 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:38,123 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_10080_scaler.pkl (0.54 KB)
2025-07-18 10:12:38,125 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_10080min.joblib (0.54 KB)
2025-07-18 10:12:38,126 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler10080min.joblib (0.54 KB)
2025-07-18 10:12:38,509 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:38,510 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:38,510 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_10080_scaler.pkl', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler10080min.joblib']
2025-07-18 10:12:38,510 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:12:38,511 - models.train - INFO - Training data shape: (946, 60, 7)
2025-07-18 10:12:38,511 - models.train - INFO - Initializing model for horizon 10080...
2025-07-18 10:12:38,511 - models.train - INFO - Using scikit-learn gb model for 10080 days horizon
2025-07-18 10:12:38,511 - models.train - INFO - Training model for horizon 10080...
2025-07-18 10:12:38,511 - models.train - INFO - Training machine learning model (gb) for horizon 10080. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:12:38,511 - models.sklearn_model - INFO - Training gb model for 10080 minutes horizon
2025-07-18 10:12:38,511 - models.sklearn_model - INFO - Reshaping 3D input with shape (946, 60, 7) to 2D
2025-07-18 10:12:38,511 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:12:43,240 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:12:43,244 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_10080min.joblib
2025-07-18 10:12:43,244 - models.train - INFO - Machine learning model (gb) training completed for horizon 10080
2025-07-18 10:12:43,244 - models.train - INFO - Saving model for horizon 10080...
2025-07-18 10:12:43,247 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_10080min.joblib
2025-07-18 10:12:43,247 - models.train - INFO - Model saved for horizon 10080
2025-07-18 10:12:43,247 - models.train - INFO - Model for 10080 days horizon trained and saved successfully
2025-07-18 10:12:43,248 - models.train - INFO - Successfully trained 5 models
2025-07-18 10:12:43,253 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:43,254 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:12:43,468 - app.utils.memory_management - INFO - Garbage collection: collected 30 objects
2025-07-18 10:12:43,469 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:43,469 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:12:46,090 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:12:46,118 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:46,119 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:12:46,325 - app.utils.memory_management - INFO - Garbage collection: collected 802 objects
2025-07-18 10:12:46,325 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:46,326 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:12:48,244 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:12:48,289 - app - INFO - File COMI contains 2025 data
2025-07-18 10:12:48,375 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-18 10:12:48,375 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 10:12:48,377 - models.train - INFO - Starting training from CSV: data/stocks/COMI.csv
2025-07-18 10:12:48,377 - models.train - INFO - Symbol: COMI, Model type: gb
2025-07-18 10:12:48,377 - models.train - INFO - Horizons: [5, 15, 30, 60] (in minutes)
2025-07-18 10:12:48,378 - models.train - INFO - Loading data from data/stocks/COMI.csv...
2025-07-18 10:12:48,392 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-18 10:12:48,392 - models.train - INFO - Converting Date column to datetime...
2025-07-18 10:12:48,397 - models.train - INFO - Starting model training...
2025-07-18 10:12:48,398 - models.train - INFO - Training models for symbol: COMI
2025-07-18 10:12:48,398 - models.train - INFO - Model type: gb
2025-07-18 10:12:48,398 - models.train - INFO - Horizons: [5, 15, 30, 60]
2025-07-18 10:12:48,399 - models.train - INFO - Sequence length: 60
2025-07-18 10:12:48,399 - models.train - INFO - Epochs: 1
2025-07-18 10:12:48,399 - models.train - INFO - Batch size: 32
2025-07-18 10:12:48,400 - models.train - INFO - Data shape: (1250, 36)
2025-07-18 10:12:48,400 - models.train - INFO - Preparing features...
2025-07-18 10:12:48,425 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-18 10:12:48,426 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-18 10:12:48,426 - models.train - INFO - Training model for 5 minutes horizon
2025-07-18 10:12:48,426 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-18 10:12:48,430 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:12:48,430 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:12:48,434 - models.train - INFO - Creating target variable for horizon 5 minutes...
2025-07-18 10:12:48,434 - models.train - INFO - Converting 5 minutes to approximately 1 days for target shifting
2025-07-18 10:12:48,435 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:12:48,435 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:12:48,437 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-18 10:12:48,437 - models.train - INFO - Preprocessing data...
2025-07-18 10:12:48,658 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:48,658 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:12:48,872 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:12:49,069 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:49,070 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-18 10:12:49,070 - models.train - INFO - Saving scaler for horizon 5...
2025-07-18 10:12:49,261 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:49,265 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-18 10:12:49,269 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_5min.joblib (0.53 KB)
2025-07-18 10:12:49,271 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler5min.joblib (0.53 KB)
2025-07-18 10:12:49,670 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:49,671 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.22s
2025-07-18 10:12:49,671 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_5_scaler.pkl', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler5min.joblib']
2025-07-18 10:12:49,671 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:12:49,671 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:12:49,671 - models.train - INFO - Initializing model for horizon 5...
2025-07-18 10:12:49,671 - models.train - INFO - Using scikit-learn gb model for 5 days horizon
2025-07-18 10:12:49,671 - models.train - INFO - Training model for horizon 5...
2025-07-18 10:12:49,671 - models.train - INFO - Training machine learning model (gb) for horizon 5. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:12:49,671 - models.sklearn_model - INFO - Training gb model for 5 minutes horizon
2025-07-18 10:12:49,672 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:12:49,672 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:12:54,487 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:12:54,490 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_5min.joblib
2025-07-18 10:12:54,490 - models.train - INFO - Machine learning model (gb) training completed for horizon 5
2025-07-18 10:12:54,490 - models.train - INFO - Saving model for horizon 5...
2025-07-18 10:12:54,495 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_5min.joblib
2025-07-18 10:12:54,495 - models.train - INFO - Model saved for horizon 5
2025-07-18 10:12:54,495 - models.train - INFO - Model for 5 days horizon trained and saved successfully
2025-07-18 10:12:54,495 - models.train - INFO - Training model for 15 minutes horizon
2025-07-18 10:12:54,496 - models.train - INFO - Data shape before processing: (1250, 37)
2025-07-18 10:12:54,498 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:12:54,499 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:12:54,499 - models.train - INFO - Creating target variable for horizon 15 minutes...
2025-07-18 10:12:54,499 - models.train - INFO - Converting 15 minutes to approximately 1 days for target shifting
2025-07-18 10:12:54,500 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:12:54,500 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:12:54,502 - models.train - INFO - Data shape after dropping NaN rows: (1249, 38)
2025-07-18 10:12:54,502 - models.train - INFO - Preprocessing data...
2025-07-18 10:12:54,688 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:54,689 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:12:54,889 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:12:55,079 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:55,080 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:12:55,080 - models.train - INFO - Saving scaler for horizon 15...
2025-07-18 10:12:55,272 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:55,274 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-18 10:12:55,275 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_15min.joblib (0.53 KB)
2025-07-18 10:12:55,276 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler15min.joblib (0.53 KB)
2025-07-18 10:12:55,687 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:12:55,688 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.23s
2025-07-18 10:12:55,688 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_15_scaler.pkl', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler15min.joblib']
2025-07-18 10:12:55,688 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:12:55,688 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:12:55,688 - models.train - INFO - Initializing model for horizon 15...
2025-07-18 10:12:55,688 - models.train - INFO - Using scikit-learn gb model for 15 days horizon
2025-07-18 10:12:55,689 - models.train - INFO - Training model for horizon 15...
2025-07-18 10:12:55,689 - models.train - INFO - Training machine learning model (gb) for horizon 15. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:12:55,689 - models.sklearn_model - INFO - Training gb model for 15 minutes horizon
2025-07-18 10:12:55,689 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:12:55,689 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:13:00,569 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:13:00,572 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_15min.joblib
2025-07-18 10:13:00,572 - models.train - INFO - Machine learning model (gb) training completed for horizon 15
2025-07-18 10:13:00,572 - models.train - INFO - Saving model for horizon 15...
2025-07-18 10:13:00,577 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_15min.joblib
2025-07-18 10:13:00,577 - models.train - INFO - Model saved for horizon 15
2025-07-18 10:13:00,577 - models.train - INFO - Model for 15 days horizon trained and saved successfully
2025-07-18 10:13:00,577 - models.train - INFO - Training model for 30 minutes horizon
2025-07-18 10:13:00,577 - models.train - INFO - Data shape before processing: (1250, 38)
2025-07-18 10:13:00,580 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:13:00,580 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:13:00,580 - models.train - INFO - Creating target variable for horizon 30 minutes...
2025-07-18 10:13:00,580 - models.train - INFO - Converting 30 minutes to approximately 1 days for target shifting
2025-07-18 10:13:00,581 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:13:00,581 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:13:00,584 - models.train - INFO - Data shape after dropping NaN rows: (1249, 39)
2025-07-18 10:13:00,584 - models.train - INFO - Preprocessing data...
2025-07-18 10:13:00,769 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:00,770 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:13:00,967 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:13:01,156 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:01,157 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:13:01,157 - models.train - INFO - Saving scaler for horizon 30...
2025-07-18 10:13:01,348 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:01,352 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 10:13:01,355 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_30min.joblib (0.53 KB)
2025-07-18 10:13:01,356 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler30min.joblib (0.53 KB)
2025-07-18 10:13:01,738 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:01,739 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:13:01,739 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_30_scaler.pkl', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler30min.joblib']
2025-07-18 10:13:01,739 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:13:01,740 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:13:01,740 - models.train - INFO - Initializing model for horizon 30...
2025-07-18 10:13:01,740 - models.train - INFO - Using scikit-learn gb model for 30 days horizon
2025-07-18 10:13:01,740 - models.train - INFO - Training model for horizon 30...
2025-07-18 10:13:01,740 - models.train - INFO - Training machine learning model (gb) for horizon 30. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:13:01,740 - models.sklearn_model - INFO - Training gb model for 30 minutes horizon
2025-07-18 10:13:01,740 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:13:01,740 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:13:06,817 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:13:06,820 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_30min.joblib
2025-07-18 10:13:06,820 - models.train - INFO - Machine learning model (gb) training completed for horizon 30
2025-07-18 10:13:06,820 - models.train - INFO - Saving model for horizon 30...
2025-07-18 10:13:06,825 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_30min.joblib
2025-07-18 10:13:06,825 - models.train - INFO - Model saved for horizon 30
2025-07-18 10:13:06,825 - models.train - INFO - Model for 30 days horizon trained and saved successfully
2025-07-18 10:13:06,825 - models.train - INFO - Training model for 60 minutes horizon
2025-07-18 10:13:06,825 - models.train - INFO - Data shape before processing: (1250, 39)
2025-07-18 10:13:06,828 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:13:06,828 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:13:06,828 - models.train - INFO - Creating target variable for horizon 60 minutes...
2025-07-18 10:13:06,828 - models.train - INFO - Converting 60 minutes to approximately 1 days for target shifting
2025-07-18 10:13:06,830 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:13:06,830 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:13:06,832 - models.train - INFO - Data shape after dropping NaN rows: (1249, 40)
2025-07-18 10:13:06,832 - models.train - INFO - Preprocessing data...
2025-07-18 10:13:07,018 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:07,018 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:13:07,215 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:13:07,406 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:07,406 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:13:07,407 - models.train - INFO - Saving scaler for horizon 60...
2025-07-18 10:13:07,598 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:07,601 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 10:13:07,603 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler_60min.joblib (0.53 KB)
2025-07-18 10:13:07,605 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_gb_scaler60min.joblib (0.53 KB)
2025-07-18 10:13:07,987 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:07,987 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:13:07,987 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_60_scaler.pkl', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler60min.joblib']
2025-07-18 10:13:07,988 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:13:07,988 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:13:07,988 - models.train - INFO - Initializing model for horizon 60...
2025-07-18 10:13:07,988 - models.train - INFO - Using scikit-learn gb model for 60 days horizon
2025-07-18 10:13:07,988 - models.train - INFO - Training model for horizon 60...
2025-07-18 10:13:07,988 - models.train - INFO - Training machine learning model (gb) for horizon 60. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:13:07,988 - models.sklearn_model - INFO - Training gb model for 60 minutes horizon
2025-07-18 10:13:07,988 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:13:07,988 - models.sklearn_model - INFO - Training XGBRegressor model...
2025-07-18 10:13:12,822 - models.sklearn_model - INFO - XGBRegressor model training completed
2025-07-18 10:13:12,825 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_60min.joblib
2025-07-18 10:13:12,826 - models.train - INFO - Machine learning model (gb) training completed for horizon 60
2025-07-18 10:13:12,826 - models.train - INFO - Saving model for horizon 60...
2025-07-18 10:13:12,830 - models.sklearn_model - INFO - Successfully saved gb model to COMI_gb_60min.joblib
2025-07-18 10:13:12,831 - models.train - INFO - Model saved for horizon 60
2025-07-18 10:13:12,831 - models.train - INFO - Model for 60 days horizon trained and saved successfully
2025-07-18 10:13:12,831 - models.train - INFO - Successfully trained 4 models
2025-07-18 10:13:12,835 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:12,835 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:13:13,042 - app.utils.memory_management - INFO - Garbage collection: collected 30 objects
2025-07-18 10:13:13,043 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:13,043 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:13:35,645 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:13:35,679 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:35,681 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:13:35,899 - app.utils.memory_management - INFO - Garbage collection: collected 802 objects
2025-07-18 10:13:35,905 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:35,905 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:13:39,127 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:13:39,173 - app - INFO - File COMI contains 2025 data
2025-07-18 10:13:39,256 - app - INFO - Saved data to data/stocks/COMI.csv
2025-07-18 10:13:39,257 - app - INFO - Date range: 2020-10-02 to 2025-07-17
2025-07-18 10:13:39,259 - models.train - INFO - Starting training from CSV: data/stocks/COMI.csv
2025-07-18 10:13:39,260 - models.train - INFO - Symbol: COMI, Model type: lr
2025-07-18 10:13:39,260 - models.train - INFO - Horizons: [5, 15, 30, 60] (in minutes)
2025-07-18 10:13:39,261 - models.train - INFO - Loading data from data/stocks/COMI.csv...
2025-07-18 10:13:39,273 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-18 10:13:39,273 - models.train - INFO - Converting Date column to datetime...
2025-07-18 10:13:39,275 - models.train - INFO - Starting model training...
2025-07-18 10:13:39,277 - models.train - INFO - Training models for symbol: COMI
2025-07-18 10:13:39,277 - models.train - INFO - Model type: lr
2025-07-18 10:13:39,277 - models.train - INFO - Horizons: [5, 15, 30, 60]
2025-07-18 10:13:39,277 - models.train - INFO - Sequence length: 60
2025-07-18 10:13:39,277 - models.train - INFO - Epochs: 1
2025-07-18 10:13:39,277 - models.train - INFO - Batch size: 32
2025-07-18 10:13:39,278 - models.train - INFO - Data shape: (1250, 36)
2025-07-18 10:13:39,278 - models.train - INFO - Preparing features...
2025-07-18 10:13:39,315 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-18 10:13:39,315 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-18 10:13:39,316 - models.train - INFO - Training model for 5 minutes horizon
2025-07-18 10:13:39,316 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-18 10:13:39,320 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:13:39,320 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:13:39,320 - models.train - INFO - Creating target variable for horizon 5 minutes...
2025-07-18 10:13:39,320 - models.train - INFO - Converting 5 minutes to approximately 1 days for target shifting
2025-07-18 10:13:39,322 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:13:39,322 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:13:39,324 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-18 10:13:39,324 - models.train - INFO - Preprocessing data...
2025-07-18 10:13:39,589 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:39,589 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:13:39,810 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:13:40,000 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:40,000 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.22s
2025-07-18 10:13:40,000 - models.train - INFO - Saving scaler for horizon 5...
2025-07-18 10:13:40,188 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:40,193 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_5_scaler.pkl (0.53 KB)
2025-07-18 10:13:40,197 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_lr_scaler_5min.joblib (0.53 KB)
2025-07-18 10:13:40,199 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_lr_scaler5min.joblib (0.53 KB)
2025-07-18 10:13:40,586 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:40,586 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-18 10:13:40,586 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_5_scaler.pkl', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler5min.joblib']
2025-07-18 10:13:40,586 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:13:40,587 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:13:40,587 - models.train - INFO - Initializing model for horizon 5...
2025-07-18 10:13:40,587 - models.train - INFO - Using scikit-learn lr model for 5 days horizon
2025-07-18 10:13:40,587 - models.train - INFO - Training model for horizon 5...
2025-07-18 10:13:40,587 - models.train - INFO - Training machine learning model (lr) for horizon 5. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:13:40,587 - models.sklearn_model - INFO - Training lr model for 5 minutes horizon
2025-07-18 10:13:40,587 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:13:40,587 - models.sklearn_model - INFO - Training enhanced LinearRegression model...
2025-07-18 10:13:40,609 - models.sklearn_model - INFO - Enhanced LinearRegression model training completed
2025-07-18 10:13:40,611 - models.sklearn_model - INFO - Successfully saved lr model to COMI_lr_5min.joblib
2025-07-18 10:13:40,611 - models.train - INFO - Machine learning model (lr) training completed for horizon 5
2025-07-18 10:13:40,611 - models.train - INFO - Saving model for horizon 5...
2025-07-18 10:13:40,612 - models.sklearn_model - INFO - Successfully saved lr model to COMI_lr_5min.joblib
2025-07-18 10:13:40,612 - models.train - INFO - Model saved for horizon 5
2025-07-18 10:13:40,612 - models.train - INFO - Model for 5 days horizon trained and saved successfully
2025-07-18 10:13:40,612 - models.train - INFO - Training model for 15 minutes horizon
2025-07-18 10:13:40,612 - models.train - INFO - Data shape before processing: (1250, 37)
2025-07-18 10:13:40,615 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:13:40,615 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:13:40,615 - models.train - INFO - Creating target variable for horizon 15 minutes...
2025-07-18 10:13:40,615 - models.train - INFO - Converting 15 minutes to approximately 1 days for target shifting
2025-07-18 10:13:40,616 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:13:40,616 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:13:40,618 - models.train - INFO - Data shape after dropping NaN rows: (1249, 38)
2025-07-18 10:13:40,618 - models.train - INFO - Preprocessing data...
2025-07-18 10:13:40,815 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:40,816 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:13:41,091 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:13:41,280 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:41,281 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.28s
2025-07-18 10:13:41,281 - models.train - INFO - Saving scaler for horizon 15...
2025-07-18 10:13:41,469 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:41,471 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_15_scaler.pkl (0.53 KB)
2025-07-18 10:13:41,472 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_lr_scaler_15min.joblib (0.53 KB)
2025-07-18 10:13:41,474 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_lr_scaler15min.joblib (0.53 KB)
2025-07-18 10:13:41,853 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:41,854 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:13:41,854 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_15_scaler.pkl', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler15min.joblib']
2025-07-18 10:13:41,854 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:13:41,854 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:13:41,854 - models.train - INFO - Initializing model for horizon 15...
2025-07-18 10:13:41,854 - models.train - INFO - Using scikit-learn lr model for 15 days horizon
2025-07-18 10:13:41,855 - models.train - INFO - Training model for horizon 15...
2025-07-18 10:13:41,855 - models.train - INFO - Training machine learning model (lr) for horizon 15. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:13:41,855 - models.sklearn_model - INFO - Training lr model for 15 minutes horizon
2025-07-18 10:13:41,855 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:13:41,855 - models.sklearn_model - INFO - Training enhanced LinearRegression model...
2025-07-18 10:13:41,863 - models.sklearn_model - INFO - Enhanced LinearRegression model training completed
2025-07-18 10:13:41,865 - models.sklearn_model - INFO - Successfully saved lr model to COMI_lr_15min.joblib
2025-07-18 10:13:41,866 - models.train - INFO - Machine learning model (lr) training completed for horizon 15
2025-07-18 10:13:41,866 - models.train - INFO - Saving model for horizon 15...
2025-07-18 10:13:41,867 - models.sklearn_model - INFO - Successfully saved lr model to COMI_lr_15min.joblib
2025-07-18 10:13:41,867 - models.train - INFO - Model saved for horizon 15
2025-07-18 10:13:41,867 - models.train - INFO - Model for 15 days horizon trained and saved successfully
2025-07-18 10:13:41,867 - models.train - INFO - Training model for 30 minutes horizon
2025-07-18 10:13:41,867 - models.train - INFO - Data shape before processing: (1250, 38)
2025-07-18 10:13:41,871 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:13:41,871 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:13:41,871 - models.train - INFO - Creating target variable for horizon 30 minutes...
2025-07-18 10:13:41,871 - models.train - INFO - Converting 30 minutes to approximately 1 days for target shifting
2025-07-18 10:13:41,872 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:13:41,872 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:13:41,874 - models.train - INFO - Data shape after dropping NaN rows: (1249, 39)
2025-07-18 10:13:41,874 - models.train - INFO - Preprocessing data...
2025-07-18 10:13:42,071 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:42,072 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:13:42,270 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:13:42,459 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:42,459 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:13:42,460 - models.train - INFO - Saving scaler for horizon 30...
2025-07-18 10:13:42,649 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:42,651 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 10:13:42,652 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_lr_scaler_30min.joblib (0.53 KB)
2025-07-18 10:13:42,654 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_lr_scaler30min.joblib (0.53 KB)
2025-07-18 10:13:43,036 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:43,036 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:13:43,036 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_30_scaler.pkl', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler30min.joblib']
2025-07-18 10:13:43,036 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:13:43,036 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:13:43,036 - models.train - INFO - Initializing model for horizon 30...
2025-07-18 10:13:43,036 - models.train - INFO - Using scikit-learn lr model for 30 days horizon
2025-07-18 10:13:43,037 - models.train - INFO - Training model for horizon 30...
2025-07-18 10:13:43,037 - models.train - INFO - Training machine learning model (lr) for horizon 30. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:13:43,037 - models.sklearn_model - INFO - Training lr model for 30 minutes horizon
2025-07-18 10:13:43,037 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:13:43,037 - models.sklearn_model - INFO - Training enhanced LinearRegression model...
2025-07-18 10:13:43,045 - models.sklearn_model - INFO - Enhanced LinearRegression model training completed
2025-07-18 10:13:43,046 - models.sklearn_model - INFO - Successfully saved lr model to COMI_lr_30min.joblib
2025-07-18 10:13:43,046 - models.train - INFO - Machine learning model (lr) training completed for horizon 30
2025-07-18 10:13:43,046 - models.train - INFO - Saving model for horizon 30...
2025-07-18 10:13:43,048 - models.sklearn_model - INFO - Successfully saved lr model to COMI_lr_30min.joblib
2025-07-18 10:13:43,049 - models.train - INFO - Model saved for horizon 30
2025-07-18 10:13:43,049 - models.train - INFO - Model for 30 days horizon trained and saved successfully
2025-07-18 10:13:43,049 - models.train - INFO - Training model for 60 minutes horizon
2025-07-18 10:13:43,049 - models.train - INFO - Data shape before processing: (1250, 39)
2025-07-18 10:13:43,052 - models.train - INFO - Detected data frequency: daily
2025-07-18 10:13:43,052 - models.train - INFO - Minimum required rows: 70
2025-07-18 10:13:43,052 - models.train - INFO - Creating target variable for horizon 60 minutes...
2025-07-18 10:13:43,052 - models.train - INFO - Converting 60 minutes to approximately 1 days for target shifting
2025-07-18 10:13:43,053 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-18 10:13:43,053 - models.train - INFO - Dropping rows with NaN targets...
2025-07-18 10:13:43,055 - models.train - INFO - Data shape after dropping NaN rows: (1249, 40)
2025-07-18 10:13:43,055 - models.train - INFO - Preprocessing data...
2025-07-18 10:13:43,251 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:43,252 - app.utils.memory_management - WARNING - psutil not available. Cannot enforce memory limit.
2025-07-18 10:13:43,447 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-18 10:13:43,637 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:43,638 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-18 10:13:43,638 - models.train - INFO - Saving scaler for horizon 60...
2025-07-18 10:13:43,829 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:43,831 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 10:13:43,832 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_lr_scaler_60min.joblib (0.53 KB)
2025-07-18 10:13:43,834 - app.utils.data_processing - INFO - Saved scaler to saved_models/COMI_lr_scaler60min.joblib (0.53 KB)
2025-07-18 10:13:44,216 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:44,217 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-18 10:13:44,217 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_60_scaler.pkl', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler60min.joblib']
2025-07-18 10:13:44,217 - models.train - INFO - Splitting data into training and validation sets...
2025-07-18 10:13:44,217 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-18 10:13:44,217 - models.train - INFO - Initializing model for horizon 60...
2025-07-18 10:13:44,217 - models.train - INFO - Using scikit-learn lr model for 60 days horizon
2025-07-18 10:13:44,217 - models.train - INFO - Training model for horizon 60...
2025-07-18 10:13:44,217 - models.train - INFO - Training machine learning model (lr) for horizon 60. This may be quick as ML models don't use epochs like neural networks.
2025-07-18 10:13:44,218 - models.sklearn_model - INFO - Training lr model for 60 minutes horizon
2025-07-18 10:13:44,218 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-18 10:13:44,218 - models.sklearn_model - INFO - Training enhanced LinearRegression model...
2025-07-18 10:13:44,225 - models.sklearn_model - INFO - Enhanced LinearRegression model training completed
2025-07-18 10:13:44,227 - models.sklearn_model - INFO - Successfully saved lr model to COMI_lr_60min.joblib
2025-07-18 10:13:44,227 - models.train - INFO - Machine learning model (lr) training completed for horizon 60
2025-07-18 10:13:44,227 - models.train - INFO - Saving model for horizon 60...
2025-07-18 10:13:44,229 - models.sklearn_model - INFO - Successfully saved lr model to COMI_lr_60min.joblib
2025-07-18 10:13:44,229 - models.train - INFO - Model saved for horizon 60
2025-07-18 10:13:44,229 - models.train - INFO - Model for 60 days horizon trained and saved successfully
2025-07-18 10:13:44,230 - models.train - INFO - Successfully trained 4 models
2025-07-18 10:13:44,234 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:44,234 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:13:44,449 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-18 10:13:44,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:44,451 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:13:55,938 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:13:55,964 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:55,964 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:13:56,164 - app.utils.memory_management - INFO - Garbage collection: collected 802 objects
2025-07-18 10:13:56,164 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:13:56,166 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:14:37,670 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:14:37,693 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:14:37,693 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:14:37,896 - app.utils.memory_management - INFO - Garbage collection: collected 710 objects
2025-07-18 10:14:37,897 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:14:37,898 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:15:12,201 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:15:12,224 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:15:12,225 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:15:12,432 - app.utils.memory_management - INFO - Garbage collection: collected 710 objects
2025-07-18 10:15:12,433 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:15:12,434 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:15:39,207 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:15:49,124 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:15:49,126 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:15:49,357 - app.utils.memory_management - INFO - Garbage collection: collected 1737 objects
2025-07-18 10:15:49,358 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:15:49,359 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:15:55,570 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:16:01,260 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:01,262 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:16:01,449 - app.utils.memory_management - INFO - Garbage collection: collected 2621 objects
2025-07-18 10:16:01,450 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:01,451 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:16:03,059 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:16:08,460 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-18 10:16:08,488 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-18 10:16:08,676 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:08,677 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 10:16:08,871 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:08,872 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:08,873 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-18 10:16:08,874 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-18 10:16:08,874 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_30min.joblib or saved_models/COMI_rf_30min.pkl
2025-07-18 10:16:08,874 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-18 10:16:08,874 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-18 10:16:08,874 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:08,875 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:08,875 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-18 10:16:08,875 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-18 10:16:08,899 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-18 10:16:09,088 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:09,090 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 10:16:09,288 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:09,289 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:09,291 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-18 10:16:09,291 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-18 10:16:09,291 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_60min.joblib or saved_models/COMI_rf_60min.pkl
2025-07-18 10:16:09,291 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-18 10:16:09,292 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-18 10:16:09,293 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:09,293 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:09,293 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-18 10:16:09,323 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 10:16:09,327 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 10:16:09,328 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-18 10:16:09,348 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-18 10:16:09,532 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:09,532 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 10:16:09,729 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:09,729 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:09,731 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-07-18 10:16:09,731 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-07-18 10:16:09,731 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_rf_1440min.joblib or saved_models/COMI_rf_1440min.pkl
2025-07-18 10:16:09,731 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-18 10:16:09,731 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-18 10:16:09,731 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:09,732 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:09,732 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-18 10:16:09,947 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:09,948 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:16:10,149 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-18 10:16:10,149 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:10,150 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:16:14,374 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:16:19,793 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:19,796 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:16:20,002 - app.utils.memory_management - INFO - Garbage collection: collected 2819 objects
2025-07-18 10:16:20,003 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:20,004 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:16:21,762 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:16:27,052 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-18 10:16:27,084 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-18 10:16:27,271 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:27,272 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 10:16:27,465 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:27,466 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:27,467 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-18 10:16:27,467 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-18 10:16:27,468 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_30min.joblib or saved_models/COMI_gb_30min.pkl
2025-07-18 10:16:27,468 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-18 10:16:27,487 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-18 10:16:27,487 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:27,488 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:27,488 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-18 10:16:27,488 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-18 10:16:27,508 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-18 10:16:27,702 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:27,703 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 10:16:27,918 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:27,919 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:27,921 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-18 10:16:27,922 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-18 10:16:27,922 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_60min.joblib or saved_models/COMI_gb_60min.pkl
2025-07-18 10:16:27,923 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-18 10:16:27,943 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-18 10:16:27,943 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:27,944 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:27,944 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-18 10:16:27,947 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using gb
2025-07-18 10:16:27,950 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 10:16:27,953 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-18 10:16:27,955 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 10:16:27,957 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-18 10:16:27,980 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-18 10:16:28,176 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:28,176 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 10:16:28,379 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:28,379 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:28,382 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-07-18 10:16:28,383 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-07-18 10:16:28,383 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_gb_1440min.joblib or saved_models/COMI_gb_1440min.pkl
2025-07-18 10:16:28,384 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-18 10:16:28,402 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-18 10:16:28,402 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:28,403 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:28,403 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-18 10:16:28,629 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:28,630 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:16:28,843 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-18 10:16:28,844 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:28,844 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:16:33,093 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:16:33,107 - app - INFO - Found 14 stock files in data/stocks
2025-07-18 10:16:38,621 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:38,624 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:16:38,822 - app.utils.memory_management - INFO - Garbage collection: collected 2826 objects
2025-07-18 10:16:38,823 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:38,823 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:16:39,621 - app - INFO - Using TensorFlow-based LSTM model
2025-07-18 10:16:44,416 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-18 10:16:44,443 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-18 10:16:44,639 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:44,640 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_30_scaler.pkl (0.53 KB)
2025-07-18 10:16:44,842 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:44,843 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:44,845 - models.predict - INFO - Using scikit-learn lr model for 30 minutes horizon
2025-07-18 10:16:44,845 - models.predict - INFO - Loading lr model for COMI with horizon 30
2025-07-18 10:16:44,845 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_30min.joblib or saved_models/COMI_lr_30min.pkl
2025-07-18 10:16:44,845 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-18 10:16:44,845 - models.predict - ERROR - Error in prediction for horizon 30: Model not trained or loaded
2025-07-18 10:16:44,846 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:44,846 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:44,846 - models.predict - INFO - Prediction for 30 minutes horizon: 89980.0
2025-07-18 10:16:44,846 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-18 10:16:44,867 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-18 10:16:45,054 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:45,055 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_60_scaler.pkl (0.53 KB)
2025-07-18 10:16:45,254 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:45,255 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:45,257 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-18 10:16:45,257 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-18 10:16:45,257 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_60min.joblib or saved_models/COMI_lr_60min.pkl
2025-07-18 10:16:45,257 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-18 10:16:45,258 - models.predict - ERROR - Error in prediction for horizon 60: Model not trained or loaded
2025-07-18 10:16:45,258 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:45,258 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:45,258 - models.predict - INFO - Prediction for 60 minutes horizon: 89980.0
2025-07-18 10:16:45,262 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using lr
2025-07-18 10:16:45,265 - app.utils.data_processing - INFO - No model found for COMI with 240 minutes horizon using rf
2025-07-18 10:16:45,269 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-18 10:16:45,272 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-18 10:16:45,273 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-18 10:16:45,293 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-18 10:16:45,481 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:45,482 - app.utils.data_processing - INFO - Found scaler at saved_models/COMI_1440_scaler.pkl (0.53 KB)
2025-07-18 10:16:45,676 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:45,676 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-18 10:16:45,677 - models.predict - INFO - Using scikit-learn lr model for 1440 minutes horizon
2025-07-18 10:16:45,678 - models.predict - INFO - Loading lr model for COMI with horizon 1440
2025-07-18 10:16:45,678 - models.sklearn_model - INFO - Attempting to load model from saved_models/COMI_lr_1440min.joblib or saved_models/COMI_lr_1440min.pkl
2025-07-18 10:16:45,678 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-18 10:16:45,678 - models.predict - ERROR - Error in prediction for horizon 1440: Model not trained or loaded
2025-07-18 10:16:45,679 - models.predict - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/predict.py", line 563, in predict_future_prices
    prediction = model.predict(X_pred)
  File "/Users/<USER>/Downloads/AI Stocks Bot/models/sklearn_model.py", line 426, in predict
    raise ValueError("Model not trained or loaded")
ValueError: Model not trained or loaded

2025-07-18 10:16:45,679 - models.predict - INFO - Using fallback price due to error: 89980.0
2025-07-18 10:16:45,679 - models.predict - INFO - Prediction for 1440 minutes horizon: 89980.0
2025-07-18 10:16:45,906 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:45,907 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:16:46,117 - app.utils.memory_management - INFO - Garbage collection: collected 885 objects
2025-07-18 10:16:46,118 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:16:46,119 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:29:50,872 - app - INFO - Cleaning up resources...
2025-07-18 10:29:50,878 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:29:50,879 - app.utils.memory_management - INFO - Memory before cleanup: 0.00 MB
2025-07-18 10:29:51,392 - app.utils.memory_management - INFO - Garbage collection: collected 1915 objects
2025-07-18 10:29:51,393 - app.utils.memory_management - WARNING - psutil not available. Cannot get detailed memory usage.
2025-07-18 10:29:51,393 - app.utils.memory_management - INFO - Memory after cleanup: 0.00 MB (freed 0.00 MB)
2025-07-18 10:29:51,393 - app - INFO - Application shutdown complete
