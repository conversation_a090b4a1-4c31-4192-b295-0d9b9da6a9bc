2025-07-04 23:16:57,981 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-04 23:17:04,054 - app - INFO - Memory management utilities loaded
2025-07-04 23:17:04,060 - app - INFO - Error handling utilities loaded
2025-07-04 23:17:04,069 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-04 23:17:04,073 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-04 23:17:04,076 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-04 23:17:04,077 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-04 23:17:04,096 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-04 23:17:04,109 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-04 23:17:04,110 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-04 23:17:04,119 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-04 23:17:04,123 - app - INFO - Applied NumPy fix
2025-07-04 23:17:04,125 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-04 23:17:04,128 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-04 23:17:04,130 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-04 23:17:04,137 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-04 23:17:04,137 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-04 23:17:04,138 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-04 23:17:04,138 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-04 23:17:04,139 - app - INFO - Applied NumPy BitGenerator fix
2025-07-04 23:17:28,583 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-07-04 23:17:28,583 - app - INFO - Applied TensorFlow fix
2025-07-04 23:17:28,588 - app.config - INFO - Configuration initialized
2025-07-04 23:17:28,600 - models.train - INFO - TensorFlow version: 2.9.1
2025-07-04 23:17:28,889 - models.train - INFO - TensorFlow test successful
2025-07-04 23:17:34,565 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-07-04 23:17:34,566 - models.train - INFO - Transformer model is available
2025-07-04 23:17:34,566 - models.train - INFO - Using TensorFlow-based models
2025-07-04 23:17:34,572 - models.predict - INFO - Transformer model is available for predictions
2025-07-04 23:17:34,573 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-04 23:17:34,581 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-04 23:17:36,660 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-04 23:17:36,662 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-04 23:17:36,662 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-04 23:17:36,662 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-04 23:17:36,662 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-04 23:17:36,662 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-04 23:17:36,662 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-04 23:17:36,664 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-04 23:17:36,664 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-04 23:17:36,664 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-04 23:17:37,073 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-04 23:17:37,077 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:17:37,936 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-04 23:17:40,756 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-04 23:17:41,113 - app.utils.session_state - INFO - Initializing session state
2025-07-04 23:17:41,115 - app.utils.session_state - INFO - Session state initialized
2025-07-04 23:17:42,332 - app - INFO - Found 14 stock files in data/stocks
2025-07-04 23:17:42,415 - app.utils.memory_management - INFO - Memory before cleanup: 391.61 MB
2025-07-04 23:17:42,735 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-04 23:17:42,735 - app.utils.memory_management - INFO - Memory after cleanup: 391.66 MB (freed -0.05 MB)
2025-07-04 23:17:49,826 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:17:49,912 - app.utils.memory_management - INFO - Memory before cleanup: 396.31 MB
2025-07-04 23:17:50,347 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-04 23:17:50,347 - app.utils.memory_management - INFO - Memory after cleanup: 396.31 MB (freed 0.00 MB)
2025-07-04 23:17:51,395 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:17:51,557 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.13 seconds
2025-07-04 23:17:51,559 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-07-04 23:17:51,559 - app - INFO - Data shape: (750, 36)
2025-07-04 23:17:51,560 - app - INFO - File COMI contains 2025 data
2025-07-04 23:17:51,650 - app - INFO - Feature engineering for COMI completed in 0.09 seconds
2025-07-04 23:17:51,651 - app - INFO - Features shape: (750, 36)
2025-07-04 23:17:51,691 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-07-04 23:17:51,695 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-07-04 23:17:51,696 - app - INFO - Data shape: (750, 36)
2025-07-04 23:17:51,696 - app - INFO - File COMI contains 2025 data
2025-07-04 23:17:51,703 - app.utils.memory_management - INFO - Memory before cleanup: 401.55 MB
2025-07-04 23:17:51,948 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-07-04 23:17:51,948 - app.utils.memory_management - INFO - Memory after cleanup: 401.55 MB (freed -0.00 MB)
2025-07-04 23:17:52,119 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:17:52,308 - app.utils.memory_management - INFO - Memory before cleanup: 403.64 MB
2025-07-04 23:17:52,515 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-07-04 23:17:52,516 - app.utils.memory_management - INFO - Memory after cleanup: 403.64 MB (freed 0.00 MB)
2025-07-04 23:18:00,353 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:18:00,527 - app.utils.memory_management - INFO - Memory before cleanup: 404.11 MB
2025-07-04 23:18:00,810 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-07-04 23:18:00,873 - app.utils.memory_management - INFO - Memory after cleanup: 404.11 MB (freed 0.00 MB)
2025-07-04 23:18:10,169 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:18:10,258 - app.utils.memory_management - INFO - Memory before cleanup: 404.28 MB
2025-07-04 23:18:10,629 - app.utils.memory_management - INFO - Garbage collection: collected 229 objects
2025-07-04 23:18:10,637 - app.utils.memory_management - INFO - Memory after cleanup: 404.28 MB (freed 0.00 MB)
2025-07-04 23:18:10,843 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:18:27,402 - app.pages.ai_advisor - WARNING - Advanced prediction module not available, using fallback
2025-07-04 23:18:27,404 - app.pages.ai_advisor - INFO - Generating hybrid predictions for COMI
2025-07-04 23:18:27,404 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-07-04 23:18:27,404 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-07-04 23:18:27,406 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-04 23:18:27,504 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-04 23:18:27,662 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-04 23:18:27,899 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.04s
2025-07-04 23:18:27,901 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-04 23:18:28,232 - models.hybrid_model - INFO - XGBoost is available
2025-07-04 23:18:28,232 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-04 23:18:28,232 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-04 23:18:28,232 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-07-04 23:18:28,232 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-07-04 23:18:28,308 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-04 23:18:28,309 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-04 23:18:28,309 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-04 23:18:28,318 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:18:28,318 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-04 23:18:28,319 - models.predict - INFO - Prediction for 30 minutes horizon: 74.11819835939582
2025-07-04 23:18:28,409 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-04 23:18:28,588 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-04 23:18:28,758 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:28,759 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-04 23:18:28,761 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-04 23:18:28,761 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-07-04 23:18:28,763 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-07-04 23:18:28,804 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-04 23:18:28,823 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-04 23:18:28,823 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-04 23:18:28,824 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:18:28,825 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7917314838161941
2025-07-04 23:18:28,825 - models.predict - INFO - Prediction for 30 minutes horizon: 76.07539936561052
2025-07-04 23:18:28,917 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-04 23:18:29,132 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-04 23:18:29,299 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-04 23:18:29,301 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-04 23:18:29,301 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-07-04 23:18:30,882 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-04 23:18:33,158 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:18:33,158 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9345698356628418
2025-07-04 23:18:33,158 - models.predict - INFO - Prediction for 30 minutes horizon: 81.54468070295958
2025-07-04 23:18:33,158 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 77.24609280932196
2025-07-04 23:18:33,158 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-07-04 23:18:33,158 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-04 23:18:33,243 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-04 23:18:33,467 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-04 23:18:33,634 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.09 MB, Percent: 0.00%, Execution time: 0.03s
2025-07-04 23:18:33,634 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-04 23:18:33,634 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-04 23:18:33,634 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-04 23:18:33,653 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-04 23:18:33,703 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-04 23:18:33,703 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-04 23:18:33,703 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-04 23:18:33,718 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:18:33,718 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7406162917613983
2025-07-04 23:18:33,718 - models.predict - INFO - Prediction for 60 minutes horizon: 74.11819835939582
2025-07-04 23:18:33,803 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-04 23:18:33,968 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-04 23:18:34,122 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:34,122 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-04 23:18:34,122 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-04 23:18:34,122 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-07-04 23:18:34,152 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-07-04 23:18:34,189 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-04 23:18:34,206 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-04 23:18:34,206 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-04 23:18:34,206 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:18:34,206 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7917314838161941
2025-07-04 23:18:34,206 - models.predict - INFO - Prediction for 60 minutes horizon: 76.07539936561052
2025-07-04 23:18:34,290 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-04 23:18:34,504 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-04 23:18:34,710 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:34,712 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-04 23:18:34,712 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-04 23:18:35,909 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-04 23:18:37,665 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:18:37,665 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 1.351715326309204
2025-07-04 23:18:37,667 - models.predict - INFO - Prediction for 60 minutes horizon: 97.51718400796054
2025-07-04 23:18:37,672 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 82.57026057765563
2025-07-04 23:18:37,673 - app.models.adaptive - INFO - No valid models for COMI with 240min horizon, using equal weights
2025-07-04 23:18:37,673 - app.models.predict - INFO - Ensemble weights for COMI with 240min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-04 23:18:37,849 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-04 23:18:38,130 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-04 23:18:38,483 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.09 MB, Percent: 0.00%, Execution time: 0.13s
2025-07-04 23:18:38,485 - models.predict - INFO - Using scikit-learn rf model for 240 minutes horizon
2025-07-04 23:18:38,485 - models.predict - INFO - Loading rf model for COMI with horizon 240
2025-07-04 23:18:38,485 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_240min.joblib
2025-07-04 23:18:38,485 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_240min.joblib, searching for alternatives...
2025-07-04 23:18:38,489 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-07-04 23:18:38,493 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-07-04 23:18:38,494 - models.predict - WARNING - Model file not found or import error for rf with horizon 240: No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-07-04 23:18:38,496 - models.predict - INFO - Skipping rf model for horizon 240 - model not trained for this horizon
2025-07-04 23:18:38,605 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-04 23:18:38,769 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-04 23:18:38,969 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.10 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:38,973 - models.predict - INFO - Using scikit-learn gb model for 240 minutes horizon
2025-07-04 23:18:38,973 - models.predict - INFO - Loading gb model for COMI with horizon 240
2025-07-04 23:18:38,973 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_240min.joblib
2025-07-04 23:18:38,973 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_240min.joblib, searching for alternatives...
2025-07-04 23:18:38,973 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-07-04 23:18:38,973 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-07-04 23:18:38,973 - models.predict - WARNING - Model file not found or import error for gb with horizon 240: No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-07-04 23:18:38,981 - models.predict - INFO - Skipping gb model for horizon 240 - model not trained for this horizon
2025-07-04 23:18:39,060 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-04 23:18:39,222 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-04 23:18:39,377 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:39,379 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-07-04 23:18:39,379 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-07-04 23:18:39,379 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-07-04 23:18:39,379 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-07-04 23:18:39,381 - app.models.predict - WARNING - No models have predictions for 240min horizon, using original ensemble
2025-07-04 23:18:39,444 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-04 23:18:39,615 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-04 23:18:39,775 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:39,777 - models.predict - INFO - Using RobustEnsembleModel for 240 minutes horizon
2025-07-04 23:18:39,791 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_240min.joblib
2025-07-04 23:18:39,792 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 240
2025-07-04 23:18:39,792 - models.predict - INFO - Created fallback ensemble model with base price: 84.3
2025-07-04 23:18:39,793 - models.predict - INFO - Loading ensemble model for COMI with horizon 240
2025-07-04 23:18:39,793 - models.predict - INFO - Ensemble model already loaded
2025-07-04 23:18:39,813 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-07-04 23:18:39,813 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 82.88993328539193
2025-07-04 23:18:39,813 - models.predict - WARNING - Prediction 4584.068090674209 is too far from current price 84.3, using fallback
2025-07-04 23:18:39,813 - models.predict - INFO - Prediction for 240 minutes horizon: 85.69830253159076
2025-07-04 23:18:39,815 - app.models.adaptive - INFO - No valid models for COMI with 1440min horizon, using equal weights
2025-07-04 23:18:39,815 - app.models.predict - INFO - Ensemble weights for COMI with 1440min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-04 23:18:39,884 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-04 23:18:40,107 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-04 23:18:40,308 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-04 23:18:40,308 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-07-04 23:18:40,308 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-07-04 23:18:40,308 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_1440min.joblib
2025-07-04 23:18:40,308 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_1440min.joblib
2025-07-04 23:18:40,377 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-04 23:18:40,377 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-04 23:18:40,377 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-04 23:18:40,393 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:18:40,393 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7814051270484924
2025-07-04 23:18:40,393 - models.predict - INFO - Prediction for 1440 minutes horizon: 84.5209990287759
2025-07-04 23:18:40,458 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-04 23:18:40,624 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-04 23:18:40,802 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:40,804 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-07-04 23:18:40,804 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-07-04 23:18:40,804 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_1440min.joblib
2025-07-04 23:18:40,806 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_1440min.joblib
2025-07-04 23:18:40,851 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-04 23:18:40,878 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-04 23:18:40,878 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-04 23:18:40,879 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:18:40,880 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7518808425780584
2025-07-04 23:18:40,880 - models.predict - INFO - Prediction for 1440 minutes horizon: 82.90306833020503
2025-07-04 23:18:40,961 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-04 23:18:41,152 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-04 23:18:41,319 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:41,319 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-04 23:18:41,319 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-07-04 23:18:42,226 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-04 23:18:43,544 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:18:43,544 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7969218492507935
2025-07-04 23:18:43,544 - models.predict - INFO - Prediction for 1440 minutes horizon: 85.37131535794687
2025-07-04 23:18:43,549 - app.models.predict - INFO - Adaptive ensemble prediction for 1440min horizon: 84.26512757230927
2025-07-04 23:18:43,551 - app.models.adaptive - INFO - No valid models for COMI with 4320min horizon, using equal weights
2025-07-04 23:18:43,551 - app.models.predict - INFO - Ensemble weights for COMI with 4320min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-04 23:18:43,628 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-04 23:18:43,811 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-04 23:18:44,012 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.06 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-04 23:18:44,019 - models.predict - INFO - Using scikit-learn rf model for 4320 minutes horizon
2025-07-04 23:18:44,019 - models.predict - INFO - Loading rf model for COMI with horizon 4320
2025-07-04 23:18:44,019 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4320min.joblib
2025-07-04 23:18:44,019 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4320min.joblib
2025-07-04 23:18:44,078 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-04 23:18:44,078 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-04 23:18:44,078 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-04 23:18:44,078 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:18:44,078 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7842993110418319
2025-07-04 23:18:44,078 - models.predict - INFO - Prediction for 4320 minutes horizon: 84.67960030274837
2025-07-04 23:18:44,165 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-04 23:18:44,347 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-04 23:18:44,511 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:44,513 - models.predict - INFO - Using scikit-learn gb model for 4320 minutes horizon
2025-07-04 23:18:44,513 - models.predict - INFO - Loading gb model for COMI with horizon 4320
2025-07-04 23:18:44,513 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_4320min.joblib
2025-07-04 23:18:44,514 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_4320min.joblib
2025-07-04 23:18:44,555 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-04 23:18:44,574 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-04 23:18:44,575 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-04 23:18:44,576 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:18:44,578 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7628674455104619
2025-07-04 23:18:44,579 - models.predict - INFO - Prediction for 4320 minutes horizon: 83.5051341372577
2025-07-04 23:18:44,667 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-04 23:18:44,833 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-04 23:18:44,999 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:45,001 - models.predict - INFO - Loading lstm model for COMI with horizon 4320
2025-07-04 23:18:45,002 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_4320min.keras
2025-07-04 23:18:45,918 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-04 23:18:47,281 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:18:47,281 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7770062685012817
2025-07-04 23:18:47,281 - models.predict - INFO - Prediction for 4320 minutes horizon: 84.27994159385888
2025-07-04 23:18:47,281 - app.models.predict - INFO - Adaptive ensemble prediction for 4320min horizon: 84.15489201128831
2025-07-04 23:18:47,281 - app.models.predict - INFO - Prediction completed in 19.88 seconds
2025-07-04 23:18:47,281 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-07-04 23:18:47,281 - app.models.predict - INFO - Using specified model type: lstm
2025-07-04 23:18:47,368 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-04 23:18:47,582 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-04 23:18:47,752 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:47,752 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-04 23:18:47,752 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-07-04 23:18:48,689 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-04 23:18:50,330 - tensorflow - WARNING - 5 out of the last 5 calls to <function Model.make_predict_function.<locals>.predict_function at 0x00000156D79803A0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-07-04 23:18:50,336 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:18:50,336 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9345698356628418
2025-07-04 23:18:50,340 - models.predict - INFO - Prediction for 30 minutes horizon: 81.54468070295958
2025-07-04 23:18:50,340 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-04 23:18:50,543 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-04 23:18:50,701 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:50,701 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-04 23:18:50,701 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-04 23:18:51,594 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-04 23:18:53,407 - tensorflow - WARNING - 6 out of the last 6 calls to <function Model.make_predict_function.<locals>.predict_function at 0x00000156D7981B40> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-07-04 23:18:53,419 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:18:53,421 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 1.351715326309204
2025-07-04 23:18:53,422 - models.predict - INFO - Prediction for 60 minutes horizon: 97.51718400796054
2025-07-04 23:18:53,422 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-04 23:18:53,761 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-04 23:18:54,028 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:54,030 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-07-04 23:18:54,036 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-07-04 23:18:54,036 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-07-04 23:18:54,040 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-07-04 23:18:54,043 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-04 23:18:54,405 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-04 23:18:54,749 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-04 23:18:54,756 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-04 23:18:54,756 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-07-04 23:18:57,017 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-04 23:18:58,915 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:18:58,915 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7969218492507935
2025-07-04 23:18:58,918 - models.predict - INFO - Prediction for 1440 minutes horizon: 85.37131535794687
2025-07-04 23:18:58,918 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-04 23:18:59,220 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-04 23:18:59,490 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:18:59,497 - models.predict - INFO - Loading lstm model for COMI with horizon 4320
2025-07-04 23:18:59,498 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_4320min.keras
2025-07-04 23:19:00,942 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-04 23:19:02,429 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:19:02,429 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7770062685012817
2025-07-04 23:19:02,429 - models.predict - INFO - Prediction for 4320 minutes horizon: 84.27994159385888
2025-07-04 23:19:02,438 - app.models.predict - INFO - Prediction completed in 15.16 seconds
2025-07-04 23:19:02,438 - app.models.predict - INFO - Using specified model type: bilstm
2025-07-04 23:19:02,520 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-04 23:19:02,762 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-04 23:19:02,914 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:19:02,929 - models.predict - INFO - Loading bilstm model for COMI with horizon 30
2025-07-04 23:19:02,929 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_30min.keras
2025-07-04 23:19:05,124 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-04 23:19:07,643 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:19:07,643 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 1.22274911403656
2025-07-04 23:19:07,645 - models.predict - INFO - Prediction for 30 minutes horizon: 92.57906697697825
2025-07-04 23:19:07,646 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-04 23:19:07,910 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-04 23:19:08,133 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:19:08,135 - models.predict - INFO - Loading bilstm model for COMI with horizon 60
2025-07-04 23:19:08,140 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_60min.keras
2025-07-04 23:19:11,775 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-04 23:19:14,974 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:19:14,975 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 1.2781462669372559
2025-07-04 23:19:14,975 - models.predict - INFO - Prediction for 60 minutes horizon: 94.70022428931782
2025-07-04 23:19:14,975 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-04 23:19:15,295 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-04 23:19:15,465 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:19:15,466 - models.predict - INFO - Loading bilstm model for COMI with horizon 240
2025-07-04 23:19:15,467 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-07-04 23:19:15,467 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 240: Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-07-04 23:19:15,467 - models.predict - INFO - Skipping bilstm model for horizon 240 - model not trained for this horizon
2025-07-04 23:19:15,467 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-04 23:19:15,735 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-04 23:19:15,923 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:19:15,926 - models.predict - INFO - Loading bilstm model for COMI with horizon 1440
2025-07-04 23:19:15,927 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_1440min.keras
2025-07-04 23:19:18,277 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-04 23:19:20,786 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:19:20,787 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7715983390808105
2025-07-04 23:19:20,787 - models.predict - INFO - Prediction for 1440 minutes horizon: 83.98358707817717
2025-07-04 23:19:20,788 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-04 23:19:20,999 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-04 23:19:21,173 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:19:21,173 - models.predict - INFO - Loading bilstm model for COMI with horizon 4320
2025-07-04 23:19:21,173 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_4320min.keras
2025-07-04 23:19:23,047 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-04 23:19:25,685 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-04 23:19:25,685 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.765579104423523
2025-07-04 23:19:25,685 - models.predict - INFO - Prediction for 4320 minutes horizon: 83.65373303738983
2025-07-04 23:19:25,689 - app.models.predict - INFO - Prediction completed in 23.25 seconds
2025-07-04 23:19:25,689 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-07-04 23:19:25,979 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-04 23:19:26,068 - cmdstanpy - DEBUG - Adding TBB (D:\AI Stocks Bot\python310_venv\lib\site-packages\prophet\stan_model\cmdstan-2.33.1\stan\lib\stan_math\lib\tbb) to PATH
2025-07-04 23:19:26,088 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-04 23:19:26,117 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\y9_ep6gr.json
2025-07-04 23:19:26,176 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\yfvf72yq.json
2025-07-04 23:19:26,187 - cmdstanpy - DEBUG - idx 0
2025-07-04 23:19:26,189 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-04 23:19:26,189 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=95922', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\y9_ep6gr.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\yfvf72yq.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\prophet_modeli0um2opv\\prophet_model-20250704231926.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-04 23:19:26,189 - cmdstanpy - INFO - Chain [1] start processing
2025-07-04 23:19:27,545 - cmdstanpy - INFO - Chain [1] done processing
2025-07-04 23:19:27,620 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-04 23:19:27,708 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-04 23:19:27,715 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-04 23:19:27,737 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\655kxgxq.json
2025-07-04 23:19:27,789 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\txuic70k.json
2025-07-04 23:19:27,789 - cmdstanpy - DEBUG - idx 0
2025-07-04 23:19:27,789 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-04 23:19:27,789 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=90402', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\655kxgxq.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\txuic70k.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\prophet_model88cmshih\\prophet_model-20250704231927.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-04 23:19:27,789 - cmdstanpy - INFO - Chain [1] start processing
2025-07-04 23:19:28,841 - cmdstanpy - INFO - Chain [1] done processing
2025-07-04 23:19:28,895 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-04 23:19:28,975 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-04 23:19:28,982 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-04 23:19:29,005 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\fbi3fcof.json
2025-07-04 23:19:29,065 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\6kqu3gqf.json
2025-07-04 23:19:29,067 - cmdstanpy - DEBUG - idx 0
2025-07-04 23:19:29,067 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-04 23:19:29,067 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=25600', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\fbi3fcof.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\6kqu3gqf.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\prophet_modelahcqhkwi\\prophet_model-20250704231929.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-04 23:19:29,069 - cmdstanpy - INFO - Chain [1] start processing
2025-07-04 23:19:30,159 - cmdstanpy - INFO - Chain [1] done processing
2025-07-04 23:19:30,262 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-04 23:19:30,380 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-04 23:19:30,386 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-04 23:19:30,411 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\g8434_v0.json
2025-07-04 23:19:30,468 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\9vl07v6j.json
2025-07-04 23:19:30,470 - cmdstanpy - DEBUG - idx 0
2025-07-04 23:19:30,471 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-04 23:19:30,472 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=15805', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\g8434_v0.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\9vl07v6j.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\prophet_modelrqhb0_b_\\prophet_model-20250704231930.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-04 23:19:30,472 - cmdstanpy - INFO - Chain [1] start processing
2025-07-04 23:19:31,644 - cmdstanpy - INFO - Chain [1] done processing
2025-07-04 23:19:31,847 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-04 23:19:31,980 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-04 23:19:32,001 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-04 23:19:32,026 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\mh14jaj8.json
2025-07-04 23:19:32,110 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpgv0rpxgd\5dxxxwd5.json
2025-07-04 23:19:32,113 - cmdstanpy - DEBUG - idx 0
2025-07-04 23:19:32,113 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-04 23:19:32,114 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=65385', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\mh14jaj8.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\5dxxxwd5.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgv0rpxgd\\prophet_modely1ibu1f2\\prophet_model-20250704231932.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-04 23:19:32,114 - cmdstanpy - INFO - Chain [1] start processing
2025-07-04 23:19:33,637 - cmdstanpy - INFO - Chain [1] done processing
2025-07-04 23:19:33,714 - app.models.hybrid_predict - INFO - Statistical predictions generated for COMI
2025-07-04 23:19:33,793 - app.models.hybrid_predict - INFO - Trend-adjusted predictions generated for COMI
2025-07-04 23:19:33,798 - app.pages.ai_advisor - INFO - Hybrid predictions generated for 5 horizons
2025-07-04 23:19:33,799 - app.pages.ai_advisor - INFO - Detecting market regime for COMI
2025-07-04 23:19:33,802 - app.pages.ai_advisor - INFO - Market regime detected: sideways
2025-07-04 23:19:33,826 - app.pages.ai_advisor - INFO - Created enhanced basic fallback predictions with 3 models
2025-07-04 23:19:33,827 - app.pages.ai_advisor - INFO - Consensus predictions generated for 5 horizons
2025-07-04 23:19:36,662 - app.utils.memory_management - INFO - Memory before cleanup: 572.11 MB
2025-07-04 23:19:37,203 - app.utils.memory_management - INFO - Garbage collection: collected 59133 objects
2025-07-04 23:19:37,204 - app.utils.memory_management - INFO - Memory after cleanup: 485.65 MB (freed 86.46 MB)
2025-07-04 23:20:04,076 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:20:06,938 - app.utils.memory_management - INFO - Memory before cleanup: 116.94 MB
2025-07-04 23:20:07,294 - app.utils.memory_management - INFO - Garbage collection: collected 1299 objects
2025-07-04 23:20:07,295 - app.utils.memory_management - INFO - Memory after cleanup: 296.37 MB (freed -179.43 MB)
2025-07-04 23:20:12,268 - app - INFO - Using TensorFlow-based LSTM model
2025-07-04 23:20:12,355 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-04 23:20:12,444 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-04 23:20:12,652 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-04 23:20:12,823 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.11 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:20:12,825 - models.predict - INFO - Using RobustEnsembleModel for 30 minutes horizon
2025-07-04 23:20:12,918 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_30min.joblib
2025-07-04 23:20:12,920 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 30
2025-07-04 23:20:12,920 - models.predict - INFO - Loading ensemble model for COMI with horizon 30
2025-07-04 23:20:12,920 - models.predict - INFO - Ensemble model already loaded
2025-07-04 23:20:12,951 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:20:12,951 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9908505838908588
2025-07-04 23:20:12,951 - models.predict - INFO - Prediction for 30 minutes horizon: 83.6996708856103
2025-07-04 23:20:12,951 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-04 23:20:12,980 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-04 23:20:13,153 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-04 23:20:13,326 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:20:13,326 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-04 23:20:13,402 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-04 23:20:13,402 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-04 23:20:13,402 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-04 23:20:13,402 - models.predict - INFO - Ensemble model already loaded
2025-07-04 23:20:13,434 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:20:13,434 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.9908505838908588
2025-07-04 23:20:13,434 - models.predict - INFO - Prediction for 60 minutes horizon: 83.6996708856103
2025-07-04 23:20:13,434 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,455 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,466 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,476 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,491 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,504 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,517 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,527 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,543 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-04 23:20:13,550 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-04 23:20:13,566 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using gb
2025-07-04 23:20:13,566 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lr
2025-07-04 23:20:13,582 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using svr
2025-07-04 23:20:13,599 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-04 23:20:13,601 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using bilstm
2025-07-04 23:20:13,620 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using prophet
2025-07-04 23:20:13,634 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using transformer
2025-07-04 23:20:13,634 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-04 23:20:13,645 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-04 23:20:13,666 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-04 23:20:13,847 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-04 23:20:14,041 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-04 23:20:14,041 - models.predict - INFO - Using RobustEnsembleModel for 1440 minutes horizon
2025-07-04 23:20:14,134 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_1440min.joblib
2025-07-04 23:20:14,134 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 1440
2025-07-04 23:20:14,134 - models.predict - INFO - Loading ensemble model for COMI with horizon 1440
2025-07-04 23:20:14,134 - models.predict - INFO - Ensemble model already loaded
2025-07-04 23:20:14,148 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-04 23:20:14,148 - models.predict - INFO - Current price: 84.3, Predicted scaled value: 0.7530065618314385
2025-07-04 23:20:14,148 - models.predict - INFO - Prediction for 1440 minutes horizon: 82.9647577418431
2025-07-04 23:20:14,357 - app.utils.memory_management - INFO - Memory before cleanup: 318.66 MB
2025-07-04 23:20:14,575 - app.utils.memory_management - INFO - Garbage collection: collected 905 objects
2025-07-04 23:20:14,578 - app.utils.memory_management - INFO - Memory after cleanup: 318.66 MB (freed 0.00 MB)
2025-07-04 23:20:23,924 - app - INFO - Cleaning up resources...
2025-07-04 23:20:23,925 - app.utils.memory_management - INFO - Memory before cleanup: 320.70 MB
2025-07-04 23:20:24,116 - app.utils.memory_management - INFO - Garbage collection: collected 314 objects
2025-07-04 23:20:24,118 - app.utils.memory_management - INFO - Memory after cleanup: 320.71 MB (freed -0.01 MB)
2025-07-04 23:20:24,118 - app - INFO - Application shutdown complete
