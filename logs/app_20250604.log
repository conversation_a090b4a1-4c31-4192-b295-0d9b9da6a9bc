2025-06-04 12:33:15,657 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-04 12:33:19,923 - app - INFO - Memory management utilities loaded
2025-06-04 12:33:19,939 - app - INFO - Error handling utilities loaded
2025-06-04 12:33:19,961 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 12:33:19,963 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 12:33:19,975 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 12:33:19,976 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 12:33:19,976 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 12:33:19,976 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 12:33:19,976 - app - INFO - Applied NumPy fix
2025-06-04 12:33:19,985 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 12:33:19,986 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 12:33:19,986 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 12:33:19,987 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 12:33:19,988 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 12:33:19,988 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 12:33:36,147 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 12:33:36,149 - app - INFO - Applied TensorFlow fix
2025-06-04 12:33:36,171 - app.config - INFO - Configuration initialized
2025-06-04 12:33:36,197 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 12:33:36,214 - models.train - INFO - TensorFlow test successful
2025-06-04 12:33:37,544 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 12:33:37,544 - models.train - INFO - Transformer model is available
2025-06-04 12:33:37,546 - models.train - INFO - Using TensorFlow-based models
2025-06-04 12:33:37,560 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 12:33:37,561 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 12:33:37,585 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 12:33:38,067 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 12:33:38,067 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 12:33:38,069 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 12:33:38,225 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 12:33:38,240 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:33:38,879 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 12:33:39,820 - app.utils.session_state - INFO - Initializing session state
2025-06-04 12:33:39,821 - app.utils.session_state - INFO - Session state initialized
2025-06-04 12:33:41,264 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 12:33:41,282 - app.utils.memory_management - INFO - Memory before cleanup: 425.18 MB
2025-06-04 12:33:41,467 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 12:33:41,471 - app.utils.memory_management - INFO - Memory after cleanup: 425.56 MB (freed -0.38 MB)
2025-06-04 12:34:00,363 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:00,555 - app.utils.memory_management - INFO - Memory before cleanup: 429.08 MB
2025-06-04 12:34:00,962 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 12:34:00,964 - app.utils.memory_management - INFO - Memory after cleanup: 429.12 MB (freed -0.04 MB)
2025-06-04 12:34:06,444 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:06,835 - app.utils.memory_management - INFO - Memory before cleanup: 430.14 MB
2025-06-04 12:34:07,137 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-04 12:34:07,139 - app.utils.memory_management - INFO - Memory after cleanup: 430.18 MB (freed -0.04 MB)
2025-06-04 12:34:07,953 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:20,581 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 12:34:20,582 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 12:34:20,586 - app.utils.memory_management - INFO - Memory before cleanup: 431.47 MB
2025-06-04 12:34:20,879 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-04 12:34:20,881 - app.utils.memory_management - INFO - Memory after cleanup: 431.47 MB (freed 0.00 MB)
2025-06-04 12:34:20,883 - app.utils.memory_management - INFO - Memory before cleanup: 431.47 MB
2025-06-04 12:34:21,068 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-04 12:34:21,068 - app.utils.memory_management - INFO - Memory after cleanup: 431.47 MB (freed 0.00 MB)
2025-06-04 12:34:56,063 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:34:56,216 - app.utils.memory_management - INFO - Memory before cleanup: 432.13 MB
2025-06-04 12:34:56,534 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-04 12:34:56,536 - app.utils.memory_management - INFO - Memory after cleanup: 432.13 MB (freed 0.00 MB)
2025-06-04 12:35:00,872 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:35:08,662 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:35:08,662 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:35:09,554 - app.utils.memory_management - INFO - Memory before cleanup: 448.73 MB
2025-06-04 12:35:09,775 - app.utils.memory_management - INFO - Garbage collection: collected 1130 objects
2025-06-04 12:35:09,777 - app.utils.memory_management - INFO - Memory after cleanup: 448.80 MB (freed -0.06 MB)
2025-06-04 12:36:16,932 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:16,988 - app.utils.memory_management - INFO - Memory before cleanup: 450.92 MB
2025-06-04 12:36:17,230 - app.utils.memory_management - INFO - Garbage collection: collected 271 objects
2025-06-04 12:36:17,232 - app.utils.memory_management - INFO - Memory after cleanup: 450.92 MB (freed 0.00 MB)
2025-06-04 12:36:20,439 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:20,469 - app.utils.memory_management - INFO - Memory before cleanup: 450.92 MB
2025-06-04 12:36:20,691 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 12:36:20,691 - app.utils.memory_management - INFO - Memory after cleanup: 450.92 MB (freed 0.00 MB)
2025-06-04 12:36:21,917 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:36:30,471 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 12:36:30,472 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 12:36:30,473 - app.utils.memory_management - INFO - Memory before cleanup: 450.93 MB
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Memory after cleanup: 450.93 MB (freed 0.00 MB)
2025-06-04 12:36:30,672 - app.utils.memory_management - INFO - Memory before cleanup: 450.93 MB
2025-06-04 12:36:30,921 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-04 12:36:30,923 - app.utils.memory_management - INFO - Memory after cleanup: 450.93 MB (freed 0.00 MB)
2025-06-04 12:44:06,456 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:44:06,545 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 12:44:06,571 - app.utils.memory_management - INFO - Memory before cleanup: 451.14 MB
2025-06-04 12:44:06,810 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-04 12:44:06,810 - app.utils.memory_management - INFO - Memory after cleanup: 451.14 MB (freed 0.00 MB)
2025-06-04 12:44:08,668 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 12:44:16,960 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:44:16,962 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 12:44:17,159 - app.utils.memory_management - INFO - Memory before cleanup: 451.19 MB
2025-06-04 12:44:17,355 - app.utils.memory_management - INFO - Garbage collection: collected 1435 objects
2025-06-04 12:44:17,356 - app.utils.memory_management - INFO - Memory after cleanup: 451.19 MB (freed 0.00 MB)
2025-06-04 13:04:25,458 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:04:26,902 - app - INFO - Memory management utilities loaded
2025-06-04 13:04:26,904 - app - INFO - Error handling utilities loaded
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:04:26,906 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:04:26,908 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:04:26,908 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 13:04:26,910 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 13:04:26,920 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 13:04:26,924 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 13:04:26,924 - app - INFO - Applied NumPy fix
2025-06-04 13:04:26,925 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:04:26,926 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 13:04:26,927 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:04:26,930 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:04:26,937 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:04:26,939 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 13:04:30,536 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 13:04:30,539 - app - INFO - Applied TensorFlow fix
2025-06-04 13:04:30,989 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:31,037 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:31,037 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:31,041 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:31,057 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:31,058 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:31,070 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:04:33,436 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:33,436 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:33,438 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:04:33,452 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:04:33,452 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:04:33,460 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:04:35,027 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:35,027 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:04:35,038 - app - INFO - Cleaning up resources...
2025-06-04 13:04:35,043 - app.utils.memory_management - INFO - Memory before cleanup: 366.84 MB
2025-06-04 13:04:35,189 - app.utils.memory_management - INFO - Garbage collection: collected 20 objects
2025-06-04 13:04:35,189 - app.utils.memory_management - INFO - Memory after cleanup: 366.85 MB (freed -0.00 MB)
2025-06-04 13:04:35,189 - app - INFO - Application shutdown complete
2025-06-04 13:05:01,865 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:05:01,886 - app - INFO - Memory management utilities loaded
2025-06-04 13:05:01,892 - app - INFO - Error handling utilities loaded
2025-06-04 13:05:01,897 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:05:01,904 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:05:01,907 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:05:01,910 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:05:49,280 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 13:05:50,477 - app - INFO - Memory management utilities loaded
2025-06-04 13:05:50,479 - app - INFO - Error handling utilities loaded
2025-06-04 13:05:50,480 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 13:05:50,482 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 13:05:50,482 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 13:05:50,483 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 13:05:50,483 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 13:05:50,484 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 13:05:50,485 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 13:05:50,485 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 13:05:50,485 - app - INFO - Applied NumPy fix
2025-06-04 13:05:50,486 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:05:50,487 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 13:05:50,488 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:05:50,489 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:05:50,489 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:05:50,489 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 13:05:55,464 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 13:05:55,465 - app - INFO - Applied TensorFlow fix
2025-06-04 13:05:55,467 - app.config - INFO - Configuration initialized
2025-06-04 13:05:55,470 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 13:05:55,480 - models.train - INFO - TensorFlow test successful
2025-06-04 13:05:56,079 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 13:05:56,079 - models.train - INFO - Transformer model is available
2025-06-04 13:05:56,080 - models.train - INFO - Using TensorFlow-based models
2025-06-04 13:05:56,081 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 13:05:56,081 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 13:05:56,085 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 13:05:56,451 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 13:05:56,451 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 13:05:56,451 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 13:05:56,451 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 13:05:56,452 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 13:05:56,553 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 13:05:56,557 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:05:56,900 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 13:05:57,474 - app.utils.session_state - INFO - Initializing session state
2025-06-04 13:05:57,476 - app.utils.session_state - INFO - Session state initialized
2025-06-04 13:05:58,830 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 13:05:58,850 - app.utils.memory_management - INFO - Memory before cleanup: 425.21 MB
2025-06-04 13:05:59,128 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 13:05:59,129 - app.utils.memory_management - INFO - Memory after cleanup: 425.61 MB (freed -0.39 MB)
2025-06-04 13:06:07,380 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:06:07,540 - app.utils.memory_management - INFO - Memory before cleanup: 429.07 MB
2025-06-04 13:06:07,728 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 13:06:07,729 - app.utils.memory_management - INFO - Memory after cleanup: 429.07 MB (freed -0.00 MB)
2025-06-04 13:06:12,355 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 13:06:12,399 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 13:06:12,420 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 13:06:12,421 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 13:06:12,423 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 13:06:14,082 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:06:14,082 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 13:06:14,843 - app.utils.memory_management - INFO - Memory before cleanup: 448.17 MB
2025-06-04 13:06:15,080 - app.utils.memory_management - INFO - Garbage collection: collected 1193 objects
2025-06-04 13:06:15,081 - app.utils.memory_management - INFO - Memory after cleanup: 448.27 MB (freed -0.10 MB)
2025-06-04 15:14:32,179 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:14:39,510 - app - INFO - Memory management utilities loaded
2025-06-04 15:14:39,519 - app - INFO - Error handling utilities loaded
2025-06-04 15:14:39,525 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:14:39,529 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:14:39,532 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:14:39,533 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:14:39,534 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 15:14:39,535 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 15:14:39,535 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 15:14:39,536 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 15:14:39,536 - app - INFO - Applied NumPy fix
2025-06-04 15:14:39,548 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:14:39,549 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 15:14:39,550 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:14:39,551 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:14:39,551 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:14:39,552 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 15:15:06,586 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 15:15:06,590 - app - INFO - Applied TensorFlow fix
2025-06-04 15:15:06,609 - app.config - INFO - Configuration initialized
2025-06-04 15:15:06,692 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 15:15:06,709 - models.train - INFO - TensorFlow test successful
2025-06-04 15:15:12,094 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 15:15:12,094 - models.train - INFO - Transformer model is available
2025-06-04 15:15:12,094 - models.train - INFO - Using TensorFlow-based models
2025-06-04 15:15:12,105 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 15:15:12,105 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 15:15:12,139 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 15:15:13,996 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 15:15:13,996 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:15:13,997 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:15:14,445 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 15:15:14,460 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:15:15,348 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 15:15:18,539 - app.utils.session_state - INFO - Initializing session state
2025-06-04 15:15:18,540 - app.utils.session_state - INFO - Session state initialized
2025-06-04 15:15:19,963 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:15:19,981 - app.utils.memory_management - INFO - Memory before cleanup: 425.26 MB
2025-06-04 15:15:20,162 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 15:15:20,163 - app.utils.memory_management - INFO - Memory after cleanup: 425.65 MB (freed -0.39 MB)
2025-06-04 15:27:47,940 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:27:47,968 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:27:47,988 - app.utils.memory_management - INFO - Memory before cleanup: 424.50 MB
2025-06-04 15:27:48,156 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 15:27:48,160 - app.utils.memory_management - INFO - Memory after cleanup: 424.51 MB (freed -0.00 MB)
2025-06-04 15:27:51,978 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:27:52,009 - app.utils.memory_management - INFO - Memory before cleanup: 425.38 MB
2025-06-04 15:27:52,291 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-04 15:27:52,292 - app.utils.memory_management - INFO - Memory after cleanup: 425.42 MB (freed -0.04 MB)
2025-06-04 15:27:53,752 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:28:09,581 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 15:28:09,582 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 15:28:09,583 - app.utils.memory_management - INFO - Memory before cleanup: 426.79 MB
2025-06-04 15:28:09,769 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-04 15:28:09,769 - app.utils.memory_management - INFO - Memory after cleanup: 426.79 MB (freed 0.00 MB)
2025-06-04 15:28:09,769 - app.utils.memory_management - INFO - Memory before cleanup: 426.79 MB
2025-06-04 15:28:09,907 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-04 15:28:09,907 - app.utils.memory_management - INFO - Memory after cleanup: 426.79 MB (freed 0.00 MB)
2025-06-04 15:28:47,673 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:28:50,863 - app.utils.memory_management - INFO - Memory before cleanup: 427.47 MB
2025-06-04 15:28:51,031 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-04 15:28:51,031 - app.utils.memory_management - INFO - Memory after cleanup: 427.47 MB (freed 0.00 MB)
2025-06-04 15:28:54,287 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:28:54,333 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:28:54,401 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-09 to 2025-06-03
2025-06-04 15:28:54,403 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:28:54,407 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:28:55,990 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:28:55,990 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:28:56,961 - app.utils.memory_management - INFO - Memory before cleanup: 443.82 MB
2025-06-04 15:28:57,155 - app.utils.memory_management - INFO - Garbage collection: collected 1109 objects
2025-06-04 15:28:57,161 - app.utils.memory_management - INFO - Memory after cleanup: 443.82 MB (freed 0.00 MB)
2025-06-04 15:29:28,090 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:29:28,152 - app.utils.memory_management - INFO - Memory before cleanup: 445.87 MB
2025-06-04 15:29:28,363 - app.utils.memory_management - INFO - Garbage collection: collected 281 objects
2025-06-04 15:29:28,365 - app.utils.memory_management - INFO - Memory after cleanup: 445.87 MB (freed 0.00 MB)
2025-06-04 15:34:10,802 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:10,807 - app.utils.session_state - INFO - Initializing session state
2025-06-04 15:34:10,808 - app.utils.session_state - INFO - Session state initialized
2025-06-04 15:34:10,819 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:34:10,829 - app.utils.memory_management - INFO - Memory before cleanup: 445.90 MB
2025-06-04 15:34:11,077 - app.utils.memory_management - INFO - Garbage collection: collected 181 objects
2025-06-04 15:34:11,079 - app.utils.memory_management - INFO - Memory after cleanup: 445.90 MB (freed 0.00 MB)
2025-06-04 15:34:17,486 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:17,509 - app.utils.memory_management - INFO - Memory before cleanup: 399.76 MB
2025-06-04 15:34:17,691 - app.utils.memory_management - INFO - Garbage collection: collected 200 objects
2025-06-04 15:34:17,691 - app.utils.memory_management - INFO - Memory after cleanup: 399.76 MB (freed 0.00 MB)
2025-06-04 15:34:24,948 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:25,114 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:25,150 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:25,150 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:25,154 - app.utils.memory_management - INFO - Memory before cleanup: 400.09 MB
2025-06-04 15:34:25,413 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-04 15:34:25,413 - app.utils.memory_management - INFO - Memory after cleanup: 400.09 MB (freed 0.00 MB)
2025-06-04 15:34:35,744 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:35,812 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:35,819 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:35,820 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:35,822 - app.utils.memory_management - INFO - Memory before cleanup: 400.26 MB
2025-06-04 15:34:36,056 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-04 15:34:36,056 - app.utils.memory_management - INFO - Memory after cleanup: 400.26 MB (freed 0.00 MB)
2025-06-04 15:34:38,696 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:38,789 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:38,800 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:38,806 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:38,810 - app.utils.memory_management - INFO - Memory before cleanup: 400.34 MB
2025-06-04 15:34:39,003 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-06-04 15:34:39,004 - app.utils.memory_management - INFO - Memory after cleanup: 400.34 MB (freed 0.00 MB)
2025-06-04 15:34:39,778 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:39,851 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:39,858 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:39,858 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:39,861 - app.utils.memory_management - INFO - Memory before cleanup: 400.40 MB
2025-06-04 15:34:40,051 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-04 15:34:40,054 - app.utils.memory_management - INFO - Memory after cleanup: 400.40 MB (freed 0.00 MB)
2025-06-04 15:34:42,121 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:42,261 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:42,303 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:42,304 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:42,306 - app.utils.memory_management - INFO - Memory before cleanup: 400.83 MB
2025-06-04 15:34:42,548 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-04 15:34:42,548 - app.utils.memory_management - INFO - Memory after cleanup: 399.83 MB (freed 1.00 MB)
2025-06-04 15:34:46,888 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:47,028 - app - INFO - File COMI contains 2025 data
2025-06-04 15:34:47,056 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-04 15:34:47,057 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 15:34:47,059 - app.utils.memory_management - INFO - Memory before cleanup: 401.19 MB
2025-06-04 15:34:47,246 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-04 15:34:47,247 - app.utils.memory_management - INFO - Memory after cleanup: 401.19 MB (freed 0.00 MB)
2025-06-04 15:34:53,652 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:53,699 - app.utils.memory_management - INFO - Memory before cleanup: 401.15 MB
2025-06-04 15:34:54,039 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-06-04 15:34:54,042 - app.utils.memory_management - INFO - Memory after cleanup: 401.15 MB (freed 0.00 MB)
2025-06-04 15:34:56,812 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:34:56,860 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:34:56,878 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 15:34:56,879 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:34:56,882 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:34:58,967 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:34:58,967 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:34:59,229 - app.utils.memory_management - INFO - Memory before cleanup: 402.59 MB
2025-06-04 15:34:59,415 - app.utils.memory_management - INFO - Garbage collection: collected 1756 objects
2025-06-04 15:34:59,415 - app.utils.memory_management - INFO - Memory after cleanup: 402.59 MB (freed 0.00 MB)
2025-06-04 15:46:11,733 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:46:11,764 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:46:11,800 - app.utils.memory_management - INFO - Memory before cleanup: 393.98 MB
2025-06-04 15:46:12,049 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-06-04 15:46:12,049 - app.utils.memory_management - INFO - Memory after cleanup: 393.98 MB (freed 0.00 MB)
2025-06-04 15:46:15,683 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:46:15,719 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:46:15,735 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 15:46:15,736 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:46:15,739 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:46:17,366 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:46:17,366 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:46:17,642 - app.utils.memory_management - INFO - Memory before cleanup: 395.21 MB
2025-06-04 15:46:17,825 - app.utils.memory_management - INFO - Garbage collection: collected 1700 objects
2025-06-04 15:46:17,826 - app.utils.memory_management - INFO - Memory after cleanup: 395.21 MB (freed 0.00 MB)
2025-06-04 15:48:00,687 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:48:00,743 - app.utils.memory_management - INFO - Memory before cleanup: 395.30 MB
2025-06-04 15:48:01,017 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-06-04 15:48:01,019 - app.utils.memory_management - INFO - Memory after cleanup: 395.30 MB (freed 0.00 MB)
2025-06-04 15:48:03,004 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:48:03,035 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:48:03,050 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 15:48:03,051 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:48:03,053 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:48:04,622 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:48:04,624 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:48:04,849 - app.utils.memory_management - INFO - Memory before cleanup: 395.33 MB
2025-06-04 15:48:04,999 - app.utils.memory_management - INFO - Garbage collection: collected 2051 objects
2025-06-04 15:48:04,999 - app.utils.memory_management - INFO - Memory after cleanup: 395.33 MB (freed 0.00 MB)
2025-06-04 15:55:06,397 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:55:07,678 - app - INFO - Memory management utilities loaded
2025-06-04 15:55:07,679 - app - INFO - Error handling utilities loaded
2025-06-04 15:55:07,680 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:55:07,682 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:55:07,682 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:55:07,682 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:55:07,683 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 15:55:07,684 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 15:55:07,684 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 15:55:07,687 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 15:55:07,688 - app - INFO - Applied NumPy fix
2025-06-04 15:55:07,692 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:55:07,692 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:55:07,693 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:55:07,693 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 15:55:07,693 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:55:07,694 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:55:07,694 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:55:07,694 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 15:55:26,819 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 15:55:26,820 - app - INFO - Applied TensorFlow fix
2025-06-04 15:55:26,910 - app.components.advanced_smc_features - INFO - Found 7 high swing points with lookback 5
2025-06-04 15:55:26,947 - app.components.advanced_smc_features - INFO - Found 6 low swing points with lookback 5
2025-06-04 15:55:26,947 - app.components.advanced_smc_features - INFO - Found 7 swing highs and 6 swing lows
2025-06-04 15:55:26,951 - app.components.advanced_smc_features - INFO - Detected 5 BOS events
2025-06-04 15:55:27,024 - app.components.advanced_smc_features - INFO - Found 7 high swing points with lookback 5
2025-06-04 15:55:27,056 - app.components.advanced_smc_features - INFO - Found 6 low swing points with lookback 5
2025-06-04 15:55:27,059 - app.components.advanced_smc_features - INFO - Found 7 swing highs and 6 swing lows
2025-06-04 15:55:27,059 - app.components.advanced_smc_features - INFO - Detected 5 BOS events
2025-06-04 15:55:27,694 - app - INFO - Cleaning up resources...
2025-06-04 15:55:27,703 - app.utils.memory_management - INFO - Memory before cleanup: 322.03 MB
2025-06-04 15:55:27,816 - app.utils.memory_management - INFO - Garbage collection: collected 411 objects
2025-06-04 15:55:27,816 - app.utils.memory_management - INFO - Memory after cleanup: 322.32 MB (freed -0.29 MB)
2025-06-04 15:55:27,817 - app - INFO - Application shutdown complete
2025-06-04 15:57:15,191 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:57:15,201 - app - INFO - Memory management utilities loaded
2025-06-04 15:57:15,203 - app - INFO - Error handling utilities loaded
2025-06-04 15:57:15,206 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:57:15,206 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:57:15,208 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:57:15,209 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:57:40,945 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:57:41,690 - app - INFO - Memory management utilities loaded
2025-06-04 15:57:41,690 - app - INFO - Error handling utilities loaded
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:57:41,690 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 15:57:41,699 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 15:57:41,699 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 15:57:41,699 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 15:57:41,699 - app - INFO - Applied NumPy fix
2025-06-04 15:57:41,701 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:57:41,701 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:57:41,701 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:57:41,701 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 15:57:41,702 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:57:41,702 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:57:41,702 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:57:41,702 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 15:57:46,395 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 15:57:46,395 - app - INFO - Applied TensorFlow fix
2025-06-04 15:57:46,421 - app - INFO - Cleaning up resources...
2025-06-04 15:57:46,424 - app.utils.memory_management - INFO - Memory before cleanup: 309.56 MB
2025-06-04 15:57:46,532 - app.utils.memory_management - INFO - Garbage collection: collected 33 objects
2025-06-04 15:57:46,532 - app.utils.memory_management - INFO - Memory after cleanup: 309.90 MB (freed -0.34 MB)
2025-06-04 15:57:46,533 - app - INFO - Application shutdown complete
2025-06-04 15:58:23,215 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 15:58:25,019 - app - INFO - Memory management utilities loaded
2025-06-04 15:58:25,021 - app - INFO - Error handling utilities loaded
2025-06-04 15:58:25,024 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 15:58:25,034 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 15:58:25,042 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 15:58:25,042 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 15:58:25,044 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 15:58:25,045 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 15:58:25,045 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 15:58:25,045 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 15:58:25,051 - app - INFO - Applied NumPy fix
2025-06-04 15:58:25,054 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:58:25,055 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:58:25,057 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:58:25,058 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 15:58:25,058 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:58:25,058 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:58:25,060 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:58:25,060 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 15:58:29,774 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 15:58:29,774 - app - INFO - Applied TensorFlow fix
2025-06-04 15:58:29,776 - app.config - INFO - Configuration initialized
2025-06-04 15:58:29,781 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 15:58:29,793 - models.train - INFO - TensorFlow test successful
2025-06-04 15:58:34,030 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 15:58:34,030 - models.train - INFO - Transformer model is available
2025-06-04 15:58:34,030 - models.train - INFO - Using TensorFlow-based models
2025-06-04 15:58:34,032 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 15:58:34,032 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 15:58:34,035 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 15:58:35,642 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 15:58:35,643 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 15:58:35,643 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 15:58:35,643 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 15:58:35,643 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 15:58:35,643 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 15:58:35,643 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 15:58:35,644 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 15:58:35,644 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 15:58:35,644 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 15:58:35,932 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 15:58:35,935 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:58:36,444 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 15:58:38,561 - app.utils.session_state - INFO - Initializing session state
2025-06-04 15:58:38,563 - app.utils.session_state - INFO - Session state initialized
2025-06-04 15:58:39,821 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 15:58:39,835 - app.utils.memory_management - INFO - Memory before cleanup: 427.42 MB
2025-06-04 15:58:40,010 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 15:58:40,011 - app.utils.memory_management - INFO - Memory after cleanup: 427.42 MB (freed -0.00 MB)
2025-06-04 15:58:44,260 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:58:44,418 - app.utils.memory_management - INFO - Memory before cleanup: 429.98 MB
2025-06-04 15:58:44,588 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 15:58:44,589 - app.utils.memory_management - INFO - Memory after cleanup: 429.98 MB (freed 0.00 MB)
2025-06-04 15:58:48,605 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 15:58:48,648 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 15:58:48,669 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 15:58:48,670 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 15:58:48,675 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 15:58:49,022 - app.components.advanced_smc_features - INFO - Found 12 high swing points with lookback 10
2025-06-04 15:58:49,071 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-04 15:58:49,071 - app.components.advanced_smc_features - INFO - Found 12 swing highs and 8 swing lows
2025-06-04 15:58:49,078 - app.components.advanced_smc_features - INFO - Detected 11 BOS events
2025-06-04 15:58:49,454 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 15:58:49,505 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 15:58:49,505 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 15:58:49,505 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 15:58:49,855 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 15:58:49,905 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 15:58:49,905 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 15:58:49,911 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 15:58:50,217 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 15:58:50,270 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 15:58:50,271 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 15:58:50,275 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 15:58:50,319 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:58:50,319 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 15:58:51,271 - app.utils.memory_management - INFO - Memory before cleanup: 447.48 MB
2025-06-04 15:58:51,584 - app.utils.memory_management - INFO - Garbage collection: collected 1100 objects
2025-06-04 15:58:51,584 - app.utils.memory_management - INFO - Memory after cleanup: 447.52 MB (freed -0.04 MB)
2025-06-04 16:01:07,069 - app - INFO - Cleaning up resources...
2025-06-04 16:01:07,069 - app.utils.memory_management - INFO - Memory before cleanup: 449.26 MB
2025-06-04 16:01:07,304 - app.utils.memory_management - INFO - Garbage collection: collected 359 objects
2025-06-04 16:01:07,306 - app.utils.memory_management - INFO - Memory after cleanup: 449.26 MB (freed 0.00 MB)
2025-06-04 16:01:07,306 - app - INFO - Application shutdown complete
2025-06-04 16:01:08,865 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 16:01:09,646 - app - INFO - Memory management utilities loaded
2025-06-04 16:01:09,646 - app - INFO - Error handling utilities loaded
2025-06-04 16:01:09,646 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 16:01:09,646 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 16:01:09,646 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 16:01:09,646 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 16:01:09,646 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 16:01:09,646 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 16:01:09,646 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 16:01:09,646 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 16:01:09,654 - app - INFO - Applied NumPy fix
2025-06-04 16:01:09,655 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:01:09,656 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:01:09,656 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:01:09,656 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 16:01:09,656 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:01:09,658 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:01:09,658 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:01:09,658 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 16:01:15,832 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 16:01:15,832 - app - INFO - Applied TensorFlow fix
2025-06-04 16:01:15,854 - app - INFO - Cleaning up resources...
2025-06-04 16:01:15,857 - app.utils.memory_management - INFO - Memory before cleanup: 309.68 MB
2025-06-04 16:01:15,982 - app.utils.memory_management - INFO - Garbage collection: collected 33 objects
2025-06-04 16:01:15,983 - app.utils.memory_management - INFO - Memory after cleanup: 310.03 MB (freed -0.35 MB)
2025-06-04 16:01:15,984 - app - INFO - Application shutdown complete
2025-06-04 16:02:31,234 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 16:02:31,910 - app - INFO - Memory management utilities loaded
2025-06-04 16:02:31,910 - app - INFO - Error handling utilities loaded
2025-06-04 16:02:31,910 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 16:02:31,910 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 16:02:31,910 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 16:02:31,910 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 16:02:31,910 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 16:02:31,916 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 16:02:31,916 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 16:02:31,916 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 16:02:31,917 - app - INFO - Applied NumPy fix
2025-06-04 16:02:31,919 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:02:31,920 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:02:31,920 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:02:31,920 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 16:02:31,921 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:02:31,922 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:02:31,922 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:02:31,925 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 16:02:36,379 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 16:02:36,379 - app - INFO - Applied TensorFlow fix
2025-06-04 16:02:36,436 - app.components.advanced_smc_features - INFO - Found 7 high swing points with lookback 5
2025-06-04 16:02:36,458 - app.components.advanced_smc_features - INFO - Found 6 low swing points with lookback 5
2025-06-04 16:02:36,458 - app.components.advanced_smc_features - INFO - Found 7 swing highs and 6 swing lows
2025-06-04 16:02:36,474 - app.components.advanced_smc_features - INFO - Detected 5 BOS events
2025-06-04 16:02:36,500 - app - INFO - Cleaning up resources...
2025-06-04 16:02:36,503 - app.utils.memory_management - INFO - Memory before cleanup: 312.36 MB
2025-06-04 16:02:36,612 - app.utils.memory_management - INFO - Garbage collection: collected 33 objects
2025-06-04 16:02:36,612 - app.utils.memory_management - INFO - Memory after cleanup: 312.37 MB (freed -0.01 MB)
2025-06-04 16:02:36,612 - app - INFO - Application shutdown complete
2025-06-04 16:03:27,854 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 16:03:29,427 - app - INFO - Memory management utilities loaded
2025-06-04 16:03:29,430 - app - INFO - Error handling utilities loaded
2025-06-04 16:03:29,432 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 16:03:29,434 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 16:03:29,434 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 16:03:29,435 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 16:03:29,435 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 16:03:29,438 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 16:03:29,439 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 16:03:29,440 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 16:03:29,441 - app - INFO - Applied NumPy fix
2025-06-04 16:03:29,444 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:03:29,444 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:03:29,445 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:03:29,445 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 16:03:29,446 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:03:29,446 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:03:29,447 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:03:29,447 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 16:03:35,771 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 16:03:35,773 - app - INFO - Applied TensorFlow fix
2025-06-04 16:03:35,781 - app.config - INFO - Configuration initialized
2025-06-04 16:03:35,791 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 16:03:35,805 - models.train - INFO - TensorFlow test successful
2025-06-04 16:03:36,712 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 16:03:36,715 - models.train - INFO - Transformer model is available
2025-06-04 16:03:36,716 - models.train - INFO - Using TensorFlow-based models
2025-06-04 16:03:36,718 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 16:03:36,718 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 16:03:36,722 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 16:03:37,093 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:03:37,093 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:03:37,095 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:03:37,095 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:03:37,095 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:03:37,095 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 16:03:37,095 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 16:03:37,097 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:03:37,097 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:03:37,098 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:03:37,228 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 16:03:37,231 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:03:37,710 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 16:03:38,418 - app.utils.session_state - INFO - Initializing session state
2025-06-04 16:03:38,420 - app.utils.session_state - INFO - Session state initialized
2025-06-04 16:03:39,816 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 16:03:39,830 - app.utils.memory_management - INFO - Memory before cleanup: 425.27 MB
2025-06-04 16:03:40,057 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-04 16:03:40,058 - app.utils.memory_management - INFO - Memory after cleanup: 425.64 MB (freed -0.36 MB)
2025-06-04 16:03:44,862 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:03:45,207 - app.utils.memory_management - INFO - Memory before cleanup: 429.19 MB
2025-06-04 16:03:45,492 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:03:45,494 - app.utils.memory_management - INFO - Memory after cleanup: 429.19 MB (freed 0.00 MB)
2025-06-04 16:03:47,803 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:03:47,862 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 16:03:47,889 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 16:03:47,893 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 16:03:47,898 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 16:03:48,317 - app.components.advanced_smc_features - INFO - Found 12 high swing points with lookback 10
2025-06-04 16:03:48,380 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-04 16:03:48,382 - app.components.advanced_smc_features - INFO - Found 12 swing highs and 8 swing lows
2025-06-04 16:03:48,402 - app.components.advanced_smc_features - INFO - Detected 11 BOS events
2025-06-04 16:03:48,846 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:03:48,903 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:03:48,903 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:03:48,916 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:03:49,270 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:03:49,329 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:03:49,329 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:03:49,329 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:03:49,694 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:03:49,753 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:03:49,753 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:03:49,767 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:03:49,796 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 16:03:49,796 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 16:03:51,230 - app.utils.memory_management - INFO - Memory before cleanup: 448.57 MB
2025-06-04 16:03:51,468 - app.utils.memory_management - INFO - Garbage collection: collected 1107 objects
2025-06-04 16:03:51,470 - app.utils.memory_management - INFO - Memory after cleanup: 448.61 MB (freed -0.04 MB)
2025-06-04 16:19:47,749 - app - INFO - Cleaning up resources...
2025-06-04 16:19:47,749 - app.utils.memory_management - INFO - Memory before cleanup: 450.52 MB
2025-06-04 16:19:47,926 - app.utils.memory_management - INFO - Garbage collection: collected 359 objects
2025-06-04 16:19:47,927 - app.utils.memory_management - INFO - Memory after cleanup: 450.52 MB (freed 0.00 MB)
2025-06-04 16:19:47,927 - app - INFO - Application shutdown complete
2025-06-04 16:20:03,010 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 16:20:05,554 - app - INFO - Memory management utilities loaded
2025-06-04 16:20:05,559 - app - INFO - Error handling utilities loaded
2025-06-04 16:20:05,562 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 16:20:05,565 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 16:20:05,567 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 16:20:05,568 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 16:20:05,572 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 16:20:05,573 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 16:20:05,574 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 16:20:05,574 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 16:20:05,574 - app - INFO - Applied NumPy fix
2025-06-04 16:20:05,577 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:20:05,579 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:20:05,579 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:20:05,579 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 16:20:05,581 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:20:05,582 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:20:05,583 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:20:05,583 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 16:20:11,684 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 16:20:11,690 - app - INFO - Applied TensorFlow fix
2025-06-04 16:20:11,698 - app.config - INFO - Configuration initialized
2025-06-04 16:20:11,732 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 16:20:11,751 - models.train - INFO - TensorFlow test successful
2025-06-04 16:20:12,561 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 16:20:12,563 - models.train - INFO - Transformer model is available
2025-06-04 16:20:12,563 - models.train - INFO - Using TensorFlow-based models
2025-06-04 16:20:12,565 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 16:20:12,565 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 16:20:12,569 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 16:20:13,006 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:20:13,006 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:20:13,007 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:20:13,007 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:20:13,007 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:20:13,007 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 16:20:13,007 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 16:20:13,008 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:20:13,008 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:20:13,008 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:20:13,162 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 16:20:13,164 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:20:13,690 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 16:20:14,546 - app.utils.session_state - INFO - Initializing session state
2025-06-04 16:20:14,547 - app.utils.session_state - INFO - Session state initialized
2025-06-04 16:20:15,919 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 16:20:15,943 - app.utils.memory_management - INFO - Memory before cleanup: 428.63 MB
2025-06-04 16:20:16,140 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:20:16,142 - app.utils.memory_management - INFO - Memory after cleanup: 428.64 MB (freed -0.01 MB)
2025-06-04 16:20:20,142 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:20:20,300 - app.utils.memory_management - INFO - Memory before cleanup: 431.00 MB
2025-06-04 16:20:20,573 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:20:20,573 - app.utils.memory_management - INFO - Memory after cleanup: 431.00 MB (freed 0.00 MB)
2025-06-04 16:20:22,609 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:20:22,659 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 16:20:22,680 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 16:20:22,681 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 16:20:22,687 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 16:20:23,075 - app.components.advanced_smc_features - INFO - Found 12 high swing points with lookback 10
2025-06-04 16:20:23,131 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-04 16:20:23,132 - app.components.advanced_smc_features - INFO - Found 12 swing highs and 8 swing lows
2025-06-04 16:20:23,141 - app.components.advanced_smc_features - INFO - Detected 11 BOS events
2025-06-04 16:20:23,520 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:20:23,583 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:20:23,583 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:20:23,589 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:20:23,920 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:20:24,004 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:20:24,004 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:20:24,010 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:20:24,346 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:20:24,404 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:20:24,404 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:20:24,404 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:20:24,460 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 16:20:24,464 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 16:20:25,730 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 16:20:25,741 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 16:20:25,746 - app.utils.memory_management - INFO - Memory before cleanup: 448.61 MB
2025-06-04 16:20:25,963 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-04 16:20:25,965 - app.utils.memory_management - INFO - Memory after cleanup: 448.65 MB (freed -0.04 MB)
2025-06-04 16:20:25,969 - app.utils.memory_management - INFO - Memory before cleanup: 448.65 MB
2025-06-04 16:20:26,238 - app.utils.memory_management - INFO - Garbage collection: collected 840 objects
2025-06-04 16:20:26,242 - app.utils.memory_management - INFO - Memory after cleanup: 448.65 MB (freed 0.00 MB)
2025-06-04 16:20:31,635 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:20:31,640 - app.utils.session_state - INFO - Initializing session state
2025-06-04 16:20:31,642 - app.utils.session_state - INFO - Session state initialized
2025-06-04 16:20:31,668 - app.utils.memory_management - INFO - Memory before cleanup: 450.56 MB
2025-06-04 16:20:31,902 - app.utils.memory_management - INFO - Garbage collection: collected 287 objects
2025-06-04 16:20:31,904 - app.utils.memory_management - INFO - Memory after cleanup: 450.56 MB (freed 0.00 MB)
2025-06-04 16:20:38,482 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:20:38,538 - app.utils.memory_management - INFO - Memory before cleanup: 451.12 MB
2025-06-04 16:20:38,795 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-04 16:20:38,795 - app.utils.memory_management - INFO - Memory after cleanup: 451.12 MB (freed 0.00 MB)
2025-06-04 16:20:40,390 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:20:40,439 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 16:20:40,462 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 16:20:40,464 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 16:20:40,467 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 16:20:40,876 - app.components.advanced_smc_features - INFO - Found 12 high swing points with lookback 10
2025-06-04 16:20:40,929 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-04 16:20:40,929 - app.components.advanced_smc_features - INFO - Found 12 swing highs and 8 swing lows
2025-06-04 16:20:40,937 - app.components.advanced_smc_features - INFO - Detected 11 BOS events
2025-06-04 16:20:41,338 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:20:41,404 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:20:41,404 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:20:41,412 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:20:41,738 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:20:41,804 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:20:41,804 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:20:41,804 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:20:42,142 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 16:20:42,205 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 16:20:42,205 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 16:20:42,205 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 16:20:42,249 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 16:20:42,251 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 16:20:42,536 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-04 16:20:42,537 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-04 16:20:42,539 - app.utils.memory_management - INFO - Memory before cleanup: 452.81 MB
2025-06-04 16:20:42,768 - app.utils.memory_management - INFO - Garbage collection: collected 894 objects
2025-06-04 16:20:42,768 - app.utils.memory_management - INFO - Memory after cleanup: 452.81 MB (freed 0.00 MB)
2025-06-04 16:20:42,770 - app.utils.memory_management - INFO - Memory before cleanup: 452.81 MB
2025-06-04 16:20:42,970 - app.utils.memory_management - INFO - Garbage collection: collected 840 objects
2025-06-04 16:20:42,975 - app.utils.memory_management - INFO - Memory after cleanup: 452.81 MB (freed 0.00 MB)
2025-06-04 16:50:03,532 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 16:50:06,081 - app - INFO - Memory management utilities loaded
2025-06-04 16:50:06,083 - app - INFO - Error handling utilities loaded
2025-06-04 16:50:06,086 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 16:50:06,098 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 16:50:06,100 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 16:50:06,100 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 16:50:06,104 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 16:50:06,114 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 16:50:06,117 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 16:50:06,117 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 16:50:06,119 - app - INFO - Applied NumPy fix
2025-06-04 16:50:06,122 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:50:06,122 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:50:06,132 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:50:06,134 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 16:50:06,134 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:50:06,136 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:50:06,137 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:50:06,138 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 16:50:12,325 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 16:50:12,325 - app - INFO - Applied TensorFlow fix
2025-06-04 16:50:12,328 - app.config - INFO - Configuration initialized
2025-06-04 16:50:12,329 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 16:50:12,343 - models.train - INFO - TensorFlow test successful
2025-06-04 16:50:12,892 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 16:50:12,892 - models.train - INFO - Transformer model is available
2025-06-04 16:50:12,893 - models.train - INFO - Using TensorFlow-based models
2025-06-04 16:50:12,894 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 16:50:12,894 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 16:50:12,899 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 16:50:13,244 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:50:13,244 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:50:13,244 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:50:13,244 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:50:13,244 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:50:13,245 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 16:50:13,245 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 16:50:13,245 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:50:13,245 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:50:13,247 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:50:13,362 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 16:50:13,365 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:50:13,740 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 16:50:14,433 - app.utils.session_state - INFO - Initializing session state
2025-06-04 16:50:14,435 - app.utils.session_state - INFO - Session state initialized
2025-06-04 16:50:16,187 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 16:50:16,212 - app.utils.memory_management - INFO - Memory before cleanup: 427.48 MB
2025-06-04 16:50:16,472 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:50:16,474 - app.utils.memory_management - INFO - Memory after cleanup: 427.49 MB (freed -0.01 MB)
2025-06-04 16:50:28,750 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:50:28,802 - app.utils.memory_management - INFO - Memory before cleanup: 429.76 MB
2025-06-04 16:50:29,014 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:50:29,014 - app.utils.memory_management - INFO - Memory after cleanup: 429.76 MB (freed -0.00 MB)
2025-06-04 16:50:40,137 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:50:40,261 - app.utils.memory_management - INFO - Memory before cleanup: 429.77 MB
2025-06-04 16:50:40,553 - app.utils.memory_management - INFO - Garbage collection: collected 181 objects
2025-06-04 16:50:40,555 - app.utils.memory_management - INFO - Memory after cleanup: 429.81 MB (freed -0.04 MB)
2025-06-04 16:56:16,238 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 16:56:16,244 - app - INFO - Memory management utilities loaded
2025-06-04 16:56:16,246 - app - INFO - Error handling utilities loaded
2025-06-04 16:56:16,248 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 16:56:16,248 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 16:56:16,249 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 16:56:16,249 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 16:56:42,492 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 16:56:44,555 - app - INFO - Memory management utilities loaded
2025-06-04 16:56:44,559 - app - INFO - Error handling utilities loaded
2025-06-04 16:56:44,561 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 16:56:44,565 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 16:56:44,567 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 16:56:44,568 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 16:56:44,571 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 16:56:44,574 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 16:56:44,581 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 16:56:44,583 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 16:56:44,583 - app - INFO - Applied NumPy fix
2025-06-04 16:56:44,587 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:56:44,587 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:56:44,589 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:56:44,594 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 16:56:44,595 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:56:44,597 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:56:44,598 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:56:44,599 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 16:56:52,477 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 16:56:52,479 - app - INFO - Applied TensorFlow fix
2025-06-04 16:56:52,483 - app.config - INFO - Configuration initialized
2025-06-04 16:56:52,491 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 16:56:52,509 - models.train - INFO - TensorFlow test successful
2025-06-04 16:56:53,205 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 16:56:53,206 - models.train - INFO - Transformer model is available
2025-06-04 16:56:53,208 - models.train - INFO - Using TensorFlow-based models
2025-06-04 16:56:53,211 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 16:56:53,211 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 16:56:53,215 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 16:56:53,619 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:56:53,619 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:56:53,621 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:56:53,623 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:56:53,625 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:56:53,625 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 16:56:53,627 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 16:56:53,629 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:56:53,631 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:56:53,631 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:56:53,762 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 16:56:53,774 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:56:54,257 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 16:56:55,326 - app.pages.smc_analysis - ERROR - SMC components not available: cannot import name 'display_predictive_analytics_panel' from 'app.components.predictive_analytics' (D:\AI Stocks Bot\app\components\predictive_analytics.py)
2025-06-04 16:56:55,347 - app.utils.session_state - INFO - Initializing session state
2025-06-04 16:56:55,349 - app.utils.session_state - INFO - Session state initialized
2025-06-04 16:56:56,883 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 16:56:56,926 - app.utils.memory_management - INFO - Memory before cleanup: 427.93 MB
2025-06-04 16:56:57,222 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:56:57,223 - app.utils.memory_management - INFO - Memory after cleanup: 427.94 MB (freed -0.01 MB)
2025-06-04 16:56:59,900 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:56:59,963 - app.utils.memory_management - INFO - Memory before cleanup: 430.21 MB
2025-06-04 16:57:00,219 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:57:00,219 - app.utils.memory_management - INFO - Memory after cleanup: 430.21 MB (freed 0.00 MB)
2025-06-04 16:58:43,367 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 16:58:46,346 - app - INFO - Memory management utilities loaded
2025-06-04 16:58:46,349 - app - INFO - Error handling utilities loaded
2025-06-04 16:58:46,350 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 16:58:46,352 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 16:58:46,352 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 16:58:46,353 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 16:58:46,354 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 16:58:46,355 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 16:58:46,356 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 16:58:46,357 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 16:58:46,358 - app - INFO - Applied NumPy fix
2025-06-04 16:58:46,361 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:58:46,363 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:58:46,364 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:58:46,365 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 16:58:46,367 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:58:46,368 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:58:46,369 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:58:46,371 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 16:58:51,301 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 16:58:51,305 - app - INFO - Applied TensorFlow fix
2025-06-04 16:58:51,308 - app.config - INFO - Configuration initialized
2025-06-04 16:58:51,325 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 16:58:51,337 - models.train - INFO - TensorFlow test successful
2025-06-04 16:58:51,989 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 16:58:51,990 - models.train - INFO - Transformer model is available
2025-06-04 16:58:51,991 - models.train - INFO - Using TensorFlow-based models
2025-06-04 16:58:51,994 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 16:58:51,995 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 16:58:52,000 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 16:58:52,409 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:58:52,409 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 16:58:52,410 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 16:58:52,410 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 16:58:52,410 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 16:58:52,410 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 16:58:52,410 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 16:58:52,411 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 16:58:52,411 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 16:58:52,411 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 16:58:52,535 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 16:58:52,538 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:58:52,949 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 16:58:53,621 - app.pages.smc_analysis - ERROR - SMC components not available: cannot import name 'display_predictive_analytics_panel' from 'app.components.predictive_analytics' (D:\AI Stocks Bot\app\components\predictive_analytics.py)
2025-06-04 16:58:53,637 - app.utils.session_state - INFO - Initializing session state
2025-06-04 16:58:53,640 - app.utils.session_state - INFO - Session state initialized
2025-06-04 16:58:55,428 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 16:58:55,456 - app.utils.memory_management - INFO - Memory before cleanup: 424.10 MB
2025-06-04 16:58:55,685 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:58:55,688 - app.utils.memory_management - INFO - Memory after cleanup: 424.55 MB (freed -0.45 MB)
2025-06-04 16:58:58,262 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 16:58:58,283 - app.utils.memory_management - INFO - Memory before cleanup: 427.90 MB
2025-06-04 16:58:58,458 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 16:58:58,458 - app.utils.memory_management - INFO - Memory after cleanup: 427.90 MB (freed -0.00 MB)
2025-06-04 17:01:26,667 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 17:01:28,808 - app - INFO - Memory management utilities loaded
2025-06-04 17:01:28,811 - app - INFO - Error handling utilities loaded
2025-06-04 17:01:28,813 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 17:01:28,815 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 17:01:28,817 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 17:01:28,819 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 17:01:28,821 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 17:01:28,822 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 17:01:28,823 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 17:01:28,825 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 17:01:28,829 - app - INFO - Applied NumPy fix
2025-06-04 17:01:28,871 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 17:01:28,881 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 17:01:28,884 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 17:01:28,886 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 17:01:28,900 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 17:01:29,008 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 17:01:29,009 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 17:01:29,010 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 17:01:35,231 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 17:01:35,231 - app - INFO - Applied TensorFlow fix
2025-06-04 17:01:35,233 - app.config - INFO - Configuration initialized
2025-06-04 17:01:35,240 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 17:01:35,249 - models.train - INFO - TensorFlow test successful
2025-06-04 17:01:35,830 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 17:01:35,831 - models.train - INFO - Transformer model is available
2025-06-04 17:01:35,831 - models.train - INFO - Using TensorFlow-based models
2025-06-04 17:01:35,832 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 17:01:35,833 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 17:01:35,835 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 17:01:36,179 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 17:01:36,180 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 17:01:36,180 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 17:01:36,180 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 17:01:36,180 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 17:01:36,180 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 17:01:36,181 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 17:01:36,181 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 17:01:36,181 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 17:01:36,181 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 17:01:36,286 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 17:01:36,290 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 17:01:36,659 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 17:01:37,545 - app.utils.session_state - INFO - Initializing session state
2025-06-04 17:01:37,547 - app.utils.session_state - INFO - Session state initialized
2025-06-04 17:01:39,289 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 17:01:39,324 - app.utils.memory_management - INFO - Memory before cleanup: 427.79 MB
2025-06-04 17:01:39,569 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-04 17:01:39,571 - app.utils.memory_management - INFO - Memory after cleanup: 427.80 MB (freed -0.01 MB)
2025-06-04 17:01:42,481 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 17:01:42,535 - app.utils.memory_management - INFO - Memory before cleanup: 430.07 MB
2025-06-04 17:01:42,768 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 17:01:42,770 - app.utils.memory_management - INFO - Memory after cleanup: 430.07 MB (freed 0.00 MB)
2025-06-04 17:01:48,296 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 17:01:48,342 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 17:01:48,414 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 17:01:48,416 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 17:01:48,424 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 17:01:48,832 - app.components.advanced_smc_features - INFO - Found 12 high swing points with lookback 10
2025-06-04 17:01:48,899 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-04 17:01:48,900 - app.components.advanced_smc_features - INFO - Found 12 swing highs and 8 swing lows
2025-06-04 17:01:48,908 - app.components.advanced_smc_features - INFO - Detected 11 BOS events
2025-06-04 17:01:49,448 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 17:01:49,532 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 17:01:49,534 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 17:01:49,547 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 17:01:49,908 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 17:01:49,966 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 17:01:49,966 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 17:01:49,975 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 17:01:50,345 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 17:01:50,406 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 17:01:50,406 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 17:01:50,417 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 17:01:50,464 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 17:01:50,481 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 17:01:51,969 - app.utils.memory_management - INFO - Memory before cleanup: 447.59 MB
2025-06-04 17:01:52,163 - app.utils.memory_management - INFO - Garbage collection: collected 1307 objects
2025-06-04 17:01:52,163 - app.utils.memory_management - INFO - Memory after cleanup: 447.63 MB (freed -0.04 MB)
2025-06-04 17:01:58,223 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 17:01:58,307 - app.utils.session_state - INFO - Initializing session state
2025-06-04 17:01:58,319 - app.utils.session_state - INFO - Session state initialized
2025-06-04 17:01:58,370 - app.utils.memory_management - INFO - Memory before cleanup: 449.52 MB
2025-06-04 17:01:58,575 - app.utils.memory_management - INFO - Garbage collection: collected 267 objects
2025-06-04 17:01:58,577 - app.utils.memory_management - INFO - Memory after cleanup: 449.52 MB (freed 0.00 MB)
2025-06-04 17:02:04,431 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 17:02:04,454 - app.utils.memory_management - INFO - Memory before cleanup: 449.81 MB
2025-06-04 17:02:04,680 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-04 17:02:04,681 - app.utils.memory_management - INFO - Memory after cleanup: 449.81 MB (freed 0.00 MB)
2025-06-04 17:02:06,878 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 17:02:06,912 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-06-04 17:02:06,939 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-09-10 to 2025-06-04
2025-06-04 17:02:06,940 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-06-04 17:02:06,942 - app.pages.smc_analysis - INFO - SMC Data Quality: 100.0% (180 bars, 267 days)
2025-06-04 17:02:07,416 - app.components.advanced_smc_features - INFO - Found 12 high swing points with lookback 10
2025-06-04 17:02:07,483 - app.components.advanced_smc_features - INFO - Found 8 low swing points with lookback 10
2025-06-04 17:02:07,483 - app.components.advanced_smc_features - INFO - Found 12 swing highs and 8 swing lows
2025-06-04 17:02:07,496 - app.components.advanced_smc_features - INFO - Detected 11 BOS events
2025-06-04 17:02:07,939 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 17:02:08,007 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 17:02:08,009 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 17:02:08,017 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 17:02:08,381 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 17:02:08,450 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 17:02:08,450 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 17:02:08,459 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 17:02:08,813 - app.components.advanced_smc_features - INFO - Found 15 high swing points with lookback 7
2025-06-04 17:02:08,876 - app.components.advanced_smc_features - INFO - Found 12 low swing points with lookback 7
2025-06-04 17:02:08,876 - app.components.advanced_smc_features - INFO - Found 15 swing highs and 12 swing lows
2025-06-04 17:02:08,884 - app.components.advanced_smc_features - INFO - Detected 14 BOS events
2025-06-04 17:02:08,926 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 17:02:08,928 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-04 17:02:09,232 - app.utils.memory_management - INFO - Memory before cleanup: 450.85 MB
2025-06-04 17:02:09,472 - app.utils.memory_management - INFO - Garbage collection: collected 1760 objects
2025-06-04 17:02:09,474 - app.utils.memory_management - INFO - Memory after cleanup: 450.85 MB (freed 0.00 MB)
2025-06-04 20:40:03,028 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:40:03,045 - app.utils.session_state - INFO - Initializing session state
2025-06-04 20:40:03,047 - app.utils.session_state - INFO - Session state initialized
2025-06-04 20:40:03,059 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 20:40:03,072 - app.utils.memory_management - INFO - Memory before cleanup: 289.61 MB
2025-06-04 20:40:03,430 - app.utils.memory_management - INFO - Garbage collection: collected 267 objects
2025-06-04 20:40:03,430 - app.utils.memory_management - INFO - Memory after cleanup: 400.81 MB (freed -111.20 MB)
2025-06-04 20:40:07,567 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:40:07,593 - app.utils.memory_management - INFO - Memory before cleanup: 404.25 MB
2025-06-04 20:40:07,790 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-04 20:40:07,790 - app.utils.memory_management - INFO - Memory after cleanup: 404.27 MB (freed -0.03 MB)
2025-06-04 20:40:21,092 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:40:21,123 - app.utils.memory_management - INFO - Memory before cleanup: 404.28 MB
2025-06-04 20:40:21,317 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 20:40:21,317 - app.utils.memory_management - INFO - Memory after cleanup: 404.28 MB (freed 0.00 MB)
2025-06-04 20:40:22,535 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:40:22,606 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.05 seconds
2025-06-04 20:40:22,608 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:40:22,610 - app - INFO - Data shape: (585, 36)
2025-06-04 20:40:22,612 - app - INFO - File COMI contains 2025 data
2025-06-04 20:40:22,667 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-04 20:40:22,668 - app - INFO - Features shape: (585, 36)
2025-06-04 20:40:22,690 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:40:22,691 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:40:22,693 - app - INFO - Data shape: (585, 36)
2025-06-04 20:40:22,694 - app - INFO - File COMI contains 2025 data
2025-06-04 20:40:22,698 - app.utils.memory_management - INFO - Memory before cleanup: 406.95 MB
2025-06-04 20:40:22,878 - app.utils.memory_management - INFO - Garbage collection: collected 286 objects
2025-06-04 20:40:22,879 - app.utils.memory_management - INFO - Memory after cleanup: 406.95 MB (freed 0.00 MB)
2025-06-04 20:40:23,070 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:40:23,109 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:40:23,143 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:40:23,146 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:40:23,146 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:40:23,147 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:40:23,206 - app.utils.memory_management - INFO - Memory before cleanup: 407.04 MB
2025-06-04 20:40:23,401 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-04 20:40:23,401 - app.utils.memory_management - INFO - Memory after cleanup: 407.04 MB (freed 0.00 MB)
2025-06-04 20:47:32,934 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 20:47:32,947 - app - INFO - Memory management utilities loaded
2025-06-04 20:47:32,952 - app - INFO - Error handling utilities loaded
2025-06-04 20:47:32,954 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 20:47:32,959 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 20:47:32,961 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 20:47:32,962 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 20:48:33,170 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 20:48:34,993 - app - INFO - Memory management utilities loaded
2025-06-04 20:48:34,995 - app - INFO - Error handling utilities loaded
2025-06-04 20:48:34,997 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 20:48:34,999 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 20:48:35,002 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 20:48:35,002 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 20:48:35,004 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 20:48:35,005 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 20:48:35,005 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 20:48:35,008 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 20:48:35,009 - app - INFO - Applied NumPy fix
2025-06-04 20:48:35,010 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 20:48:35,011 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 20:48:35,012 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 20:48:35,013 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 20:48:35,013 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 20:48:35,013 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 20:48:35,013 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 20:48:35,013 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 20:48:41,052 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 20:48:41,055 - app - INFO - Applied TensorFlow fix
2025-06-04 20:48:41,066 - app.config - INFO - Configuration initialized
2025-06-04 20:48:41,090 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 20:48:41,112 - models.train - INFO - TensorFlow test successful
2025-06-04 20:48:42,886 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 20:48:42,887 - models.train - INFO - Transformer model is available
2025-06-04 20:48:42,887 - models.train - INFO - Using TensorFlow-based models
2025-06-04 20:48:42,888 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 20:48:42,889 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 20:48:42,892 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 20:48:43,307 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 20:48:43,308 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 20:48:43,309 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 20:48:43,310 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 20:48:43,311 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 20:48:43,311 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 20:48:43,311 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 20:48:43,312 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 20:48:43,313 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 20:48:43,313 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 20:48:43,495 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 20:48:43,499 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:48:44,035 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 20:48:44,942 - app.utils.session_state - INFO - Initializing session state
2025-06-04 20:48:44,945 - app.utils.session_state - INFO - Session state initialized
2025-06-04 20:48:46,322 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 20:48:46,341 - app.utils.memory_management - INFO - Memory before cleanup: 426.24 MB
2025-06-04 20:48:46,555 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 20:48:46,555 - app.utils.memory_management - INFO - Memory after cleanup: 426.24 MB (freed -0.00 MB)
2025-06-04 20:48:51,571 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:48:51,597 - app.utils.memory_management - INFO - Memory before cleanup: 429.87 MB
2025-06-04 20:48:51,795 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 20:48:51,797 - app.utils.memory_management - INFO - Memory after cleanup: 429.87 MB (freed 0.00 MB)
2025-06-04 20:48:52,834 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:48:52,890 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:48:52,892 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:48:52,894 - app - INFO - Data shape: (585, 36)
2025-06-04 20:48:52,897 - app - INFO - File COMI contains 2025 data
2025-06-04 20:48:52,937 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-04 20:48:52,941 - app - INFO - Features shape: (585, 36)
2025-06-04 20:48:52,968 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:48:52,969 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:48:52,972 - app - INFO - Data shape: (585, 36)
2025-06-04 20:48:52,975 - app - INFO - File COMI contains 2025 data
2025-06-04 20:48:52,984 - app.utils.memory_management - INFO - Memory before cleanup: 433.57 MB
2025-06-04 20:48:53,199 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-04 20:48:53,201 - app.utils.memory_management - INFO - Memory after cleanup: 433.61 MB (freed -0.04 MB)
2025-06-04 20:48:53,364 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:48:53,434 - app.utils.memory_management - INFO - Memory before cleanup: 434.54 MB
2025-06-04 20:48:53,628 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-04 20:48:53,629 - app.utils.memory_management - INFO - Memory after cleanup: 434.52 MB (freed 0.02 MB)
2025-06-04 20:48:55,093 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:48:55,164 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:48:55,204 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:48:55,208 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:48:55,209 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:48:55,210 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:48:55,277 - app.utils.memory_management - INFO - Memory before cleanup: 436.45 MB
2025-06-04 20:48:55,466 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-04 20:48:55,466 - app.utils.memory_management - INFO - Memory after cleanup: 436.45 MB (freed 0.00 MB)
2025-06-04 20:49:04,052 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:49:04,124 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:49:04,176 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-04 20:49:04,176 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:49:04,178 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:49:04,180 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:49:04,248 - app.utils.memory_management - INFO - Memory before cleanup: 436.54 MB
2025-06-04 20:49:04,426 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-04 20:49:04,428 - app.utils.memory_management - INFO - Memory after cleanup: 436.54 MB (freed 0.00 MB)
2025-06-04 20:52:09,273 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:52:09,355 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:52:09,398 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:52:09,401 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:52:09,404 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:52:09,406 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:52:09,504 - app.utils.memory_management - INFO - Memory before cleanup: 436.41 MB
2025-06-04 20:52:09,704 - app.utils.memory_management - INFO - Garbage collection: collected 302 objects
2025-06-04 20:52:09,705 - app.utils.memory_management - INFO - Memory after cleanup: 436.41 MB (freed 0.00 MB)
2025-06-04 20:52:12,276 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:52:12,353 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:52:12,383 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:52:12,384 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:52:12,385 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:52:12,385 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:52:12,451 - app.utils.memory_management - INFO - Memory before cleanup: 436.43 MB
2025-06-04 20:52:12,632 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-04 20:52:12,633 - app.utils.memory_management - INFO - Memory after cleanup: 436.43 MB (freed 0.00 MB)
2025-06-04 20:56:05,760 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 20:56:09,078 - app - INFO - Memory management utilities loaded
2025-06-04 20:56:09,081 - app - INFO - Error handling utilities loaded
2025-06-04 20:56:09,083 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 20:56:09,086 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 20:56:09,087 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 20:56:09,088 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 20:56:09,090 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 20:56:09,092 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 20:56:09,093 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 20:56:09,097 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 20:56:09,098 - app - INFO - Applied NumPy fix
2025-06-04 20:56:09,100 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 20:56:09,101 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 20:56:09,101 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 20:56:09,102 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 20:56:09,103 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 20:56:09,104 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 20:56:09,105 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 20:56:09,107 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 20:56:14,342 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 20:56:14,343 - app - INFO - Applied TensorFlow fix
2025-06-04 20:56:14,347 - app.config - INFO - Configuration initialized
2025-06-04 20:56:14,352 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 20:56:14,362 - models.train - INFO - TensorFlow test successful
2025-06-04 20:56:15,013 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 20:56:15,013 - models.train - INFO - Transformer model is available
2025-06-04 20:56:15,013 - models.train - INFO - Using TensorFlow-based models
2025-06-04 20:56:15,015 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 20:56:15,016 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 20:56:15,019 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 20:56:15,389 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 20:56:15,390 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 20:56:15,390 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 20:56:15,390 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 20:56:15,390 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 20:56:15,391 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 20:56:15,391 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 20:56:15,391 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 20:56:15,391 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 20:56:15,391 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 20:56:15,511 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 20:56:15,514 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:15,949 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 20:56:16,712 - app.utils.session_state - INFO - Initializing session state
2025-06-04 20:56:16,713 - app.utils.session_state - INFO - Session state initialized
2025-06-04 20:56:18,302 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 20:56:18,343 - app.utils.memory_management - INFO - Memory before cleanup: 425.05 MB
2025-06-04 20:56:18,926 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-04 20:56:18,926 - app.utils.memory_management - INFO - Memory after cleanup: 425.61 MB (freed -0.56 MB)
2025-06-04 20:56:22,961 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:22,992 - app.utils.memory_management - INFO - Memory before cleanup: 428.60 MB
2025-06-04 20:56:23,173 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 20:56:23,175 - app.utils.memory_management - INFO - Memory after cleanup: 428.60 MB (freed 0.00 MB)
2025-06-04 20:56:24,220 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:24,283 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-04 20:56:24,285 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:56:24,285 - app - INFO - Data shape: (585, 36)
2025-06-04 20:56:24,286 - app - INFO - File COMI contains 2025 data
2025-06-04 20:56:24,333 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-04 20:56:24,334 - app - INFO - Features shape: (585, 36)
2025-06-04 20:56:24,362 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:56:24,363 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:56:24,365 - app - INFO - Data shape: (585, 36)
2025-06-04 20:56:24,367 - app - INFO - File COMI contains 2025 data
2025-06-04 20:56:24,372 - app.utils.memory_management - INFO - Memory before cleanup: 432.44 MB
2025-06-04 20:56:24,558 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-04 20:56:24,558 - app.utils.memory_management - INFO - Memory after cleanup: 432.48 MB (freed -0.04 MB)
2025-06-04 20:56:24,720 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:24,838 - app.utils.memory_management - INFO - Memory before cleanup: 433.48 MB
2025-06-04 20:56:25,036 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-04 20:56:25,037 - app.utils.memory_management - INFO - Memory after cleanup: 433.46 MB (freed 0.02 MB)
2025-06-04 20:56:26,569 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:26,640 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:56:26,682 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:56:26,685 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:56:26,686 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:56:26,687 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:56:26,764 - app.utils.memory_management - INFO - Memory before cleanup: 435.37 MB
2025-06-04 20:56:27,065 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-04 20:56:27,066 - app.utils.memory_management - INFO - Memory after cleanup: 435.37 MB (freed 0.00 MB)
2025-06-04 20:56:30,280 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:30,621 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:56:30,757 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:56:30,759 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:56:30,760 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:56:30,763 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:56:30,829 - app.utils.memory_management - INFO - Memory before cleanup: 435.61 MB
2025-06-04 20:56:31,059 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-04 20:56:31,061 - app.utils.memory_management - INFO - Memory after cleanup: 435.61 MB (freed 0.00 MB)
2025-06-04 20:56:32,863 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:32,891 - app.utils.session_state - INFO - Initializing session state
2025-06-04 20:56:32,895 - app.utils.session_state - INFO - Session state initialized
2025-06-04 20:56:32,940 - app.utils.memory_management - INFO - Memory before cleanup: 435.60 MB
2025-06-04 20:56:33,172 - app.utils.memory_management - INFO - Garbage collection: collected 302 objects
2025-06-04 20:56:33,173 - app.utils.memory_management - INFO - Memory after cleanup: 435.60 MB (freed -0.00 MB)
2025-06-04 20:56:46,873 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:46,912 - app.utils.memory_management - INFO - Memory before cleanup: 435.85 MB
2025-06-04 20:56:47,124 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-04 20:56:47,126 - app.utils.memory_management - INFO - Memory after cleanup: 435.85 MB (freed 0.00 MB)
2025-06-04 20:56:50,984 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:51,026 - app.utils.memory_management - INFO - Memory before cleanup: 435.85 MB
2025-06-04 20:56:51,305 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 20:56:51,307 - app.utils.memory_management - INFO - Memory after cleanup: 435.85 MB (freed 0.00 MB)
2025-06-04 20:56:52,141 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:52,187 - app.utils.memory_management - INFO - Memory before cleanup: 435.87 MB
2025-06-04 20:56:52,411 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-04 20:56:52,417 - app.utils.memory_management - INFO - Memory after cleanup: 435.87 MB (freed 0.00 MB)
2025-06-04 20:56:52,599 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:52,667 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:56:52,718 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:56:52,721 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:56:52,723 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:56:52,725 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:56:52,803 - app.utils.memory_management - INFO - Memory before cleanup: 436.55 MB
2025-06-04 20:56:53,093 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-04 20:56:53,095 - app.utils.memory_management - INFO - Memory after cleanup: 436.55 MB (freed 0.00 MB)
2025-06-04 20:56:57,207 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:56:57,337 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:56:57,417 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-04 20:56:57,422 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:56:57,424 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:56:57,429 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:56:57,593 - app.utils.memory_management - INFO - Memory before cleanup: 436.64 MB
2025-06-04 20:56:58,133 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-04 20:56:58,151 - app.utils.memory_management - INFO - Memory after cleanup: 436.62 MB (freed 0.02 MB)
2025-06-04 20:57:35,123 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:57:35,131 - app.utils.session_state - INFO - Initializing session state
2025-06-04 20:57:35,135 - app.utils.session_state - INFO - Session state initialized
2025-06-04 20:57:35,162 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 20:57:35,231 - app.utils.memory_management - INFO - Memory before cleanup: 436.58 MB
2025-06-04 20:57:35,572 - app.utils.memory_management - INFO - Garbage collection: collected 310 objects
2025-06-04 20:57:35,573 - app.utils.memory_management - INFO - Memory after cleanup: 436.58 MB (freed 0.00 MB)
2025-06-04 20:57:37,354 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:57:37,380 - app.utils.memory_management - INFO - Memory before cleanup: 436.55 MB
2025-06-04 20:57:37,582 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-04 20:57:37,582 - app.utils.memory_management - INFO - Memory after cleanup: 436.55 MB (freed 0.00 MB)
2025-06-04 20:57:42,488 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:57:42,539 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:57:42,540 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:57:42,540 - app - INFO - Data shape: (585, 36)
2025-06-04 20:57:42,540 - app - INFO - File COMI contains 2025 data
2025-06-04 20:57:42,576 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-04 20:57:42,577 - app - INFO - Features shape: (585, 36)
2025-06-04 20:57:42,604 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:57:42,605 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:57:42,605 - app - INFO - Data shape: (585, 36)
2025-06-04 20:57:42,605 - app - INFO - File COMI contains 2025 data
2025-06-04 20:57:42,608 - app.utils.memory_management - INFO - Memory before cleanup: 436.63 MB
2025-06-04 20:57:42,761 - app.utils.memory_management - INFO - Garbage collection: collected 313 objects
2025-06-04 20:57:42,763 - app.utils.memory_management - INFO - Memory after cleanup: 436.63 MB (freed 0.00 MB)
2025-06-04 20:57:42,968 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:57:43,138 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:57:43,209 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:57:43,216 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:57:43,223 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:57:43,232 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:57:43,364 - app.utils.memory_management - INFO - Memory before cleanup: 436.58 MB
2025-06-04 20:57:43,638 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-04 20:57:43,640 - app.utils.memory_management - INFO - Memory after cleanup: 436.58 MB (freed 0.00 MB)
2025-06-04 20:57:46,363 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:57:46,486 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:57:46,532 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:57:46,537 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:57:46,540 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:57:46,543 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:57:46,621 - app.utils.memory_management - INFO - Memory before cleanup: 436.59 MB
2025-06-04 20:57:46,821 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-04 20:57:46,822 - app.utils.memory_management - INFO - Memory after cleanup: 436.59 MB (freed 0.00 MB)
2025-06-04 20:58:05,761 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:58:05,828 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:58:05,860 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:58:05,861 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:58:05,861 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:58:05,862 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:58:05,927 - app.utils.memory_management - INFO - Memory before cleanup: 436.58 MB
2025-06-04 20:58:06,105 - app.utils.memory_management - INFO - Garbage collection: collected 303 objects
2025-06-04 20:58:06,105 - app.utils.memory_management - INFO - Memory after cleanup: 436.58 MB (freed 0.00 MB)
2025-06-04 20:58:08,933 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:58:09,017 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:58:09,052 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:58:09,053 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:58:09,054 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:58:09,060 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:58:09,137 - app.utils.memory_management - INFO - Memory before cleanup: 436.59 MB
2025-06-04 20:58:09,305 - app.utils.memory_management - INFO - Garbage collection: collected 296 objects
2025-06-04 20:58:09,306 - app.utils.memory_management - INFO - Memory after cleanup: 436.59 MB (freed 0.00 MB)
2025-06-04 20:58:14,748 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:58:14,833 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:58:14,867 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:58:14,868 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:58:14,869 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:58:14,872 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:58:14,962 - app.utils.memory_management - INFO - Memory before cleanup: 436.57 MB
2025-06-04 20:58:15,150 - app.utils.memory_management - INFO - Garbage collection: collected 303 objects
2025-06-04 20:58:15,154 - app.utils.memory_management - INFO - Memory after cleanup: 436.57 MB (freed 0.00 MB)
2025-06-04 20:58:16,657 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:58:16,721 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:58:16,751 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:58:16,752 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:58:16,752 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:58:16,753 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:58:16,818 - app.utils.memory_management - INFO - Memory before cleanup: 436.55 MB
2025-06-04 20:58:16,990 - app.utils.memory_management - INFO - Garbage collection: collected 296 objects
2025-06-04 20:58:16,990 - app.utils.memory_management - INFO - Memory after cleanup: 436.55 MB (freed 0.00 MB)
2025-06-04 20:58:22,754 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:58:22,817 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:58:22,845 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:58:22,846 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:58:22,846 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:58:22,847 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:58:22,898 - app.utils.memory_management - INFO - Memory before cleanup: 436.53 MB
2025-06-04 20:58:23,074 - app.utils.memory_management - INFO - Garbage collection: collected 303 objects
2025-06-04 20:58:23,075 - app.utils.memory_management - INFO - Memory after cleanup: 436.53 MB (freed 0.00 MB)
2025-06-04 20:58:24,699 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:58:24,796 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:58:24,838 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:58:24,842 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:58:24,844 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:58:24,848 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:58:24,910 - app.utils.memory_management - INFO - Memory before cleanup: 436.55 MB
2025-06-04 20:58:25,085 - app.utils.memory_management - INFO - Garbage collection: collected 296 objects
2025-06-04 20:58:25,086 - app.utils.memory_management - INFO - Memory after cleanup: 436.55 MB (freed 0.00 MB)
2025-06-04 20:58:28,697 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:58:28,783 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:58:28,819 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 20:58:28,821 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:58:28,822 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:58:28,824 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:58:28,919 - app.utils.memory_management - INFO - Memory before cleanup: 436.53 MB
2025-06-04 20:58:29,085 - app.utils.memory_management - INFO - Garbage collection: collected 303 objects
2025-06-04 20:58:29,085 - app.utils.memory_management - INFO - Memory after cleanup: 436.53 MB (freed 0.00 MB)
2025-06-04 20:58:30,281 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 20:58:30,353 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 20:58:30,388 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 20:58:30,390 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 20:58:30,390 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 20:58:30,391 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 20:58:30,453 - app.utils.memory_management - INFO - Memory before cleanup: 436.55 MB
2025-06-04 20:58:30,621 - app.utils.memory_management - INFO - Garbage collection: collected 296 objects
2025-06-04 20:58:30,621 - app.utils.memory_management - INFO - Memory after cleanup: 436.55 MB (freed 0.00 MB)
2025-06-04 21:03:41,176 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 21:03:43,067 - app - INFO - Memory management utilities loaded
2025-06-04 21:03:43,069 - app - INFO - Error handling utilities loaded
2025-06-04 21:03:43,072 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 21:03:43,073 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 21:03:43,074 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 21:03:43,075 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 21:03:43,077 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 21:03:43,079 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 21:03:43,081 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 21:03:43,086 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 21:03:43,087 - app - INFO - Applied NumPy fix
2025-06-04 21:03:43,089 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 21:03:43,090 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 21:03:43,091 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 21:03:43,091 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 21:03:43,093 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 21:03:43,096 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 21:03:43,100 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 21:03:43,100 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 21:03:49,333 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 21:03:49,333 - app - INFO - Applied TensorFlow fix
2025-06-04 21:03:49,337 - app.config - INFO - Configuration initialized
2025-06-04 21:03:49,342 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 21:03:49,356 - models.train - INFO - TensorFlow test successful
2025-06-04 21:03:50,129 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 21:03:50,132 - models.train - INFO - Transformer model is available
2025-06-04 21:03:50,135 - models.train - INFO - Using TensorFlow-based models
2025-06-04 21:03:50,141 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 21:03:50,144 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 21:03:50,151 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 21:03:50,564 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 21:03:50,564 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 21:03:50,564 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 21:03:50,564 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 21:03:50,564 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 21:03:50,564 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 21:03:50,571 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 21:03:50,571 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 21:03:50,571 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 21:03:50,571 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 21:03:50,697 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 21:03:50,699 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:03:51,139 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 21:03:52,325 - app.utils.session_state - INFO - Initializing session state
2025-06-04 21:03:52,326 - app.utils.session_state - INFO - Session state initialized
2025-06-04 21:03:53,551 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 21:03:53,861 - app.utils.memory_management - INFO - Memory before cleanup: 424.13 MB
2025-06-04 21:03:54,041 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-04 21:03:54,042 - app.utils.memory_management - INFO - Memory after cleanup: 424.49 MB (freed -0.36 MB)
2025-06-04 21:03:57,218 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:03:57,224 - app.utils.session_state - INFO - Initializing session state
2025-06-04 21:03:57,228 - app.utils.session_state - INFO - Session state initialized
2025-06-04 21:03:57,248 - app.utils.memory_management - INFO - Memory before cleanup: 428.09 MB
2025-06-04 21:03:57,467 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 21:03:57,469 - app.utils.memory_management - INFO - Memory after cleanup: 428.09 MB (freed 0.00 MB)
2025-06-04 21:04:02,480 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:04:02,619 - app.utils.memory_management - INFO - Memory before cleanup: 428.80 MB
2025-06-04 21:04:02,963 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-04 21:04:02,968 - app.utils.memory_management - INFO - Memory after cleanup: 428.84 MB (freed -0.04 MB)
2025-06-04 21:04:03,680 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:04:03,768 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.04 seconds
2025-06-04 21:04:03,770 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:04:03,771 - app - INFO - Data shape: (585, 36)
2025-06-04 21:04:03,772 - app - INFO - File COMI contains 2025 data
2025-06-04 21:04:03,827 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-04 21:04:03,829 - app - INFO - Features shape: (585, 36)
2025-06-04 21:04:03,882 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.04 seconds
2025-06-04 21:04:03,887 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:04:03,887 - app - INFO - Data shape: (585, 36)
2025-06-04 21:04:03,888 - app - INFO - File COMI contains 2025 data
2025-06-04 21:04:03,898 - app.utils.memory_management - INFO - Memory before cleanup: 432.74 MB
2025-06-04 21:04:04,172 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-04 21:04:04,174 - app.utils.memory_management - INFO - Memory after cleanup: 432.74 MB (freed 0.00 MB)
2025-06-04 21:04:04,370 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:04:04,470 - app.utils.memory_management - INFO - Memory before cleanup: 433.66 MB
2025-06-04 21:04:04,734 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-04 21:04:04,735 - app.utils.memory_management - INFO - Memory after cleanup: 433.66 MB (freed 0.00 MB)
2025-06-04 21:04:06,239 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:04:06,334 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 21:04:06,383 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 21:04:06,384 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:04:06,384 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 21:04:06,385 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 21:04:06,478 - app.utils.memory_management - INFO - Memory before cleanup: 435.62 MB
2025-06-04 21:04:06,696 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-04 21:04:06,698 - app.utils.memory_management - INFO - Memory after cleanup: 435.62 MB (freed 0.00 MB)
2025-06-04 21:04:10,311 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:04:14,595 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 21:04:14,650 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 21:04:14,651 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:04:14,652 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 21:04:14,653 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 21:04:14,748 - app.utils.memory_management - INFO - Memory before cleanup: 436.13 MB
2025-06-04 21:04:14,969 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-04 21:04:14,969 - app.utils.memory_management - INFO - Memory after cleanup: 436.13 MB (freed 0.00 MB)
2025-06-04 21:09:54,020 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-04 21:09:56,288 - app - INFO - Memory management utilities loaded
2025-06-04 21:09:56,294 - app - INFO - Error handling utilities loaded
2025-06-04 21:09:56,296 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-04 21:09:56,298 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-04 21:09:56,299 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-04 21:09:56,300 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-04 21:09:56,312 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-04 21:09:56,315 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-04 21:09:56,317 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-04 21:09:56,319 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-04 21:09:56,328 - app - INFO - Applied NumPy fix
2025-06-04 21:09:56,331 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 21:09:56,332 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 21:09:56,334 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 21:09:56,335 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-04 21:09:56,335 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 21:09:56,335 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 21:09:56,337 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 21:09:56,339 - app - INFO - Applied NumPy BitGenerator fix
2025-06-04 21:10:02,308 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-04 21:10:02,308 - app - INFO - Applied TensorFlow fix
2025-06-04 21:10:02,311 - app.config - INFO - Configuration initialized
2025-06-04 21:10:02,317 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-04 21:10:02,327 - models.train - INFO - TensorFlow test successful
2025-06-04 21:10:02,862 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-04 21:10:02,862 - models.train - INFO - Transformer model is available
2025-06-04 21:10:02,862 - models.train - INFO - Using TensorFlow-based models
2025-06-04 21:10:02,865 - models.predict - INFO - Transformer model is available for predictions
2025-06-04 21:10:02,865 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-04 21:10:02,868 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-04 21:10:03,213 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 21:10:03,213 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-04 21:10:03,215 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-04 21:10:03,215 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-04 21:10:03,215 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-04 21:10:03,215 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-04 21:10:03,215 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-04 21:10:03,215 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-04 21:10:03,215 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-04 21:10:03,215 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-04 21:10:03,320 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-04 21:10:03,323 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:10:03,731 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-04 21:10:04,793 - app.utils.session_state - INFO - Initializing session state
2025-06-04 21:10:04,806 - app.utils.session_state - INFO - Session state initialized
2025-06-04 21:10:06,111 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 21:10:06,135 - app.utils.memory_management - INFO - Memory before cleanup: 425.45 MB
2025-06-04 21:10:06,388 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-04 21:10:06,388 - app.utils.memory_management - INFO - Memory after cleanup: 425.83 MB (freed -0.38 MB)
2025-06-04 21:10:09,769 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:10:09,780 - app.utils.session_state - INFO - Initializing session state
2025-06-04 21:10:09,784 - app.utils.session_state - INFO - Session state initialized
2025-06-04 21:10:09,810 - app.utils.memory_management - INFO - Memory before cleanup: 429.39 MB
2025-06-04 21:10:10,057 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-04 21:10:10,059 - app.utils.memory_management - INFO - Memory after cleanup: 429.39 MB (freed 0.00 MB)
2025-06-04 21:10:16,094 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:10:16,162 - app.utils.memory_management - INFO - Memory before cleanup: 430.33 MB
2025-06-04 21:10:16,510 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-04 21:10:16,511 - app.utils.memory_management - INFO - Memory after cleanup: 430.37 MB (freed -0.04 MB)
2025-06-04 21:10:17,254 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:10:17,313 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 21:10:17,316 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:10:17,318 - app - INFO - Data shape: (585, 36)
2025-06-04 21:10:17,320 - app - INFO - File COMI contains 2025 data
2025-06-04 21:10:17,419 - app - INFO - Feature engineering for COMI completed in 0.10 seconds
2025-06-04 21:10:17,425 - app - INFO - Features shape: (585, 36)
2025-06-04 21:10:17,515 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-04 21:10:17,517 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:10:17,519 - app - INFO - Data shape: (585, 36)
2025-06-04 21:10:17,525 - app - INFO - File COMI contains 2025 data
2025-06-04 21:10:17,539 - app.utils.memory_management - INFO - Memory before cleanup: 434.00 MB
2025-06-04 21:10:17,774 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-04 21:10:17,774 - app.utils.memory_management - INFO - Memory after cleanup: 434.00 MB (freed 0.00 MB)
2025-06-04 21:10:18,036 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:10:18,154 - app.utils.memory_management - INFO - Memory before cleanup: 434.90 MB
2025-06-04 21:10:18,450 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-04 21:10:18,452 - app.utils.memory_management - INFO - Memory after cleanup: 434.90 MB (freed 0.00 MB)
2025-06-04 21:10:20,450 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:10:20,523 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 21:10:20,569 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 21:10:20,572 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:10:20,574 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 21:10:20,575 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 21:10:20,650 - app.utils.memory_management - INFO - Memory before cleanup: 436.71 MB
2025-06-04 21:10:20,902 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-04 21:10:20,904 - app.utils.memory_management - INFO - Memory after cleanup: 436.71 MB (freed 0.00 MB)
2025-06-04 21:10:23,201 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:10:27,328 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 21:10:27,378 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-04 21:10:27,380 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:10:27,389 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 21:10:27,391 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 21:10:27,579 - app.utils.memory_management - INFO - Memory before cleanup: 437.31 MB
2025-06-04 21:10:27,793 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-04 21:10:27,793 - app.utils.memory_management - INFO - Memory after cleanup: 437.31 MB (freed 0.00 MB)
2025-06-04 21:14:28,781 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:14:28,787 - app.utils.session_state - INFO - Initializing session state
2025-06-04 21:14:28,789 - app.utils.session_state - INFO - Session state initialized
2025-06-04 21:14:28,804 - app.utils.memory_management - INFO - Memory before cleanup: 437.42 MB
2025-06-04 21:14:28,976 - app.utils.memory_management - INFO - Garbage collection: collected 304 objects
2025-06-04 21:14:28,977 - app.utils.memory_management - INFO - Memory after cleanup: 437.42 MB (freed 0.00 MB)
2025-06-04 21:20:24,732 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:20:24,737 - app.utils.session_state - INFO - Initializing session state
2025-06-04 21:20:24,738 - app.utils.session_state - INFO - Session state initialized
2025-06-04 21:20:24,755 - app - INFO - Found 8 stock files in data/stocks
2025-06-04 21:20:24,768 - app.utils.memory_management - INFO - Memory before cleanup: 437.63 MB
2025-06-04 21:20:24,960 - app.utils.memory_management - INFO - Garbage collection: collected 226 objects
2025-06-04 21:20:24,960 - app.utils.memory_management - INFO - Memory after cleanup: 437.63 MB (freed 0.00 MB)
2025-06-04 21:20:30,786 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:20:30,815 - app.utils.memory_management - INFO - Memory before cleanup: 437.77 MB
2025-06-04 21:20:31,021 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-04 21:20:31,021 - app.utils.memory_management - INFO - Memory after cleanup: 437.77 MB (freed 0.00 MB)
2025-06-04 21:20:32,314 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:20:32,334 - app.utils.memory_management - INFO - Memory before cleanup: 437.81 MB
2025-06-04 21:20:32,517 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-04 21:20:32,518 - app.utils.memory_management - INFO - Memory after cleanup: 437.81 MB (freed 0.00 MB)
2025-06-04 21:20:32,696 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:20:32,746 - app.utils.memory_management - INFO - Memory before cleanup: 437.82 MB
2025-06-04 21:20:32,939 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-04 21:20:32,939 - app.utils.memory_management - INFO - Memory after cleanup: 437.81 MB (freed 0.02 MB)
2025-06-04 21:20:35,131 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:20:35,202 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 21:20:35,255 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-04 21:20:35,258 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:20:35,260 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 21:20:35,262 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 21:20:35,319 - app.utils.memory_management - INFO - Memory before cleanup: 437.81 MB
2025-06-04 21:20:35,487 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-04 21:20:35,488 - app.utils.memory_management - INFO - Memory after cleanup: 437.81 MB (freed 0.00 MB)
2025-06-04 21:20:38,320 - app - INFO - Using TensorFlow-based LSTM model
2025-06-04 21:20:39,440 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-04 21:20:39,485 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-04 21:20:39,487 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-04 21:20:39,488 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-04 21:20:39,489 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-04 21:20:39,568 - app.utils.memory_management - INFO - Memory before cleanup: 437.84 MB
2025-06-04 21:20:39,768 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-04 21:20:39,770 - app.utils.memory_management - INFO - Memory after cleanup: 437.84 MB (freed 0.00 MB)
