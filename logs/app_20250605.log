2025-06-05 09:51:20,215 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-05 09:51:25,179 - app - INFO - Memory management utilities loaded
2025-06-05 09:51:25,181 - app - INFO - Error handling utilities loaded
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-05 09:51:25,188 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-05 09:51:25,188 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-05 09:51:25,190 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-05 09:51:25,190 - app - INFO - Applied NumPy fix
2025-06-05 09:51:25,192 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 09:51:25,192 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 09:51:25,192 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 09:51:25,194 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-05 09:51:25,194 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 09:51:25,195 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 09:51:25,196 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 09:51:25,196 - app - INFO - Applied NumPy BitGenerator fix
2025-06-05 09:51:45,039 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-05 09:51:45,040 - app - INFO - Applied TensorFlow fix
2025-06-05 09:51:45,043 - app.config - INFO - Configuration initialized
2025-06-05 09:51:45,062 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-05 09:51:45,074 - models.train - INFO - TensorFlow test successful
2025-06-05 09:51:49,812 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-05 09:51:49,812 - models.train - INFO - Transformer model is available
2025-06-05 09:51:49,812 - models.train - INFO - Using TensorFlow-based models
2025-06-05 09:51:49,814 - models.predict - INFO - Transformer model is available for predictions
2025-06-05 09:51:49,815 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-05 09:51:49,820 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-05 09:51:50,329 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 09:51:50,331 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 09:51:50,331 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 09:51:50,333 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 09:51:50,334 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 09:51:50,335 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-05 09:51:50,338 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-05 09:51:50,342 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 09:51:50,343 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 09:51:50,344 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 09:51:50,465 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-05 09:51:50,470 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:51:50,885 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-05 09:51:51,863 - app.utils.session_state - INFO - Initializing session state
2025-06-05 09:51:51,866 - app.utils.session_state - INFO - Session state initialized
2025-06-05 09:51:53,407 - app - INFO - Found 8 stock files in data/stocks
2025-06-05 09:51:53,421 - app.utils.memory_management - INFO - Memory before cleanup: 426.66 MB
2025-06-05 09:51:53,635 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-05 09:51:53,642 - app.utils.memory_management - INFO - Memory after cleanup: 426.66 MB (freed -0.00 MB)
2025-06-05 09:52:15,215 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:15,264 - app.utils.memory_management - INFO - Memory before cleanup: 430.45 MB
2025-06-05 09:52:15,584 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 09:52:15,586 - app.utils.memory_management - INFO - Memory after cleanup: 430.45 MB (freed 0.00 MB)
2025-06-05 09:52:17,550 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:17,600 - app.utils.memory_management - INFO - Memory before cleanup: 431.38 MB
2025-06-05 09:52:17,859 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-05 09:52:17,859 - app.utils.memory_management - INFO - Memory after cleanup: 431.42 MB (freed -0.04 MB)
2025-06-05 09:52:21,780 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:21,831 - app.utils.memory_management - INFO - Memory before cleanup: 431.43 MB
2025-06-05 09:52:22,103 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-05 09:52:22,104 - app.utils.memory_management - INFO - Memory after cleanup: 431.43 MB (freed 0.00 MB)
2025-06-05 09:52:23,230 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:42,489 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-05 09:52:42,495 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-05 09:52:42,499 - app.utils.memory_management - INFO - Memory before cleanup: 432.78 MB
2025-06-05 09:52:42,738 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-05 09:52:42,740 - app.utils.memory_management - INFO - Memory after cleanup: 432.78 MB (freed 0.00 MB)
2025-06-05 09:52:42,743 - app.utils.memory_management - INFO - Memory before cleanup: 432.78 MB
2025-06-05 09:52:43,030 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-05 09:52:43,032 - app.utils.memory_management - INFO - Memory after cleanup: 432.78 MB (freed 0.00 MB)
2025-06-05 09:52:59,810 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:59,859 - app.utils.memory_management - INFO - Memory before cleanup: 433.45 MB
2025-06-05 09:53:00,156 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-05 09:53:00,156 - app.utils.memory_management - INFO - Memory after cleanup: 433.45 MB (freed 0.00 MB)
2025-06-05 09:53:06,982 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:53:07,012 - app.utils.memory_management - INFO - Memory before cleanup: 433.45 MB
2025-06-05 09:53:07,223 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-05 09:53:07,223 - app.utils.memory_management - INFO - Memory after cleanup: 433.45 MB (freed 0.00 MB)
2025-06-05 09:53:08,621 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:53:08,675 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 09:53:08,678 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 09:53:08,680 - app - INFO - Data shape: (585, 36)
2025-06-05 09:53:08,685 - app - INFO - File COMI contains 2025 data
2025-06-05 09:53:08,725 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-06-05 09:53:08,729 - app - INFO - Features shape: (585, 36)
2025-06-05 09:53:08,758 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 09:53:08,763 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 09:53:08,765 - app - INFO - Data shape: (585, 36)
2025-06-05 09:53:08,767 - app - INFO - File COMI contains 2025 data
2025-06-05 09:53:08,772 - app.utils.memory_management - INFO - Memory before cleanup: 436.60 MB
2025-06-05 09:53:08,969 - app.utils.memory_management - INFO - Garbage collection: collected 286 objects
2025-06-05 09:53:08,969 - app.utils.memory_management - INFO - Memory after cleanup: 436.60 MB (freed 0.00 MB)
2025-06-05 09:53:09,157 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:53:09,250 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 09:53:09,298 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 09:53:09,300 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 09:53:09,302 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 09:53:09,306 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 09:53:09,435 - app.utils.memory_management - INFO - Memory before cleanup: 436.59 MB
2025-06-05 09:53:09,722 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 09:53:09,725 - app.utils.memory_management - INFO - Memory after cleanup: 436.59 MB (freed 0.00 MB)
2025-06-05 09:53:13,424 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:53:15,530 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 09:53:15,566 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 09:53:15,570 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 09:53:15,570 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 09:53:15,571 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 09:53:15,632 - app.utils.memory_management - INFO - Memory before cleanup: 436.86 MB
2025-06-05 09:53:15,838 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-05 09:53:15,840 - app.utils.memory_management - INFO - Memory after cleanup: 436.86 MB (freed 0.00 MB)
2025-06-05 10:03:42,616 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-05 10:03:42,622 - app - INFO - Memory management utilities loaded
2025-06-05 10:03:42,626 - app - INFO - Error handling utilities loaded
2025-06-05 10:03:42,628 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 10:03:42,630 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 10:03:42,631 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 10:03:42,633 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 10:04:19,661 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-05 10:04:21,424 - app - INFO - Memory management utilities loaded
2025-06-05 10:04:21,426 - app - INFO - Error handling utilities loaded
2025-06-05 10:04:21,428 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 10:04:21,430 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 10:04:21,432 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 10:04:21,432 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 10:04:21,434 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-05 10:04:21,434 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-05 10:04:21,436 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-05 10:04:21,438 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-05 10:04:21,438 - app - INFO - Applied NumPy fix
2025-06-05 10:04:21,440 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 10:04:21,442 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 10:04:21,443 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 10:04:21,445 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-05 10:04:21,447 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 10:04:21,449 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 10:04:21,451 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 10:04:21,453 - app - INFO - Applied NumPy BitGenerator fix
2025-06-05 10:04:25,730 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-05 10:04:25,730 - app - INFO - Applied TensorFlow fix
2025-06-05 10:04:25,730 - app.config - INFO - Configuration initialized
2025-06-05 10:04:25,735 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-05 10:04:25,746 - models.train - INFO - TensorFlow test successful
2025-06-05 10:04:26,224 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-05 10:04:26,224 - models.train - INFO - Transformer model is available
2025-06-05 10:04:26,224 - models.train - INFO - Using TensorFlow-based models
2025-06-05 10:04:26,224 - models.predict - INFO - Transformer model is available for predictions
2025-06-05 10:04:26,224 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-05 10:04:26,227 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-05 10:04:26,543 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 10:04:26,543 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 10:04:26,543 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 10:04:26,543 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 10:04:26,545 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 10:04:26,545 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-05 10:04:26,545 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-05 10:04:26,545 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 10:04:26,545 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 10:04:26,545 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 10:04:26,635 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-05 10:04:26,635 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:04:26,958 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-05 10:04:27,473 - app.utils.session_state - INFO - Initializing session state
2025-06-05 10:04:27,475 - app.utils.session_state - INFO - Session state initialized
2025-06-05 10:04:28,721 - app - INFO - Found 8 stock files in data/stocks
2025-06-05 10:04:28,748 - app.utils.memory_management - INFO - Memory before cleanup: 425.02 MB
2025-06-05 10:04:28,925 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 10:04:28,936 - app.utils.memory_management - INFO - Memory after cleanup: 425.38 MB (freed -0.36 MB)
2025-06-05 10:04:45,202 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:04:45,225 - app.utils.memory_management - INFO - Memory before cleanup: 428.29 MB
2025-06-05 10:04:45,416 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 10:04:45,416 - app.utils.memory_management - INFO - Memory after cleanup: 428.29 MB (freed 0.00 MB)
2025-06-05 10:04:46,426 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:04:46,480 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-05 10:04:46,482 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:04:46,482 - app - INFO - Data shape: (585, 36)
2025-06-05 10:04:46,483 - app - INFO - File COMI contains 2025 data
2025-06-05 10:04:46,518 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-06-05 10:04:46,519 - app - INFO - Features shape: (585, 36)
2025-06-05 10:04:46,547 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:04:46,548 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:04:46,549 - app - INFO - Data shape: (585, 36)
2025-06-05 10:04:46,550 - app - INFO - File COMI contains 2025 data
2025-06-05 10:04:46,557 - app.utils.memory_management - INFO - Memory before cleanup: 432.26 MB
2025-06-05 10:04:46,726 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-05 10:04:46,726 - app.utils.memory_management - INFO - Memory after cleanup: 432.30 MB (freed -0.04 MB)
2025-06-05 10:04:46,890 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:04:46,957 - app.utils.memory_management - INFO - Memory before cleanup: 433.24 MB
2025-06-05 10:04:47,139 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 10:04:47,140 - app.utils.memory_management - INFO - Memory after cleanup: 433.20 MB (freed 0.04 MB)
2025-06-05 10:04:48,534 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:04:48,589 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:04:48,630 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:04:48,632 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:04:48,633 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:04:48,633 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:04:48,684 - app.utils.memory_management - INFO - Memory before cleanup: 435.43 MB
2025-06-05 10:04:48,864 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 10:04:48,865 - app.utils.memory_management - INFO - Memory after cleanup: 435.43 MB (freed 0.00 MB)
2025-06-05 10:04:59,342 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:04:59,408 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:04:59,443 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:04:59,445 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:04:59,446 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:04:59,447 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:04:59,498 - app.utils.memory_management - INFO - Memory before cleanup: 435.52 MB
2025-06-05 10:04:59,694 - app.utils.memory_management - INFO - Garbage collection: collected 296 objects
2025-06-05 10:04:59,695 - app.utils.memory_management - INFO - Memory after cleanup: 435.52 MB (freed 0.00 MB)
2025-06-05 10:08:14,386 - app - INFO - Cleaning up resources...
2025-06-05 10:08:14,387 - app.utils.memory_management - INFO - Memory before cleanup: 430.46 MB
2025-06-05 10:08:14,589 - app.utils.memory_management - INFO - Garbage collection: collected 372 objects
2025-06-05 10:08:14,591 - app.utils.memory_management - INFO - Memory after cleanup: 430.46 MB (freed 0.00 MB)
2025-06-05 10:08:14,591 - app - INFO - Application shutdown complete
2025-06-05 10:08:41,307 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-05 10:08:42,746 - app - INFO - Memory management utilities loaded
2025-06-05 10:08:42,748 - app - INFO - Error handling utilities loaded
2025-06-05 10:08:42,748 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 10:08:42,750 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 10:08:42,750 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 10:08:42,750 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 10:08:42,750 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-05 10:08:42,752 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-05 10:08:42,752 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-05 10:08:42,752 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-05 10:08:42,754 - app - INFO - Applied NumPy fix
2025-06-05 10:08:42,755 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 10:08:42,755 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 10:08:42,756 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 10:08:42,757 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-05 10:08:42,758 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 10:08:42,758 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 10:08:42,759 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 10:08:42,759 - app - INFO - Applied NumPy BitGenerator fix
2025-06-05 10:08:46,289 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-05 10:08:46,290 - app - INFO - Applied TensorFlow fix
2025-06-05 10:08:46,292 - app.config - INFO - Configuration initialized
2025-06-05 10:08:46,295 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-05 10:08:46,310 - models.train - INFO - TensorFlow test successful
2025-06-05 10:08:46,786 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-05 10:08:46,786 - models.train - INFO - Transformer model is available
2025-06-05 10:08:46,787 - models.train - INFO - Using TensorFlow-based models
2025-06-05 10:08:46,788 - models.predict - INFO - Transformer model is available for predictions
2025-06-05 10:08:46,788 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-05 10:08:46,790 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-05 10:08:47,104 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 10:08:47,105 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 10:08:47,106 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 10:08:47,106 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 10:08:47,106 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 10:08:47,106 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-05 10:08:47,106 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-05 10:08:47,106 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 10:08:47,107 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 10:08:47,107 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 10:08:47,269 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-05 10:08:47,275 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:08:47,732 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-05 10:08:48,253 - app.utils.session_state - INFO - Initializing session state
2025-06-05 10:08:48,254 - app.utils.session_state - INFO - Session state initialized
2025-06-05 10:08:49,498 - app - INFO - Found 8 stock files in data/stocks
2025-06-05 10:08:49,512 - app.utils.memory_management - INFO - Memory before cleanup: 425.10 MB
2025-06-05 10:08:49,793 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-05 10:08:49,794 - app.utils.memory_management - INFO - Memory after cleanup: 425.11 MB (freed -0.01 MB)
2025-06-05 10:08:55,500 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:08:55,531 - app.utils.memory_management - INFO - Memory before cleanup: 428.36 MB
2025-06-05 10:08:55,707 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 10:08:55,710 - app.utils.memory_management - INFO - Memory after cleanup: 428.36 MB (freed 0.00 MB)
2025-06-05 10:08:57,221 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:08:57,291 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-05 10:08:57,297 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:08:57,298 - app - INFO - Data shape: (585, 36)
2025-06-05 10:08:57,303 - app - INFO - File COMI contains 2025 data
2025-06-05 10:08:57,345 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-05 10:08:57,347 - app - INFO - Features shape: (585, 36)
2025-06-05 10:08:57,372 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:08:57,374 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:08:57,380 - app - INFO - Data shape: (585, 36)
2025-06-05 10:08:57,383 - app - INFO - File COMI contains 2025 data
2025-06-05 10:08:57,392 - app.utils.memory_management - INFO - Memory before cleanup: 432.39 MB
2025-06-05 10:08:57,629 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-05 10:08:57,630 - app.utils.memory_management - INFO - Memory after cleanup: 432.43 MB (freed -0.04 MB)
2025-06-05 10:08:57,793 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:08:57,854 - app.utils.memory_management - INFO - Memory before cleanup: 433.36 MB
2025-06-05 10:08:58,047 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 10:08:58,047 - app.utils.memory_management - INFO - Memory after cleanup: 433.34 MB (freed 0.02 MB)
2025-06-05 10:08:59,543 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:08:59,620 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:08:59,675 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:08:59,683 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:08:59,689 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:08:59,693 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:08:59,792 - app.utils.memory_management - INFO - Memory before cleanup: 435.64 MB
2025-06-05 10:08:59,991 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 10:08:59,992 - app.utils.memory_management - INFO - Memory after cleanup: 435.64 MB (freed 0.00 MB)
2025-06-05 10:09:04,210 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:09:04,269 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:09:04,271 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:09:04,271 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:09:04,274 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:09:04,316 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:09:04,364 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-05 10:09:04,368 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:09:04,372 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:09:04,378 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:09:04,453 - app.utils.memory_management - INFO - Memory before cleanup: 435.68 MB
2025-06-05 10:09:04,623 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-05 10:09:04,624 - app.utils.memory_management - INFO - Memory after cleanup: 435.68 MB (freed 0.00 MB)
2025-06-05 10:09:25,408 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:09:25,414 - app.utils.session_state - INFO - Initializing session state
2025-06-05 10:09:25,415 - app.utils.session_state - INFO - Session state initialized
2025-06-05 10:09:25,435 - app.utils.memory_management - INFO - Memory before cleanup: 435.65 MB
2025-06-05 10:09:25,613 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-05 10:09:25,614 - app.utils.memory_management - INFO - Memory after cleanup: 435.65 MB (freed 0.00 MB)
2025-06-05 10:09:34,756 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:09:34,787 - app.utils.memory_management - INFO - Memory before cleanup: 435.90 MB
2025-06-05 10:09:34,960 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-05 10:09:34,961 - app.utils.memory_management - INFO - Memory after cleanup: 435.90 MB (freed 0.00 MB)
2025-06-05 10:09:36,256 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:09:36,279 - app.utils.memory_management - INFO - Memory before cleanup: 435.92 MB
2025-06-05 10:09:36,499 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-05 10:09:36,501 - app.utils.memory_management - INFO - Memory after cleanup: 435.92 MB (freed 0.00 MB)
2025-06-05 10:09:36,661 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:09:36,746 - app.utils.memory_management - INFO - Memory before cleanup: 435.93 MB
2025-06-05 10:09:36,955 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 10:09:36,955 - app.utils.memory_management - INFO - Memory after cleanup: 435.91 MB (freed 0.02 MB)
2025-06-05 10:09:37,768 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:09:37,889 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:09:37,919 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:09:37,920 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:09:37,921 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:09:37,922 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:09:37,982 - app.utils.memory_management - INFO - Memory before cleanup: 435.86 MB
2025-06-05 10:09:38,180 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 10:09:38,182 - app.utils.memory_management - INFO - Memory after cleanup: 435.86 MB (freed 0.00 MB)
2025-06-05 10:09:40,815 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:09:40,889 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:09:40,894 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:09:40,897 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:09:40,911 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:09:40,981 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:09:41,068 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:09:41,074 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:09:41,075 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:09:41,077 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:09:41,166 - app.utils.memory_management - INFO - Memory before cleanup: 435.90 MB
2025-06-05 10:09:41,442 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-05 10:09:41,444 - app.utils.memory_management - INFO - Memory after cleanup: 435.88 MB (freed 0.02 MB)
2025-06-05 10:28:50,387 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-05 10:28:51,807 - app - INFO - Memory management utilities loaded
2025-06-05 10:28:51,809 - app - INFO - Error handling utilities loaded
2025-06-05 10:28:51,811 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 10:28:51,812 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 10:28:51,814 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 10:28:51,816 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 10:28:51,819 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-05 10:28:51,821 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-05 10:28:51,822 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-05 10:28:51,824 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-05 10:28:51,826 - app - INFO - Applied NumPy fix
2025-06-05 10:28:51,832 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 10:28:51,833 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 10:28:51,834 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 10:28:51,839 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-05 10:28:51,841 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 10:28:51,841 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 10:28:51,842 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 10:28:51,844 - app - INFO - Applied NumPy BitGenerator fix
2025-06-05 10:28:55,737 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-05 10:28:55,737 - app - INFO - Applied TensorFlow fix
2025-06-05 10:28:55,753 - app.config - INFO - Configuration initialized
2025-06-05 10:28:55,756 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-05 10:28:55,766 - models.train - INFO - TensorFlow test successful
2025-06-05 10:28:56,272 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-05 10:28:56,273 - models.train - INFO - Transformer model is available
2025-06-05 10:28:56,273 - models.train - INFO - Using TensorFlow-based models
2025-06-05 10:28:56,274 - models.predict - INFO - Transformer model is available for predictions
2025-06-05 10:28:56,275 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-05 10:28:56,277 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-05 10:28:56,622 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 10:28:56,623 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 10:28:56,623 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 10:28:56,623 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 10:28:56,624 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 10:28:56,624 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-05 10:28:56,624 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-05 10:28:56,624 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 10:28:56,624 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 10:28:56,625 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 10:28:56,706 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-05 10:28:56,708 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:28:57,048 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-05 10:28:57,708 - app.utils.session_state - INFO - Initializing session state
2025-06-05 10:28:57,712 - app.utils.session_state - INFO - Session state initialized
2025-06-05 10:28:59,213 - app - INFO - Found 8 stock files in data/stocks
2025-06-05 10:28:59,235 - app.utils.memory_management - INFO - Memory before cleanup: 424.79 MB
2025-06-05 10:28:59,477 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-05 10:28:59,478 - app.utils.memory_management - INFO - Memory after cleanup: 425.17 MB (freed -0.38 MB)
2025-06-05 10:29:04,948 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:04,981 - app.utils.memory_management - INFO - Memory before cleanup: 428.75 MB
2025-06-05 10:29:05,161 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 10:29:05,163 - app.utils.memory_management - INFO - Memory after cleanup: 428.75 MB (freed 0.00 MB)
2025-06-05 10:29:06,652 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:06,701 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-05 10:29:06,702 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:29:06,703 - app - INFO - Data shape: (585, 36)
2025-06-05 10:29:06,703 - app - INFO - File COMI contains 2025 data
2025-06-05 10:29:06,749 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-05 10:29:06,749 - app - INFO - Features shape: (585, 36)
2025-06-05 10:29:06,772 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:29:06,775 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:29:06,779 - app - INFO - Data shape: (585, 36)
2025-06-05 10:29:06,780 - app - INFO - File COMI contains 2025 data
2025-06-05 10:29:06,789 - app.utils.memory_management - INFO - Memory before cleanup: 432.73 MB
2025-06-05 10:29:06,985 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-05 10:29:06,985 - app.utils.memory_management - INFO - Memory after cleanup: 432.77 MB (freed -0.04 MB)
2025-06-05 10:29:07,189 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:07,259 - app.utils.memory_management - INFO - Memory before cleanup: 433.81 MB
2025-06-05 10:29:07,452 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 10:29:07,453 - app.utils.memory_management - INFO - Memory after cleanup: 433.79 MB (freed 0.02 MB)
2025-06-05 10:29:08,664 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:08,717 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:29:08,749 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:29:08,750 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:29:08,750 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:29:08,751 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:29:08,812 - app.utils.memory_management - INFO - Memory before cleanup: 435.27 MB
2025-06-05 10:29:09,022 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 10:29:09,022 - app.utils.memory_management - INFO - Memory after cleanup: 435.27 MB (freed 0.00 MB)
2025-06-05 10:29:12,610 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:12,796 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:29:12,800 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:29:12,827 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:29:12,829 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:29:12,905 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:29:12,943 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:29:12,946 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:29:12,947 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:29:12,948 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:29:13,019 - app.utils.memory_management - INFO - Memory before cleanup: 435.54 MB
2025-06-05 10:29:13,292 - app.utils.memory_management - INFO - Garbage collection: collected 296 objects
2025-06-05 10:29:13,292 - app.utils.memory_management - INFO - Memory after cleanup: 435.54 MB (freed 0.00 MB)
2025-06-05 10:29:18,944 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:18,965 - app.utils.session_state - INFO - Initializing session state
2025-06-05 10:29:18,984 - app.utils.session_state - INFO - Session state initialized
2025-06-05 10:29:19,049 - app.utils.memory_management - INFO - Memory before cleanup: 435.52 MB
2025-06-05 10:29:19,294 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-05 10:29:19,296 - app.utils.memory_management - INFO - Memory after cleanup: 435.52 MB (freed 0.00 MB)
2025-06-05 10:29:27,817 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:27,876 - app.utils.memory_management - INFO - Memory before cleanup: 435.86 MB
2025-06-05 10:29:28,114 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-05 10:29:28,116 - app.utils.memory_management - INFO - Memory after cleanup: 435.86 MB (freed 0.00 MB)
2025-06-05 10:29:29,108 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:29,129 - app.utils.memory_management - INFO - Memory before cleanup: 435.89 MB
2025-06-05 10:29:29,313 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-05 10:29:29,314 - app.utils.memory_management - INFO - Memory after cleanup: 435.89 MB (freed 0.00 MB)
2025-06-05 10:29:29,500 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:29,555 - app.utils.memory_management - INFO - Memory before cleanup: 435.95 MB
2025-06-05 10:29:29,815 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 10:29:29,818 - app.utils.memory_management - INFO - Memory after cleanup: 435.93 MB (freed 0.02 MB)
2025-06-05 10:29:32,116 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:32,191 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:29:32,236 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:29:32,239 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:29:32,241 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:29:32,244 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:29:32,326 - app.utils.memory_management - INFO - Memory before cleanup: 436.75 MB
2025-06-05 10:29:32,568 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 10:29:32,571 - app.utils.memory_management - INFO - Memory after cleanup: 436.75 MB (freed 0.00 MB)
2025-06-05 10:29:34,741 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:29:34,831 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:29:34,834 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:29:34,836 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:29:34,841 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:29:34,995 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:29:35,075 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:29:35,077 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:29:35,078 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:29:35,081 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:29:35,262 - app.utils.memory_management - INFO - Memory before cleanup: 436.80 MB
2025-06-05 10:29:35,534 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-05 10:29:35,540 - app.utils.memory_management - INFO - Memory after cleanup: 436.78 MB (freed 0.02 MB)
2025-06-05 10:31:54,936 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:31:55,074 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:31:55,115 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:31:55,116 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:31:55,117 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:31:55,122 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:31:55,268 - app.utils.memory_management - INFO - Memory before cleanup: 436.67 MB
2025-06-05 10:31:55,471 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-05 10:31:55,474 - app.utils.memory_management - INFO - Memory after cleanup: 436.67 MB (freed 0.00 MB)
2025-06-05 10:31:58,148 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:31:58,203 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:31:58,204 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:31:58,205 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:31:58,205 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:31:58,254 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:31:58,286 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:31:58,290 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:31:58,290 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:31:58,292 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:31:58,347 - app.utils.memory_management - INFO - Memory before cleanup: 436.70 MB
2025-06-05 10:31:58,518 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-05 10:31:58,519 - app.utils.memory_management - INFO - Memory after cleanup: 436.70 MB (freed 0.00 MB)
2025-06-05 10:47:47,148 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:47:47,181 - app - INFO - Found 8 stock files in data/stocks
2025-06-05 10:47:47,245 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:47:47,298 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:47:47,301 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:47:47,304 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:47:47,308 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:47:47,389 - app.utils.memory_management - INFO - Memory before cleanup: 436.84 MB
2025-06-05 10:47:47,624 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-05 10:47:47,625 - app.utils.memory_management - INFO - Memory after cleanup: 436.84 MB (freed 0.00 MB)
2025-06-05 10:48:04,655 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:48:04,728 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:48:04,732 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:48:04,732 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:48:04,733 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:48:04,793 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:48:04,862 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:48:04,864 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:48:04,865 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:48:04,868 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:48:04,939 - app.utils.memory_management - INFO - Memory before cleanup: 436.82 MB
2025-06-05 10:48:05,156 - app.utils.memory_management - INFO - Garbage collection: collected 301 objects
2025-06-05 10:48:05,157 - app.utils.memory_management - INFO - Memory after cleanup: 436.82 MB (freed 0.00 MB)
2025-06-05 10:57:15,446 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-05 10:57:17,137 - app - INFO - Memory management utilities loaded
2025-06-05 10:57:17,144 - app - INFO - Error handling utilities loaded
2025-06-05 10:57:17,146 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 10:57:17,148 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 10:57:17,149 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 10:57:17,150 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 10:57:17,151 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-05 10:57:17,152 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-05 10:57:17,152 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-05 10:57:17,153 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-05 10:57:17,153 - app - INFO - Applied NumPy fix
2025-06-05 10:57:17,154 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 10:57:17,155 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 10:57:17,155 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 10:57:17,155 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-05 10:57:17,155 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 10:57:17,155 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 10:57:17,156 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 10:57:17,156 - app - INFO - Applied NumPy BitGenerator fix
2025-06-05 10:57:21,678 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-05 10:57:21,678 - app - INFO - Applied TensorFlow fix
2025-06-05 10:57:21,682 - app.config - INFO - Configuration initialized
2025-06-05 10:57:21,687 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-05 10:57:21,700 - models.train - INFO - TensorFlow test successful
2025-06-05 10:57:22,290 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-05 10:57:22,290 - models.train - INFO - Transformer model is available
2025-06-05 10:57:22,290 - models.train - INFO - Using TensorFlow-based models
2025-06-05 10:57:22,292 - models.predict - INFO - Transformer model is available for predictions
2025-06-05 10:57:22,292 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-05 10:57:22,295 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-05 10:57:22,701 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 10:57:22,701 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 10:57:22,701 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 10:57:22,702 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 10:57:22,702 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 10:57:22,702 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-05 10:57:22,702 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-05 10:57:22,702 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 10:57:22,703 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 10:57:22,703 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 10:57:22,799 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-05 10:57:22,802 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:57:23,222 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-05 10:57:24,022 - app.utils.session_state - INFO - Initializing session state
2025-06-05 10:57:24,037 - app.utils.session_state - INFO - Session state initialized
2025-06-05 10:57:25,497 - app - INFO - Found 8 stock files in data/stocks
2025-06-05 10:57:25,528 - app.utils.memory_management - INFO - Memory before cleanup: 425.04 MB
2025-06-05 10:57:25,739 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 10:57:25,739 - app.utils.memory_management - INFO - Memory after cleanup: 425.41 MB (freed -0.36 MB)
2025-06-05 10:57:30,752 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:57:30,785 - app.utils.memory_management - INFO - Memory before cleanup: 429.10 MB
2025-06-05 10:57:30,967 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 10:57:30,968 - app.utils.memory_management - INFO - Memory after cleanup: 429.10 MB (freed 0.00 MB)
2025-06-05 10:57:32,082 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:57:32,137 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-05 10:57:32,138 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:57:32,139 - app - INFO - Data shape: (585, 36)
2025-06-05 10:57:32,142 - app - INFO - File COMI contains 2025 data
2025-06-05 10:57:32,186 - app - INFO - Feature engineering for COMI completed in 0.04 seconds
2025-06-05 10:57:32,189 - app - INFO - Features shape: (585, 36)
2025-06-05 10:57:32,243 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-05 10:57:32,248 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:57:32,250 - app - INFO - Data shape: (585, 36)
2025-06-05 10:57:32,252 - app - INFO - File COMI contains 2025 data
2025-06-05 10:57:32,267 - app.utils.memory_management - INFO - Memory before cleanup: 433.20 MB
2025-06-05 10:57:32,469 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-05 10:57:32,469 - app.utils.memory_management - INFO - Memory after cleanup: 433.24 MB (freed -0.04 MB)
2025-06-05 10:57:32,617 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:57:32,686 - app.utils.memory_management - INFO - Memory before cleanup: 434.20 MB
2025-06-05 10:57:32,873 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 10:57:32,875 - app.utils.memory_management - INFO - Memory after cleanup: 434.18 MB (freed 0.02 MB)
2025-06-05 10:57:34,102 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:57:34,161 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:57:34,212 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:57:34,223 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:57:34,227 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:57:34,230 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:57:34,320 - app.utils.memory_management - INFO - Memory before cleanup: 436.09 MB
2025-06-05 10:57:34,544 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 10:57:34,544 - app.utils.memory_management - INFO - Memory after cleanup: 436.09 MB (freed 0.00 MB)
2025-06-05 10:57:37,191 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:57:37,259 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:57:37,260 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:57:37,263 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:57:37,264 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:57:37,319 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:57:37,368 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:57:37,370 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:57:37,372 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:57:37,373 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:57:37,437 - app.utils.memory_management - INFO - Memory before cleanup: 436.13 MB
2025-06-05 10:57:37,669 - app.utils.memory_management - INFO - Garbage collection: collected 296 objects
2025-06-05 10:57:37,670 - app.utils.memory_management - INFO - Memory after cleanup: 436.13 MB (freed 0.00 MB)
2025-06-05 10:58:30,799 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:58:30,804 - app.utils.session_state - INFO - Initializing session state
2025-06-05 10:58:30,807 - app.utils.session_state - INFO - Session state initialized
2025-06-05 10:58:30,831 - app.utils.memory_management - INFO - Memory before cleanup: 436.05 MB
2025-06-05 10:58:31,173 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-05 10:58:31,174 - app.utils.memory_management - INFO - Memory after cleanup: 436.05 MB (freed 0.00 MB)
2025-06-05 10:58:35,904 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:58:36,000 - app.utils.memory_management - INFO - Memory before cleanup: 436.32 MB
2025-06-05 10:58:36,220 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-05 10:58:36,224 - app.utils.memory_management - INFO - Memory after cleanup: 436.32 MB (freed 0.00 MB)
2025-06-05 10:58:37,233 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:58:37,297 - app.utils.memory_management - INFO - Memory before cleanup: 436.34 MB
2025-06-05 10:58:37,505 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-05 10:58:37,505 - app.utils.memory_management - INFO - Memory after cleanup: 436.34 MB (freed 0.00 MB)
2025-06-05 10:58:37,659 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:58:37,718 - app.utils.memory_management - INFO - Memory before cleanup: 436.35 MB
2025-06-05 10:58:37,908 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 10:58:37,908 - app.utils.memory_management - INFO - Memory after cleanup: 436.33 MB (freed 0.02 MB)
2025-06-05 10:58:39,350 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:58:39,467 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:58:39,501 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 10:58:39,503 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:58:39,504 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:58:39,504 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:58:39,598 - app.utils.memory_management - INFO - Memory before cleanup: 436.37 MB
2025-06-05 10:58:39,816 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 10:58:39,818 - app.utils.memory_management - INFO - Memory after cleanup: 436.37 MB (freed 0.00 MB)
2025-06-05 10:58:42,371 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 10:58:42,441 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:58:42,444 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:58:42,445 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:58:42,446 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:58:42,496 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 10:58:42,555 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 10:58:42,556 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 10:58:42,560 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 10:58:42,562 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 10:58:42,639 - app.utils.memory_management - INFO - Memory before cleanup: 436.40 MB
2025-06-05 10:58:42,850 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-05 10:58:42,852 - app.utils.memory_management - INFO - Memory after cleanup: 436.40 MB (freed 0.00 MB)
2025-06-05 11:02:12,876 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-05 11:02:12,882 - app - INFO - Memory management utilities loaded
2025-06-05 11:02:12,884 - app - INFO - Error handling utilities loaded
2025-06-05 11:02:12,885 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 11:02:12,886 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 11:02:12,886 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 11:02:12,887 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 11:03:17,660 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-05 11:03:19,188 - app - INFO - Memory management utilities loaded
2025-06-05 11:03:19,193 - app - INFO - Error handling utilities loaded
2025-06-05 11:03:19,195 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 11:03:19,196 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 11:03:19,197 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 11:03:19,199 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 11:03:19,202 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-05 11:03:19,204 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-05 11:03:19,205 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-05 11:03:19,205 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-05 11:03:19,211 - app - INFO - Applied NumPy fix
2025-06-05 11:03:19,213 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 11:03:19,213 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 11:03:19,214 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 11:03:19,214 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-05 11:03:19,215 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 11:03:19,216 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 11:03:19,216 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 11:03:19,216 - app - INFO - Applied NumPy BitGenerator fix
2025-06-05 11:03:23,249 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-05 11:03:23,250 - app - INFO - Applied TensorFlow fix
2025-06-05 11:03:23,252 - app.config - INFO - Configuration initialized
2025-06-05 11:03:23,256 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-05 11:03:23,275 - models.train - INFO - TensorFlow test successful
2025-06-05 11:03:23,730 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-05 11:03:23,730 - models.train - INFO - Transformer model is available
2025-06-05 11:03:23,730 - models.train - INFO - Using TensorFlow-based models
2025-06-05 11:03:23,732 - models.predict - INFO - Transformer model is available for predictions
2025-06-05 11:03:23,732 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-05 11:03:23,735 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-05 11:03:24,045 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 11:03:24,045 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 11:03:24,045 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 11:03:24,046 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 11:03:24,046 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 11:03:24,046 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-05 11:03:24,047 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-05 11:03:24,047 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 11:03:24,047 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 11:03:24,047 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 11:03:24,140 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-05 11:03:24,144 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:24,508 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-05 11:03:25,027 - app.utils.session_state - INFO - Initializing session state
2025-06-05 11:03:25,028 - app.utils.session_state - INFO - Session state initialized
2025-06-05 11:03:26,275 - app - INFO - Found 8 stock files in data/stocks
2025-06-05 11:03:26,294 - app.utils.memory_management - INFO - Memory before cleanup: 425.14 MB
2025-06-05 11:03:26,518 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-05 11:03:26,518 - app.utils.memory_management - INFO - Memory after cleanup: 425.50 MB (freed -0.37 MB)
2025-06-05 11:03:30,658 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:30,691 - app.utils.memory_management - INFO - Memory before cleanup: 429.15 MB
2025-06-05 11:03:30,896 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 11:03:30,896 - app.utils.memory_management - INFO - Memory after cleanup: 429.15 MB (freed 0.00 MB)
2025-06-05 11:03:32,107 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:32,191 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.04 seconds
2025-06-05 11:03:32,193 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 11:03:32,194 - app - INFO - Data shape: (585, 36)
2025-06-05 11:03:32,195 - app - INFO - File COMI contains 2025 data
2025-06-05 11:03:32,251 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-05 11:03:32,258 - app - INFO - Features shape: (585, 36)
2025-06-05 11:03:32,312 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.04 seconds
2025-06-05 11:03:32,315 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 11:03:32,318 - app - INFO - Data shape: (585, 36)
2025-06-05 11:03:32,320 - app - INFO - File COMI contains 2025 data
2025-06-05 11:03:32,335 - app.utils.memory_management - INFO - Memory before cleanup: 432.62 MB
2025-06-05 11:03:32,633 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-05 11:03:32,635 - app.utils.memory_management - INFO - Memory after cleanup: 432.66 MB (freed -0.04 MB)
2025-06-05 11:03:33,016 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:33,225 - app.utils.memory_management - INFO - Memory before cleanup: 433.57 MB
2025-06-05 11:03:33,529 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 11:03:33,531 - app.utils.memory_management - INFO - Memory after cleanup: 433.57 MB (freed 0.00 MB)
2025-06-05 11:03:34,757 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:34,836 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 11:03:34,900 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-05 11:03:34,905 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 11:03:34,910 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 11:03:34,914 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 11:03:34,984 - app.utils.memory_management - INFO - Memory before cleanup: 435.61 MB
2025-06-05 11:03:35,187 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 11:03:35,188 - app.utils.memory_management - INFO - Memory after cleanup: 435.61 MB (freed 0.00 MB)
2025-06-05 11:03:37,170 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:37,239 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 11:03:37,240 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 11:03:37,240 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 11:03:37,241 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 11:03:37,283 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 11:03:37,327 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 11:03:37,331 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 11:03:37,331 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 11:03:37,332 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 11:03:37,400 - app.utils.memory_management - INFO - Memory before cleanup: 435.70 MB
2025-06-05 11:03:37,619 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-05 11:03:37,620 - app.utils.memory_management - INFO - Memory after cleanup: 435.70 MB (freed 0.00 MB)
2025-06-05 11:03:50,875 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:50,880 - app.utils.session_state - INFO - Initializing session state
2025-06-05 11:03:50,883 - app.utils.session_state - INFO - Session state initialized
2025-06-05 11:03:50,921 - app.utils.memory_management - INFO - Memory before cleanup: 435.68 MB
2025-06-05 11:03:51,179 - app.utils.memory_management - INFO - Garbage collection: collected 307 objects
2025-06-05 11:03:51,182 - app.utils.memory_management - INFO - Memory after cleanup: 435.68 MB (freed 0.00 MB)
2025-06-05 11:03:57,322 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:57,352 - app.utils.memory_management - INFO - Memory before cleanup: 436.04 MB
2025-06-05 11:03:57,551 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-05 11:03:57,551 - app.utils.memory_management - INFO - Memory after cleanup: 436.04 MB (freed 0.00 MB)
2025-06-05 11:03:58,404 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:58,426 - app.utils.memory_management - INFO - Memory before cleanup: 436.06 MB
2025-06-05 11:03:58,626 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-05 11:03:58,629 - app.utils.memory_management - INFO - Memory after cleanup: 436.06 MB (freed 0.00 MB)
2025-06-05 11:03:58,801 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:03:58,851 - app.utils.memory_management - INFO - Memory before cleanup: 436.07 MB
2025-06-05 11:03:59,053 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 11:03:59,053 - app.utils.memory_management - INFO - Memory after cleanup: 436.05 MB (freed 0.02 MB)
2025-06-05 11:04:01,278 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:04:01,381 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 11:04:01,422 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 11:04:01,424 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 11:04:01,425 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 11:04:01,425 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 11:04:01,485 - app.utils.memory_management - INFO - Memory before cleanup: 436.06 MB
2025-06-05 11:04:01,785 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-05 11:04:01,788 - app.utils.memory_management - INFO - Memory after cleanup: 436.06 MB (freed 0.00 MB)
2025-06-05 11:04:04,003 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 11:04:04,100 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-05 11:04:04,102 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 11:04:04,105 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 11:04:04,113 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 11:04:04,172 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 11:04:04,214 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 11:04:04,216 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 11:04:04,221 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 11:04:04,223 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 11:04:04,301 - app.utils.memory_management - INFO - Memory before cleanup: 436.73 MB
2025-06-05 11:04:04,524 - app.utils.memory_management - INFO - Garbage collection: collected 295 objects
2025-06-05 11:04:04,525 - app.utils.memory_management - INFO - Memory after cleanup: 436.73 MB (freed 0.00 MB)
2025-06-05 12:08:50,990 - app - INFO - Cleaning up resources...
2025-06-05 12:08:50,990 - app.utils.memory_management - INFO - Memory before cleanup: 432.65 MB
2025-06-05 12:08:51,140 - app.utils.memory_management - INFO - Garbage collection: collected 1148 objects
2025-06-05 12:08:51,140 - app.utils.memory_management - INFO - Memory after cleanup: 432.65 MB (freed 0.00 MB)
2025-06-05 12:08:51,140 - app - INFO - Application shutdown complete
