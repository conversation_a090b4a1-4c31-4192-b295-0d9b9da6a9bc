2025-05-16 23:09:27,986 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:09:30,679 - app - INFO - Memory management utilities loaded
2025-05-16 23:09:30,679 - app - INFO - Error handling utilities loaded
2025-05-16 23:09:30,679 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-16 23:09:30,679 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-16 23:09:30,679 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-16 23:09:30,679 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-16 23:09:30,688 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-16 23:09:30,688 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-16 23:09:30,688 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-16 23:09:30,690 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:09:30,698 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-16 23:09:30,698 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-16 23:09:30,698 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-16 23:09:30,700 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-16 23:09:30,700 - app - INFO - Applied NumPy fix
2025-05-16 23:09:30,701 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:09:30,702 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:09:30,702 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:09:30,702 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-16 23:09:30,702 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:09:30,703 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:09:30,703 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:09:30,703 - app - INFO - Applied NumPy BitGenerator fix
2025-05-16 23:09:30,705 - app.config - INFO - Configuration initialized
2025-05-16 23:09:41,782 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-16 23:09:42,174 - models.train - INFO - TensorFlow test successful
2025-05-16 23:09:44,053 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-16 23:09:44,053 - models.train - INFO - Transformer model is available
2025-05-16 23:09:44,053 - models.train - INFO - Using TensorFlow-based models
2025-05-16 23:09:44,053 - models.predict - INFO - Transformer model is available for predictions
2025-05-16 23:09:44,053 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-16 23:09:44,053 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:09:44,053 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:09:44,558 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:09:44,559 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:09:44,559 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:09:44,559 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:09:44,559 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:09:44,559 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-16 23:09:44,559 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-16 23:09:44,559 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:09:44,559 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:09:44,560 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:09:44,560 - root - WARNING - Seaborn not available: No module named 'seaborn'. Some visualization features will be limited.
2025-05-16 23:09:44,567 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-16 23:09:44,851 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-16 23:09:44,976 - app.services.llm_service - INFO - llama_cpp is available
2025-05-16 23:10:57,825 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:10:58,849 - app - INFO - Memory management utilities loaded
2025-05-16 23:10:58,849 - app - INFO - Error handling utilities loaded
2025-05-16 23:10:58,849 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-16 23:10:58,849 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-16 23:10:58,849 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-16 23:10:58,849 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-16 23:10:58,849 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-16 23:10:58,849 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-16 23:10:58,849 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-16 23:10:58,849 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:10:58,849 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-16 23:10:58,849 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-16 23:10:58,849 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-16 23:10:58,849 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-16 23:10:58,849 - app - INFO - Applied NumPy fix
2025-05-16 23:10:58,849 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:10:58,859 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:10:58,859 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:10:58,859 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-16 23:10:58,860 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:10:58,860 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:10:58,860 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:10:58,860 - app - INFO - Applied NumPy BitGenerator fix
2025-05-16 23:10:58,863 - app.config - INFO - Configuration initialized
2025-05-16 23:11:01,412 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-16 23:11:01,440 - models.train - INFO - TensorFlow test successful
2025-05-16 23:11:01,897 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-16 23:11:01,898 - models.train - INFO - Transformer model is available
2025-05-16 23:11:01,898 - models.train - INFO - Using TensorFlow-based models
2025-05-16 23:11:01,898 - models.predict - INFO - Transformer model is available for predictions
2025-05-16 23:11:01,899 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-16 23:11:01,899 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:11:02,277 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:11:02,277 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:11:02,277 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:11:02,278 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:11:02,278 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:11:02,278 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-16 23:11:02,279 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-16 23:11:02,279 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:11:02,279 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:11:02,279 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:11:02,280 - root - WARNING - Seaborn not available: No module named 'seaborn'. Some visualization features will be limited.
2025-05-16 23:11:02,285 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-16 23:11:02,412 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-16 23:11:02,468 - app.services.llm_service - INFO - llama_cpp is available
2025-05-16 23:12:08,760 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:12:09,603 - app - INFO - Memory management utilities loaded
2025-05-16 23:12:09,604 - app - INFO - Error handling utilities loaded
2025-05-16 23:12:09,605 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-16 23:12:09,605 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-16 23:12:09,605 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-16 23:12:09,605 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-16 23:12:09,606 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-16 23:12:09,606 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-16 23:12:09,606 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-16 23:12:09,606 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:12:09,606 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-16 23:12:09,606 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-16 23:12:09,606 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-16 23:12:09,606 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-16 23:12:09,606 - app - INFO - Applied NumPy fix
2025-05-16 23:12:09,606 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:12:09,606 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:12:09,606 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:12:09,606 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-16 23:12:09,606 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:12:09,606 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:12:09,606 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:12:09,606 - app - INFO - Applied NumPy BitGenerator fix
2025-05-16 23:12:09,611 - app.config - INFO - Configuration initialized
2025-05-16 23:12:12,040 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-16 23:12:12,069 - models.train - INFO - TensorFlow test successful
2025-05-16 23:12:12,430 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-16 23:12:12,430 - models.train - INFO - Transformer model is available
2025-05-16 23:12:12,430 - models.train - INFO - Using TensorFlow-based models
2025-05-16 23:12:12,431 - models.predict - INFO - Transformer model is available for predictions
2025-05-16 23:12:12,431 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-16 23:12:12,432 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:12:12,863 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:12:12,864 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:12:12,864 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:12:12,864 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:12:12,865 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:12:12,865 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-16 23:12:12,865 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-16 23:12:12,865 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:12:12,865 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:12:12,866 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:12:12,866 - root - WARNING - Seaborn not available: No module named 'seaborn'. Some visualization features will be limited.
2025-05-16 23:12:12,871 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-16 23:12:13,005 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-16 23:12:13,073 - app.services.llm_service - INFO - llama_cpp is available
2025-05-16 23:12:15,901 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:13:42,651 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:13:43,503 - app - INFO - Memory management utilities loaded
2025-05-16 23:13:43,503 - app - INFO - Error handling utilities loaded
2025-05-16 23:13:43,503 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-16 23:13:43,503 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-16 23:13:43,503 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-16 23:13:43,503 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-16 23:13:43,503 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-16 23:13:43,503 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-16 23:13:43,503 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-16 23:13:43,503 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:13:43,503 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-16 23:13:43,503 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-16 23:13:43,503 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-16 23:13:43,503 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-16 23:13:43,503 - app - INFO - Applied NumPy fix
2025-05-16 23:13:43,503 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:13:43,503 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:13:43,503 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:13:43,503 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-16 23:13:43,518 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:13:43,518 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:13:43,518 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:13:43,518 - app - INFO - Applied NumPy BitGenerator fix
2025-05-16 23:13:43,520 - app.config - INFO - Configuration initialized
2025-05-16 23:13:46,036 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-16 23:13:46,071 - models.train - INFO - TensorFlow test successful
2025-05-16 23:13:46,421 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-16 23:13:46,436 - models.train - INFO - Transformer model is available
2025-05-16 23:13:46,436 - models.train - INFO - Using TensorFlow-based models
2025-05-16 23:13:46,436 - models.predict - INFO - Transformer model is available for predictions
2025-05-16 23:13:46,436 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-16 23:13:46,436 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:13:46,866 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:13:46,866 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:13:46,866 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:13:46,867 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:13:46,867 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:13:46,867 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-16 23:13:46,867 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-16 23:13:46,867 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:13:46,867 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:13:46,868 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:13:46,868 - root - WARNING - Seaborn not available: No module named 'seaborn'. Some visualization features will be limited.
2025-05-16 23:13:46,874 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-16 23:13:47,000 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-16 23:13:47,059 - app.services.llm_service - INFO - llama_cpp is available
2025-05-16 23:13:47,065 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:13:47,066 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:13:47,879 - app - INFO - Found 8 stock files in data/stocks
2025-05-16 23:13:47,884 - app.utils.memory_management - INFO - Memory before cleanup: 416.01 MB
2025-05-16 23:13:47,975 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:13:47,975 - app.utils.memory_management - INFO - Memory after cleanup: 416.02 MB (freed -0.01 MB)
2025-05-16 23:13:51,983 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:13:51,986 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:13:51,987 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:13:51,999 - app.utils.memory_management - INFO - Memory before cleanup: 419.88 MB
2025-05-16 23:13:52,108 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:13:52,110 - app.utils.memory_management - INFO - Memory after cleanup: 419.88 MB (freed 0.00 MB)
2025-05-16 23:15:56,414 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:15:56,446 - app.utils.memory_management - INFO - Memory before cleanup: 27.91 MB
2025-05-16 23:15:56,731 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-16 23:15:56,731 - app.utils.memory_management - INFO - Memory after cleanup: 232.90 MB (freed -204.99 MB)
2025-05-16 23:15:58,800 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:15:58,990 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.17 seconds
2025-05-16 23:15:59,003 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:15:59,004 - app - INFO - Data shape: (571, 36)
2025-05-16 23:15:59,004 - app - INFO - File COMI contains 2025 data
2025-05-16 23:15:59,040 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-16 23:15:59,041 - app - INFO - Features shape: (571, 36)
2025-05-16 23:15:59,053 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:15:59,053 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:15:59,054 - app - INFO - Data shape: (571, 36)
2025-05-16 23:15:59,054 - app - INFO - File COMI contains 2025 data
2025-05-16 23:15:59,231 - app.utils.memory_management - INFO - Memory before cleanup: 248.02 MB
2025-05-16 23:15:59,350 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-16 23:15:59,350 - app.utils.memory_management - INFO - Memory after cleanup: 248.11 MB (freed -0.09 MB)
2025-05-16 23:16:03,454 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:16:03,570 - app.services.llm_service - ERROR - Model file not found: D:\AI Stocks Bot\models\llm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:16:03,672 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-05-16 23:16:03,673 - app.utils.error_handling - INFO - llm_insights_component executed in 0.22 seconds
2025-05-16 23:16:03,675 - app.utils.memory_management - INFO - Memory before cleanup: 250.29 MB
2025-05-16 23:16:03,804 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:16:03,805 - app.utils.memory_management - INFO - Memory after cleanup: 250.29 MB (freed 0.00 MB)
2025-05-16 23:18:40,103 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:18:41,045 - app - INFO - Memory management utilities loaded
2025-05-16 23:18:41,049 - app - INFO - Error handling utilities loaded
2025-05-16 23:18:41,057 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-16 23:18:41,061 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-16 23:18:41,062 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-16 23:18:41,063 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-16 23:18:41,067 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-16 23:18:41,069 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-16 23:18:41,069 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-16 23:18:41,069 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:18:41,070 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-16 23:18:41,071 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-16 23:18:41,072 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-16 23:18:41,074 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-16 23:18:41,075 - app - INFO - Applied NumPy fix
2025-05-16 23:18:41,078 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:18:41,080 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:18:41,082 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:18:41,082 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-16 23:18:41,083 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:18:41,085 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:18:41,086 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:18:41,086 - app - INFO - Applied NumPy BitGenerator fix
2025-05-16 23:18:41,093 - app.config - INFO - Configuration initialized
2025-05-16 23:18:42,306 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:18:42,308 - app - INFO - Memory management utilities loaded
2025-05-16 23:18:42,308 - app - INFO - Error handling utilities loaded
2025-05-16 23:18:42,308 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-16 23:18:42,308 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-16 23:18:42,308 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-16 23:18:42,308 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:18:44,025 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-16 23:18:44,059 - models.train - INFO - TensorFlow test successful
2025-05-16 23:18:44,503 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-16 23:18:44,504 - models.train - INFO - Transformer model is available
2025-05-16 23:18:44,504 - models.train - INFO - Using TensorFlow-based models
2025-05-16 23:18:44,505 - models.predict - INFO - Transformer model is available for predictions
2025-05-16 23:18:44,505 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-16 23:18:44,505 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:18:44,850 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:18:44,850 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:18:44,850 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:18:44,850 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:18:44,851 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:18:44,851 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-16 23:18:44,851 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-16 23:18:44,851 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:18:44,851 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:18:44,852 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:18:44,852 - root - WARNING - Seaborn not available: No module named 'seaborn'. Some visualization features will be limited.
2025-05-16 23:18:44,858 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-16 23:18:44,989 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-16 23:18:45,050 - app.services.llm_service - INFO - llama_cpp is available
2025-05-16 23:18:45,056 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:18:45,057 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:18:45,948 - app - INFO - Found 8 stock files in data/stocks
2025-05-16 23:18:45,954 - app.utils.memory_management - INFO - Memory before cleanup: 415.66 MB
2025-05-16 23:18:46,061 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:18:46,061 - app.utils.memory_management - INFO - Memory after cleanup: 415.68 MB (freed -0.01 MB)
2025-05-16 23:19:25,012 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:19:25,024 - app.utils.memory_management - INFO - Memory before cleanup: 417.39 MB
2025-05-16 23:19:25,132 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:19:25,132 - app.utils.memory_management - INFO - Memory after cleanup: 417.39 MB (freed 0.00 MB)
2025-05-16 23:19:26,351 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:19:26,380 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:19:26,380 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:19:26,383 - app - INFO - Data shape: (571, 36)
2025-05-16 23:19:26,383 - app - INFO - File COMI contains 2025 data
2025-05-16 23:19:26,401 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-16 23:19:26,401 - app - INFO - Features shape: (571, 36)
2025-05-16 23:19:26,414 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:19:26,415 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:19:26,415 - app - INFO - Data shape: (571, 36)
2025-05-16 23:19:26,415 - app - INFO - File COMI contains 2025 data
2025-05-16 23:19:26,434 - app.utils.memory_management - INFO - Memory before cleanup: 422.78 MB
2025-05-16 23:19:26,539 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-16 23:19:26,539 - app.utils.memory_management - INFO - Memory after cleanup: 422.82 MB (freed -0.04 MB)
2025-05-16 23:19:28,757 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:19:28,869 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:19:28,869 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:19:28,886 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.02 seconds
2025-05-16 23:19:28,886 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:19:28,886 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:19:28,886 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:19:28,905 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:19:28,905 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:19:28,905 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:19:28,905 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:19:29,041 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.02 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.07s
2025-05-16 23:19:29,041 - app.utils.error_handling - INFO - llm_insights_component executed in 0.28 seconds
2025-05-16 23:19:29,042 - app.utils.memory_management - INFO - Memory before cleanup: 423.55 MB
2025-05-16 23:19:29,136 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:19:29,136 - app.utils.memory_management - INFO - Memory after cleanup: 423.55 MB (freed 0.00 MB)
2025-05-16 23:19:49,582 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:19:49,699 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:19:49,702 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:19:49,714 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:19:49,715 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:19:49,715 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:19:49,715 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:19:49,722 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:19:49,722 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:19:49,722 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:19:49,722 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:19:49,857 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.78 MB, VMS: 0.76 MB, Percent: 0.00%, Execution time: 0.05s
2025-05-16 23:19:49,858 - app.utils.error_handling - INFO - llm_insights_component executed in 0.27 seconds
2025-05-16 23:19:49,859 - app.utils.memory_management - INFO - Memory before cleanup: 424.27 MB
2025-05-16 23:19:49,956 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:19:49,956 - app.utils.memory_management - INFO - Memory after cleanup: 424.27 MB (freed 0.00 MB)
2025-05-16 23:20:06,573 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:20:06,691 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:20:06,694 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:20:06,704 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:20:06,704 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:20:06,705 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:20:06,705 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:20:06,715 - app.services.llm_service - INFO - Loading model from D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf...
2025-05-16 23:20:35,302 - app.services.llm_service - INFO - Model loaded successfully in 28.59 seconds
2025-05-16 23:20:35,310 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:20:35,310 - app.services.llm_service - INFO - Loading model from D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf...
2025-05-16 23:20:40,139 - app.services.llm_service - INFO - Model loaded successfully in 4.83 seconds
2025-05-16 23:22:12,472 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.47 seconds
2025-05-16 23:22:12,517 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:22:12,518 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:22:12,518 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:22:14,179 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 7723.11 MB, VMS: 4582.88 MB, Percent: 47.61%, Execution time: 126.26s
2025-05-16 23:22:14,179 - app.utils.error_handling - INFO - llm_insights_component executed in 127.61 seconds
2025-05-16 23:22:14,181 - app.utils.memory_management - INFO - Memory before cleanup: 8147.44 MB
2025-05-16 23:22:14,341 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:22:14,342 - app.utils.memory_management - INFO - Memory after cleanup: 8147.44 MB (freed 0.00 MB)
2025-05-16 23:23:42,972 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:23:43,115 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:23:43,119 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:23:43,168 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.05 seconds
2025-05-16 23:23:43,168 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:23:43,169 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:23:43,169 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:23:43,184 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-16 23:23:43,184 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:23:43,198 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:23:43,198 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:23:43,336 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 2.01 MB, VMS: 0.46 MB, Percent: 0.01%, Execution time: 0.11s
2025-05-16 23:23:43,336 - app.utils.error_handling - INFO - llm_insights_component executed in 0.35 seconds
2025-05-16 23:23:43,338 - app.utils.memory_management - INFO - Memory before cleanup: 8153.23 MB
2025-05-16 23:23:43,449 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:23:43,449 - app.utils.memory_management - INFO - Memory after cleanup: 8153.23 MB (freed 0.00 MB)
2025-05-16 23:32:18,604 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:32:21,521 - app - INFO - Memory management utilities loaded
2025-05-16 23:32:21,531 - app - INFO - Error handling utilities loaded
2025-05-16 23:32:21,541 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-16 23:32:21,542 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-16 23:32:21,542 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-16 23:32:21,542 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-16 23:32:21,542 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-16 23:32:21,542 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-16 23:32:21,543 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-16 23:32:21,543 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:32:21,554 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-16 23:32:21,554 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-16 23:32:21,555 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-16 23:32:21,555 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-16 23:32:21,555 - app - INFO - Applied NumPy fix
2025-05-16 23:32:21,567 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:32:21,567 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:32:21,567 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:32:21,568 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-16 23:32:21,568 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:32:21,568 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:32:21,568 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:32:21,568 - app - INFO - Applied NumPy BitGenerator fix
2025-05-16 23:32:21,619 - app.config - INFO - Configuration initialized
2025-05-16 23:32:33,577 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-16 23:32:33,861 - models.train - INFO - TensorFlow test successful
2025-05-16 23:32:35,814 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-16 23:32:35,814 - models.train - INFO - Transformer model is available
2025-05-16 23:32:35,815 - models.train - INFO - Using TensorFlow-based models
2025-05-16 23:32:35,821 - models.predict - INFO - Transformer model is available for predictions
2025-05-16 23:32:35,821 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-16 23:32:35,821 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:32:35,821 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:32:37,042 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:32:37,043 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:32:37,043 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:32:37,043 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:32:37,043 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:32:37,043 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-16 23:32:37,043 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-16 23:32:37,044 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:32:37,044 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:32:37,044 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:32:37,045 - root - WARNING - Seaborn not available: No module named 'seaborn'. Some visualization features will be limited.
2025-05-16 23:32:37,134 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-16 23:32:37,899 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-16 23:32:38,230 - app.services.llm_service - INFO - llama_cpp is available
2025-05-16 23:32:38,255 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:32:38,257 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:32:38,258 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:32:38,259 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:32:38,264 - app - INFO - Found 8 stock files in data/stocks
2025-05-16 23:32:38,271 - app.utils.memory_management - INFO - Memory before cleanup: 414.11 MB
2025-05-16 23:32:38,373 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:32:38,373 - app.utils.memory_management - INFO - Memory after cleanup: 414.52 MB (freed -0.42 MB)
2025-05-16 23:32:39,999 - app.utils.memory_management - INFO - Memory before cleanup: 420.04 MB
2025-05-16 23:32:40,094 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:32:40,095 - app.utils.memory_management - INFO - Memory after cleanup: 420.04 MB (freed -0.00 MB)
2025-05-16 23:32:46,229 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:32:46,242 - app.utils.memory_management - INFO - Memory before cleanup: 421.29 MB
2025-05-16 23:32:46,360 - app.utils.memory_management - INFO - Garbage collection: collected 193 objects
2025-05-16 23:32:46,376 - app.utils.memory_management - INFO - Memory after cleanup: 421.32 MB (freed -0.04 MB)
2025-05-16 23:32:47,393 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:32:47,483 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.07 seconds
2025-05-16 23:32:47,484 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:32:47,484 - app - INFO - Data shape: (571, 36)
2025-05-16 23:32:47,484 - app - INFO - File COMI contains 2025 data
2025-05-16 23:32:47,516 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-05-16 23:32:47,516 - app - INFO - Features shape: (571, 36)
2025-05-16 23:32:47,532 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-16 23:32:47,532 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:32:47,533 - app - INFO - Data shape: (571, 36)
2025-05-16 23:32:47,533 - app - INFO - File COMI contains 2025 data
2025-05-16 23:32:47,695 - app.utils.memory_management - INFO - Memory before cleanup: 424.78 MB
2025-05-16 23:32:47,801 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-16 23:32:47,801 - app.utils.memory_management - INFO - Memory after cleanup: 424.79 MB (freed -0.01 MB)
2025-05-16 23:32:50,455 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:32:50,579 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:32:50,579 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:32:50,595 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.02 seconds
2025-05-16 23:32:50,595 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:32:50,595 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:32:50,595 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:32:50,612 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:32:50,612 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:32:50,612 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:32:50,612 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:32:50,748 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.02 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.07s
2025-05-16 23:32:50,748 - app.utils.error_handling - INFO - llm_insights_component executed in 0.28 seconds
2025-05-16 23:32:50,750 - app.utils.memory_management - INFO - Memory before cleanup: 425.42 MB
2025-05-16 23:32:50,846 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:32:50,846 - app.utils.memory_management - INFO - Memory after cleanup: 425.42 MB (freed 0.00 MB)
2025-05-16 23:32:59,573 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:32:59,694 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:32:59,705 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:32:59,742 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:32:59,742 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:32:59,743 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:32:59,743 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:32:59,771 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:32:59,771 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:32:59,771 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:32:59,771 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:32:59,891 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.02 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.10s
2025-05-16 23:32:59,891 - app.utils.error_handling - INFO - llm_insights_component executed in 0.32 seconds
2025-05-16 23:32:59,905 - app.utils.memory_management - INFO - Memory before cleanup: 425.48 MB
2025-05-16 23:33:00,009 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:33:00,009 - app.utils.memory_management - INFO - Memory after cleanup: 425.48 MB (freed 0.00 MB)
2025-05-16 23:33:07,083 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:33:07,221 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:33:07,226 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:33:07,229 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:33:07,229 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:33:07,229 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:33:07,229 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:33:07,825 - app.services.news_service - INFO - Retrieved 4 news articles for COMI
2025-05-16 23:33:07,844 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:33:07,844 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:33:07,844 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:33:07,844 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:33:07,996 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.96 MB, VMS: 0.05 MB, Percent: 0.01%, Execution time: 0.67s
2025-05-16 23:33:07,996 - app.utils.error_handling - INFO - llm_insights_component executed in 0.89 seconds
2025-05-16 23:33:07,998 - app.utils.memory_management - INFO - Memory before cleanup: 426.44 MB
2025-05-16 23:33:08,105 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:33:08,105 - app.utils.memory_management - INFO - Memory after cleanup: 426.44 MB (freed 0.00 MB)
2025-05-16 23:33:51,751 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:33:51,983 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:33:51,988 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:33:51,993 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:33:51,994 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:33:51,994 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:33:51,995 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:33:52,021 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:33:52,022 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:33:52,022 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:33:52,023 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:33:52,209 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.03 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.07s
2025-05-16 23:33:52,209 - app.utils.error_handling - INFO - llm_insights_component executed in 0.43 seconds
2025-05-16 23:33:52,209 - app.utils.memory_management - INFO - Memory before cleanup: 426.68 MB
2025-05-16 23:33:52,323 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:33:52,323 - app.utils.memory_management - INFO - Memory after cleanup: 426.68 MB (freed 0.00 MB)
2025-05-16 23:33:53,778 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:33:53,897 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:33:53,899 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:33:53,907 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-05-16 23:33:53,907 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:33:53,907 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:33:53,907 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:33:53,923 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:33:53,923 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:33:53,923 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:33:53,923 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:33:54,541 - app.services.news_service - ERROR - Error searching financial news: 426 - {"status":"error","code":"parameterInvalid","message":"You are trying to request results too far in the past. Your plan permits you to request articles as far back as 2025-04-15, but you have requested 2025-02-15. You may need to upgrade to a paid plan."}
2025-05-16 23:33:54,641 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.02 MB, VMS: 0.02 MB, Percent: 0.00%, Execution time: 0.64s
2025-05-16 23:33:54,641 - app.utils.error_handling - INFO - llm_insights_component executed in 0.85 seconds
2025-05-16 23:33:54,641 - app.utils.memory_management - INFO - Memory before cleanup: 426.69 MB
2025-05-16 23:33:54,741 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:33:54,741 - app.utils.memory_management - INFO - Memory after cleanup: 426.69 MB (freed 0.00 MB)
2025-05-16 23:34:11,310 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:34:11,432 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:34:11,436 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:34:11,443 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-05-16 23:34:11,443 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:34:11,444 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:34:11,444 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:34:11,466 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:34:11,466 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:34:11,466 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:34:11,467 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:34:12,109 - app.services.news_service - INFO - Retrieved 3 top headlines for business in us
2025-05-16 23:34:12,208 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.02 MB, VMS: 0.02 MB, Percent: 0.00%, Execution time: 0.69s
2025-05-16 23:34:12,208 - app.utils.error_handling - INFO - llm_insights_component executed in 0.88 seconds
2025-05-16 23:34:12,225 - app.utils.memory_management - INFO - Memory before cleanup: 426.70 MB
2025-05-16 23:34:12,325 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:34:12,325 - app.utils.memory_management - INFO - Memory after cleanup: 426.70 MB (freed 0.00 MB)
2025-05-16 23:34:36,037 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:34:36,156 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:34:36,159 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:34:36,164 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:34:36,165 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:34:36,165 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:34:36,166 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:34:36,702 - app.services.news_service - INFO - Retrieved 4 news articles for COMI
2025-05-16 23:34:36,728 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:34:36,729 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:34:36,729 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:34:36,730 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:34:36,848 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.03 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.59s
2025-05-16 23:34:36,848 - app.utils.error_handling - INFO - llm_insights_component executed in 0.80 seconds
2025-05-16 23:34:36,848 - app.utils.memory_management - INFO - Memory before cleanup: 426.69 MB
2025-05-16 23:34:36,949 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:34:36,949 - app.utils.memory_management - INFO - Memory after cleanup: 426.69 MB (freed 0.00 MB)
2025-05-16 23:39:09,107 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:39:09,979 - app - INFO - Memory management utilities loaded
2025-05-16 23:39:09,979 - app - INFO - Error handling utilities loaded
2025-05-16 23:39:09,979 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-16 23:39:09,979 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-16 23:39:09,979 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-16 23:39:09,979 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-16 23:39:09,979 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-16 23:39:09,979 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-16 23:39:09,979 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-16 23:39:09,979 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:39:09,979 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-16 23:39:09,979 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-16 23:39:09,979 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-16 23:39:09,979 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-16 23:39:09,979 - app - INFO - Applied NumPy fix
2025-05-16 23:39:09,979 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:39:09,979 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:39:09,979 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:39:09,979 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-16 23:39:09,979 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:39:09,979 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:39:09,979 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:39:09,979 - app - INFO - Applied NumPy BitGenerator fix
2025-05-16 23:39:09,979 - app.config - INFO - Configuration initialized
2025-05-16 23:39:12,476 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-16 23:39:12,501 - models.train - INFO - TensorFlow test successful
2025-05-16 23:39:12,943 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-16 23:39:12,943 - models.train - INFO - Transformer model is available
2025-05-16 23:39:12,943 - models.train - INFO - Using TensorFlow-based models
2025-05-16 23:39:12,944 - models.predict - INFO - Transformer model is available for predictions
2025-05-16 23:39:12,945 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-16 23:39:12,945 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:39:13,290 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:39:13,290 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:39:13,290 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:39:13,290 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:39:13,290 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:39:13,290 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-16 23:39:13,291 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-16 23:39:13,291 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:39:13,291 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:39:13,291 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:39:13,292 - root - WARNING - Seaborn not available: No module named 'seaborn'. Some visualization features will be limited.
2025-05-16 23:39:13,299 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-16 23:39:13,507 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-16 23:39:13,612 - app.services.llm_service - INFO - llama_cpp is available
2025-05-16 23:39:13,632 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:39:13,632 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:39:14,540 - app - INFO - Found 8 stock files in data/stocks
2025-05-16 23:39:14,547 - app.utils.memory_management - INFO - Memory before cleanup: 416.52 MB
2025-05-16 23:39:14,671 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:39:14,671 - app - INFO - Memory management utilities loaded
2025-05-16 23:39:14,671 - app - INFO - Error handling utilities loaded
2025-05-16 23:39:14,671 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:39:14,671 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-16 23:39:14,671 - app.utils.memory_management - INFO - Memory after cleanup: 416.53 MB (freed -0.01 MB)
2025-05-16 23:39:14,671 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-16 23:39:14,671 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:39:30,620 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:39:30,632 - app.utils.memory_management - INFO - Memory before cleanup: 419.84 MB
2025-05-16 23:39:30,733 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:39:30,733 - app.utils.memory_management - INFO - Memory after cleanup: 419.84 MB (freed 0.00 MB)
2025-05-16 23:39:31,885 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:39:31,917 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-16 23:39:31,918 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:39:31,918 - app - INFO - Data shape: (571, 36)
2025-05-16 23:39:31,918 - app - INFO - File COMI contains 2025 data
2025-05-16 23:39:31,992 - app - INFO - Feature engineering for COMI completed in 0.07 seconds
2025-05-16 23:39:31,993 - app - INFO - Features shape: (571, 36)
2025-05-16 23:39:32,009 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:39:32,010 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:39:32,010 - app - INFO - Data shape: (571, 36)
2025-05-16 23:39:32,010 - app - INFO - File COMI contains 2025 data
2025-05-16 23:39:32,022 - app.utils.memory_management - INFO - Memory before cleanup: 425.29 MB
2025-05-16 23:39:32,134 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-16 23:39:32,134 - app.utils.memory_management - INFO - Memory after cleanup: 425.33 MB (freed -0.04 MB)
2025-05-16 23:39:49,946 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:39:50,051 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:39:50,068 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:39:50,068 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:39:50,068 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:39:50,068 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:39:50,068 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:39:50,085 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:39:50,085 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:39:50,101 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:39:50,101 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:39:50,222 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.02 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.07s
2025-05-16 23:39:50,222 - app.utils.error_handling - INFO - llm_insights_component executed in 0.27 seconds
2025-05-16 23:39:50,222 - app.utils.memory_management - INFO - Memory before cleanup: 424.02 MB
2025-05-16 23:39:50,352 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:39:50,352 - app.utils.memory_management - INFO - Memory after cleanup: 424.02 MB (freed 0.00 MB)
2025-05-16 23:39:58,480 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:39:58,601 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:39:58,604 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:39:58,607 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:39:58,608 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:39:58,609 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:39:58,610 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:39:58,635 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:39:58,636 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:39:58,636 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:39:58,637 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:39:58,765 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.16 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.06s
2025-05-16 23:39:58,765 - app.utils.error_handling - INFO - llm_insights_component executed in 0.27 seconds
2025-05-16 23:39:58,766 - app.utils.memory_management - INFO - Memory before cleanup: 424.18 MB
2025-05-16 23:39:58,902 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:39:58,902 - app.utils.memory_management - INFO - Memory after cleanup: 424.18 MB (freed 0.00 MB)
2025-05-16 23:40:02,414 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:40:02,536 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:40:02,540 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:40:02,547 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:40:02,548 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:40:02,548 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:40:02,549 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:40:03,100 - app.services.news_service - INFO - Retrieved 0 news articles for COMI in egypt market
2025-05-16 23:40:03,110 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:40:03,111 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:40:03,111 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:40:03,112 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:40:03,240 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 1.09 MB, VMS: 0.30 MB, Percent: 0.01%, Execution time: 0.61s
2025-05-16 23:40:03,240 - app.utils.error_handling - INFO - llm_insights_component executed in 0.81 seconds
2025-05-16 23:40:03,241 - app.utils.memory_management - INFO - Memory before cleanup: 425.27 MB
2025-05-16 23:40:03,332 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:40:03,332 - app.utils.memory_management - INFO - Memory after cleanup: 425.27 MB (freed -0.00 MB)
2025-05-16 23:40:21,089 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:40:21,232 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:40:21,232 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:40:21,232 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:40:21,232 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:40:21,232 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:40:21,232 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:40:21,262 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:40:21,263 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:40:21,263 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:40:21,263 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:40:21,388 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.11 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.06s
2025-05-16 23:40:21,388 - app.utils.error_handling - INFO - llm_insights_component executed in 0.29 seconds
2025-05-16 23:40:21,389 - app.utils.memory_management - INFO - Memory before cleanup: 425.50 MB
2025-05-16 23:40:21,489 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:40:21,490 - app.utils.memory_management - INFO - Memory after cleanup: 425.50 MB (freed 0.00 MB)
2025-05-16 23:40:22,634 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:40:22,757 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:40:22,759 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:40:22,765 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:40:22,766 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:40:22,766 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:40:22,767 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:40:22,788 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:40:22,789 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:40:22,790 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:40:22,790 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:40:23,302 - app.services.news_service - ERROR - Error searching financial news: 426 - {"status":"error","code":"parameterInvalid","message":"You are trying to request results too far in the past. Your plan permits you to request articles as far back as 2025-04-15, but you have requested 2025-02-15. You may need to upgrade to a paid plan."}
2025-05-16 23:40:23,434 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.10 MB, VMS: 0.02 MB, Percent: 0.00%, Execution time: 0.58s
2025-05-16 23:40:23,434 - app.utils.error_handling - INFO - llm_insights_component executed in 0.78 seconds
2025-05-16 23:40:23,434 - app.utils.memory_management - INFO - Memory before cleanup: 425.59 MB
2025-05-16 23:40:23,534 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:40:23,534 - app.utils.memory_management - INFO - Memory after cleanup: 425.59 MB (freed 0.00 MB)
2025-05-16 23:40:30,959 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:40:31,082 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:40:31,085 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:40:31,089 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:40:31,090 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:40:31,091 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:40:31,091 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:40:31,114 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:40:31,115 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:40:31,115 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:40:31,116 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:40:31,664 - app.services.news_service - INFO - Retrieved 3 market news articles for egypt market
2025-05-16 23:40:31,759 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.20 MB, VMS: 0.27 MB, Percent: 0.00%, Execution time: 0.59s
2025-05-16 23:40:31,774 - app.utils.error_handling - INFO - llm_insights_component executed in 0.80 seconds
2025-05-16 23:40:31,776 - app.utils.memory_management - INFO - Memory before cleanup: 425.77 MB
2025-05-16 23:40:31,879 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:40:31,880 - app.utils.memory_management - INFO - Memory after cleanup: 425.77 MB (freed 0.00 MB)
2025-05-16 23:41:11,252 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:41:11,451 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:41:11,480 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:41:11,488 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:41:11,489 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:41:11,489 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:41:11,490 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:41:12,015 - app.services.news_service - INFO - Retrieved 0 news articles for COMI in egypt market
2025-05-16 23:41:12,032 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.02 seconds
2025-05-16 23:41:12,033 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:41:12,033 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:41:12,034 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:41:12,148 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.02 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.61s
2025-05-16 23:41:12,148 - app.utils.error_handling - INFO - llm_insights_component executed in 0.87 seconds
2025-05-16 23:41:12,148 - app.utils.memory_management - INFO - Memory before cleanup: 425.77 MB
2025-05-16 23:41:12,263 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:41:12,264 - app.utils.memory_management - INFO - Memory after cleanup: 425.77 MB (freed 0.00 MB)
2025-05-16 23:50:01,470 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-16 23:50:02,328 - app - INFO - Memory management utilities loaded
2025-05-16 23:50:02,328 - app - INFO - Error handling utilities loaded
2025-05-16 23:50:02,328 - app.utils.numpy_fix - WARNING - numpy._core module not found, applying fix
2025-05-16 23:50:02,328 - app.utils.numpy_fix - INFO - Successfully copied numpy.core attributes to mock module
2025-05-16 23:50:02,328 - app.utils.numpy_fix - WARNING - numpy._core.multiarray module not found, applying fix
2025-05-16 23:50:02,328 - app.utils.numpy_fix - INFO - Found multiarray at numpy.core.multiarray
2025-05-16 23:50:02,328 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core.multiarray module
2025-05-16 23:50:02,328 - app.utils.numpy_fix - WARNING - numpy._core._multiarray_umath module not found, applying fix
2025-05-16 23:50:02,328 - app.utils.numpy_fix - INFO - Successfully created mock numpy._core._multiarray_umath module
2025-05-16 23:50:02,328 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-05-16 23:50:02,328 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-05-16 23:50:02,328 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-05-16 23:50:02,328 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-05-16 23:50:02,328 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-05-16 23:50:02,328 - app - INFO - Applied NumPy fix
2025-05-16 23:50:02,328 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:50:02,328 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:50:02,328 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:50:02,328 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-05-16 23:50:02,328 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:50:02,328 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:50:02,328 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:50:02,328 - app - INFO - Applied NumPy BitGenerator fix
2025-05-16 23:50:02,328 - app.config - INFO - Configuration initialized
2025-05-16 23:50:04,861 - models.train - INFO - TensorFlow version: 2.9.1
2025-05-16 23:50:04,901 - models.train - INFO - TensorFlow test successful
2025-05-16 23:50:05,342 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-05-16 23:50:05,342 - models.train - INFO - Transformer model is available
2025-05-16 23:50:05,342 - models.train - INFO - Using TensorFlow-based models
2025-05-16 23:50:05,342 - models.predict - INFO - Transformer model is available for predictions
2025-05-16 23:50:05,342 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-05-16 23:50:05,342 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:50:05,704 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:50:05,705 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-05-16 23:50:05,705 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.23.5
2025-05-16 23:50:05,705 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-05-16 23:50:05,705 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-05-16 23:50:05,705 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-05-16 23:50:05,706 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-05-16 23:50:05,706 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-05-16 23:50:05,706 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-05-16 23:50:05,706 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-05-16 23:50:05,707 - root - WARNING - Seaborn not available: No module named 'seaborn'. Some visualization features will be limited.
2025-05-16 23:50:05,712 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-05-16 23:50:05,851 - app.models.advanced_time_series - WARNING - pmdarima not available. Auto ARIMA will not be available.
2025-05-16 23:50:05,915 - app.services.llm_service - INFO - llama_cpp is available
2025-05-16 23:50:05,957 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:50:05,959 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:50:06,773 - app - INFO - Found 8 stock files in data/stocks
2025-05-16 23:50:06,780 - app.utils.memory_management - INFO - Memory before cleanup: 417.02 MB
2025-05-16 23:50:06,889 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:50:06,890 - app.utils.memory_management - INFO - Memory after cleanup: 417.02 MB (freed -0.00 MB)
2025-05-16 23:50:11,163 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:50:11,167 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:50:11,170 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:50:11,182 - app.utils.memory_management - INFO - Memory before cleanup: 420.26 MB
2025-05-16 23:50:11,297 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:50:11,299 - app.utils.memory_management - INFO - Memory after cleanup: 420.29 MB (freed -0.03 MB)
2025-05-16 23:50:11,408 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:50:11,411 - app.utils.session_state - INFO - Initializing session state
2025-05-16 23:50:11,413 - app.utils.session_state - INFO - Session state initialized
2025-05-16 23:50:11,420 - app.utils.memory_management - INFO - Memory before cleanup: 420.64 MB
2025-05-16 23:50:11,526 - app.utils.memory_management - INFO - Garbage collection: collected 87 objects
2025-05-16 23:50:11,527 - app.utils.memory_management - INFO - Memory after cleanup: 420.68 MB (freed -0.04 MB)
2025-05-16 23:50:23,900 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:50:23,922 - app.utils.memory_management - INFO - Memory before cleanup: 421.66 MB
2025-05-16 23:50:24,049 - app.utils.memory_management - INFO - Garbage collection: collected 299 objects
2025-05-16 23:50:24,049 - app.utils.memory_management - INFO - Memory after cleanup: 421.66 MB (freed 0.00 MB)
2025-05-16 23:50:25,401 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:50:25,439 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-05-16 23:50:25,462 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:50:25,473 - app - INFO - Data shape: (571, 36)
2025-05-16 23:50:25,473 - app - INFO - File COMI contains 2025 data
2025-05-16 23:50:25,497 - app - INFO - Feature engineering for COMI completed in 0.02 seconds
2025-05-16 23:50:25,498 - app - INFO - Features shape: (571, 36)
2025-05-16 23:50:25,503 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-16 23:50:25,503 - app - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:50:25,503 - app - INFO - Data shape: (571, 36)
2025-05-16 23:50:25,503 - app - INFO - File COMI contains 2025 data
2025-05-16 23:50:25,533 - app.utils.memory_management - INFO - Memory before cleanup: 427.15 MB
2025-05-16 23:50:25,659 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-05-16 23:50:25,659 - app.utils.memory_management - INFO - Memory after cleanup: 427.15 MB (freed 0.00 MB)
2025-05-16 23:50:34,121 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:50:34,225 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:50:34,242 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:50:34,242 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:50:34,242 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:50:34,242 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:50:34,242 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:50:34,258 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:50:34,258 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:50:34,258 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:50:34,258 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:50:34,431 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.03 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.07s
2025-05-16 23:50:34,435 - app.utils.error_handling - INFO - llm_insights_component executed in 0.31 seconds
2025-05-16 23:50:34,437 - app.utils.memory_management - INFO - Memory before cleanup: 425.68 MB
2025-05-16 23:50:34,541 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:50:34,542 - app.utils.memory_management - INFO - Memory after cleanup: 425.68 MB (freed 0.00 MB)
2025-05-16 23:50:39,913 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:50:40,044 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:50:40,050 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:50:40,059 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:50:40,060 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:50:40,060 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:50:40,061 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:50:40,086 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:50:40,087 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:50:40,087 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:50:40,087 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:50:40,214 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.03 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.07s
2025-05-16 23:50:40,214 - app.utils.error_handling - INFO - llm_insights_component executed in 0.30 seconds
2025-05-16 23:50:40,214 - app.utils.memory_management - INFO - Memory before cleanup: 425.71 MB
2025-05-16 23:50:40,349 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:50:40,349 - app.utils.memory_management - INFO - Memory after cleanup: 425.71 MB (freed 0.00 MB)
2025-05-16 23:50:41,820 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:50:41,945 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:50:41,949 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:50:41,957 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:50:41,957 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:50:41,957 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:50:41,957 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:50:41,971 - app.services.llm_service - INFO - Loading model from D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf...
2025-05-16 23:51:09,059 - app.services.llm_service - INFO - Model loaded successfully in 27.09 seconds
2025-05-16 23:51:09,059 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:51:09,059 - app.services.llm_service - INFO - Loading model from D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf...
2025-05-16 23:51:20,137 - app.services.llm_service - INFO - Model loaded successfully in 11.06 seconds
2025-05-16 23:52:52,268 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.08 seconds
2025-05-16 23:52:52,271 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:52:52,271 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:52:52,272 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:52:53,737 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 6982.07 MB, VMS: 4582.89 MB, Percent: 43.04%, Execution time: 130.61s
2025-05-16 23:52:53,737 - app.utils.error_handling - INFO - llm_insights_component executed in 131.90 seconds
2025-05-16 23:52:53,739 - app.utils.memory_management - INFO - Memory before cleanup: 7407.85 MB
2025-05-16 23:52:53,837 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:52:53,837 - app.utils.memory_management - INFO - Memory after cleanup: 7407.85 MB (freed 0.00 MB)
2025-05-16 23:53:07,895 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:53:08,063 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:53:08,067 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:53:08,099 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-05-16 23:53:08,100 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:53:08,100 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:53:08,101 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:53:08,131 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:53:08,132 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:53:08,132 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:53:08,132 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:53:08,292 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 1.23 MB, VMS: 1.21 MB, Percent: 0.01%, Execution time: 0.10s
2025-05-16 23:53:08,292 - app.utils.error_handling - INFO - llm_insights_component executed in 0.37 seconds
2025-05-16 23:53:08,292 - app.utils.memory_management - INFO - Memory before cleanup: 7412.34 MB
2025-05-16 23:53:08,398 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:53:08,398 - app.utils.memory_management - INFO - Memory after cleanup: 7412.34 MB (freed 0.00 MB)
2025-05-16 23:53:10,878 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:53:10,999 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:53:11,002 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:53:11,009 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:53:11,009 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:53:11,009 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:53:11,009 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:53:11,914 - app.services.news_service - INFO - Retrieved 1 news articles for COMI in egypt market
2025-05-16 23:53:11,951 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:53:11,951 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:53:11,952 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:53:11,952 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:53:12,078 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 3.84 MB, VMS: 0.05 MB, Percent: 0.02%, Execution time: 0.98s
2025-05-16 23:53:12,095 - app.utils.error_handling - INFO - llm_insights_component executed in 1.20 seconds
2025-05-16 23:53:12,095 - app.utils.memory_management - INFO - Memory before cleanup: 7416.24 MB
2025-05-16 23:53:12,215 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:53:12,215 - app.utils.memory_management - INFO - Memory after cleanup: 7416.24 MB (freed 0.00 MB)
2025-05-16 23:53:54,015 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:53:54,196 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:53:54,206 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:53:54,222 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:53:54,223 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:53:54,223 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:53:54,223 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:53:54,256 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:53:54,258 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:53:54,258 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:53:54,258 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:53:54,385 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.05 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.09s
2025-05-16 23:53:54,385 - app.utils.error_handling - INFO - llm_insights_component executed in 0.35 seconds
2025-05-16 23:53:54,385 - app.utils.memory_management - INFO - Memory before cleanup: 7418.53 MB
2025-05-16 23:53:54,486 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:53:54,486 - app.utils.memory_management - INFO - Memory after cleanup: 7418.53 MB (freed 0.00 MB)
2025-05-16 23:53:56,038 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:53:56,157 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:53:56,160 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:53:56,172 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:53:56,173 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:53:56,173 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:53:56,173 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:53:56,193 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:53:56,194 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:53:56,194 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:53:56,194 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:53:57,093 - app.services.news_service - ERROR - Error searching financial news: 426 - {"status":"error","code":"parameterInvalid","message":"You are trying to request results too far in the past. Your plan permits you to request articles as far back as 2025-04-15, but you have requested 2024-11-17. You may need to upgrade to a paid plan."}
2025-05-16 23:53:57,223 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.06 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.95s
2025-05-16 23:53:57,223 - app.utils.error_handling - INFO - llm_insights_component executed in 1.17 seconds
2025-05-16 23:53:57,224 - app.utils.memory_management - INFO - Memory before cleanup: 7418.61 MB
2025-05-16 23:53:57,355 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:53:57,355 - app.utils.memory_management - INFO - Memory after cleanup: 7418.61 MB (freed 0.00 MB)
2025-05-16 23:54:15,456 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:54:15,585 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:54:15,590 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:54:15,602 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:54:15,603 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:54:15,603 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:54:15,603 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:54:15,622 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:54:15,623 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:54:15,623 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:54:15,624 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:54:16,192 - app.services.news_service - INFO - Retrieved 3 market news articles for egypt market
2025-05-16 23:54:16,290 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.06 MB, VMS: 0.02 MB, Percent: 0.00%, Execution time: 0.61s
2025-05-16 23:54:16,290 - app.utils.error_handling - INFO - llm_insights_component executed in 0.83 seconds
2025-05-16 23:54:16,290 - app.utils.memory_management - INFO - Memory before cleanup: 7418.70 MB
2025-05-16 23:54:16,407 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:54:16,407 - app.utils.memory_management - INFO - Memory after cleanup: 7418.70 MB (freed 0.00 MB)
2025-05-16 23:55:14,130 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:55:14,156 - app - INFO - Found 8 stock files in data/stocks
2025-05-16 23:55:14,306 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:55:14,310 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:55:14,322 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:55:14,322 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:55:14,322 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:55:14,322 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:55:14,913 - app.services.news_service - INFO - Retrieved 1 news articles for COMI in egypt market
2025-05-16 23:55:14,930 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.02 seconds
2025-05-16 23:55:14,930 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:55:14,931 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:55:14,931 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:55:15,047 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.20 MB, VMS: -0.02 MB, Percent: 0.00%, Execution time: 0.65s
2025-05-16 23:55:15,047 - app.utils.error_handling - INFO - llm_insights_component executed in 0.89 seconds
2025-05-16 23:55:15,047 - app.utils.memory_management - INFO - Memory before cleanup: 7419.72 MB
2025-05-16 23:55:15,147 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:55:15,147 - app.utils.memory_management - INFO - Memory after cleanup: 7419.72 MB (freed 0.00 MB)
2025-05-16 23:55:25,480 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:55:25,597 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:55:25,603 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:55:25,613 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:55:25,613 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:55:25,613 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:55:25,614 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:55:26,077 - app.services.news_service - INFO - Retrieved 1 news articles for COMI in egypt market
2025-05-16 23:55:26,118 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:55:26,118 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:55:26,119 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:55:26,119 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:55:26,244 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.05 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.55s
2025-05-16 23:55:26,245 - app.utils.error_handling - INFO - llm_insights_component executed in 0.75 seconds
2025-05-16 23:55:26,246 - app.utils.memory_management - INFO - Memory before cleanup: 7419.78 MB
2025-05-16 23:55:26,342 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:55:26,342 - app.utils.memory_management - INFO - Memory after cleanup: 7419.78 MB (freed 0.00 MB)
2025-05-16 23:55:58,374 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:55:58,523 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:55:58,532 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:55:58,545 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-05-16 23:55:58,545 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:55:58,546 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:55:58,546 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:55:58,581 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-05-16 23:55:58,582 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:55:58,582 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:55:58,582 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:55:58,756 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.05 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.09s
2025-05-16 23:55:58,756 - app.utils.error_handling - INFO - llm_insights_component executed in 0.36 seconds
2025-05-16 23:55:58,756 - app.utils.memory_management - INFO - Memory before cleanup: 7419.84 MB
2025-05-16 23:55:58,857 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:55:58,857 - app.utils.memory_management - INFO - Memory after cleanup: 7419.84 MB (freed 0.00 MB)
2025-05-16 23:56:10,976 - app - INFO - Using TensorFlow-based LSTM model
2025-05-16 23:56:11,097 - app.services.llm_service - INFO - LLM Service initialized with model path: D:\AI Stocks Bot\modelsllm\capybarahermes-2.5-mistral-7b.Q4_K_M.gguf
2025-05-16 23:56:11,103 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-05-16 23:56:11,106 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.00 seconds
2025-05-16 23:56:11,106 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-05-15
2025-05-16 23:56:11,106 - app.utils.common - INFO - Data shape: (571, 36)
2025-05-16 23:56:11,106 - app.utils.common - INFO - File COMI contains 2025 data
2025-05-16 23:56:11,602 - app.services.news_service - INFO - Retrieved 1 news articles for COMI in egypt market
2025-05-16 23:56:11,619 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.02 seconds
2025-05-16 23:56:11,619 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-05-16 23:56:11,619 - app.utils.common - INFO - Data shape: (309, 6)
2025-05-16 23:56:11,619 - app.utils.common - INFO - File ABUK contains 2025 data
2025-05-16 23:56:11,733 - app.utils.memory_management - INFO - Memory usage for llm_insights_component: RSS: 0.08 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.55s
2025-05-16 23:56:11,733 - app.utils.error_handling - INFO - llm_insights_component executed in 0.74 seconds
2025-05-16 23:56:11,733 - app.utils.memory_management - INFO - Memory before cleanup: 7420.07 MB
2025-05-16 23:56:11,837 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-05-16 23:56:11,837 - app.utils.memory_management - INFO - Memory after cleanup: 7420.07 MB (freed 0.00 MB)
2025-05-17 00:03:11,031 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-05-17 00:03:11,055 - app - INFO - Memory management utilities loaded
2025-05-17 00:03:11,070 - app - INFO - Error handling utilities loaded
2025-05-17 00:03:11,081 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-05-17 00:03:11,081 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-05-17 00:03:11,082 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-05-17 00:03:11,082 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
