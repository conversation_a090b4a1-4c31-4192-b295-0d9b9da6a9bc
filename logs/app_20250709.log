2025-07-09 13:20:24,051 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-07-09 13:20:27,792 - app - INFO - Memory management utilities loaded
2025-07-09 13:20:27,796 - app - INFO - Error handling utilities loaded
2025-07-09 13:20:27,798 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-07-09 13:20:27,800 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-07-09 13:20:27,800 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-07-09 13:20:27,801 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-07-09 13:20:27,813 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-07-09 13:20:27,822 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-07-09 13:20:27,823 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-07-09 13:20:27,823 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-07-09 13:20:27,823 - app - INFO - Applied NumPy fix
2025-07-09 13:20:27,825 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-09 13:20:27,829 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-09 13:20:27,830 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-09 13:20:27,830 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-07-09 13:20:27,831 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-09 13:20:27,831 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-09 13:20:27,831 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-09 13:20:27,832 - app - INFO - Applied NumPy BitGenerator fix
2025-07-09 13:20:50,889 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-07-09 13:20:50,890 - app - INFO - Applied TensorFlow fix
2025-07-09 13:20:50,895 - app.config - INFO - Configuration initialized
2025-07-09 13:20:50,904 - models.train - INFO - TensorFlow version: 2.9.1
2025-07-09 13:20:51,217 - models.train - INFO - TensorFlow test successful
2025-07-09 13:21:02,214 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-07-09 13:21:02,214 - models.train - INFO - Transformer model is available
2025-07-09 13:21:02,215 - models.train - INFO - Using TensorFlow-based models
2025-07-09 13:21:02,223 - models.predict - INFO - Transformer model is available for predictions
2025-07-09 13:21:02,223 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-07-09 13:21:02,229 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-07-09 13:21:04,176 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-09 13:21:04,177 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-07-09 13:21:04,177 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-07-09 13:21:04,177 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-07-09 13:21:04,178 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-07-09 13:21:04,178 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-07-09 13:21:04,178 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-07-09 13:21:04,178 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-07-09 13:21:04,179 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-07-09 13:21:04,179 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-07-09 13:21:04,570 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-07-09 13:21:04,570 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:21:05,131 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-07-09 13:21:07,706 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-07-09 13:21:08,033 - app.utils.session_state - INFO - Initializing session state
2025-07-09 13:21:08,035 - app.utils.session_state - INFO - Session state initialized
2025-07-09 13:21:09,319 - app - INFO - Found 14 stock files in data/stocks
2025-07-09 13:21:09,373 - app.utils.memory_management - INFO - Memory before cleanup: 429.43 MB
2025-07-09 13:21:09,669 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-09 13:21:09,669 - app.utils.memory_management - INFO - Memory after cleanup: 429.44 MB (freed -0.01 MB)
2025-07-09 13:21:25,479 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:21:25,556 - app.utils.memory_management - INFO - Memory before cleanup: 433.56 MB
2025-07-09 13:21:25,915 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-09 13:21:25,915 - app.utils.memory_management - INFO - Memory after cleanup: 433.56 MB (freed 0.00 MB)
2025-07-09 13:21:26,773 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:21:26,940 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.11 seconds
2025-07-09 13:21:26,942 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-07-09 13:21:26,943 - app - INFO - Data shape: (750, 36)
2025-07-09 13:21:26,943 - app - INFO - File COMI contains 2025 data
2025-07-09 13:21:27,019 - app - INFO - Feature engineering for COMI completed in 0.08 seconds
2025-07-09 13:21:27,020 - app - INFO - Features shape: (750, 36)
2025-07-09 13:21:27,049 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-07-09 13:21:27,050 - app - INFO - Date range: 2022-08-16 to 2025-06-30
2025-07-09 13:21:27,051 - app - INFO - Data shape: (750, 36)
2025-07-09 13:21:27,051 - app - INFO - File COMI contains 2025 data
2025-07-09 13:21:27,055 - app.utils.memory_management - INFO - Memory before cleanup: 438.20 MB
2025-07-09 13:21:27,260 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-07-09 13:21:27,261 - app.utils.memory_management - INFO - Memory after cleanup: 438.23 MB (freed -0.04 MB)
2025-07-09 13:21:27,456 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:21:27,579 - app.utils.memory_management - INFO - Memory before cleanup: 439.25 MB
2025-07-09 13:21:27,785 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-07-09 13:21:27,786 - app.utils.memory_management - INFO - Memory after cleanup: 439.25 MB (freed 0.00 MB)
2025-07-09 13:21:39,944 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:21:40,011 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-09 13:21:40,029 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-09 13:21:40,272 - app.utils.memory_management - INFO - Memory before cleanup: 439.61 MB
2025-07-09 13:21:40,565 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-07-09 13:21:40,567 - app.utils.memory_management - INFO - Memory after cleanup: 439.61 MB (freed 0.00 MB)
2025-07-09 13:21:56,495 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:21:56,532 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-09 13:21:56,548 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-09 13:21:56,694 - app.utils.memory_management - INFO - Memory before cleanup: 439.87 MB
2025-07-09 13:21:56,930 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-07-09 13:21:56,933 - app.utils.memory_management - INFO - Memory after cleanup: 439.87 MB (freed 0.00 MB)
2025-07-09 13:22:00,388 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:22:00,420 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-07-09 13:22:00,431 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-07-09 13:22:00,488 - app.utils.historical_data_downloader - INFO - Starting historical data generation for COMI (5 years)
2025-07-09 13:22:00,489 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-07-09 13:22:00,498 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-07-09 13:22:00,499 - app.utils.historical_data_downloader - INFO - Fetching current live data for COMI...
2025-07-09 13:22:00,499 - app.utils.historical_data_downloader - INFO - Downloading historical data for COMI with intervals: ['1D', '1W']
2025-07-09 13:22:00,500 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-07-09 13:22:00,503 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-COMI'], 'intervals': ['1D', '1W']}
2025-07-09 13:23:23,241 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-07-09 13:23:23,243 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-COMI': [{'pair': 'EGX-COMI', 'price': 84000.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Relative Strength Index (14)', 'value': 57355.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic %K (14, 3, 3)', 'value': 91921.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Commodity Channel Index (20)', 'value': 144303.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Average Directional Index (14)', 'value': 10925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Awesome Oscillator', 'value': 2260.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Momentum (10)', 'value': 3030.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'MACD Level (12, 26)', 'value': 969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96984.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Williams Percent Range (14)', 'value': -14286.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Bull Bear Power', 'value': 4461.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 67804.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (10)', 'value': 82064.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (10)', 'value': 81815.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (20)', 'value': 81099.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (20)', 'value': 80330.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (30)', 'value': 80664.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (30)', 'value': 79736.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (50)', 'value': 79545.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (50)', 'value': 81168.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (100)', 'value': 73929.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (100)', 'value': 76838.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (200)', 'value': 62854.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (200)', 'value': 57121.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Volume Weighted Moving Average (20)', 'value': 80252.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Hull Moving Average (9)', 'value': 83791.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]}, 'message': 'Successfully scraped 1 pairs'}
2025-07-09 13:23:23,243 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-COMI']
2025-07-09 13:23:23,243 - app.utils.historical_data_downloader - INFO - Found data for EGX-COMI: [{'pair': 'EGX-COMI', 'price': 84000.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Relative Strength Index (14)', 'value': 57355.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic %K (14, 3, 3)', 'value': 91921.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Commodity Channel Index (20)', 'value': 144303.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Average Directional Index (14)', 'value': 10925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Awesome Oscillator', 'value': 2260.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Momentum (10)', 'value': 3030.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'MACD Level (12, 26)', 'value': 969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96984.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Williams Percent Range (14)', 'value': -14286.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Bull Bear Power', 'value': 4461.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 67804.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (10)', 'value': 82064.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (10)', 'value': 81815.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (20)', 'value': 81099.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (20)', 'value': 80330.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (30)', 'value': 80664.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (30)', 'value': 79736.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (50)', 'value': 79545.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (50)', 'value': 81168.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (100)', 'value': 73929.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (100)', 'value': 76838.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (200)', 'value': 62854.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (200)', 'value': 57121.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Volume Weighted Moving Average (20)', 'value': 80252.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Hull Moving Average (9)', 'value': 83791.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-07-09 13:23:23,243 - app.utils.historical_data_downloader - INFO - Processing 1 data points for COMI
2025-07-09 13:23:23,245 - app.utils.historical_data_downloader - INFO - Raw data structure: [{'pair': 'EGX-COMI', 'price': 84000.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Relative Strength Index (14)', 'value': 57355.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic %K (14, 3, 3)', 'value': 91921.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Commodity Channel Index (20)', 'value': 144303.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Average Directional Index (14)', 'value': 10925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Awesome Oscillator', 'value': 2260.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Momentum (10)', 'value': 3030.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'MACD Level (12, 26)', 'value': 969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96984.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Williams Percent Range (14)', 'value': -14286.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Bull Bear Power', 'value': 4461.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 67804.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (10)', 'value': 82064.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (10)', 'value': 81815.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (20)', 'value': 81099.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (20)', 'value': 80330.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (30)', 'value': 80664.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (30)', 'value': 79736.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (50)', 'value': 79545.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (50)', 'value': 81168.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (100)', 'value': 73929.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (100)', 'value': 76838.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (200)', 'value': 62854.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (200)', 'value': 57121.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Volume Weighted Moving Average (20)', 'value': 80252.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Hull Moving Average (9)', 'value': 83791.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-07-09 13:23:23,245 - app.utils.historical_data_downloader - INFO - First item structure: {'pair': 'EGX-COMI', 'price': 84000.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Relative Strength Index (14)', 'value': 57355.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic %K (14, 3, 3)', 'value': 91921.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Commodity Channel Index (20)', 'value': 144303.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Average Directional Index (14)', 'value': 10925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Awesome Oscillator', 'value': 2260.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Momentum (10)', 'value': 3030.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'MACD Level (12, 26)', 'value': 969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96984.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Williams Percent Range (14)', 'value': -14286.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Bull Bear Power', 'value': 4461.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 67804.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (10)', 'value': 82064.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (10)', 'value': 81815.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (20)', 'value': 81099.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (20)', 'value': 80330.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (30)', 'value': 80664.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (30)', 'value': 79736.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (50)', 'value': 79545.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (50)', 'value': 81168.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (100)', 'value': 73929.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (100)', 'value': 76838.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (200)', 'value': 62854.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (200)', 'value': 57121.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Volume Weighted Moving Average (20)', 'value': 80252.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Hull Moving Average (9)', 'value': 83791.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}
2025-07-09 13:23:23,245 - app.utils.historical_data_downloader - INFO - Processing interval 1D: pair=EGX-COMI, price=84000.0
2025-07-09 13:23:23,245 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 84000.0 -> 84.0 EGP
2025-07-09 13:23:23,247 - app.utils.historical_data_downloader - INFO - Successfully processed 1D: price=84.0 EGP
2025-07-09 13:23:23,247 - app.utils.historical_data_downloader - INFO - Successfully processed 1 intervals for COMI
2025-07-09 13:23:23,247 - app.utils.historical_data_downloader - INFO - Successfully processed historical data for COMI
2025-07-09 13:23:23,247 - app.utils.historical_data_downloader - INFO - Successfully retrieved current data for COMI: {'1D': {'symbol': 'COMI', 'interval': '1D', 'current_price': 84.0, 'timestamp': datetime.datetime(2025, 7, 9, 13, 23, 23, 247248), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Relative Strength Index (14)', 'value': 57355.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic %K (14, 3, 3)', 'value': 91921.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Commodity Channel Index (20)', 'value': 144303.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Average Directional Index (14)', 'value': 10925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Awesome Oscillator', 'value': 2260.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Momentum (10)', 'value': 3030.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'MACD Level (12, 26)', 'value': 969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96984.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Williams Percent Range (14)', 'value': -14286.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Bull Bear Power', 'value': 4461.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 67804.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (10)', 'value': 82064.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (10)', 'value': 81815.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (20)', 'value': 81099.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (20)', 'value': 80330.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (30)', 'value': 80664.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (30)', 'value': 79736.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (50)', 'value': 79545.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (50)', 'value': 81168.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (100)', 'value': 73929.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (100)', 'value': 76838.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (200)', 'value': 62854.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (200)', 'value': 57121.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Volume Weighted Moving Average (20)', 'value': 80252.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Hull Moving Average (9)', 'value': 83791.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 84000.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Relative Strength Index (14)', 'value': 57355.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic %K (14, 3, 3)', 'value': 91921.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Commodity Channel Index (20)', 'value': 144303.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Average Directional Index (14)', 'value': 10925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Awesome Oscillator', 'value': 2260.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Momentum (10)', 'value': 3030.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'MACD Level (12, 26)', 'value': 969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 96984.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Williams Percent Range (14)', 'value': -14286.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Bull Bear Power', 'value': 4461.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 67804.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (10)', 'value': 82064.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (10)', 'value': 81815.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (20)', 'value': 81099.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (20)', 'value': 80330.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (30)', 'value': 80664.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (30)', 'value': 79736.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (50)', 'value': 79545.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (50)', 'value': 81168.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (100)', 'value': 73929.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (100)', 'value': 76838.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Exponential Moving Average (200)', 'value': 62854.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Simple Moving Average (200)', 'value': 57121.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79225.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Volume Weighted Moving Average (20)', 'value': 80252.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'name': 'Hull Moving Average (9)', 'value': 83791.0, 'action': 'Buy'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '09/07/2025 13:23', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}}}
2025-07-09 13:23:23,249 - app.utils.historical_data_downloader - INFO - Found valid price from 1D: 84.0 EGP
2025-07-09 13:23:23,255 - app.utils.historical_data_downloader - INFO - Using current price for COMI: 84.0 EGP
2025-07-09 13:23:23,261 - app.utils.historical_data_downloader - INFO - Generating 5 years of synthetic historical data...
2025-07-09 13:23:23,267 - app.utils.historical_data_downloader - INFO - Generating 5 years of synthetic data for COMI
2025-07-09 13:23:23,358 - app.utils.historical_data_downloader - INFO - Generated 1250 days of synthetic historical data for COMI
2025-07-09 13:23:23,358 - app.utils.historical_data_downloader - INFO - Generated 1250 days of synthetic data
2025-07-09 13:23:23,362 - app.utils.historical_data_downloader - INFO - Saving data to: data\stocks\COMI.csv
2025-07-09 13:23:23,392 - app.utils.historical_data_downloader - INFO - Saved 1250 days of historical data to data\stocks\COMI.csv
2025-07-09 13:23:23,392 - app.utils.historical_data_downloader - INFO - CSV file size: 55138 bytes
2025-07-09 13:23:23,412 - app.utils.historical_data_downloader - INFO - Verification: CSV contains 1250 rows
2025-07-09 13:23:23,412 - app.utils.historical_data_downloader - INFO - Successfully completed historical data generation for COMI
2025-07-09 13:23:23,518 - app.utils.memory_management - INFO - Memory before cleanup: 435.05 MB
2025-07-09 13:23:23,743 - app.utils.memory_management - INFO - Garbage collection: collected 246 objects
2025-07-09 13:23:23,745 - app.utils.memory_management - INFO - Memory after cleanup: 435.06 MB (freed -0.01 MB)
2025-07-09 13:23:34,166 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:23:34,373 - app.utils.memory_management - INFO - Memory before cleanup: 435.67 MB
2025-07-09 13:23:34,805 - app.utils.memory_management - INFO - Garbage collection: collected 260 objects
2025-07-09 13:23:34,807 - app.utils.memory_management - INFO - Memory after cleanup: 435.67 MB (freed 0.00 MB)
2025-07-09 13:23:43,090 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:23:43,193 - app - INFO - File COMI contains 2025 data
2025-07-09 13:23:43,264 - app - INFO - Saved data to data/stocks\COMI.csv
2025-07-09 13:23:43,268 - app - INFO - Date range: 2020-09-24 to 2025-07-09
2025-07-09 13:23:43,272 - app.utils.memory_management - INFO - Memory before cleanup: 436.13 MB
2025-07-09 13:23:43,638 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-07-09 13:23:43,650 - app.utils.memory_management - INFO - Memory after cleanup: 436.13 MB (freed 0.00 MB)
2025-07-09 13:23:46,495 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:23:46,579 - app - INFO - File COMI contains 2025 data
2025-07-09 13:23:46,618 - app - INFO - Saved data to data/stocks\COMI.csv
2025-07-09 13:23:46,645 - app - INFO - Date range: 2020-09-24 to 2025-07-09
2025-07-09 13:23:46,656 - app.utils.memory_management - INFO - Memory before cleanup: 436.14 MB
2025-07-09 13:23:46,974 - app.utils.memory_management - INFO - Garbage collection: collected 251 objects
2025-07-09 13:23:46,974 - app.utils.memory_management - INFO - Memory after cleanup: 436.14 MB (freed 0.00 MB)
2025-07-09 13:23:51,112 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:23:51,382 - app - INFO - File COMI contains 2025 data
2025-07-09 13:23:51,397 - app - INFO - Saved data to data/stocks\COMI.csv
2025-07-09 13:23:51,401 - app - INFO - Date range: 2020-09-24 to 2025-07-09
2025-07-09 13:23:51,404 - app.utils.memory_management - INFO - Memory before cleanup: 436.23 MB
2025-07-09 13:23:51,632 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-07-09 13:23:51,632 - app.utils.memory_management - INFO - Memory after cleanup: 436.23 MB (freed 0.00 MB)
2025-07-09 13:23:52,786 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:23:52,995 - app - INFO - File COMI contains 2025 data
2025-07-09 13:23:53,031 - app - INFO - Saved data to data/stocks\COMI.csv
2025-07-09 13:23:53,035 - app - INFO - Date range: 2020-09-24 to 2025-07-09
2025-07-09 13:23:53,043 - app.utils.memory_management - INFO - Memory before cleanup: 436.23 MB
2025-07-09 13:23:53,746 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-07-09 13:23:54,068 - app.utils.memory_management - INFO - Memory after cleanup: 436.23 MB (freed 0.00 MB)
2025-07-09 13:23:56,580 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:23:56,941 - app - INFO - File COMI contains 2025 data
2025-07-09 13:23:57,084 - app - INFO - Saved data to data/stocks\COMI.csv
2025-07-09 13:23:57,092 - app - INFO - Date range: 2020-09-24 to 2025-07-09
2025-07-09 13:23:57,108 - app.utils.memory_management - INFO - Memory before cleanup: 438.17 MB
2025-07-09 13:23:57,532 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-07-09 13:23:57,533 - app.utils.memory_management - INFO - Memory after cleanup: 437.18 MB (freed 0.99 MB)
2025-07-09 13:24:01,389 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:24:01,594 - app - INFO - File COMI contains 2025 data
2025-07-09 13:24:01,880 - app - INFO - Saved data to data/stocks\COMI.csv
2025-07-09 13:24:01,884 - app - INFO - Date range: 2020-09-24 to 2025-07-09
2025-07-09 13:24:01,893 - app.utils.memory_management - INFO - Memory before cleanup: 438.94 MB
2025-07-09 13:24:02,242 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-07-09 13:24:02,246 - app.utils.memory_management - INFO - Memory after cleanup: 437.95 MB (freed 0.99 MB)
2025-07-09 13:24:11,188 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:24:11,333 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-09 13:24:11,337 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:11,341 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:24:11,355 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:11,363 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:24:11,365 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-09 13:24:11,374 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-09 13:24:11,400 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:24:11,410 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 13:24:11,444 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:24:11,515 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 13:24:11,640 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-09 13:24:11,666 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-07-09 13:24:11,668 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:24:11,668 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 13:24:11,670 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:24:11,675 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 13:24:11,678 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-09 13:24:11,679 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-07-09 13:24:11,679 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:24:11,680 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 13:24:11,680 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:24:11,680 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 13:24:11,681 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-09 13:24:11,682 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-09 13:24:11,682 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:11,686 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:24:11,690 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:11,690 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:24:11,690 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-09 13:24:11,728 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:11,729 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:11,730 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:24:11,759 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:24:11,780 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:24:11,824 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:24:11,830 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:11,830 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:24:11,832 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:11,832 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:24:11,832 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 13:24:11,860 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:24:11,860 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 13:24:11,883 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:24:11,906 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 13:24:11,914 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 13:24:11,916 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:24:11,920 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 13:24:11,925 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:24:11,927 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 13:24:11,933 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-09 13:24:11,937 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:24:11,941 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 13:24:11,943 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:24:11,946 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 13:24:11,948 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:24:11,948 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:11,948 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:24:11,949 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:11,949 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:24:11,982 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:12,002 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:24:12,007 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:24:12,009 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:24:12,015 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:12,098 - app.utils.memory_management - INFO - Memory before cleanup: 438.01 MB
2025-07-09 13:24:12,387 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-07-09 13:24:12,396 - app.utils.memory_management - INFO - Memory after cleanup: 438.01 MB (freed 0.00 MB)
2025-07-09 13:24:17,054 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:24:17,151 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:17,153 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:17,154 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:24:17,177 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:24:17,205 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:24:17,253 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:24:17,258 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:17,260 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:24:17,261 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:17,263 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:24:17,267 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 13:24:17,281 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:24:17,282 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 13:24:17,301 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:24:17,341 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 13:24:17,344 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 13:24:17,345 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:24:17,347 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 13:24:17,347 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:24:17,348 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 13:24:17,349 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-09 13:24:17,350 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:24:17,350 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 13:24:17,351 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:24:17,352 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 13:24:17,353 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:24:17,355 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:17,355 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:24:17,358 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:17,359 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:24:17,371 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:17,419 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:24:17,420 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:24:17,421 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:24:17,422 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:24:17,460 - app.utils.memory_management - INFO - Memory before cleanup: 438.02 MB
2025-07-09 13:24:18,103 - app.utils.memory_management - INFO - Garbage collection: collected 269 objects
2025-07-09 13:24:18,111 - app.utils.memory_management - INFO - Memory after cleanup: 438.02 MB (freed 0.00 MB)
2025-07-09 13:24:19,539 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:24:19,610 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:19,623 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:24:19,627 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:19,634 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:24:19,635 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:24:19,636 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-09 13:24:19,638 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:19,638 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:24:19,639 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:24:19,651 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:24:19,652 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:24:19,652 - app.pages.predictions_consolidated - INFO - CONSISTENT auto mode selected: ensemble from ['ensemble', 'rf', 'gb', 'lstm', 'lr']
2025-07-09 13:24:19,653 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:25:14,558 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=False
2025-07-09 13:25:14,615 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:25:14,840 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:25:15,014 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-09 13:25:15,014 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-09 13:25:15,106 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-09 13:25:15,106 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-09 13:25:15,106 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-09 13:25:15,106 - models.predict - INFO - Ensemble model already loaded
2025-07-09 13:25:15,133 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:25:15,133 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.8812860044032118
2025-07-09 13:25:15,133 - models.predict - INFO - Prediction for 60 minutes horizon: 79.50444248876045
2025-07-09 13:25:15,133 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 79.50 vs current 84.00 (5.4% change, limit: 2.0%). Applying correction.
2025-07-09 13:25:15,133 - app.pages.predictions_consolidated - INFO - Corrected prediction: 79.50 -> 82.82 (change: -1.4%)
2025-07-09 13:25:15,150 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 79.50 -> 82.82 for 60min
2025-07-09 13:25:15,156 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:25:15,158 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:25:15,160 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:25:15,174 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:25:15,175 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:25:15,580 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:25:15,580 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:25:15,580 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:25:15,601 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:25:15,603 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:25:15,621 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:25:15,622 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:25:15,623 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:25:15,624 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:25:15,624 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:25:15,625 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 13:25:15,647 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:25:15,649 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 13:25:15,662 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:25:15,680 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 13:25:15,681 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 13:25:15,681 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:25:15,681 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 13:25:15,683 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:25:15,683 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 13:25:15,685 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-09 13:25:15,685 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:25:15,685 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 13:25:15,685 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:25:15,687 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 13:25:15,687 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:25:15,687 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:25:15,689 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:25:15,689 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:25:15,689 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:25:15,705 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:25:15,717 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:25:15,718 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:25:15,718 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:25:15,718 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:25:15,728 - app.utils.memory_management - INFO - Memory before cleanup: 440.49 MB
2025-07-09 13:25:15,934 - app.utils.memory_management - INFO - Garbage collection: collected 205 objects
2025-07-09 13:25:15,936 - app.utils.memory_management - INFO - Memory after cleanup: 440.49 MB (freed 0.00 MB)
2025-07-09 13:26:14,257 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:26:14,367 - app - INFO - Found 14 stock files in data/stocks
2025-07-09 13:26:14,542 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview api
2025-07-09 13:26:15,056 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:26:15,058 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:26:15,058 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:26:15,058 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:26:15,064 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 13:26:15,066 - app.utils.error_handling - INFO - live_trading_component executed in 0.68 seconds
2025-07-09 13:26:15,066 - app.utils.memory_management - INFO - Memory before cleanup: 443.16 MB
2025-07-09 13:26:15,424 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-07-09 13:26:15,424 - app.utils.memory_management - INFO - Memory after cleanup: 443.16 MB (freed 0.00 MB)
2025-07-09 13:26:19,659 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:26:48,294 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:26:48,295 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:26:48,296 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:26:48,298 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:26:48,310 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 13:26:48,313 - app.utils.error_handling - INFO - live_trading_component executed in 28.64 seconds
2025-07-09 13:26:48,314 - app.utils.memory_management - INFO - Memory before cleanup: 443.81 MB
2025-07-09 13:26:48,737 - app.utils.memory_management - INFO - Garbage collection: collected 216 objects
2025-07-09 13:26:48,737 - app.utils.memory_management - INFO - Memory after cleanup: 443.81 MB (freed 0.00 MB)
2025-07-09 13:27:15,854 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:27:15,969 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:15,989 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:15,992 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:27:16,014 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:27:16,106 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:27:16,197 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:27:16,470 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:27:16,476 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:27:16,486 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:16,495 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:27:16,501 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 13:27:16,516 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:27:16,523 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 13:27:16,539 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:27:16,554 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 13:27:16,555 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 13:27:16,556 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:27:16,557 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 13:27:16,559 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:27:16,559 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 13:27:16,559 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-09 13:27:16,561 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:27:16,561 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 13:27:16,561 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:27:16,567 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 13:27:16,568 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:27:16,568 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:27:16,569 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:27:16,569 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:16,570 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:27:16,576 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:27:16,595 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:27:16,600 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:27:16,601 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:27:16,602 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:27:16,641 - app.utils.memory_management - INFO - Memory before cleanup: 443.79 MB
2025-07-09 13:27:16,860 - app.utils.memory_management - INFO - Garbage collection: collected 221 objects
2025-07-09 13:27:16,863 - app.utils.memory_management - INFO - Memory after cleanup: 443.79 MB (freed 0.00 MB)
2025-07-09 13:27:28,021 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:27:28,174 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:28,176 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:28,178 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:27:28,195 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:27:28,211 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:27:28,274 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:27:28,277 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:27:28,277 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:27:28,278 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:28,278 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:27:28,279 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 13:27:28,288 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:27:28,289 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 13:27:28,313 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:27:28,322 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 13:27:28,322 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 13:27:28,324 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:27:28,328 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 13:27:28,329 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:27:28,329 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 13:27:28,330 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-09 13:27:28,330 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:27:28,332 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 13:27:28,333 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:27:28,333 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 13:27:28,334 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:27:28,334 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:27:28,334 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:27:28,335 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:28,335 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:27:28,350 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:28,366 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:27:28,369 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:27:28,369 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:27:28,370 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:28,382 - app.utils.memory_management - INFO - Memory before cleanup: 443.79 MB
2025-07-09 13:27:28,669 - app.utils.memory_management - INFO - Garbage collection: collected 267 objects
2025-07-09 13:27:28,669 - app.utils.memory_management - INFO - Memory after cleanup: 443.79 MB (freed 0.00 MB)
2025-07-09 13:27:30,488 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:27:30,549 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:30,549 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:30,556 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:27:30,568 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:27:30,568 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:27:30,583 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:27:30,584 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:27:30,585 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:27:30,585 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:30,587 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:27:30,588 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 13:27:30,604 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 13:27:30,604 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 13:27:30,619 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:27:30,632 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 13:27:30,632 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 13:27:30,634 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 13:27:30,634 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 13:27:30,634 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:27:30,636 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 13:27:30,636 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-09 13:27:30,639 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 13:27:30,639 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 13:27:30,641 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:27:30,641 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 13:27:30,641 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 13:27:30,643 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 13:27:30,643 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:27:30,645 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:30,646 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 13:27:30,658 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:30,691 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:27:30,695 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:27:30,697 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:27:30,699 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:27:30,705 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:28:29,471 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=rf, live_data=False
2025-07-09 13:28:29,525 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:28:29,719 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:28:29,868 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:28:29,868 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-09 13:28:29,893 - models.hybrid_model - INFO - XGBoost is available
2025-07-09 13:28:29,893 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-07-09 13:28:29,895 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-09 13:28:29,895 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-09 13:28:29,897 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-09 13:28:29,956 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:28:29,956 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:28:29,956 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:28:29,961 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:28:29,963 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7442987191677094
2025-07-09 13:28:29,963 - models.predict - INFO - Prediction for 60 minutes horizon: 74.25919852657152
2025-07-09 13:28:29,963 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 74.26 vs current 84.00 (11.6% change, limit: 2.0%). Applying correction.
2025-07-09 13:28:29,968 - app.pages.predictions_consolidated - INFO - Corrected prediction: 74.26 -> 82.82 (change: -1.4%)
2025-07-09 13:28:29,968 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 74.26 -> 82.82 for 60min
2025-07-09 13:28:29,981 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:29:10,870 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lstm, live_data=False
2025-07-09 13:29:10,964 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:29:11,162 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:29:11,320 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:29:11,322 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-09 13:29:11,330 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-09 13:29:12,962 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:29:15,164 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:29:15,164 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 1.2289073467254639
2025-07-09 13:29:15,164 - models.predict - INFO - Prediction for 60 minutes horizon: 92.81486574307318
2025-07-09 13:29:15,164 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 92.81 vs current 84.00 (10.5% change, limit: 2.0%). Applying correction.
2025-07-09 13:29:15,164 - app.pages.predictions_consolidated - INFO - Corrected prediction: 92.81 -> 85.18 (change: 1.4%)
2025-07-09 13:29:15,164 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 92.81 -> 85.18 for 60min
2025-07-09 13:29:15,171 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 13:29:44,837 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=gb, live_data=True
2025-07-09 13:29:44,885 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:29:45,143 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:29:45,324 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:29:45,324 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-09 13:29:45,324 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-09 13:29:45,324 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-07-09 13:29:45,324 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-07-09 13:29:45,368 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:29:45,375 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:29:45,375 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:29:45,375 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:29:45,392 - models.predict - INFO - Current price: 83.91, Predicted scaled value: 0.7879988620210531
2025-07-09 13:29:45,392 - models.predict - INFO - Prediction for 60 minutes horizon: 75.93247725498952
2025-07-09 13:29:45,395 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 75.93 vs current 83.91 (9.5% change, limit: 2.0%). Applying correction.
2025-07-09 13:29:45,395 - app.pages.predictions_consolidated - INFO - Corrected prediction: 75.93 -> 82.74 (change: -1.4%)
2025-07-09 13:29:45,399 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 75.93 -> 82.74 for 60min
2025-07-09 13:29:45,399 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 13:30:26,312 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=lr, live_data=True
2025-07-09 13:30:26,378 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:30:26,648 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:30:26,807 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:30:26,807 - models.predict - INFO - Using scikit-learn lr model for 60 minutes horizon
2025-07-09 13:30:26,807 - models.predict - INFO - Loading lr model for COMI with horizon 60
2025-07-09 13:30:26,807 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_60min.joblib
2025-07-09 13:30:26,807 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_60min.joblib
2025-07-09 13:30:26,830 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:30:26,831 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-07-09 13:30:26,831 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:30:26,831 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:30:26,831 - models.predict - INFO - Current price: 83.91, Predicted scaled value: 1.1767246453758573
2025-07-09 13:30:26,831 - models.predict - INFO - Prediction for 60 minutes horizon: 90.81678979964398
2025-07-09 13:30:26,831 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 90.82 vs current 83.91 (8.2% change, limit: 2.0%). Applying correction.
2025-07-09 13:30:26,831 - app.pages.predictions_consolidated - INFO - Corrected prediction: 90.82 -> 85.08 (change: 1.4%)
2025-07-09 13:30:26,831 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 90.82 -> 85.08 for 60min
2025-07-09 13:30:26,831 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:31:03,339 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=60, model=ensemble, live_data=False
2025-07-09 13:31:03,382 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:31:03,685 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:31:03,832 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:31:03,832 - models.predict - INFO - Using RobustEnsembleModel for 60 minutes horizon
2025-07-09 13:31:03,877 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_60min.joblib
2025-07-09 13:31:03,877 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 60
2025-07-09 13:31:03,877 - models.predict - INFO - Loading ensemble model for COMI with horizon 60
2025-07-09 13:31:03,879 - models.predict - INFO - Ensemble model already loaded
2025-07-09 13:31:03,905 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:31:03,907 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.8812860044032118
2025-07-09 13:31:03,907 - models.predict - INFO - Prediction for 60 minutes horizon: 79.50444248876045
2025-07-09 13:31:03,907 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 79.50 vs current 84.00 (5.4% change, limit: 2.0%). Applying correction.
2025-07-09 13:31:03,909 - app.pages.predictions_consolidated - INFO - Corrected prediction: 79.50 -> 82.82 (change: -1.4%)
2025-07-09 13:31:03,909 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 79.50 -> 82.82 for 60min
2025-07-09 13:31:04,009 - app.utils.memory_management - INFO - Memory before cleanup: 475.17 MB
2025-07-09 13:31:04,221 - app.utils.memory_management - INFO - Garbage collection: collected 36 objects
2025-07-09 13:31:04,223 - app.utils.memory_management - INFO - Memory after cleanup: 475.17 MB (freed 0.00 MB)
2025-07-09 13:31:25,867 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:31:26,169 - app - INFO - Found 14 stock files in data/stocks
2025-07-09 13:31:26,348 - app.utils.memory_management - INFO - Memory before cleanup: 475.59 MB
2025-07-09 13:31:26,660 - app.utils.memory_management - INFO - Garbage collection: collected 315 objects
2025-07-09 13:31:26,661 - app.utils.memory_management - INFO - Memory after cleanup: 475.59 MB (freed 0.00 MB)
2025-07-09 13:31:31,589 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:31:31,716 - app.utils.memory_management - INFO - Memory before cleanup: 475.57 MB
2025-07-09 13:31:32,138 - app.utils.memory_management - INFO - Garbage collection: collected 233 objects
2025-07-09 13:31:32,175 - app.utils.memory_management - INFO - Memory after cleanup: 475.57 MB (freed 0.00 MB)
2025-07-09 13:31:32,449 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:32:11,448 - app.pages.ai_advisor - WARNING - Advanced prediction module not available, using fallback
2025-07-09 13:32:11,450 - app.pages.ai_advisor - INFO - Generating hybrid predictions for COMI
2025-07-09 13:32:11,450 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-07-09 13:32:11,450 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-07-09 13:32:11,450 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-09 13:32:11,556 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-09 13:32:11,716 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-09 13:32:11,924 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-09 13:32:11,924 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-09 13:32:11,924 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-09 13:32:11,924 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-07-09 13:32:11,924 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-07-09 13:32:11,983 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-09 13:32:11,983 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:32:11,983 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:32:11,983 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:32:11,983 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7442987191677094
2025-07-09 13:32:12,000 - models.predict - INFO - Prediction for 30 minutes horizon: 74.25919852657152
2025-07-09 13:32:12,083 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-09 13:32:12,249 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-09 13:32:12,418 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:12,418 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-07-09 13:32:12,418 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-07-09 13:32:12,418 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-07-09 13:32:12,418 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-07-09 13:32:12,453 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-09 13:32:12,468 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:32:12,468 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:32:12,468 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:32:12,468 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7900242041484784
2025-07-09 13:32:12,468 - models.predict - INFO - Prediction for 30 minutes horizon: 76.01002761703211
2025-07-09 13:32:12,562 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-09 13:32:12,769 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-09 13:32:12,958 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:12,960 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-09 13:32:12,960 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-07-09 13:32:13,946 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-09 13:32:15,244 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:15,253 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.8410308361053467
2025-07-09 13:32:15,253 - models.predict - INFO - Prediction for 30 minutes horizon: 77.96307185645483
2025-07-09 13:32:15,253 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 76.07743266668615
2025-07-09 13:32:15,253 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-07-09 13:32:15,253 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-09 13:32:15,340 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:32:15,546 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:32:15,741 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:15,741 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-09 13:32:15,741 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-09 13:32:15,741 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-09 13:32:15,741 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-09 13:32:15,777 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:32:15,777 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:32:15,777 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:32:15,783 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:32:15,783 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7442987191677094
2025-07-09 13:32:15,783 - models.predict - INFO - Prediction for 60 minutes horizon: 74.25919852657152
2025-07-09 13:32:15,882 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:32:16,055 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:32:16,238 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:16,238 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-07-09 13:32:16,238 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-07-09 13:32:16,238 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-07-09 13:32:16,238 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-07-09 13:32:16,238 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:32:16,277 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:32:16,277 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:32:16,277 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:32:16,277 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7900242041484784
2025-07-09 13:32:16,279 - models.predict - INFO - Prediction for 60 minutes horizon: 76.01002761703211
2025-07-09 13:32:16,373 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:32:16,554 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:32:16,720 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-09 13:32:16,720 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-09 13:32:16,736 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-09 13:32:17,639 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:32:18,923 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:18,923 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 1.2289073467254639
2025-07-09 13:32:18,923 - models.predict - INFO - Prediction for 60 minutes horizon: 92.81486574307318
2025-07-09 13:32:18,923 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 81.02803062889227
2025-07-09 13:32:18,923 - app.models.adaptive - INFO - No valid models for COMI with 240min horizon, using equal weights
2025-07-09 13:32:18,923 - app.models.predict - INFO - Ensemble weights for COMI with 240min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-09 13:32:19,013 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-09 13:32:19,211 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-09 13:32:19,458 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.05 MB, VMS: 0.08 MB, Percent: 0.00%, Execution time: 0.08s
2025-07-09 13:32:19,458 - models.predict - INFO - Using scikit-learn rf model for 240 minutes horizon
2025-07-09 13:32:19,458 - models.predict - INFO - Loading rf model for COMI with horizon 240
2025-07-09 13:32:19,458 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_240min.joblib
2025-07-09 13:32:19,458 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_240min.joblib, searching for alternatives...
2025-07-09 13:32:19,458 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-07-09 13:32:19,458 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-07-09 13:32:19,465 - models.predict - WARNING - Model file not found or import error for rf with horizon 240: No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-07-09 13:32:19,465 - models.predict - INFO - Skipping rf model for horizon 240 - model not trained for this horizon
2025-07-09 13:32:19,573 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-09 13:32:19,740 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-09 13:32:19,909 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:19,909 - models.predict - INFO - Using scikit-learn gb model for 240 minutes horizon
2025-07-09 13:32:19,909 - models.predict - INFO - Loading gb model for COMI with horizon 240
2025-07-09 13:32:19,909 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_240min.joblib
2025-07-09 13:32:19,909 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_240min.joblib, searching for alternatives...
2025-07-09 13:32:19,909 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-07-09 13:32:19,909 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-07-09 13:32:19,909 - models.predict - WARNING - Model file not found or import error for gb with horizon 240: No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-07-09 13:32:19,909 - models.predict - INFO - Skipping gb model for horizon 240 - model not trained for this horizon
2025-07-09 13:32:20,011 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-09 13:32:20,191 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-09 13:32:20,374 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:20,374 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-07-09 13:32:20,374 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-07-09 13:32:20,374 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-07-09 13:32:20,374 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-07-09 13:32:20,374 - app.models.predict - WARNING - No models have predictions for 240min horizon, using original ensemble
2025-07-09 13:32:20,485 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-09 13:32:20,641 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-09 13:32:20,821 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.02s
2025-07-09 13:32:20,825 - models.predict - INFO - Using RobustEnsembleModel for 240 minutes horizon
2025-07-09 13:32:20,825 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_240min.joblib
2025-07-09 13:32:20,825 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 240
2025-07-09 13:32:20,827 - models.predict - INFO - Created fallback ensemble model with base price: 84.0
2025-07-09 13:32:20,827 - models.predict - INFO - Loading ensemble model for COMI with horizon 240
2025-07-09 13:32:20,827 - models.predict - INFO - Ensemble model already loaded
2025-07-09 13:32:20,845 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-07-09 13:32:20,847 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 88.93401478827533
2025-07-09 13:32:20,849 - models.predict - WARNING - Prediction 4915.283738524104 is too far from current price 84.0, using fallback
2025-07-09 13:32:20,849 - models.predict - INFO - Prediction for 240 minutes horizon: 82.36570026013933
2025-07-09 13:32:20,851 - app.models.adaptive - INFO - No valid models for COMI with 1440min horizon, using equal weights
2025-07-09 13:32:20,851 - app.models.predict - INFO - Ensemble weights for COMI with 1440min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-09 13:32:20,927 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-09 13:32:21,075 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-09 13:32:21,276 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-07-09 13:32:21,276 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-07-09 13:32:21,276 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-07-09 13:32:21,276 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_1440min.joblib
2025-07-09 13:32:21,290 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_1440min.joblib
2025-07-09 13:32:21,365 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-09 13:32:21,365 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:32:21,365 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:32:21,365 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:32:21,365 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7699124312400818
2025-07-09 13:32:21,365 - models.predict - INFO - Prediction for 1440 minutes horizon: 83.8911993336678
2025-07-09 13:32:21,473 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-09 13:32:21,633 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-09 13:32:21,792 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:21,792 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-07-09 13:32:21,792 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-07-09 13:32:21,792 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_1440min.joblib
2025-07-09 13:32:21,792 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_1440min.joblib
2025-07-09 13:32:21,836 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-09 13:32:21,844 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:32:21,844 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:32:21,844 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:32:21,857 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7785909119505469
2025-07-09 13:32:21,861 - models.predict - INFO - Prediction for 1440 minutes horizon: 84.36678005002614
2025-07-09 13:32:21,925 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-09 13:32:22,127 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-09 13:32:22,291 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:22,293 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-09 13:32:22,295 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-07-09 13:32:23,193 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-09 13:32:24,426 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:24,428 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7276133894920349
2025-07-09 13:32:24,430 - models.predict - INFO - Prediction for 1440 minutes horizon: 81.57321197540246
2025-07-09 13:32:24,432 - app.models.predict - INFO - Adaptive ensemble prediction for 1440min horizon: 83.27706378636546
2025-07-09 13:32:24,434 - app.models.adaptive - INFO - No valid models for COMI with 4320min horizon, using equal weights
2025-07-09 13:32:24,434 - app.models.predict - INFO - Ensemble weights for COMI with 4320min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-07-09 13:32:24,514 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-09 13:32:24,745 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-09 13:32:24,926 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.05 MB, VMS: 0.08 MB, Percent: 0.00%, Execution time: 0.04s
2025-07-09 13:32:24,929 - models.predict - INFO - Using scikit-learn rf model for 4320 minutes horizon
2025-07-09 13:32:24,929 - models.predict - INFO - Loading rf model for COMI with horizon 4320
2025-07-09 13:32:24,929 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4320min.joblib
2025-07-09 13:32:24,929 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4320min.joblib
2025-07-09 13:32:24,981 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-09 13:32:24,994 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:32:24,996 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:32:24,996 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:32:24,996 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7689033222198486
2025-07-09 13:32:24,996 - models.predict - INFO - Prediction for 4320 minutes horizon: 83.8359001624491
2025-07-09 13:32:25,097 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-09 13:32:25,264 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-09 13:32:25,412 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:25,412 - models.predict - INFO - Using scikit-learn gb model for 4320 minutes horizon
2025-07-09 13:32:25,412 - models.predict - INFO - Loading gb model for COMI with horizon 4320
2025-07-09 13:32:25,412 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_4320min.joblib
2025-07-09 13:32:25,429 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_4320min.joblib
2025-07-09 13:32:25,464 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-09 13:32:25,482 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:32:25,482 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:32:25,482 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:32:25,482 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7778819042517496
2025-07-09 13:32:25,482 - models.predict - INFO - Prediction for 4320 minutes horizon: 84.32792643030317
2025-07-09 13:32:25,567 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-09 13:32:25,760 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-09 13:32:25,916 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:25,918 - models.predict - INFO - Loading lstm model for COMI with horizon 4320
2025-07-09 13:32:25,920 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_4320min.keras
2025-07-09 13:32:26,802 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-09 13:32:28,048 - tensorflow - WARNING - 5 out of the last 5 calls to <function Model.make_predict_function.<locals>.predict_function at 0x000001ED7D4175B0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-07-09 13:32:28,049 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:28,049 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7374230623245239
2025-07-09 13:32:28,049 - models.predict - INFO - Prediction for 4320 minutes horizon: 82.1107820165838
2025-07-09 13:32:28,049 - app.models.predict - INFO - Adaptive ensemble prediction for 4320min horizon: 83.42486953644536
2025-07-09 13:32:28,058 - app.models.predict - INFO - Prediction completed in 16.61 seconds
2025-07-09 13:32:28,058 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-07-09 13:32:28,058 - app.models.predict - INFO - Using specified model type: lstm
2025-07-09 13:32:28,138 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-09 13:32:28,350 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-09 13:32:28,519 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:28,522 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-09 13:32:28,522 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-07-09 13:32:29,523 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-09 13:32:30,761 - tensorflow - WARNING - 6 out of the last 6 calls to <function Model.make_predict_function.<locals>.predict_function at 0x000001ED7D416710> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-07-09 13:32:30,768 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:30,768 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.8410308361053467
2025-07-09 13:32:30,769 - models.predict - INFO - Prediction for 30 minutes horizon: 77.96307185645483
2025-07-09 13:32:30,770 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:32:30,935 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:32:31,117 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:31,117 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-09 13:32:31,117 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-09 13:32:31,918 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:32:33,146 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:33,146 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 1.2289073467254639
2025-07-09 13:32:33,148 - models.predict - INFO - Prediction for 60 minutes horizon: 92.81486574307318
2025-07-09 13:32:33,157 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-09 13:32:33,369 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-09 13:32:33,605 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:33,611 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-07-09 13:32:33,615 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-07-09 13:32:33,615 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-07-09 13:32:33,626 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-07-09 13:32:33,634 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-09 13:32:33,997 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-09 13:32:34,294 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-09 13:32:34,296 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-09 13:32:34,298 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-07-09 13:32:35,967 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-09 13:32:38,685 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:38,717 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7276133894920349
2025-07-09 13:32:38,738 - models.predict - INFO - Prediction for 1440 minutes horizon: 81.57321197540246
2025-07-09 13:32:38,755 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-09 13:32:39,153 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-09 13:32:39,448 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:39,452 - models.predict - INFO - Loading lstm model for COMI with horizon 4320
2025-07-09 13:32:39,458 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_4320min.keras
2025-07-09 13:32:41,074 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-09 13:32:42,600 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:42,600 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7374230623245239
2025-07-09 13:32:42,600 - models.predict - INFO - Prediction for 4320 minutes horizon: 82.1107820165838
2025-07-09 13:32:42,604 - app.models.predict - INFO - Prediction completed in 14.55 seconds
2025-07-09 13:32:42,604 - app.models.predict - INFO - Using specified model type: bilstm
2025-07-09 13:32:42,687 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-09 13:32:42,900 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-09 13:32:43,097 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:43,098 - models.predict - INFO - Loading bilstm model for COMI with horizon 30
2025-07-09 13:32:43,100 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_30min.keras
2025-07-09 13:32:44,856 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-09 13:32:47,244 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:47,244 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 1.1254525184631348
2025-07-09 13:32:47,244 - models.predict - INFO - Prediction for 30 minutes horizon: 88.85357975679072
2025-07-09 13:32:47,246 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:32:47,488 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:32:47,650 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:32:47,650 - models.predict - INFO - Loading bilstm model for COMI with horizon 60
2025-07-09 13:32:47,650 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_60min.keras
2025-07-09 13:32:50,927 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:32:56,214 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:32:56,216 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 1.1600416898727417
2025-07-09 13:32:56,218 - models.predict - INFO - Prediction for 60 minutes horizon: 90.17799933472057
2025-07-09 13:32:56,224 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-07-09 13:32:56,679 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-07-09 13:32:56,998 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.07 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-09 13:32:57,002 - models.predict - INFO - Loading bilstm model for COMI with horizon 240
2025-07-09 13:32:57,006 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-07-09 13:32:57,012 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 240: Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-07-09 13:32:57,020 - models.predict - INFO - Skipping bilstm model for horizon 240 - model not trained for this horizon
2025-07-09 13:32:57,024 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-09 13:32:57,457 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-09 13:32:57,738 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-09 13:32:57,749 - models.predict - INFO - Loading bilstm model for COMI with horizon 1440
2025-07-09 13:32:57,755 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_1440min.keras
2025-07-09 13:32:59,595 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-09 13:33:02,440 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:33:02,442 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7677649259567261
2025-07-09 13:33:02,444 - models.predict - INFO - Prediction for 1440 minutes horizon: 83.77351605071597
2025-07-09 13:33:02,444 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-07-09 13:33:02,811 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-07-09 13:33:03,007 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-09 13:33:03,007 - models.predict - INFO - Loading bilstm model for COMI with horizon 4320
2025-07-09 13:33:03,010 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_4320min.keras
2025-07-09 13:33:04,986 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-07-09 13:33:07,677 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:33:07,679 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7575132846832275
2025-07-09 13:33:07,679 - models.predict - INFO - Prediction for 4320 minutes horizon: 83.2117261403207
2025-07-09 13:33:07,683 - app.models.predict - INFO - Prediction completed in 25.08 seconds
2025-07-09 13:33:07,683 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-07-09 13:33:07,972 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-09 13:33:08,065 - cmdstanpy - DEBUG - Adding TBB (D:\AI Stocks Bot\python310_venv\lib\site-packages\prophet\stan_model\cmdstan-2.33.1\stan\lib\stan_math\lib\tbb) to PATH
2025-07-09 13:33:08,088 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-09 13:33:08,129 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\vkojuv5e.json
2025-07-09 13:33:08,234 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\9a72ak3j.json
2025-07-09 13:33:08,241 - cmdstanpy - DEBUG - idx 0
2025-07-09 13:33:08,241 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-09 13:33:08,246 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=9380', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\vkojuv5e.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\9a72ak3j.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\prophet_modelz4eq7i7j\\prophet_model-20250709133308.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-09 13:33:08,246 - cmdstanpy - INFO - Chain [1] start processing
2025-07-09 13:33:10,781 - cmdstanpy - INFO - Chain [1] done processing
2025-07-09 13:33:10,848 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-09 13:33:10,931 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-09 13:33:10,944 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-09 13:33:10,965 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\wt56czl7.json
2025-07-09 13:33:11,084 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\8ffxqpf2.json
2025-07-09 13:33:11,089 - cmdstanpy - DEBUG - idx 0
2025-07-09 13:33:11,104 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-09 13:33:11,116 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=86709', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\wt56czl7.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\8ffxqpf2.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\prophet_modelnl_imqn9\\prophet_model-20250709133311.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-09 13:33:11,120 - cmdstanpy - INFO - Chain [1] start processing
2025-07-09 13:33:13,066 - cmdstanpy - INFO - Chain [1] done processing
2025-07-09 13:33:13,151 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-09 13:33:13,438 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-09 13:33:13,453 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-09 13:33:13,486 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\th4fyizp.json
2025-07-09 13:33:13,655 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\dn1nsicb.json
2025-07-09 13:33:13,662 - cmdstanpy - DEBUG - idx 0
2025-07-09 13:33:13,666 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-09 13:33:13,668 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=21771', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\th4fyizp.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\dn1nsicb.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\prophet_modelx4an7i_v\\prophet_model-20250709133313.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-09 13:33:13,678 - cmdstanpy - INFO - Chain [1] start processing
2025-07-09 13:33:16,420 - cmdstanpy - INFO - Chain [1] done processing
2025-07-09 13:33:16,615 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-09 13:33:17,057 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-09 13:33:17,082 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-09 13:33:17,124 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\2_wxmxal.json
2025-07-09 13:33:17,317 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\3qiab7n9.json
2025-07-09 13:33:17,325 - cmdstanpy - DEBUG - idx 0
2025-07-09 13:33:17,332 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-09 13:33:17,336 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=42601', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\2_wxmxal.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\3qiab7n9.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\prophet_model4s44qze9\\prophet_model-20250709133317.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-09 13:33:17,342 - cmdstanpy - INFO - Chain [1] start processing
2025-07-09 13:33:20,837 - cmdstanpy - INFO - Chain [1] done processing
2025-07-09 13:33:21,012 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-07-09 13:33:21,328 - cmdstanpy - DEBUG - TBB already found in load path
2025-07-09 13:33:21,356 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-07-09 13:33:21,478 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\tbn0o7x3.json
2025-07-09 13:33:21,824 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmprizcdlnv\ds12929s.json
2025-07-09 13:33:21,841 - cmdstanpy - DEBUG - idx 0
2025-07-09 13:33:21,845 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-07-09 13:33:21,847 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=11841', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\tbn0o7x3.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\ds12929s.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprizcdlnv\\prophet_modelatlofxuc\\prophet_model-20250709133321.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-07-09 13:33:21,854 - cmdstanpy - INFO - Chain [1] start processing
2025-07-09 13:33:24,313 - cmdstanpy - INFO - Chain [1] done processing
2025-07-09 13:33:24,382 - app.models.hybrid_predict - INFO - Statistical predictions generated for COMI
2025-07-09 13:33:24,422 - app.models.hybrid_predict - INFO - Trend-adjusted predictions generated for COMI
2025-07-09 13:33:24,422 - app.models.hybrid_predict - WARNING - Prediction for COMI at horizon 4320 was floored: 39.08 -> 67.20
2025-07-09 13:33:24,426 - app.pages.ai_advisor - INFO - Hybrid predictions generated for 5 horizons
2025-07-09 13:33:24,426 - app.pages.ai_advisor - INFO - Detecting market regime for COMI
2025-07-09 13:33:24,429 - app.pages.ai_advisor - INFO - Market regime detected: sideways
2025-07-09 13:33:24,441 - app.pages.ai_advisor - INFO - Created enhanced basic fallback predictions with 3 models
2025-07-09 13:33:24,441 - app.pages.ai_advisor - INFO - Consensus predictions generated for 5 horizons
2025-07-09 13:33:26,682 - app.utils.memory_management - INFO - Memory before cleanup: 604.23 MB
2025-07-09 13:33:27,116 - app.utils.memory_management - INFO - Garbage collection: collected 59176 objects
2025-07-09 13:33:27,124 - app.utils.memory_management - INFO - Memory after cleanup: 530.11 MB (freed 74.12 MB)
2025-07-09 13:33:55,923 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:34:01,325 - app.utils.memory_management - INFO - Memory before cleanup: 528.99 MB
2025-07-09 13:34:01,654 - app.utils.memory_management - INFO - Garbage collection: collected 1288 objects
2025-07-09 13:34:01,654 - app.utils.memory_management - INFO - Memory after cleanup: 528.99 MB (freed 0.00 MB)
2025-07-09 13:34:11,710 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:34:12,078 - app.utils.memory_management - INFO - Memory before cleanup: 529.64 MB
2025-07-09 13:34:12,298 - app.utils.memory_management - INFO - Garbage collection: collected 1264 objects
2025-07-09 13:34:12,298 - app.utils.memory_management - INFO - Memory after cleanup: 529.64 MB (freed 0.00 MB)
2025-07-09 13:34:13,975 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 13:34:14,133 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 13:34:14,230 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-09 13:34:14,429 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-09 13:34:14,642 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-09 13:34:14,644 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-07-09 13:34:14,646 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-07-09 13:34:15,686 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-09 13:34:17,112 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:34:17,112 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.8410308361053467
2025-07-09 13:34:17,112 - models.predict - INFO - Prediction for 30 minutes horizon: 77.96307185645483
2025-07-09 13:34:17,112 - app.pages.market_overview_dashboard - WARNING - Unrealistic prediction for Tomorrow: 77.96307185645483 vs 84.0
2025-07-09 13:34:17,116 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 13:34:17,147 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-07-09 13:34:17,408 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-07-09 13:34:17,614 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.04 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-09 13:34:17,616 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-07-09 13:34:17,616 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-07-09 13:34:17,616 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-07-09 13:34:17,618 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-07-09 13:34:17,663 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-07-09 13:34:17,663 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:34:17,663 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:34:17,669 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:34:17,673 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7442987191677094
2025-07-09 13:34:17,673 - models.predict - INFO - Prediction for 30 minutes horizon: 74.25919852657152
2025-07-09 13:34:17,683 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 13:34:17,717 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:34:17,911 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:34:18,120 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:34:18,123 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-07-09 13:34:18,123 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-07-09 13:34:19,146 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:34:20,534 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:34:20,536 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 1.2289073467254639
2025-07-09 13:34:20,536 - models.predict - INFO - Prediction for 60 minutes horizon: 92.81486574307318
2025-07-09 13:34:20,536 - app.pages.market_overview_dashboard - WARNING - Unrealistic prediction for 2nd Day: 92.81486574307318 vs 84.0
2025-07-09 13:34:20,536 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 13:34:20,561 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 13:34:20,937 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-07-09 13:34:21,125 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 13:34:21,125 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-09 13:34:21,125 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-09 13:34:21,129 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-09 13:34:21,129 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-09 13:34:21,162 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 13:34:21,162 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 13:34:21,162 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 13:34:21,176 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 13:34:21,178 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7442987191677094
2025-07-09 13:34:21,178 - models.predict - INFO - Prediction for 60 minutes horizon: 74.25919852657152
2025-07-09 13:34:21,190 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-09 13:34:21,202 - app.utils.data_processing - INFO - Found scaler for COMI with 240 minutes horizon, but no model file
2025-07-09 13:34:21,216 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using lstm
2025-07-09 13:34:21,227 - app.utils.data_processing - INFO - No model found for COMI with 480 minutes horizon using rf
2025-07-09 13:34:21,238 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 13:34:21,270 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-07-09 13:34:21,461 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-07-09 13:34:21,654 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-09 13:34:21,657 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-07-09 13:34:21,658 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-07-09 13:34:22,755 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-07-09 13:34:24,333 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 13:34:24,333 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.7276133894920349
2025-07-09 13:34:24,333 - models.predict - INFO - Prediction for 1440 minutes horizon: 81.57321197540246
2025-07-09 13:34:24,581 - app.utils.memory_management - INFO - Memory before cleanup: 550.85 MB
2025-07-09 13:34:24,988 - app.utils.memory_management - INFO - Garbage collection: collected 14827 objects
2025-07-09 13:34:25,002 - app.utils.memory_management - INFO - Memory after cleanup: 534.13 MB (freed 16.72 MB)
2025-07-09 14:28:26,167 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:28:26,214 - app - INFO - Found 14 stock files in data/stocks
2025-07-09 14:28:26,228 - app.utils.memory_management - INFO - Memory before cleanup: 491.05 MB
2025-07-09 14:28:26,633 - app.utils.memory_management - INFO - Garbage collection: collected 232 objects
2025-07-09 14:28:26,633 - app.utils.memory_management - INFO - Memory after cleanup: 491.05 MB (freed -0.00 MB)
2025-07-09 14:28:31,886 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:28:31,923 - app.utils.memory_management - INFO - Memory before cleanup: 491.03 MB
2025-07-09 14:28:32,191 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-07-09 14:28:32,191 - app.utils.memory_management - INFO - Memory after cleanup: 491.03 MB (freed 0.00 MB)
2025-07-09 14:28:33,482 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:28:33,525 - app.pages.smc_analysis - INFO - Loading CSV data from: data/stocks/COMI.csv
2025-07-09 14:28:33,640 - app.pages.smc_analysis - INFO - Prepared SMC data: 180 bars from 2024-10-31 to 2025-07-09
2025-07-09 14:28:33,653 - app.pages.smc_analysis - INFO - Successfully loaded 180 bars of historical data for SMC analysis
2025-07-09 14:29:11,878 - app.utils.memory_management - INFO - Memory before cleanup: 494.99 MB
2025-07-09 14:29:12,183 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-07-09 14:29:12,199 - app.utils.memory_management - INFO - Memory after cleanup: 494.99 MB (freed 0.00 MB)
2025-07-09 14:29:34,960 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:29:35,284 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:29:35,321 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:29:35,322 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:29:35,330 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:29:35,339 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 14:29:35,347 - app.utils.error_handling - INFO - live_trading_component executed in 0.36 seconds
2025-07-09 14:29:35,354 - app.utils.memory_management - INFO - Memory before cleanup: 495.09 MB
2025-07-09 14:29:35,682 - app.utils.memory_management - INFO - Garbage collection: collected 298 objects
2025-07-09 14:29:35,684 - app.utils.memory_management - INFO - Memory after cleanup: 495.09 MB (freed 0.00 MB)
2025-07-09 14:29:52,416 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:30:16,449 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:30:16,451 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:30:16,451 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:30:16,452 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:30:16,457 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 14:30:16,460 - app.utils.error_handling - INFO - live_trading_component executed in 23.85 seconds
2025-07-09 14:30:16,461 - app.utils.memory_management - INFO - Memory before cleanup: 495.10 MB
2025-07-09 14:30:16,776 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-07-09 14:30:16,807 - app.utils.memory_management - INFO - Memory after cleanup: 495.10 MB (freed 0.00 MB)
2025-07-09 14:30:59,214 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:30:59,347 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:30:59,347 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:30:59,349 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:30:59,349 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:30:59,363 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:30:59,364 - models.train - INFO - Symbol: COMI, Model type: rf
2025-07-09 14:30:59,364 - models.train - INFO - Horizons: [5] (in minutes)
2025-07-09 14:30:59,367 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:30:59,445 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:30:59,445 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:30:59,449 - models.train - INFO - Starting model training...
2025-07-09 14:30:59,450 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:30:59,455 - models.train - INFO - Model type: rf
2025-07-09 14:30:59,456 - models.train - INFO - Horizons: [5]
2025-07-09 14:30:59,457 - models.train - INFO - Sequence length: 60
2025-07-09 14:30:59,457 - models.train - INFO - Epochs: 1
2025-07-09 14:30:59,458 - models.train - INFO - Batch size: 32
2025-07-09 14:30:59,461 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:30:59,461 - models.train - INFO - Preparing features...
2025-07-09 14:30:59,515 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:30:59,516 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:30:59,517 - models.train - INFO - Training model for 5 minutes horizon
2025-07-09 14:30:59,518 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:30:59,521 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:30:59,521 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:30:59,522 - models.train - INFO - Creating target variable for horizon 5 minutes...
2025-07-09 14:30:59,522 - models.train - INFO - Converting 5 minutes to approximately 1 days for target shifting
2025-07-09 14:30:59,524 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:30:59,524 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:30:59,528 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:30:59,528 - models.train - INFO - Preprocessing data...
2025-07-09 14:31:00,182 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:31:00,397 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.98 MB, VMS: 1.97 MB, Percent: 0.01%, Execution time: 0.22s
2025-07-09 14:31:00,399 - models.train - INFO - Saving scaler for horizon 5...
2025-07-09 14:31:00,695 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_5_scaler.pkl (0.53 KB)
2025-07-09 14:31:00,695 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_rf_scaler_5min.joblib (0.53 KB)
2025-07-09 14:31:00,695 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_rf_scaler5min.joblib (0.53 KB)
2025-07-09 14:31:01,232 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.06 MB, VMS: 0.06 MB, Percent: 0.00%, Execution time: 0.34s
2025-07-09 14:31:01,233 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_5_scaler.pkl', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler5min.joblib']
2025-07-09 14:31:01,233 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:31:01,234 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:31:01,234 - models.train - INFO - Initializing model for horizon 5...
2025-07-09 14:31:01,234 - models.train - INFO - Using scikit-learn rf model for 5 days horizon
2025-07-09 14:31:01,234 - models.train - INFO - Training model for horizon 5...
2025-07-09 14:31:01,234 - models.train - INFO - Training machine learning model (rf) for horizon 5. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:31:01,235 - models.sklearn_model - INFO - Training rf model for 5 minutes horizon
2025-07-09 14:31:01,235 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-09 14:31:01,236 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-09 14:31:24,663 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-09 14:31:24,735 - models.train - INFO - Machine learning model (rf) training completed for horizon 5
2025-07-09 14:31:24,735 - models.train - INFO - Saving model for horizon 5...
2025-07-09 14:31:24,793 - models.train - INFO - Model saved for horizon 5
2025-07-09 14:31:24,794 - models.train - INFO - Model for 5 days horizon trained and saved successfully
2025-07-09 14:31:24,794 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:31:24,798 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:31:24,802 - models.train - INFO - Symbol: COMI, Model type: rf
2025-07-09 14:31:24,803 - models.train - INFO - Horizons: [15] (in minutes)
2025-07-09 14:31:24,803 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:31:24,825 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:31:24,825 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:31:24,828 - models.train - INFO - Starting model training...
2025-07-09 14:31:24,829 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:31:24,829 - models.train - INFO - Model type: rf
2025-07-09 14:31:24,831 - models.train - INFO - Horizons: [15]
2025-07-09 14:31:24,831 - models.train - INFO - Sequence length: 60
2025-07-09 14:31:24,838 - models.train - INFO - Epochs: 1
2025-07-09 14:31:24,839 - models.train - INFO - Batch size: 32
2025-07-09 14:31:24,839 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:31:24,840 - models.train - INFO - Preparing features...
2025-07-09 14:31:24,881 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:31:24,882 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:31:24,883 - models.train - INFO - Training model for 15 minutes horizon
2025-07-09 14:31:24,883 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:31:24,885 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:31:24,886 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:31:24,886 - models.train - INFO - Creating target variable for horizon 15 minutes...
2025-07-09 14:31:24,886 - models.train - INFO - Converting 15 minutes to approximately 1 days for target shifting
2025-07-09 14:31:24,887 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:31:24,888 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:31:24,893 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:31:24,895 - models.train - INFO - Preprocessing data...
2025-07-09 14:31:25,276 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:31:25,470 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.19s
2025-07-09 14:31:25,470 - models.train - INFO - Saving scaler for horizon 15...
2025-07-09 14:31:25,671 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-07-09 14:31:25,675 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_rf_scaler_15min.joblib (0.53 KB)
2025-07-09 14:31:25,680 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_rf_scaler15min.joblib (0.53 KB)
2025-07-09 14:31:26,033 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-09 14:31:26,034 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_15_scaler.pkl', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler15min.joblib']
2025-07-09 14:31:26,034 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:31:26,034 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:31:26,034 - models.train - INFO - Initializing model for horizon 15...
2025-07-09 14:31:26,035 - models.train - INFO - Using scikit-learn rf model for 15 days horizon
2025-07-09 14:31:26,035 - models.train - INFO - Training model for horizon 15...
2025-07-09 14:31:26,035 - models.train - INFO - Training machine learning model (rf) for horizon 15. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:31:26,035 - models.sklearn_model - INFO - Training rf model for 15 minutes horizon
2025-07-09 14:31:26,035 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-09 14:31:26,036 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-09 14:31:48,729 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-09 14:31:48,790 - models.train - INFO - Machine learning model (rf) training completed for horizon 15
2025-07-09 14:31:48,792 - models.train - INFO - Saving model for horizon 15...
2025-07-09 14:31:48,851 - models.train - INFO - Model saved for horizon 15
2025-07-09 14:31:48,851 - models.train - INFO - Model for 15 days horizon trained and saved successfully
2025-07-09 14:31:48,852 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:31:48,864 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:31:48,865 - models.train - INFO - Symbol: COMI, Model type: rf
2025-07-09 14:31:48,865 - models.train - INFO - Horizons: [30] (in minutes)
2025-07-09 14:31:48,866 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:31:48,886 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:31:48,890 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:31:48,898 - models.train - INFO - Starting model training...
2025-07-09 14:31:48,898 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:31:48,899 - models.train - INFO - Model type: rf
2025-07-09 14:31:48,899 - models.train - INFO - Horizons: [30]
2025-07-09 14:31:48,899 - models.train - INFO - Sequence length: 60
2025-07-09 14:31:48,900 - models.train - INFO - Epochs: 1
2025-07-09 14:31:48,900 - models.train - INFO - Batch size: 32
2025-07-09 14:31:48,900 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:31:48,901 - models.train - INFO - Preparing features...
2025-07-09 14:31:48,950 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:31:48,951 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:31:48,951 - models.train - INFO - Training model for 30 minutes horizon
2025-07-09 14:31:48,952 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:31:48,957 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:31:48,957 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:31:48,958 - models.train - INFO - Creating target variable for horizon 30 minutes...
2025-07-09 14:31:48,959 - models.train - INFO - Converting 30 minutes to approximately 1 days for target shifting
2025-07-09 14:31:48,961 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:31:48,963 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:31:48,966 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:31:48,967 - models.train - INFO - Preprocessing data...
2025-07-09 14:31:49,492 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:31:49,669 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.21s
2025-07-09 14:31:49,670 - models.train - INFO - Saving scaler for horizon 30...
2025-07-09 14:31:49,872 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-07-09 14:31:49,884 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_rf_scaler_30min.joblib (0.53 KB)
2025-07-09 14:31:49,886 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_rf_scaler30min.joblib (0.53 KB)
2025-07-09 14:31:50,317 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.25s
2025-07-09 14:31:50,317 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_30_scaler.pkl', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler30min.joblib']
2025-07-09 14:31:50,317 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:31:50,318 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:31:50,318 - models.train - INFO - Initializing model for horizon 30...
2025-07-09 14:31:50,318 - models.train - INFO - Using scikit-learn rf model for 30 days horizon
2025-07-09 14:31:50,319 - models.train - INFO - Training model for horizon 30...
2025-07-09 14:31:50,320 - models.train - INFO - Training machine learning model (rf) for horizon 30. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:31:50,323 - models.sklearn_model - INFO - Training rf model for 30 minutes horizon
2025-07-09 14:31:50,324 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-09 14:31:50,325 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-09 14:32:13,389 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-09 14:32:13,439 - models.train - INFO - Machine learning model (rf) training completed for horizon 30
2025-07-09 14:32:13,440 - models.train - INFO - Saving model for horizon 30...
2025-07-09 14:32:13,493 - models.train - INFO - Model saved for horizon 30
2025-07-09 14:32:13,493 - models.train - INFO - Model for 30 days horizon trained and saved successfully
2025-07-09 14:32:13,496 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:32:13,499 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:32:13,500 - models.train - INFO - Symbol: COMI, Model type: rf
2025-07-09 14:32:13,500 - models.train - INFO - Horizons: [60] (in minutes)
2025-07-09 14:32:13,501 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:32:13,526 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:32:13,529 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:32:13,534 - models.train - INFO - Starting model training...
2025-07-09 14:32:13,534 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:32:13,534 - models.train - INFO - Model type: rf
2025-07-09 14:32:13,535 - models.train - INFO - Horizons: [60]
2025-07-09 14:32:13,535 - models.train - INFO - Sequence length: 60
2025-07-09 14:32:13,536 - models.train - INFO - Epochs: 1
2025-07-09 14:32:13,536 - models.train - INFO - Batch size: 32
2025-07-09 14:32:13,536 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:32:13,537 - models.train - INFO - Preparing features...
2025-07-09 14:32:13,579 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:32:13,581 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:32:13,582 - models.train - INFO - Training model for 60 minutes horizon
2025-07-09 14:32:13,582 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:32:13,583 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:32:13,583 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:32:13,583 - models.train - INFO - Creating target variable for horizon 60 minutes...
2025-07-09 14:32:13,583 - models.train - INFO - Converting 60 minutes to approximately 1 days for target shifting
2025-07-09 14:32:13,583 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:32:13,588 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:32:13,589 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:32:13,590 - models.train - INFO - Preprocessing data...
2025-07-09 14:32:13,964 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:32:14,172 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.18s
2025-07-09 14:32:14,173 - models.train - INFO - Saving scaler for horizon 60...
2025-07-09 14:32:14,387 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-09 14:32:14,389 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_rf_scaler_60min.joblib (0.53 KB)
2025-07-09 14:32:14,391 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_rf_scaler60min.joblib (0.53 KB)
2025-07-09 14:32:14,753 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-09 14:32:14,754 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_60_scaler.pkl', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler60min.joblib']
2025-07-09 14:32:14,754 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:32:14,754 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:32:14,755 - models.train - INFO - Initializing model for horizon 60...
2025-07-09 14:32:14,755 - models.train - INFO - Using scikit-learn rf model for 60 days horizon
2025-07-09 14:32:14,755 - models.train - INFO - Training model for horizon 60...
2025-07-09 14:32:14,755 - models.train - INFO - Training machine learning model (rf) for horizon 60. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:32:14,755 - models.sklearn_model - INFO - Training rf model for 60 minutes horizon
2025-07-09 14:32:14,756 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-09 14:32:14,756 - models.sklearn_model - INFO - Training RandomForestRegressor model...
2025-07-09 14:32:38,593 - models.sklearn_model - INFO - RandomForestRegressor model training completed
2025-07-09 14:32:38,658 - models.train - INFO - Machine learning model (rf) training completed for horizon 60
2025-07-09 14:32:38,658 - models.train - INFO - Saving model for horizon 60...
2025-07-09 14:32:38,711 - models.train - INFO - Model saved for horizon 60
2025-07-09 14:32:38,711 - models.train - INFO - Model for 60 days horizon trained and saved successfully
2025-07-09 14:32:38,712 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:32:38,716 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:32:38,724 - app.utils.error_handling - INFO - live_trading_component executed in 99.45 seconds
2025-07-09 14:32:38,726 - app.utils.memory_management - INFO - Memory before cleanup: 504.61 MB
2025-07-09 14:32:38,958 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-09 14:32:38,958 - app.utils.memory_management - INFO - Memory after cleanup: 504.61 MB (freed 0.00 MB)
2025-07-09 14:33:05,264 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:33:05,406 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:33:05,408 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:33:05,408 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:33:05,409 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:33:05,419 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:33:05,420 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:33:05,423 - app.utils.error_handling - INFO - live_trading_component executed in 0.07 seconds
2025-07-09 14:33:05,424 - app.utils.memory_management - INFO - Memory before cleanup: 504.58 MB
2025-07-09 14:33:05,868 - app.utils.memory_management - INFO - Garbage collection: collected 231 objects
2025-07-09 14:33:05,868 - app.utils.memory_management - INFO - Memory after cleanup: 504.58 MB (freed 0.00 MB)
2025-07-09 14:33:09,674 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:33:09,745 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:33:09,746 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:33:09,746 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:33:09,747 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:33:09,754 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:33:09,755 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:33:09,818 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-09 14:33:10,102 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.53 KB)
2025-07-09 14:33:10,303 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-07-09 14:33:10,305 - models.predict - INFO - Using scikit-learn rf model for 5 minutes horizon
2025-07-09 14:33:10,305 - models.predict - INFO - Loading rf model for COMI with horizon 5
2025-07-09 14:33:10,305 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_5min.joblib
2025-07-09 14:33:10,305 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_5min.joblib
2025-07-09 14:33:10,367 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-09 14:33:10,367 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 14:33:10,367 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 14:33:10,374 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 14:33:10,374 - models.predict - INFO - Current price: 83.9, Predicted scaled value: 0.4256341296434403
2025-07-09 14:33:10,375 - models.predict - INFO - Prediction for 5 minutes horizon: 83.29550019694167
2025-07-09 14:33:10,375 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 14:33:10,556 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-09 14:33:10,726 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 14:33:10,728 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-07-09 14:33:10,731 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-07-09 14:33:10,732 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-07-09 14:33:10,732 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-07-09 14:33:10,794 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 14:33:10,795 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 14:33:10,795 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 14:33:10,800 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 14:33:10,801 - models.predict - INFO - Current price: 83.9, Predicted scaled value: 0.4256341296434403
2025-07-09 14:33:10,802 - models.predict - INFO - Prediction for 60 minutes horizon: 83.29550019694167
2025-07-09 14:33:10,888 - app.utils.error_handling - INFO - live_trading_component executed in 1.18 seconds
2025-07-09 14:33:10,889 - app.utils.memory_management - INFO - Memory before cleanup: 489.97 MB
2025-07-09 14:33:11,178 - app.utils.memory_management - INFO - Garbage collection: collected 967 objects
2025-07-09 14:33:11,179 - app.utils.memory_management - INFO - Memory after cleanup: 489.98 MB (freed -0.00 MB)
2025-07-09 14:33:46,143 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:33:46,237 - app - INFO - Found 14 stock files in data/stocks
2025-07-09 14:33:46,375 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 14:33:46,376 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 14:33:46,377 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 14:33:46,377 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:33:46,393 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:33:46,394 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:33:46,404 - app.utils.error_handling - INFO - live_trading_component executed in 0.16 seconds
2025-07-09 14:33:46,421 - app.utils.memory_management - INFO - Memory before cleanup: 490.67 MB
2025-07-09 14:33:46,863 - app.utils.memory_management - INFO - Garbage collection: collected 222 objects
2025-07-09 14:33:46,865 - app.utils.memory_management - INFO - Memory after cleanup: 490.67 MB (freed 0.00 MB)
2025-07-09 14:33:56,872 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:33:56,949 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 14:33:56,950 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 14:33:56,950 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 14:33:56,950 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:33:56,958 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:33:56,958 - models.train - INFO - Symbol: COMI, Model type: gb
2025-07-09 14:33:56,959 - models.train - INFO - Horizons: [5] (in minutes)
2025-07-09 14:33:56,960 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:33:56,986 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:33:56,986 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:33:56,989 - models.train - INFO - Starting model training...
2025-07-09 14:33:56,990 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:33:56,991 - models.train - INFO - Model type: gb
2025-07-09 14:33:56,991 - models.train - INFO - Horizons: [5]
2025-07-09 14:33:56,993 - models.train - INFO - Sequence length: 60
2025-07-09 14:33:56,993 - models.train - INFO - Epochs: 1
2025-07-09 14:33:56,993 - models.train - INFO - Batch size: 32
2025-07-09 14:33:56,996 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:33:56,997 - models.train - INFO - Preparing features...
2025-07-09 14:33:57,033 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:33:57,034 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:33:57,035 - models.train - INFO - Training model for 5 minutes horizon
2025-07-09 14:33:57,035 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:33:57,043 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:33:57,048 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:33:57,049 - models.train - INFO - Creating target variable for horizon 5 minutes...
2025-07-09 14:33:57,051 - models.train - INFO - Converting 5 minutes to approximately 1 days for target shifting
2025-07-09 14:33:57,055 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:33:57,057 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:33:57,060 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:33:57,061 - models.train - INFO - Preprocessing data...
2025-07-09 14:33:57,684 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:33:57,880 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.34s
2025-07-09 14:33:57,880 - models.train - INFO - Saving scaler for horizon 5...
2025-07-09 14:33:58,068 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_5_scaler.pkl (0.53 KB)
2025-07-09 14:33:58,070 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_gb_scaler_5min.joblib (0.53 KB)
2025-07-09 14:33:58,072 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_gb_scaler5min.joblib (0.53 KB)
2025-07-09 14:33:58,436 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.07 MB, VMS: 0.18 MB, Percent: 0.00%, Execution time: 0.18s
2025-07-09 14:33:58,438 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_5_scaler.pkl', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler5min.joblib']
2025-07-09 14:33:58,438 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:33:58,438 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:33:58,438 - models.train - INFO - Initializing model for horizon 5...
2025-07-09 14:33:58,438 - models.train - INFO - Using scikit-learn gb model for 5 days horizon
2025-07-09 14:33:58,438 - models.train - INFO - Training model for horizon 5...
2025-07-09 14:33:58,438 - models.train - INFO - Training machine learning model (gb) for horizon 5. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:33:58,438 - models.sklearn_model - INFO - Training gb model for 5 minutes horizon
2025-07-09 14:33:58,438 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-09 14:33:58,438 - models.sklearn_model - INFO - Training GradientBoostingRegressor model...
2025-07-09 14:34:12,634 - models.sklearn_model - INFO - GradientBoostingRegressor model training completed
2025-07-09 14:34:12,645 - models.train - INFO - Machine learning model (gb) training completed for horizon 5
2025-07-09 14:34:12,646 - models.train - INFO - Saving model for horizon 5...
2025-07-09 14:34:12,655 - models.train - INFO - Model saved for horizon 5
2025-07-09 14:34:12,656 - models.train - INFO - Model for 5 days horizon trained and saved successfully
2025-07-09 14:34:12,656 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:34:12,665 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:34:12,666 - models.train - INFO - Symbol: COMI, Model type: gb
2025-07-09 14:34:12,667 - models.train - INFO - Horizons: [15] (in minutes)
2025-07-09 14:34:12,668 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:34:12,695 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:34:12,696 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:34:12,700 - models.train - INFO - Starting model training...
2025-07-09 14:34:12,702 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:34:12,703 - models.train - INFO - Model type: gb
2025-07-09 14:34:12,717 - models.train - INFO - Horizons: [15]
2025-07-09 14:34:12,738 - models.train - INFO - Sequence length: 60
2025-07-09 14:34:12,772 - models.train - INFO - Epochs: 1
2025-07-09 14:34:12,782 - models.train - INFO - Batch size: 32
2025-07-09 14:34:12,806 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:34:12,809 - models.train - INFO - Preparing features...
2025-07-09 14:34:12,860 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:34:12,861 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:34:12,862 - models.train - INFO - Training model for 15 minutes horizon
2025-07-09 14:34:12,863 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:34:12,866 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:34:12,866 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:34:12,867 - models.train - INFO - Creating target variable for horizon 15 minutes...
2025-07-09 14:34:12,867 - models.train - INFO - Converting 15 minutes to approximately 1 days for target shifting
2025-07-09 14:34:12,869 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:34:12,869 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:34:12,871 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:34:12,872 - models.train - INFO - Preprocessing data...
2025-07-09 14:34:13,217 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:34:13,385 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.17s
2025-07-09 14:34:13,385 - models.train - INFO - Saving scaler for horizon 15...
2025-07-09 14:34:13,560 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-07-09 14:34:13,562 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_gb_scaler_15min.joblib (0.53 KB)
2025-07-09 14:34:13,564 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_gb_scaler15min.joblib (0.53 KB)
2025-07-09 14:34:13,929 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.18s
2025-07-09 14:34:13,929 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_15_scaler.pkl', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler15min.joblib']
2025-07-09 14:34:13,930 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:34:13,930 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:34:13,930 - models.train - INFO - Initializing model for horizon 15...
2025-07-09 14:34:13,930 - models.train - INFO - Using scikit-learn gb model for 15 days horizon
2025-07-09 14:34:13,930 - models.train - INFO - Training model for horizon 15...
2025-07-09 14:34:13,930 - models.train - INFO - Training machine learning model (gb) for horizon 15. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:34:13,931 - models.sklearn_model - INFO - Training gb model for 15 minutes horizon
2025-07-09 14:34:13,931 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-09 14:34:13,931 - models.sklearn_model - INFO - Training GradientBoostingRegressor model...
2025-07-09 14:34:26,320 - models.sklearn_model - INFO - GradientBoostingRegressor model training completed
2025-07-09 14:34:26,337 - models.train - INFO - Machine learning model (gb) training completed for horizon 15
2025-07-09 14:34:26,337 - models.train - INFO - Saving model for horizon 15...
2025-07-09 14:34:26,348 - models.train - INFO - Model saved for horizon 15
2025-07-09 14:34:26,348 - models.train - INFO - Model for 15 days horizon trained and saved successfully
2025-07-09 14:34:26,349 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:34:26,351 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:34:26,352 - models.train - INFO - Symbol: COMI, Model type: gb
2025-07-09 14:34:26,352 - models.train - INFO - Horizons: [30] (in minutes)
2025-07-09 14:34:26,353 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:34:26,381 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:34:26,382 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:34:26,384 - models.train - INFO - Starting model training...
2025-07-09 14:34:26,385 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:34:26,385 - models.train - INFO - Model type: gb
2025-07-09 14:34:26,385 - models.train - INFO - Horizons: [30]
2025-07-09 14:34:26,386 - models.train - INFO - Sequence length: 60
2025-07-09 14:34:26,386 - models.train - INFO - Epochs: 1
2025-07-09 14:34:26,386 - models.train - INFO - Batch size: 32
2025-07-09 14:34:26,387 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:34:26,387 - models.train - INFO - Preparing features...
2025-07-09 14:34:26,435 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:34:26,435 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:34:26,436 - models.train - INFO - Training model for 30 minutes horizon
2025-07-09 14:34:26,436 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:34:26,442 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:34:26,443 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:34:26,444 - models.train - INFO - Creating target variable for horizon 30 minutes...
2025-07-09 14:34:26,444 - models.train - INFO - Converting 30 minutes to approximately 1 days for target shifting
2025-07-09 14:34:26,446 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:34:26,446 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:34:26,448 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:34:26,448 - models.train - INFO - Preprocessing data...
2025-07-09 14:34:26,823 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:34:27,026 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.19s
2025-07-09 14:34:27,026 - models.train - INFO - Saving scaler for horizon 30...
2025-07-09 14:34:27,229 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-07-09 14:34:27,238 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_gb_scaler_30min.joblib (0.53 KB)
2025-07-09 14:34:27,242 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_gb_scaler30min.joblib (0.53 KB)
2025-07-09 14:34:27,604 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-09 14:34:27,604 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_30_scaler.pkl', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler30min.joblib']
2025-07-09 14:34:27,605 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:34:27,605 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:34:27,606 - models.train - INFO - Initializing model for horizon 30...
2025-07-09 14:34:27,608 - models.train - INFO - Using scikit-learn gb model for 30 days horizon
2025-07-09 14:34:27,608 - models.train - INFO - Training model for horizon 30...
2025-07-09 14:34:27,608 - models.train - INFO - Training machine learning model (gb) for horizon 30. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:34:27,608 - models.sklearn_model - INFO - Training gb model for 30 minutes horizon
2025-07-09 14:34:27,609 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-09 14:34:27,609 - models.sklearn_model - INFO - Training GradientBoostingRegressor model...
2025-07-09 14:34:41,229 - models.sklearn_model - INFO - GradientBoostingRegressor model training completed
2025-07-09 14:34:41,235 - models.train - INFO - Machine learning model (gb) training completed for horizon 30
2025-07-09 14:34:41,240 - models.train - INFO - Saving model for horizon 30...
2025-07-09 14:34:41,247 - models.train - INFO - Model saved for horizon 30
2025-07-09 14:34:41,248 - models.train - INFO - Model for 30 days horizon trained and saved successfully
2025-07-09 14:34:41,248 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:34:41,251 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:34:41,252 - models.train - INFO - Symbol: COMI, Model type: gb
2025-07-09 14:34:41,257 - models.train - INFO - Horizons: [60] (in minutes)
2025-07-09 14:34:41,259 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:34:41,283 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:34:41,283 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:34:41,367 - models.train - INFO - Starting model training...
2025-07-09 14:34:41,369 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:34:41,371 - models.train - INFO - Model type: gb
2025-07-09 14:34:41,379 - models.train - INFO - Horizons: [60]
2025-07-09 14:34:41,380 - models.train - INFO - Sequence length: 60
2025-07-09 14:34:41,381 - models.train - INFO - Epochs: 1
2025-07-09 14:34:41,381 - models.train - INFO - Batch size: 32
2025-07-09 14:34:41,384 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:34:41,391 - models.train - INFO - Preparing features...
2025-07-09 14:34:41,491 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:34:41,499 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:34:41,503 - models.train - INFO - Training model for 60 minutes horizon
2025-07-09 14:34:41,504 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:34:41,511 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:34:41,513 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:34:41,513 - models.train - INFO - Creating target variable for horizon 60 minutes...
2025-07-09 14:34:41,514 - models.train - INFO - Converting 60 minutes to approximately 1 days for target shifting
2025-07-09 14:34:41,515 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:34:41,516 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:34:41,540 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:34:41,541 - models.train - INFO - Preprocessing data...
2025-07-09 14:34:41,910 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:34:42,090 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.19s
2025-07-09 14:34:42,091 - models.train - INFO - Saving scaler for horizon 60...
2025-07-09 14:34:42,264 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-09 14:34:42,266 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_gb_scaler_60min.joblib (0.53 KB)
2025-07-09 14:34:42,268 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_gb_scaler60min.joblib (0.53 KB)
2025-07-09 14:34:42,633 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.20s
2025-07-09 14:34:42,633 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_60_scaler.pkl', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler60min.joblib']
2025-07-09 14:34:42,634 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:34:42,634 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:34:42,635 - models.train - INFO - Initializing model for horizon 60...
2025-07-09 14:34:42,635 - models.train - INFO - Using scikit-learn gb model for 60 days horizon
2025-07-09 14:34:42,635 - models.train - INFO - Training model for horizon 60...
2025-07-09 14:34:42,635 - models.train - INFO - Training machine learning model (gb) for horizon 60. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:34:42,635 - models.sklearn_model - INFO - Training gb model for 60 minutes horizon
2025-07-09 14:34:42,636 - models.sklearn_model - INFO - Reshaping 3D input with shape (951, 60, 7) to 2D
2025-07-09 14:34:42,636 - models.sklearn_model - INFO - Training GradientBoostingRegressor model...
2025-07-09 14:34:56,697 - models.sklearn_model - INFO - GradientBoostingRegressor model training completed
2025-07-09 14:34:56,713 - models.train - INFO - Machine learning model (gb) training completed for horizon 60
2025-07-09 14:34:56,713 - models.train - INFO - Saving model for horizon 60...
2025-07-09 14:34:56,724 - models.train - INFO - Model saved for horizon 60
2025-07-09 14:34:56,724 - models.train - INFO - Model for 60 days horizon trained and saved successfully
2025-07-09 14:34:56,725 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:34:56,729 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 14:34:56,730 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:34:56,743 - app.utils.error_handling - INFO - live_trading_component executed in 59.84 seconds
2025-07-09 14:34:56,748 - app.utils.memory_management - INFO - Memory before cleanup: 494.88 MB
2025-07-09 14:34:57,003 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-09 14:34:57,003 - app.utils.memory_management - INFO - Memory after cleanup: 494.88 MB (freed 0.00 MB)
2025-07-09 14:35:48,526 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:35:48,641 - app.utils.data_processing - INFO - Found hybrid model for COMI with 5 minutes horizon
2025-07-09 14:35:48,651 - app.utils.data_processing - INFO - Found hybrid model for COMI with 15 minutes horizon
2025-07-09 14:35:48,663 - app.utils.data_processing - INFO - Found hybrid model for COMI with 30 minutes horizon
2025-07-09 14:35:48,664 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-09 14:35:48,715 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 14:35:48,716 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:35:48,726 - app.utils.error_handling - INFO - live_trading_component executed in 0.16 seconds
2025-07-09 14:35:48,728 - app.utils.memory_management - INFO - Memory before cleanup: 492.53 MB
2025-07-09 14:35:49,155 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-07-09 14:35:49,155 - app.utils.memory_management - INFO - Memory after cleanup: 492.53 MB (freed 0.00 MB)
2025-07-09 14:35:53,652 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:35:53,737 - app.utils.data_processing - INFO - Found hybrid model for COMI with 5 minutes horizon
2025-07-09 14:35:53,738 - app.utils.data_processing - INFO - Found hybrid model for COMI with 15 minutes horizon
2025-07-09 14:35:53,739 - app.utils.data_processing - INFO - Found hybrid model for COMI with 30 minutes horizon
2025-07-09 14:35:53,741 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-09 14:35:53,748 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:35:53,749 - models.train - INFO - Symbol: COMI, Model type: hybrid
2025-07-09 14:35:53,752 - models.train - INFO - Horizons: [5] (in minutes)
2025-07-09 14:35:53,754 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:35:53,774 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:35:53,776 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:35:53,780 - models.train - INFO - Starting model training...
2025-07-09 14:35:53,782 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:35:53,786 - models.train - INFO - Model type: hybrid
2025-07-09 14:35:53,825 - models.train - INFO - Horizons: [5]
2025-07-09 14:35:53,828 - models.train - INFO - Sequence length: 60
2025-07-09 14:35:53,829 - models.train - INFO - Epochs: 1
2025-07-09 14:35:53,830 - models.train - INFO - Batch size: 32
2025-07-09 14:35:53,830 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:35:53,830 - models.train - INFO - Preparing features...
2025-07-09 14:35:53,872 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:35:53,875 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:35:53,876 - models.train - INFO - Training model for 5 minutes horizon
2025-07-09 14:35:53,876 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:35:53,879 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:35:53,879 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:35:53,880 - models.train - INFO - Creating target variable for horizon 5 minutes...
2025-07-09 14:35:53,880 - models.train - INFO - Converting 5 minutes to approximately 1 days for target shifting
2025-07-09 14:35:53,882 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:35:53,883 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:35:53,885 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:35:53,885 - models.train - INFO - Preprocessing data...
2025-07-09 14:35:54,516 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:35:54,723 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.21s
2025-07-09 14:35:54,725 - models.train - INFO - Saving scaler for horizon 5...
2025-07-09 14:35:54,924 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_5_scaler.pkl (0.53 KB)
2025-07-09 14:35:54,927 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_hybrid_scaler_5min.joblib (0.53 KB)
2025-07-09 14:35:54,930 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_hybrid_scaler5min.joblib (0.53 KB)
2025-07-09 14:35:55,301 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.01 MB, VMS: 0.01 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-09 14:35:55,302 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_5_scaler.pkl', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler5min.joblib']
2025-07-09 14:35:55,302 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:35:55,302 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:35:55,303 - models.train - INFO - Initializing model for horizon 5...
2025-07-09 14:35:55,303 - models.train - INFO - Using scikit-learn hybrid model for 5 days horizon
2025-07-09 14:35:55,303 - models.train - INFO - Training model for horizon 5...
2025-07-09 14:35:55,303 - models.train - INFO - Training machine learning model (hybrid) for horizon 5. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:35:55,304 - models.sklearn_model - INFO - Creating ARIMAMLHybrid model with RF component
2025-07-09 14:35:55,304 - models.sklearn_model - INFO - Training hybrid model for 5 minutes horizon
2025-07-09 14:35:55,304 - models.hybrid_model - INFO - Training rf model directly
2025-07-09 14:36:17,594 - models.train - INFO - Machine learning model (hybrid) training completed for horizon 5
2025-07-09 14:36:17,596 - models.train - INFO - Saving model for horizon 5...
2025-07-09 14:36:17,643 - models.train - INFO - Model saved for horizon 5
2025-07-09 14:36:17,644 - models.train - INFO - Model for 5 days horizon trained and saved successfully
2025-07-09 14:36:17,644 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:36:17,652 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:36:17,653 - models.train - INFO - Symbol: COMI, Model type: hybrid
2025-07-09 14:36:17,653 - models.train - INFO - Horizons: [15] (in minutes)
2025-07-09 14:36:17,654 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:36:17,675 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:36:17,676 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:36:17,683 - models.train - INFO - Starting model training...
2025-07-09 14:36:17,685 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:36:17,686 - models.train - INFO - Model type: hybrid
2025-07-09 14:36:17,686 - models.train - INFO - Horizons: [15]
2025-07-09 14:36:17,688 - models.train - INFO - Sequence length: 60
2025-07-09 14:36:17,688 - models.train - INFO - Epochs: 1
2025-07-09 14:36:17,689 - models.train - INFO - Batch size: 32
2025-07-09 14:36:17,689 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:36:17,689 - models.train - INFO - Preparing features...
2025-07-09 14:36:17,735 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:36:17,735 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:36:17,736 - models.train - INFO - Training model for 15 minutes horizon
2025-07-09 14:36:17,736 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:36:17,739 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:36:17,754 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:36:17,770 - models.train - INFO - Creating target variable for horizon 15 minutes...
2025-07-09 14:36:17,772 - models.train - INFO - Converting 15 minutes to approximately 1 days for target shifting
2025-07-09 14:36:17,774 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:36:17,774 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:36:17,776 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:36:17,776 - models.train - INFO - Preprocessing data...
2025-07-09 14:36:18,182 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:36:18,358 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.18s
2025-07-09 14:36:18,358 - models.train - INFO - Saving scaler for horizon 15...
2025-07-09 14:36:18,529 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-07-09 14:36:18,532 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_hybrid_scaler_15min.joblib (0.53 KB)
2025-07-09 14:36:18,534 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_hybrid_scaler15min.joblib (0.53 KB)
2025-07-09 14:36:18,908 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.19s
2025-07-09 14:36:18,909 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_15_scaler.pkl', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler15min.joblib']
2025-07-09 14:36:18,909 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:36:18,909 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:36:18,909 - models.train - INFO - Initializing model for horizon 15...
2025-07-09 14:36:18,910 - models.train - INFO - Using scikit-learn hybrid model for 15 days horizon
2025-07-09 14:36:18,910 - models.train - INFO - Training model for horizon 15...
2025-07-09 14:36:18,910 - models.train - INFO - Training machine learning model (hybrid) for horizon 15. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:36:18,910 - models.sklearn_model - INFO - Creating ARIMAMLHybrid model with RF component
2025-07-09 14:36:18,911 - models.sklearn_model - INFO - Training hybrid model for 15 minutes horizon
2025-07-09 14:36:18,911 - models.hybrid_model - INFO - Training rf model directly
2025-07-09 14:36:41,436 - models.train - INFO - Machine learning model (hybrid) training completed for horizon 15
2025-07-09 14:36:41,437 - models.train - INFO - Saving model for horizon 15...
2025-07-09 14:36:41,491 - models.train - INFO - Model saved for horizon 15
2025-07-09 14:36:41,491 - models.train - INFO - Model for 15 days horizon trained and saved successfully
2025-07-09 14:36:41,491 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:36:41,491 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:36:41,491 - models.train - INFO - Symbol: COMI, Model type: hybrid
2025-07-09 14:36:41,491 - models.train - INFO - Horizons: [30] (in minutes)
2025-07-09 14:36:41,491 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:36:41,513 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:36:41,513 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:36:41,517 - models.train - INFO - Starting model training...
2025-07-09 14:36:41,517 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:36:41,517 - models.train - INFO - Model type: hybrid
2025-07-09 14:36:41,517 - models.train - INFO - Horizons: [30]
2025-07-09 14:36:41,517 - models.train - INFO - Sequence length: 60
2025-07-09 14:36:41,518 - models.train - INFO - Epochs: 1
2025-07-09 14:36:41,518 - models.train - INFO - Batch size: 32
2025-07-09 14:36:41,518 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:36:41,523 - models.train - INFO - Preparing features...
2025-07-09 14:36:41,566 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:36:41,567 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:36:41,568 - models.train - INFO - Training model for 30 minutes horizon
2025-07-09 14:36:41,573 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:36:41,577 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:36:41,578 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:36:41,578 - models.train - INFO - Creating target variable for horizon 30 minutes...
2025-07-09 14:36:41,579 - models.train - INFO - Converting 30 minutes to approximately 1 days for target shifting
2025-07-09 14:36:41,581 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:36:41,582 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:36:41,584 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:36:41,585 - models.train - INFO - Preprocessing data...
2025-07-09 14:36:41,973 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:36:42,154 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.19s
2025-07-09 14:36:42,155 - models.train - INFO - Saving scaler for horizon 30...
2025-07-09 14:36:42,327 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_30_scaler.pkl (0.53 KB)
2025-07-09 14:36:42,334 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_hybrid_scaler_30min.joblib (0.53 KB)
2025-07-09 14:36:42,338 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_hybrid_scaler30min.joblib (0.53 KB)
2025-07-09 14:36:42,708 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.21s
2025-07-09 14:36:42,709 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_30_scaler.pkl', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler30min.joblib']
2025-07-09 14:36:42,709 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:36:42,710 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:36:42,710 - models.train - INFO - Initializing model for horizon 30...
2025-07-09 14:36:42,710 - models.train - INFO - Using scikit-learn hybrid model for 30 days horizon
2025-07-09 14:36:42,710 - models.train - INFO - Training model for horizon 30...
2025-07-09 14:36:42,710 - models.train - INFO - Training machine learning model (hybrid) for horizon 30. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:36:42,710 - models.sklearn_model - INFO - Creating ARIMAMLHybrid model with RF component
2025-07-09 14:36:42,711 - models.sklearn_model - INFO - Training hybrid model for 30 minutes horizon
2025-07-09 14:36:42,711 - models.hybrid_model - INFO - Training rf model directly
2025-07-09 14:37:06,375 - models.train - INFO - Machine learning model (hybrid) training completed for horizon 30
2025-07-09 14:37:06,376 - models.train - INFO - Saving model for horizon 30...
2025-07-09 14:37:06,431 - models.train - INFO - Model saved for horizon 30
2025-07-09 14:37:06,431 - models.train - INFO - Model for 30 days horizon trained and saved successfully
2025-07-09 14:37:06,432 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:37:06,434 - models.train - INFO - Starting training from CSV: data/stocks\COMI.csv
2025-07-09 14:37:06,434 - models.train - INFO - Symbol: COMI, Model type: hybrid
2025-07-09 14:37:06,435 - models.train - INFO - Horizons: [60] (in minutes)
2025-07-09 14:37:06,435 - models.train - INFO - Loading data from data/stocks\COMI.csv...
2025-07-09 14:37:06,453 - models.train - INFO - Data loaded. Shape: (1250, 36)
2025-07-09 14:37:06,453 - models.train - INFO - Converting Date column to datetime...
2025-07-09 14:37:06,458 - models.train - INFO - Starting model training...
2025-07-09 14:37:06,458 - models.train - INFO - Training models for symbol: COMI
2025-07-09 14:37:06,458 - models.train - INFO - Model type: hybrid
2025-07-09 14:37:06,459 - models.train - INFO - Horizons: [60]
2025-07-09 14:37:06,463 - models.train - INFO - Sequence length: 60
2025-07-09 14:37:06,463 - models.train - INFO - Epochs: 1
2025-07-09 14:37:06,463 - models.train - INFO - Batch size: 32
2025-07-09 14:37:06,465 - models.train - INFO - Data shape: (1250, 36)
2025-07-09 14:37:06,465 - models.train - INFO - Preparing features...
2025-07-09 14:37:06,527 - models.train - INFO - Features prepared. Shape: (1250, 36)
2025-07-09 14:37:06,531 - models.train - INFO - Available columns after feature engineering: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'SMA5', 'SMA20', 'SMA50', 'EMA5', 'EMA20', 'EMA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff', 'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width', 'ATR', 'Hour', 'DayOfWeek', 'Month', 'Year', 'DayOfMonth', 'Hour_sin', 'Hour_cos', 'DayOfWeek_sin', 'DayOfWeek_cos', 'Month_sin', 'Month_cos', 'Price_Change', 'Price_Change_1d', 'Price_Change_5d', 'Volatility_5d']
2025-07-09 14:37:06,532 - models.train - INFO - Training model for 60 minutes horizon
2025-07-09 14:37:06,532 - models.train - INFO - Data shape before processing: (1250, 36)
2025-07-09 14:37:06,535 - models.train - INFO - Detected data frequency: daily
2025-07-09 14:37:06,536 - models.train - INFO - Minimum required rows: 70
2025-07-09 14:37:06,537 - models.train - INFO - Creating target variable for horizon 60 minutes...
2025-07-09 14:37:06,537 - models.train - INFO - Converting 60 minutes to approximately 1 days for target shifting
2025-07-09 14:37:06,540 - models.train - INFO - Created target variable. Valid targets: 1249/1250
2025-07-09 14:37:06,541 - models.train - INFO - Dropping rows with NaN targets...
2025-07-09 14:37:06,544 - models.train - INFO - Data shape after dropping NaN rows: (1249, 37)
2025-07-09 14:37:06,544 - models.train - INFO - Preprocessing data...
2025-07-09 14:37:06,946 - app.utils.data_processing - INFO - Created sequences: X shape=(1189, 60, 7), memory=1.90 MB; y shape=(1189,), memory=0.00 MB
2025-07-09 14:37:07,128 - app.utils.memory_management - INFO - Memory usage for preprocess_data: RSS: 1.91 MB, VMS: 1.91 MB, Percent: 0.01%, Execution time: 0.22s
2025-07-09 14:37:07,128 - models.train - INFO - Saving scaler for horizon 60...
2025-07-09 14:37:07,306 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-09 14:37:07,308 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_hybrid_scaler_60min.joblib (0.53 KB)
2025-07-09 14:37:07,311 - app.utils.data_processing - INFO - Saved scaler to saved_models\COMI_hybrid_scaler60min.joblib (0.53 KB)
2025-07-09 14:37:07,652 - app.utils.memory_management - INFO - Memory usage for save_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.17s
2025-07-09 14:37:07,652 - models.train - INFO - Saved scaler with 3 naming conventions: ['COMI_60_scaler.pkl', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler60min.joblib']
2025-07-09 14:37:07,653 - models.train - INFO - Splitting data into training and validation sets...
2025-07-09 14:37:07,653 - models.train - INFO - Training data shape: (951, 60, 7)
2025-07-09 14:37:07,653 - models.train - INFO - Initializing model for horizon 60...
2025-07-09 14:37:07,653 - models.train - INFO - Using scikit-learn hybrid model for 60 days horizon
2025-07-09 14:37:07,653 - models.train - INFO - Training model for horizon 60...
2025-07-09 14:37:07,654 - models.train - INFO - Training machine learning model (hybrid) for horizon 60. This may be quick as ML models don't use epochs like neural networks.
2025-07-09 14:37:07,654 - models.sklearn_model - INFO - Creating ARIMAMLHybrid model with RF component
2025-07-09 14:37:07,654 - models.sklearn_model - INFO - Training hybrid model for 60 minutes horizon
2025-07-09 14:37:07,654 - models.hybrid_model - INFO - Training rf model directly
2025-07-09 14:37:29,208 - models.train - INFO - Machine learning model (hybrid) training completed for horizon 60
2025-07-09 14:37:29,208 - models.train - INFO - Saving model for horizon 60...
2025-07-09 14:37:29,256 - models.train - INFO - Model saved for horizon 60
2025-07-09 14:37:29,257 - models.train - INFO - Model for 60 days horizon trained and saved successfully
2025-07-09 14:37:29,258 - models.train - INFO - Successfully trained 1 models
2025-07-09 14:37:29,263 - app.utils.data_processing - INFO - Found hybrid model for COMI with 5 minutes horizon
2025-07-09 14:37:29,263 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-09 14:37:29,272 - app.utils.error_handling - INFO - live_trading_component executed in 95.58 seconds
2025-07-09 14:37:29,275 - app.utils.memory_management - INFO - Memory before cleanup: 501.02 MB
2025-07-09 14:37:29,534 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-07-09 14:37:29,534 - app.utils.memory_management - INFO - Memory after cleanup: 501.02 MB (freed 0.00 MB)
2025-07-09 14:37:48,285 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:37:48,442 - app.utils.data_processing - INFO - Found hybrid model for COMI with 5 minutes horizon
2025-07-09 14:37:48,463 - app.utils.data_processing - INFO - Found hybrid model for COMI with 15 minutes horizon
2025-07-09 14:37:48,466 - app.utils.data_processing - INFO - Found hybrid model for COMI with 30 minutes horizon
2025-07-09 14:37:48,466 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-09 14:37:48,475 - app.utils.data_processing - INFO - Found hybrid model for COMI with 5 minutes horizon
2025-07-09 14:37:48,478 - app.utils.data_processing - INFO - Found hybrid model for COMI with 60 minutes horizon
2025-07-09 14:37:48,552 - models.predict - INFO - Making predictions for 5 minutes horizon
2025-07-09 14:37:48,989 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_5_scaler.pkl (0.53 KB)
2025-07-09 14:37:49,265 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 14:37:49,269 - models.predict - INFO - Using scikit-learn hybrid model for 5 minutes horizon
2025-07-09 14:37:49,270 - models.predict - INFO - Loading hybrid model for COMI with horizon 5
2025-07-09 14:37:49,271 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_5min.joblib
2025-07-09 14:37:49,271 - models.sklearn_model - INFO - Found hybrid model at saved_models\COMI_arima_ml_rf_5min.joblib
2025-07-09 14:37:49,271 - models.sklearn_model - INFO - Loading model from saved_models\COMI_arima_ml_rf_5min.joblib
2025-07-09 14:37:49,382 - models.predict - INFO - Successfully loaded model for COMI with horizon 5
2025-07-09 14:37:49,383 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-07-09 14:37:49,384 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 14:37:49,391 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 14:37:49,392 - models.predict - INFO - Current price: 83.9, Predicted scaled value: 0.4256341296434403
2025-07-09 14:37:49,393 - models.predict - INFO - Prediction for 5 minutes horizon: 83.29550019694167
2025-07-09 14:37:49,393 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-07-09 14:37:49,594 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.53 KB)
2025-07-09 14:37:49,767 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 14:37:49,770 - models.predict - INFO - Using scikit-learn hybrid model for 60 minutes horizon
2025-07-09 14:37:49,773 - models.predict - INFO - Loading hybrid model for COMI with horizon 60
2025-07-09 14:37:49,774 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_hybrid_60min.joblib
2025-07-09 14:37:49,774 - models.sklearn_model - INFO - Found hybrid model at saved_models\COMI_arima_ml_rf_60min.joblib
2025-07-09 14:37:49,775 - models.sklearn_model - INFO - Loading model from saved_models\COMI_arima_ml_rf_60min.joblib
2025-07-09 14:37:49,837 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-07-09 14:37:49,837 - models.sklearn_model - INFO - Using hybrid model predict method for hybrid
2025-07-09 14:37:49,838 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 14:37:49,842 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 14:37:49,843 - models.predict - INFO - Current price: 83.9, Predicted scaled value: 0.4256341296434403
2025-07-09 14:37:49,843 - models.predict - INFO - Prediction for 60 minutes horizon: 83.29550019694167
2025-07-09 14:37:49,881 - app.utils.error_handling - INFO - live_trading_component executed in 1.50 seconds
2025-07-09 14:37:49,882 - app.utils.memory_management - INFO - Memory before cleanup: 486.18 MB
2025-07-09 14:37:50,180 - app.utils.memory_management - INFO - Garbage collection: collected 853 objects
2025-07-09 14:37:50,182 - app.utils.memory_management - INFO - Memory after cleanup: 486.18 MB (freed -0.00 MB)
2025-07-09 14:39:36,254 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:39:36,294 - app - INFO - Found 14 stock files in data/stocks
2025-07-09 14:39:36,341 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-09 14:39:36,345 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:36,345 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:39:36,346 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:36,350 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 14:39:36,352 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-09 14:39:36,358 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-07-09 14:39:36,380 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 14:39:36,397 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 14:39:36,418 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 14:39:36,431 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 14:39:36,465 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-07-09 14:39:36,498 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-07-09 14:39:36,499 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 14:39:36,500 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 14:39:36,501 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:39:36,501 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 14:39:36,502 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-07-09 14:39:36,503 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-07-09 14:39:36,503 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 14:39:36,504 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 14:39:36,505 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 14:39:36,507 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 14:39:36,508 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-07-09 14:39:36,509 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-07-09 14:39:36,510 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:36,512 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:39:36,512 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:36,513 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 14:39:36,517 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-07-09 14:39:36,554 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:36,558 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:36,567 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:39:36,591 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 14:39:36,597 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 14:39:36,618 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:39:36,620 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:36,624 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:39:36,626 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:36,627 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 14:39:36,627 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 14:39:36,639 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 14:39:36,642 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 14:39:36,655 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 14:39:36,669 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 14:39:36,669 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 14:39:36,670 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 14:39:36,672 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 14:39:36,673 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:39:36,675 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 14:39:36,675 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-09 14:39:36,676 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 14:39:36,676 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 14:39:36,678 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 14:39:36,678 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 14:39:36,679 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:39:36,679 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:36,680 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:39:36,680 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:36,680 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 14:39:36,689 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:36,710 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 14:39:36,711 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 14:39:36,712 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 14:39:36,712 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:36,728 - app.utils.memory_management - INFO - Memory before cleanup: 486.32 MB
2025-07-09 14:39:37,120 - app.utils.memory_management - INFO - Garbage collection: collected 222 objects
2025-07-09 14:39:37,125 - app.utils.memory_management - INFO - Memory after cleanup: 486.29 MB (freed 0.04 MB)
2025-07-09 14:39:50,619 - app - INFO - Using TensorFlow-based LSTM model
2025-07-09 14:39:50,685 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:50,692 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:50,693 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:39:50,705 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 14:39:50,725 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 14:39:50,748 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:39:50,809 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:50,813 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:39:50,813 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:50,814 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 14:39:50,815 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-07-09 14:39:50,826 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 14:39:50,827 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-07-09 14:39:50,840 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-07-09 14:39:50,853 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-07-09 14:39:50,855 - app.utils.data_processing - INFO - Found gb model for COMI with 5 minutes horizon
2025-07-09 14:39:50,858 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 14:39:50,860 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-07-09 14:39:50,860 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon
2025-07-09 14:39:50,862 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-07-09 14:39:50,862 - app.utils.data_processing - INFO - Found lr model for COMI with 5 minutes horizon
2025-07-09 14:39:50,864 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 14:39:50,864 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-07-09 14:39:50,865 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon
2025-07-09 14:39:50,865 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-07-09 14:39:50,866 - app.utils.data_processing - INFO - Found rf model for COMI with 5 minutes horizon
2025-07-09 14:39:50,868 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:50,872 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-07-09 14:39:50,873 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon
2025-07-09 14:39:50,874 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-07-09 14:39:50,890 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:50,904 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 14:39:50,906 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 14:39:50,908 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 14:39:50,908 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:39:50,941 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:40:44,273 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=15, model=rf, live_data=False
2025-07-09 14:40:44,303 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-09 14:40:44,567 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-07-09 14:40:44,721 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 14:40:44,721 - models.predict - INFO - Using scikit-learn rf model for 15 minutes horizon
2025-07-09 14:40:44,721 - models.predict - INFO - Loading rf model for COMI with horizon 15
2025-07-09 14:40:44,721 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_15min.joblib
2025-07-09 14:40:44,721 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_15min.joblib
2025-07-09 14:40:44,795 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-09 14:40:44,796 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-07-09 14:40:44,796 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 14:40:44,801 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 14:40:44,802 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.42283715844154357
2025-07-09 14:40:44,802 - models.predict - INFO - Prediction for 15 minutes horizon: 83.05180010201691
2025-07-09 14:40:44,817 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-07-09 14:41:29,576 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=15, model=lstm, live_data=False
2025-07-09 14:41:29,607 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-09 14:41:29,799 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-07-09 14:41:29,966 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 14:41:29,967 - models.predict - INFO - Loading lstm model for COMI with horizon 15
2025-07-09 14:41:29,968 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_15min.keras
2025-07-09 14:41:30,848 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-09 14:41:32,009 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-07-09 14:41:32,009 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.44482699036598206
2025-07-09 14:41:32,010 - models.predict - INFO - Prediction for 15 minutes horizon: 84.96777411123479
2025-07-09 14:41:32,011 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-07-09 14:42:18,598 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=15, model=gb, live_data=False
2025-07-09 14:42:18,632 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-09 14:42:18,852 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-07-09 14:42:19,018 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.04 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 14:42:19,020 - models.predict - INFO - Using scikit-learn gb model for 15 minutes horizon
2025-07-09 14:42:19,020 - models.predict - INFO - Loading gb model for COMI with horizon 15
2025-07-09 14:42:19,021 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_15min.joblib
2025-07-09 14:42:19,021 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_15min.joblib
2025-07-09 14:42:19,050 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-09 14:42:19,071 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-07-09 14:42:19,072 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 14:42:19,074 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 14:42:19,074 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.4208668025926789
2025-07-09 14:42:19,075 - models.predict - INFO - Prediction for 15 minutes horizon: 82.8801230010592
2025-07-09 14:42:19,077 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-07-09 14:42:48,094 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=15, model=lr, live_data=True
2025-07-09 14:42:48,138 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-09 14:42:48,333 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-07-09 14:42:48,497 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 14:42:48,499 - models.predict - INFO - Using scikit-learn lr model for 15 minutes horizon
2025-07-09 14:42:48,499 - models.predict - INFO - Loading lr model for COMI with horizon 15
2025-07-09 14:42:48,499 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_lr_15min.joblib
2025-07-09 14:42:48,500 - models.sklearn_model - INFO - Loading model from saved_models\COMI_lr_15min.joblib
2025-07-09 14:42:48,518 - models.predict - INFO - Successfully loaded model for COMI with horizon 15
2025-07-09 14:42:48,518 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. Ridge expected <= 2.. Trying with prepared data.
2025-07-09 14:42:48,519 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-07-09 14:42:48,519 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 14:42:48,520 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.49710231478976796
2025-07-09 14:42:48,520 - models.predict - INFO - Prediction for 15 minutes horizon: 89.52252301807364
2025-07-09 14:42:48,521 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 89.52 vs current 84.00 (6.6% change, limit: 2.0%). Applying correction.
2025-07-09 14:42:48,522 - app.pages.predictions_consolidated - INFO - Corrected prediction: 89.52 -> 85.18 (change: 1.4%)
2025-07-09 14:42:48,522 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 89.52 -> 85.18 for 15min
2025-07-09 14:42:48,523 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-07-09 14:43:19,629 - app.pages.predictions_consolidated - INFO - UNIFIED PREDICTION: symbol=COMI, horizon=15, model=ensemble, live_data=False
2025-07-09 14:43:19,659 - models.predict - INFO - Making predictions for 15 minutes horizon
2025-07-09 14:43:19,836 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_15_scaler.pkl (0.53 KB)
2025-07-09 14:43:20,019 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-07-09 14:43:20,021 - models.predict - INFO - Using RobustEnsembleModel for 15 minutes horizon
2025-07-09 14:43:20,083 - models.robust_ensemble - INFO - Loaded ensemble model from saved_models\COMI_ensemble_15min.joblib
2025-07-09 14:43:20,084 - models.predict - INFO - Successfully loaded RobustEnsembleModel for COMI with horizon 15
2025-07-09 14:43:20,084 - models.predict - INFO - Loading ensemble model for COMI with horizon 15
2025-07-09 14:43:20,085 - models.predict - INFO - Ensemble model already loaded
2025-07-09 14:43:20,111 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-07-09 14:43:20,111 - models.predict - INFO - Current price: 84.0, Predicted scaled value: 0.5709771613587099
2025-07-09 14:43:20,112 - models.predict - INFO - Prediction for 15 minutes horizon: 95.95923824388434
2025-07-09 14:43:20,114 - app.pages.predictions_consolidated - WARNING - UNREALISTIC prediction detected: 95.96 vs current 84.00 (14.2% change, limit: 2.0%). Applying correction.
2025-07-09 14:43:20,114 - app.pages.predictions_consolidated - INFO - Corrected prediction: 95.96 -> 85.18 (change: 1.4%)
2025-07-09 14:43:20,114 - app.pages.predictions_consolidated - WARNING - Prediction corrected: 95.96 -> 85.18 for 15min
2025-07-09 14:43:20,162 - app.utils.memory_management - INFO - Memory before cleanup: 480.86 MB
2025-07-09 14:43:20,363 - app.utils.memory_management - INFO - Garbage collection: collected 36 objects
2025-07-09 14:43:20,364 - app.utils.memory_management - INFO - Memory after cleanup: 480.86 MB (freed 0.00 MB)
2025-07-09 16:15:30,569 - app - INFO - Cleaning up resources...
2025-07-09 16:15:30,572 - app.utils.memory_management - INFO - Memory before cleanup: 471.33 MB
2025-07-09 16:15:30,827 - app.utils.memory_management - INFO - Garbage collection: collected 365 objects
2025-07-09 16:15:30,827 - app.utils.memory_management - INFO - Memory after cleanup: 471.36 MB (freed -0.02 MB)
2025-07-09 16:15:30,827 - app - INFO - Application shutdown complete
