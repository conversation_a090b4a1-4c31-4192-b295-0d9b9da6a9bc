2025-06-25 12:29:57,236 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-25 12:30:01,489 - app - INFO - Memory management utilities loaded
2025-06-25 12:30:01,497 - app - INFO - Error handling utilities loaded
2025-06-25 12:30:01,501 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-25 12:30:01,510 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-25 12:30:01,520 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-25 12:30:01,524 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-25 12:30:01,538 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-25 12:30:01,542 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-25 12:30:01,542 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-25 12:30:01,547 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-25 12:30:01,550 - app - INFO - Applied NumPy fix
2025-06-25 12:30:01,552 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-25 12:30:01,553 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-25 12:30:01,553 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-25 12:30:01,553 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-25 12:30:01,553 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-25 12:30:01,554 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-25 12:30:01,554 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-25 12:30:01,554 - app - INFO - Applied NumPy BitGenerator fix
2025-06-25 12:30:24,942 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-25 12:30:24,942 - app - INFO - Applied TensorFlow fix
2025-06-25 12:30:24,946 - app.config - INFO - Configuration initialized
2025-06-25 12:30:24,954 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-25 12:30:24,974 - models.train - INFO - TensorFlow test successful
2025-06-25 12:30:30,076 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-25 12:30:30,077 - models.train - INFO - Transformer model is available
2025-06-25 12:30:30,077 - models.train - INFO - Using TensorFlow-based models
2025-06-25 12:30:30,090 - models.predict - INFO - Transformer model is available for predictions
2025-06-25 12:30:30,090 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-25 12:30:30,096 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-25 12:30:32,060 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-25 12:30:32,060 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-25 12:30:32,061 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-25 12:30:32,061 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-25 12:30:32,061 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-25 12:30:32,061 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-25 12:30:32,062 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-25 12:30:32,062 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-25 12:30:32,062 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-25 12:30:32,062 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-25 12:30:32,448 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-25 12:30:32,452 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:30:33,003 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-25 12:30:35,546 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-25 12:30:35,836 - app.utils.session_state - INFO - Initializing session state
2025-06-25 12:30:35,838 - app.utils.session_state - INFO - Session state initialized
2025-06-25 12:30:37,049 - app - INFO - Found 14 stock files in data/stocks
2025-06-25 12:30:37,073 - app.utils.memory_management - INFO - Memory before cleanup: 428.91 MB
2025-06-25 12:30:37,276 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-25 12:30:37,276 - app.utils.memory_management - INFO - Memory after cleanup: 428.91 MB (freed -0.00 MB)
2025-06-25 12:30:42,836 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:30:42,862 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-25 12:30:42,871 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-25 12:30:42,969 - app.utils.memory_management - INFO - Memory before cleanup: 433.38 MB
2025-06-25 12:30:43,201 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-25 12:30:43,202 - app.utils.memory_management - INFO - Memory after cleanup: 433.38 MB (freed 0.00 MB)
2025-06-25 12:33:59,492 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-25 12:34:01,670 - app - INFO - Memory management utilities loaded
2025-06-25 12:34:01,675 - app - INFO - Error handling utilities loaded
2025-06-25 12:34:01,676 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-25 12:34:01,677 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-25 12:34:01,677 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-25 12:34:01,678 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-25 12:34:01,678 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-25 12:34:01,678 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-25 12:34:01,679 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-25 12:34:01,679 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-25 12:34:01,679 - app - INFO - Applied NumPy fix
2025-06-25 12:34:01,680 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-25 12:34:01,681 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-25 12:34:01,681 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-25 12:34:01,681 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-25 12:34:01,681 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-25 12:34:01,681 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-25 12:34:01,682 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-25 12:34:01,682 - app - INFO - Applied NumPy BitGenerator fix
2025-06-25 12:34:20,360 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-25 12:34:20,360 - app - INFO - Applied TensorFlow fix
2025-06-25 12:34:20,364 - app.config - INFO - Configuration initialized
2025-06-25 12:34:20,371 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-25 12:34:20,381 - models.train - INFO - TensorFlow test successful
2025-06-25 12:34:24,914 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-25 12:34:24,914 - models.train - INFO - Transformer model is available
2025-06-25 12:34:24,915 - models.train - INFO - Using TensorFlow-based models
2025-06-25 12:34:24,923 - models.predict - INFO - Transformer model is available for predictions
2025-06-25 12:34:24,923 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-25 12:34:24,928 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-25 12:34:26,721 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-25 12:34:26,724 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-25 12:34:26,724 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-25 12:34:26,724 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-25 12:34:26,724 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-25 12:34:26,724 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-25 12:34:26,724 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-25 12:34:26,726 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-25 12:34:26,726 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-25 12:34:26,726 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-25 12:34:27,099 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-25 12:34:27,103 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:34:27,103 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:34:27,614 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-25 12:34:29,861 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-25 12:34:30,127 - app.utils.session_state - INFO - Initializing session state
2025-06-25 12:34:30,131 - app.utils.memory_management - INFO - Memory before cleanup: 425.30 MB
2025-06-25 12:34:30,133 - app.utils.session_state - INFO - Session state initialized
2025-06-25 12:34:30,278 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-25 12:34:30,280 - app.utils.memory_management - INFO - Memory after cleanup: 425.71 MB (freed -0.41 MB)
2025-06-25 12:34:31,632 - app - INFO - Found 14 stock files in data/stocks
2025-06-25 12:34:31,642 - app.utils.memory_management - INFO - Memory before cleanup: 430.41 MB
2025-06-25 12:34:31,799 - app.utils.memory_management - INFO - Garbage collection: collected 69 objects
2025-06-25 12:34:31,800 - app.utils.memory_management - INFO - Memory after cleanup: 430.42 MB (freed -0.00 MB)
2025-06-25 12:34:36,163 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:34:36,182 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-25 12:34:36,213 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-25 12:34:36,296 - app.utils.memory_management - INFO - Memory before cleanup: 433.57 MB
2025-06-25 12:34:36,483 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-06-25 12:34:36,484 - app.utils.memory_management - INFO - Memory after cleanup: 433.63 MB (freed -0.06 MB)
2025-06-25 12:34:45,934 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:34:45,949 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-25 12:34:45,957 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-25 12:34:46,033 - app.utils.memory_management - INFO - Memory before cleanup: 434.88 MB
2025-06-25 12:34:46,223 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-25 12:34:46,223 - app.utils.memory_management - INFO - Memory after cleanup: 434.88 MB (freed 0.00 MB)
2025-06-25 12:34:58,504 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:34:58,529 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-25 12:34:58,537 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-25 12:34:58,690 - app.utils.memory_management - INFO - Memory before cleanup: 434.84 MB
2025-06-25 12:34:58,915 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-25 12:34:58,917 - app.utils.memory_management - INFO - Memory after cleanup: 434.84 MB (freed 0.00 MB)
2025-06-25 12:35:02,264 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:35:02,289 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-25 12:35:02,295 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-25 12:35:02,330 - app.utils.historical_data_downloader - INFO - Starting historical data generation for VALU (0 years)
2025-06-25 12:35:02,331 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-25 12:35:02,338 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-25 12:35:02,340 - app.utils.historical_data_downloader - INFO - Fetching current live data for VALU...
2025-06-25 12:35:02,341 - app.utils.historical_data_downloader - INFO - Downloading historical data for VALU with intervals: ['1D', '1W']
2025-06-25 12:35:02,342 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-25 12:35:02,344 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-VALU'], 'intervals': ['1D', '1W']}
2025-06-25 12:36:15,910 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-06-25 12:36:15,911 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-VALU': []}, 'message': 'Successfully scraped 1 pairs'}
2025-06-25 12:36:15,911 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-VALU']
2025-06-25 12:36:15,912 - app.utils.historical_data_downloader - INFO - Found data for EGX-VALU: []
2025-06-25 12:36:15,912 - app.utils.historical_data_downloader - INFO - Processing 0 data points for VALU
2025-06-25 12:36:15,912 - app.utils.historical_data_downloader - INFO - Raw data structure: []
2025-06-25 12:36:15,912 - app.utils.historical_data_downloader - ERROR - Unexpected raw_data structure for VALU: <class 'list'>
2025-06-25 12:36:15,912 - app.utils.historical_data_downloader - ERROR - Failed to process historical data for VALU
2025-06-25 12:36:15,913 - app.utils.historical_data_downloader - ERROR - Could not get current data for VALU
2025-06-25 12:36:15,913 - app.utils.historical_data_downloader - ERROR - This could be due to:
2025-06-25 12:36:15,913 - app.utils.historical_data_downloader - ERROR - 1. Stock symbol not found on EGX
2025-06-25 12:36:15,913 - app.utils.historical_data_downloader - ERROR - 2. API server connection issues
2025-06-25 12:36:15,914 - app.utils.historical_data_downloader - ERROR - 3. Invalid stock symbol format
2025-06-25 12:36:15,969 - app.utils.memory_management - INFO - Memory before cleanup: 434.92 MB
2025-06-25 12:36:16,208 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-25 12:36:16,210 - app.utils.memory_management - INFO - Memory after cleanup: 434.92 MB (freed 0.00 MB)
2025-06-25 12:37:33,832 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 12:37:33,856 - app.utils.memory_management - INFO - Memory before cleanup: 430.92 MB
2025-06-25 12:37:34,068 - app.utils.memory_management - INFO - Garbage collection: collected 259 objects
2025-06-25 12:37:34,069 - app.utils.memory_management - INFO - Memory after cleanup: 430.92 MB (freed -0.00 MB)
2025-06-25 13:17:08,526 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 13:17:08,542 - app - INFO - Found 14 stock files in data/stocks
2025-06-25 13:17:08,555 - app.utils.memory_management - INFO - Memory before cleanup: 381.19 MB
2025-06-25 13:17:08,755 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-25 13:17:08,756 - app.utils.memory_management - INFO - Memory after cleanup: 381.20 MB (freed -0.01 MB)
2025-06-25 13:17:12,926 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 13:17:12,948 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-25 13:17:12,955 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-25 13:17:13,046 - app.utils.memory_management - INFO - Memory before cleanup: 381.38 MB
2025-06-25 13:17:13,255 - app.utils.memory_management - INFO - Garbage collection: collected 187 objects
2025-06-25 13:17:13,256 - app.utils.memory_management - INFO - Memory after cleanup: 381.38 MB (freed 0.00 MB)
2025-06-25 13:17:20,875 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 13:17:20,897 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-25 13:17:20,902 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-25 13:17:20,985 - app.utils.memory_management - INFO - Memory before cleanup: 381.44 MB
2025-06-25 13:17:21,191 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-25 13:17:21,192 - app.utils.memory_management - INFO - Memory after cleanup: 381.44 MB (freed 0.00 MB)
2025-06-25 13:17:21,866 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 13:17:21,968 - app.utils.memory_management - INFO - Memory before cleanup: 381.50 MB
2025-06-25 13:17:22,156 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-25 13:17:22,157 - app.utils.memory_management - INFO - Memory after cleanup: 381.50 MB (freed 0.00 MB)
2025-06-25 13:17:32,779 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 13:17:32,840 - app.utils.memory_management - INFO - Memory before cleanup: 381.55 MB
2025-06-25 13:17:33,092 - app.utils.memory_management - INFO - Garbage collection: collected 230 objects
2025-06-25 13:17:33,092 - app.utils.memory_management - INFO - Memory after cleanup: 381.55 MB (freed 0.00 MB)
2025-06-25 13:17:33,264 - app - INFO - Using TensorFlow-based LSTM model
2025-06-25 13:17:46,905 - app.pages.ai_advisor - WARNING - Advanced prediction module not available, using fallback
2025-06-25 13:17:46,905 - app.pages.ai_advisor - INFO - Generating hybrid predictions for COMI
2025-06-25 13:17:46,905 - app.models.predict - INFO - Using adaptive ensemble for COMI
2025-06-25 13:17:46,905 - app.models.adaptive - INFO - No valid models for COMI with 30min horizon, using equal weights
2025-06-25 13:17:46,905 - app.models.predict - INFO - Ensemble weights for COMI with 30min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-25 13:17:46,989 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-25 13:17:47,133 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-25 13:17:47,289 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.04 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-06-25 13:17:47,289 - models.predict - INFO - Using scikit-learn rf model for 30 minutes horizon
2025-06-25 13:17:48,542 - models.hybrid_model - INFO - XGBoost is available
2025-06-25 13:17:48,544 - models.sklearn_model - INFO - Successfully imported hybrid models
2025-06-25 13:17:48,561 - models.predict - INFO - Loading rf model for COMI with horizon 30
2025-06-25 13:17:48,561 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_30min.joblib
2025-06-25 13:17:48,572 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_30min.joblib
2025-06-25 13:17:48,718 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-25 13:17:48,718 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-25 13:17:48,718 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-25 13:17:48,736 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-25 13:17:48,738 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7406162917613983
2025-06-25 13:17:48,738 - models.predict - INFO - Prediction for 30 minutes horizon: 74.11819835939582
2025-06-25 13:17:48,792 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-25 13:17:48,966 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-25 13:17:49,124 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.01 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:49,126 - models.predict - INFO - Using scikit-learn gb model for 30 minutes horizon
2025-06-25 13:17:49,128 - models.predict - INFO - Loading gb model for COMI with horizon 30
2025-06-25 13:17:49,128 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_30min.joblib
2025-06-25 13:17:49,130 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_30min.joblib
2025-06-25 13:17:49,239 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-25 13:17:49,251 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-25 13:17:49,251 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-25 13:17:49,251 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-25 13:17:49,258 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7917314838161941
2025-06-25 13:17:49,258 - models.predict - INFO - Prediction for 30 minutes horizon: 76.07539936561052
2025-06-25 13:17:49,308 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-25 13:17:49,441 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-25 13:17:49,572 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:49,574 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-06-25 13:17:49,574 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-06-25 13:17:50,570 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-25 13:17:51,776 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:17:51,776 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.8537483811378479
2025-06-25 13:17:51,776 - models.predict - INFO - Prediction for 30 minutes horizon: 78.45002673099603
2025-06-25 13:17:51,776 - app.models.predict - INFO - Adaptive ensemble prediction for 30min horizon: 76.21454148533412
2025-06-25 13:17:51,784 - app.models.adaptive - INFO - No valid models for COMI with 60min horizon, using equal weights
2025-06-25 13:17:51,784 - app.models.predict - INFO - Ensemble weights for COMI with 60min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-25 13:17:51,830 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-25 13:17:52,009 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-25 13:17:52,156 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.09 MB, Percent: 0.00%, Execution time: 0.02s
2025-06-25 13:17:52,158 - models.predict - INFO - Using scikit-learn rf model for 60 minutes horizon
2025-06-25 13:17:52,158 - models.predict - INFO - Loading rf model for COMI with horizon 60
2025-06-25 13:17:52,158 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_60min.joblib
2025-06-25 13:17:52,158 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_60min.joblib
2025-06-25 13:17:52,215 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-25 13:17:52,217 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-25 13:17:52,217 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-25 13:17:52,221 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-25 13:17:52,221 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7406162917613983
2025-06-25 13:17:52,221 - models.predict - INFO - Prediction for 60 minutes horizon: 74.11819835939582
2025-06-25 13:17:52,271 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-25 13:17:52,418 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-25 13:17:52,555 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:52,555 - models.predict - INFO - Using scikit-learn gb model for 60 minutes horizon
2025-06-25 13:17:52,555 - models.predict - INFO - Loading gb model for COMI with horizon 60
2025-06-25 13:17:52,555 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_60min.joblib
2025-06-25 13:17:52,555 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_60min.joblib
2025-06-25 13:17:52,592 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-25 13:17:52,609 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-25 13:17:52,611 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-25 13:17:52,611 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-25 13:17:52,611 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7917314838161941
2025-06-25 13:17:52,611 - models.predict - INFO - Prediction for 60 minutes horizon: 76.07539936561052
2025-06-25 13:17:52,655 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-25 13:17:52,788 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-25 13:17:52,933 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:52,933 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-06-25 13:17:52,937 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-06-25 13:17:53,739 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-25 13:17:54,873 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:17:54,873 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 1.2493574619293213
2025-06-25 13:17:54,873 - models.predict - INFO - Prediction for 60 minutes horizon: 93.59790077522742
2025-06-25 13:17:54,873 - app.models.predict - INFO - Adaptive ensemble prediction for 60min horizon: 81.26383283341124
2025-06-25 13:17:54,873 - app.models.adaptive - INFO - No valid models for COMI with 240min horizon, using equal weights
2025-06-25 13:17:54,873 - app.models.predict - INFO - Ensemble weights for COMI with 240min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-25 13:17:54,924 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-25 13:17:55,090 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-25 13:17:55,303 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.09 MB, Percent: 0.00%, Execution time: 0.07s
2025-06-25 13:17:55,303 - models.predict - INFO - Using scikit-learn rf model for 240 minutes horizon
2025-06-25 13:17:55,306 - models.predict - INFO - Loading rf model for COMI with horizon 240
2025-06-25 13:17:55,306 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_240min.joblib
2025-06-25 13:17:55,307 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_rf_240min.joblib, searching for alternatives...
2025-06-25 13:17:55,307 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-06-25 13:17:55,310 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-06-25 13:17:55,312 - models.predict - WARNING - Model file not found or import error for rf with horizon 240: No model files found for COMI with horizon 240. Please train a rf model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-06-25 13:17:55,312 - models.predict - INFO - Skipping rf model for horizon 240 - model not trained for this horizon
2025-06-25 13:17:55,368 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-25 13:17:55,511 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-25 13:17:55,641 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:55,641 - models.predict - INFO - Using scikit-learn gb model for 240 minutes horizon
2025-06-25 13:17:55,641 - models.predict - INFO - Loading gb model for COMI with horizon 240
2025-06-25 13:17:55,641 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_240min.joblib
2025-06-25 13:17:55,641 - models.sklearn_model - WARNING - Model not found at saved_models\COMI_gb_240min.joblib, searching for alternatives...
2025-06-25 13:17:55,641 - models.sklearn_model - INFO - Found 0 potential model files: []
2025-06-25 13:17:55,658 - models.sklearn_model - ERROR - No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-06-25 13:17:55,660 - models.predict - WARNING - Model file not found or import error for gb with horizon 240: No model files found for COMI with horizon 240. Please train a gb model for this horizon first. Available .joblib files in saved_models: ['COMI_arima_ml_params_10080min.joblib', 'COMI_arima_ml_params_120960min.joblib', 'COMI_arima_ml_params_12min.joblib', 'COMI_arima_ml_params_1440min.joblib', 'COMI_arima_ml_params_15min.joblib', 'COMI_arima_ml_params_1min.joblib', 'COMI_arima_ml_params_20160min.joblib', 'COMI_arima_ml_params_25min.joblib', 'COMI_arima_ml_params_2880min.joblib', 'COMI_arima_ml_params_2min.joblib', 'COMI_arima_ml_params_30min.joblib', 'COMI_arima_ml_params_3min.joblib', 'COMI_arima_ml_params_40320min.joblib', 'COMI_arima_ml_params_4320min.joblib', 'COMI_arima_ml_params_4min.joblib', 'COMI_arima_ml_params_5min.joblib', 'COMI_arima_ml_params_60min.joblib', 'COMI_arima_ml_params_69min.joblib', 'COMI_arima_ml_params_7200min.joblib', 'COMI_arima_ml_params_7min.joblib', 'COMI_arima_ml_params_80640min.joblib', 'COMI_arima_ml_params_8min.joblib', 'COMI_arima_ml_rf_10080min.joblib', 'COMI_arima_ml_rf_120960min.joblib', 'COMI_arima_ml_rf_12min.joblib', 'COMI_arima_ml_rf_1440min.joblib', 'COMI_arima_ml_rf_15min.joblib', 'COMI_arima_ml_rf_1min.joblib', 'COMI_arima_ml_rf_20160min.joblib', 'COMI_arima_ml_rf_25min.joblib', 'COMI_arima_ml_rf_2880min.joblib', 'COMI_arima_ml_rf_2min.joblib', 'COMI_arima_ml_rf_30min.joblib', 'COMI_arima_ml_rf_3min.joblib', 'COMI_arima_ml_rf_40320min.joblib', 'COMI_arima_ml_rf_4320min.joblib', 'COMI_arima_ml_rf_4min.joblib', 'COMI_arima_ml_rf_5min.joblib', 'COMI_arima_ml_rf_60min.joblib', 'COMI_arima_ml_rf_69min.joblib', 'COMI_arima_ml_rf_7200min.joblib', 'COMI_arima_ml_rf_7min.joblib', 'COMI_arima_ml_rf_80640min.joblib', 'COMI_arima_ml_rf_8min.joblib', 'COMI_bilstm_scaler10080min.joblib', 'COMI_bilstm_scaler120960min.joblib', 'COMI_bilstm_scaler1440min.joblib', 'COMI_bilstm_scaler15min.joblib', 'COMI_bilstm_scaler20160min.joblib', 'COMI_bilstm_scaler25min.joblib', 'COMI_bilstm_scaler2880min.joblib', 'COMI_bilstm_scaler30min.joblib', 'COMI_bilstm_scaler40320min.joblib', 'COMI_bilstm_scaler4320min.joblib', 'COMI_bilstm_scaler4min.joblib', 'COMI_bilstm_scaler5min.joblib', 'COMI_bilstm_scaler60min.joblib', 'COMI_bilstm_scaler69min.joblib', 'COMI_bilstm_scaler7200min.joblib', 'COMI_bilstm_scaler80640min.joblib', 'COMI_bilstm_scaler_10080min.joblib', 'COMI_bilstm_scaler_120960min.joblib', 'COMI_bilstm_scaler_1440min.joblib', 'COMI_bilstm_scaler_15min.joblib', 'COMI_bilstm_scaler_20160min.joblib', 'COMI_bilstm_scaler_25min.joblib', 'COMI_bilstm_scaler_2880min.joblib', 'COMI_bilstm_scaler_30min.joblib', 'COMI_bilstm_scaler_40320min.joblib', 'COMI_bilstm_scaler_4320min.joblib', 'COMI_bilstm_scaler_4min.joblib', 'COMI_bilstm_scaler_5min.joblib', 'COMI_bilstm_scaler_60min.joblib', 'COMI_bilstm_scaler_69min.joblib', 'COMI_bilstm_scaler_7200min.joblib', 'COMI_bilstm_scaler_80640min.joblib', 'COMI_enhanced_ensemble_scaler10080min.joblib', 'COMI_enhanced_ensemble_scaler120960min.joblib', 'COMI_enhanced_ensemble_scaler20160min.joblib', 'COMI_enhanced_ensemble_scaler40320min.joblib', 'COMI_enhanced_ensemble_scaler80640min.joblib', 'COMI_enhanced_ensemble_scaler_10080min.joblib', 'COMI_enhanced_ensemble_scaler_120960min.joblib', 'COMI_enhanced_ensemble_scaler_20160min.joblib', 'COMI_enhanced_ensemble_scaler_40320min.joblib', 'COMI_enhanced_ensemble_scaler_80640min.joblib', 'COMI_ensemble_10080min.joblib', 'COMI_ensemble_120960min.joblib', 'COMI_ensemble_12min.joblib', 'COMI_ensemble_1440min.joblib', 'COMI_ensemble_15min.joblib', 'COMI_ensemble_1min.joblib', 'COMI_ensemble_20160min.joblib', 'COMI_ensemble_25min.joblib', 'COMI_ensemble_2880min.joblib', 'COMI_ensemble_2min.joblib', 'COMI_ensemble_30min.joblib', 'COMI_ensemble_3min.joblib', 'COMI_ensemble_40320min.joblib', 'COMI_ensemble_4320min.joblib', 'COMI_ensemble_4min.joblib', 'COMI_ensemble_5min.joblib', 'COMI_ensemble_60min.joblib', 'COMI_ensemble_69min.joblib', 'COMI_ensemble_7200min.joblib', 'COMI_ensemble_7min.joblib', 'COMI_ensemble_80640min.joblib', 'COMI_ensemble_8min.joblib', 'COMI_ensemble_base0_10080min.joblib', 'COMI_ensemble_base0_120960min.joblib', 'COMI_ensemble_base0_12min.joblib', 'COMI_ensemble_base0_1440min.joblib', 'COMI_ensemble_base0_1min.joblib', 'COMI_ensemble_base0_20160min.joblib', 'COMI_ensemble_base0_25min.joblib', 'COMI_ensemble_base0_2880min.joblib', 'COMI_ensemble_base0_2min.joblib', 'COMI_ensemble_base0_30min.joblib', 'COMI_ensemble_base0_3min.joblib', 'COMI_ensemble_base0_40320min.joblib', 'COMI_ensemble_base0_4320min.joblib', 'COMI_ensemble_base0_4min.joblib', 'COMI_ensemble_base0_5min.joblib', 'COMI_ensemble_base0_69min.joblib', 'COMI_ensemble_base0_7200min.joblib', 'COMI_ensemble_base0_7min.joblib', 'COMI_ensemble_base0_80640min.joblib', 'COMI_ensemble_base0_8min.joblib', 'COMI_ensemble_base1_10080min.joblib', 'COMI_ensemble_base1_120960min.joblib', 'COMI_ensemble_base1_12min.joblib', 'COMI_ensemble_base1_1440min.joblib', 'COMI_ensemble_base1_1min.joblib', 'COMI_ensemble_base1_20160min.joblib', 'COMI_ensemble_base1_25min.joblib', 'COMI_ensemble_base1_2880min.joblib', 'COMI_ensemble_base1_2min.joblib', 'COMI_ensemble_base1_30min.joblib', 'COMI_ensemble_base1_3min.joblib', 'COMI_ensemble_base1_40320min.joblib', 'COMI_ensemble_base1_4320min.joblib', 'COMI_ensemble_base1_4min.joblib', 'COMI_ensemble_base1_5min.joblib', 'COMI_ensemble_base1_69min.joblib', 'COMI_ensemble_base1_7200min.joblib', 'COMI_ensemble_base1_7min.joblib', 'COMI_ensemble_base1_80640min.joblib', 'COMI_ensemble_base1_8min.joblib', 'COMI_ensemble_base2_10080min.joblib', 'COMI_ensemble_base2_120960min.joblib', 'COMI_ensemble_base2_12min.joblib', 'COMI_ensemble_base2_1440min.joblib', 'COMI_ensemble_base2_1min.joblib', 'COMI_ensemble_base2_20160min.joblib', 'COMI_ensemble_base2_25min.joblib', 'COMI_ensemble_base2_2880min.joblib', 'COMI_ensemble_base2_2min.joblib', 'COMI_ensemble_base2_30min.joblib', 'COMI_ensemble_base2_3min.joblib', 'COMI_ensemble_base2_40320min.joblib', 'COMI_ensemble_base2_4320min.joblib', 'COMI_ensemble_base2_4min.joblib', 'COMI_ensemble_base2_5min.joblib', 'COMI_ensemble_base2_69min.joblib', 'COMI_ensemble_base2_7200min.joblib', 'COMI_ensemble_base2_7min.joblib', 'COMI_ensemble_base2_80640min.joblib', 'COMI_ensemble_base2_8min.joblib', 'COMI_ensemble_base3_10080min.joblib', 'COMI_ensemble_base3_120960min.joblib', 'COMI_ensemble_base3_1440min.joblib', 'COMI_ensemble_base3_1min.joblib', 'COMI_ensemble_base3_20160min.joblib', 'COMI_ensemble_base3_25min.joblib', 'COMI_ensemble_base3_2880min.joblib', 'COMI_ensemble_base3_30min.joblib', 'COMI_ensemble_base3_40320min.joblib', 'COMI_ensemble_base3_4320min.joblib', 'COMI_ensemble_base3_4min.joblib', 'COMI_ensemble_base3_69min.joblib', 'COMI_ensemble_base3_7200min.joblib', 'COMI_ensemble_base3_80640min.joblib', 'COMI_ensemble_scaler10080min.joblib', 'COMI_ensemble_scaler120960min.joblib', 'COMI_ensemble_scaler1440min.joblib', 'COMI_ensemble_scaler15min.joblib', 'COMI_ensemble_scaler20160min.joblib', 'COMI_ensemble_scaler240min.joblib', 'COMI_ensemble_scaler25min.joblib', 'COMI_ensemble_scaler2880min.joblib', 'COMI_ensemble_scaler30min.joblib', 'COMI_ensemble_scaler40320min.joblib', 'COMI_ensemble_scaler4320min.joblib', 'COMI_ensemble_scaler4min.joblib', 'COMI_ensemble_scaler5min.joblib', 'COMI_ensemble_scaler60min.joblib', 'COMI_ensemble_scaler69min.joblib', 'COMI_ensemble_scaler7200min.joblib', 'COMI_ensemble_scaler80640min.joblib', 'COMI_ensemble_scaler_10080min.joblib', 'COMI_ensemble_scaler_120960min.joblib', 'COMI_ensemble_scaler_1440min.joblib', 'COMI_ensemble_scaler_15min.joblib', 'COMI_ensemble_scaler_20160min.joblib', 'COMI_ensemble_scaler_240min.joblib', 'COMI_ensemble_scaler_25min.joblib', 'COMI_ensemble_scaler_2880min.joblib', 'COMI_ensemble_scaler_30min.joblib', 'COMI_ensemble_scaler_40320min.joblib', 'COMI_ensemble_scaler_4320min.joblib', 'COMI_ensemble_scaler_4min.joblib', 'COMI_ensemble_scaler_5min.joblib', 'COMI_ensemble_scaler_60min.joblib', 'COMI_ensemble_scaler_69min.joblib', 'COMI_ensemble_scaler_7200min.joblib', 'COMI_ensemble_scaler_80640min.joblib', 'COMI_ensemble_weights_10080min.joblib', 'COMI_ensemble_weights_120960min.joblib', 'COMI_ensemble_weights_12min.joblib', 'COMI_ensemble_weights_1440min.joblib', 'COMI_ensemble_weights_1min.joblib', 'COMI_ensemble_weights_20160min.joblib', 'COMI_ensemble_weights_25min.joblib', 'COMI_ensemble_weights_2880min.joblib', 'COMI_ensemble_weights_2min.joblib', 'COMI_ensemble_weights_30min.joblib', 'COMI_ensemble_weights_3min.joblib', 'COMI_ensemble_weights_40320min.joblib', 'COMI_ensemble_weights_4320min.joblib', 'COMI_ensemble_weights_4min.joblib', 'COMI_ensemble_weights_5min.joblib', 'COMI_ensemble_weights_69min.joblib', 'COMI_ensemble_weights_7200min.joblib', 'COMI_ensemble_weights_7min.joblib', 'COMI_ensemble_weights_80640min.joblib', 'COMI_ensemble_weights_8min.joblib', 'COMI_gb_10080min.joblib', 'COMI_gb_120960min.joblib', 'COMI_gb_1440min.joblib', 'COMI_gb_15min.joblib', 'COMI_gb_1min.joblib', 'COMI_gb_20160min.joblib', 'COMI_gb_25min.joblib', 'COMI_gb_2880min.joblib', 'COMI_gb_2min.joblib', 'COMI_gb_30min.joblib', 'COMI_gb_3min.joblib', 'COMI_gb_40320min.joblib', 'COMI_gb_4320min.joblib', 'COMI_gb_4min.joblib', 'COMI_gb_5min.joblib', 'COMI_gb_60min.joblib', 'COMI_gb_69min.joblib', 'COMI_gb_7200min.joblib', 'COMI_gb_7min.joblib', 'COMI_gb_80640min.joblib', 'COMI_gb_scaler10080min.joblib', 'COMI_gb_scaler120960min.joblib', 'COMI_gb_scaler1440min.joblib', 'COMI_gb_scaler15min.joblib', 'COMI_gb_scaler20160min.joblib', 'COMI_gb_scaler25min.joblib', 'COMI_gb_scaler2880min.joblib', 'COMI_gb_scaler30min.joblib', 'COMI_gb_scaler40320min.joblib', 'COMI_gb_scaler4320min.joblib', 'COMI_gb_scaler4min.joblib', 'COMI_gb_scaler5min.joblib', 'COMI_gb_scaler60min.joblib', 'COMI_gb_scaler69min.joblib', 'COMI_gb_scaler7200min.joblib', 'COMI_gb_scaler80640min.joblib', 'COMI_gb_scaler_10080min.joblib', 'COMI_gb_scaler_120960min.joblib', 'COMI_gb_scaler_1440min.joblib', 'COMI_gb_scaler_15min.joblib', 'COMI_gb_scaler_20160min.joblib', 'COMI_gb_scaler_25min.joblib', 'COMI_gb_scaler_2880min.joblib', 'COMI_gb_scaler_30min.joblib', 'COMI_gb_scaler_40320min.joblib', 'COMI_gb_scaler_4320min.joblib', 'COMI_gb_scaler_4min.joblib', 'COMI_gb_scaler_5min.joblib', 'COMI_gb_scaler_60min.joblib', 'COMI_gb_scaler_69min.joblib', 'COMI_gb_scaler_7200min.joblib', 'COMI_gb_scaler_80640min.joblib', 'COMI_hybrid_10080min.joblib', 'COMI_hybrid_120960min.joblib', 'COMI_hybrid_12min.joblib', 'COMI_hybrid_1440min.joblib', 'COMI_hybrid_15min.joblib', 'COMI_hybrid_1min.joblib', 'COMI_hybrid_20160min.joblib', 'COMI_hybrid_25min.joblib', 'COMI_hybrid_2880min.joblib', 'COMI_hybrid_2min.joblib', 'COMI_hybrid_30min.joblib', 'COMI_hybrid_3min.joblib', 'COMI_hybrid_40320min.joblib', 'COMI_hybrid_4320min.joblib', 'COMI_hybrid_4min.joblib', 'COMI_hybrid_5min.joblib', 'COMI_hybrid_60min.joblib', 'COMI_hybrid_69min.joblib', 'COMI_hybrid_7200min.joblib', 'COMI_hybrid_7min.joblib', 'COMI_hybrid_80640min.joblib', 'COMI_hybrid_8min.joblib', 'COMI_hybrid_scaler10080min.joblib', 'COMI_hybrid_scaler120960min.joblib', 'COMI_hybrid_scaler1440min.joblib', 'COMI_hybrid_scaler15min.joblib', 'COMI_hybrid_scaler20160min.joblib', 'COMI_hybrid_scaler25min.joblib', 'COMI_hybrid_scaler2880min.joblib', 'COMI_hybrid_scaler30min.joblib', 'COMI_hybrid_scaler40320min.joblib', 'COMI_hybrid_scaler4320min.joblib', 'COMI_hybrid_scaler4min.joblib', 'COMI_hybrid_scaler5min.joblib', 'COMI_hybrid_scaler60min.joblib', 'COMI_hybrid_scaler69min.joblib', 'COMI_hybrid_scaler7200min.joblib', 'COMI_hybrid_scaler80640min.joblib', 'COMI_hybrid_scaler_10080min.joblib', 'COMI_hybrid_scaler_120960min.joblib', 'COMI_hybrid_scaler_1440min.joblib', 'COMI_hybrid_scaler_15min.joblib', 'COMI_hybrid_scaler_20160min.joblib', 'COMI_hybrid_scaler_25min.joblib', 'COMI_hybrid_scaler_2880min.joblib', 'COMI_hybrid_scaler_30min.joblib', 'COMI_hybrid_scaler_40320min.joblib', 'COMI_hybrid_scaler_4320min.joblib', 'COMI_hybrid_scaler_4min.joblib', 'COMI_hybrid_scaler_5min.joblib', 'COMI_hybrid_scaler_60min.joblib', 'COMI_hybrid_scaler_69min.joblib', 'COMI_hybrid_scaler_7200min.joblib', 'COMI_hybrid_scaler_80640min.joblib', 'COMI_lr_10080min.joblib', 'COMI_lr_120960min.joblib', 'COMI_lr_1440min.joblib', 'COMI_lr_15min.joblib', 'COMI_lr_20160min.joblib', 'COMI_lr_25min.joblib', 'COMI_lr_2880min.joblib', 'COMI_lr_30min.joblib', 'COMI_lr_40320min.joblib', 'COMI_lr_4320min.joblib', 'COMI_lr_4min.joblib', 'COMI_lr_5min.joblib', 'COMI_lr_60min.joblib', 'COMI_lr_69min.joblib', 'COMI_lr_7200min.joblib', 'COMI_lr_80640min.joblib', 'COMI_lr_scaler10080min.joblib', 'COMI_lr_scaler120960min.joblib', 'COMI_lr_scaler1440min.joblib', 'COMI_lr_scaler15min.joblib', 'COMI_lr_scaler20160min.joblib', 'COMI_lr_scaler25min.joblib', 'COMI_lr_scaler2880min.joblib', 'COMI_lr_scaler30min.joblib', 'COMI_lr_scaler40320min.joblib', 'COMI_lr_scaler4320min.joblib', 'COMI_lr_scaler4min.joblib', 'COMI_lr_scaler5min.joblib', 'COMI_lr_scaler60min.joblib', 'COMI_lr_scaler69min.joblib', 'COMI_lr_scaler7200min.joblib', 'COMI_lr_scaler80640min.joblib', 'COMI_lr_scaler_10080min.joblib', 'COMI_lr_scaler_120960min.joblib', 'COMI_lr_scaler_1440min.joblib', 'COMI_lr_scaler_15min.joblib', 'COMI_lr_scaler_20160min.joblib', 'COMI_lr_scaler_25min.joblib', 'COMI_lr_scaler_2880min.joblib', 'COMI_lr_scaler_30min.joblib', 'COMI_lr_scaler_40320min.joblib', 'COMI_lr_scaler_4320min.joblib', 'COMI_lr_scaler_4min.joblib', 'COMI_lr_scaler_5min.joblib', 'COMI_lr_scaler_60min.joblib', 'COMI_lr_scaler_69min.joblib', 'COMI_lr_scaler_7200min.joblib', 'COMI_lr_scaler_80640min.joblib', 'COMI_lstm_scaler10080min.joblib', 'COMI_lstm_scaler120960min.joblib', 'COMI_lstm_scaler120min.joblib', 'COMI_lstm_scaler1440min.joblib', 'COMI_lstm_scaler15min.joblib', 'COMI_lstm_scaler20160min.joblib', 'COMI_lstm_scaler25min.joblib', 'COMI_lstm_scaler2880min.joblib', 'COMI_lstm_scaler30min.joblib', 'COMI_lstm_scaler40320min.joblib', 'COMI_lstm_scaler4320min.joblib', 'COMI_lstm_scaler4min.joblib', 'COMI_lstm_scaler5min.joblib', 'COMI_lstm_scaler60min.joblib', 'COMI_lstm_scaler69min.joblib', 'COMI_lstm_scaler7200min.joblib', 'COMI_lstm_scaler80640min.joblib', 'COMI_lstm_scaler_10080min.joblib', 'COMI_lstm_scaler_120960min.joblib', 'COMI_lstm_scaler_120min.joblib', 'COMI_lstm_scaler_1440min.joblib', 'COMI_lstm_scaler_15min.joblib', 'COMI_lstm_scaler_20160min.joblib', 'COMI_lstm_scaler_25min.joblib', 'COMI_lstm_scaler_2880min.joblib', 'COMI_lstm_scaler_30min.joblib', 'COMI_lstm_scaler_40320min.joblib', 'COMI_lstm_scaler_4320min.joblib', 'COMI_lstm_scaler_4min.joblib', 'COMI_lstm_scaler_5min.joblib', 'COMI_lstm_scaler_60min.joblib', 'COMI_lstm_scaler_69min.joblib', 'COMI_lstm_scaler_7200min.joblib', 'COMI_lstm_scaler_80640min.joblib', 'COMI_prophet_10080min.joblib', 'COMI_prophet_120960min.joblib', 'COMI_prophet_12min.joblib', 'COMI_prophet_1440min.joblib', 'COMI_prophet_15min.joblib', 'COMI_prophet_1min.joblib', 'COMI_prophet_20160min.joblib', 'COMI_prophet_25min.joblib', 'COMI_prophet_2880min.joblib', 'COMI_prophet_2min.joblib', 'COMI_prophet_30min.joblib', 'COMI_prophet_3min.joblib', 'COMI_prophet_40320min.joblib', 'COMI_prophet_4320min.joblib', 'COMI_prophet_4min.joblib', 'COMI_prophet_5min.joblib', 'COMI_prophet_60min.joblib', 'COMI_prophet_69min.joblib', 'COMI_prophet_7200min.joblib', 'COMI_prophet_7min.joblib', 'COMI_prophet_80640min.joblib', 'COMI_prophet_8min.joblib', 'COMI_prophet_scaler10080min.joblib', 'COMI_prophet_scaler120960min.joblib', 'COMI_prophet_scaler1440min.joblib', 'COMI_prophet_scaler15min.joblib', 'COMI_prophet_scaler20160min.joblib', 'COMI_prophet_scaler25min.joblib', 'COMI_prophet_scaler2880min.joblib', 'COMI_prophet_scaler30min.joblib', 'COMI_prophet_scaler40320min.joblib', 'COMI_prophet_scaler4320min.joblib', 'COMI_prophet_scaler4min.joblib', 'COMI_prophet_scaler5min.joblib', 'COMI_prophet_scaler60min.joblib', 'COMI_prophet_scaler69min.joblib', 'COMI_prophet_scaler7200min.joblib', 'COMI_prophet_scaler80640min.joblib', 'COMI_prophet_scaler_10080min.joblib', 'COMI_prophet_scaler_120960min.joblib', 'COMI_prophet_scaler_1440min.joblib', 'COMI_prophet_scaler_15min.joblib', 'COMI_prophet_scaler_20160min.joblib', 'COMI_prophet_scaler_25min.joblib', 'COMI_prophet_scaler_2880min.joblib', 'COMI_prophet_scaler_30min.joblib', 'COMI_prophet_scaler_40320min.joblib', 'COMI_prophet_scaler_4320min.joblib', 'COMI_prophet_scaler_4min.joblib', 'COMI_prophet_scaler_5min.joblib', 'COMI_prophet_scaler_60min.joblib', 'COMI_prophet_scaler_69min.joblib', 'COMI_prophet_scaler_7200min.joblib', 'COMI_prophet_scaler_80640min.joblib', 'COMI_rf_10080min.joblib', 'COMI_rf_120960min.joblib', 'COMI_rf_1440min.joblib', 'COMI_rf_15min.joblib', 'COMI_rf_1min.joblib', 'COMI_rf_20160min.joblib', 'COMI_rf_25min.joblib', 'COMI_rf_2880min.joblib', 'COMI_rf_2min.joblib', 'COMI_rf_30min.joblib', 'COMI_rf_3min.joblib', 'COMI_rf_40320min.joblib', 'COMI_rf_4320min.joblib', 'COMI_rf_4min.joblib', 'COMI_rf_5min.joblib', 'COMI_rf_60min.joblib', 'COMI_rf_69min.joblib', 'COMI_rf_7200min.joblib', 'COMI_rf_7min.joblib', 'COMI_rf_80640min.joblib', 'COMI_rf_scaler10080min.joblib', 'COMI_rf_scaler120960min.joblib', 'COMI_rf_scaler1440min.joblib', 'COMI_rf_scaler15min.joblib', 'COMI_rf_scaler20160min.joblib', 'COMI_rf_scaler25min.joblib', 'COMI_rf_scaler2880min.joblib', 'COMI_rf_scaler30min.joblib', 'COMI_rf_scaler40320min.joblib', 'COMI_rf_scaler4320min.joblib', 'COMI_rf_scaler4min.joblib', 'COMI_rf_scaler5min.joblib', 'COMI_rf_scaler60min.joblib', 'COMI_rf_scaler69min.joblib', 'COMI_rf_scaler7200min.joblib', 'COMI_rf_scaler80640min.joblib', 'COMI_rf_scaler_10080min.joblib', 'COMI_rf_scaler_120960min.joblib', 'COMI_rf_scaler_1440min.joblib', 'COMI_rf_scaler_15min.joblib', 'COMI_rf_scaler_20160min.joblib', 'COMI_rf_scaler_25min.joblib', 'COMI_rf_scaler_2880min.joblib', 'COMI_rf_scaler_30min.joblib', 'COMI_rf_scaler_40320min.joblib', 'COMI_rf_scaler_4320min.joblib', 'COMI_rf_scaler_4min.joblib', 'COMI_rf_scaler_5min.joblib', 'COMI_rf_scaler_60min.joblib', 'COMI_rf_scaler_69min.joblib', 'COMI_rf_scaler_7200min.joblib', 'COMI_rf_scaler_80640min.joblib', 'COMI_svr_10080min.joblib', 'COMI_svr_120960min.joblib', 'COMI_svr_1440min.joblib', 'COMI_svr_15min.joblib', 'COMI_svr_1min.joblib', 'COMI_svr_20160min.joblib', 'COMI_svr_25min.joblib', 'COMI_svr_2880min.joblib', 'COMI_svr_2min.joblib', 'COMI_svr_30min.joblib', 'COMI_svr_3min.joblib', 'COMI_svr_40320min.joblib', 'COMI_svr_4320min.joblib', 'COMI_svr_4min.joblib', 'COMI_svr_5min.joblib', 'COMI_svr_60min.joblib', 'COMI_svr_69min.joblib', 'COMI_svr_7200min.joblib', 'COMI_svr_7min.joblib', 'COMI_svr_80640min.joblib', 'COMI_svr_scaler10080min.joblib', 'COMI_svr_scaler120960min.joblib', 'COMI_svr_scaler1440min.joblib', 'COMI_svr_scaler15min.joblib', 'COMI_svr_scaler20160min.joblib', 'COMI_svr_scaler25min.joblib', 'COMI_svr_scaler2880min.joblib', 'COMI_svr_scaler30min.joblib', 'COMI_svr_scaler40320min.joblib', 'COMI_svr_scaler4320min.joblib', 'COMI_svr_scaler4min.joblib', 'COMI_svr_scaler5min.joblib', 'COMI_svr_scaler60min.joblib', 'COMI_svr_scaler69min.joblib', 'COMI_svr_scaler7200min.joblib', 'COMI_svr_scaler80640min.joblib', 'COMI_svr_scaler_10080min.joblib', 'COMI_svr_scaler_120960min.joblib', 'COMI_svr_scaler_1440min.joblib', 'COMI_svr_scaler_15min.joblib', 'COMI_svr_scaler_20160min.joblib', 'COMI_svr_scaler_25min.joblib', 'COMI_svr_scaler_2880min.joblib', 'COMI_svr_scaler_30min.joblib', 'COMI_svr_scaler_40320min.joblib', 'COMI_svr_scaler_4320min.joblib', 'COMI_svr_scaler_4min.joblib', 'COMI_svr_scaler_5min.joblib', 'COMI_svr_scaler_60min.joblib', 'COMI_svr_scaler_69min.joblib', 'COMI_svr_scaler_7200min.joblib', 'COMI_svr_scaler_80640min.joblib', 'COMI_transformer_scaler10080min.joblib', 'COMI_transformer_scaler120960min.joblib', 'COMI_transformer_scaler1440min.joblib', 'COMI_transformer_scaler15min.joblib', 'COMI_transformer_scaler20160min.joblib', 'COMI_transformer_scaler25min.joblib', 'COMI_transformer_scaler2880min.joblib', 'COMI_transformer_scaler30min.joblib', 'COMI_transformer_scaler40320min.joblib', 'COMI_transformer_scaler4320min.joblib', 'COMI_transformer_scaler4min.joblib', 'COMI_transformer_scaler5min.joblib', 'COMI_transformer_scaler60min.joblib', 'COMI_transformer_scaler69min.joblib', 'COMI_transformer_scaler7200min.joblib', 'COMI_transformer_scaler80640min.joblib', 'COMI_transformer_scaler_10080.joblib', 'COMI_transformer_scaler_10080min.joblib', 'COMI_transformer_scaler_120960.joblib', 'COMI_transformer_scaler_120960min.joblib', 'COMI_transformer_scaler_1440.joblib', 'COMI_transformer_scaler_1440min.joblib', 'COMI_transformer_scaler_15.joblib', 'COMI_transformer_scaler_15min.joblib', 'COMI_transformer_scaler_20160.joblib', 'COMI_transformer_scaler_20160min.joblib', 'COMI_transformer_scaler_25.joblib', 'COMI_transformer_scaler_25min.joblib', 'COMI_transformer_scaler_2880.joblib', 'COMI_transformer_scaler_2880min.joblib', 'COMI_transformer_scaler_30.joblib', 'COMI_transformer_scaler_30min.joblib', 'COMI_transformer_scaler_4.joblib', 'COMI_transformer_scaler_40320.joblib', 'COMI_transformer_scaler_40320min.joblib', 'COMI_transformer_scaler_4320.joblib', 'COMI_transformer_scaler_4320min.joblib', 'COMI_transformer_scaler_4min.joblib', 'COMI_transformer_scaler_5.joblib', 'COMI_transformer_scaler_5min.joblib', 'COMI_transformer_scaler_60.joblib', 'COMI_transformer_scaler_60min.joblib', 'COMI_transformer_scaler_69.joblib', 'COMI_transformer_scaler_69min.joblib', 'COMI_transformer_scaler_7200.joblib', 'COMI_transformer_scaler_7200min.joblib', 'COMI_transformer_scaler_80640.joblib', 'COMI_transformer_scaler_80640min.joblib', 'COMI_xgb_10080min.joblib', 'COMI_xgb_120960min.joblib', 'COMI_xgb_1440min.joblib', 'COMI_xgb_15min.joblib', 'COMI_xgb_20160min.joblib', 'COMI_xgb_25min.joblib', 'COMI_xgb_2880min.joblib', 'COMI_xgb_30min.joblib', 'COMI_xgb_40320min.joblib', 'COMI_xgb_4320min.joblib', 'COMI_xgb_4min.joblib', 'COMI_xgb_5min.joblib', 'COMI_xgb_60min.joblib', 'COMI_xgb_69min.joblib', 'COMI_xgb_7200min.joblib', 'COMI_xgb_80640min.joblib', 'COMI_xgb_scaler10080min.joblib', 'COMI_xgb_scaler120960min.joblib', 'COMI_xgb_scaler1440min.joblib', 'COMI_xgb_scaler15min.joblib', 'COMI_xgb_scaler20160min.joblib', 'COMI_xgb_scaler25min.joblib', 'COMI_xgb_scaler2880min.joblib', 'COMI_xgb_scaler30min.joblib', 'COMI_xgb_scaler40320min.joblib', 'COMI_xgb_scaler4320min.joblib', 'COMI_xgb_scaler4min.joblib', 'COMI_xgb_scaler5min.joblib', 'COMI_xgb_scaler60min.joblib', 'COMI_xgb_scaler69min.joblib', 'COMI_xgb_scaler7200min.joblib', 'COMI_xgb_scaler80640min.joblib', 'COMI_xgb_scaler_10080min.joblib', 'COMI_xgb_scaler_120960min.joblib', 'COMI_xgb_scaler_1440min.joblib', 'COMI_xgb_scaler_15min.joblib', 'COMI_xgb_scaler_20160min.joblib', 'COMI_xgb_scaler_25min.joblib', 'COMI_xgb_scaler_2880min.joblib', 'COMI_xgb_scaler_30min.joblib', 'COMI_xgb_scaler_40320min.joblib', 'COMI_xgb_scaler_4320min.joblib', 'COMI_xgb_scaler_4min.joblib', 'COMI_xgb_scaler_5min.joblib', 'COMI_xgb_scaler_60min.joblib', 'COMI_xgb_scaler_69min.joblib', 'COMI_xgb_scaler_7200min.joblib', 'COMI_xgb_scaler_80640min.joblib']
2025-06-25 13:17:55,660 - models.predict - INFO - Skipping gb model for horizon 240 - model not trained for this horizon
2025-06-25 13:17:55,714 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-25 13:17:55,841 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-25 13:17:55,975 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:55,975 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-06-25 13:17:55,975 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-06-25 13:17:55,975 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-06-25 13:17:55,975 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-06-25 13:17:55,975 - app.models.predict - WARNING - No models have predictions for 240min horizon, using original ensemble
2025-06-25 13:17:56,025 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-25 13:17:56,170 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-25 13:17:56,381 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:56,381 - models.predict - INFO - Using RobustEnsembleModel for 240 minutes horizon
2025-06-25 13:17:56,400 - models.predict - WARNING - Failed to load RobustEnsembleModel: Model file not found: saved_models\COMI_ensemble_240min.joblib
2025-06-25 13:17:56,400 - models.predict - INFO - Creating fallback ensemble model for COMI with horizon 240
2025-06-25 13:17:56,400 - models.predict - INFO - Created fallback ensemble model with base price: 79.12
2025-06-25 13:17:56,400 - models.predict - INFO - Loading ensemble model for COMI with horizon 240
2025-06-25 13:17:56,400 - models.predict - INFO - Ensemble model already loaded
2025-06-25 13:17:56,419 - models.predict - INFO - Processing scikit-learn model prediction with shape: unknown
2025-06-25 13:17:56,419 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 73.67270020251956
2025-06-25 13:17:56,419 - models.predict - WARNING - Prediction 4078.9637459577084 is too far from current price 79.12, using fallback
2025-06-25 13:17:56,419 - models.predict - INFO - Prediction for 240 minutes horizon: 79.80521651305587
2025-06-25 13:17:56,419 - app.models.adaptive - INFO - No valid models for COMI with 1440min horizon, using equal weights
2025-06-25 13:17:56,419 - app.models.predict - INFO - Ensemble weights for COMI with 1440min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-25 13:17:56,469 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-25 13:17:56,592 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-25 13:17:56,742 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.03s
2025-06-25 13:17:56,742 - models.predict - INFO - Using scikit-learn rf model for 1440 minutes horizon
2025-06-25 13:17:56,742 - models.predict - INFO - Loading rf model for COMI with horizon 1440
2025-06-25 13:17:56,742 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_1440min.joblib
2025-06-25 13:17:56,759 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_1440min.joblib
2025-06-25 13:17:56,832 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-25 13:17:56,832 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-25 13:17:56,832 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-25 13:17:56,837 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-25 13:17:56,837 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.6861131644248962
2025-06-25 13:17:56,837 - models.predict - INFO - Prediction for 1440 minutes horizon: 79.29899976880476
2025-06-25 13:17:56,875 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-25 13:17:57,019 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-25 13:17:57,159 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:57,159 - models.predict - INFO - Using scikit-learn gb model for 1440 minutes horizon
2025-06-25 13:17:57,159 - models.predict - INFO - Loading gb model for COMI with horizon 1440
2025-06-25 13:17:57,159 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_1440min.joblib
2025-06-25 13:17:57,159 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_1440min.joblib
2025-06-25 13:17:57,198 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-25 13:17:57,209 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-25 13:17:57,209 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-25 13:17:57,209 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-25 13:17:57,209 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.68871785537216
2025-06-25 13:17:57,209 - models.predict - INFO - Prediction for 1440 minutes horizon: 79.44173682473877
2025-06-25 13:17:57,267 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-25 13:17:57,417 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-25 13:17:57,543 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:17:57,543 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-06-25 13:17:57,543 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-06-25 13:17:58,368 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-25 13:17:59,495 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:17:59,495 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7297780513763428
2025-06-25 13:17:59,495 - models.predict - INFO - Prediction for 1440 minutes horizon: 81.69183544003393
2025-06-25 13:17:59,495 - app.models.predict - INFO - Adaptive ensemble prediction for 1440min horizon: 80.14419067785914
2025-06-25 13:17:59,505 - app.models.adaptive - INFO - No valid models for COMI with 4320min horizon, using equal weights
2025-06-25 13:17:59,505 - app.models.predict - INFO - Ensemble weights for COMI with 4320min horizon: {'rf': 0.3333333333333333, 'gb': 0.3333333333333333, 'lstm': 0.3333333333333333}
2025-06-25 13:17:59,545 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-25 13:17:59,712 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-25 13:17:59,866 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.06 MB, VMS: 0.08 MB, Percent: 0.00%, Execution time: 0.02s
2025-06-25 13:17:59,866 - models.predict - INFO - Using scikit-learn rf model for 4320 minutes horizon
2025-06-25 13:17:59,866 - models.predict - INFO - Loading rf model for COMI with horizon 4320
2025-06-25 13:17:59,866 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_rf_4320min.joblib
2025-06-25 13:17:59,866 - models.sklearn_model - INFO - Loading model from saved_models\COMI_rf_4320min.joblib
2025-06-25 13:17:59,924 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-25 13:17:59,924 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. RandomForestRegressor expected <= 2.. Trying with prepared data.
2025-06-25 13:17:59,924 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-25 13:17:59,931 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-25 13:17:59,931 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.688961706161499
2025-06-25 13:17:59,933 - models.predict - INFO - Prediction for 4320 minutes horizon: 79.45509984724784
2025-06-25 13:17:59,983 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-25 13:18:00,127 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-25 13:18:00,262 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:00,262 - models.predict - INFO - Using scikit-learn gb model for 4320 minutes horizon
2025-06-25 13:18:00,262 - models.predict - INFO - Loading gb model for COMI with horizon 4320
2025-06-25 13:18:00,262 - models.sklearn_model - INFO - Attempting to load model from saved_models\COMI_gb_4320min.joblib
2025-06-25 13:18:00,262 - models.sklearn_model - INFO - Loading model from saved_models\COMI_gb_4320min.joblib
2025-06-25 13:18:00,296 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-25 13:18:00,318 - models.sklearn_model - WARNING - Prediction with original shape failed: Found array with dim 3. GradientBoostingRegressor expected <= 2.. Trying with prepared data.
2025-06-25 13:18:00,320 - models.sklearn_model - INFO - Reshaping 3D input with shape (1, 60, 7) to 2D
2025-06-25 13:18:00,320 - models.predict - INFO - Processing scikit-learn model prediction with shape: (1,)
2025-06-25 13:18:00,320 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.6908922457848714
2025-06-25 13:18:00,320 - models.predict - INFO - Prediction for 4320 minutes horizon: 79.56089341269697
2025-06-25 13:18:00,370 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-25 13:18:00,512 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-25 13:18:00,652 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:00,652 - models.predict - INFO - Loading lstm model for COMI with horizon 4320
2025-06-25 13:18:00,652 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_4320min.keras
2025-06-25 13:18:01,447 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-25 13:18:02,622 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:02,622 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7200455665588379
2025-06-25 13:18:02,622 - models.predict - INFO - Prediction for 4320 minutes horizon: 81.15849530183736
2025-06-25 13:18:02,627 - app.models.predict - INFO - Adaptive ensemble prediction for 4320min horizon: 80.05816285392739
2025-06-25 13:18:02,627 - app.models.predict - INFO - Prediction completed in 15.72 seconds
2025-06-25 13:18:02,627 - app.models.hybrid_predict - INFO - ML predictions generated for COMI
2025-06-25 13:18:02,627 - app.models.predict - INFO - Using specified model type: lstm
2025-06-25 13:18:02,681 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-25 13:18:02,879 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-25 13:18:03,039 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.02 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:03,041 - models.predict - INFO - Loading lstm model for COMI with horizon 30
2025-06-25 13:18:03,041 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_30min.keras
2025-06-25 13:18:03,870 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-25 13:18:05,004 - tensorflow - WARNING - 5 out of the last 5 calls to <function Model.make_predict_function.<locals>.predict_function at 0x000002831F6C6200> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-06-25 13:18:05,008 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:05,010 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.8537483811378479
2025-06-25 13:18:05,010 - models.predict - INFO - Prediction for 30 minutes horizon: 78.45002673099603
2025-06-25 13:18:05,010 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-25 13:18:05,160 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-25 13:18:05,300 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:05,300 - models.predict - INFO - Loading lstm model for COMI with horizon 60
2025-06-25 13:18:05,304 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_60min.keras
2025-06-25 13:18:06,107 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-25 13:18:07,577 - tensorflow - WARNING - 6 out of the last 6 calls to <function Model.make_predict_function.<locals>.predict_function at 0x000002831F6C6830> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.
2025-06-25 13:18:07,583 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:07,584 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 1.2493574619293213
2025-06-25 13:18:07,586 - models.predict - INFO - Prediction for 60 minutes horizon: 93.59790077522742
2025-06-25 13:18:07,586 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-25 13:18:07,861 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-25 13:18:08,128 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.01s
2025-06-25 13:18:08,130 - models.predict - INFO - Loading lstm model for COMI with horizon 240
2025-06-25 13:18:08,130 - models.lstm_model - ERROR - Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-06-25 13:18:08,130 - models.predict - WARNING - Model file not found or import error for lstm with horizon 240: Model not found at saved_models\COMI_lstm_240min.keras or saved_models\COMI_lstm_240min.h5
2025-06-25 13:18:08,132 - models.predict - INFO - Skipping lstm model for horizon 240 - model not trained for this horizon
2025-06-25 13:18:08,132 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-25 13:18:08,415 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-25 13:18:08,659 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:08,661 - models.predict - INFO - Loading lstm model for COMI with horizon 1440
2025-06-25 13:18:08,661 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_1440min.keras
2025-06-25 13:18:10,174 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-25 13:18:12,089 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:12,093 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7297780513763428
2025-06-25 13:18:12,094 - models.predict - INFO - Prediction for 1440 minutes horizon: 81.69183544003393
2025-06-25 13:18:12,096 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-25 13:18:12,367 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-25 13:18:12,584 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:12,589 - models.predict - INFO - Loading lstm model for COMI with horizon 4320
2025-06-25 13:18:12,592 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_lstm_4320min.keras
2025-06-25 13:18:13,745 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-25 13:18:14,983 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:14,983 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7200455665588379
2025-06-25 13:18:14,983 - models.predict - INFO - Prediction for 4320 minutes horizon: 81.15849530183736
2025-06-25 13:18:14,990 - app.models.predict - INFO - Prediction completed in 12.36 seconds
2025-06-25 13:18:14,993 - app.models.predict - INFO - Using specified model type: bilstm
2025-06-25 13:18:15,040 - models.predict - INFO - Making predictions for 30 minutes horizon
2025-06-25 13:18:15,257 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_30_scaler.pkl (0.52 KB)
2025-06-25 13:18:15,410 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:15,412 - models.predict - INFO - Loading bilstm model for COMI with horizon 30
2025-06-25 13:18:15,412 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_30min.keras
2025-06-25 13:18:17,130 - models.predict - INFO - Successfully loaded model for COMI with horizon 30
2025-06-25 13:18:19,323 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:19,323 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 1.094914197921753
2025-06-25 13:18:19,323 - models.predict - INFO - Prediction for 30 minutes horizon: 87.68426728257316
2025-06-25 13:18:19,325 - models.predict - INFO - Making predictions for 60 minutes horizon
2025-06-25 13:18:19,527 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_60_scaler.pkl (0.52 KB)
2025-06-25 13:18:19,675 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.00 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:19,676 - models.predict - INFO - Loading bilstm model for COMI with horizon 60
2025-06-25 13:18:19,681 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_60min.keras
2025-06-25 13:18:21,790 - models.predict - INFO - Successfully loaded model for COMI with horizon 60
2025-06-25 13:18:24,715 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:24,716 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 1.1382977962493896
2025-06-25 13:18:24,716 - models.predict - INFO - Prediction for 60 minutes horizon: 89.34542551922891
2025-06-25 13:18:24,716 - models.predict - INFO - Making predictions for 240 minutes horizon
2025-06-25 13:18:24,971 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_240_scaler.pkl (0.53 KB)
2025-06-25 13:18:25,115 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.02 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:25,117 - models.predict - INFO - Loading bilstm model for COMI with horizon 240
2025-06-25 13:18:25,119 - models.lstm_model - ERROR - Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-06-25 13:18:25,119 - models.predict - WARNING - Model file not found or import error for bilstm with horizon 240: Model not found at saved_models\COMI_bilstm_240min.keras or saved_models\COMI_bilstm_240min.h5
2025-06-25 13:18:25,119 - models.predict - INFO - Skipping bilstm model for horizon 240 - model not trained for this horizon
2025-06-25 13:18:25,119 - models.predict - INFO - Making predictions for 1440 minutes horizon
2025-06-25 13:18:25,320 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_1440_scaler.pkl (0.52 KB)
2025-06-25 13:18:25,457 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.02 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:25,459 - models.predict - INFO - Loading bilstm model for COMI with horizon 1440
2025-06-25 13:18:25,464 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_1440min.keras
2025-06-25 13:18:27,090 - models.predict - INFO - Successfully loaded model for COMI with horizon 1440
2025-06-25 13:18:29,242 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:29,242 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7134897112846375
2025-06-25 13:18:29,242 - models.predict - INFO - Prediction for 1440 minutes horizon: 80.79923445288644
2025-06-25 13:18:29,244 - models.predict - INFO - Making predictions for 4320 minutes horizon
2025-06-25 13:18:29,424 - app.utils.data_processing - INFO - Found scaler at saved_models\COMI_4320_scaler.pkl (0.52 KB)
2025-06-25 13:18:29,557 - app.utils.memory_management - INFO - Memory usage for load_scaler: RSS: 0.00 MB, VMS: 0.03 MB, Percent: 0.00%, Execution time: 0.00s
2025-06-25 13:18:29,574 - models.predict - INFO - Loading bilstm model for COMI with horizon 4320
2025-06-25 13:18:29,574 - models.lstm_model - INFO - Loading model from native Keras format: saved_models\COMI_bilstm_4320min.keras
2025-06-25 13:18:31,161 - models.predict - INFO - Successfully loaded model for COMI with horizon 4320
2025-06-25 13:18:33,313 - models.predict - INFO - Processing TensorFlow model prediction with shape: (1, 1)
2025-06-25 13:18:33,313 - models.predict - INFO - Current price: 79.12, Predicted scaled value: 0.7178929448127747
2025-06-25 13:18:33,313 - models.predict - INFO - Prediction for 4320 minutes horizon: 81.04053163674483
2025-06-25 13:18:33,313 - app.models.predict - INFO - Prediction completed in 18.32 seconds
2025-06-25 13:18:33,313 - app.models.hybrid_predict - INFO - DL predictions generated for COMI
2025-06-25 13:18:33,922 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-25 13:18:34,004 - cmdstanpy - DEBUG - Adding TBB (D:\AI Stocks Bot\python310_venv\lib\site-packages\prophet\stan_model\cmdstan-2.33.1\stan\lib\stan_math\lib\tbb) to PATH
2025-06-25 13:18:34,011 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-25 13:18:34,061 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\hhhpr96o.json
2025-06-25 13:18:34,118 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\nm4t4m94.json
2025-06-25 13:18:34,120 - cmdstanpy - DEBUG - idx 0
2025-06-25 13:18:34,120 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-25 13:18:34,120 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=19619', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\hhhpr96o.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\nm4t4m94.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\prophet_modelp4u6064w\\prophet_model-20250625131834.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-25 13:18:34,120 - cmdstanpy - INFO - Chain [1] start processing
2025-06-25 13:18:40,605 - cmdstanpy - INFO - Chain [1] done processing
2025-06-25 13:18:40,674 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-25 13:18:40,764 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-25 13:18:40,774 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-25 13:18:40,797 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\9x2sjg2g.json
2025-06-25 13:18:40,868 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\vue1aclf.json
2025-06-25 13:18:40,870 - cmdstanpy - DEBUG - idx 0
2025-06-25 13:18:40,872 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-25 13:18:40,872 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=71855', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\9x2sjg2g.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\vue1aclf.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\prophet_modelryhlymr9\\prophet_model-20250625131840.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-25 13:18:40,872 - cmdstanpy - INFO - Chain [1] start processing
2025-06-25 13:18:41,854 - cmdstanpy - INFO - Chain [1] done processing
2025-06-25 13:18:41,908 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-25 13:18:41,990 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-25 13:18:41,998 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-25 13:18:42,021 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\wshpbkvp.json
2025-06-25 13:18:42,096 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\_u4n5i3c.json
2025-06-25 13:18:42,098 - cmdstanpy - DEBUG - idx 0
2025-06-25 13:18:42,098 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-25 13:18:42,100 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=90028', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\wshpbkvp.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\_u4n5i3c.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\prophet_modelmqhouuue\\prophet_model-20250625131842.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-25 13:18:42,101 - cmdstanpy - INFO - Chain [1] start processing
2025-06-25 13:18:43,015 - cmdstanpy - INFO - Chain [1] done processing
2025-06-25 13:18:43,056 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-25 13:18:43,129 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-25 13:18:43,134 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-25 13:18:43,149 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\v21u_so6.json
2025-06-25 13:18:43,204 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\clymc8eh.json
2025-06-25 13:18:43,204 - cmdstanpy - DEBUG - idx 0
2025-06-25 13:18:43,204 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-25 13:18:43,204 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=14608', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\v21u_so6.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\clymc8eh.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\prophet_modelrocwkapy\\prophet_model-20250625131843.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-25 13:18:43,204 - cmdstanpy - INFO - Chain [1] start processing
2025-06-25 13:18:44,039 - cmdstanpy - INFO - Chain [1] done processing
2025-06-25 13:18:44,084 - cmdstanpy - DEBUG - cmd: where.exe tbb.dll
cwd: None
2025-06-25 13:18:44,154 - cmdstanpy - DEBUG - TBB already found in load path
2025-06-25 13:18:44,159 - prophet - INFO - Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.
2025-06-25 13:18:44,171 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\32hobnk5.json
2025-06-25 13:18:44,222 - cmdstanpy - DEBUG - input tempfile: C:\Users\<USER>\AppData\Local\Temp\tmpjxvv3mtm\q16j3fr6.json
2025-06-25 13:18:44,222 - cmdstanpy - DEBUG - idx 0
2025-06-25 13:18:44,222 - cmdstanpy - DEBUG - running CmdStan, num_threads: None
2025-06-25 13:18:44,222 - cmdstanpy - DEBUG - CmdStan args: ['D:\\AI Stocks Bot\\python310_venv\\Lib\\site-packages\\prophet\\stan_model\\prophet_model.bin', 'random', 'seed=23779', 'data', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\32hobnk5.json', 'init=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\q16j3fr6.json', 'output', 'file=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpjxvv3mtm\\prophet_modelc42rtb97\\prophet_model-20250625131844.csv', 'method=optimize', 'algorithm=lbfgs', 'iter=10000']
2025-06-25 13:18:44,222 - cmdstanpy - INFO - Chain [1] start processing
2025-06-25 13:18:45,057 - cmdstanpy - INFO - Chain [1] done processing
2025-06-25 13:18:45,112 - app.models.hybrid_predict - INFO - Statistical predictions generated for COMI
2025-06-25 13:18:45,112 - app.models.hybrid_predict - INFO - Trend-adjusted predictions generated for COMI
2025-06-25 13:18:45,112 - app.pages.ai_advisor - INFO - Hybrid predictions generated for 5 horizons
2025-06-25 13:18:45,112 - app.pages.ai_advisor - INFO - Detecting market regime for COMI
2025-06-25 13:18:45,112 - app.pages.ai_advisor - INFO - Market regime detected: sideways
2025-06-25 13:18:45,126 - app.pages.ai_advisor - INFO - Created enhanced basic fallback predictions with 3 models
2025-06-25 13:18:45,126 - app.pages.ai_advisor - INFO - Consensus predictions generated for 5 horizons
2025-06-25 13:18:47,964 - app.utils.memory_management - INFO - Memory before cleanup: 584.75 MB
2025-06-25 13:18:48,316 - app.utils.memory_management - INFO - Garbage collection: collected 59133 objects
2025-06-25 13:18:48,316 - app.utils.memory_management - INFO - Memory after cleanup: 479.50 MB (freed 105.26 MB)
2025-06-25 15:24:13,283 - app - INFO - Cleaning up resources...
2025-06-25 15:24:13,284 - app.utils.memory_management - INFO - Memory before cleanup: 193.16 MB
2025-06-25 15:24:13,605 - app.utils.memory_management - INFO - Garbage collection: collected 396 objects
2025-06-25 15:24:13,607 - app.utils.memory_management - INFO - Memory after cleanup: 403.87 MB (freed -210.71 MB)
2025-06-25 15:24:13,607 - app - INFO - Application shutdown complete
