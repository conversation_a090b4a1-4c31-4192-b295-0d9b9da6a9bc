2025-06-03 11:58:56,880 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-03 11:59:05,305 - app - INFO - Memory management utilities loaded
2025-06-03 11:59:05,325 - app - INFO - Error handling utilities loaded
2025-06-03 11:59:05,343 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 11:59:05,347 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 11:59:05,349 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 11:59:05,350 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 11:59:05,367 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 11:59:05,368 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 11:59:05,372 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 11:59:05,373 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 11:59:05,375 - app - INFO - Applied NumPy fix
2025-06-03 11:59:05,399 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 11:59:05,400 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 11:59:05,402 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 11:59:05,412 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 11:59:05,415 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 11:59:05,416 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 11:59:05,425 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 11:59:05,428 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 11:59:31,059 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 11:59:31,059 - app - INFO - Applied TensorFlow fix
2025-06-03 11:59:31,086 - app.config - INFO - Configuration initialized
2025-06-03 11:59:31,110 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 11:59:31,130 - models.train - INFO - TensorFlow test successful
2025-06-03 11:59:36,643 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 11:59:36,643 - models.train - INFO - Transformer model is available
2025-06-03 11:59:36,645 - models.train - INFO - Using TensorFlow-based models
2025-06-03 11:59:36,657 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 11:59:36,658 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 11:59:36,682 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 11:59:38,739 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 11:59:38,740 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 11:59:38,740 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 11:59:38,742 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 11:59:38,743 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 11:59:38,744 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 11:59:38,744 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 11:59:38,746 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 11:59:38,747 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 11:59:38,750 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 11:59:39,220 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 11:59:39,237 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 11:59:40,248 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 11:59:44,041 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 11:59:44,116 - app.utils.session_state - INFO - Initializing session state
2025-06-03 11:59:44,118 - app.utils.session_state - INFO - Session state initialized
2025-06-03 11:59:45,646 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 11:59:45,703 - app.utils.memory_management - INFO - Memory before cleanup: 428.73 MB
2025-06-03 11:59:46,006 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 11:59:46,010 - app.utils.memory_management - INFO - Memory after cleanup: 429.20 MB (freed -0.46 MB)
2025-06-03 12:01:44,153 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:44,228 - app.utils.memory_management - INFO - Memory before cleanup: 432.46 MB
2025-06-03 12:01:44,456 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:01:44,461 - app.utils.memory_management - INFO - Memory after cleanup: 432.46 MB (freed 0.00 MB)
2025-06-03 12:01:48,977 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:49,154 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.13 seconds
2025-06-03 12:01:49,158 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:01:49,160 - app - INFO - Data shape: (581, 36)
2025-06-03 12:01:49,171 - app - INFO - File COMI contains 2025 data
2025-06-03 12:01:49,226 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-03 12:01:49,228 - app - INFO - Features shape: (581, 36)
2025-06-03 12:01:49,261 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-03 12:01:49,267 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:01:49,268 - app - INFO - Data shape: (581, 36)
2025-06-03 12:01:49,272 - app - INFO - File COMI contains 2025 data
2025-06-03 12:01:49,288 - app.utils.memory_management - INFO - Memory before cleanup: 436.91 MB
2025-06-03 12:01:49,531 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-03 12:01:49,533 - app.utils.memory_management - INFO - Memory after cleanup: 436.91 MB (freed 0.00 MB)
2025-06-03 12:01:49,758 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:01:49,858 - app.utils.memory_management - INFO - Memory before cleanup: 438.00 MB
2025-06-03 12:01:50,125 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:01:50,130 - app.utils.memory_management - INFO - Memory after cleanup: 438.00 MB (freed 0.00 MB)
2025-06-03 12:02:08,058 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:10,102 - app.utils.memory_management - INFO - Memory before cleanup: 438.50 MB
2025-06-03 12:02:10,284 - app.utils.memory_management - INFO - Garbage collection: collected 313 objects
2025-06-03 12:02:10,286 - app.utils.memory_management - INFO - Memory after cleanup: 438.50 MB (freed 0.00 MB)
2025-06-03 12:02:51,072 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:51,079 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:02:51,081 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:02:51,111 - app.utils.memory_management - INFO - Memory before cleanup: 438.37 MB
2025-06-03 12:02:51,334 - app.utils.memory_management - INFO - Garbage collection: collected 177 objects
2025-06-03 12:02:51,336 - app.utils.memory_management - INFO - Memory after cleanup: 438.37 MB (freed 0.00 MB)
2025-06-03 12:02:56,379 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:56,416 - app.utils.memory_management - INFO - Memory before cleanup: 438.57 MB
2025-06-03 12:02:56,647 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-03 12:02:56,647 - app.utils.memory_management - INFO - Memory after cleanup: 438.57 MB (freed 0.00 MB)
2025-06-03 12:02:57,524 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:57,556 - app.utils.memory_management - INFO - Memory before cleanup: 438.59 MB
2025-06-03 12:02:57,811 - app.utils.memory_management - INFO - Garbage collection: collected 184 objects
2025-06-03 12:02:57,813 - app.utils.memory_management - INFO - Memory after cleanup: 438.59 MB (freed 0.00 MB)
2025-06-03 12:02:58,029 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:02:58,096 - app.utils.memory_management - INFO - Memory before cleanup: 438.59 MB
2025-06-03 12:02:58,306 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:02:58,307 - app.utils.memory_management - INFO - Memory after cleanup: 438.59 MB (freed 0.00 MB)
2025-06-03 12:03:00,343 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:00,439 - app.utils.memory_management - INFO - Memory before cleanup: 438.60 MB
2025-06-03 12:03:00,673 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-03 12:03:00,673 - app.utils.memory_management - INFO - Memory after cleanup: 438.60 MB (freed 0.00 MB)
2025-06-03 12:03:05,176 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:05,231 - app.utils.memory_management - INFO - Memory before cleanup: 438.61 MB
2025-06-03 12:03:05,458 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-03 12:03:05,458 - app.utils.memory_management - INFO - Memory after cleanup: 438.61 MB (freed 0.00 MB)
2025-06-03 12:03:06,801 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:03:24,118 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-03 12:03:24,120 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-03 12:03:24,121 - app.utils.memory_management - INFO - Memory before cleanup: 438.81 MB
2025-06-03 12:03:24,319 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-03 12:03:24,322 - app.utils.memory_management - INFO - Memory after cleanup: 438.81 MB (freed 0.00 MB)
2025-06-03 12:03:24,327 - app.utils.memory_management - INFO - Memory before cleanup: 438.82 MB
2025-06-03 12:03:24,626 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 12:03:24,628 - app.utils.memory_management - INFO - Memory after cleanup: 438.82 MB (freed 0.00 MB)
2025-06-03 12:26:17,672 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:26:17,677 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:26:17,680 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:26:17,695 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:26:17,710 - app.utils.memory_management - INFO - Memory before cleanup: 433.84 MB
2025-06-03 12:26:17,909 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-03 12:26:17,910 - app.utils.memory_management - INFO - Memory after cleanup: 433.84 MB (freed 0.00 MB)
2025-06-03 12:27:29,337 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:27:29,347 - app - INFO - Memory management utilities loaded
2025-06-03 12:27:29,351 - app - INFO - Error handling utilities loaded
2025-06-03 12:27:29,355 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:27:29,358 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:27:29,359 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:27:29,360 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:28:20,797 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:28:27,693 - app - INFO - Memory management utilities loaded
2025-06-03 12:28:27,696 - app - INFO - Error handling utilities loaded
2025-06-03 12:28:27,701 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:28:27,705 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:28:27,706 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:28:27,708 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:28:27,714 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:28:27,724 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:28:27,731 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:28:27,739 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:28:27,742 - app - INFO - Applied NumPy fix
2025-06-03 12:28:27,749 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:28:27,753 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:28:27,754 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:28:27,757 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:28:27,759 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:28:27,767 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:28:27,776 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:28:27,779 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:28:43,957 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:28:43,958 - app - INFO - Applied TensorFlow fix
2025-06-03 12:28:43,959 - app.config - INFO - Configuration initialized
2025-06-03 12:28:43,965 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 12:28:43,975 - models.train - INFO - TensorFlow test successful
2025-06-03 12:28:48,635 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 12:28:48,635 - models.train - INFO - Transformer model is available
2025-06-03 12:28:48,636 - models.train - INFO - Using TensorFlow-based models
2025-06-03 12:28:48,637 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 12:28:48,637 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 12:28:48,640 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 12:28:50,122 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:28:50,122 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:28:50,123 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:28:50,124 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:28:50,349 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 12:28:50,351 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:28:50,352 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:28:50,714 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 12:28:52,909 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 12:28:52,949 - app.utils.memory_management - INFO - Memory before cleanup: 426.55 MB
2025-06-03 12:28:53,115 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:28:53,116 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:28:53,117 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:28:53,118 - app.utils.memory_management - INFO - Memory after cleanup: 426.91 MB (freed -0.36 MB)
2025-06-03 12:28:54,664 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:28:54,691 - app.utils.memory_management - INFO - Memory before cleanup: 431.00 MB
2025-06-03 12:28:54,919 - app.utils.memory_management - INFO - Garbage collection: collected 69 objects
2025-06-03 12:28:54,922 - app.utils.memory_management - INFO - Memory after cleanup: 431.00 MB (freed 0.00 MB)
2025-06-03 12:29:00,198 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:29:00,253 - app.utils.memory_management - INFO - Memory before cleanup: 433.99 MB
2025-06-03 12:29:00,450 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:29:00,451 - app.utils.memory_management - INFO - Memory after cleanup: 434.03 MB (freed -0.04 MB)
2025-06-03 12:29:08,961 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:29:24,569 - app.utils.memory_management - INFO - Memory before cleanup: 449.51 MB
2025-06-03 12:29:24,774 - app.utils.memory_management - INFO - Garbage collection: collected 980 objects
2025-06-03 12:29:24,774 - app.utils.memory_management - INFO - Memory after cleanup: 449.51 MB (freed 0.00 MB)
2025-06-03 12:36:43,358 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:36:44,065 - app - INFO - Memory management utilities loaded
2025-06-03 12:36:44,065 - app - INFO - Error handling utilities loaded
2025-06-03 12:36:44,065 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:36:44,070 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:36:44,072 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:36:44,072 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:36:44,072 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:36:44,072 - app - INFO - Applied NumPy fix
2025-06-03 12:36:44,074 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:36:44,078 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:36:44,080 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:36:44,081 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:36:44,081 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:36:44,081 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:36:48,996 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:36:48,996 - app - INFO - Applied TensorFlow fix
2025-06-03 12:36:49,761 - app - INFO - Cleaning up resources...
2025-06-03 12:36:49,764 - app.utils.memory_management - INFO - Memory before cleanup: 320.98 MB
2025-06-03 12:36:49,881 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-03 12:36:49,881 - app.utils.memory_management - INFO - Memory after cleanup: 321.32 MB (freed -0.34 MB)
2025-06-03 12:36:49,881 - app - INFO - Application shutdown complete
2025-06-03 12:40:55,936 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:40:55,971 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:40:55,996 - app.utils.memory_management - INFO - Memory before cleanup: 447.53 MB
2025-06-03 12:40:56,224 - app.utils.memory_management - INFO - Garbage collection: collected 214 objects
2025-06-03 12:40:56,225 - app.utils.memory_management - INFO - Memory after cleanup: 447.56 MB (freed -0.03 MB)
2025-06-03 12:40:59,884 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:41:09,709 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-03 12:41:09,712 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-03 12:41:09,714 - app.utils.memory_management - INFO - Memory before cleanup: 448.61 MB
2025-06-03 12:41:09,924 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-06-03 12:41:09,926 - app.utils.memory_management - INFO - Memory after cleanup: 448.61 MB (freed 0.00 MB)
2025-06-03 12:41:09,926 - app.utils.memory_management - INFO - Memory before cleanup: 448.61 MB
2025-06-03 12:41:10,148 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 12:41:10,149 - app.utils.memory_management - INFO - Memory after cleanup: 448.61 MB (freed 0.00 MB)
2025-06-03 12:43:40,200 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:43:40,878 - app - INFO - Memory management utilities loaded
2025-06-03 12:43:40,878 - app - INFO - Error handling utilities loaded
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:43:40,884 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:43:40,888 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:43:40,888 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:43:40,888 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:43:40,889 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:43:40,889 - app - INFO - Applied NumPy fix
2025-06-03 12:43:40,890 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:43:40,890 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:43:40,891 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:43:40,892 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:43:45,367 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:43:45,367 - app - INFO - Applied TensorFlow fix
2025-06-03 12:43:46,155 - app - INFO - Cleaning up resources...
2025-06-03 12:43:46,161 - app.utils.memory_management - INFO - Memory before cleanup: 321.38 MB
2025-06-03 12:43:46,277 - app.utils.memory_management - INFO - Garbage collection: collected 371 objects
2025-06-03 12:43:46,279 - app.utils.memory_management - INFO - Memory after cleanup: 321.73 MB (freed -0.35 MB)
2025-06-03 12:43:46,279 - app - INFO - Application shutdown complete
2025-06-03 12:44:02,992 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:44:03,002 - app - INFO - Memory management utilities loaded
2025-06-03 12:44:03,007 - app - INFO - Error handling utilities loaded
2025-06-03 12:44:03,009 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:44:03,011 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:44:03,013 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:44:03,015 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:44:32,676 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:44:34,480 - app - INFO - Memory management utilities loaded
2025-06-03 12:44:34,483 - app - INFO - Error handling utilities loaded
2025-06-03 12:44:34,484 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:44:34,486 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:44:34,486 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:44:34,489 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:44:34,491 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:44:34,493 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:44:34,494 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:44:34,497 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:44:34,497 - app - INFO - Applied NumPy fix
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:44:34,499 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:44:34,500 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:44:34,501 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:44:34,503 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:44:39,833 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:44:39,833 - app - INFO - Applied TensorFlow fix
2025-06-03 12:44:39,835 - app.config - INFO - Configuration initialized
2025-06-03 12:44:39,839 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 12:44:39,852 - models.train - INFO - TensorFlow test successful
2025-06-03 12:44:40,475 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 12:44:40,475 - models.train - INFO - Transformer model is available
2025-06-03 12:44:40,477 - models.train - INFO - Using TensorFlow-based models
2025-06-03 12:44:40,478 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 12:44:40,478 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 12:44:40,478 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 12:44:40,835 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:44:40,835 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:44:40,836 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:44:40,837 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:44:40,942 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 12:44:40,944 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:40,944 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:41,290 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 12:44:41,962 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 12:44:41,991 - app.utils.memory_management - INFO - Memory before cleanup: 426.16 MB
2025-06-03 12:44:41,992 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:44:42,139 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:44:42,140 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:44:42,141 - app.utils.memory_management - INFO - Memory after cleanup: 426.54 MB (freed -0.38 MB)
2025-06-03 12:44:43,422 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:44:43,443 - app.utils.memory_management - INFO - Memory before cleanup: 430.34 MB
2025-06-03 12:44:43,651 - app.utils.memory_management - INFO - Garbage collection: collected 70 objects
2025-06-03 12:44:43,651 - app.utils.memory_management - INFO - Memory after cleanup: 430.34 MB (freed 0.00 MB)
2025-06-03 12:44:48,865 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:44:48,903 - app.utils.memory_management - INFO - Memory before cleanup: 433.75 MB
2025-06-03 12:44:49,104 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:44:49,125 - app.utils.memory_management - INFO - Memory after cleanup: 433.79 MB (freed -0.04 MB)
2025-06-03 12:44:53,578 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:45:00,951 - app.utils.memory_management - INFO - Memory before cleanup: 450.56 MB
2025-06-03 12:45:01,163 - app.utils.memory_management - INFO - Garbage collection: collected 998 objects
2025-06-03 12:45:01,164 - app.utils.memory_management - INFO - Memory after cleanup: 450.56 MB (freed 0.00 MB)
2025-06-03 12:49:04,520 - app - INFO - Cleaning up resources...
2025-06-03 12:49:04,521 - app.utils.memory_management - INFO - Memory before cleanup: 452.26 MB
2025-06-03 12:49:04,856 - app.utils.memory_management - INFO - Garbage collection: collected 294 objects
2025-06-03 12:49:04,860 - app.utils.memory_management - INFO - Memory after cleanup: 452.26 MB (freed 0.00 MB)
2025-06-03 12:49:04,865 - app - INFO - Application shutdown complete
2025-06-03 12:50:16,779 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 12:50:18,404 - app - INFO - Memory management utilities loaded
2025-06-03 12:50:18,406 - app - INFO - Error handling utilities loaded
2025-06-03 12:50:18,406 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 12:50:18,408 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 12:50:18,408 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 12:50:18,410 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 12:50:18,411 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 12:50:18,412 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 12:50:18,412 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 12:50:18,413 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 12:50:18,413 - app - INFO - Applied NumPy fix
2025-06-03 12:50:18,418 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:50:18,418 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:50:18,420 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:50:18,420 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 12:50:18,420 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:50:18,421 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:50:18,421 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:50:18,423 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 12:50:23,636 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 12:50:23,636 - app - INFO - Applied TensorFlow fix
2025-06-03 12:50:23,638 - app.config - INFO - Configuration initialized
2025-06-03 12:50:23,642 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 12:50:23,652 - models.train - INFO - TensorFlow test successful
2025-06-03 12:50:24,246 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 12:50:24,246 - models.train - INFO - Transformer model is available
2025-06-03 12:50:24,246 - models.train - INFO - Using TensorFlow-based models
2025-06-03 12:50:24,246 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 12:50:24,246 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 12:50:24,246 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 12:50:24,600 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:50:24,601 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 12:50:24,601 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 12:50:24,601 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 12:50:24,601 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 12:50:24,602 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 12:50:24,716 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 12:50:24,718 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:25,099 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 12:50:25,768 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 12:50:25,796 - app.utils.session_state - INFO - Initializing session state
2025-06-03 12:50:25,797 - app.utils.session_state - INFO - Session state initialized
2025-06-03 12:50:27,089 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 12:50:27,104 - app.utils.memory_management - INFO - Memory before cleanup: 431.12 MB
2025-06-03 12:50:27,313 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:50:27,314 - app.utils.memory_management - INFO - Memory after cleanup: 431.12 MB (freed -0.00 MB)
2025-06-03 12:50:33,075 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:33,111 - app.utils.memory_management - INFO - Memory before cleanup: 434.16 MB
2025-06-03 12:50:33,314 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 12:50:33,347 - app.utils.memory_management - INFO - Memory after cleanup: 434.16 MB (freed 0.00 MB)
2025-06-03 12:50:34,242 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:34,310 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.05 seconds
2025-06-03 12:50:34,312 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:50:34,314 - app - INFO - Data shape: (581, 36)
2025-06-03 12:50:34,318 - app - INFO - File COMI contains 2025 data
2025-06-03 12:50:34,372 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-03 12:50:34,373 - app - INFO - Features shape: (581, 36)
2025-06-03 12:50:34,406 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.03 seconds
2025-06-03 12:50:34,408 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 12:50:34,408 - app - INFO - Data shape: (581, 36)
2025-06-03 12:50:34,410 - app - INFO - File COMI contains 2025 data
2025-06-03 12:50:34,412 - app.utils.memory_management - INFO - Memory before cleanup: 438.49 MB
2025-06-03 12:50:34,618 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-03 12:50:34,619 - app.utils.memory_management - INFO - Memory after cleanup: 438.53 MB (freed -0.04 MB)
2025-06-03 12:50:34,798 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:34,861 - app.utils.memory_management - INFO - Memory before cleanup: 439.58 MB
2025-06-03 12:50:35,054 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 12:50:35,055 - app.utils.memory_management - INFO - Memory after cleanup: 439.56 MB (freed 0.02 MB)
2025-06-03 12:50:36,677 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:36,713 - app.utils.memory_management - INFO - Memory before cleanup: 440.11 MB
2025-06-03 12:50:36,911 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-03 12:50:36,912 - app.utils.memory_management - INFO - Memory after cleanup: 440.11 MB (freed 0.00 MB)
2025-06-03 12:50:39,532 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 12:50:46,452 - app.utils.memory_management - INFO - Memory before cleanup: 452.37 MB
2025-06-03 12:50:46,666 - app.utils.memory_management - INFO - Garbage collection: collected 1082 objects
2025-06-03 12:50:46,668 - app.utils.memory_management - INFO - Memory after cleanup: 452.37 MB (freed 0.00 MB)
2025-06-03 13:02:44,064 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:02:44,072 - app - INFO - Memory management utilities loaded
2025-06-03 13:02:44,078 - app - INFO - Error handling utilities loaded
2025-06-03 13:02:44,080 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:02:44,081 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:02:44,081 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:02:44,082 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:03:10,942 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:03:12,702 - app - INFO - Memory management utilities loaded
2025-06-03 13:03:12,706 - app - INFO - Error handling utilities loaded
2025-06-03 13:03:12,708 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:03:12,708 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:03:12,710 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:03:12,710 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:03:12,710 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 13:03:12,711 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 13:03:12,711 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 13:03:12,711 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 13:03:12,712 - app - INFO - Applied NumPy fix
2025-06-03 13:03:12,713 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:03:12,714 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:03:12,714 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:03:12,715 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 13:03:12,715 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:03:12,715 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:03:12,715 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:03:12,716 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 13:03:17,970 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 13:03:17,970 - app - INFO - Applied TensorFlow fix
2025-06-03 13:03:17,973 - app.config - INFO - Configuration initialized
2025-06-03 13:03:17,978 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 13:03:17,987 - models.train - INFO - TensorFlow test successful
2025-06-03 13:03:18,740 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 13:03:18,740 - models.train - INFO - Transformer model is available
2025-06-03 13:03:18,743 - models.train - INFO - Using TensorFlow-based models
2025-06-03 13:03:18,747 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 13:03:18,747 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 13:03:18,750 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 13:03:19,154 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:03:19,155 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:03:19,155 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:03:19,155 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:03:19,155 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:03:19,155 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 13:03:19,156 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 13:03:19,156 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:03:19,156 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:03:19,156 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:03:19,280 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 13:03:19,282 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:19,283 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:19,754 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 13:03:20,646 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 13:03:20,681 - app.utils.session_state - INFO - Initializing session state
2025-06-03 13:03:20,686 - app.utils.memory_management - INFO - Memory before cleanup: 428.08 MB
2025-06-03 13:03:20,688 - app.utils.session_state - INFO - Session state initialized
2025-06-03 13:03:20,869 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-03 13:03:20,871 - app.utils.memory_management - INFO - Memory after cleanup: 428.13 MB (freed -0.05 MB)
2025-06-03 13:03:22,430 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 13:03:22,439 - app.utils.memory_management - INFO - Memory before cleanup: 431.63 MB
2025-06-03 13:03:22,612 - app.utils.memory_management - INFO - Garbage collection: collected 69 objects
2025-06-03 13:03:22,613 - app.utils.memory_management - INFO - Memory after cleanup: 431.63 MB (freed 0.00 MB)
2025-06-03 13:03:29,627 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:29,672 - app.utils.memory_management - INFO - Memory before cleanup: 434.52 MB
2025-06-03 13:03:29,922 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 13:03:29,925 - app.utils.memory_management - INFO - Memory after cleanup: 434.56 MB (freed -0.04 MB)
2025-06-03 13:03:31,048 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:31,095 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-03 13:03:31,098 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 13:03:31,100 - app - INFO - Data shape: (581, 36)
2025-06-03 13:03:31,102 - app - INFO - File COMI contains 2025 data
2025-06-03 13:03:31,132 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-06-03 13:03:31,132 - app - INFO - Features shape: (581, 36)
2025-06-03 13:03:31,160 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-03 13:03:31,162 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 13:03:31,163 - app - INFO - Data shape: (581, 36)
2025-06-03 13:03:31,163 - app - INFO - File COMI contains 2025 data
2025-06-03 13:03:31,166 - app.utils.memory_management - INFO - Memory before cleanup: 438.82 MB
2025-06-03 13:03:31,358 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-03 13:03:31,359 - app.utils.memory_management - INFO - Memory after cleanup: 438.82 MB (freed 0.00 MB)
2025-06-03 13:03:31,548 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:31,612 - app.utils.memory_management - INFO - Memory before cleanup: 439.88 MB
2025-06-03 13:03:31,803 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 13:03:31,803 - app.utils.memory_management - INFO - Memory after cleanup: 439.86 MB (freed 0.02 MB)
2025-06-03 13:03:34,441 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:34,485 - app.utils.memory_management - INFO - Memory before cleanup: 440.55 MB
2025-06-03 13:03:34,685 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-03 13:03:34,686 - app.utils.memory_management - INFO - Memory after cleanup: 440.55 MB (freed 0.00 MB)
2025-06-03 13:03:37,753 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:03:48,110 - app.utils.memory_management - INFO - Memory before cleanup: 444.76 MB
2025-06-03 13:03:48,281 - app.utils.memory_management - INFO - Garbage collection: collected 721 objects
2025-06-03 13:03:48,281 - app.utils.memory_management - INFO - Memory after cleanup: 444.76 MB (freed 0.00 MB)
2025-06-03 13:03:48,281 - app.utils.memory_management - INFO - Memory before cleanup: 444.76 MB
2025-06-03 13:03:48,444 - app.utils.memory_management - INFO - Garbage collection: collected 1454 objects
2025-06-03 13:03:48,445 - app.utils.memory_management - INFO - Memory after cleanup: 444.77 MB (freed -0.01 MB)
2025-06-03 13:10:27,767 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:10:27,773 - app - INFO - Memory management utilities loaded
2025-06-03 13:10:27,775 - app - INFO - Error handling utilities loaded
2025-06-03 13:10:27,780 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:10:27,781 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:10:27,781 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:10:27,781 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:11:00,498 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:11:03,201 - app - INFO - Memory management utilities loaded
2025-06-03 13:11:03,204 - app - INFO - Error handling utilities loaded
2025-06-03 13:11:03,206 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:11:03,211 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:11:03,213 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:11:03,216 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:11:03,217 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 13:11:03,220 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 13:11:03,222 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 13:11:03,223 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 13:11:03,226 - app - INFO - Applied NumPy fix
2025-06-03 13:11:03,230 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:11:03,232 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:11:03,235 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:11:03,237 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 13:11:03,239 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:11:03,241 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:11:03,242 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:11:03,245 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 13:11:09,878 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 13:11:09,878 - app - INFO - Applied TensorFlow fix
2025-06-03 13:11:09,880 - app.config - INFO - Configuration initialized
2025-06-03 13:11:09,884 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 13:11:09,893 - models.train - INFO - TensorFlow test successful
2025-06-03 13:11:10,423 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 13:11:10,423 - models.train - INFO - Transformer model is available
2025-06-03 13:11:10,423 - models.train - INFO - Using TensorFlow-based models
2025-06-03 13:11:10,425 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 13:11:10,425 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 13:11:10,427 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 13:11:10,767 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:11:10,767 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:11:10,767 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 13:11:10,768 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:11:10,769 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:11:10,769 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:11:10,867 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 13:11:10,869 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:11:10,869 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:11:11,205 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 13:11:11,844 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 13:11:11,881 - app.utils.memory_management - INFO - Memory before cleanup: 427.60 MB
2025-06-03 13:11:11,885 - app.utils.session_state - INFO - Initializing session state
2025-06-03 13:11:12,045 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 13:11:12,047 - app.utils.session_state - INFO - Session state initialized
2025-06-03 13:11:12,048 - app.utils.memory_management - INFO - Memory after cleanup: 427.70 MB (freed -0.10 MB)
2025-06-03 13:11:13,395 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 13:11:13,406 - app.utils.memory_management - INFO - Memory before cleanup: 431.56 MB
2025-06-03 13:11:13,584 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 13:11:13,585 - app.utils.memory_management - INFO - Memory after cleanup: 431.56 MB (freed 0.00 MB)
2025-06-03 13:11:16,091 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:11:16,128 - app.utils.memory_management - INFO - Memory before cleanup: 434.82 MB
2025-06-03 13:11:16,300 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-03 13:11:16,301 - app.utils.memory_management - INFO - Memory after cleanup: 434.86 MB (freed -0.04 MB)
2025-06-03 13:11:19,633 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:11:26,990 - app.utils.session_state - ERROR - Error tracked: app_crash - Invalid property specified for object of type plotly.graph_objs.Candlestick: 'hovertemplate'

Did you mean "hovertext"?

    Valid properties:
        close
            Sets the close values.
        closesrc
            Sets the source reference on Chart Studio Cloud for
            `close`.
        customdata
            Assigns extra data each datum. This may be useful when
            listening to hover, click and selection events. Note
            that, "scatter" traces also appends customdata items in
            the markers DOM elements
        customdatasrc
            Sets the source reference on Chart Studio Cloud for
            `customdata`.
        decreasing
            :class:`plotly.graph_objects.candlestick.Decreasing`
            instance or dict with compatible properties
        high
            Sets the high values.
        highsrc
            Sets the source reference on Chart Studio Cloud for
            `high`.
        hoverinfo
            Determines which trace information appear on hover. If
            `none` or `skip` are set, no information is displayed
            upon hovering. But, if `none` is set, click and hover
            events are still fired.
        hoverinfosrc
            Sets the source reference on Chart Studio Cloud for
            `hoverinfo`.
        hoverlabel
            :class:`plotly.graph_objects.candlestick.Hoverlabel`
            instance or dict with compatible properties
        hovertext
            Same as `text`.
        hovertextsrc
            Sets the source reference on Chart Studio Cloud for
            `hovertext`.
        ids
            Assigns id labels to each datum. These ids for object
            constancy of data points during animation. Should be an
            array of strings, not numbers or any other type.
        idssrc
            Sets the source reference on Chart Studio Cloud for
            `ids`.
        increasing
            :class:`plotly.graph_objects.candlestick.Increasing`
            instance or dict with compatible properties
        legend
            Sets the reference to a legend to show this trace in.
            References to these legends are "legend", "legend2",
            "legend3", etc. Settings for these legends are set in
            the layout, under `layout.legend`, `layout.legend2`,
            etc.
        legendgroup
            Sets the legend group for this trace. Traces and shapes
            part of the same legend group hide/show at the same
            time when toggling legend items.
        legendgrouptitle
            :class:`plotly.graph_objects.candlestick.Legendgrouptit
            le` instance or dict with compatible properties
        legendrank
            Sets the legend rank for this trace. Items and groups
            with smaller ranks are presented on top/left side while
            with "reversed" `legend.traceorder` they are on
            bottom/right side. The default legendrank is 1000, so
            that you can use ranks less than 1000 to place certain
            items before all unranked items, and ranks greater than
            1000 to go after all unranked items. When having
            unranked or equal rank items shapes would be displayed
            after traces i.e. according to their order in data and
            layout.
        legendwidth
            Sets the width (in px or fraction) of the legend for
            this trace.
        line
            :class:`plotly.graph_objects.candlestick.Line` instance
            or dict with compatible properties
        low
            Sets the low values.
        lowsrc
            Sets the source reference on Chart Studio Cloud for
            `low`.
        meta
            Assigns extra meta information associated with this
            trace that can be used in various text attributes.
            Attributes such as trace `name`, graph, axis and
            colorbar `title.text`, annotation `text`
            `rangeselector`, `updatemenues` and `sliders` `label`
            text all support `meta`. To access the trace `meta`
            values in an attribute in the same trace, simply use
            `%{meta[i]}` where `i` is the index or key of the
            `meta` item in question. To access trace `meta` in
            layout attributes, use `%{data[n[.meta[i]}` where `i`
            is the index or key of the `meta` and `n` is the trace
            index.
        metasrc
            Sets the source reference on Chart Studio Cloud for
            `meta`.
        name
            Sets the trace name. The trace name appears as the
            legend item and on hover.
        opacity
            Sets the opacity of the trace.
        open
            Sets the open values.
        opensrc
            Sets the source reference on Chart Studio Cloud for
            `open`.
        selectedpoints
            Array containing integer indices of selected points.
            Has an effect only for traces that support selections.
            Note that an empty array means an empty selection where
            the `unselected` are turned on for all points, whereas,
            any other non-array values means no selection all where
            the `selected` and `unselected` styles have no effect.
        showlegend
            Determines whether or not an item corresponding to this
            trace is shown in the legend.
        stream
            :class:`plotly.graph_objects.candlestick.Stream`
            instance or dict with compatible properties
        text
            Sets hover text elements associated with each sample
            point. If a single string, the same string appears over
            all the data points. If an array of string, the items
            are mapped in order to this trace's sample points.
        textsrc
            Sets the source reference on Chart Studio Cloud for
            `text`.
        uid
            Assign an id to this trace, Use this to provide object
            constancy between traces during animations and
            transitions.
        uirevision
            Controls persistence of some user-driven changes to the
            trace: `constraintrange` in `parcoords` traces, as well
            as some `editable: true` modifications such as `name`
            and `colorbar.title`. Defaults to `layout.uirevision`.
            Note that other user-driven trace attribute changes are
            controlled by `layout` attributes: `trace.visible` is
            controlled by `layout.legend.uirevision`,
            `selectedpoints` is controlled by
            `layout.selectionrevision`, and `colorbar.(x|y)`
            (accessible with `config: {editable: true}`) is
            controlled by `layout.editrevision`. Trace changes are
            tracked by `uid`, which only falls back on trace index
            if no `uid` is provided. So if your app can add/remove
            traces before the end of the `data` array, such that
            the same trace has a different index, you can still
            preserve user-driven changes if you give each trace a
            `uid` that stays with it as it moves.
        visible
            Determines whether or not this trace is visible. If
            "legendonly", the trace is not drawn, but can appear as
            a legend item (provided that the legend itself is
            visible).
        whiskerwidth
            Sets the width of the whiskers relative to the box'
            width. For example, with 1, the whiskers are as wide as
            the box(es).
        x
            Sets the x coordinates. If absent, linear coordinate
            will be generated.
        xaxis
            Sets a reference between this trace's x coordinates and
            a 2D cartesian x axis. If "x" (the default value), the
            x coordinates refer to `layout.xaxis`. If "x2", the x
            coordinates refer to `layout.xaxis2`, and so on.
        xcalendar
            Sets the calendar system to use with `x` date data.
        xhoverformat
            Sets the hover text formatting rulefor `x`  using d3
            formatting mini-languages which are very similar to
            those in Python. For numbers, see:
            https://github.com/d3/d3-format/tree/v1.4.5#d3-format.
            And for dates see: https://github.com/d3/d3-time-
            format/tree/v2.2.3#locale_format. We add two items to
            d3's date formatter: "%h" for half of the year as a
            decimal number as well as "%{n}f" for fractional
            seconds with n digits. For example, *2016-10-13
            09:15:23.456* with tickformat "%H~%M~%S.%2f" would
            display *09~15~23.46*By default the values are
            formatted using `xaxis.hoverformat`.
        xperiod
            Only relevant when the axis `type` is "date". Sets the
            period positioning in milliseconds or "M<n>" on the x
            axis. Special values in the form of "M<n>" could be
            used to declare the number of months. In this case `n`
            must be a positive integer.
        xperiod0
            Only relevant when the axis `type` is "date". Sets the
            base for period positioning in milliseconds or date
            string on the x0 axis. When `x0period` is round number
            of weeks, the `x0period0` by default would be on a
            Sunday i.e. 2000-01-02, otherwise it would be at
            2000-01-01.
        xperiodalignment
            Only relevant when the axis `type` is "date". Sets the
            alignment of data points on the x axis.
        xsrc
            Sets the source reference on Chart Studio Cloud for
            `x`.
        yaxis
            Sets a reference between this trace's y coordinates and
            a 2D cartesian y axis. If "y" (the default value), the
            y coordinates refer to `layout.yaxis`. If "y2", the y
            coordinates refer to `layout.yaxis2`, and so on.
        yhoverformat
            Sets the hover text formatting rulefor `y`  using d3
            formatting mini-languages which are very similar to
            those in Python. For numbers, see:
            https://github.com/d3/d3-format/tree/v1.4.5#d3-format.
            And for dates see: https://github.com/d3/d3-time-
            format/tree/v2.2.3#locale_format. We add two items to
            d3's date formatter: "%h" for half of the year as a
            decimal number as well as "%{n}f" for fractional
            seconds with n digits. For example, *2016-10-13
            09:15:23.456* with tickformat "%H~%M~%S.%2f" would
            display *09~15~23.46*By default the values are
            formatted using `yaxis.hoverformat`.
        zorder
            Sets the layer on which this trace is displayed,
            relative to other SVG traces on the same subplot. SVG
            traces with higher `zorder` appear in front of those
            with lower `zorder`.
        
Did you mean "hovertext"?

Bad property path:
hovertemplate
^^^^^^^^^^^^^
2025-06-03 13:11:26,992 - app - ERROR - Application crashed: Invalid property specified for object of type plotly.graph_objs.Candlestick: 'hovertemplate'

Did you mean "hovertext"?

    Valid properties:
        close
            Sets the close values.
        closesrc
            Sets the source reference on Chart Studio Cloud for
            `close`.
        customdata
            Assigns extra data each datum. This may be useful when
            listening to hover, click and selection events. Note
            that, "scatter" traces also appends customdata items in
            the markers DOM elements
        customdatasrc
            Sets the source reference on Chart Studio Cloud for
            `customdata`.
        decreasing
            :class:`plotly.graph_objects.candlestick.Decreasing`
            instance or dict with compatible properties
        high
            Sets the high values.
        highsrc
            Sets the source reference on Chart Studio Cloud for
            `high`.
        hoverinfo
            Determines which trace information appear on hover. If
            `none` or `skip` are set, no information is displayed
            upon hovering. But, if `none` is set, click and hover
            events are still fired.
        hoverinfosrc
            Sets the source reference on Chart Studio Cloud for
            `hoverinfo`.
        hoverlabel
            :class:`plotly.graph_objects.candlestick.Hoverlabel`
            instance or dict with compatible properties
        hovertext
            Same as `text`.
        hovertextsrc
            Sets the source reference on Chart Studio Cloud for
            `hovertext`.
        ids
            Assigns id labels to each datum. These ids for object
            constancy of data points during animation. Should be an
            array of strings, not numbers or any other type.
        idssrc
            Sets the source reference on Chart Studio Cloud for
            `ids`.
        increasing
            :class:`plotly.graph_objects.candlestick.Increasing`
            instance or dict with compatible properties
        legend
            Sets the reference to a legend to show this trace in.
            References to these legends are "legend", "legend2",
            "legend3", etc. Settings for these legends are set in
            the layout, under `layout.legend`, `layout.legend2`,
            etc.
        legendgroup
            Sets the legend group for this trace. Traces and shapes
            part of the same legend group hide/show at the same
            time when toggling legend items.
        legendgrouptitle
            :class:`plotly.graph_objects.candlestick.Legendgrouptit
            le` instance or dict with compatible properties
        legendrank
            Sets the legend rank for this trace. Items and groups
            with smaller ranks are presented on top/left side while
            with "reversed" `legend.traceorder` they are on
            bottom/right side. The default legendrank is 1000, so
            that you can use ranks less than 1000 to place certain
            items before all unranked items, and ranks greater than
            1000 to go after all unranked items. When having
            unranked or equal rank items shapes would be displayed
            after traces i.e. according to their order in data and
            layout.
        legendwidth
            Sets the width (in px or fraction) of the legend for
            this trace.
        line
            :class:`plotly.graph_objects.candlestick.Line` instance
            or dict with compatible properties
        low
            Sets the low values.
        lowsrc
            Sets the source reference on Chart Studio Cloud for
            `low`.
        meta
            Assigns extra meta information associated with this
            trace that can be used in various text attributes.
            Attributes such as trace `name`, graph, axis and
            colorbar `title.text`, annotation `text`
            `rangeselector`, `updatemenues` and `sliders` `label`
            text all support `meta`. To access the trace `meta`
            values in an attribute in the same trace, simply use
            `%{meta[i]}` where `i` is the index or key of the
            `meta` item in question. To access trace `meta` in
            layout attributes, use `%{data[n[.meta[i]}` where `i`
            is the index or key of the `meta` and `n` is the trace
            index.
        metasrc
            Sets the source reference on Chart Studio Cloud for
            `meta`.
        name
            Sets the trace name. The trace name appears as the
            legend item and on hover.
        opacity
            Sets the opacity of the trace.
        open
            Sets the open values.
        opensrc
            Sets the source reference on Chart Studio Cloud for
            `open`.
        selectedpoints
            Array containing integer indices of selected points.
            Has an effect only for traces that support selections.
            Note that an empty array means an empty selection where
            the `unselected` are turned on for all points, whereas,
            any other non-array values means no selection all where
            the `selected` and `unselected` styles have no effect.
        showlegend
            Determines whether or not an item corresponding to this
            trace is shown in the legend.
        stream
            :class:`plotly.graph_objects.candlestick.Stream`
            instance or dict with compatible properties
        text
            Sets hover text elements associated with each sample
            point. If a single string, the same string appears over
            all the data points. If an array of string, the items
            are mapped in order to this trace's sample points.
        textsrc
            Sets the source reference on Chart Studio Cloud for
            `text`.
        uid
            Assign an id to this trace, Use this to provide object
            constancy between traces during animations and
            transitions.
        uirevision
            Controls persistence of some user-driven changes to the
            trace: `constraintrange` in `parcoords` traces, as well
            as some `editable: true` modifications such as `name`
            and `colorbar.title`. Defaults to `layout.uirevision`.
            Note that other user-driven trace attribute changes are
            controlled by `layout` attributes: `trace.visible` is
            controlled by `layout.legend.uirevision`,
            `selectedpoints` is controlled by
            `layout.selectionrevision`, and `colorbar.(x|y)`
            (accessible with `config: {editable: true}`) is
            controlled by `layout.editrevision`. Trace changes are
            tracked by `uid`, which only falls back on trace index
            if no `uid` is provided. So if your app can add/remove
            traces before the end of the `data` array, such that
            the same trace has a different index, you can still
            preserve user-driven changes if you give each trace a
            `uid` that stays with it as it moves.
        visible
            Determines whether or not this trace is visible. If
            "legendonly", the trace is not drawn, but can appear as
            a legend item (provided that the legend itself is
            visible).
        whiskerwidth
            Sets the width of the whiskers relative to the box'
            width. For example, with 1, the whiskers are as wide as
            the box(es).
        x
            Sets the x coordinates. If absent, linear coordinate
            will be generated.
        xaxis
            Sets a reference between this trace's x coordinates and
            a 2D cartesian x axis. If "x" (the default value), the
            x coordinates refer to `layout.xaxis`. If "x2", the x
            coordinates refer to `layout.xaxis2`, and so on.
        xcalendar
            Sets the calendar system to use with `x` date data.
        xhoverformat
            Sets the hover text formatting rulefor `x`  using d3
            formatting mini-languages which are very similar to
            those in Python. For numbers, see:
            https://github.com/d3/d3-format/tree/v1.4.5#d3-format.
            And for dates see: https://github.com/d3/d3-time-
            format/tree/v2.2.3#locale_format. We add two items to
            d3's date formatter: "%h" for half of the year as a
            decimal number as well as "%{n}f" for fractional
            seconds with n digits. For example, *2016-10-13
            09:15:23.456* with tickformat "%H~%M~%S.%2f" would
            display *09~15~23.46*By default the values are
            formatted using `xaxis.hoverformat`.
        xperiod
            Only relevant when the axis `type` is "date". Sets the
            period positioning in milliseconds or "M<n>" on the x
            axis. Special values in the form of "M<n>" could be
            used to declare the number of months. In this case `n`
            must be a positive integer.
        xperiod0
            Only relevant when the axis `type` is "date". Sets the
            base for period positioning in milliseconds or date
            string on the x0 axis. When `x0period` is round number
            of weeks, the `x0period0` by default would be on a
            Sunday i.e. 2000-01-02, otherwise it would be at
            2000-01-01.
        xperiodalignment
            Only relevant when the axis `type` is "date". Sets the
            alignment of data points on the x axis.
        xsrc
            Sets the source reference on Chart Studio Cloud for
            `x`.
        yaxis
            Sets a reference between this trace's y coordinates and
            a 2D cartesian y axis. If "y" (the default value), the
            y coordinates refer to `layout.yaxis`. If "y2", the y
            coordinates refer to `layout.yaxis2`, and so on.
        yhoverformat
            Sets the hover text formatting rulefor `y`  using d3
            formatting mini-languages which are very similar to
            those in Python. For numbers, see:
            https://github.com/d3/d3-format/tree/v1.4.5#d3-format.
            And for dates see: https://github.com/d3/d3-time-
            format/tree/v2.2.3#locale_format. We add two items to
            d3's date formatter: "%h" for half of the year as a
            decimal number as well as "%{n}f" for fractional
            seconds with n digits. For example, *2016-10-13
            09:15:23.456* with tickformat "%H~%M~%S.%2f" would
            display *09~15~23.46*By default the values are
            formatted using `yaxis.hoverformat`.
        zorder
            Sets the layer on which this trace is displayed,
            relative to other SVG traces on the same subplot. SVG
            traces with higher `zorder` appear in front of those
            with lower `zorder`.
        
Did you mean "hovertext"?

Bad property path:
hovertemplate
^^^^^^^^^^^^^
2025-06-03 13:11:26,996 - app.utils.memory_management - INFO - Memory before cleanup: 453.59 MB
2025-06-03 13:11:27,180 - app.utils.memory_management - INFO - Garbage collection: collected 759 objects
2025-06-03 13:11:27,181 - app.utils.memory_management - INFO - Memory after cleanup: 453.59 MB (freed 0.00 MB)
2025-06-03 13:11:27,182 - app.utils.memory_management - INFO - Memory before cleanup: 453.59 MB
2025-06-03 13:11:27,348 - app.utils.memory_management - INFO - Garbage collection: collected 1919 objects
2025-06-03 13:11:27,349 - app.utils.memory_management - INFO - Memory after cleanup: 453.59 MB (freed -0.01 MB)
2025-06-03 13:13:17,826 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:13:17,832 - app - INFO - Memory management utilities loaded
2025-06-03 13:13:17,834 - app - INFO - Error handling utilities loaded
2025-06-03 13:13:17,835 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:13:17,836 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:13:17,836 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:13:17,837 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:14:11,186 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 13:14:12,985 - app - INFO - Memory management utilities loaded
2025-06-03 13:14:12,987 - app - INFO - Error handling utilities loaded
2025-06-03 13:14:12,989 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 13:14:12,989 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 13:14:12,991 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 13:14:12,991 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 13:14:12,991 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 13:14:12,996 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 13:14:13,000 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 13:14:13,002 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 13:14:13,004 - app - INFO - Applied NumPy fix
2025-06-03 13:14:13,006 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:14:13,010 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:14:13,014 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:14:13,016 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 13:14:13,018 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:14:13,020 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:14:13,022 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:14:13,024 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 13:14:18,958 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 13:14:18,958 - app - INFO - Applied TensorFlow fix
2025-06-03 13:14:18,960 - app.config - INFO - Configuration initialized
2025-06-03 13:14:18,964 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 13:14:18,976 - models.train - INFO - TensorFlow test successful
2025-06-03 13:14:19,622 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 13:14:19,622 - models.train - INFO - Transformer model is available
2025-06-03 13:14:19,624 - models.train - INFO - Using TensorFlow-based models
2025-06-03 13:14:19,624 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 13:14:19,624 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 13:14:19,626 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 13:14:19,992 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:14:19,994 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 13:14:19,994 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 13:14:19,994 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 13:14:19,994 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 13:14:19,994 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 13:14:19,994 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 13:14:19,994 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 13:14:19,994 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 13:14:19,994 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 13:14:20,108 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 13:14:20,112 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:14:20,490 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 13:14:21,240 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 13:14:21,272 - app.utils.session_state - INFO - Initializing session state
2025-06-03 13:14:21,272 - app.utils.session_state - INFO - Session state initialized
2025-06-03 13:14:24,418 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 13:14:24,436 - app.utils.memory_management - INFO - Memory before cleanup: 430.84 MB
2025-06-03 13:14:24,667 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 13:14:24,667 - app.utils.memory_management - INFO - Memory after cleanup: 431.20 MB (freed -0.37 MB)
2025-06-03 13:14:28,111 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:14:28,149 - app.utils.memory_management - INFO - Memory before cleanup: 434.18 MB
2025-06-03 13:14:28,360 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 13:14:28,360 - app.utils.memory_management - INFO - Memory after cleanup: 434.18 MB (freed 0.00 MB)
2025-06-03 13:14:31,606 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:14:39,313 - app.utils.memory_management - INFO - Memory before cleanup: 451.80 MB
2025-06-03 13:14:39,561 - app.utils.memory_management - INFO - Garbage collection: collected 1085 objects
2025-06-03 13:14:39,562 - app.utils.memory_management - INFO - Memory after cleanup: 451.84 MB (freed -0.04 MB)
2025-06-03 13:15:33,474 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:15:33,529 - app.utils.memory_management - INFO - Memory before cleanup: 453.42 MB
2025-06-03 13:15:33,849 - app.utils.memory_management - INFO - Garbage collection: collected 227 objects
2025-06-03 13:15:33,851 - app.utils.memory_management - INFO - Memory after cleanup: 453.42 MB (freed 0.00 MB)
2025-06-03 13:15:37,083 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 13:15:43,516 - app.utils.memory_management - INFO - Memory before cleanup: 453.47 MB
2025-06-03 13:15:43,679 - app.utils.memory_management - INFO - Garbage collection: collected 1132 objects
2025-06-03 13:15:43,679 - app.utils.memory_management - INFO - Memory after cleanup: 453.47 MB (freed 0.00 MB)
2025-06-03 14:17:00,126 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:17:00,160 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 14:17:11,505 - app.utils.memory_management - INFO - Memory before cleanup: 450.00 MB
2025-06-03 14:17:11,755 - app.utils.memory_management - INFO - Garbage collection: collected 1270 objects
2025-06-03 14:17:11,756 - app.utils.memory_management - INFO - Memory after cleanup: 450.02 MB (freed -0.01 MB)
2025-06-03 14:17:54,654 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:17:54,660 - app.utils.session_state - INFO - Initializing session state
2025-06-03 14:17:54,661 - app.utils.session_state - INFO - Session state initialized
2025-06-03 14:17:54,683 - app.utils.memory_management - INFO - Memory before cleanup: 450.37 MB
2025-06-03 14:17:54,881 - app.utils.memory_management - INFO - Garbage collection: collected 229 objects
2025-06-03 14:17:54,882 - app.utils.memory_management - INFO - Memory after cleanup: 450.37 MB (freed 0.00 MB)
2025-06-03 14:18:07,148 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:18:07,181 - app.utils.memory_management - INFO - Memory before cleanup: 451.16 MB
2025-06-03 14:18:07,423 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-03 14:18:07,423 - app.utils.memory_management - INFO - Memory after cleanup: 451.16 MB (freed 0.00 MB)
2025-06-03 14:18:10,285 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:18:16,277 - app.utils.memory_management - INFO - Memory before cleanup: 451.19 MB
2025-06-03 14:18:16,501 - app.utils.memory_management - INFO - Garbage collection: collected 1308 objects
2025-06-03 14:18:16,501 - app.utils.memory_management - INFO - Memory after cleanup: 451.19 MB (freed 0.00 MB)
2025-06-03 14:25:54,909 - app - INFO - Cleaning up resources...
2025-06-03 14:25:54,911 - app.utils.memory_management - INFO - Memory before cleanup: 450.87 MB
2025-06-03 14:25:55,243 - app.utils.memory_management - INFO - Garbage collection: collected 424 objects
2025-06-03 14:25:55,255 - app.utils.memory_management - INFO - Memory after cleanup: 450.87 MB (freed 0.00 MB)
2025-06-03 14:25:55,282 - app - INFO - Application shutdown complete
2025-06-03 14:27:04,667 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 14:27:07,262 - app - INFO - Memory management utilities loaded
2025-06-03 14:27:07,275 - app - INFO - Error handling utilities loaded
2025-06-03 14:27:07,279 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 14:27:07,297 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 14:27:07,298 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 14:27:07,301 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 14:27:07,302 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 14:27:07,303 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 14:27:07,305 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 14:27:07,305 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 14:27:07,306 - app - INFO - Applied NumPy fix
2025-06-03 14:27:07,307 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 14:27:07,309 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 14:27:07,309 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 14:27:07,311 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 14:27:07,314 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 14:27:07,315 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 14:27:07,316 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 14:27:07,317 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 14:27:13,264 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 14:27:13,266 - app - INFO - Applied TensorFlow fix
2025-06-03 14:27:13,270 - app.config - INFO - Configuration initialized
2025-06-03 14:27:13,293 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 14:27:13,311 - models.train - INFO - TensorFlow test successful
2025-06-03 14:27:14,055 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 14:27:14,057 - models.train - INFO - Transformer model is available
2025-06-03 14:27:14,059 - models.train - INFO - Using TensorFlow-based models
2025-06-03 14:27:14,061 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 14:27:14,061 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 14:27:14,066 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 14:27:14,497 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 14:27:14,498 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 14:27:14,498 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 14:27:14,498 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 14:27:14,498 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 14:27:14,498 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 14:27:14,499 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 14:27:14,499 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 14:27:14,499 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 14:27:14,500 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 14:27:14,632 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 14:27:14,635 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:27:15,058 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 14:27:15,964 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 14:27:15,999 - app.utils.session_state - INFO - Initializing session state
2025-06-03 14:27:16,001 - app.utils.session_state - INFO - Session state initialized
2025-06-03 14:27:17,346 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 14:27:17,365 - app.utils.memory_management - INFO - Memory before cleanup: 431.10 MB
2025-06-03 14:27:17,640 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 14:27:17,643 - app.utils.memory_management - INFO - Memory after cleanup: 431.10 MB (freed -0.00 MB)
2025-06-03 14:27:20,335 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:27:20,372 - app.utils.memory_management - INFO - Memory before cleanup: 434.43 MB
2025-06-03 14:27:20,571 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 14:27:20,572 - app.utils.memory_management - INFO - Memory after cleanup: 434.43 MB (freed 0.00 MB)
2025-06-03 14:27:23,671 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:27:35,009 - app.utils.memory_management - INFO - Memory before cleanup: 451.96 MB
2025-06-03 14:27:35,200 - app.utils.memory_management - INFO - Garbage collection: collected 1063 objects
2025-06-03 14:27:35,200 - app.utils.memory_management - INFO - Memory after cleanup: 452.00 MB (freed -0.04 MB)
2025-06-03 14:40:33,321 - app - INFO - Cleaning up resources...
2025-06-03 14:40:33,322 - app.utils.memory_management - INFO - Memory before cleanup: 453.45 MB
2025-06-03 14:40:33,523 - app.utils.memory_management - INFO - Garbage collection: collected 300 objects
2025-06-03 14:40:33,523 - app.utils.memory_management - INFO - Memory after cleanup: 453.45 MB (freed 0.00 MB)
2025-06-03 14:40:33,523 - app - INFO - Application shutdown complete
2025-06-03 14:41:25,666 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 14:41:27,536 - app - INFO - Memory management utilities loaded
2025-06-03 14:41:27,538 - app - INFO - Error handling utilities loaded
2025-06-03 14:41:27,542 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 14:41:27,547 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 14:41:27,548 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 14:41:27,552 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 14:41:27,553 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 14:41:27,554 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 14:41:27,554 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 14:41:27,555 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 14:41:27,555 - app - INFO - Applied NumPy fix
2025-06-03 14:41:27,556 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 14:41:27,556 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 14:41:27,557 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 14:41:27,557 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 14:41:27,558 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 14:41:27,558 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 14:41:27,558 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 14:41:27,559 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 14:41:35,231 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 14:41:35,231 - app - INFO - Applied TensorFlow fix
2025-06-03 14:41:35,237 - app.config - INFO - Configuration initialized
2025-06-03 14:41:35,243 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 14:41:35,259 - models.train - INFO - TensorFlow test successful
2025-06-03 14:41:36,024 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 14:41:36,024 - models.train - INFO - Transformer model is available
2025-06-03 14:41:36,026 - models.train - INFO - Using TensorFlow-based models
2025-06-03 14:41:36,028 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 14:41:36,028 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 14:41:36,032 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 14:41:36,462 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 14:41:36,462 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 14:41:36,466 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 14:41:36,468 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 14:41:36,474 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 14:41:36,474 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 14:41:36,478 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 14:41:36,480 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 14:41:36,482 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 14:41:36,482 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 14:41:36,637 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 14:41:36,641 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:41:37,083 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 14:41:37,934 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 14:41:37,982 - app.utils.session_state - INFO - Initializing session state
2025-06-03 14:41:37,983 - app.utils.session_state - INFO - Session state initialized
2025-06-03 14:41:40,541 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 14:41:40,603 - app.utils.memory_management - INFO - Memory before cleanup: 431.21 MB
2025-06-03 14:41:40,994 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 14:41:40,995 - app.utils.memory_management - INFO - Memory after cleanup: 431.24 MB (freed -0.03 MB)
2025-06-03 14:41:45,443 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:41:45,487 - app.utils.memory_management - INFO - Memory before cleanup: 434.11 MB
2025-06-03 14:41:45,739 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 14:41:45,739 - app.utils.memory_management - INFO - Memory after cleanup: 434.11 MB (freed 0.00 MB)
2025-06-03 14:41:49,059 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:41:58,936 - app.utils.memory_management - INFO - Memory before cleanup: 450.70 MB
2025-06-03 14:41:59,199 - app.utils.memory_management - INFO - Garbage collection: collected 1012 objects
2025-06-03 14:41:59,199 - app.utils.memory_management - INFO - Memory after cleanup: 450.74 MB (freed -0.04 MB)
2025-06-03 14:56:04,495 - app - INFO - Cleaning up resources...
2025-06-03 14:56:04,497 - app.utils.memory_management - INFO - Memory before cleanup: 452.31 MB
2025-06-03 14:56:04,703 - app.utils.memory_management - INFO - Garbage collection: collected 319 objects
2025-06-03 14:56:04,703 - app.utils.memory_management - INFO - Memory after cleanup: 452.29 MB (freed 0.02 MB)
2025-06-03 14:56:04,703 - app - INFO - Application shutdown complete
2025-06-03 14:56:10,094 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 14:56:13,387 - app - INFO - Memory management utilities loaded
2025-06-03 14:56:13,395 - app - INFO - Error handling utilities loaded
2025-06-03 14:56:13,397 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 14:56:13,401 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 14:56:13,403 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 14:56:13,403 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 14:56:13,415 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 14:56:13,425 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 14:56:13,431 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 14:56:13,435 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 14:56:13,436 - app - INFO - Applied NumPy fix
2025-06-03 14:56:13,446 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 14:56:13,454 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 14:56:13,464 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 14:56:13,470 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 14:56:13,480 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 14:56:13,482 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 14:56:13,486 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 14:56:13,492 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 14:56:21,257 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 14:56:21,258 - app - INFO - Applied TensorFlow fix
2025-06-03 14:56:21,259 - app.config - INFO - Configuration initialized
2025-06-03 14:56:21,263 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 14:56:21,274 - models.train - INFO - TensorFlow test successful
2025-06-03 14:56:22,013 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 14:56:22,014 - models.train - INFO - Transformer model is available
2025-06-03 14:56:22,015 - models.train - INFO - Using TensorFlow-based models
2025-06-03 14:56:22,018 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 14:56:22,020 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 14:56:22,023 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 14:56:22,491 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 14:56:22,492 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 14:56:22,492 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 14:56:22,492 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 14:56:22,492 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 14:56:22,492 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 14:56:22,493 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 14:56:22,493 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 14:56:22,493 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 14:56:22,493 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 14:56:22,645 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 14:56:22,647 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:56:23,123 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 14:56:23,795 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 14:56:23,854 - app.utils.session_state - INFO - Initializing session state
2025-06-03 14:56:23,856 - app.utils.session_state - INFO - Session state initialized
2025-06-03 14:56:25,258 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 14:56:25,273 - app.utils.memory_management - INFO - Memory before cleanup: 431.44 MB
2025-06-03 14:56:25,463 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 14:56:25,463 - app.utils.memory_management - INFO - Memory after cleanup: 431.44 MB (freed -0.00 MB)
2025-06-03 14:57:10,349 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:57:10,410 - app.utils.memory_management - INFO - Memory before cleanup: 434.09 MB
2025-06-03 14:57:10,631 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 14:57:10,632 - app.utils.memory_management - INFO - Memory after cleanup: 434.09 MB (freed 0.00 MB)
2025-06-03 14:57:13,578 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 14:57:24,158 - app.components.ai_pattern_recognition - ERROR - Error in pattern detection: 'PremiumDiscountZone' object has no attribute 'get'
2025-06-03 14:57:25,463 - app.utils.memory_management - INFO - Memory before cleanup: 451.45 MB
2025-06-03 14:57:25,769 - app.utils.memory_management - INFO - Garbage collection: collected 1027 objects
2025-06-03 14:57:25,769 - app.utils.memory_management - INFO - Memory after cleanup: 451.49 MB (freed -0.04 MB)
2025-06-03 15:03:05,010 - app - INFO - Cleaning up resources...
2025-06-03 15:03:05,012 - app.utils.memory_management - INFO - Memory before cleanup: 453.50 MB
2025-06-03 15:03:05,225 - app.utils.memory_management - INFO - Garbage collection: collected 396 objects
2025-06-03 15:03:05,227 - app.utils.memory_management - INFO - Memory after cleanup: 453.50 MB (freed 0.00 MB)
2025-06-03 15:03:05,227 - app - INFO - Application shutdown complete
2025-06-03 15:03:17,924 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 15:03:18,586 - app - INFO - Memory management utilities loaded
2025-06-03 15:03:18,586 - app - INFO - Error handling utilities loaded
2025-06-03 15:03:18,586 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 15:03:18,586 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 15:03:18,586 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 15:03:18,586 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 15:03:18,586 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 15:03:18,586 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 15:03:18,586 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 15:03:18,586 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 15:03:18,596 - app - INFO - Applied NumPy fix
2025-06-03 15:03:18,597 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 15:03:18,597 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 15:03:18,597 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 15:03:18,597 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 15:03:18,597 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 15:03:18,598 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 15:03:18,598 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 15:03:18,598 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 15:03:24,064 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 15:03:24,064 - app - INFO - Applied TensorFlow fix
2025-06-03 15:03:24,132 - app - INFO - Cleaning up resources...
2025-06-03 15:03:24,134 - app.utils.memory_management - INFO - Memory before cleanup: 312.82 MB
2025-06-03 15:03:24,249 - app.utils.memory_management - INFO - Garbage collection: collected 43 objects
2025-06-03 15:03:24,249 - app.utils.memory_management - INFO - Memory after cleanup: 312.91 MB (freed -0.10 MB)
2025-06-03 15:03:24,249 - app - INFO - Application shutdown complete
2025-06-03 15:04:31,113 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 15:04:32,756 - app - INFO - Memory management utilities loaded
2025-06-03 15:04:32,756 - app - INFO - Error handling utilities loaded
2025-06-03 15:04:32,756 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 15:04:32,756 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 15:04:32,756 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 15:04:32,756 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 15:04:32,756 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 15:04:32,756 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 15:04:32,764 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 15:04:32,764 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 15:04:32,764 - app - INFO - Applied NumPy fix
2025-06-03 15:04:32,767 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 15:04:32,767 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 15:04:32,767 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 15:04:32,768 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 15:04:32,768 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 15:04:32,768 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 15:04:32,768 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 15:04:32,769 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 15:04:37,488 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 15:04:37,489 - app - INFO - Applied TensorFlow fix
2025-06-03 15:04:37,569 - app - INFO - Cleaning up resources...
2025-06-03 15:04:37,571 - app.utils.memory_management - INFO - Memory before cleanup: 311.04 MB
2025-06-03 15:04:37,681 - app.utils.memory_management - INFO - Garbage collection: collected 60 objects
2025-06-03 15:04:37,683 - app.utils.memory_management - INFO - Memory after cleanup: 311.41 MB (freed -0.38 MB)
2025-06-03 15:04:37,683 - app - INFO - Application shutdown complete
2025-06-03 15:04:53,537 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 15:04:56,477 - app - INFO - Memory management utilities loaded
2025-06-03 15:04:56,479 - app - INFO - Error handling utilities loaded
2025-06-03 15:04:56,481 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 15:04:56,483 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 15:04:56,483 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 15:04:56,483 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 15:04:56,485 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 15:04:56,485 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 15:04:56,485 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 15:04:56,487 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 15:04:56,487 - app - INFO - Applied NumPy fix
2025-06-03 15:04:56,487 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 15:04:56,487 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 15:04:56,489 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 15:04:56,489 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 15:04:56,489 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 15:04:56,489 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 15:04:56,489 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 15:04:56,489 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 15:05:02,872 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 15:05:02,872 - app - INFO - Applied TensorFlow fix
2025-06-03 15:05:02,875 - app.config - INFO - Configuration initialized
2025-06-03 15:05:02,879 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 15:05:02,890 - models.train - INFO - TensorFlow test successful
2025-06-03 15:05:03,587 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 15:05:03,588 - models.train - INFO - Transformer model is available
2025-06-03 15:05:03,588 - models.train - INFO - Using TensorFlow-based models
2025-06-03 15:05:03,589 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 15:05:03,590 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 15:05:03,592 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 15:05:03,965 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 15:05:03,965 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 15:05:03,965 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 15:05:03,966 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 15:05:03,966 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 15:05:03,966 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 15:05:03,966 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 15:05:03,967 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 15:05:03,967 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 15:05:03,967 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 15:05:04,089 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 15:05:04,091 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 15:05:04,521 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 15:05:05,278 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 15:05:05,340 - app.utils.session_state - INFO - Initializing session state
2025-06-03 15:05:05,349 - app.utils.session_state - INFO - Session state initialized
2025-06-03 15:05:06,762 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 15:05:06,778 - app.utils.memory_management - INFO - Memory before cleanup: 430.82 MB
2025-06-03 15:05:06,991 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 15:05:06,993 - app.utils.memory_management - INFO - Memory after cleanup: 430.84 MB (freed -0.02 MB)
2025-06-03 15:05:09,650 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 15:05:09,696 - app.utils.memory_management - INFO - Memory before cleanup: 433.90 MB
2025-06-03 15:05:09,904 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 15:05:09,905 - app.utils.memory_management - INFO - Memory after cleanup: 433.90 MB (freed 0.00 MB)
2025-06-03 15:05:13,472 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 15:05:22,006 - app.components.ai_pattern_recognition - ERROR - Error in pattern detection: 'PremiumDiscountZone' object has no attribute 'get'
2025-06-03 15:05:22,904 - app.utils.memory_management - INFO - Memory before cleanup: 452.09 MB
2025-06-03 15:05:23,304 - app.utils.memory_management - INFO - Garbage collection: collected 1027 objects
2025-06-03 15:05:23,306 - app.utils.memory_management - INFO - Memory after cleanup: 452.13 MB (freed -0.04 MB)
2025-06-03 15:09:11,779 - app - INFO - Cleaning up resources...
2025-06-03 15:09:11,782 - app.utils.memory_management - INFO - Memory before cleanup: 453.93 MB
2025-06-03 15:09:12,019 - app.utils.memory_management - INFO - Garbage collection: collected 343 objects
2025-06-03 15:09:12,019 - app.utils.memory_management - INFO - Memory after cleanup: 453.93 MB (freed -0.00 MB)
2025-06-03 15:09:12,019 - app - INFO - Application shutdown complete
2025-06-03 15:10:24,374 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 15:10:27,243 - app - INFO - Memory management utilities loaded
2025-06-03 15:10:27,245 - app - INFO - Error handling utilities loaded
2025-06-03 15:10:27,246 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 15:10:27,248 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 15:10:27,249 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 15:10:27,250 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 15:10:27,252 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 15:10:27,252 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 15:10:27,253 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 15:10:27,253 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 15:10:27,254 - app - INFO - Applied NumPy fix
2025-06-03 15:10:27,255 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 15:10:27,255 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 15:10:27,256 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 15:10:27,256 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 15:10:27,256 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 15:10:27,257 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 15:10:27,258 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 15:10:27,258 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 15:10:33,997 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 15:10:33,997 - app - INFO - Applied TensorFlow fix
2025-06-03 15:10:33,999 - app.config - INFO - Configuration initialized
2025-06-03 15:10:34,002 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 15:10:34,013 - models.train - INFO - TensorFlow test successful
2025-06-03 15:10:34,577 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 15:10:34,578 - models.train - INFO - Transformer model is available
2025-06-03 15:10:34,578 - models.train - INFO - Using TensorFlow-based models
2025-06-03 15:10:34,579 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 15:10:34,580 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 15:10:34,582 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 15:10:34,928 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 15:10:34,928 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 15:10:34,928 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 15:10:34,928 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 15:10:34,929 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 15:10:34,929 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 15:10:34,929 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 15:10:34,929 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 15:10:34,929 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 15:10:34,929 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 15:10:35,054 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 15:10:35,056 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 15:10:35,398 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 15:10:36,042 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 15:10:36,087 - app.utils.session_state - INFO - Initializing session state
2025-06-03 15:10:36,088 - app.utils.session_state - INFO - Session state initialized
2025-06-03 15:10:37,296 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 15:10:37,307 - app.utils.memory_management - INFO - Memory before cleanup: 431.54 MB
2025-06-03 15:10:37,489 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 15:10:37,489 - app.utils.memory_management - INFO - Memory after cleanup: 431.54 MB (freed -0.00 MB)
2025-06-03 15:10:41,144 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 15:10:41,257 - app.utils.memory_management - INFO - Memory before cleanup: 434.01 MB
2025-06-03 15:10:41,458 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 15:10:41,459 - app.utils.memory_management - INFO - Memory after cleanup: 434.01 MB (freed 0.00 MB)
2025-06-03 15:10:49,049 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 15:10:57,156 - app.components.ai_pattern_recognition - ERROR - Error in pattern detection: 'PremiumDiscountZone' object has no attribute 'get'
2025-06-03 15:10:57,989 - app.utils.memory_management - INFO - Memory before cleanup: 452.97 MB
2025-06-03 15:10:58,209 - app.utils.memory_management - INFO - Garbage collection: collected 1030 objects
2025-06-03 15:10:58,212 - app.utils.memory_management - INFO - Memory after cleanup: 453.01 MB (freed -0.04 MB)
2025-06-03 17:20:00,162 - app - INFO - Cleaning up resources...
2025-06-03 17:20:00,162 - app.utils.memory_management - INFO - Memory before cleanup: 298.97 MB
2025-06-03 17:20:00,428 - app.utils.memory_management - INFO - Garbage collection: collected 353 objects
2025-06-03 17:20:00,432 - app.utils.memory_management - INFO - Memory after cleanup: 410.74 MB (freed -111.77 MB)
2025-06-03 17:20:00,436 - app - INFO - Application shutdown complete
2025-06-03 17:21:56,473 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 17:21:57,203 - app - INFO - Memory management utilities loaded
2025-06-03 17:21:57,203 - app - INFO - Error handling utilities loaded
2025-06-03 17:21:57,203 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 17:21:57,208 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 17:21:57,208 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 17:21:57,208 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 17:21:57,208 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 17:21:57,208 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 17:21:57,210 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 17:21:57,210 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 17:21:57,210 - app - INFO - Applied NumPy fix
2025-06-03 17:21:57,212 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:21:57,212 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:21:57,212 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:21:57,213 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 17:21:57,213 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:21:57,213 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:21:57,213 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:21:57,213 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 17:22:01,831 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 17:22:01,833 - app - INFO - Applied TensorFlow fix
2025-06-03 17:22:05,010 - app.components.predictive_analytics - ERROR - Error in prediction generation: This MinMaxScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:22:05,010 - app.components.predictive_analytics - ERROR - Error in scenario analysis: Could not generate base prediction
2025-06-03 17:22:05,041 - app - INFO - Cleaning up resources...
2025-06-03 17:22:05,045 - app.utils.memory_management - INFO - Memory before cleanup: 357.62 MB
2025-06-03 17:22:05,167 - app.utils.memory_management - INFO - Garbage collection: collected 84 objects
2025-06-03 17:22:05,167 - app.utils.memory_management - INFO - Memory after cleanup: 357.97 MB (freed -0.35 MB)
2025-06-03 17:22:05,167 - app - INFO - Application shutdown complete
2025-06-03 17:22:26,645 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 17:22:29,488 - app - INFO - Memory management utilities loaded
2025-06-03 17:22:29,490 - app - INFO - Error handling utilities loaded
2025-06-03 17:22:29,493 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 17:22:29,497 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 17:22:29,500 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 17:22:29,503 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 17:22:29,513 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 17:22:29,515 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 17:22:29,516 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 17:22:29,516 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 17:22:29,517 - app - INFO - Applied NumPy fix
2025-06-03 17:22:29,518 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:22:29,518 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:22:29,519 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:22:29,520 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 17:22:29,523 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:22:29,528 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:22:29,529 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:22:29,529 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 17:22:36,960 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 17:22:36,960 - app - INFO - Applied TensorFlow fix
2025-06-03 17:22:36,964 - app.config - INFO - Configuration initialized
2025-06-03 17:22:36,972 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 17:22:36,986 - models.train - INFO - TensorFlow test successful
2025-06-03 17:22:37,666 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 17:22:37,668 - models.train - INFO - Transformer model is available
2025-06-03 17:22:37,668 - models.train - INFO - Using TensorFlow-based models
2025-06-03 17:22:37,672 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 17:22:37,672 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 17:22:37,675 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 17:22:38,062 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 17:22:38,062 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 17:22:38,062 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:22:38,062 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:22:38,063 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:22:38,063 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 17:22:38,063 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 17:22:38,063 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:22:38,064 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:22:38,064 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:22:38,206 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 17:22:38,209 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:22:38,690 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 17:22:39,661 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 17:22:39,726 - app.pages.predictive_analytics - ERROR - Predictive analytics components not available: No module named 'app.components.price_scraper'
2025-06-03 17:22:39,737 - app.utils.session_state - INFO - Initializing session state
2025-06-03 17:22:39,740 - app.utils.session_state - INFO - Session state initialized
2025-06-03 17:22:41,212 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 17:22:41,234 - app.utils.memory_management - INFO - Memory before cleanup: 431.69 MB
2025-06-03 17:22:41,496 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:22:41,497 - app.utils.memory_management - INFO - Memory after cleanup: 431.70 MB (freed -0.01 MB)
2025-06-03 17:22:44,983 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:22:45,384 - app.utils.memory_management - INFO - Memory before cleanup: 434.29 MB
2025-06-03 17:22:45,649 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:22:45,651 - app.utils.memory_management - INFO - Memory after cleanup: 434.29 MB (freed 0.00 MB)
2025-06-03 17:22:49,386 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:23:02,476 - app.components.ai_pattern_recognition - ERROR - Error in pattern detection: 'PremiumDiscountZone' object has no attribute 'get'
2025-06-03 17:23:02,492 - app.components.predictive_analytics - ERROR - Error in prediction generation: This MinMaxScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:23:03,656 - app.utils.memory_management - INFO - Memory before cleanup: 452.61 MB
2025-06-03 17:23:03,956 - app.utils.memory_management - INFO - Garbage collection: collected 1014 objects
2025-06-03 17:23:03,956 - app.utils.memory_management - INFO - Memory after cleanup: 452.65 MB (freed -0.04 MB)
2025-06-03 17:23:41,672 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:23:41,748 - app.utils.memory_management - INFO - Memory before cleanup: 454.78 MB
2025-06-03 17:23:42,052 - app.utils.memory_management - INFO - Garbage collection: collected 284 objects
2025-06-03 17:23:42,052 - app.utils.memory_management - INFO - Memory after cleanup: 454.78 MB (freed 0.00 MB)
2025-06-03 17:23:46,425 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:23:54,760 - app.components.ai_pattern_recognition - ERROR - Error in pattern detection: 'PremiumDiscountZone' object has no attribute 'get'
2025-06-03 17:23:54,769 - app.components.predictive_analytics - ERROR - Error in prediction generation: This MinMaxScaler instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:23:54,950 - app.utils.memory_management - INFO - Memory before cleanup: 454.94 MB
2025-06-03 17:23:55,136 - app.utils.memory_management - INFO - Garbage collection: collected 1427 objects
2025-06-03 17:23:55,138 - app.utils.memory_management - INFO - Memory after cleanup: 454.94 MB (freed 0.00 MB)
2025-06-03 17:28:40,375 - app - INFO - Cleaning up resources...
2025-06-03 17:28:40,375 - app.utils.memory_management - INFO - Memory before cleanup: 454.74 MB
2025-06-03 17:28:40,611 - app.utils.memory_management - INFO - Garbage collection: collected 355 objects
2025-06-03 17:28:40,612 - app.utils.memory_management - INFO - Memory after cleanup: 454.74 MB (freed 0.00 MB)
2025-06-03 17:28:40,612 - app - INFO - Application shutdown complete
2025-06-03 17:28:43,158 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 17:28:43,880 - app - INFO - Memory management utilities loaded
2025-06-03 17:28:43,882 - app - INFO - Error handling utilities loaded
2025-06-03 17:28:43,882 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 17:28:43,882 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 17:28:43,882 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 17:28:43,882 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 17:28:43,882 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 17:28:43,888 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 17:28:43,888 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 17:28:43,888 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 17:28:43,888 - app - INFO - Applied NumPy fix
2025-06-03 17:28:43,890 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:28:43,891 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:28:43,891 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:28:43,891 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 17:28:43,891 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:28:43,892 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:28:43,892 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:28:43,893 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 17:28:49,533 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 17:28:49,535 - app - INFO - Applied TensorFlow fix
2025-06-03 17:28:51,776 - app.components.predictive_analytics - ERROR - Error in horizon prediction: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:28:51,829 - app.components.predictive_analytics - ERROR - Error in horizon prediction: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:28:52,145 - app.components.predictive_analytics - ERROR - Error in horizon prediction: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:28:52,152 - app.components.predictive_analytics - ERROR - Error in horizon prediction: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:28:52,490 - app - INFO - Cleaning up resources...
2025-06-03 17:28:52,520 - app.utils.memory_management - INFO - Memory before cleanup: 355.90 MB
2025-06-03 17:28:52,821 - app.utils.memory_management - INFO - Garbage collection: collected 10 objects
2025-06-03 17:28:52,825 - app.utils.memory_management - INFO - Memory after cleanup: 355.91 MB (freed -0.01 MB)
2025-06-03 17:28:52,827 - app - INFO - Application shutdown complete
2025-06-03 17:29:46,802 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 17:29:48,291 - app - INFO - Memory management utilities loaded
2025-06-03 17:29:48,293 - app - INFO - Error handling utilities loaded
2025-06-03 17:29:48,295 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 17:29:48,295 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 17:29:48,295 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 17:29:48,295 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 17:29:48,297 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 17:29:48,298 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 17:29:48,298 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 17:29:48,298 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 17:29:48,298 - app - INFO - Applied NumPy fix
2025-06-03 17:29:48,300 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:29:48,300 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:29:48,300 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:29:48,300 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 17:29:48,301 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:29:48,302 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:29:48,302 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:29:48,302 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 17:29:52,707 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 17:29:52,707 - app - INFO - Applied TensorFlow fix
2025-06-03 17:29:54,027 - app - INFO - Cleaning up resources...
2025-06-03 17:29:54,034 - app.utils.memory_management - INFO - Memory before cleanup: 351.05 MB
2025-06-03 17:29:54,225 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:29:54,225 - app.utils.memory_management - INFO - Memory after cleanup: 351.41 MB (freed -0.36 MB)
2025-06-03 17:29:54,225 - app - INFO - Application shutdown complete
2025-06-03 17:30:19,990 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 17:30:22,366 - app - INFO - Memory management utilities loaded
2025-06-03 17:30:22,369 - app - INFO - Error handling utilities loaded
2025-06-03 17:30:22,371 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 17:30:22,373 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 17:30:22,375 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 17:30:22,376 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 17:30:22,377 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 17:30:22,379 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 17:30:22,380 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 17:30:22,382 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 17:30:22,383 - app - INFO - Applied NumPy fix
2025-06-03 17:30:22,387 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:30:22,389 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:30:22,391 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:30:22,391 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 17:30:22,393 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:30:22,393 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:30:22,396 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:30:22,396 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 17:30:29,399 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 17:30:29,399 - app - INFO - Applied TensorFlow fix
2025-06-03 17:30:29,405 - app.config - INFO - Configuration initialized
2025-06-03 17:30:29,412 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 17:30:29,430 - models.train - INFO - TensorFlow test successful
2025-06-03 17:30:30,504 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 17:30:30,507 - models.train - INFO - Transformer model is available
2025-06-03 17:30:30,508 - models.train - INFO - Using TensorFlow-based models
2025-06-03 17:30:30,514 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 17:30:30,515 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 17:30:30,519 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 17:30:31,015 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 17:30:31,015 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 17:30:31,017 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:30:31,017 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:30:31,019 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:30:31,019 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 17:30:31,019 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 17:30:31,019 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:30:31,021 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:30:31,021 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:30:31,154 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 17:30:31,156 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:30:31,591 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 17:30:32,408 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 17:30:32,472 - app.utils.session_state - INFO - Initializing session state
2025-06-03 17:30:32,473 - app.utils.session_state - INFO - Session state initialized
2025-06-03 17:30:34,076 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 17:30:34,099 - app.utils.memory_management - INFO - Memory before cleanup: 432.05 MB
2025-06-03 17:30:34,403 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:30:34,404 - app.utils.memory_management - INFO - Memory after cleanup: 432.06 MB (freed -0.01 MB)
2025-06-03 17:30:38,002 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:30:38,188 - app.utils.memory_management - INFO - Memory before cleanup: 434.50 MB
2025-06-03 17:30:38,531 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:30:38,536 - app.utils.memory_management - INFO - Memory after cleanup: 434.50 MB (freed 0.00 MB)
2025-06-03 17:30:42,597 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:30:52,427 - app.components.predictive_analytics - ERROR - Error in horizon prediction: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:30:52,427 - app.components.predictive_analytics - ERROR - Error in horizon prediction: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:30:53,347 - app.utils.memory_management - INFO - Memory before cleanup: 453.07 MB
2025-06-03 17:30:53,642 - app.utils.memory_management - INFO - Garbage collection: collected 1021 objects
2025-06-03 17:30:53,642 - app.utils.memory_management - INFO - Memory after cleanup: 453.11 MB (freed -0.04 MB)
2025-06-03 17:31:49,058 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:31:49,126 - app.utils.memory_management - INFO - Memory before cleanup: 455.07 MB
2025-06-03 17:31:49,376 - app.utils.memory_management - INFO - Garbage collection: collected 271 objects
2025-06-03 17:31:49,377 - app.utils.memory_management - INFO - Memory after cleanup: 455.07 MB (freed 0.00 MB)
2025-06-03 17:31:56,083 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:32:05,388 - app.components.predictive_analytics - ERROR - Error in horizon prediction: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:32:05,388 - app.components.predictive_analytics - ERROR - Error in horizon prediction: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:32:05,530 - app.utils.memory_management - INFO - Memory before cleanup: 455.15 MB
2025-06-03 17:32:05,693 - app.utils.memory_management - INFO - Garbage collection: collected 1435 objects
2025-06-03 17:32:05,693 - app.utils.memory_management - INFO - Memory after cleanup: 455.15 MB (freed 0.00 MB)
2025-06-03 17:39:40,638 - app - INFO - Cleaning up resources...
2025-06-03 17:39:40,639 - app.utils.memory_management - INFO - Memory before cleanup: 454.93 MB
2025-06-03 17:39:40,855 - app.utils.memory_management - INFO - Garbage collection: collected 342 objects
2025-06-03 17:39:40,857 - app.utils.memory_management - INFO - Memory after cleanup: 454.93 MB (freed 0.00 MB)
2025-06-03 17:39:40,857 - app - INFO - Application shutdown complete
2025-06-03 17:39:43,735 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 17:39:44,494 - app - INFO - Memory management utilities loaded
2025-06-03 17:39:44,498 - app - INFO - Error handling utilities loaded
2025-06-03 17:39:44,498 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 17:39:44,500 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 17:39:44,500 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 17:39:44,500 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 17:39:44,500 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 17:39:44,500 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 17:39:44,502 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 17:39:44,502 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 17:39:44,502 - app - INFO - Applied NumPy fix
2025-06-03 17:39:44,503 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:39:44,503 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:39:44,504 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:39:44,504 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 17:39:44,504 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:39:44,505 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:39:44,505 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:39:44,505 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 17:39:48,999 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 17:39:49,001 - app - INFO - Applied TensorFlow fix
2025-06-03 17:39:51,552 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:39:51,556 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,558 - app.components.predictive_analytics - ERROR - Error in horizon prediction: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,560 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,561 - app.components.predictive_analytics - ERROR - Error in prediction generation: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,585 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:39:51,595 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,599 - app.components.predictive_analytics - ERROR - Error in horizon prediction: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,612 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,635 - app.components.predictive_analytics - ERROR - Error in prediction generation: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,641 - app.components.predictive_analytics - ERROR - Error in scenario analysis: Could not generate base prediction
2025-06-03 17:39:51,692 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,796 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:39:51,814 - app - INFO - Cleaning up resources...
2025-06-03 17:39:51,819 - app.utils.memory_management - INFO - Memory before cleanup: 352.50 MB
2025-06-03 17:39:52,029 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:39:52,033 - app.utils.memory_management - INFO - Memory after cleanup: 352.87 MB (freed -0.37 MB)
2025-06-03 17:39:52,033 - app - INFO - Application shutdown complete
2025-06-03 17:42:42,370 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 17:42:44,714 - app - INFO - Memory management utilities loaded
2025-06-03 17:42:44,714 - app - INFO - Error handling utilities loaded
2025-06-03 17:42:44,718 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 17:42:44,722 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 17:42:44,726 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 17:42:44,729 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 17:42:44,733 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 17:42:44,735 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 17:42:44,736 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 17:42:44,736 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 17:42:44,736 - app - INFO - Applied NumPy fix
2025-06-03 17:42:44,738 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:42:44,741 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:42:44,741 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:42:44,741 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 17:42:44,742 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:42:44,742 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:42:44,742 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:42:44,743 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 17:42:49,436 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 17:42:49,436 - app - INFO - Applied TensorFlow fix
2025-06-03 17:42:50,810 - app - INFO - Cleaning up resources...
2025-06-03 17:42:50,833 - app.utils.memory_management - INFO - Memory before cleanup: 351.22 MB
2025-06-03 17:42:50,982 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:42:50,982 - app.utils.memory_management - INFO - Memory after cleanup: 351.58 MB (freed -0.36 MB)
2025-06-03 17:42:50,982 - app - INFO - Application shutdown complete
2025-06-03 17:44:21,681 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 17:44:23,380 - app - INFO - Memory management utilities loaded
2025-06-03 17:44:23,382 - app - INFO - Error handling utilities loaded
2025-06-03 17:44:23,385 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 17:44:23,387 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 17:44:23,388 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 17:44:23,388 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 17:44:23,389 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 17:44:23,390 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 17:44:23,390 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 17:44:23,390 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 17:44:23,391 - app - INFO - Applied NumPy fix
2025-06-03 17:44:23,392 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:44:23,392 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:44:23,393 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:44:23,393 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 17:44:23,395 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:44:23,395 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:44:23,396 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:44:23,396 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 17:44:27,715 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 17:44:27,716 - app - INFO - Applied TensorFlow fix
2025-06-03 17:44:27,718 - app.config - INFO - Configuration initialized
2025-06-03 17:44:27,723 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 17:44:27,731 - models.train - INFO - TensorFlow test successful
2025-06-03 17:44:28,330 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 17:44:28,330 - models.train - INFO - Transformer model is available
2025-06-03 17:44:28,330 - models.train - INFO - Using TensorFlow-based models
2025-06-03 17:44:28,331 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 17:44:28,332 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 17:44:28,334 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 17:44:28,659 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 17:44:28,659 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 17:44:28,659 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 17:44:28,660 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 17:44:28,660 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 17:44:28,660 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 17:44:28,660 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 17:44:28,660 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 17:44:28,660 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 17:44:28,661 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 17:44:28,760 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 17:44:28,762 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:44:29,109 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 17:44:29,757 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 17:44:29,812 - app.utils.session_state - INFO - Initializing session state
2025-06-03 17:44:29,813 - app.utils.session_state - INFO - Session state initialized
2025-06-03 17:44:30,972 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 17:44:30,988 - app.utils.memory_management - INFO - Memory before cleanup: 430.21 MB
2025-06-03 17:44:31,186 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:44:31,186 - app.utils.memory_management - INFO - Memory after cleanup: 430.22 MB (freed -0.01 MB)
2025-06-03 17:44:53,421 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:44:53,572 - app.utils.memory_management - INFO - Memory before cleanup: 434.04 MB
2025-06-03 17:44:53,776 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 17:44:53,778 - app.utils.memory_management - INFO - Memory after cleanup: 434.04 MB (freed 0.00 MB)
2025-06-03 17:44:55,625 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:45:08,356 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:45:08,356 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:45:08,356 - app.components.predictive_analytics - ERROR - Error in horizon prediction: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:45:08,356 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:45:08,356 - app.components.predictive_analytics - ERROR - Error in prediction generation: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:45:09,251 - app.utils.memory_management - INFO - Memory before cleanup: 453.32 MB
2025-06-03 17:45:09,494 - app.utils.memory_management - INFO - Garbage collection: collected 1021 objects
2025-06-03 17:45:09,496 - app.utils.memory_management - INFO - Memory after cleanup: 453.36 MB (freed -0.04 MB)
2025-06-03 17:45:43,397 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:45:43,448 - app.utils.memory_management - INFO - Memory before cleanup: 455.45 MB
2025-06-03 17:45:43,710 - app.utils.memory_management - INFO - Garbage collection: collected 271 objects
2025-06-03 17:45:43,713 - app.utils.memory_management - INFO - Memory after cleanup: 455.45 MB (freed 0.00 MB)
2025-06-03 17:58:09,468 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 17:58:09,490 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 17:58:21,805 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 17:58:21,807 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:58:21,807 - app.components.predictive_analytics - ERROR - Error in horizon prediction: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:58:21,807 - app.components.predictive_analytics - ERROR - Error in fallback predictions: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:58:21,807 - app.components.predictive_analytics - ERROR - Error in prediction generation: PredictionResult.__init__() got an unexpected keyword argument 'model_type'
2025-06-03 17:58:21,951 - app.utils.memory_management - INFO - Memory before cleanup: 452.14 MB
2025-06-03 17:58:22,163 - app.utils.memory_management - INFO - Garbage collection: collected 1377 objects
2025-06-03 17:58:22,165 - app.utils.memory_management - INFO - Memory after cleanup: 452.14 MB (freed 0.00 MB)
2025-06-03 18:01:23,272 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 18:01:23,332 - app.utils.memory_management - INFO - Memory before cleanup: 452.19 MB
2025-06-03 18:01:23,566 - app.utils.memory_management - INFO - Garbage collection: collected 273 objects
2025-06-03 18:01:23,567 - app.utils.memory_management - INFO - Memory after cleanup: 452.19 MB (freed 0.00 MB)
2025-06-03 18:03:21,547 - app - INFO - Cleaning up resources...
2025-06-03 18:03:21,548 - app.utils.memory_management - INFO - Memory before cleanup: 452.45 MB
2025-06-03 18:03:21,815 - app.utils.memory_management - INFO - Garbage collection: collected 319 objects
2025-06-03 18:03:21,816 - app.utils.memory_management - INFO - Memory after cleanup: 452.45 MB (freed 0.00 MB)
2025-06-03 18:03:21,817 - app - INFO - Application shutdown complete
2025-06-03 18:04:56,332 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 18:04:58,168 - app - INFO - Memory management utilities loaded
2025-06-03 18:04:58,172 - app - INFO - Error handling utilities loaded
2025-06-03 18:04:58,172 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 18:04:58,177 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 18:04:58,177 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 18:04:58,177 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 18:04:58,177 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 18:04:58,177 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 18:04:58,179 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 18:04:58,179 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 18:04:58,179 - app - INFO - Applied NumPy fix
2025-06-03 18:04:58,181 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 18:04:58,181 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 18:04:58,182 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 18:04:58,182 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 18:04:58,183 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 18:04:58,183 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 18:04:58,183 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 18:04:58,184 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 18:05:02,564 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 18:05:02,564 - app - INFO - Applied TensorFlow fix
2025-06-03 18:05:03,840 - app - INFO - Cleaning up resources...
2025-06-03 18:05:03,844 - app.utils.memory_management - INFO - Memory before cleanup: 350.49 MB
2025-06-03 18:05:03,958 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 18:05:03,958 - app.utils.memory_management - INFO - Memory after cleanup: 350.86 MB (freed -0.37 MB)
2025-06-03 18:05:03,958 - app - INFO - Application shutdown complete
2025-06-03 18:07:13,828 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 18:07:15,353 - app - INFO - Memory management utilities loaded
2025-06-03 18:07:15,355 - app - INFO - Error handling utilities loaded
2025-06-03 18:07:15,357 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 18:07:15,357 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 18:07:15,357 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 18:07:15,357 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 18:07:15,357 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 18:07:15,361 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 18:07:15,361 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 18:07:15,361 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 18:07:15,361 - app - INFO - Applied NumPy fix
2025-06-03 18:07:15,362 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 18:07:15,362 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 18:07:15,362 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 18:07:15,363 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 18:07:15,363 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 18:07:15,363 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 18:07:15,363 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 18:07:15,364 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 18:07:20,039 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 18:07:20,039 - app - INFO - Applied TensorFlow fix
2025-06-03 18:07:21,417 - app - INFO - Cleaning up resources...
2025-06-03 18:07:21,423 - app.utils.memory_management - INFO - Memory before cleanup: 361.46 MB
2025-06-03 18:07:21,549 - app.utils.memory_management - INFO - Garbage collection: collected 75 objects
2025-06-03 18:07:21,549 - app.utils.memory_management - INFO - Memory after cleanup: 361.79 MB (freed -0.34 MB)
2025-06-03 18:07:21,549 - app - INFO - Application shutdown complete
2025-06-03 18:07:45,833 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 18:07:48,507 - app - INFO - Memory management utilities loaded
2025-06-03 18:07:48,509 - app - INFO - Error handling utilities loaded
2025-06-03 18:07:48,510 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 18:07:48,512 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 18:07:48,513 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 18:07:48,517 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 18:07:48,519 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 18:07:48,523 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 18:07:48,523 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 18:07:48,525 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 18:07:48,525 - app - INFO - Applied NumPy fix
2025-06-03 18:07:48,529 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 18:07:48,533 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 18:07:48,535 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 18:07:48,537 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 18:07:48,539 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 18:07:48,541 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 18:07:48,543 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 18:07:48,543 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 18:07:55,378 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 18:07:55,380 - app - INFO - Applied TensorFlow fix
2025-06-03 18:07:55,384 - app.config - INFO - Configuration initialized
2025-06-03 18:07:55,400 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 18:07:55,427 - models.train - INFO - TensorFlow test successful
2025-06-03 18:07:56,419 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 18:07:56,419 - models.train - INFO - Transformer model is available
2025-06-03 18:07:56,419 - models.train - INFO - Using TensorFlow-based models
2025-06-03 18:07:56,421 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 18:07:56,422 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 18:07:56,426 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 18:07:56,812 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 18:07:56,813 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 18:07:56,813 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 18:07:56,813 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 18:07:56,814 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 18:07:56,814 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 18:07:56,814 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 18:07:56,814 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 18:07:56,814 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 18:07:56,815 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 18:07:56,995 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 18:07:57,003 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 18:07:57,744 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 18:07:58,604 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 18:07:58,643 - app.utils.session_state - INFO - Initializing session state
2025-06-03 18:07:58,645 - app.utils.session_state - INFO - Session state initialized
2025-06-03 18:08:00,051 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 18:08:00,091 - app.utils.memory_management - INFO - Memory before cleanup: 430.15 MB
2025-06-03 18:08:00,383 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-03 18:08:00,385 - app.utils.memory_management - INFO - Memory after cleanup: 430.54 MB (freed -0.39 MB)
2025-06-03 18:08:03,425 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 18:08:03,588 - app.utils.memory_management - INFO - Memory before cleanup: 434.04 MB
2025-06-03 18:08:03,802 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 18:08:03,804 - app.utils.memory_management - INFO - Memory after cleanup: 434.04 MB (freed 0.00 MB)
2025-06-03 18:08:06,492 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 18:08:19,378 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 18:08:19,378 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 18:08:20,264 - app.utils.memory_management - INFO - Memory before cleanup: 451.82 MB
2025-06-03 18:08:20,544 - app.utils.memory_management - INFO - Garbage collection: collected 1007 objects
2025-06-03 18:08:20,545 - app.utils.memory_management - INFO - Memory after cleanup: 451.86 MB (freed -0.04 MB)
2025-06-03 22:31:40,114 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:31:40,163 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 22:31:56,228 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 22:31:56,228 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 22:31:56,434 - app.utils.memory_management - INFO - Memory before cleanup: 346.02 MB
2025-06-03 22:31:56,758 - app.utils.memory_management - INFO - Garbage collection: collected 1487 objects
2025-06-03 22:31:56,761 - app.utils.memory_management - INFO - Memory after cleanup: 346.95 MB (freed -0.93 MB)
2025-06-03 22:32:09,395 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:32:09,403 - app.utils.session_state - INFO - Initializing session state
2025-06-03 22:32:09,407 - app.utils.session_state - INFO - Session state initialized
2025-06-03 22:32:09,435 - app.utils.memory_management - INFO - Memory before cleanup: 348.74 MB
2025-06-03 22:32:09,632 - app.utils.memory_management - INFO - Garbage collection: collected 274 objects
2025-06-03 22:32:09,636 - app.utils.memory_management - INFO - Memory after cleanup: 348.74 MB (freed 0.00 MB)
2025-06-03 22:32:21,985 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:32:22,031 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-03 22:32:22,154 - app.utils.memory_management - INFO - Memory before cleanup: 349.77 MB
2025-06-03 22:32:22,357 - app.utils.memory_management - INFO - Garbage collection: collected 353 objects
2025-06-03 22:32:22,358 - app.utils.memory_management - INFO - Memory after cleanup: 349.77 MB (freed 0.00 MB)
2025-06-03 22:38:50,446 - app - INFO - Cleaning up resources...
2025-06-03 22:38:50,447 - app.utils.memory_management - INFO - Memory before cleanup: 349.56 MB
2025-06-03 22:38:50,624 - app.utils.memory_management - INFO - Garbage collection: collected 630 objects
2025-06-03 22:38:50,624 - app.utils.memory_management - INFO - Memory after cleanup: 349.56 MB (freed 0.00 MB)
2025-06-03 22:38:50,624 - app - INFO - Application shutdown complete
2025-06-03 22:41:31,049 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 22:41:37,587 - app - INFO - Memory management utilities loaded
2025-06-03 22:41:37,598 - app - INFO - Error handling utilities loaded
2025-06-03 22:41:37,604 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 22:41:37,606 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 22:41:37,607 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 22:41:37,608 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 22:41:37,617 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 22:41:37,618 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 22:41:37,619 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 22:41:37,619 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 22:41:37,619 - app - INFO - Applied NumPy fix
2025-06-03 22:41:37,626 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 22:41:37,628 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 22:41:37,628 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 22:41:37,628 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 22:41:37,628 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 22:41:37,628 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 22:41:37,628 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 22:41:37,628 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 22:42:18,341 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 22:42:18,341 - app - INFO - Applied TensorFlow fix
2025-06-03 22:42:18,362 - app.config - INFO - Configuration initialized
2025-06-03 22:42:18,404 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 22:42:18,532 - models.train - INFO - TensorFlow test successful
2025-06-03 22:42:27,485 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 22:42:27,486 - models.train - INFO - Transformer model is available
2025-06-03 22:42:27,486 - models.train - INFO - Using TensorFlow-based models
2025-06-03 22:42:27,700 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 22:42:27,700 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 22:42:27,717 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 22:42:34,732 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 22:42:34,733 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 22:42:34,733 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 22:42:34,734 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 22:42:34,734 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 22:42:34,734 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 22:42:34,734 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 22:42:34,735 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 22:42:34,735 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 22:42:34,735 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 22:42:35,665 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 22:42:35,680 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:42:36,976 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 22:42:42,677 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 22:42:42,868 - app.utils.session_state - INFO - Initializing session state
2025-06-03 22:42:42,870 - app.utils.session_state - INFO - Session state initialized
2025-06-03 22:42:44,355 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 22:42:44,376 - app.utils.memory_management - INFO - Memory before cleanup: 432.89 MB
2025-06-03 22:42:44,671 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 22:42:44,673 - app.utils.memory_management - INFO - Memory after cleanup: 432.89 MB (freed -0.01 MB)
2025-06-03 22:42:49,228 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:42:51,333 - app.utils.memory_management - INFO - Memory before cleanup: 435.45 MB
2025-06-03 22:42:51,515 - app.utils.memory_management - INFO - Garbage collection: collected 119 objects
2025-06-03 22:42:51,516 - app.utils.memory_management - INFO - Memory after cleanup: 435.45 MB (freed 0.00 MB)
2025-06-03 22:49:00,310 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:49:00,315 - app.utils.session_state - INFO - Initializing session state
2025-06-03 22:49:00,323 - app.utils.session_state - INFO - Session state initialized
2025-06-03 22:49:00,350 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 22:49:00,386 - app.utils.memory_management - INFO - Memory before cleanup: 435.78 MB
2025-06-03 22:49:00,762 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-03 22:49:00,765 - app.utils.memory_management - INFO - Memory after cleanup: 435.82 MB (freed -0.04 MB)
2025-06-03 22:49:58,437 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:50:00,478 - app.utils.memory_management - INFO - Memory before cleanup: 436.09 MB
2025-06-03 22:50:00,625 - app.utils.memory_management - INFO - Garbage collection: collected 328 objects
2025-06-03 22:50:00,626 - app.utils.memory_management - INFO - Memory after cleanup: 436.09 MB (freed 0.00 MB)
2025-06-03 22:50:25,869 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:50:25,874 - app.utils.session_state - INFO - Initializing session state
2025-06-03 22:50:25,877 - app.utils.session_state - INFO - Session state initialized
2025-06-03 22:50:25,896 - app.utils.memory_management - INFO - Memory before cleanup: 435.91 MB
2025-06-03 22:50:26,071 - app.utils.memory_management - INFO - Garbage collection: collected 178 objects
2025-06-03 22:50:26,072 - app.utils.memory_management - INFO - Memory after cleanup: 435.91 MB (freed 0.00 MB)
2025-06-03 22:50:29,610 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:50:29,650 - app.utils.memory_management - INFO - Memory before cleanup: 436.79 MB
2025-06-03 22:50:29,877 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-03 22:50:29,877 - app.utils.memory_management - INFO - Memory after cleanup: 436.79 MB (freed 0.00 MB)
2025-06-03 22:50:35,508 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:50:35,542 - app.utils.memory_management - INFO - Memory before cleanup: 436.79 MB
2025-06-03 22:50:35,762 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-03 22:50:35,763 - app.utils.memory_management - INFO - Memory after cleanup: 436.79 MB (freed 0.00 MB)
2025-06-03 22:50:36,988 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:50:45,826 - app.utils.memory_management - INFO - Memory before cleanup: 436.81 MB
2025-06-03 22:50:46,008 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-03 22:50:46,008 - app.utils.memory_management - INFO - Memory after cleanup: 436.81 MB (freed 0.00 MB)
2025-06-03 22:50:52,946 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:50:53,201 - app.utils.memory_management - INFO - Memory before cleanup: 436.81 MB
2025-06-03 22:50:53,394 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-03 22:50:53,395 - app.utils.memory_management - INFO - Memory after cleanup: 436.81 MB (freed 0.00 MB)
2025-06-03 22:50:56,790 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:51:01,401 - app.utils.memory_management - INFO - Memory before cleanup: 436.82 MB
2025-06-03 22:51:01,559 - app.utils.memory_management - INFO - Garbage collection: collected 187 objects
2025-06-03 22:51:01,565 - app.utils.memory_management - INFO - Memory after cleanup: 436.82 MB (freed 0.00 MB)
2025-06-03 22:51:06,616 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:51:06,649 - app.utils.memory_management - INFO - Memory before cleanup: 436.82 MB
2025-06-03 22:51:06,863 - app.utils.memory_management - INFO - Garbage collection: collected 190 objects
2025-06-03 22:51:06,865 - app.utils.memory_management - INFO - Memory after cleanup: 436.82 MB (freed 0.00 MB)
2025-06-03 22:51:11,748 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:51:11,803 - app.utils.memory_management - INFO - Memory before cleanup: 436.82 MB
2025-06-03 22:51:12,032 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-03 22:51:12,032 - app.utils.memory_management - INFO - Memory after cleanup: 436.82 MB (freed 0.00 MB)
2025-06-03 22:51:14,158 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:51:19,171 - app.utils.memory_management - INFO - Memory before cleanup: 436.82 MB
2025-06-03 22:51:19,364 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-03 22:51:19,364 - app.utils.memory_management - INFO - Memory after cleanup: 436.82 MB (freed 0.00 MB)
2025-06-03 22:51:45,655 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:51:45,696 - app.utils.memory_management - INFO - Memory before cleanup: 436.87 MB
2025-06-03 22:51:45,902 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-03 22:51:45,904 - app.utils.memory_management - INFO - Memory after cleanup: 436.87 MB (freed 0.00 MB)
2025-06-03 22:51:49,129 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:51:54,440 - app.utils.memory_management - INFO - Memory before cleanup: 436.87 MB
2025-06-03 22:51:54,603 - app.utils.memory_management - INFO - Garbage collection: collected 187 objects
2025-06-03 22:51:54,606 - app.utils.memory_management - INFO - Memory after cleanup: 436.87 MB (freed 0.00 MB)
2025-06-03 22:53:59,654 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:53:59,658 - app.utils.session_state - INFO - Initializing session state
2025-06-03 22:53:59,660 - app.utils.session_state - INFO - Session state initialized
2025-06-03 22:53:59,674 - app.utils.memory_management - INFO - Memory before cleanup: 436.64 MB
2025-06-03 22:53:59,857 - app.utils.memory_management - INFO - Garbage collection: collected 190 objects
2025-06-03 22:53:59,858 - app.utils.memory_management - INFO - Memory after cleanup: 436.64 MB (freed 0.00 MB)
2025-06-03 22:54:03,846 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:54:03,866 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 22:54:03,881 - app.utils.memory_management - INFO - Memory before cleanup: 437.14 MB
2025-06-03 22:54:04,087 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-03 22:54:04,087 - app.utils.memory_management - INFO - Memory after cleanup: 437.14 MB (freed 0.00 MB)
2025-06-03 22:54:14,775 - app - INFO - Cleaning up resources...
2025-06-03 22:54:14,775 - app.utils.memory_management - INFO - Memory before cleanup: 436.84 MB
2025-06-03 22:54:14,936 - app.utils.memory_management - INFO - Garbage collection: collected 347 objects
2025-06-03 22:54:14,938 - app.utils.memory_management - INFO - Memory after cleanup: 436.84 MB (freed 0.00 MB)
2025-06-03 22:54:14,938 - app - INFO - Application shutdown complete
2025-06-03 22:55:30,023 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 22:55:31,380 - app - INFO - Memory management utilities loaded
2025-06-03 22:55:31,381 - app - INFO - Error handling utilities loaded
2025-06-03 22:55:31,384 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 22:55:31,385 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 22:55:31,386 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 22:55:31,386 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 22:55:31,388 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 22:55:31,388 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 22:55:31,390 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 22:55:31,390 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 22:55:31,390 - app - INFO - Applied NumPy fix
2025-06-03 22:55:31,392 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 22:55:31,394 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 22:55:31,394 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 22:55:31,394 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 22:55:31,396 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 22:55:31,396 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 22:55:31,396 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 22:55:31,396 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 22:55:35,453 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 22:55:35,453 - app - INFO - Applied TensorFlow fix
2025-06-03 22:55:35,456 - app.config - INFO - Configuration initialized
2025-06-03 22:55:35,459 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 22:55:35,471 - models.train - INFO - TensorFlow test successful
2025-06-03 22:55:35,957 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 22:55:35,957 - models.train - INFO - Transformer model is available
2025-06-03 22:55:35,958 - models.train - INFO - Using TensorFlow-based models
2025-06-03 22:55:35,959 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 22:55:35,959 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 22:55:35,961 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 22:55:36,274 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 22:55:36,274 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 22:55:36,275 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 22:55:36,275 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 22:55:36,275 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 22:55:36,275 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 22:55:36,275 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 22:55:36,275 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 22:55:36,276 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 22:55:36,276 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 22:55:36,356 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 22:55:36,358 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:55:36,686 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 22:55:37,216 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 22:55:37,254 - app.utils.session_state - INFO - Initializing session state
2025-06-03 22:55:37,256 - app.utils.session_state - INFO - Session state initialized
2025-06-03 22:55:38,452 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 22:55:38,466 - app.utils.memory_management - INFO - Memory before cleanup: 431.07 MB
2025-06-03 22:55:38,643 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 22:55:38,643 - app.utils.memory_management - INFO - Memory after cleanup: 431.46 MB (freed -0.39 MB)
2025-06-03 22:56:03,671 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:56:03,707 - app.utils.memory_management - INFO - Memory before cleanup: 435.12 MB
2025-06-03 22:56:03,902 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 22:56:03,903 - app.utils.memory_management - INFO - Memory after cleanup: 435.12 MB (freed -0.00 MB)
2025-06-03 22:56:07,540 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:56:07,571 - app.utils.memory_management - INFO - Memory before cleanup: 435.93 MB
2025-06-03 22:56:07,839 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-03 22:56:07,839 - app.utils.memory_management - INFO - Memory after cleanup: 435.97 MB (freed -0.04 MB)
2025-06-03 22:56:08,794 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:56:14,684 - app.utils.memory_management - INFO - Memory before cleanup: 435.97 MB
2025-06-03 22:56:14,849 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-03 22:56:14,849 - app.utils.memory_management - INFO - Memory after cleanup: 435.97 MB (freed 0.00 MB)
2025-06-03 22:56:31,727 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:56:31,821 - app.utils.memory_management - INFO - Memory before cleanup: 435.90 MB
2025-06-03 22:56:32,044 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-03 22:56:32,044 - app.utils.memory_management - INFO - Memory after cleanup: 435.90 MB (freed 0.00 MB)
2025-06-03 22:56:33,969 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 22:56:38,909 - app.utils.memory_management - INFO - Memory before cleanup: 435.91 MB
2025-06-03 22:56:39,069 - app.utils.memory_management - INFO - Garbage collection: collected 187 objects
2025-06-03 22:56:39,071 - app.utils.memory_management - INFO - Memory after cleanup: 435.91 MB (freed 0.00 MB)
2025-06-03 23:03:29,440 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 23:03:30,579 - app - INFO - Memory management utilities loaded
2025-06-03 23:03:30,579 - app - INFO - Error handling utilities loaded
2025-06-03 23:03:30,579 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 23:03:30,579 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 23:03:30,579 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 23:03:30,579 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 23:03:30,579 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 23:03:30,579 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 23:03:30,579 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 23:03:30,579 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 23:03:30,579 - app - INFO - Applied NumPy fix
2025-06-03 23:03:30,586 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 23:03:30,586 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 23:03:30,586 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 23:03:30,587 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 23:03:30,588 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 23:03:30,589 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 23:03:30,589 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 23:03:30,589 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 23:03:34,182 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 23:03:34,182 - app - INFO - Applied TensorFlow fix
2025-06-03 23:03:46,367 - app - INFO - Cleaning up resources...
2025-06-03 23:03:46,371 - app.utils.memory_management - INFO - Memory before cleanup: 324.59 MB
2025-06-03 23:03:46,492 - app.utils.memory_management - INFO - Garbage collection: collected 670 objects
2025-06-03 23:03:46,494 - app.utils.memory_management - INFO - Memory after cleanup: 324.60 MB (freed -0.01 MB)
2025-06-03 23:03:46,494 - app - INFO - Application shutdown complete
2025-06-03 23:04:22,136 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 23:04:22,140 - app - INFO - Memory management utilities loaded
2025-06-03 23:04:22,142 - app - INFO - Error handling utilities loaded
2025-06-03 23:04:22,144 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 23:04:22,145 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 23:04:22,145 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 23:04:22,145 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 23:04:57,870 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 23:04:59,258 - app - INFO - Memory management utilities loaded
2025-06-03 23:04:59,262 - app - INFO - Error handling utilities loaded
2025-06-03 23:04:59,264 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 23:04:59,266 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 23:04:59,269 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 23:04:59,270 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 23:04:59,272 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 23:04:59,273 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 23:04:59,281 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 23:04:59,291 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 23:04:59,299 - app - INFO - Applied NumPy fix
2025-06-03 23:04:59,303 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 23:04:59,304 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 23:04:59,305 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 23:04:59,307 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 23:04:59,309 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 23:04:59,310 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 23:04:59,311 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 23:04:59,312 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 23:05:03,428 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 23:05:03,428 - app - INFO - Applied TensorFlow fix
2025-06-03 23:05:03,430 - app.config - INFO - Configuration initialized
2025-06-03 23:05:03,434 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 23:05:03,444 - models.train - INFO - TensorFlow test successful
2025-06-03 23:05:04,074 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 23:05:04,074 - models.train - INFO - Transformer model is available
2025-06-03 23:05:04,075 - models.train - INFO - Using TensorFlow-based models
2025-06-03 23:05:04,076 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 23:05:04,076 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 23:05:04,079 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 23:05:04,436 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 23:05:04,436 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 23:05:04,436 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 23:05:04,437 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 23:05:04,437 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 23:05:04,437 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 23:05:04,437 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 23:05:04,437 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 23:05:04,438 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 23:05:04,438 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 23:05:04,525 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 23:05:04,528 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:05:04,528 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:05:04,861 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 23:05:05,410 - app.services.llm_service - INFO - llama_cpp is available
2025-06-03 23:05:05,457 - app.utils.session_state - INFO - Initializing session state
2025-06-03 23:05:05,458 - app.utils.session_state - INFO - Initializing session state
2025-06-03 23:05:05,461 - app.utils.session_state - INFO - Session state initialized
2025-06-03 23:05:05,462 - app.utils.session_state - INFO - Session state initialized
2025-06-03 23:05:05,483 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 23:05:05,586 - app.utils.memory_management - INFO - Memory before cleanup: 427.50 MB
2025-06-03 23:05:05,792 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 23:05:05,793 - app.utils.memory_management - INFO - Memory after cleanup: 427.95 MB (freed -0.45 MB)
2025-06-03 23:05:07,993 - app.utils.memory_management - INFO - Memory before cleanup: 433.67 MB
2025-06-03 23:05:08,150 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 23:05:08,150 - app.utils.memory_management - INFO - Memory after cleanup: 433.67 MB (freed 0.00 MB)
2025-06-03 23:05:13,427 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:05:13,459 - app.utils.memory_management - INFO - Memory before cleanup: 434.96 MB
2025-06-03 23:05:13,644 - app.utils.memory_management - INFO - Garbage collection: collected 198 objects
2025-06-03 23:05:13,645 - app.utils.memory_management - INFO - Memory after cleanup: 435.00 MB (freed -0.04 MB)
2025-06-03 23:05:18,549 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:05:18,578 - app.utils.memory_management - INFO - Memory before cleanup: 435.80 MB
2025-06-03 23:05:18,759 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-03 23:05:18,759 - app.utils.memory_management - INFO - Memory after cleanup: 435.80 MB (freed 0.00 MB)
2025-06-03 23:05:19,963 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:05:25,994 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-03 23:05:25,995 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-03 23:05:25,997 - app.utils.memory_management - INFO - Memory before cleanup: 437.18 MB
2025-06-03 23:05:26,199 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-03 23:05:26,204 - app.utils.memory_management - INFO - Memory after cleanup: 437.18 MB (freed 0.00 MB)
2025-06-03 23:05:26,204 - app.utils.memory_management - INFO - Memory before cleanup: 437.18 MB
2025-06-03 23:05:26,388 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 23:05:26,389 - app.utils.memory_management - INFO - Memory after cleanup: 437.18 MB (freed -0.00 MB)
2025-06-03 23:05:43,485 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:05:43,599 - app.utils.memory_management - INFO - Memory before cleanup: 437.50 MB
2025-06-03 23:05:43,901 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-03 23:05:43,902 - app.utils.memory_management - INFO - Memory after cleanup: 437.50 MB (freed 0.00 MB)
2025-06-03 23:05:47,338 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:05:54,832 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 23:05:54,832 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 23:05:55,495 - app.utils.memory_management - INFO - Memory before cleanup: 453.32 MB
2025-06-03 23:05:55,700 - app.utils.memory_management - INFO - Garbage collection: collected 1129 objects
2025-06-03 23:05:55,701 - app.utils.memory_management - INFO - Memory after cleanup: 453.25 MB (freed 0.07 MB)
2025-06-03 23:16:32,286 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 23:16:33,595 - app - INFO - Memory management utilities loaded
2025-06-03 23:16:33,597 - app - INFO - Error handling utilities loaded
2025-06-03 23:16:33,597 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 23:16:33,599 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 23:16:33,599 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 23:16:33,599 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 23:16:33,599 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 23:16:33,599 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 23:16:33,601 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 23:16:33,601 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 23:16:33,601 - app - INFO - Applied NumPy fix
2025-06-03 23:16:33,602 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 23:16:33,602 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 23:16:33,603 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 23:16:33,604 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 23:16:33,605 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 23:16:33,605 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 23:16:33,606 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 23:16:33,606 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 23:16:37,398 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 23:16:37,398 - app - INFO - Applied TensorFlow fix
2025-06-03 23:16:38,530 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 23:16:38,530 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 23:16:38,530 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 23:16:38,535 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 23:16:38,747 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 23:16:38,747 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 23:16:38,749 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 23:16:38,750 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 23:16:38,751 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 23:16:38,752 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 23:16:38,755 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 23:16:38,758 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 23:16:38,759 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 23:16:38,761 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 23:16:38,854 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 23:16:38,864 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 23:16:45,218 - app - INFO - Cleaning up resources...
2025-06-03 23:16:45,222 - app.utils.memory_management - INFO - Memory before cleanup: 403.71 MB
2025-06-03 23:16:45,364 - app.utils.memory_management - INFO - Garbage collection: collected 4 objects
2025-06-03 23:16:45,364 - app.utils.memory_management - INFO - Memory after cleanup: 403.90 MB (freed -0.19 MB)
2025-06-03 23:16:45,364 - app - INFO - Application shutdown complete
2025-06-03 23:17:26,629 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-03 23:17:28,551 - app - INFO - Memory management utilities loaded
2025-06-03 23:17:28,553 - app - INFO - Error handling utilities loaded
2025-06-03 23:17:28,557 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-03 23:17:28,560 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-03 23:17:28,562 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-03 23:17:28,562 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-03 23:17:28,564 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-03 23:17:28,569 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-03 23:17:28,569 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-03 23:17:28,571 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-03 23:17:28,573 - app - INFO - Applied NumPy fix
2025-06-03 23:17:28,575 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 23:17:28,575 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 23:17:28,576 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 23:17:28,576 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-03 23:17:28,576 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 23:17:28,578 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 23:17:28,578 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 23:17:28,578 - app - INFO - Applied NumPy BitGenerator fix
2025-06-03 23:17:33,598 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-03 23:17:33,600 - app - INFO - Applied TensorFlow fix
2025-06-03 23:17:33,605 - app.config - INFO - Configuration initialized
2025-06-03 23:17:33,618 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-03 23:17:33,632 - models.train - INFO - TensorFlow test successful
2025-06-03 23:17:34,439 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-03 23:17:34,439 - models.train - INFO - Transformer model is available
2025-06-03 23:17:34,439 - models.train - INFO - Using TensorFlow-based models
2025-06-03 23:17:34,441 - models.predict - INFO - Transformer model is available for predictions
2025-06-03 23:17:34,441 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-03 23:17:34,443 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-03 23:17:34,780 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 23:17:34,780 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-03 23:17:34,781 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-03 23:17:34,781 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-03 23:17:34,781 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-03 23:17:34,781 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-03 23:17:34,781 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-03 23:17:34,781 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-03 23:17:34,781 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-03 23:17:34,781 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-03 23:17:34,890 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-03 23:17:34,892 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:17:35,352 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-03 23:17:35,993 - app.utils.session_state - INFO - Initializing session state
2025-06-03 23:17:35,995 - app.utils.session_state - INFO - Session state initialized
2025-06-03 23:17:38,064 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 23:17:38,094 - app.utils.memory_management - INFO - Memory before cleanup: 424.75 MB
2025-06-03 23:17:38,315 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-03 23:17:38,317 - app.utils.memory_management - INFO - Memory after cleanup: 424.75 MB (freed -0.00 MB)
2025-06-03 23:17:47,947 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:17:47,973 - app.utils.memory_management - INFO - Memory before cleanup: 428.57 MB
2025-06-03 23:17:48,159 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 23:17:48,160 - app.utils.memory_management - INFO - Memory after cleanup: 428.57 MB (freed 0.00 MB)
2025-06-03 23:17:49,406 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:17:49,559 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.13 seconds
2025-06-03 23:17:49,566 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 23:17:49,566 - app - INFO - Data shape: (581, 36)
2025-06-03 23:17:49,567 - app - INFO - File COMI contains 2025 data
2025-06-03 23:17:49,617 - app - INFO - Feature engineering for COMI completed in 0.05 seconds
2025-06-03 23:17:49,617 - app - INFO - Features shape: (581, 36)
2025-06-03 23:17:49,638 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-03 23:17:49,639 - app - INFO - Date range: 2023-01-02 to 2025-05-29
2025-06-03 23:17:49,639 - app - INFO - Data shape: (581, 36)
2025-06-03 23:17:49,639 - app - INFO - File COMI contains 2025 data
2025-06-03 23:17:49,643 - app.utils.memory_management - INFO - Memory before cleanup: 432.88 MB
2025-06-03 23:17:49,818 - app.utils.memory_management - INFO - Garbage collection: collected 288 objects
2025-06-03 23:17:49,820 - app.utils.memory_management - INFO - Memory after cleanup: 432.91 MB (freed -0.04 MB)
2025-06-03 23:17:50,022 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:17:50,150 - app.utils.memory_management - INFO - Memory before cleanup: 434.09 MB
2025-06-03 23:17:50,337 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-03 23:17:50,337 - app.utils.memory_management - INFO - Memory after cleanup: 434.09 MB (freed 0.00 MB)
2025-06-03 23:18:01,953 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:18:01,987 - app.utils.memory_management - INFO - Memory before cleanup: 434.65 MB
2025-06-03 23:18:02,183 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-03 23:18:02,184 - app.utils.memory_management - INFO - Memory after cleanup: 434.65 MB (freed 0.00 MB)
2025-06-03 23:18:07,116 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:18:07,147 - app.utils.memory_management - INFO - Memory before cleanup: 434.78 MB
2025-06-03 23:18:07,331 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-03 23:18:07,332 - app.utils.memory_management - INFO - Memory after cleanup: 434.78 MB (freed 0.00 MB)
2025-06-03 23:18:08,425 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:18:22,120 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-03 23:18:22,120 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-03 23:18:22,120 - app.utils.memory_management - INFO - Memory before cleanup: 434.93 MB
2025-06-03 23:18:22,324 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-03 23:18:22,326 - app.utils.memory_management - INFO - Memory after cleanup: 434.93 MB (freed 0.00 MB)
2025-06-03 23:18:22,328 - app.utils.memory_management - INFO - Memory before cleanup: 434.93 MB
2025-06-03 23:18:22,514 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 23:18:22,514 - app.utils.memory_management - INFO - Memory after cleanup: 434.93 MB (freed 0.00 MB)
2025-06-03 23:18:38,598 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:18:38,738 - app.utils.memory_management - INFO - Memory before cleanup: 434.94 MB
2025-06-03 23:18:38,951 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-03 23:18:38,951 - app.utils.memory_management - INFO - Memory after cleanup: 434.94 MB (freed 0.00 MB)
2025-06-03 23:18:41,905 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:18:49,557 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 23:18:49,559 - app.components.predictive_analytics - WARNING - Models not fitted, using fallback predictions: This RandomForestRegressor instance is not fitted yet. Call 'fit' with appropriate arguments before using this estimator.
2025-06-03 23:18:50,219 - app.utils.memory_management - INFO - Memory before cleanup: 449.12 MB
2025-06-03 23:18:50,420 - app.utils.memory_management - INFO - Garbage collection: collected 1143 objects
2025-06-03 23:18:50,420 - app.utils.memory_management - INFO - Memory after cleanup: 449.12 MB (freed 0.00 MB)
2025-06-03 23:19:03,862 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:19:04,234 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-03 23:19:04,661 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-03 23:19:04,696 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.03 seconds
2025-06-03 23:19:04,698 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-06-03 23:19:04,698 - app.utils.common - INFO - Data shape: (309, 6)
2025-06-03 23:19:04,698 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-03 23:19:05,124 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.46 seconds
2025-06-03 23:19:05,442 - app.utils.memory_management - INFO - Memory before cleanup: 455.70 MB
2025-06-03 23:19:05,645 - app.utils.memory_management - INFO - Garbage collection: collected 3089 objects
2025-06-03 23:19:05,645 - app.utils.memory_management - INFO - Memory after cleanup: 455.68 MB (freed 0.02 MB)
2025-06-03 23:19:20,043 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:19:20,428 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-03 23:19:20,745 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-03 23:19:20,763 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-06-03 23:19:20,765 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-06-03 23:19:20,765 - app.utils.common - INFO - Data shape: (309, 6)
2025-06-03 23:19:20,766 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-03 23:19:21,060 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.32 seconds
2025-06-03 23:19:21,384 - app.utils.memory_management - INFO - Memory before cleanup: 458.11 MB
2025-06-03 23:19:21,575 - app.utils.memory_management - INFO - Garbage collection: collected 2237 objects
2025-06-03 23:19:21,576 - app.utils.memory_management - INFO - Memory after cleanup: 458.09 MB (freed 0.02 MB)
2025-06-03 23:19:21,819 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:19:21,914 - app.utils.memory_management - INFO - Memory before cleanup: 458.12 MB
2025-06-03 23:19:22,166 - app.utils.memory_management - INFO - Garbage collection: collected 354 objects
2025-06-03 23:19:22,168 - app.utils.memory_management - INFO - Memory after cleanup: 458.12 MB (freed 0.00 MB)
2025-06-03 23:19:24,514 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:19:24,535 - app.utils.memory_management - INFO - Memory before cleanup: 458.18 MB
2025-06-03 23:19:24,733 - app.utils.memory_management - INFO - Garbage collection: collected 179 objects
2025-06-03 23:19:24,734 - app.utils.memory_management - INFO - Memory after cleanup: 458.18 MB (freed 0.00 MB)
2025-06-03 23:19:27,445 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:19:27,466 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-06-03 23:19:28,501 - app.utils.memory_management - INFO - Memory before cleanup: 468.96 MB
2025-06-03 23:19:28,684 - app.utils.memory_management - INFO - Garbage collection: collected 520 objects
2025-06-03 23:19:28,686 - app.utils.memory_management - INFO - Memory after cleanup: 468.96 MB (freed 0.00 MB)
2025-06-03 23:19:31,405 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:19:31,795 - app.utils.memory_management - INFO - Memory before cleanup: 472.93 MB
2025-06-03 23:19:31,987 - app.utils.memory_management - INFO - Garbage collection: collected 324 objects
2025-06-03 23:19:31,988 - app.utils.memory_management - INFO - Memory after cleanup: 472.93 MB (freed 0.00 MB)
2025-06-03 23:19:32,685 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:19:32,710 - scrapers.price_scraper - INFO - Initialized PriceScraper with source: tradingview
2025-06-03 23:19:32,993 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-03 23:19:32,994 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-06-03 23:19:32,995 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-03 23:19:32,996 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-06-03 23:19:33,001 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-03 23:19:33,004 - app.utils.error_handling - INFO - live_trading_component executed in 0.30 seconds
2025-06-03 23:19:33,008 - app.utils.memory_management - INFO - Memory before cleanup: 474.71 MB
2025-06-03 23:19:33,209 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-06-03 23:19:33,211 - app.utils.memory_management - INFO - Memory after cleanup: 474.71 MB (freed 0.00 MB)
2025-06-03 23:19:37,261 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:19:38,530 - scrapers.price_scraper - INFO - Chrome WebDriver initialized successfully
2025-06-03 23:19:38,530 - scrapers.price_scraper - INFO - Fetching TradingView data for EGX:COMI
2025-06-03 23:20:02,212 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-03 23:20:02,213 - app.utils.data_processing - INFO - Found rf model for COMI with 25 minutes horizon
2025-06-03 23:20:02,214 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-03 23:20:02,214 - app.utils.data_processing - INFO - Found rf model for COMI with 69 minutes horizon
2025-06-03 23:20:02,219 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-03 23:20:02,223 - app.utils.error_handling - INFO - live_trading_component executed in 24.93 seconds
2025-06-03 23:20:02,229 - app.utils.memory_management - INFO - Memory before cleanup: 475.87 MB
2025-06-03 23:20:02,479 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-03 23:20:02,480 - app.utils.memory_management - INFO - Memory after cleanup: 475.87 MB (freed 0.00 MB)
2025-06-03 23:26:55,504 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:26:55,570 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 23:26:55,638 - app.utils.memory_management - INFO - Memory before cleanup: 475.96 MB
2025-06-03 23:26:55,959 - app.utils.memory_management - INFO - Garbage collection: collected 219 objects
2025-06-03 23:26:55,963 - app.utils.memory_management - INFO - Memory after cleanup: 475.96 MB (freed 0.00 MB)
2025-06-03 23:27:04,433 - scrapers.price_scraper - INFO - WebDriver closed successfully
2025-06-03 23:27:08,068 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:27:08,075 - app.utils.session_state - INFO - Initializing session state
2025-06-03 23:27:08,082 - app.utils.session_state - INFO - Session state initialized
2025-06-03 23:27:08,102 - app - INFO - Found 8 stock files in data/stocks
2025-06-03 23:27:08,116 - app.utils.memory_management - INFO - Memory before cleanup: 475.99 MB
2025-06-03 23:27:08,326 - app.utils.memory_management - INFO - Garbage collection: collected 222 objects
2025-06-03 23:27:08,327 - app.utils.memory_management - INFO - Memory after cleanup: 475.99 MB (freed 0.00 MB)
2025-06-03 23:27:13,677 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:27:13,704 - app.utils.memory_management - INFO - Memory before cleanup: 475.97 MB
2025-06-03 23:27:13,936 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-03 23:27:13,936 - app.utils.memory_management - INFO - Memory after cleanup: 475.97 MB (freed 0.00 MB)
2025-06-03 23:27:21,117 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:27:21,195 - app - INFO - File COMI contains 2025 data
2025-06-03 23:27:21,239 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-03 23:27:21,244 - app - INFO - Date range: 2023-01-02 to 2025-06-03
2025-06-03 23:27:21,246 - app.utils.memory_management - INFO - Memory before cleanup: 476.02 MB
2025-06-03 23:27:21,467 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-03 23:27:21,468 - app.utils.memory_management - INFO - Memory after cleanup: 476.02 MB (freed 0.00 MB)
2025-06-03 23:27:38,024 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:27:38,107 - app - INFO - File COMI contains 2025 data
2025-06-03 23:27:38,112 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-03 23:27:38,114 - app - INFO - Date range: 2023-01-02 to 2025-06-03
2025-06-03 23:27:38,116 - app.utils.memory_management - INFO - Memory before cleanup: 476.20 MB
2025-06-03 23:27:38,331 - app.utils.memory_management - INFO - Garbage collection: collected 223 objects
2025-06-03 23:27:38,332 - app.utils.memory_management - INFO - Memory after cleanup: 476.20 MB (freed 0.00 MB)
2025-06-03 23:27:39,402 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:27:39,488 - app - INFO - File COMI contains 2025 data
2025-06-03 23:27:39,496 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-03 23:27:39,497 - app - INFO - Date range: 2023-01-02 to 2025-06-03
2025-06-03 23:27:39,502 - app.utils.memory_management - INFO - Memory before cleanup: 476.21 MB
2025-06-03 23:27:39,749 - app.utils.memory_management - INFO - Garbage collection: collected 245 objects
2025-06-03 23:27:39,749 - app.utils.memory_management - INFO - Memory after cleanup: 476.21 MB (freed 0.00 MB)
2025-06-03 23:27:42,025 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:27:42,178 - app - INFO - File COMI contains 2025 data
2025-06-03 23:27:42,250 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-03 23:27:42,252 - app - INFO - Date range: 2023-01-02 to 2025-06-03
2025-06-03 23:27:42,254 - app.utils.memory_management - INFO - Memory before cleanup: 476.22 MB
2025-06-03 23:27:42,490 - app.utils.memory_management - INFO - Garbage collection: collected 253 objects
2025-06-03 23:27:42,491 - app.utils.memory_management - INFO - Memory after cleanup: 476.22 MB (freed 0.00 MB)
2025-06-03 23:27:45,530 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:27:45,690 - app - INFO - File COMI contains 2025 data
2025-06-03 23:27:45,720 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-03 23:27:45,721 - app - INFO - Date range: 2023-01-02 to 2025-06-03
2025-06-03 23:27:45,723 - app.utils.memory_management - INFO - Memory before cleanup: 476.23 MB
2025-06-03 23:27:45,929 - app.utils.memory_management - INFO - Garbage collection: collected 256 objects
2025-06-03 23:27:45,930 - app.utils.memory_management - INFO - Memory after cleanup: 476.23 MB (freed 0.00 MB)
2025-06-03 23:27:53,472 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:27:53,500 - app.utils.state_manager - INFO - Found 8 stock files in data/stocks
2025-06-03 23:27:54,056 - app.utils.memory_management - INFO - Memory before cleanup: 477.30 MB
2025-06-03 23:27:54,327 - app.utils.memory_management - INFO - Garbage collection: collected 347 objects
2025-06-03 23:27:54,331 - app.utils.memory_management - INFO - Memory after cleanup: 477.30 MB (freed 0.00 MB)
2025-06-03 23:28:10,989 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:11,395 - app.utils.memory_management - INFO - Memory before cleanup: 477.43 MB
2025-06-03 23:28:11,589 - app.utils.memory_management - INFO - Garbage collection: collected 386 objects
2025-06-03 23:28:11,590 - app.utils.memory_management - INFO - Memory after cleanup: 477.43 MB (freed 0.00 MB)
2025-06-03 23:28:11,982 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:12,010 - app.utils.memory_management - INFO - Memory before cleanup: 477.39 MB
2025-06-03 23:28:12,229 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-06-03 23:28:12,229 - app.utils.memory_management - INFO - Memory after cleanup: 477.39 MB (freed 0.00 MB)
2025-06-03 23:28:14,653 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:14,706 - app.utils.memory_management - INFO - Memory before cleanup: 477.39 MB
2025-06-03 23:28:14,941 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-06-03 23:28:14,941 - app.utils.memory_management - INFO - Memory after cleanup: 477.39 MB (freed 0.00 MB)
2025-06-03 23:28:15,883 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:15,923 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-03 23:28:15,926 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:15,927 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-03 23:28:15,934 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:15,935 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-03 23:28:15,936 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-03 23:28:15,937 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-03 23:28:15,944 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-03 23:28:15,946 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-03 23:28:15,963 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:15,975 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-03 23:28:15,986 - app.utils.data_processing - INFO - Found lstm model for COMI with 10080 minutes horizon using glob
2025-06-03 23:28:15,992 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-03 23:28:15,994 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-03 23:28:15,995 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-03 23:28:16,003 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,009 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-03 23:28:16,013 - app.utils.data_processing - INFO - Found gb model for COMI with 10080 minutes horizon
2025-06-03 23:28:16,015 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-03 23:28:16,017 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-03 23:28:16,017 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-03 23:28:16,028 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,029 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-03 23:28:16,030 - app.utils.data_processing - INFO - Found lr model for COMI with 10080 minutes horizon
2025-06-03 23:28:16,030 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-03 23:28:16,031 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:16,032 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-03 23:28:16,037 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,042 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-03 23:28:16,043 - app.utils.data_processing - INFO - Found rf model for COMI with 10080 minutes horizon
2025-06-03 23:28:16,077 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-03 23:28:16,086 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-03 23:28:16,086 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-03 23:28:16,094 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,114 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-03 23:28:16,114 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-03 23:28:16,115 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-03 23:28:16,115 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-03 23:28:16,115 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-03 23:28:16,116 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-03 23:28:16,118 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-03 23:28:16,118 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-03 23:28:16,118 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-03 23:28:16,119 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-03 23:28:16,120 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-03 23:28:16,121 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-03 23:28:16,121 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-03 23:28:16,121 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-03 23:28:16,123 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-03 23:28:16,126 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-03 23:28:16,126 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-03 23:28:16,128 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-03 23:28:16,128 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-06-03 23:28:16,327 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-06-03 23:28:16,367 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-03 23:28:16,369 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:16,369 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-03 23:28:16,376 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,377 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-03 23:28:16,377 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-03 23:28:16,383 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-03 23:28:16,383 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-03 23:28:16,393 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,402 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-03 23:28:16,403 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-03 23:28:16,404 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-03 23:28:16,405 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-03 23:28:16,412 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,413 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-03 23:28:16,414 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-03 23:28:16,414 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-03 23:28:16,415 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-03 23:28:16,420 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,421 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-03 23:28:16,425 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-03 23:28:16,426 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:16,427 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-03 23:28:16,433 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:16,434 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-03 23:28:16,445 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:16,455 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-03 23:28:16,459 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-03 23:28:16,462 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-03 23:28:16,464 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:16,503 - app.utils.memory_management - INFO - Memory before cleanup: 477.40 MB
2025-06-03 23:28:16,712 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-03 23:28:16,712 - app.utils.memory_management - INFO - Memory after cleanup: 477.40 MB (freed 0.00 MB)
2025-06-03 23:28:18,068 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:18,118 - app.utils.data_processing - INFO - Found lstm model for COMI with 5 minutes horizon
2025-06-03 23:28:18,125 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-03 23:28:18,126 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-03 23:28:18,133 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:18,142 - app.utils.numpy_compatibility - INFO - Applying comprehensive NumPy compatibility fix (NumPy version: 1.26.4)
2025-06-03 23:28:18,142 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.bit_generator.BitGenerator
2025-06-03 23:28:18,144 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.BitGenerator
2025-06-03 23:28:18,144 - app.utils.numpy_compatibility - INFO - Fixed numpy.random.MT19937
2025-06-03 23:28:18,144 - app.utils.numpy_compatibility - INFO - Fixed numpy.random._mt19937.MT19937
2025-06-03 23:28:18,146 - app.utils.numpy_compatibility - INFO - Fixed app.utils.numpy_fix.FixedMT19937
2025-06-03 23:28:18,148 - app.utils.numpy_compatibility - INFO - Detected scikit-learn version: 1.4.2
2025-06-03 23:28:18,148 - app.utils.numpy_compatibility - INFO - Fixed sklearn.utils._random.check_random_state
2025-06-03 23:28:18,150 - app.utils.numpy_compatibility - INFO - Comprehensive NumPy compatibility fix successfully applied
2025-06-03 23:28:18,150 - app.components.ensemble_predictions - INFO - Applied comprehensive NumPy compatibility fix before ensemble predictions
2025-06-03 23:28:18,367 - app.components.ensemble_predictions - INFO - Forced garbage collection before ensemble predictions
2025-06-03 23:28:18,405 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-03 23:28:18,406 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:18,406 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-03 23:28:18,415 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:18,416 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-03 23:28:18,416 - app.utils.data_processing - INFO - Found lstm model for COMI with 4 minutes horizon
2025-06-03 23:28:18,423 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-03 23:28:18,428 - app.utils.data_processing - INFO - Found lstm model for COMI with 30 minutes horizon
2025-06-03 23:28:18,433 - app.utils.data_processing - INFO - Found lstm model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:18,443 - app.utils.data_processing - INFO - Found lstm model for COMI with 1440 minutes horizon using glob
2025-06-03 23:28:18,444 - app.utils.data_processing - INFO - Found gb model for COMI with 4 minutes horizon
2025-06-03 23:28:18,444 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-03 23:28:18,445 - app.utils.data_processing - INFO - Found gb model for COMI with 30 minutes horizon
2025-06-03 23:28:18,454 - app.utils.data_processing - INFO - Found gb model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:18,454 - app.utils.data_processing - INFO - Found gb model for COMI with 1440 minutes horizon
2025-06-03 23:28:18,455 - app.utils.data_processing - INFO - Found lr model for COMI with 4 minutes horizon
2025-06-03 23:28:18,456 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-03 23:28:18,456 - app.utils.data_processing - INFO - Found lr model for COMI with 30 minutes horizon
2025-06-03 23:28:18,464 - app.utils.data_processing - INFO - Found lr model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:18,465 - app.utils.data_processing - INFO - Found lr model for COMI with 1440 minutes horizon
2025-06-03 23:28:18,465 - app.utils.data_processing - INFO - Found rf model for COMI with 4 minutes horizon
2025-06-03 23:28:18,466 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:18,466 - app.utils.data_processing - INFO - Found rf model for COMI with 30 minutes horizon
2025-06-03 23:28:18,472 - app.utils.data_processing - INFO - Found rf model for COMI with 60 minutes horizon using glob
2025-06-03 23:28:18,473 - app.utils.data_processing - INFO - Found rf model for COMI with 1440 minutes horizon
2025-06-03 23:28:18,481 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:18,488 - app.utils.data_processing - INFO - Found lstm model for COMI with 15 minutes horizon using glob
2025-06-03 23:28:18,488 - app.utils.data_processing - INFO - Found gb model for COMI with 15 minutes horizon
2025-06-03 23:28:18,488 - app.utils.data_processing - INFO - Found lr model for COMI with 15 minutes horizon
2025-06-03 23:28:18,488 - app.utils.data_processing - INFO - Found rf model for COMI with 15 minutes horizon
2025-06-03 23:28:18,501 - app.utils.memory_management - INFO - Memory before cleanup: 477.77 MB
2025-06-03 23:28:18,733 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-03 23:28:18,733 - app.utils.memory_management - INFO - Memory after cleanup: 477.77 MB (freed 0.00 MB)
2025-06-03 23:28:26,354 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:26,388 - app.utils.memory_management - INFO - Memory before cleanup: 477.77 MB
2025-06-03 23:28:26,592 - app.utils.memory_management - INFO - Garbage collection: collected 322 objects
2025-06-03 23:28:26,593 - app.utils.memory_management - INFO - Memory after cleanup: 477.77 MB (freed 0.00 MB)
2025-06-03 23:28:32,595 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:32,630 - app.utils.memory_management - INFO - Memory before cleanup: 477.73 MB
2025-06-03 23:28:32,845 - app.utils.memory_management - INFO - Garbage collection: collected 192 objects
2025-06-03 23:28:32,845 - app.utils.memory_management - INFO - Memory after cleanup: 477.73 MB (freed 0.00 MB)
2025-06-03 23:28:33,768 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:33,869 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-06-03 23:28:33,887 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-06-03 23:28:33,902 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-06-03 23:28:33,920 - app.components.performance_metrics - INFO - Found price 82.85 for COMI at 2025-05-29 00:00:00 (within 12.7 hours of target)
2025-06-03 23:28:33,937 - app.components.performance_metrics - INFO - Found price 82.85 for COMI at 2025-05-29 00:00:00 (within 13.2 hours of target)
2025-06-03 23:28:33,953 - app.components.performance_metrics - INFO - Found price 82.85 for COMI at 2025-05-29 00:00:00 (within 13.5 hours of target)
2025-06-03 23:28:33,968 - app.components.performance_metrics - INFO - Found price 82.85 for COMI at 2025-05-29 00:00:00 (within 13.6 hours of target)
2025-06-03 23:28:33,985 - app.components.performance_metrics - INFO - Found price 82.85 for COMI at 2025-05-29 00:00:00 (within 13.8 hours of target)
2025-06-03 23:28:34,086 - app.components.performance_metrics - INFO - Updated performance metrics for 7 model-symbol combinations
2025-06-03 23:28:34,369 - app.utils.memory_management - INFO - Memory before cleanup: 478.52 MB
2025-06-03 23:28:34,588 - app.utils.memory_management - INFO - Garbage collection: collected 1605 objects
2025-06-03 23:28:34,591 - app.utils.memory_management - INFO - Memory after cleanup: 478.52 MB (freed 0.00 MB)
2025-06-03 23:28:41,665 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:41,748 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 34.1 hours
2025-06-03 23:28:41,801 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 31.1 hours
2025-06-03 23:28:41,842 - app.components.performance_metrics - WARNING - Closest data point for COMI is too far from target: 27.7 hours
2025-06-03 23:28:42,221 - app.utils.memory_management - INFO - Memory before cleanup: 479.21 MB
2025-06-03 23:28:42,592 - app.utils.memory_management - INFO - Garbage collection: collected 1547 objects
2025-06-03 23:28:42,594 - app.utils.memory_management - INFO - Memory after cleanup: 479.21 MB (freed 0.00 MB)
2025-06-03 23:28:43,618 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:28:43,664 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-03 23:28:43,704 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-03 23:28:43,706 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-03
2025-06-03 23:28:43,708 - app.utils.common - INFO - Data shape: (584, 36)
2025-06-03 23:28:43,710 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-03 23:28:43,820 - app.utils.memory_management - INFO - Memory before cleanup: 479.20 MB
2025-06-03 23:28:44,074 - app.utils.memory_management - INFO - Garbage collection: collected 190 objects
2025-06-03 23:28:44,076 - app.utils.memory_management - INFO - Memory after cleanup: 479.20 MB (freed 0.00 MB)
2025-06-03 23:29:09,148 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:29:09,195 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-03 23:29:09,231 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-03 23:29:09,232 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-03
2025-06-03 23:29:09,232 - app.utils.common - INFO - Data shape: (584, 36)
2025-06-03 23:29:09,233 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-03 23:29:09,311 - app.utils.memory_management - INFO - Memory before cleanup: 479.20 MB
2025-06-03 23:29:09,545 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-06-03 23:29:09,557 - app.utils.memory_management - INFO - Memory after cleanup: 479.20 MB (freed 0.00 MB)
2025-06-03 23:29:10,318 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:29:10,364 - app.utils.memory_management - INFO - Memory before cleanup: 479.19 MB
2025-06-03 23:29:10,569 - app.utils.memory_management - INFO - Garbage collection: collected 280 objects
2025-06-03 23:29:10,571 - app.utils.memory_management - INFO - Memory after cleanup: 479.19 MB (freed 0.00 MB)
2025-06-03 23:29:32,802 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:29:32,853 - app.utils.memory_management - INFO - Memory before cleanup: 479.19 MB
2025-06-03 23:29:33,065 - app.utils.memory_management - INFO - Garbage collection: collected 201 objects
2025-06-03 23:29:33,065 - app.utils.memory_management - INFO - Memory after cleanup: 479.19 MB (freed 0.00 MB)
2025-06-03 23:29:33,658 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:29:33,967 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-03 23:29:34,227 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-03 23:29:34,238 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.00 seconds
2025-06-03 23:29:34,239 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-06-03 23:29:34,242 - app.utils.common - INFO - Data shape: (309, 6)
2025-06-03 23:29:34,243 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-03 23:29:34,528 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.30 seconds
2025-06-03 23:29:34,865 - app.utils.memory_management - INFO - Memory before cleanup: 479.32 MB
2025-06-03 23:29:35,203 - app.utils.memory_management - INFO - Garbage collection: collected 3550 objects
2025-06-03 23:29:35,207 - app.utils.memory_management - INFO - Memory after cleanup: 479.30 MB (freed 0.02 MB)
2025-06-03 23:29:42,845 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:29:43,231 - app.components.indicator_templates - ERROR - Error saving strategy preset: [Errno 2] No such file or directory: 'strategy_presets\\Support/Resistance.json'
2025-06-03 23:29:43,527 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-03 23:29:43,540 - app.utils.common - INFO - Loaded stock data for ABUK from data/stocks\ABUK.csv in 0.01 seconds
2025-06-03 23:29:43,542 - app.utils.common - INFO - Date range: 2023-12-31 to 2025-04-10
2025-06-03 23:29:43,543 - app.utils.common - INFO - Data shape: (309, 6)
2025-06-03 23:29:43,544 - app.utils.common - INFO - File ABUK contains 2025 data
2025-06-03 23:29:43,827 - app.utils.error_handling - INFO - interactive_dashboard_component executed in 0.30 seconds
2025-06-03 23:29:44,112 - app.utils.memory_management - INFO - Memory before cleanup: 479.40 MB
2025-06-03 23:29:44,574 - app.utils.memory_management - INFO - Garbage collection: collected 3603 objects
2025-06-03 23:29:44,576 - app.utils.memory_management - INFO - Memory after cleanup: 479.39 MB (freed 0.00 MB)
2025-06-03 23:29:44,796 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:29:45,195 - app.utils.memory_management - INFO - Memory before cleanup: 480.14 MB
2025-06-03 23:29:45,403 - app.utils.memory_management - INFO - Garbage collection: collected 499 objects
2025-06-03 23:29:45,403 - app.utils.memory_management - INFO - Memory after cleanup: 480.14 MB (freed 0.00 MB)
2025-06-03 23:30:02,246 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:30:02,622 - app.utils.memory_management - INFO - Memory before cleanup: 480.15 MB
2025-06-03 23:30:02,864 - app.utils.memory_management - INFO - Garbage collection: collected 385 objects
2025-06-03 23:30:02,864 - app.utils.memory_management - INFO - Memory after cleanup: 480.15 MB (freed 0.00 MB)
2025-06-03 23:30:03,165 - app - INFO - Using TensorFlow-based LSTM model
2025-06-03 23:30:03,192 - app.utils.memory_management - INFO - Memory before cleanup: 480.12 MB
2025-06-03 23:30:03,403 - app.utils.memory_management - INFO - Garbage collection: collected 240 objects
2025-06-03 23:30:03,403 - app.utils.memory_management - INFO - Memory after cleanup: 480.12 MB (freed 0.00 MB)
2025-06-03 23:32:39,355 - app - INFO - Cleaning up resources...
2025-06-03 23:32:39,355 - app.utils.memory_management - INFO - Memory before cleanup: 479.87 MB
2025-06-03 23:32:39,527 - app.utils.memory_management - INFO - Garbage collection: collected 254 objects
2025-06-03 23:32:39,527 - app.utils.memory_management - INFO - Memory after cleanup: 479.87 MB (freed 0.00 MB)
2025-06-03 23:32:39,527 - app - INFO - Application shutdown complete
