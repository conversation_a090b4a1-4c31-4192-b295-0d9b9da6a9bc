2025-06-23 18:03:36,301 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-23 18:03:39,462 - app - INFO - Memory management utilities loaded
2025-06-23 18:03:39,465 - app - INFO - Error handling utilities loaded
2025-06-23 18:03:39,468 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-23 18:03:39,470 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-23 18:03:39,470 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-23 18:03:39,471 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-23 18:03:39,471 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-23 18:03:39,472 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-23 18:03:39,472 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-23 18:03:39,473 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-23 18:03:39,473 - app - INFO - Applied NumPy fix
2025-06-23 18:03:39,494 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-23 18:03:39,496 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-23 18:03:39,496 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-23 18:03:39,496 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-23 18:03:39,497 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-23 18:03:39,497 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-23 18:03:39,497 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-23 18:03:39,498 - app - INFO - Applied NumPy BitGenerator fix
2025-06-23 18:04:17,209 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-23 18:04:17,209 - app - INFO - Applied TensorFlow fix
2025-06-23 18:04:17,214 - app.config - INFO - Configuration initialized
2025-06-23 18:04:17,225 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-23 18:04:17,528 - models.train - INFO - TensorFlow test successful
2025-06-23 18:04:25,092 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-23 18:04:25,092 - models.train - INFO - Transformer model is available
2025-06-23 18:04:25,092 - models.train - INFO - Using TensorFlow-based models
2025-06-23 18:04:25,100 - models.predict - INFO - Transformer model is available for predictions
2025-06-23 18:04:25,102 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-23 18:04:25,107 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-23 18:04:32,276 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-23 18:04:32,276 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-23 18:04:32,276 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-23 18:04:32,277 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-23 18:04:32,277 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-23 18:04:32,277 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-23 18:04:32,277 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-23 18:04:32,277 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-23 18:04:32,278 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-23 18:04:32,278 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-23 18:04:32,615 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-23 18:04:32,619 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:04:33,227 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-23 18:04:35,724 - app.pages.advanced_technical_analysis - WARNING - TA-Lib not available. Some advanced indicators will use simplified calculations.
2025-06-23 18:04:36,043 - app.utils.session_state - INFO - Initializing session state
2025-06-23 18:04:36,044 - app.utils.session_state - INFO - Session state initialized
2025-06-23 18:04:37,346 - app - INFO - Found 14 stock files in data/stocks
2025-06-23 18:04:37,368 - app.utils.memory_management - INFO - Memory before cleanup: 431.95 MB
2025-06-23 18:04:37,563 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-23 18:04:37,563 - app.utils.memory_management - INFO - Memory after cleanup: 431.96 MB (freed -0.01 MB)
2025-06-23 18:04:47,533 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:04:47,567 - app.utils.memory_management - INFO - Memory before cleanup: 434.53 MB
2025-06-23 18:04:47,786 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-23 18:04:47,786 - app.utils.memory_management - INFO - Memory after cleanup: 434.53 MB (freed 0.00 MB)
2025-06-23 18:04:49,044 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:04:49,260 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.19 seconds
2025-06-23 18:04:49,272 - app - INFO - Date range: 2022-08-05 to 2025-06-19
2025-06-23 18:04:49,273 - app - INFO - Data shape: (750, 6)
2025-06-23 18:04:49,275 - app - INFO - File COMI contains 2025 data
2025-06-23 18:04:49,353 - app - INFO - Feature engineering for COMI completed in 0.08 seconds
2025-06-23 18:04:49,355 - app - INFO - Features shape: (750, 36)
2025-06-23 18:04:49,365 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-23 18:04:49,367 - app - INFO - Date range: 2022-08-05 to 2025-06-19
2025-06-23 18:04:49,367 - app - INFO - Data shape: (750, 6)
2025-06-23 18:04:49,367 - app - INFO - File COMI contains 2025 data
2025-06-23 18:04:49,374 - app.utils.memory_management - INFO - Memory before cleanup: 437.62 MB
2025-06-23 18:04:49,554 - app.utils.memory_management - INFO - Garbage collection: collected 212 objects
2025-06-23 18:04:49,555 - app.utils.memory_management - INFO - Memory after cleanup: 437.66 MB (freed -0.04 MB)
2025-06-23 18:04:49,746 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:04:49,957 - app.utils.memory_management - INFO - Memory before cleanup: 438.62 MB
2025-06-23 18:04:50,139 - app.utils.memory_management - INFO - Garbage collection: collected 115 objects
2025-06-23 18:04:50,139 - app.utils.memory_management - INFO - Memory after cleanup: 438.62 MB (freed 0.00 MB)
2025-06-23 18:04:54,981 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:04:55,037 - app.utils.memory_management - INFO - Memory before cleanup: 439.27 MB
2025-06-23 18:04:55,253 - app.utils.memory_management - INFO - Garbage collection: collected 202 objects
2025-06-23 18:04:55,271 - app.utils.memory_management - INFO - Memory after cleanup: 439.27 MB (freed 0.00 MB)
2025-06-23 18:05:00,723 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:05:00,749 - app.utils.memory_management - INFO - Memory before cleanup: 439.27 MB
2025-06-23 18:05:00,953 - app.utils.memory_management - INFO - Garbage collection: collected 180 objects
2025-06-23 18:05:00,954 - app.utils.memory_management - INFO - Memory after cleanup: 439.27 MB (freed 0.00 MB)
2025-06-23 18:05:02,009 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:05:02,040 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-23 18:05:02,050 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-23 18:05:02,189 - app.utils.memory_management - INFO - Memory before cleanup: 439.61 MB
2025-06-23 18:05:02,396 - app.utils.memory_management - INFO - Garbage collection: collected 180 objects
2025-06-23 18:05:02,396 - app.utils.memory_management - INFO - Memory after cleanup: 439.61 MB (freed -0.00 MB)
2025-06-23 18:05:09,000 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:05:09,026 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-23 18:05:09,035 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-23 18:05:09,129 - app.utils.memory_management - INFO - Memory before cleanup: 439.80 MB
2025-06-23 18:05:09,338 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-23 18:05:09,340 - app.utils.memory_management - INFO - Memory after cleanup: 439.80 MB (freed 0.00 MB)
2025-06-23 18:05:12,582 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:05:12,616 - app.utils.historical_data_downloader - INFO - Testing API connection to http://127.0.0.1:8000
2025-06-23 18:05:12,621 - app.utils.historical_data_downloader - INFO - API server is running and accessible
2025-06-23 18:05:12,678 - app.utils.historical_data_downloader - INFO - Starting historical data generation for COMI (3 years)
2025-06-23 18:05:12,679 - app.utils.historical_data_downloader - INFO - Requested intervals: ['1D', '1W']
2025-06-23 18:05:12,686 - app.utils.historical_data_downloader - INFO - API status check: 200
2025-06-23 18:05:12,687 - app.utils.historical_data_downloader - INFO - Fetching current live data for COMI...
2025-06-23 18:05:12,688 - app.utils.historical_data_downloader - INFO - Downloading historical data for COMI with intervals: ['1D', '1W']
2025-06-23 18:05:12,688 - app.utils.historical_data_downloader - INFO - API URL: http://127.0.0.1:8000/api/scrape_pairs
2025-06-23 18:05:12,688 - app.utils.historical_data_downloader - INFO - Request payload: {'pairs': ['EGX-COMI'], 'intervals': ['1D', '1W']}
2025-06-23 18:05:28,785 - app.utils.historical_data_downloader - INFO - API response status: 200
2025-06-23 18:05:28,786 - app.utils.historical_data_downloader - INFO - API response structure: {'success': True, 'data': {'EGX-COMI': [{'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 79779.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 79940.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 80064.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 80425.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 80061.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 80295.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 79861.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 79715.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 79614.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 78818.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 79138.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 80375.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79940.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 80499.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79176.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 80141.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 80539.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 79955.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 79518.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 79849.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 79461.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 78969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 80885.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 73414.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 76127.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 62375.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 56555.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 77925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 79631.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79983.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]}, 'message': 'Successfully scraped 1 pairs'}
2025-06-23 18:05:28,788 - app.utils.historical_data_downloader - INFO - Data keys: ['EGX-COMI']
2025-06-23 18:05:28,788 - app.utils.historical_data_downloader - INFO - Found data for EGX-COMI: [{'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 79779.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 79940.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 80064.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 80425.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 80061.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 80295.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 79861.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 79715.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 79614.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 78818.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 79138.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 80375.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79940.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 80499.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79176.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 80141.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 80539.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 79955.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 79518.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 79849.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 79461.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 78969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 80885.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 73414.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 76127.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 62375.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 56555.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 77925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 79631.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79983.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-06-23 18:05:28,788 - app.utils.historical_data_downloader - INFO - Processing 2 data points for COMI
2025-06-23 18:05:28,790 - app.utils.historical_data_downloader - INFO - Raw data structure: [{'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 79779.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 79940.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 80064.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 80425.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 80061.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 80295.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 79861.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 79715.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 79614.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 78818.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 79138.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 80375.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79940.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 80499.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79176.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}, {'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 80141.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 80539.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 79955.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 79518.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 79849.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 79461.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 78969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 80885.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 73414.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 76127.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 62375.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 56555.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 77925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 79631.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79983.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}]
2025-06-23 18:05:28,791 - app.utils.historical_data_downloader - INFO - First item structure: {'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 79779.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 79940.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 80064.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 80425.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 80061.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 80295.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 79861.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 79715.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 79614.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 78818.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 79138.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 80375.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79940.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 80499.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79176.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}
2025-06-23 18:05:28,793 - app.utils.historical_data_downloader - INFO - Processing interval 1D: pair=EGX-COMI, price=79120.0
2025-06-23 18:05:28,793 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 79120.0 -> 79.12 EGP
2025-06-23 18:05:28,794 - app.utils.historical_data_downloader - INFO - Successfully processed 1D: price=79.12 EGP
2025-06-23 18:05:28,794 - app.utils.historical_data_downloader - INFO - Processing interval 1W: pair=EGX-COMI, price=79120.0
2025-06-23 18:05:28,795 - app.utils.historical_data_downloader - INFO - Converted price from piasters: 79120.0 -> 79.12 EGP
2025-06-23 18:05:28,798 - app.utils.historical_data_downloader - INFO - Successfully processed 1W: price=79.12 EGP
2025-06-23 18:05:28,805 - app.utils.historical_data_downloader - INFO - Successfully processed 2 intervals for COMI
2025-06-23 18:05:28,809 - app.utils.historical_data_downloader - INFO - Successfully processed historical data for COMI
2025-06-23 18:05:28,813 - app.utils.historical_data_downloader - INFO - Successfully retrieved current data for COMI: {'1D': {'symbol': 'COMI', 'interval': '1D', 'current_price': 79.12, 'timestamp': datetime.datetime(2025, 6, 23, 18, 5, 28, 794072), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 79779.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 79940.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 80064.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 80425.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 80061.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 80295.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 79861.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 79715.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 79614.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 78818.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 79138.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 80375.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79940.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 80499.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79176.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 79779.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 79940.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 80064.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 80425.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 80061.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 80295.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 79861.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 79715.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 79614.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 78818.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 79138.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 80375.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 79940.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 80499.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79176.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 89933.0, 'fibo': 85683.0, 'camarilla': 84019.0, 'woodie': 89225.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 85683.0, 'fibo': 84060.0, 'camarilla': 83629.0, 'woodie': 86038.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 84267.0, 'fibo': 83057.0, 'camarilla': 83240.0, 'woodie': 84975.0, 'dm': 84975.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 81433.0, 'fibo': 81433.0, 'camarilla': 81433.0, 'woodie': 81788.0, 'dm': 81788.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 80017.0, 'fibo': 79810.0, 'camarilla': 82460.0, 'woodie': 80725.0, 'dm': 80725.0}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 77183.0, 'fibo': 78807.0, 'camarilla': 82071.0, 'woodie': 77538.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1D', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 72933.0, 'fibo': 77183.0, 'camarilla': 81681.0, 'woodie': 76475.0, 'dm': None}]}}, '1W': {'symbol': 'COMI', 'interval': '1W', 'current_price': 79.12, 'timestamp': datetime.datetime(2025, 6, 23, 18, 5, 28, 798926), 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 80141.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 80539.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 79955.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 79518.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 79849.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 79461.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 78969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 80885.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 73414.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 76127.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 62375.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 56555.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 77925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 79631.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79983.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}], 'raw_data': {'pair': 'EGX-COMI', 'price': 79120.0, 'oscillators': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Relative Strength Index (14)', 'value': 46285.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic %K (14, 3, 3)', 'value': 36942.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Commodity Channel Index (20)', 'value': -69230.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Average Directional Index (14)', 'value': 20846.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Awesome Oscillator', 'value': -1237.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Momentum (10)', 'value': -2770.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'MACD Level (12, 26)', 'value': -204.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Stochastic RSI Fast (3, 3, 14, 14)', 'value': 24840.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Williams Percent Range (14)', 'value': -64089.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Bull Bear Power', 'value': -720.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ultimate Oscillator (7, 14, 28)', 'value': 50911.0, 'action': 'Neutral'}], 'moving_averages': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (10)', 'value': 80141.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (10)', 'value': 80539.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (20)', 'value': 79955.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (20)', 'value': 79518.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (30)', 'value': 79849.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (30)', 'value': 79461.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (50)', 'value': 78969.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (50)', 'value': 80885.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (100)', 'value': 73414.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (100)', 'value': 76127.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Exponential Moving Average (200)', 'value': 62375.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Simple Moving Average (200)', 'value': 56555.0, 'action': 'Buy'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Ichimoku Base Line (9, 26, 52, 26)', 'value': 77925.0, 'action': 'Neutral'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Volume Weighted Moving Average (20)', 'value': 79631.0, 'action': 'Sell'}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'name': 'Hull Moving Average (9)', 'value': 79983.0, 'action': 'Sell'}], 'pivots': [{'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R3', 'classic': 137203.0, 'fibo': 109703.0, 'camarilla': 86673.0, 'woodie': 119750.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R2', 'classic': 109703.0, 'fibo': 99198.0, 'camarilla': 84152.0, 'woodie': 108625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'R1', 'classic': 94407.0, 'fibo': 92708.0, 'camarilla': 81631.0, 'woodie': 92250.0, 'dm': 102055.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'P', 'classic': 82203.0, 'fibo': 82203.0, 'camarilla': 82203.0, 'woodie': 81125.0, 'dm': 86028.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S1', 'classic': 66907.0, 'fibo': 71698.0, 'camarilla': 76589.0, 'woodie': 64750.0, 'dm': 74555.0}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S2', 'classic': 54703.0, 'fibo': 65208.0, 'camarilla': 74068.0, 'woodie': 53625.0, 'dm': None}, {'pair': 'EGX-COMI', 'interval': '1W', 'register_time': '23/06/2025 18:05', 'pivot': 'S3', 'classic': 27203.0, 'fibo': 54703.0, 'camarilla': 71548.0, 'woodie': 37250.0, 'dm': None}]}}}
2025-06-23 18:05:28,814 - app.utils.historical_data_downloader - INFO - Found valid price from 1D: 79.12 EGP
2025-06-23 18:05:28,818 - app.utils.historical_data_downloader - INFO - Using current price for COMI: 79.12 EGP
2025-06-23 18:05:28,819 - app.utils.historical_data_downloader - INFO - Generating 3 years of synthetic historical data...
2025-06-23 18:05:28,821 - app.utils.historical_data_downloader - INFO - Generating 3 years of synthetic data for COMI
2025-06-23 18:05:28,894 - app.utils.historical_data_downloader - INFO - Generated 750 days of synthetic historical data for COMI
2025-06-23 18:05:28,896 - app.utils.historical_data_downloader - INFO - Generated 750 days of synthetic data
2025-06-23 18:05:28,896 - app.utils.historical_data_downloader - INFO - Saving data to: data\stocks\COMI.csv
2025-06-23 18:05:28,942 - app.utils.historical_data_downloader - INFO - Saved 750 days of historical data to data\stocks\COMI.csv
2025-06-23 18:05:28,942 - app.utils.historical_data_downloader - INFO - CSV file size: 33626 bytes
2025-06-23 18:05:28,955 - app.utils.historical_data_downloader - INFO - Verification: CSV contains 750 rows
2025-06-23 18:05:28,959 - app.utils.historical_data_downloader - INFO - Successfully completed historical data generation for COMI
2025-06-23 18:05:29,264 - app.utils.memory_management - INFO - Memory before cleanup: 440.17 MB
2025-06-23 18:05:29,554 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-23 18:05:29,555 - app.utils.memory_management - INFO - Memory after cleanup: 440.12 MB (freed 0.05 MB)
2025-06-23 18:05:55,106 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:05:55,168 - app.utils.memory_management - INFO - Memory before cleanup: 440.49 MB
2025-06-23 18:05:55,468 - app.utils.memory_management - INFO - Garbage collection: collected 260 objects
2025-06-23 18:05:55,469 - app.utils.memory_management - INFO - Memory after cleanup: 440.49 MB (freed 0.00 MB)
2025-06-23 18:06:03,351 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:03,412 - app - INFO - File COMI contains 2025 data
2025-06-23 18:06:03,437 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-23 18:06:03,438 - app - INFO - Date range: 2022-08-09 to 2025-06-23
2025-06-23 18:06:03,442 - app.utils.memory_management - INFO - Memory before cleanup: 440.55 MB
2025-06-23 18:06:03,663 - app.utils.memory_management - INFO - Garbage collection: collected 208 objects
2025-06-23 18:06:03,664 - app.utils.memory_management - INFO - Memory after cleanup: 440.55 MB (freed 0.00 MB)
2025-06-23 18:06:08,009 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:08,071 - app - INFO - File COMI contains 2025 data
2025-06-23 18:06:08,080 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-23 18:06:08,081 - app - INFO - Date range: 2022-08-09 to 2025-06-23
2025-06-23 18:06:08,083 - app.utils.memory_management - INFO - Memory before cleanup: 440.55 MB
2025-06-23 18:06:08,299 - app.utils.memory_management - INFO - Garbage collection: collected 251 objects
2025-06-23 18:06:08,300 - app.utils.memory_management - INFO - Memory after cleanup: 440.55 MB (freed 0.00 MB)
2025-06-23 18:06:11,431 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:11,541 - app - INFO - File COMI contains 2025 data
2025-06-23 18:06:11,557 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-23 18:06:11,558 - app - INFO - Date range: 2022-08-09 to 2025-06-23
2025-06-23 18:06:11,560 - app.utils.memory_management - INFO - Memory before cleanup: 440.62 MB
2025-06-23 18:06:11,757 - app.utils.memory_management - INFO - Garbage collection: collected 225 objects
2025-06-23 18:06:11,757 - app.utils.memory_management - INFO - Memory after cleanup: 440.62 MB (freed 0.00 MB)
2025-06-23 18:06:14,477 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:14,561 - app - INFO - File COMI contains 2025 data
2025-06-23 18:06:14,572 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-23 18:06:14,574 - app - INFO - Date range: 2022-08-09 to 2025-06-23
2025-06-23 18:06:14,578 - app.utils.memory_management - INFO - Memory before cleanup: 440.62 MB
2025-06-23 18:06:14,784 - app.utils.memory_management - INFO - Garbage collection: collected 247 objects
2025-06-23 18:06:14,785 - app.utils.memory_management - INFO - Memory after cleanup: 440.62 MB (freed 0.00 MB)
2025-06-23 18:06:16,335 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:16,483 - app - INFO - File COMI contains 2025 data
2025-06-23 18:06:16,539 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-23 18:06:16,540 - app - INFO - Date range: 2022-08-09 to 2025-06-23
2025-06-23 18:06:16,542 - app.utils.memory_management - INFO - Memory before cleanup: 441.02 MB
2025-06-23 18:06:16,733 - app.utils.memory_management - INFO - Garbage collection: collected 255 objects
2025-06-23 18:06:16,734 - app.utils.memory_management - INFO - Memory after cleanup: 441.02 MB (freed 0.00 MB)
2025-06-23 18:06:18,853 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:19,014 - app - INFO - File COMI contains 2025 data
2025-06-23 18:06:19,062 - app - INFO - Saved data to data/stocks\COMI.csv
2025-06-23 18:06:19,063 - app - INFO - Date range: 2022-08-09 to 2025-06-23
2025-06-23 18:06:19,065 - app.utils.memory_management - INFO - Memory before cleanup: 441.27 MB
2025-06-23 18:06:19,252 - app.utils.memory_management - INFO - Garbage collection: collected 258 objects
2025-06-23 18:06:19,253 - app.utils.memory_management - INFO - Memory after cleanup: 441.27 MB (freed 0.00 MB)
2025-06-23 18:06:34,430 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:34,464 - app.utils.memory_management - INFO - Memory before cleanup: 441.27 MB
2025-06-23 18:06:34,789 - app.utils.memory_management - INFO - Garbage collection: collected 204 objects
2025-06-23 18:06:34,789 - app.utils.memory_management - INFO - Memory after cleanup: 441.27 MB (freed 0.00 MB)
2025-06-23 18:06:39,339 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:39,379 - app.utils.memory_management - INFO - Memory before cleanup: 441.27 MB
2025-06-23 18:06:39,593 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-06-23 18:06:39,594 - app.utils.memory_management - INFO - Memory after cleanup: 441.27 MB (freed 0.00 MB)
2025-06-23 18:06:40,795 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:40,855 - app.utils.memory_management - INFO - Memory before cleanup: 441.27 MB
2025-06-23 18:06:41,050 - app.utils.memory_management - INFO - Garbage collection: collected 185 objects
2025-06-23 18:06:41,050 - app.utils.memory_management - INFO - Memory after cleanup: 441.27 MB (freed 0.00 MB)
2025-06-23 18:06:43,104 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:06:50,177 - app.pages.advanced_technical_analysis - INFO - Loaded 750 days of historical data for COMI
2025-06-23 18:06:50,264 - app.utils.memory_management - INFO - Memory before cleanup: 442.43 MB
2025-06-23 18:06:50,457 - app.utils.memory_management - INFO - Garbage collection: collected 186 objects
2025-06-23 18:06:50,458 - app.utils.memory_management - INFO - Memory after cleanup: 442.43 MB (freed 0.00 MB)
2025-06-23 18:09:27,423 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:09:27,564 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for ABUK over 30 days
2025-06-23 18:09:27,597 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for ABUK: 0.314
2025-06-23 18:09:27,807 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-23 18:09:27,958 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-23 18:09:27,959 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (19.37%) exceeds limit (15.00%)
2025-06-23 18:09:27,959 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-23 18:09:27,959 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (53.1)
2025-06-23 18:09:27,960 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-23 18:09:37,831 - app.utils.ai_pattern_recognition - INFO - Using live price 46.41 EGP from API for ABUK
2025-06-23 18:09:37,831 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for ABUK
2025-06-23 18:09:37,834 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for ABUK
2025-06-23 18:09:37,834 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-23 18:09:37,842 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-23 18:09:37,892 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-23 18:09:37,893 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.95, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 48.2 vs 0.0)', 'risk_level': 'Low', 'bullish_score': 48.1947551585704, 'bearish_score': 0}
2025-06-23 18:09:37,902 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=49.65, stop=47.17, target=55.86
2025-06-23 18:09:39,221 - app.utils.memory_management - INFO - Memory before cleanup: 460.39 MB
2025-06-23 18:09:39,473 - app.utils.memory_management - INFO - Garbage collection: collected 1138 objects
2025-06-23 18:09:39,475 - app.utils.memory_management - INFO - Memory after cleanup: 460.39 MB (freed 0.00 MB)
2025-06-23 18:09:50,298 - app - INFO - Using TensorFlow-based LSTM model
2025-06-23 18:09:50,327 - app - INFO - Found 14 stock files in data/stocks
2025-06-23 18:09:50,391 - app.utils.ai_sentiment_analyzer - INFO - Analyzing sentiment for COMI over 30 days
2025-06-23 18:09:50,425 - app.utils.ai_sentiment_analyzer - INFO - Sentiment analysis complete for COMI: -0.063
2025-06-23 18:09:50,555 - app.utils.ai_ensemble_optimizer - INFO - Ensemble configuration loaded successfully
2025-06-23 18:09:50,686 - app.utils.ai_risk_manager - INFO - Starting comprehensive portfolio risk assessment
2025-06-23 18:09:50,688 - app.utils.ai_risk_manager - WARNING - RISK ALERT [CRITICAL]: Maximum drawdown (36.61%) exceeds limit (15.00%)
2025-06-23 18:09:50,688 - app.utils.ai_risk_manager - WARNING - RISK ALERT [MEDIUM]: High concentration risk detected (100.00%)
2025-06-23 18:09:50,688 - app.utils.ai_risk_manager - INFO - Risk assessment completed. Overall risk: HIGH (59.9)
2025-06-23 18:09:50,689 - app.utils.ai_pattern_recognition - INFO - Starting comprehensive pattern analysis
2025-06-23 18:09:56,099 - app.utils.ai_pattern_recognition - INFO - Using live price 79.12 EGP from API for COMI
2025-06-23 18:09:56,101 - app.utils.ai_pattern_recognition - INFO - Using live pivot data from API for COMI
2025-06-23 18:09:56,101 - app.utils.ai_pattern_recognition - INFO - No valid pivot data found, falling back to calculation for COMI
2025-06-23 18:09:56,101 - app.utils.ai_pattern_recognition - ERROR - Error detecting support/resistance: local variable 'recent_low' referenced before assignment
2025-06-23 18:09:56,103 - app.utils.ai_pattern_recognition - INFO - Pattern analysis completed successfully
2025-06-23 18:09:56,158 - app.pages.advanced_ai_features - INFO - Trading Scenario Debug: action='STRONG BUY', volatility_factor=0.050
2025-06-23 18:09:56,160 - app.pages.advanced_ai_features - INFO - Full recommendation object: {'action': 'STRONG BUY', 'confidence': 0.5, 'reasoning': 'AI consensus shows bullish signals across multiple models (Score: 38.5 vs 17.5)', 'risk_level': 'Low', 'bullish_score': 38.5, 'bearish_score': 17.5}
2025-06-23 18:09:56,161 - app.pages.advanced_ai_features - INFO - Using BUY logic: entry=78.72, stop=74.79, target=88.56
2025-06-23 18:09:56,300 - app.utils.memory_management - INFO - Memory before cleanup: 465.77 MB
2025-06-23 18:09:56,498 - app.utils.memory_management - INFO - Garbage collection: collected 1708 objects
2025-06-23 18:09:56,501 - app.utils.memory_management - INFO - Memory after cleanup: 465.77 MB (freed 0.00 MB)
2025-06-23 20:40:03,026 - app - INFO - Cleaning up resources...
2025-06-23 20:40:03,028 - app.utils.memory_management - INFO - Memory before cleanup: 452.59 MB
2025-06-23 20:40:03,210 - app.utils.memory_management - INFO - Garbage collection: collected 337 objects
2025-06-23 20:40:03,210 - app.utils.memory_management - INFO - Memory after cleanup: 452.60 MB (freed -0.00 MB)
2025-06-23 20:40:03,210 - app - INFO - Application shutdown complete
