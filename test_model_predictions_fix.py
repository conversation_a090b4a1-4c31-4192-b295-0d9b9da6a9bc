#!/usr/bin/env python3
"""
Test script to verify the RF, GB, LR model prediction fix
Tests that these models now generate realistic predictions instead of 0.00% changes
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def create_test_data():
    """Create test stock data"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)  # For reproducible results
    
    # Generate realistic stock price data
    initial_price = 89.98
    returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    test_data = pd.DataFrame({
        'Date': dates,
        'Open': [p * 0.999 for p in prices],
        'High': [p * 1.002 for p in prices],
        'Low': [p * 0.998 for p in prices],
        'Close': prices,
        'Volume': np.random.randint(50000, 200000, len(dates))
    })
    
    return test_data

def test_all_model_predictions():
    """Test all available models to see if they generate realistic predictions"""
    print("\n🧪 Testing All Model Predictions After Fix")
    print("=" * 60)
    
    try:
        from models.predict import predict_future_prices
        
        # Create test data
        test_data = create_test_data()
        current_price = test_data['Close'].iloc[-1]
        print(f"✅ Created test data with {len(test_data)} data points")
        print(f"Current price: {current_price:.2f} EGP")
        
        # Test all available models
        models_to_test = ['rf', 'gb', 'lr', 'lstm', 'xgb', 'svr', 'prophet', 'hybrid', 'ensemble']
        horizons = [30]  # 30 minutes
        symbol = 'COMI'
        
        results = {}
        
        print("\nTesting all models:")
        print("-" * 60)
        
        for model_type in models_to_test:
            try:
                print(f"\n🔄 Testing {model_type.upper()} model...")
                
                predictions = predict_future_prices(
                    test_data, 
                    symbol, 
                    horizons=horizons,
                    model_type=model_type,
                    models_path='saved_models'
                )
                
                if horizons[0] in predictions and predictions[horizons[0]] is not None:
                    predicted_price = predictions[horizons[0]]
                    price_change = predicted_price - current_price
                    price_change_pct = (price_change / current_price) * 100
                    
                    results[model_type] = {
                        'predicted_price': predicted_price,
                        'price_change_pct': price_change_pct,
                        'is_realistic': abs(price_change_pct) >= 0.1,  # At least 0.1% change
                        'is_mock': abs(price_change_pct) < 0.01  # Less than 0.01% change
                    }
                    
                    # Status indicators
                    if results[model_type]['is_mock']:
                        status = "❌ MOCK"
                        color = "🔴"
                    elif results[model_type]['is_realistic']:
                        status = "✅ REALISTIC"
                        color = "🟢"
                    else:
                        status = "⚠️ MINIMAL"
                        color = "🟡"
                    
                    print(f"   {color} {model_type.upper()}: {current_price:.2f} -> {predicted_price:.2f} EGP ({price_change_pct:+.2f}%) - {status}")
                    
                else:
                    print(f"   ❌ {model_type.upper()}: No prediction returned")
                    results[model_type] = {
                        'predicted_price': None, 
                        'price_change_pct': 0, 
                        'is_realistic': False,
                        'is_mock': True
                    }
                    
            except Exception as e:
                print(f"   ❌ {model_type.upper()}: Error - {str(e)}")
                results[model_type] = {
                    'predicted_price': None, 
                    'price_change_pct': 0, 
                    'is_realistic': False,
                    'is_mock': True
                }
        
        return results
        
    except Exception as e:
        print(f"❌ Error testing models: {str(e)}")
        return {}

def analyze_fix_effectiveness(results):
    """Analyze if the fix was effective"""
    print("\n📊 Analyzing Fix Effectiveness")
    print("=" * 60)
    
    if not results:
        print("❌ No results to analyze")
        return False
    
    # Categorize results
    mock_models = []
    minimal_models = []
    realistic_models = []
    failed_models = []
    
    for model, data in results.items():
        if data['predicted_price'] is None:
            failed_models.append(model)
        elif data['is_mock']:
            mock_models.append(model)
        elif data['is_realistic']:
            realistic_models.append(model)
        else:
            minimal_models.append(model)
    
    # Print categorization
    print(f"🔴 MOCK models (0.00-0.01% change): {mock_models}")
    print(f"🟡 MINIMAL models (0.01-0.1% change): {minimal_models}")
    print(f"🟢 REALISTIC models (>0.1% change): {realistic_models}")
    print(f"❌ FAILED models: {failed_models}")
    
    # Calculate success metrics
    total_models = len(results)
    realistic_count = len(realistic_models)
    mock_count = len(mock_models)
    
    success_rate = realistic_count / total_models * 100 if total_models > 0 else 0
    mock_rate = mock_count / total_models * 100 if total_models > 0 else 0
    
    print(f"\n📈 Success Metrics:")
    print(f"   Total models tested: {total_models}")
    print(f"   Realistic predictions: {realistic_count} ({success_rate:.1f}%)")
    print(f"   Mock predictions: {mock_count} ({mock_rate:.1f}%)")
    
    # Specific analysis for previously problematic models
    problematic_models = ['rf', 'gb', 'lr']
    fixed_models = []
    still_broken_models = []
    
    for model in problematic_models:
        if model in results:
            if results[model]['is_realistic']:
                fixed_models.append(model)
            elif results[model]['is_mock']:
                still_broken_models.append(model)
    
    print(f"\n🔧 Fix Analysis for RF, GB, LR:")
    print(f"   ✅ FIXED models: {fixed_models}")
    print(f"   ❌ STILL BROKEN: {still_broken_models}")
    
    # Overall assessment
    if len(fixed_models) >= 2 and len(still_broken_models) <= 1:
        print(f"\n🎉 FIX SUCCESS: Most problematic models are now working!")
        return True
    elif len(fixed_models) >= 1:
        print(f"\n⚠️ PARTIAL SUCCESS: Some models fixed, but more work needed")
        return True
    else:
        print(f"\n❌ FIX FAILED: Problematic models still not working")
        return False

def test_ensemble_improvement(results):
    """Test if the ensemble is now improved"""
    print("\n🔄 Testing ENSEMBLE Improvement")
    print("=" * 60)
    
    if 'ensemble' not in results:
        print("❌ No ensemble results to analyze")
        return False
    
    ensemble_data = results['ensemble']
    
    # Count how many individual models are now realistic
    individual_models = ['rf', 'gb', 'lr', 'lstm', 'xgb', 'svr', 'prophet', 'hybrid']
    realistic_individual = [model for model in individual_models 
                           if model in results and results[model]['is_realistic']]
    mock_individual = [model for model in individual_models 
                      if model in results and results[model]['is_mock']]
    
    print(f"Individual models status:")
    print(f"   🟢 Realistic: {realistic_individual}")
    print(f"   🔴 Mock: {mock_individual}")
    
    # Analyze ensemble quality
    realistic_ratio = len(realistic_individual) / len(individual_models) if individual_models else 0
    
    print(f"\nENSEMBLE Analysis:")
    print(f"   Prediction: {ensemble_data['price_change_pct']:+.2f}%")
    print(f"   Status: {'🟢 REALISTIC' if ensemble_data['is_realistic'] else '🔴 MOCK' if ensemble_data['is_mock'] else '🟡 MINIMAL'}")
    print(f"   Individual models realistic ratio: {realistic_ratio:.1%}")
    
    if realistic_ratio >= 0.6 and ensemble_data['is_realistic']:
        print(f"   🎉 ENSEMBLE QUALITY: EXCELLENT")
        return True
    elif realistic_ratio >= 0.4 and not ensemble_data['is_mock']:
        print(f"   ✅ ENSEMBLE QUALITY: GOOD")
        return True
    else:
        print(f"   ⚠️ ENSEMBLE QUALITY: NEEDS IMPROVEMENT")
        return False

def main():
    """Run all tests for the model prediction fix"""
    print("🚀 Testing Model Predictions Fix")
    print("=" * 70)
    
    # Test all models
    results = test_all_model_predictions()
    
    if not results:
        print("❌ No test results available")
        return False
    
    # Analyze fix effectiveness
    fix_success = analyze_fix_effectiveness(results)
    
    # Test ensemble improvement
    ensemble_success = test_ensemble_improvement(results)
    
    # Final summary
    print("\n" + "=" * 70)
    print("📊 FINAL ASSESSMENT")
    print("=" * 70)
    
    if fix_success and ensemble_success:
        print("🎉 COMPLETE SUCCESS!")
        print("   ✅ RF, GB, LR models are now generating realistic predictions")
        print("   ✅ ENSEMBLE is now working properly with real data")
        print("   ✅ All models available in dashboard (9 total)")
        return True
    elif fix_success:
        print("⚠️ PARTIAL SUCCESS!")
        print("   ✅ Individual models are mostly fixed")
        print("   ⚠️ ENSEMBLE still needs some improvement")
        return True
    else:
        print("❌ FIX INCOMPLETE")
        print("   ❌ Some models still returning mock data")
        print("   ❌ ENSEMBLE still compromised")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
