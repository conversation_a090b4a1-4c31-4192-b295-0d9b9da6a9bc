{"1": [{"symbol": "TEST", "model_type": "rf", "horizon": 1, "prediction_time": "2025-05-11T20:45:02.839449", "predicted_value": 1000000, "actual_value": 1e-05, "absolute_error": 999999.99999, "percentage_error": 9999999999900.0, "direction_correct": true}, {"symbol": "TEST", "model_type": "rf", "horizon": 1, "prediction_time": "2025-05-11T20:45:02.840046", "predicted_value": -100, "actual_value": -90, "absolute_error": 10, "percentage_error": -11.11111111111111, "direction_correct": true}, {"symbol": "TEST", "model_type": "rf", "horizon": 1, "prediction_time": "2025-05-11T20:45:02.848532", "predicted_value": 0, "actual_value": 0, "absolute_error": 0, "percentage_error": Infinity, "direction_correct": false}, {"symbol": "TEST", "model_type": "rf", "horizon": 1, "prediction_time": "2025-05-11T20:45:55.457054", "predicted_value": 1000000, "actual_value": 1e-05, "absolute_error": 999999.99999, "percentage_error": 9999999999900.0, "direction_correct": true}, {"symbol": "TEST", "model_type": "rf", "horizon": 1, "prediction_time": "2025-05-11T20:45:55.457732", "predicted_value": -100, "actual_value": -90, "absolute_error": 10, "percentage_error": -11.11111111111111, "direction_correct": true}, {"symbol": "TEST", "model_type": "rf", "horizon": 1, "prediction_time": "2025-05-11T20:45:55.466577", "predicted_value": 0, "actual_value": 0, "absolute_error": 0, "percentage_error": Infinity, "direction_correct": false}]}