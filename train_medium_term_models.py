#!/usr/bin/env python3
"""
Training script for missing medium-term hour models (2h and 4h)
Specifically designed for EGX trading hours (4.5 hours/day)

This script trains:
- 120min (2 hours) models - Half trading day
- 240min (4 hours) models - Almost full trading day

For all 9 model types across all 14 EGX stocks.
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime
from typing import List, Dict

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/medium_term_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_available_stocks() -> List[str]:
    """Get list of available stock CSV files"""
    stock_dir = "data/stocks"
    stocks = []
    
    if os.path.exists(stock_dir):
        for file in os.listdir(stock_dir):
            if file.endswith('.csv') and not file.startswith('.'):
                stock_symbol = file.replace('.csv', '')
                stocks.append(stock_symbol)
    
    logger.info(f"Found {len(stocks)} stock files: {stocks}")
    return sorted(stocks)

def check_existing_models(symbol: str, horizon: int) -> Dict[str, bool]:
    """Check which models already exist for a given symbol and horizon"""
    model_types = ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']
    existing_models = {}
    
    for model_type in model_types:
        # Check for different file extensions
        model_patterns = [
            f"saved_models/{symbol}_{model_type}_{horizon}min.joblib",
            f"saved_models/{symbol}_{model_type}_{horizon}min.pkl",
            f"saved_models/{symbol}_{model_type}_{horizon}min.keras",
            f"saved_models/{symbol}_{model_type}_{horizon}min.h5"
        ]
        
        exists = any(os.path.exists(pattern) for pattern in model_patterns)
        existing_models[model_type] = exists
    
    return existing_models

def validate_stock_data(csv_path: str) -> bool:
    """Validate that stock data is suitable for training"""
    try:
        df = pd.read_csv(csv_path)
        
        # Check required columns
        required_cols = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            logger.error(f"Missing required columns in {csv_path}: {missing_cols}")
            return False
        
        # Check data length (need at least 200 rows for meaningful training)
        if len(df) < 200:
            logger.warning(f"Insufficient data in {csv_path}: {len(df)} rows (need at least 200)")
            return False
        
        # Check for excessive NaN values
        nan_percentage = df[required_cols].isnull().sum().sum() / (len(df) * len(required_cols)) * 100
        if nan_percentage > 10:
            logger.warning(f"High NaN percentage in {csv_path}: {nan_percentage:.1f}%")
            return False
        
        logger.info(f"Data validation passed for {csv_path}: {len(df)} rows, {nan_percentage:.1f}% NaN")
        return True
        
    except Exception as e:
        logger.error(f"Error validating {csv_path}: {str(e)}")
        return False

def train_single_model(symbol: str, horizon: int, model_type: str) -> bool:
    """Train a single model for given symbol, horizon, and model type"""
    try:
        from models.train import train_from_csv
        
        csv_path = f"data/stocks/{symbol}.csv"
        
        logger.info(f"Training {model_type.upper()} model for {symbol} - {horizon}min horizon")
        
        # Train the model
        trained_models = train_from_csv(
            csv_path=csv_path,
            symbol=symbol,
            horizons=[horizon],  # Single horizon
            sequence_length=60,
            model_type=model_type,
            epochs=50,  # Reasonable for medium-term
            batch_size=32,
            save_path='saved_models'
        )
        
        if trained_models and horizon in trained_models:
            logger.info(f"✅ Successfully trained {model_type.upper()} for {symbol} - {horizon}min")
            return True
        else:
            logger.error(f"❌ Failed to train {model_type.upper()} for {symbol} - {horizon}min")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error training {model_type.upper()} for {symbol} - {horizon}min: {str(e)}")
        return False

def train_medium_term_models(
    stocks: List[str] = None,
    horizons: List[int] = [120, 240],  # 2h, 4h
    model_types: List[str] = None,
    skip_existing: bool = True,
    max_failures: int = 5
) -> Dict[str, Dict[str, Dict[str, bool]]]:
    """
    Train medium-term models for EGX stocks
    
    Args:
        stocks: List of stock symbols (None = all available)
        horizons: List of horizons in minutes [120, 240]
        model_types: List of model types (None = all available)
        skip_existing: Skip already trained models
        max_failures: Maximum consecutive failures before stopping
        
    Returns:
        Dictionary with training results
    """
    
    # Default parameters
    if stocks is None:
        stocks = get_available_stocks()
    
    if model_types is None:
        model_types = ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']
    
    # Validate inputs
    if not stocks:
        logger.error("No stocks available for training")
        return {}
    
    if not horizons:
        logger.error("No horizons specified for training")
        return {}
    
    # Training summary
    total_models = len(stocks) * len(horizons) * len(model_types)
    logger.info(f"🚀 Starting medium-term model training")
    logger.info(f"📊 Training Summary:")
    logger.info(f"   • Stocks: {len(stocks)} ({', '.join(stocks)})")
    logger.info(f"   • Horizons: {len(horizons)} ({', '.join([f'{h}min' for h in horizons])})")
    logger.info(f"   • Models: {len(model_types)} ({', '.join(model_types)})")
    logger.info(f"   • Total models to train: {total_models}")
    
    # Results tracking
    results = {}
    completed = 0
    skipped = 0
    failed = 0
    consecutive_failures = 0
    
    # Training loop
    for stock in stocks:
        results[stock] = {}
        
        # Validate stock data first
        csv_path = f"data/stocks/{stock}.csv"
        if not os.path.exists(csv_path):
            logger.error(f"❌ Stock data not found: {csv_path}")
            continue
            
        if not validate_stock_data(csv_path):
            logger.error(f"❌ Stock data validation failed: {stock}")
            continue
        
        for horizon in horizons:
            results[stock][horizon] = {}
            
            # Check existing models
            existing_models = check_existing_models(stock, horizon)
            
            for model_type in model_types:
                # Skip if model already exists
                if skip_existing and existing_models.get(model_type, False):
                    logger.info(f"⏭️ Skipping {model_type.upper()} for {stock} - {horizon}min (already exists)")
                    results[stock][horizon][model_type] = True
                    skipped += 1
                    continue
                
                # Train the model
                success = train_single_model(stock, horizon, model_type)
                results[stock][horizon][model_type] = success
                
                if success:
                    completed += 1
                    consecutive_failures = 0
                    logger.info(f"✅ Progress: {completed}/{total_models - skipped} completed")
                else:
                    failed += 1
                    consecutive_failures += 1
                    logger.error(f"❌ Progress: {failed} failed, {consecutive_failures} consecutive failures")
                    
                    # Stop if too many consecutive failures
                    if consecutive_failures >= max_failures:
                        logger.error(f"🛑 Stopping due to {consecutive_failures} consecutive failures")
                        return results
    
    # Final summary
    logger.info(f"🎉 Medium-term model training completed!")
    logger.info(f"📊 Final Results:")
    logger.info(f"   • ✅ Completed: {completed}")
    logger.info(f"   • ⏭️ Skipped: {skipped}")
    logger.info(f"   • ❌ Failed: {failed}")
    logger.info(f"   • 📈 Success Rate: {(completed/(completed+failed)*100):.1f}%" if (completed+failed) > 0 else "N/A")
    
    return results

def print_training_status(results: Dict[str, Dict[str, Dict[str, bool]]]):
    """Print detailed training status"""
    print("\n" + "="*80)
    print("📊 MEDIUM-TERM MODEL TRAINING STATUS")
    print("="*80)
    
    for stock in sorted(results.keys()):
        print(f"\n📈 {stock}:")
        for horizon in sorted(results[stock].keys()):
            horizon_name = f"{horizon//60}h" if horizon >= 60 else f"{horizon}min"
            print(f"   {horizon_name} ({horizon}min):")
            
            for model_type in sorted(results[stock][horizon].keys()):
                status = "✅" if results[stock][horizon][model_type] else "❌"
                print(f"      {status} {model_type.upper()}")

def main():
    """Main training function"""
    print("🚀 EGX Medium-Term Model Training")
    print("=" * 50)
    print("Training 2h and 4h models for EGX trading hours")
    print("=" * 50)
    
    # Create necessary directories
    os.makedirs('saved_models', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # Start training
    start_time = datetime.now()
    
    try:
        results = train_medium_term_models(
            stocks=None,  # All available stocks
            horizons=[120, 240],  # 2h, 4h
            model_types=None,  # All available models
            skip_existing=True,  # Skip existing models
            max_failures=5  # Stop after 5 consecutive failures
        )
        
        # Print detailed results
        print_training_status(results)
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
    except Exception as e:
        logger.error(f"Training failed with error: {str(e)}")
    finally:
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"⏱️ Total training time: {duration}")

if __name__ == "__main__":
    main()
