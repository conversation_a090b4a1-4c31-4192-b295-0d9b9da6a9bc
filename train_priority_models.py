#!/usr/bin/env python3
"""
Priority training script for medium-term models
Trains the most important models first to get quick results

Priority order:
1. High-priority stocks: COMI, EGX30, HRHO (most traded)
2. Fast models first: RF, LR, GB (quick training)
3. Both horizons: 120min (2h), 240min (4h)
4. Then slower models: LSTM, SVR, etc.
"""

import os
import sys
import logging
from datetime import datetime

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/priority_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def train_priority_batch():
    """Train priority models first for immediate testing"""
    
    print("🚀 PRIORITY MEDIUM-TERM MODEL TRAINING")
    print("=" * 50)
    print("Training most important models first for quick results")
    print("=" * 50)
    
    # Import training function
    from train_medium_term_models import train_medium_term_models
    
    # Priority configuration
    priority_stocks = ['COMI', 'EGX30', 'HRHO']  # Most traded EGX stocks
    fast_models = ['rf', 'lr', 'gb']  # Quick to train
    horizons = [120, 240]  # 2h, 4h
    
    print(f"🎯 Priority Training Configuration:")
    print(f"   • Stocks: {priority_stocks}")
    print(f"   • Models: {fast_models}")
    print(f"   • Horizons: {horizons} minutes")
    print(f"   • Total models: {len(priority_stocks) * len(fast_models) * len(horizons)} models")
    print(f"   • Estimated time: ~30 minutes")
    
    start_time = datetime.now()
    
    try:
        # Train priority models
        results = train_medium_term_models(
            stocks=priority_stocks,
            horizons=horizons,
            model_types=fast_models,
            skip_existing=True,
            max_failures=3
        )
        
        # Check results
        total_trained = 0
        total_failed = 0
        
        for stock in results:
            for horizon in results[stock]:
                for model in results[stock][horizon]:
                    if results[stock][horizon][model]:
                        total_trained += 1
                    else:
                        total_failed += 1
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n🎉 PRIORITY TRAINING COMPLETED!")
        print(f"   ✅ Successfully trained: {total_trained} models")
        print(f"   ❌ Failed: {total_failed} models")
        print(f"   ⏱️ Duration: {duration}")
        
        if total_trained > 0:
            print(f"\n✅ You can now test 2h and 4h predictions!")
            print(f"   • Go to Predictions (New) → Medium-term (hours)")
            print(f"   • Select {priority_stocks[0]} stock")
            print(f"   • Choose 2 hours or 4 hours horizon")
            print(f"   • Use RF, LR, or GB models")
        
        return total_trained > 0
        
    except Exception as e:
        logger.error(f"Priority training failed: {str(e)}")
        return False

def train_remaining_models():
    """Train remaining models after priority batch"""
    
    print("\n🔄 TRAINING REMAINING MODELS")
    print("=" * 40)
    
    from train_medium_term_models import train_medium_term_models
    
    # All stocks and models
    all_stocks = None  # Will use all available stocks
    remaining_models = ['ensemble', 'lstm', 'xgb', 'svr', 'prophet', 'hybrid']
    horizons = [120, 240]
    
    print(f"🎯 Remaining Models Configuration:")
    print(f"   • Stocks: All available (14 stocks)")
    print(f"   • Models: {remaining_models}")
    print(f"   • Horizons: {horizons} minutes")
    print(f"   • Note: This will take several hours")
    
    try:
        results = train_medium_term_models(
            stocks=all_stocks,
            horizons=horizons,
            model_types=remaining_models,
            skip_existing=True,
            max_failures=10
        )
        
        print(f"✅ Remaining models training completed!")
        return True
        
    except Exception as e:
        logger.error(f"Remaining models training failed: {str(e)}")
        return False

def main():
    """Main training orchestrator"""
    
    # Create necessary directories
    os.makedirs('saved_models', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    print("🎯 SMART MEDIUM-TERM TRAINING STRATEGY")
    print("=" * 60)
    print("Phase 1: Priority models (30 minutes) - for immediate testing")
    print("Phase 2: Remaining models (several hours) - for full coverage")
    print("=" * 60)
    
    # Phase 1: Priority training
    print("\n🚀 PHASE 1: PRIORITY MODELS")
    priority_success = train_priority_batch()
    
    if priority_success:
        print(f"\n✅ Phase 1 completed successfully!")
        print(f"🎉 You can now test medium-term predictions!")
        
        # Ask user if they want to continue with Phase 2
        print(f"\n🤔 PHASE 2 OPTIONS:")
        print(f"   A. Continue with full training (several hours)")
        print(f"   B. Stop here and test priority models first")
        print(f"   C. Run full training in background")
        
        choice = input("\nEnter your choice (A/B/C): ").upper().strip()
        
        if choice == 'A':
            print(f"\n🚀 PHASE 2: FULL TRAINING")
            train_remaining_models()
        elif choice == 'B':
            print(f"\n⏸️ Stopping after priority training")
            print(f"💡 Run 'python train_medium_term_models.py' later for full coverage")
        elif choice == 'C':
            print(f"\n🔄 Starting background training...")
            print(f"💡 Monitor progress in logs/medium_term_training.log")
            train_remaining_models()
        else:
            print(f"\n⏸️ Invalid choice. Stopping after priority training.")
    else:
        print(f"\n❌ Phase 1 failed. Check logs for details.")
        return False
    
    print(f"\n🎉 Training session completed!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
