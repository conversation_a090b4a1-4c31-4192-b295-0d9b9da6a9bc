[ai_services]
default_provider = anthropic
enabled = true
use_in_ensemble = true
replace_traditional_models = false
ai_prediction_weight = 0.3

[openai]
api_key = ********************************************************************************************************************************************************************
model = gpt-4
temperature = 0.3

[azure]
api_key = KTQF4X6W42AVXGDS
endpoint = KdEha4PFcS6Igw4zn4eCMh4wWje9D6UaLUnqUHjf9HuE/+1TnsNu6Bu78Puo07W8
model_deployment = ""

[anthropic]
api_key = AIzaSyBoltA2fUpvyNfvrUe6JF6JqREHO4WqE7w
model = claude-3-haiku-20240307

[custom]
endpoint = ""
api_key = ""

[langflow]
api_endpoint = ""
api_key = ""
workflow_id = ""

[n8n]
webhook_url = ""
api_key = ""

[advanced]
retry_on_failure = true
timeout_seconds = 30
cache_predictions = true
cache_duration_minutes = 60

[mock]
description = Mock AI provider (no API key required)

