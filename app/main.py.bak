import streamlit as st
import pandas as pd
import os
import sys
import logging

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Now we can import our modules
from app.components.upload import upload_csv_component, save_uploaded_data
from app.components.live_data import live_data_component
from app.components.prediction import prediction_component
from models.train import train_from_csv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """
    Main Streamlit application
    """
    # Set page config
    st.set_page_config(
        page_title="AI Stocks Bot for EGX",
        page_icon="📈",
        layout="wide"
    )

    # Title
    st.title("AI Stocks Bot for EGX")
    st.markdown("An AI-powered application to predict future stock prices on the Egyptian Stock Exchange (EGX)")

    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.radio("Go to", ["Home", "Upload Data", "Live Data", "Predictions", "Train Model"])

    # Initialize session state
    if 'historical_data' not in st.session_state:
        st.session_state.historical_data = None

    if 'symbol' not in st.session_state:
        st.session_state.symbol = None

    if 'live_data' not in st.session_state:
        st.session_state.live_data = None

    # Home page
    if page == "Home":
        st.header("Welcome to AI Stocks Bot")
        st.markdown("""
        This application uses AI to predict future stock prices on the Egyptian Stock Exchange (EGX).

        ### Features:

        1. **Historical CSV Upload**
           - Upload .csv files containing historical stock price data (OHLC)
           - Feature engineering includes SMA, EMA, RSI, hour of day, and day of the week

        2. **Live Price Integration**
           - Scrapes near-live prices from TradingView or Mubasher
           - Scraped price is automatically appended and transformed to match the feature format of static data

        3. **Advanced AI Model**
           - Uses LSTM neural network for time series forecasting
           - Trains on static data and predicts based on the latest (live) input
           - Includes one model per time interval: 4, 25, 30, and 69 minutes

        ### How to use:

        1. Go to the **Upload Data** page to upload historical stock data
        2. Go to the **Live Data** page to fetch live prices
        3. Go to the **Train Model** page to train the AI model
        4. Go to the **Predictions** page to generate price predictions
        """)

    # Upload Data page
    elif page == "Upload Data":
        st.header("Upload Historical Data")

        # Upload CSV component
        historical_data, symbol = upload_csv_component()

        if historical_data is not None and symbol is not None:
            # Save data to session state
            st.session_state.historical_data = historical_data
            st.session_state.symbol = symbol

            # Save data to disk
            file_path = save_uploaded_data(historical_data, symbol)

            if file_path:
                st.success(f"Data saved to {file_path}")

    # Live Data page
    elif page == "Live Data":
        st.header("Live Price Data")

        if st.session_state.symbol is None:
            st.warning("Please upload historical data and provide a stock symbol first")
        else:
            # Live data component
            live_data = live_data_component(st.session_state.symbol)

            if live_data is not None:
                st.session_state.live_data = live_data

    # Predictions page
    elif page == "Predictions":
        st.header("Price Predictions")

        if st.session_state.historical_data is None:
            st.warning("Please upload historical data first")
        else:
            # Prediction component
            prediction_component(
                st.session_state.historical_data,
                st.session_state.live_data,
                st.session_state.symbol
            )

    # Train Model page
    elif page == "Train Model":
        st.header("Train AI Model")

        if st.session_state.historical_data is None or st.session_state.symbol is None:
            st.warning("Please upload historical data and provide a stock symbol first")
        else:
            # Model type selection
            model_type = st.selectbox(
                "Select model type",
                options=["LSTM", "BiLSTM"],
                index=0
            )

            # Prediction horizons
            horizons = [5, 15, 30, 60]
            selected_horizons = st.multiselect(
                "Select prediction horizons (minutes)",
                options=horizons,
                default=horizons
            )

            # Training parameters
            epochs = st.slider("Number of epochs", min_value=10, max_value=200, value=50, step=10)
            batch_size = st.slider("Batch size", min_value=16, max_value=128, value=32, step=16)

            # Train model
            if st.button("Train Model"):
                try:
                    with st.spinner("Training model..."):
                        # Save data to disk
                        file_path = save_uploaded_data(st.session_state.historical_data, st.session_state.symbol)

                        if file_path:
                            # Train model
                            train_from_csv(
                                file_path,
                                st.session_state.symbol,
                                horizons=selected_horizons,
                                model_type=model_type.lower(),
                                epochs=epochs,
                                batch_size=batch_size
                            )

                            st.success("Model trained successfully")
                        else:
                            st.error("Failed to save data")

                except Exception as e:
                    st.error(f"Error training model: {str(e)}")
                    logger.error(f"Error training model: {str(e)}")

if __name__ == "__main__":
    main()
