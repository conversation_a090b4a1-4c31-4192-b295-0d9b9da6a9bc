"""
Configuration module for the AI Stocks Bot application.
Centralizes all configuration parameters to make the application more maintainable.
"""
import os

# Paths
DATA_DIR = 'data'
STOCK_DATA_DIR = os.path.join(DATA_DIR, 'stocks')
MODELS_DIR = 'saved_models'
LOGS_DIR = 'logs'

# Ensure directories exist
for directory in [DATA_DIR, STOCK_DATA_DIR, MODELS_DIR, LOGS_DIR]:
    os.makedirs(directory, exist_ok=True)

# Model parameters
DEFAULT_SEQUENCE_LENGTH = 60
DEFAULT_PREDICTION_HORIZONS = {
    'minutes': [5, 15, 30, 60],
    'days': [1, 2, 3, 5, 7],
    'weeks': [1, 2, 4, 8, 12]
}

# Training parameters
DEFAULT_EPOCHS = 50
DEFAULT_BATCH_SIZE = 32
DEFAULT_VALIDATION_SPLIT = 0.2

# Web scraping parameters
SCRAPING_TIMEOUT = 30  # seconds
SCRAPING_RETRY_ATTEMPTS = 3
PRICE_CACHE_EXPIRY = 300  # seconds (5 minutes)

# UI parameters
THEME_COLOR = "#1E88E5"  # Primary blue color
SECONDARY_COLOR = "#FFC107"  # Accent color
DANGER_COLOR = "#F44336"  # Error/warning color
SUCCESS_COLOR = "#4CAF50"  # Success color

# Feature engineering
CORE_FEATURES = ['Open', 'High', 'Low', 'Close', 'Volume', 'SMA20', 'RSI']
TECHNICAL_INDICATORS = [
    'SMA5', 'SMA20', 'SMA50',
    'EMA5', 'EMA20', 'EMA50',
    'RSI', 'MACD', 'MACD_Signal', 'MACD_Diff',
    'BB_Mid', 'BB_High', 'BB_Low', 'BB_Width',
    'ATR'
]

# Model types
AVAILABLE_MODELS = {
    'sklearn': ['rf', 'gb', 'lr', 'svr', 'prophet', 'hybrid', 'ensemble'],
    'tensorflow': ['lstm', 'bilstm']
}

# Model display names
MODEL_DISPLAY_NAMES = {
    'rf': 'Random Forest',
    'gb': 'Gradient Boosting',
    'lr': 'Linear Regression',
    'svr': 'Support Vector Regression',
    'xgb': 'XGBoost',
    'lstm': 'LSTM Neural Network',
    'bilstm': 'Bidirectional LSTM',
    'prophet': 'Facebook Prophet',
    'hybrid': 'ARIMA-ML Hybrid',
    'ensemble': 'Ensemble Model'
}

# Prediction settings
MAX_PRICE_CHANGE_PERCENT = 5.0  # Maximum allowed price change as percentage
