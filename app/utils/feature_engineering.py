import pandas as pd
import numpy as np

def add_technical_indicators(df, selected_indicators=None):
    """
    Add technical indicators to the DataFrame

    Args:
        df (pd.DataFrame): DataFrame containing OHLCV data
        selected_indicators (list, optional): List of indicators to add. If None, adds all indicators.

    Returns:
        pd.DataFrame: DataFrame with added technical indicators
    """
    # Make a copy to avoid modifying the original DataFrame
    df_copy = df.copy()

    # Standardize column names first - ensure uppercase for compatibility
    column_mapping = {
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    }

    for old_name, new_name in column_mapping.items():
        if old_name in df_copy.columns and new_name not in df_copy.columns:
            df_copy[new_name] = df_copy[old_name]

    # If no indicators are selected, add all indicators
    if selected_indicators is None:
        selected_indicators = [
            "SMA5", "SMA20", "SMA50",
            "EMA5", "EMA20", "EMA50",
            "RSI", "MACD", "MACD_Signal", "MACD_Diff",
            "BB_Mid", "BB_High", "BB_Low", "BB_Width",
            "ATR"
        ]

    # Simple Moving Averages (SMA)
    if any(indicator in selected_indicators for indicator in ["SMA5", "SMA20", "SMA50"]):
        if 'Close' in df_copy.columns:
            if "SMA5" in selected_indicators:
                df_copy['SMA5'] = df_copy['Close'].rolling(window=5).mean()
            if "SMA20" in selected_indicators:
                df_copy['SMA20'] = df_copy['Close'].rolling(window=20).mean()
            if "SMA50" in selected_indicators:
                df_copy['SMA50'] = df_copy['Close'].rolling(window=50).mean()
        else:
            # If Close column doesn't exist, set SMAs to 0
            if "SMA5" in selected_indicators:
                df_copy['SMA5'] = 0
            if "SMA20" in selected_indicators:
                df_copy['SMA20'] = 0
            if "SMA50" in selected_indicators:
                df_copy['SMA50'] = 0

    # Exponential Moving Averages (EMA)
    if any(indicator in selected_indicators for indicator in ["EMA5", "EMA20", "EMA50"]):
        if "EMA5" in selected_indicators:
            df_copy['EMA5'] = df_copy['Close'].ewm(span=5, adjust=False).mean()
        if "EMA20" in selected_indicators:
            df_copy['EMA20'] = df_copy['Close'].ewm(span=20, adjust=False).mean()
        if "EMA50" in selected_indicators:
            df_copy['EMA50'] = df_copy['Close'].ewm(span=50, adjust=False).mean()

    # Relative Strength Index (RSI)
    if "RSI" in selected_indicators:
        # Calculate price changes
        delta = df_copy['Close'].diff()

        # Create gain (positive) and loss (negative) series
        gain = delta.copy()
        loss = delta.copy()
        gain[gain < 0] = 0
        loss[loss > 0] = 0
        loss = abs(loss)

        # Calculate average gain and loss over 14 periods
        avg_gain = gain.rolling(window=14).mean()
        avg_loss = loss.rolling(window=14).mean()

        # Calculate RS and RSI
        rs = avg_gain / avg_loss
        df_copy['RSI'] = 100 - (100 / (1 + rs))

    # Moving Average Convergence Divergence (MACD)
    if any(indicator in selected_indicators for indicator in ["MACD", "MACD_Signal", "MACD_Diff"]):
        # MACD Line = 12-period EMA - 26-period EMA
        ema12 = df_copy['Close'].ewm(span=12, adjust=False).mean()
        ema26 = df_copy['Close'].ewm(span=26, adjust=False).mean()
        df_copy['MACD'] = ema12 - ema26

        # Signal Line = 9-period EMA of MACD Line
        if "MACD_Signal" in selected_indicators or "MACD_Diff" in selected_indicators:
            df_copy['MACD_Signal'] = df_copy['MACD'].ewm(span=9, adjust=False).mean()

        # MACD Histogram = MACD Line - Signal Line
        if "MACD_Diff" in selected_indicators:
            df_copy['MACD_Diff'] = df_copy['MACD'] - df_copy['MACD_Signal']

    # Bollinger Bands
    if any(indicator in selected_indicators for indicator in ["BB_Mid", "BB_High", "BB_Low", "BB_Width"]):
        # Middle Band = 20-period SMA
        df_copy['BB_Mid'] = df_copy['Close'].rolling(window=20).mean()

        # Standard Deviation
        std = df_copy['Close'].rolling(window=20).std()

        # Upper Band = Middle Band + (2 * Standard Deviation)
        if "BB_High" in selected_indicators:
            df_copy['BB_High'] = df_copy['BB_Mid'] + (2 * std)

        # Lower Band = Middle Band - (2 * Standard Deviation)
        if "BB_Low" in selected_indicators:
            df_copy['BB_Low'] = df_copy['BB_Mid'] - (2 * std)

        # Bandwidth
        if "BB_Width" in selected_indicators:
            df_copy['BB_Width'] = (df_copy['BB_High'] - df_copy['BB_Low']) / df_copy['BB_Mid']

    # Average True Range (ATR)
    if "ATR" in selected_indicators:
        # Check if required columns exist
        if all(col in df_copy.columns for col in ['High', 'Low', 'Close']):
            # True Range = max(high - low, abs(high - previous close), abs(low - previous close))
            high_low = df_copy['High'] - df_copy['Low']
            high_close = (df_copy['High'] - df_copy['Close'].shift()).abs()
            low_close = (df_copy['Low'] - df_copy['Close'].shift()).abs()

            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            df_copy['ATR'] = true_range.rolling(window=14).mean()
        else:
            # If required columns don't exist, set ATR to 0
            df_copy['ATR'] = 0

    # Fill NaN values with 0
    df_copy = df_copy.fillna(0)

    return df_copy

def create_time_features(df):
    """
    Create time-based features

    Args:
        df (pd.DataFrame): DataFrame with a datetime index or 'Date' column

    Returns:
        pd.DataFrame: DataFrame with added time features
    """
    df_copy = df.copy()

    # Ensure we have a datetime column
    if 'Date' in df_copy.columns:
        date_col = 'Date'
    else:
        raise ValueError("DataFrame must have a 'Date' column")

    # Ensure Date column is datetime type
    try:
        # Convert to datetime if it's not already
        if not pd.api.types.is_datetime64_any_dtype(df_copy[date_col]):
            df_copy[date_col] = pd.to_datetime(df_copy[date_col], errors='coerce')

        # Check if conversion was successful
        if df_copy[date_col].isna().all():
            raise ValueError("All dates could not be parsed. Please check your date format.")

        # If some dates couldn't be parsed, fill with a default or drop those rows
        if df_copy[date_col].isna().any():
            na_count = df_copy[date_col].isna().sum()
            total_count = len(df_copy)
            na_percentage = (na_count / total_count) * 100

            # Only show warning if it's a significant portion
            if na_percentage > 1:  # More than 1% of data
                print(f"Warning: {na_count} dates ({na_percentage:.1f}%) could not be parsed and will be filled.")

            # Forward fill missing dates
            df_copy[date_col] = df_copy[date_col].ffill()

    except Exception as e:
        print(f"Error converting Date column to datetime: {str(e)}")
        # Create default time features if date conversion fails
        df_copy['Hour'] = 0
        df_copy['DayOfWeek'] = 0
        df_copy['Month'] = 1
        df_copy['Year'] = 2023
        df_copy['DayOfMonth'] = 1
        df_copy['Hour_sin'] = 0
        df_copy['Hour_cos'] = 1
        df_copy['DayOfWeek_sin'] = 0
        df_copy['DayOfWeek_cos'] = 1
        df_copy['Month_sin'] = 0
        df_copy['Month_cos'] = 1
        return df_copy

    # Extract time components
    try:
        df_copy['Hour'] = df_copy[date_col].dt.hour
        df_copy['DayOfWeek'] = df_copy[date_col].dt.dayofweek
        df_copy['Month'] = df_copy[date_col].dt.month
        df_copy['Year'] = df_copy[date_col].dt.year
        df_copy['DayOfMonth'] = df_copy[date_col].dt.day

        # Create cyclical features for hour, day of week, and month
        df_copy['Hour_sin'] = np.sin(2 * np.pi * df_copy['Hour'] / 24)
        df_copy['Hour_cos'] = np.cos(2 * np.pi * df_copy['Hour'] / 24)
        df_copy['DayOfWeek_sin'] = np.sin(2 * np.pi * df_copy['DayOfWeek'] / 7)
        df_copy['DayOfWeek_cos'] = np.cos(2 * np.pi * df_copy['DayOfWeek'] / 7)
        df_copy['Month_sin'] = np.sin(2 * np.pi * df_copy['Month'] / 12)
        df_copy['Month_cos'] = np.cos(2 * np.pi * df_copy['Month'] / 12)

    except Exception as e:
        print(f"Error extracting time features: {str(e)}")
        # Create default time features if extraction fails
        df_copy['Hour'] = 0
        df_copy['DayOfWeek'] = 0
        df_copy['Month'] = 1
        df_copy['Year'] = 2023
        df_copy['DayOfMonth'] = 1
        df_copy['Hour_sin'] = 0
        df_copy['Hour_cos'] = 1
        df_copy['DayOfWeek_sin'] = 0
        df_copy['DayOfWeek_cos'] = 1
        df_copy['Month_sin'] = 0
        df_copy['Month_cos'] = 1

    return df_copy

def prepare_features(df):
    """
    Prepare all features for the model

    Args:
        df (pd.DataFrame): Raw DataFrame with OHLCV data

    Returns:
        pd.DataFrame: DataFrame with all features
    """
    # Make a copy to avoid modifying the original DataFrame
    df_copy = df.copy()

    # Standardize column names - ensure uppercase for compatibility
    column_mapping = {
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    }

    for old_name, new_name in column_mapping.items():
        if old_name in df_copy.columns and new_name not in df_copy.columns:
            df_copy[new_name] = df_copy[old_name]

    # Ensure Date column is datetime
    if 'Date' in df_copy.columns:
        df_copy['Date'] = pd.to_datetime(df_copy['Date'], errors='coerce')

        # Sort by date to ensure correct calculation of indicators
        df_copy = df_copy.sort_values('Date')

    # Add technical indicators (all indicators)
    df_copy = add_technical_indicators(df_copy, None)

    # Add time features
    df_copy = create_time_features(df_copy)

    # Calculate price changes - use Close column (now guaranteed to exist)
    if 'Close' in df_copy.columns:
        df_copy['Price_Change'] = df_copy['Close'].pct_change()
        df_copy['Price_Change_1d'] = df_copy['Close'].pct_change(periods=1)
        df_copy['Price_Change_5d'] = df_copy['Close'].pct_change(periods=5)

        # Calculate volatility
        df_copy['Volatility_5d'] = df_copy['Close'].rolling(window=5).std()

    # Fill NaN values
    df_copy = df_copy.fillna(0)

    return df_copy

def prepare_prediction_data(df, sequence_length=60, feature_subset=None):
    """
    Prepare data for prediction by creating sequences

    Args:
        df (pd.DataFrame): DataFrame with features
        sequence_length (int): Number of time steps to look back
        feature_subset (list): List of feature names to use (for consistent feature selection)

    Returns:
        np.ndarray: Array with shape (1, sequence_length, features)
    """
    # Get the most recent data points
    recent_data = df.iloc[-sequence_length:].copy()

    # Drop the Date column if it exists
    if 'Date' in recent_data.columns:
        recent_data = recent_data.drop('Date', axis=1)

    # Drop any non-numeric columns (like Symbol)
    for col in recent_data.columns:
        if recent_data[col].dtype == 'object':
            recent_data = recent_data.drop(col, axis=1)

    # If a specific feature subset is provided, use only those features
    if feature_subset is not None:
        # Ensure all requested features exist in the dataframe
        available_features = [f for f in feature_subset if f in recent_data.columns]
        if len(available_features) < len(feature_subset):
            missing = set(feature_subset) - set(available_features)
            print(f"Warning: Missing features: {missing}")

        if available_features:
            recent_data = recent_data[available_features]
        else:
            print("Warning: None of the requested features are available. Using all features.")

    # Define a consistent set of core features if no subset is provided
    # These are the essential features that should always be included
    elif 'Close' in recent_data.columns or 'close' in recent_data.columns:
        # Ensure we have the right column names
        if 'close' in recent_data.columns and 'Close' not in recent_data.columns:
            recent_data['Close'] = recent_data['close']
        if 'open' in recent_data.columns and 'Open' not in recent_data.columns:
            recent_data['Open'] = recent_data['open']
        if 'high' in recent_data.columns and 'High' not in recent_data.columns:
            recent_data['High'] = recent_data['high']
        if 'low' in recent_data.columns and 'Low' not in recent_data.columns:
            recent_data['Low'] = recent_data['low']
        if 'volume' in recent_data.columns and 'Volume' not in recent_data.columns:
            recent_data['Volume'] = recent_data['volume']

        core_features = ['Open', 'High', 'Low', 'Close', 'Volume', 'SMA20', 'RSI']
        available_core = [f for f in core_features if f in recent_data.columns]

        if len(available_core) >= 5:  # If we have at least 5 core features
            recent_data = recent_data[available_core]
            print(f"Using core features: {available_core}")

    # Convert to numpy array
    features = recent_data.values

    print(f"Feature shape for prediction: {features.shape}")
    return features
