"""
Improved NumPy BitGenerator Fix

This module provides a more robust fix for the BitGenerator compatibility issue between NumPy 1.23.5
and scikit-learn. It addresses the error:
'<class 'app.utils.numpy_bitgenerator_fix.fix_numpy_bitgenerator.<locals>.FixedMT19937'> is not a known BitGenerator module.'

This improved version uses a global class definition to avoid the nested class issue.
"""

import sys
import logging
import numpy as np
import random

logger = logging.getLogger(__name__)

# Define the fixed MT19937 class globally to avoid nested class issues
class FixedMT19937:
    """A fixed version of MT19937 that works with scikit-learn"""
    def __init__(self, seed=None):
        self.seed = seed
        # Store the random state
        self._random_state = random.Random(seed)
    
    def jumped(self, *args, **kwargs):
        """Implement jumped method required by some algorithms"""
        return self
    
    def next_uint32(self):
        """Generate a random 32-bit unsigned integer"""
        return self._random_state.randint(0, 2**32-1)
    
    def next_uint64(self):
        """Generate a random 64-bit unsigned integer"""
        return self._random_state.randint(0, 2**64-1)
    
    def next_double(self):
        """Generate a random double precision float"""
        return self._random_state.random()
    
    def __repr__(self):
        return f"FixedMT19937(seed={self.seed})"

def apply_numpy_fix():
    """
    Apply a comprehensive fix for NumPy BitGenerator issues.
    This addresses compatibility problems between NumPy and scikit-learn.
    """
    try:
        logger.info(f"NumPy version: {np.__version__}")
        
        # Register our fixed MT19937 class
        if hasattr(np.random, 'MT19937'):
            # Save the original class for reference
            original_mt19937 = np.random.MT19937
            
            # Replace with our fixed version
            np.random.MT19937 = FixedMT19937
            
            # If _mt19937 module exists, patch it too
            if 'numpy.random._mt19937' in sys.modules:
                sys.modules['numpy.random._mt19937'].MT19937 = FixedMT19937
                logger.info("Patched numpy.random._mt19937.MT19937")
        else:
            # If MT19937 doesn't exist, create it
            np.random.MT19937 = FixedMT19937
            logger.info("Created numpy.random.MT19937")
        
        # Make sure BitGenerator exists
        if not hasattr(np.random, 'BitGenerator'):
            # Create a simple BitGenerator class
            class BitGenerator:
                def __init__(self, seed=None):
                    self.seed = seed
            
            np.random.BitGenerator = BitGenerator
            logger.info("Created numpy.random.BitGenerator")
        
        # Patch any references to the problematic class
        for module_name in list(sys.modules.keys()):
            if module_name.startswith('app.utils.numpy'):
                module = sys.modules[module_name]
                if hasattr(module, 'FixedMT19937'):
                    module.FixedMT19937 = FixedMT19937
                    logger.info(f"Patched {module_name}.FixedMT19937")
        
        # Add our class to this module's globals
        globals()['FixedMT19937'] = FixedMT19937
        
        logger.info("NumPy BitGenerator fix successfully applied")
        return True
    
    except Exception as e:
        logger.error(f"Error applying NumPy fix: {str(e)}")
        return False

# Apply the fix when this module is imported
fix_applied = apply_numpy_fix()
