"""
Fix for TensorFlow import issues on Windows
"""

import os
import sys
import logging

logger = logging.getLogger(__name__)

def fix_tensorflow_imports():
    """
    Apply fixes for TensorFlow import issues, particularly on Windows systems.
    This addresses the "Unable to convert function return value to a Python type" error.
    """
    try:
        # Set environment variables to control TensorFlow behavior
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # Reduce TensorFlow logging
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # Disable GPU to avoid CUDA errors
        
        # Fix for "Unable to convert function return value to a Python type" error
        if sys.platform == 'win32':
            # Add DLL directory to path on Windows
            dll_path = os.path.join(sys.exec_prefix, 'Library', 'bin')
            if os.path.exists(dll_path):
                try:
                    os.add_dll_directory(dll_path)
                    logger.info(f"Added DLL directory: {dll_path}")
                except Exception as e:
                    logger.error(f"Error adding DLL directory: {str(e)}")
            
            # Check for other potential DLL paths
            other_dll_paths = [
                os.path.join(sys.exec_prefix, 'Library', 'bin'),
                os.path.join(sys.exec_prefix, 'Library', 'lib'),
                os.path.join(sys.exec_prefix, 'DLLs'),
                os.path.join(sys.exec_prefix, 'bin')
            ]
            
            for path in other_dll_paths:
                if os.path.exists(path) and path != dll_path:
                    try:
                        os.add_dll_directory(path)
                        logger.info(f"Added additional DLL directory: {path}")
                    except Exception as e:
                        logger.error(f"Error adding additional DLL directory {path}: {str(e)}")
        
        # Try to import TensorFlow to check if the fix worked
        try:
            import tensorflow as tf
            logger.info(f"TensorFlow imported successfully (version {tf.__version__})")
            return True
        except ImportError as e:
            logger.error(f"TensorFlow import failed: {str(e)}")
            return False
    except Exception as e:
        logger.error(f"Error applying TensorFlow fixes: {str(e)}")
        return False
