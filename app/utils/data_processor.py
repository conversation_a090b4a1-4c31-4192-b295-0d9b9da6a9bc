import numpy as np
import pandas as pd
from typing import Tuple, List, Dict, Any, Optional, Union

def create_sequences(X: np.ndarray, y: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    Create sequences for time series prediction.

    Args:
        X (np.ndarray): Features
        y (np.ndarray): Target
        sequence_length (int): Length of each sequence

    Returns:
        Tuple[np.ndarray, np.ndarray]: Sequences of features and targets
    """
    X_seq = []
    y_seq = []

    for i in range(len(X) - sequence_length):
        X_seq.append(X[i:i+sequence_length])
        y_seq.append(y[i+sequence_length])

    return np.array(X_seq), np.array(y_seq)

def add_time_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add time-based features to the dataframe.

    Args:
        df (pd.DataFrame): Input dataframe with a DatetimeIndex

    Returns:
        pd.DataFrame: Dataframe with additional time features
    """
    # Make a copy to avoid modifying the original dataframe
    df_copy = df.copy()

    # Ensure the index is a DatetimeIndex
    if not isinstance(df_copy.index, pd.DatetimeIndex):
        if 'Date' in df_copy.columns:
            df_copy['Date'] = pd.to_datetime(df_copy['Date'])
            df_copy.set_index('Date', inplace=True)
        else:
            raise ValueError("DataFrame must have a DatetimeIndex or a 'Date' column")

    # Add day of week (0=Monday, 6=Sunday)
    df_copy['day_of_week'] = df_copy.index.dayofweek

    # Add day of month
    df_copy['day_of_month'] = df_copy.index.day

    # Add day of year
    df_copy['day_of_year'] = df_copy.index.dayofyear

    # Add month
    df_copy['month'] = df_copy.index.month

    # Add quarter
    df_copy['quarter'] = df_copy.index.quarter

    # Add year
    df_copy['year'] = df_copy.index.year

    # Add is_month_start/end
    df_copy['is_month_start'] = df_copy.index.is_month_start.astype(int)
    df_copy['is_month_end'] = df_copy.index.is_month_end.astype(int)

    # Add is_quarter_start/end
    df_copy['is_quarter_start'] = df_copy.index.is_quarter_start.astype(int)
    df_copy['is_quarter_end'] = df_copy.index.is_quarter_end.astype(int)

    # Add is_year_start/end
    df_copy['is_year_start'] = df_copy.index.is_year_start.astype(int)
    df_copy['is_year_end'] = df_copy.index.is_year_end.astype(int)

    return df_copy

def add_lag_features(df: pd.DataFrame, columns: List[str], lag_periods: List[int]) -> pd.DataFrame:
    """
    Add lagged features to the dataframe.

    Args:
        df (pd.DataFrame): Input dataframe
        columns (List[str]): Columns to create lags for
        lag_periods (List[int]): Lag periods to create

    Returns:
        pd.DataFrame: Dataframe with additional lag features
    """
    # Make a copy to avoid modifying the original dataframe
    df_copy = df.copy()

    # Create lag features
    for col in columns:
        if col in df_copy.columns:
            for lag in lag_periods:
                df_copy[f'{col}_lag_{lag}'] = df_copy[col].shift(lag)

    return df_copy

def add_rolling_features(df: pd.DataFrame, columns: List[str], windows: List[int]) -> pd.DataFrame:
    """
    Add rolling window features to the dataframe.

    Args:
        df (pd.DataFrame): Input dataframe
        columns (List[str]): Columns to create rolling features for
        windows (List[int]): Window sizes to use

    Returns:
        pd.DataFrame: Dataframe with additional rolling features
    """
    # Make a copy to avoid modifying the original dataframe
    df_copy = df.copy()

    # Create rolling features
    for col in columns:
        if col in df_copy.columns:
            for window in windows:
                # Rolling mean
                df_copy[f'{col}_rolling_mean_{window}'] = df_copy[col].rolling(window=window).mean()

                # Rolling standard deviation
                df_copy[f'{col}_rolling_std_{window}'] = df_copy[col].rolling(window=window).std()

                # Rolling min
                df_copy[f'{col}_rolling_min_{window}'] = df_copy[col].rolling(window=window).min()

                # Rolling max
                df_copy[f'{col}_rolling_max_{window}'] = df_copy[col].rolling(window=window).max()

    return df_copy

def add_return_features(df: pd.DataFrame, columns: List[str], periods: List[int]) -> pd.DataFrame:
    """
    Add return features to the dataframe.

    Args:
        df (pd.DataFrame): Input dataframe
        columns (List[str]): Columns to create return features for
        periods (List[int]): Periods to calculate returns over

    Returns:
        pd.DataFrame: Dataframe with additional return features
    """
    # Make a copy to avoid modifying the original dataframe
    df_copy = df.copy()

    # Create return features
    for col in columns:
        if col in df_copy.columns:
            for period in periods:
                # Simple returns
                df_copy[f'{col}_return_{period}'] = df_copy[col].pct_change(periods=period)

                # Log returns
                df_copy[f'{col}_log_return_{period}'] = np.log(df_copy[col] / df_copy[col].shift(period))

    return df_copy

def prepare_data_for_training(
    df: pd.DataFrame,
    target_column: str,
    feature_columns: Optional[List[str]] = None,
    add_time_features: bool = True,
    add_lags: bool = True,
    lag_periods: List[int] = [1, 5, 10],
    add_rolling: bool = True,
    rolling_windows: List[int] = [5, 10, 20],
    add_returns: bool = True,
    return_periods: List[int] = [1, 5, 10],
    test_size: float = 0.2
) -> Dict[str, Any]:
    """
    Prepare data for model training.

    Args:
        df (pd.DataFrame): Input dataframe
        target_column (str): Target column to predict
        feature_columns (Optional[List[str]]): Columns to use as features
        add_time_features (bool): Whether to add time features
        add_lags (bool): Whether to add lag features
        lag_periods (List[int]): Lag periods to create
        add_rolling (bool): Whether to add rolling features
        rolling_windows (List[int]): Window sizes to use
        add_returns (bool): Whether to add return features
        return_periods (List[int]): Periods to calculate returns over
        test_size (float): Proportion of data to use for testing

    Returns:
        Dict[str, Any]: Dictionary containing train and test data
    """
    # Make a copy to avoid modifying the original dataframe
    df_copy = df.copy()

    # Ensure the dataframe has a DatetimeIndex
    if not isinstance(df_copy.index, pd.DatetimeIndex):
        if 'Date' in df_copy.columns:
            df_copy['Date'] = pd.to_datetime(df_copy['Date'])
            df_copy.set_index('Date', inplace=True)

    # Use all numeric columns as features if not specified
    if feature_columns is None:
        feature_columns = df_copy.select_dtypes(include=[np.number]).columns.tolist()
        # Remove the target column from features if it's there
        if target_column in feature_columns:
            feature_columns.remove(target_column)

    # Add time features
    if add_time_features:
        df_copy = add_time_features(df_copy)

    # Add lag features
    if add_lags:
        df_copy = add_lag_features(df_copy, [target_column] + feature_columns, lag_periods)

    # Add rolling features
    if add_rolling:
        df_copy = add_rolling_features(df_copy, [target_column] + feature_columns, rolling_windows)

    # Add return features
    if add_returns:
        df_copy = add_return_features(df_copy, [target_column] + feature_columns, return_periods)

    # Drop rows with NaN values
    df_copy = df_copy.dropna()

    # Split into features and target
    X = df_copy.drop(columns=[target_column])
    y = df_copy[target_column]

    # Split into train and test sets
    train_size = int(len(df_copy) * (1 - test_size))
    X_train, X_test = X.iloc[:train_size], X.iloc[train_size:]
    y_train, y_test = y.iloc[:train_size], y.iloc[train_size:]

    return {
        'X_train': X_train,
        'y_train': y_train,
        'X_test': X_test,
        'y_test': y_test,
        'feature_columns': X.columns.tolist()
    }
