"""
Retry utilities for handling transient errors in web scraping and other operations.

This module provides decorators and utilities for implementing retry logic with
exponential backoff and jitter to handle transient errors in a robust way.
"""

import time
import random
import logging
import functools
from typing import Callable, Type, Union, List, Optional, Any

# Configure logging
logger = logging.getLogger(__name__)

def retry_with_backoff(
    max_retries: int = 3,
    backoff_factor: float = 2.0,
    initial_wait: float = 1.0,
    max_wait: float = 60.0,
    jitter: bool = True,
    exceptions: Union[Type[Exception], List[Type[Exception]]] = Exception,
    on_retry: Optional[Callable[[Exception, int, float], None]] = None
) -> Callable:
    """
    Decorator for retrying a function with exponential backoff.

    Args:
        max_retries (int): Maximum number of retries before giving up
        backoff_factor (float): Multiplier for the wait time between retries
        initial_wait (float): Initial wait time in seconds
        max_wait (float): Maximum wait time in seconds
        jitter (bool): Whether to add random jitter to the wait time
        exceptions (Union[Type[Exception], List[Type[Exception]]]): Exception(s) to catch and retry
        on_retry (Optional[Callable]): Function to call on each retry with (exception, retry_count, wait_time)

    Returns:
        Callable: Decorated function with retry logic
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            retry_count = 0
            wait_time = initial_wait

            while True:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logger.error(f"Failed after {max_retries} attempts: {str(e)}")
                        raise

                    # Calculate wait time with exponential backoff
                    wait_time = min(wait_time * backoff_factor, max_wait)
                    
                    # Add jitter if enabled (±20% of wait time)
                    if jitter:
                        wait_time = wait_time * (0.8 + 0.4 * random.random())

                    logger.warning(f"Attempt {retry_count} failed, retrying in {wait_time:.2f}s: {str(e)}")
                    
                    # Call the on_retry callback if provided
                    if on_retry:
                        on_retry(e, retry_count, wait_time)
                    
                    time.sleep(wait_time)
        
        return wrapper
    
    return decorator

def retry_on_exception(
    func: Callable,
    max_retries: int = 3,
    backoff_factor: float = 2.0,
    initial_wait: float = 1.0,
    max_wait: float = 60.0,
    jitter: bool = True,
    exceptions: Union[Type[Exception], List[Type[Exception]]] = Exception
) -> Any:
    """
    Non-decorator version of retry_with_backoff for use with existing functions.

    Args:
        func (Callable): Function to retry
        max_retries (int): Maximum number of retries before giving up
        backoff_factor (float): Multiplier for the wait time between retries
        initial_wait (float): Initial wait time in seconds
        max_wait (float): Maximum wait time in seconds
        jitter (bool): Whether to add random jitter to the wait time
        exceptions (Union[Type[Exception], List[Type[Exception]]]): Exception(s) to catch and retry

    Returns:
        Any: Result of the function call
    """
    retry_count = 0
    wait_time = initial_wait

    while True:
        try:
            return func()
        except exceptions as e:
            retry_count += 1
            if retry_count > max_retries:
                logger.error(f"Failed after {max_retries} attempts: {str(e)}")
                raise

            # Calculate wait time with exponential backoff
            wait_time = min(wait_time * backoff_factor, max_wait)
            
            # Add jitter if enabled (±20% of wait time)
            if jitter:
                wait_time = wait_time * (0.8 + 0.4 * random.random())

            logger.warning(f"Attempt {retry_count} failed, retrying in {wait_time:.2f}s: {str(e)}")
            time.sleep(wait_time)
