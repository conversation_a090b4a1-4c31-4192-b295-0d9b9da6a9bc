"""
State management for the AI Stocks Bot application.

This module provides utilities for managing application state transitions,
including loading and saving data, models, and predictions.
"""

import os
import sys
import logging
import json
import pickle
import time
import types
from datetime import datetime
from typing import Dict, Any, Optional, Union, List, Tuple, Type
import pandas as pd
import numpy as np
import streamlit as st

# Configure logging
logger = logging.getLogger(__name__)

# Import session state management
from app.utils.session_state import (
    get_session_value,
    set_session_value,
    track_error,
    track_performance
)

# Import error handling utilities
try:
    from app.utils.error_handling import (
        handle_exception,
        log_exception,
        ErrorHandler
    )
    ERROR_HANDLING_AVAILABLE = True
except ImportError:
    logger.warning("Error handling utilities not available in state_manager module")
    ERROR_HANDLING_AVAILABLE = False

    # Define dummy functions if the real ones are not available
    def handle_exception(exc, default_return_value=None, raise_exception=False, log_level=logging.ERROR):
        logger.error(f"Error: {str(exc)}")
        if raise_exception:
            raise exc
        return default_return_value

    def log_exception(exc, level=logging.ERROR, include_traceback=True):
        logger.log(level, f"Exception: {str(exc)}")

    class ErrorHandler:
        def __init__(self, default_return_value=None, raise_exception=False, log_level=logging.ERROR):
            self.default_return_value = default_return_value
            self.raise_exception = raise_exception
            self.log_level = log_level
            self.exception = None

        def __enter__(self):
            return self

        def __exit__(self, exc_type: Optional[Type[Exception]],
                 exc_val: Optional[Exception],
                 exc_tb: Optional[types.TracebackType]) -> bool:
            if exc_val:
                self.exception = exc_val
                logger.log(self.log_level, f"Exception: {str(exc_val)}")
                return not self.raise_exception
            return True

# Import memory management utilities
try:
    from app.utils.memory_management import cleanup_memory
    MEMORY_MANAGEMENT_AVAILABLE = True
except ImportError:
    logger.warning("Memory management utilities not available in state_manager module")
    MEMORY_MANAGEMENT_AVAILABLE = False

    # Define a dummy function if the real one is not available
    def cleanup_memory(objects_to_clean=None):
        pass

# Import data processing utilities
from app.utils.data_processing import load_csv_data, preprocess_data, save_scaler, load_scaler

def load_stock_data(symbol: str, data_dir: str = 'data/stocks') -> Optional[pd.DataFrame]:
    """
    Load stock data from a CSV file in the stocks directory.

    Args:
        symbol (str): Stock symbol (filename without extension)
        data_dir (str): Directory to look for stock files

    Returns:
        pd.DataFrame: DataFrame containing the stock data, or None if file not found
    """
    try:
        start_time = time.time()

        # Check if data is already in session state
        if get_session_value('symbol') == symbol and get_session_value('historical_data') is not None:
            logger.info(f"Using cached data for {symbol} from session state")
            return get_session_value('historical_data')

        # Construct file path
        file_path = os.path.join(data_dir, f"{symbol}.csv")

        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"Stock file not found: {file_path}")
            return None

        # Read CSV file
        df = pd.read_csv(file_path)

        # Convert Date column to datetime
        df['Date'] = pd.to_datetime(df['Date'], errors='coerce')

        # Check for NaN dates
        if df['Date'].isna().any():
            logger.warning(f"Some dates in {symbol} couldn't be parsed. Please check the file format.")

        # Remove rows with NaN dates
        df = df.dropna(subset=['Date'])

        # Sort by date
        df = df.sort_values('Date')

        # Check if the data includes 2025
        has_2025 = (df['Date'].dt.year == 2025).any()

        end_time = time.time()
        logger.info(f"Loaded stock data for {symbol} from {file_path} in {end_time - start_time:.2f} seconds")
        logger.info(f"Date range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")
        logger.info(f"Data shape: {df.shape}")

        if has_2025:
            logger.info(f"File {symbol} contains 2025 data")

        # Store in session state
        set_session_value('symbol', symbol)
        set_session_value('historical_data', df)

        # Track performance
        track_performance('data_load', end_time - start_time)

        return df

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value=None)
        else:
            logger.error(f"Error loading stock data: {str(e)}")
            return None

def load_and_process_data(symbol: str, data_dir: str = 'data/stocks') -> Optional[pd.DataFrame]:
    """
    Load and process stock data with feature engineering.

    Args:
        symbol (str): Stock symbol
        data_dir (str): Directory to look for stock files

    Returns:
        pd.DataFrame: Processed DataFrame with technical indicators
    """
    try:
        from app.utils.feature_engineering import prepare_features

        # Check if processed data is already in session state
        if get_session_value('symbol') == symbol and get_session_value('processed_data') is not None:
            logger.info(f"Using cached processed data for {symbol} from session state")
            return get_session_value('processed_data')

        # Load raw data
        df = load_stock_data(symbol, data_dir)
        if df is None:
            return None

        # Apply feature engineering
        start_time = time.time()
        df_features = prepare_features(df)
        end_time = time.time()

        logger.info(f"Feature engineering for {symbol} completed in {end_time - start_time:.2f} seconds")
        logger.info(f"Features shape: {df_features.shape}")

        # Store in session state
        set_session_value('processed_data', df_features)

        # Track performance
        track_performance('feature_engineering', end_time - start_time)

        return df_features

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value=None)
        else:
            logger.error(f"Error processing data: {str(e)}")
            return None

def get_available_stock_files(data_dir: str = 'data/stocks') -> List[str]:
    """
    Get a list of available stock files in the stocks directory.

    Args:
        data_dir (str): Directory to look for stock files

    Returns:
        list: List of stock symbols (filenames without extension)
    """
    try:
        # Check if available stocks are already in session state
        if get_session_value('available_stocks') is not None:
            return get_session_value('available_stocks')

        # Create directory if it doesn't exist
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # Get all CSV files in the directory
        files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

        # Extract symbols (filenames without extension)
        symbols = [os.path.splitext(f)[0] for f in files]

        logger.info(f"Found {len(symbols)} stock files in {data_dir}")

        # Store in session state
        set_session_value('available_stocks', symbols)

        return symbols

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value=[])
        else:
            logger.error(f"Error getting stock files: {str(e)}")
            return []

def save_uploaded_data(df: pd.DataFrame, symbol: str, data_dir: str = 'data/stocks') -> Optional[str]:
    """
    Save uploaded data to disk in the stocks folder.

    Args:
        df (pd.DataFrame): DataFrame to save
        symbol (str): Stock symbol
        data_dir (str): Directory to save the data

    Returns:
        str: Path to the saved file
    """
    if df is None or symbol is None:
        return None

    try:
        # Create directory if it doesn't exist
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # Make a copy of the DataFrame to avoid modifying the original
        df_save = df.copy()

        # Ensure Date column is datetime
        if 'Date' in df_save.columns:
            df_save['Date'] = pd.to_datetime(df_save['Date'], errors='coerce')

            # Remove rows with NaN dates
            df_save = df_save.dropna(subset=['Date'])

            # Sort by date
            df_save = df_save.sort_values('Date')

            # Check if the data includes 2025
            has_2025 = (df_save['Date'].dt.year == 2025).any()
            if has_2025:
                logger.info(f"File {symbol} contains 2025 data")

        # Save DataFrame to CSV
        file_path = os.path.join(data_dir, f"{symbol}.csv")
        df_save.to_csv(file_path, index=False)

        # Log success message
        logger.info(f"Saved data to {file_path}")
        logger.info(f"Date range: {df_save['Date'].min().strftime('%Y-%m-%d')} to {df_save['Date'].max().strftime('%Y-%m-%d')}")

        # Update session state
        set_session_value('symbol', symbol)
        set_session_value('historical_data', df_save)

        # Clear available stocks cache to force refresh
        set_session_value('available_stocks', None)

        return file_path

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value=None)
        else:
            logger.error(f"Error saving data: {str(e)}")
            return None

def save_model(model, symbol: str, model_type: str, horizon: int, models_dir: str = 'saved_models') -> Optional[str]:
    """
    Save a trained model to disk.

    Args:
        model: The trained model to save
        symbol (str): Stock symbol
        model_type (str): Type of model (e.g., 'lstm', 'rf', 'gb')
        horizon (int): Prediction horizon in minutes
        models_dir (str): Directory to save the model

    Returns:
        str: Path to the saved model
    """
    try:
        import joblib

        # Create directory if it doesn't exist
        if not os.path.exists(models_dir):
            os.makedirs(models_dir)

        # Determine file extension based on model type
        if model_type.lower() in ['lstm', 'bilstm', 'transformer']:
            ext = 'h5'
        else:
            ext = 'joblib'

        # Construct file path
        file_path = os.path.join(models_dir, f"{symbol}_{model_type.lower()}_{horizon}min.{ext}")

        # Save model
        if ext == 'h5':
            model.save(file_path)
        else:
            joblib.dump(model, file_path)

        logger.info(f"Saved {model_type} model for {symbol} with {horizon} minute horizon to {file_path}")

        # Update models in session state
        models = get_session_value('models', {})
        models[f"{symbol}_{model_type}_{horizon}"] = model
        set_session_value('models', models)

        return file_path

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value=None)
        else:
            logger.error(f"Error saving model: {str(e)}")
            return None

def load_model(symbol: str, model_type: str, horizon: int, models_dir: str = 'saved_models') -> Any:
    """
    Load a trained model from disk.

    Args:
        symbol (str): Stock symbol
        model_type (str): Type of model (e.g., 'lstm', 'rf', 'gb')
        horizon (int): Prediction horizon in minutes
        models_dir (str): Directory to load the model from

    Returns:
        The loaded model
    """
    try:
        import joblib

        # Check if model is already in session state
        model_key = f"{symbol}_{model_type}_{horizon}"
        models = get_session_value('models', {})
        if model_key in models:
            logger.info(f"Using cached model for {symbol} with {horizon} minute horizon from session state")
            return models[model_key]

        # Determine file extension based on model type
        if model_type.lower() in ['lstm', 'bilstm', 'transformer']:
            ext = 'h5'
        else:
            ext = 'joblib'

        # Construct file path
        file_path = os.path.join(models_dir, f"{symbol}_{model_type.lower()}_{horizon}min.{ext}")

        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"Model file not found: {file_path}")
            return None

        # Load model
        if ext == 'h5':
            from tensorflow.keras.models import load_model as keras_load_model  # type: ignore
            model = keras_load_model(file_path)
        else:
            model = joblib.load(file_path)

        logger.info(f"Loaded {model_type} model for {symbol} with {horizon} minute horizon from {file_path}")

        # Store in session state
        models[model_key] = model
        set_session_value('models', models)

        return model

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value=None)
        else:
            logger.error(f"Error loading model: {str(e)}")
            return None

def save_predictions(predictions: Dict, symbol: str, model_type: str, predictions_dir: str = 'predictions') -> Optional[str]:
    """
    Save predictions to disk.

    Args:
        predictions (Dict): Dictionary of predictions
        symbol (str): Stock symbol
        model_type (str): Type of model used for predictions
        predictions_dir (str): Directory to save the predictions

    Returns:
        str: Path to the saved predictions
    """
    try:
        # Create directory if it doesn't exist
        if not os.path.exists(predictions_dir):
            os.makedirs(predictions_dir)

        # Construct file path
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        file_path = os.path.join(predictions_dir, f"{symbol}_{model_type}_{timestamp}.json")

        # Convert datetime keys to strings
        serializable_predictions = {}
        for horizon, pred_dict in predictions.items():
            serializable_predictions[horizon] = {
                dt.strftime('%Y-%m-%d %H:%M:%S'): price
                for dt, price in pred_dict.items()
            }

        # Save predictions
        with open(file_path, 'w') as f:
            json.dump(serializable_predictions, f, indent=2)

        logger.info(f"Saved predictions for {symbol} using {model_type} to {file_path}")

        # Update predictions in session state
        all_predictions = get_session_value('predictions', {})
        all_predictions[f"{symbol}_{model_type}_{timestamp}"] = predictions
        set_session_value('predictions', all_predictions)

        return file_path

    except Exception as e:
        if ERROR_HANDLING_AVAILABLE:
            return handle_exception(e, default_return_value=None)
        else:
            logger.error(f"Error saving predictions: {str(e)}")
            return None
