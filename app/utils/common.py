"""
Common utility functions for the AI Stocks Bot application.
This module centralizes frequently used functions to reduce code duplication.
"""
import os
import logging
import pandas as pd
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

def load_stock_data(symbol, base_path='data/stocks'):
    """
    Load stock data from CSV file with standardized error handling.
    
    Args:
        symbol (str): Stock symbol
        base_path (str): Base directory for stock data files
        
    Returns:
        pd.DataFrame or None: DataFrame with stock data or None if file not found
    """
    try:
        file_path = os.path.join(base_path, f"{symbol}.csv")
        
        if not os.path.exists(file_path):
            logger.error(f"Stock data file not found: {file_path}")
            return None
            
        # Load data and convert date column
        start_time = datetime.now()
        df = pd.read_csv(file_path)
        
        # Convert Date column to datetime
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
            
        # Log performance metrics
        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"Loaded stock data for {symbol} from {file_path} in {duration:.2f} seconds")
        logger.info(f"Date range: {df['Date'].min().date()} to {df['Date'].max().date()}")
        logger.info(f"Data shape: {df.shape}")
        
        # Check if data contains current year
        current_year = datetime.now().year
        if df['Date'].max().year >= current_year:
            logger.info(f"File {symbol} contains {current_year} data")
            
        return df
        
    except Exception as e:
        logger.error(f"Error loading stock data for {symbol}: {str(e)}")
        return None

def get_available_stocks(base_path='data/stocks'):
    """
    Get list of available stock symbols from data directory.
    
    Args:
        base_path (str): Base directory for stock data files
        
    Returns:
        list: List of available stock symbols
    """
    try:
        if not os.path.exists(base_path):
            logger.warning(f"Stock data directory not found: {base_path}")
            return []
            
        # Get all CSV files in the directory
        stock_files = [f for f in os.listdir(base_path) if f.endswith('.csv')]
        
        # Extract symbols from filenames
        symbols = [os.path.splitext(f)[0] for f in stock_files]
        
        logger.info(f"Found {len(symbols)} stock files in {base_path}")
        return symbols
        
    except Exception as e:
        logger.error(f"Error getting available stocks: {str(e)}")
        return []

def format_prediction_result(symbol, current_price, predicted_price, horizon, model_type, timestamp=None):
    """
    Format prediction results in a standardized way.
    
    Args:
        symbol (str): Stock symbol
        current_price (float): Current stock price
        predicted_price (float): Predicted stock price
        horizon (int): Prediction horizon
        model_type (str): Model type used for prediction
        timestamp (str, optional): Timestamp of prediction
        
    Returns:
        dict: Formatted prediction result
    """
    if timestamp is None:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
    # Calculate change and percent change
    price_change = predicted_price - current_price
    percent_change = (price_change / current_price) * 100 if current_price > 0 else 0
    
    return {
        'symbol': symbol,
        'current_price': current_price,
        'predicted_price': predicted_price,
        'price_change': price_change,
        'percent_change': percent_change,
        'horizon': horizon,
        'model_type': model_type,
        'timestamp': timestamp
    }

def safe_float_convert(value, default=0.0):
    """
    Safely convert a value to float with error handling.
    
    Args:
        value: Value to convert
        default (float): Default value if conversion fails
        
    Returns:
        float: Converted value or default
    """
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def get_model_display_name(model_type):
    """
    Get a user-friendly display name for a model type.
    
    Args:
        model_type (str): Internal model type code
        
    Returns:
        str: User-friendly model name
    """
    model_names = {
        'rf': 'Random Forest',
        'gb': 'Gradient Boosting',
        'lr': 'Linear Regression',
        'svr': 'Support Vector Regression',
        'xgb': 'XGBoost',
        'lstm': 'LSTM Neural Network',
        'bilstm': 'Bidirectional LSTM',
        'prophet': 'Facebook Prophet',
        'hybrid': 'ARIMA-ML Hybrid',
        'ensemble': 'Ensemble Model'
    }
    
    return model_names.get(model_type.lower(), model_type)
