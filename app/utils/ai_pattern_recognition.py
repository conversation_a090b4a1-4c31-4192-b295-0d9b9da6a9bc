"""
Advanced AI Pattern Recognition System
Detects complex chart patterns and trading signals using AI/ML techniques
"""

import logging
import numpy as np
import pandas as pd
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy import signal
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PatternSignal:
    """Pattern recognition signal"""
    pattern_name: str
    pattern_type: str  # BULLISH, BEARISH, NEUTRAL
    confidence: float  # 0 to 1
    strength: float  # Pattern strength
    start_date: datetime
    end_date: datetime
    entry_price: float
    target_price: float
    stop_loss: float
    risk_reward_ratio: float
    pattern_description: str
    key_levels: List[float]
    volume_confirmation: bool
    timestamp: datetime

@dataclass
class CandlestickPattern:
    """Candlestick pattern detection result"""
    pattern_name: str
    pattern_type: str
    confidence: float
    candle_indices: List[int]
    description: str
    reliability: str  # HIGH, MEDIUM, LOW

class AIPatternRecognition:
    """Advanced AI-powered pattern recognition system"""

    def __init__(self):
        self.pattern_history = []
        self.logger = logging.getLogger(__name__)
        self.api_base_url = "http://127.0.0.1:8000"
        self._initialize_pattern_settings()

    def check_api_server_status(self) -> bool:
        """Check if TradingView API server is running"""
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def fetch_live_data_from_api(self, symbol: str) -> Optional[Dict]:
        """Fetch live data from TradingView API server"""
        try:
            # Format symbol for EGX
            egx_symbol = f"EGX-{symbol}"

            payload = {
                "pairs": [egx_symbol],
                "intervals": ["1D"]
            }

            response = requests.post(
                f"{self.api_base_url}/api/scrape_pairs",
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                stock_data = data.get(egx_symbol, [])

                if stock_data:
                    api_data = stock_data[0]

                    # Extract price from the API response
                    raw_price = api_data.get('price', 0)

                    # Convert from piasters to EGP (divide by 1000)
                    # API returns prices in piasters (81500 = 81.50 EGP)
                    current_price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                    # Extract pivot data for support/resistance calculation
                    pivots = api_data.get('pivots', {})

                    return {
                        'symbol': symbol,
                        'current_price': current_price,
                        'currency': 'EGP',
                        'timestamp': datetime.now().isoformat(),
                        'source': 'TradingView API',
                        'real_time': True,
                        'pivots': pivots,
                        'oscillators': api_data.get('oscillators', {}),
                        'moving_averages': api_data.get('moving_averages', {}),
                        'full_data': api_data
                    }

            return None

        except Exception as e:
            self.logger.error(f"Error fetching live data from API: {str(e)}")
            return None

    def _initialize_pattern_settings(self):
        """Initialize pattern recognition settings"""
        # Pattern recognition parameters
        self.min_pattern_length = 5
        self.max_pattern_length = 50
        self.confidence_threshold = 0.6

        # Candlestick pattern definitions
        self.candlestick_patterns = {
            'doji': {'reliability': 'MEDIUM', 'type': 'REVERSAL'},
            'hammer': {'reliability': 'HIGH', 'type': 'BULLISH'},
            'shooting_star': {'reliability': 'HIGH', 'type': 'BEARISH'},
            'engulfing_bullish': {'reliability': 'HIGH', 'type': 'BULLISH'},
            'engulfing_bearish': {'reliability': 'HIGH', 'type': 'BEARISH'},
            'morning_star': {'reliability': 'HIGH', 'type': 'BULLISH'},
            'evening_star': {'reliability': 'HIGH', 'type': 'BEARISH'},
            'harami_bullish': {'reliability': 'MEDIUM', 'type': 'BULLISH'},
            'harami_bearish': {'reliability': 'MEDIUM', 'type': 'BEARISH'}
        }

    def analyze_patterns(self, df: pd.DataFrame, symbol: str = None) -> Dict[str, List]:
        """
        Comprehensive pattern analysis using AI techniques with live data integration
        """
        try:
            logger.info("Starting comprehensive pattern analysis")

            if df.empty or len(df) < self.min_pattern_length:
                logger.warning("Insufficient data for pattern analysis")
                return self._create_empty_pattern_result()

            # Ensure required columns exist
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in df.columns for col in required_columns):
                logger.error("Missing required OHLCV columns")
                return self._create_empty_pattern_result()

            pattern_results = {
                'chart_patterns': [],
                'candlestick_patterns': [],
                'support_resistance': [],
                'trend_patterns': [],
                'volume_patterns': [],
                'fibonacci_levels': [],
                'pattern_summary': {}
            }

            # 1. Chart Pattern Recognition
            pattern_results['chart_patterns'] = self._detect_chart_patterns(df)

            # 2. Candlestick Pattern Recognition
            pattern_results['candlestick_patterns'] = self._detect_candlestick_patterns(df)

            # 3. Support and Resistance Levels (Enhanced with live data)
            pattern_results['support_resistance'] = self._detect_support_resistance(df, symbol)

            # 4. Trend Pattern Analysis
            pattern_results['trend_patterns'] = self._analyze_trend_patterns(df)

            # 5. Volume Pattern Analysis
            pattern_results['volume_patterns'] = self._analyze_volume_patterns(df)

            # 6. Fibonacci Retracement Levels
            pattern_results['fibonacci_levels'] = self._calculate_fibonacci_levels(df)

            # 7. Pattern Summary
            pattern_results['pattern_summary'] = self._create_pattern_summary(pattern_results)

            logger.info("Pattern analysis completed successfully")
            return pattern_results

        except Exception as e:
            logger.error(f"Error in pattern analysis: {str(e)}")
            return self._create_empty_pattern_result()

    def _detect_chart_patterns(self, df: pd.DataFrame) -> List[PatternSignal]:
        """Detect major chart patterns using AI techniques"""

        patterns = []

        try:
            # Head and Shoulders pattern
            head_shoulders = self._detect_head_and_shoulders(df)
            if head_shoulders:
                patterns.append(head_shoulders)

            # Double Top/Bottom patterns
            double_patterns = self._detect_double_patterns(df)
            patterns.extend(double_patterns)

            # Triangle patterns
            triangle_patterns = self._detect_triangle_patterns(df)
            patterns.extend(triangle_patterns)

            # Flag and Pennant patterns
            flag_patterns = self._detect_flag_patterns(df)
            patterns.extend(flag_patterns)

            # Cup and Handle pattern
            cup_handle = self._detect_cup_and_handle(df)
            if cup_handle:
                patterns.append(cup_handle)

        except Exception as e:
            logger.error(f"Error detecting chart patterns: {str(e)}")

        return patterns

    def _detect_head_and_shoulders(self, df: pd.DataFrame) -> Optional[PatternSignal]:
        """Detect Head and Shoulders pattern"""

        try:
            if len(df) < 20:
                return None

            # Use closing prices for pattern detection
            prices = df['Close'].values

            # Find local maxima (potential shoulders and head)
            peaks, _ = signal.find_peaks(prices, distance=5, prominence=np.std(prices) * 0.5)

            if len(peaks) < 3:
                return None

            # Look for head and shoulders pattern in recent peaks
            for i in range(len(peaks) - 2):
                left_shoulder = peaks[i]
                head = peaks[i + 1]
                right_shoulder = peaks[i + 2]

                # Check if middle peak is highest (head)
                if (prices[head] > prices[left_shoulder] and
                    prices[head] > prices[right_shoulder] and
                    abs(prices[left_shoulder] - prices[right_shoulder]) / prices[head] < 0.05):  # Shoulders similar height

                    # Calculate neckline
                    neckline_level = min(prices[left_shoulder:head].min(), prices[head:right_shoulder].min())

                    # Calculate target and stop loss
                    head_height = prices[head] - neckline_level
                    target_price = neckline_level - head_height  # Bearish target
                    stop_loss = prices[head] * 1.02  # 2% above head

                    return PatternSignal(
                        pattern_name="Head and Shoulders",
                        pattern_type="BEARISH",
                        confidence=0.75,
                        strength=0.8,
                        start_date=df.index[left_shoulder],
                        end_date=df.index[right_shoulder],
                        entry_price=neckline_level,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        risk_reward_ratio=abs(target_price - neckline_level) / abs(stop_loss - neckline_level),
                        pattern_description="Classic bearish reversal pattern with head higher than shoulders",
                        key_levels=[neckline_level, prices[head]],
                        volume_confirmation=self._check_volume_confirmation(df, left_shoulder, right_shoulder),
                        timestamp=datetime.now()
                    )

            return None

        except Exception as e:
            logger.error(f"Error detecting head and shoulders: {str(e)}")
            return None

    def _detect_double_patterns(self, df: pd.DataFrame) -> List[PatternSignal]:
        """Detect Double Top and Double Bottom patterns"""

        patterns = []

        try:
            prices = df['Close'].values

            # Find peaks and troughs
            peaks, _ = signal.find_peaks(prices, distance=10, prominence=np.std(prices) * 0.3)
            troughs, _ = signal.find_peaks(-prices, distance=10, prominence=np.std(prices) * 0.3)

            # Double Top detection
            for i in range(len(peaks) - 1):
                peak1, peak2 = peaks[i], peaks[i + 1]

                # Check if peaks are similar height
                if abs(prices[peak1] - prices[peak2]) / max(prices[peak1], prices[peak2]) < 0.03:

                    # Find valley between peaks
                    valley_idx = np.argmin(prices[peak1:peak2]) + peak1
                    valley_price = prices[valley_idx]

                    # Calculate target and stop loss
                    peak_height = max(prices[peak1], prices[peak2]) - valley_price
                    target_price = valley_price - peak_height
                    stop_loss = max(prices[peak1], prices[peak2]) * 1.02

                    patterns.append(PatternSignal(
                        pattern_name="Double Top",
                        pattern_type="BEARISH",
                        confidence=0.70,
                        strength=0.75,
                        start_date=df.index[peak1],
                        end_date=df.index[peak2],
                        entry_price=valley_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        risk_reward_ratio=abs(target_price - valley_price) / abs(stop_loss - valley_price),
                        pattern_description="Bearish reversal pattern with two similar peaks",
                        key_levels=[valley_price, max(prices[peak1], prices[peak2])],
                        volume_confirmation=self._check_volume_confirmation(df, peak1, peak2),
                        timestamp=datetime.now()
                    ))

            # Double Bottom detection
            for i in range(len(troughs) - 1):
                trough1, trough2 = troughs[i], troughs[i + 1]

                # Check if troughs are similar depth
                if abs(prices[trough1] - prices[trough2]) / min(prices[trough1], prices[trough2]) < 0.03:

                    # Find peak between troughs
                    peak_idx = np.argmax(prices[trough1:trough2]) + trough1
                    peak_price = prices[peak_idx]

                    # Calculate target and stop loss
                    trough_depth = peak_price - min(prices[trough1], prices[trough2])
                    target_price = peak_price + trough_depth
                    stop_loss = min(prices[trough1], prices[trough2]) * 0.98

                    patterns.append(PatternSignal(
                        pattern_name="Double Bottom",
                        pattern_type="BULLISH",
                        confidence=0.70,
                        strength=0.75,
                        start_date=df.index[trough1],
                        end_date=df.index[trough2],
                        entry_price=peak_price,
                        target_price=target_price,
                        stop_loss=stop_loss,
                        risk_reward_ratio=abs(target_price - peak_price) / abs(stop_loss - peak_price),
                        pattern_description="Bullish reversal pattern with two similar troughs",
                        key_levels=[min(prices[trough1], prices[trough2]), peak_price],
                        volume_confirmation=self._check_volume_confirmation(df, trough1, trough2),
                        timestamp=datetime.now()
                    ))

        except Exception as e:
            logger.error(f"Error detecting double patterns: {str(e)}")

        return patterns

    def _detect_triangle_patterns(self, df: pd.DataFrame) -> List[PatternSignal]:
        """Detect Triangle patterns (Ascending, Descending, Symmetrical)"""

        patterns = []

        try:
            if len(df) < 20:
                return patterns

            prices = df['Close'].values

            # Find recent price action for triangle detection
            recent_data = df.tail(30)
            recent_prices = recent_data['Close'].values

            # Calculate trend lines
            highs = []
            lows = []

            for i in range(2, len(recent_prices) - 2):
                # Local high
                if (recent_prices[i] > recent_prices[i-1] and
                    recent_prices[i] > recent_prices[i+1] and
                    recent_prices[i] > recent_prices[i-2] and
                    recent_prices[i] > recent_prices[i+2]):
                    highs.append((i, recent_prices[i]))

                # Local low
                if (recent_prices[i] < recent_prices[i-1] and
                    recent_prices[i] < recent_prices[i+1] and
                    recent_prices[i] < recent_prices[i-2] and
                    recent_prices[i] < recent_prices[i+2]):
                    lows.append((i, recent_prices[i]))

            if len(highs) >= 2 and len(lows) >= 2:
                # Calculate trend line slopes
                high_slope = self._calculate_trend_slope([h[1] for h in highs])
                low_slope = self._calculate_trend_slope([l[1] for l in lows])

                # Determine triangle type
                if abs(high_slope) < 0.001 and low_slope > 0.001:  # Ascending triangle
                    pattern_type = "BULLISH"
                    pattern_name = "Ascending Triangle"
                    confidence = 0.65
                elif high_slope < -0.001 and abs(low_slope) < 0.001:  # Descending triangle
                    pattern_type = "BEARISH"
                    pattern_name = "Descending Triangle"
                    confidence = 0.65
                elif high_slope < -0.001 and low_slope > 0.001:  # Symmetrical triangle
                    pattern_type = "NEUTRAL"
                    pattern_name = "Symmetrical Triangle"
                    confidence = 0.60
                else:
                    return patterns

                # Calculate breakout levels
                resistance_level = max([h[1] for h in highs])
                support_level = min([l[1] for l in lows])
                current_price = recent_prices[-1]

                # Calculate targets
                triangle_height = resistance_level - support_level
                if pattern_type == "BULLISH":
                    target_price = resistance_level + triangle_height
                    stop_loss = support_level * 0.98
                    entry_price = resistance_level * 1.01  # Breakout above resistance
                elif pattern_type == "BEARISH":
                    target_price = support_level - triangle_height
                    stop_loss = resistance_level * 1.02
                    entry_price = support_level * 0.99  # Breakdown below support
                else:  # Symmetrical
                    if current_price > (resistance_level + support_level) / 2:
                        target_price = resistance_level + triangle_height
                        entry_price = resistance_level * 1.01
                        stop_loss = support_level * 0.98
                    else:
                        target_price = support_level - triangle_height
                        entry_price = support_level * 0.99
                        stop_loss = resistance_level * 1.02

                patterns.append(PatternSignal(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    confidence=confidence,
                    strength=0.7,
                    start_date=recent_data.index[0],
                    end_date=recent_data.index[-1],
                    entry_price=entry_price,
                    target_price=target_price,
                    stop_loss=stop_loss,
                    risk_reward_ratio=abs(target_price - entry_price) / abs(stop_loss - entry_price),
                    pattern_description=f"{pattern_name} pattern indicating potential breakout",
                    key_levels=[support_level, resistance_level],
                    volume_confirmation=True,  # Simplified for now
                    timestamp=datetime.now()
                ))

        except Exception as e:
            logger.error(f"Error detecting triangle patterns: {str(e)}")

        return patterns

    def _detect_flag_patterns(self, df: pd.DataFrame) -> List[PatternSignal]:
        """Detect Flag and Pennant patterns"""

        patterns = []

        try:
            if len(df) < 15:
                return patterns

            prices = df['Close'].values
            volumes = df['Volume'].values

            # Look for strong price moves followed by consolidation
            for i in range(10, len(prices) - 5):
                # Check for strong move (flagpole)
                move_start = i - 10
                move_end = i

                price_change = (prices[move_end] - prices[move_start]) / prices[move_start]

                if abs(price_change) > 0.05:  # 5% move
                    # Check for consolidation (flag)
                    consolidation_prices = prices[move_end:move_end + 5]

                    if len(consolidation_prices) >= 5:
                        consolidation_volatility = np.std(consolidation_prices) / np.mean(consolidation_prices)

                        if consolidation_volatility < 0.02:  # Low volatility consolidation
                            pattern_type = "BULLISH" if price_change > 0 else "BEARISH"
                            pattern_name = "Bull Flag" if price_change > 0 else "Bear Flag"

                            # Calculate targets
                            flagpole_height = abs(prices[move_end] - prices[move_start])
                            current_price = prices[move_end + 4]

                            if pattern_type == "BULLISH":
                                target_price = current_price + flagpole_height
                                stop_loss = min(consolidation_prices) * 0.98
                            else:
                                target_price = current_price - flagpole_height
                                stop_loss = max(consolidation_prices) * 1.02

                            patterns.append(PatternSignal(
                                pattern_name=pattern_name,
                                pattern_type=pattern_type,
                                confidence=0.68,
                                strength=0.75,
                                start_date=df.index[move_start],
                                end_date=df.index[move_end + 4],
                                entry_price=current_price,
                                target_price=target_price,
                                stop_loss=stop_loss,
                                risk_reward_ratio=abs(target_price - current_price) / abs(stop_loss - current_price),
                                pattern_description=f"{pattern_name} continuation pattern",
                                key_levels=[min(consolidation_prices), max(consolidation_prices)],
                                volume_confirmation=np.mean(volumes[move_start:move_end]) > np.mean(volumes[move_end:move_end + 5]),
                                timestamp=datetime.now()
                            ))

        except Exception as e:
            logger.error(f"Error detecting flag patterns: {str(e)}")

        return patterns

    def _detect_cup_and_handle(self, df: pd.DataFrame) -> Optional[PatternSignal]:
        """Detect Cup and Handle pattern"""

        try:
            if len(df) < 30:
                return None

            prices = df['Close'].values

            # Look for cup formation (U-shaped recovery)
            for i in range(20, len(prices) - 10):
                cup_start = i - 20
                cup_bottom = i - 10
                cup_end = i

                # Check for cup shape
                if (prices[cup_start] > prices[cup_bottom] and
                    prices[cup_end] > prices[cup_bottom] and
                    abs(prices[cup_start] - prices[cup_end]) / prices[cup_start] < 0.05):

                    # Look for handle formation
                    handle_start = cup_end
                    handle_end = min(handle_start + 10, len(prices) - 1)

                    handle_prices = prices[handle_start:handle_end]

                    if len(handle_prices) >= 5:
                        # Handle should be a small pullback
                        handle_pullback = (max(handle_prices) - min(handle_prices)) / max(handle_prices)

                        if 0.02 < handle_pullback < 0.15:  # 2-15% pullback
                            # Calculate targets
                            cup_depth = max(prices[cup_start], prices[cup_end]) - prices[cup_bottom]
                            breakout_level = max(handle_prices)
                            target_price = breakout_level + cup_depth
                            stop_loss = min(handle_prices) * 0.95

                            return PatternSignal(
                                pattern_name="Cup and Handle",
                                pattern_type="BULLISH",
                                confidence=0.72,
                                strength=0.8,
                                start_date=df.index[cup_start],
                                end_date=df.index[handle_end],
                                entry_price=breakout_level * 1.01,
                                target_price=target_price,
                                stop_loss=stop_loss,
                                risk_reward_ratio=abs(target_price - breakout_level) / abs(stop_loss - breakout_level),
                                pattern_description="Bullish continuation pattern with cup and handle formation",
                                key_levels=[prices[cup_bottom], breakout_level],
                                volume_confirmation=True,  # Simplified
                                timestamp=datetime.now()
                            )

            return None

        except Exception as e:
            logger.error(f"Error detecting cup and handle: {str(e)}")
            return None

    def _detect_candlestick_patterns(self, df: pd.DataFrame) -> List[CandlestickPattern]:
        """Detect candlestick patterns"""

        patterns = []

        try:
            if len(df) < 3:
                return patterns

            # Get OHLC data
            opens = df['Open'].values
            highs = df['High'].values
            lows = df['Low'].values
            closes = df['Close'].values

            # Detect various candlestick patterns
            for i in range(2, len(df)):
                # Doji pattern
                if self._is_doji(opens[i], highs[i], lows[i], closes[i]):
                    patterns.append(CandlestickPattern(
                        pattern_name="Doji",
                        pattern_type="REVERSAL",
                        confidence=0.6,
                        candle_indices=[i],
                        description="Indecision candle indicating potential reversal",
                        reliability="MEDIUM"
                    ))

                # Hammer pattern
                if self._is_hammer(opens[i], highs[i], lows[i], closes[i]):
                    patterns.append(CandlestickPattern(
                        pattern_name="Hammer",
                        pattern_type="BULLISH",
                        confidence=0.75,
                        candle_indices=[i],
                        description="Bullish reversal pattern with long lower shadow",
                        reliability="HIGH"
                    ))

                # Shooting Star pattern
                if self._is_shooting_star(opens[i], highs[i], lows[i], closes[i]):
                    patterns.append(CandlestickPattern(
                        pattern_name="Shooting Star",
                        pattern_type="BEARISH",
                        confidence=0.75,
                        candle_indices=[i],
                        description="Bearish reversal pattern with long upper shadow",
                        reliability="HIGH"
                    ))

                # Engulfing patterns (requires previous candle)
                if i > 0:
                    if self._is_bullish_engulfing(opens[i-1:i+1], highs[i-1:i+1],
                                                lows[i-1:i+1], closes[i-1:i+1]):
                        patterns.append(CandlestickPattern(
                            pattern_name="Bullish Engulfing",
                            pattern_type="BULLISH",
                            confidence=0.8,
                            candle_indices=[i-1, i],
                            description="Strong bullish reversal pattern",
                            reliability="HIGH"
                        ))

                    if self._is_bearish_engulfing(opens[i-1:i+1], highs[i-1:i+1],
                                                lows[i-1:i+1], closes[i-1:i+1]):
                        patterns.append(CandlestickPattern(
                            pattern_name="Bearish Engulfing",
                            pattern_type="BEARISH",
                            confidence=0.8,
                            candle_indices=[i-1, i],
                            description="Strong bearish reversal pattern",
                            reliability="HIGH"
                        ))

                # Three-candle patterns
                if i >= 2:
                    if self._is_morning_star(opens[i-2:i+1], highs[i-2:i+1],
                                           lows[i-2:i+1], closes[i-2:i+1]):
                        patterns.append(CandlestickPattern(
                            pattern_name="Morning Star",
                            pattern_type="BULLISH",
                            confidence=0.85,
                            candle_indices=[i-2, i-1, i],
                            description="Strong bullish reversal pattern",
                            reliability="HIGH"
                        ))

                    if self._is_evening_star(opens[i-2:i+1], highs[i-2:i+1],
                                           lows[i-2:i+1], closes[i-2:i+1]):
                        patterns.append(CandlestickPattern(
                            pattern_name="Evening Star",
                            pattern_type="BEARISH",
                            confidence=0.85,
                            candle_indices=[i-2, i-1, i],
                            description="Strong bearish reversal pattern",
                            reliability="HIGH"
                        ))

        except Exception as e:
            logger.error(f"Error detecting candlestick patterns: {str(e)}")

        return patterns

    def _detect_support_resistance(self, df: pd.DataFrame, symbol: str = None) -> List[Dict]:
        """Enhanced support/resistance detection with live data integration"""

        levels = []

        try:
            prices = df['Close'].values
            highs = df['High'].values
            lows = df['Low'].values
            current_price = prices[-1]

            # Try to get live data for more accurate current price and pivot levels
            live_data = None
            if symbol and self.check_api_server_status():
                live_data = self.fetch_live_data_from_api(symbol)
                if live_data:
                    current_price = live_data['current_price']
                    self.logger.info(f"Using live price {current_price:.2f} EGP from API for {symbol}")
                else:
                    self.logger.info(f"Using CSV price {current_price:.2f} EGP for {symbol}")
            else:
                self.logger.info(f"API not available, using CSV price {current_price:.2f} EGP for {symbol}")

            # Get price statistics for validation
            price_std = np.std(prices)
            price_range = np.max(prices) - np.min(prices)

            # Use recent data for more relevant levels (last 50 periods)
            recent_periods = min(50, len(prices))
            recent_prices = prices[-recent_periods:]
            recent_highs = highs[-recent_periods:]
            recent_lows = lows[-recent_periods:]

            # Calculate pivot levels - use live data if available
            if live_data and live_data.get('pivots'):
                # Use live pivot data from TradingView API
                pivots = live_data['pivots']
                self.logger.info(f"Using live pivot data from API for {symbol}")

                # Handle different pivot data structures
                if isinstance(pivots, list) and len(pivots) > 0:
                    # If pivots is a list, take the first element
                    pivot_data = pivots[0] if isinstance(pivots[0], dict) else {}
                elif isinstance(pivots, dict):
                    # If pivots is already a dict
                    pivot_data = pivots
                else:
                    # Fallback to empty dict
                    pivot_data = {}

                # Extract pivot levels from API data
                r1 = pivot_data.get('R1', 0)
                r2 = pivot_data.get('R2', 0)
                r3 = pivot_data.get('R3', 0)
                s1 = pivot_data.get('S1', 0)
                s2 = pivot_data.get('S2', 0)
                s3 = pivot_data.get('S3', 0)
                pivot_point = pivot_data.get('PP', current_price)

                # Convert from piasters to EGP if needed
                if r1 > 1000:  # Likely in piasters
                    r1, r2, r3 = r1/1000, r2/1000, r3/1000
                    s1, s2, s3 = s1/1000, s2/1000, s3/1000
                    pivot_point = pivot_point/1000

                # If no valid pivot data found, fall back to calculation
                if not any([r1, r2, r3, s1, s2, s3]):
                    self.logger.info(f"No valid pivot data found, falling back to calculation for {symbol}")
                    live_data = None  # Force fallback to calculation

            else:
                # Calculate traditional pivot levels using recent data
                recent_high = np.max(recent_highs)
                recent_low = np.min(recent_lows)
                pivot_point = (recent_high + recent_low + current_price) / 3

                # Calculate traditional pivot levels with realistic constraints
                r1 = 2 * pivot_point - recent_low
                r2 = pivot_point + (recent_high - recent_low)
                r3 = recent_high + 2 * (pivot_point - recent_low)
                s1 = 2 * pivot_point - recent_high
                s2 = pivot_point - (recent_high - recent_low)
                s3 = recent_low - 2 * (recent_high - pivot_point)

                self.logger.info(f"Using calculated pivot levels for {symbol}")

            # Create pivot levels list with all calculated levels
            pivot_levels = [
                {'level': r3, 'type': 'resistance', 'base_strength': 0.9, 'method': 'live_pivot' if live_data and live_data.get('pivots') else 'calculated_pivot'},
                {'level': r2, 'type': 'resistance', 'base_strength': 0.8, 'method': 'live_pivot' if live_data and live_data.get('pivots') else 'calculated_pivot'},
                {'level': r1, 'type': 'resistance', 'base_strength': 0.7, 'method': 'live_pivot' if live_data and live_data.get('pivots') else 'calculated_pivot'},
                {'level': s1, 'type': 'support', 'base_strength': 0.7, 'method': 'live_pivot' if live_data and live_data.get('pivots') else 'calculated_pivot'},
                {'level': s2, 'type': 'support', 'base_strength': 0.8, 'method': 'live_pivot' if live_data and live_data.get('pivots') else 'calculated_pivot'},
                {'level': s3, 'type': 'support', 'base_strength': 0.9, 'method': 'live_pivot' if live_data and live_data.get('pivots') else 'calculated_pivot'},
            ]

            for pivot_level in pivot_levels:
                level_price = pivot_level['level']
                level_type = pivot_level['type']

                # Validate level is in correct position and reasonable range
                distance_pct = abs(level_price - current_price) / current_price

                if (distance_pct <= 0.15 and  # Within 15% of current price
                    ((level_type == 'resistance' and level_price > current_price * 1.005) or
                     (level_type == 'support' and level_price < current_price * 0.995))):

                    # Count actual touches with more realistic threshold
                    if level_type == 'resistance':
                        touches = np.sum(np.abs(recent_highs - level_price) / level_price < 0.015)  # 1.5%
                        close_touches = np.sum(np.abs(recent_prices - level_price) / level_price < 0.015)
                    else:
                        touches = np.sum(np.abs(recent_lows - level_price) / level_price < 0.015)  # 1.5%
                        close_touches = np.sum(np.abs(recent_prices - level_price) / level_price < 0.015)

                    total_touches = max(touches, close_touches)

                    # Calculate realistic strength (0-100%)
                    base_strength = pivot_level['base_strength']
                    touch_bonus = min(total_touches * 0.05, 0.2)  # Max 20% bonus
                    proximity_bonus = max(0, (0.15 - distance_pct) / 0.15 * 0.1)  # Closer = stronger

                    final_strength = min(base_strength + touch_bonus + proximity_bonus, 1.0)

                    levels.append({
                        'type': level_type,
                        'level': level_price,
                        'strength': final_strength,
                        'touches': max(int(total_touches), 1),
                        'last_test': df.index[-1],
                        'method': 'pivot'
                    })

            # Find significant historical levels using improved peak/trough detection
            # Use adaptive parameters based on data characteristics
            min_distance = max(3, len(recent_prices) // 15)  # Adaptive distance
            prominence_threshold = price_std * 0.4  # More conservative

            peaks, peak_properties = signal.find_peaks(
                recent_prices,
                distance=min_distance,
                prominence=prominence_threshold,
                height=np.percentile(recent_prices, 60)  # Only significant peaks
            )

            troughs, trough_properties = signal.find_peaks(
                -recent_prices,
                distance=min_distance,
                prominence=prominence_threshold,
                height=-np.percentile(recent_prices, 40)  # Only significant troughs
            )

            # Process historical resistance levels
            for i, peak_idx in enumerate(peaks):
                actual_idx = len(prices) - recent_periods + peak_idx  # Convert to full array index
                resistance_price = max(recent_prices[peak_idx], recent_highs[peak_idx])

                # Only consider if above current price and within reasonable range
                distance_pct = (resistance_price - current_price) / current_price

                if 0.005 < distance_pct <= 0.12:  # 0.5% to 12% above current
                    # Count touches more accurately
                    touches = 0
                    for j in range(len(recent_prices)):
                        if abs(recent_highs[j] - resistance_price) / resistance_price < 0.012:  # 1.2%
                            touches += 1
                        elif abs(recent_prices[j] - resistance_price) / resistance_price < 0.012:
                            touches += 0.5  # Partial credit for close prices

                    if touches >= 1.5:  # At least 1.5 touches
                        # Calculate strength based on touches and prominence
                        prominence = peak_properties['prominences'][i] if i < len(peak_properties['prominences']) else price_std

                        base_strength = 0.5
                        touch_strength = min(touches * 0.1, 0.3)
                        prominence_strength = min(prominence / (price_std * 2), 0.2)

                        final_strength = min(base_strength + touch_strength + prominence_strength, 1.0)

                        levels.append({
                            'type': 'resistance',
                            'level': resistance_price,
                            'strength': final_strength,
                            'touches': int(touches),
                            'last_test': df.index[actual_idx] if actual_idx < len(df.index) else df.index[-1],
                            'method': 'historical'
                        })

            # Process historical support levels
            for i, trough_idx in enumerate(troughs):
                actual_idx = len(prices) - recent_periods + trough_idx
                support_price = min(recent_prices[trough_idx], recent_lows[trough_idx])

                # Only consider if below current price and within reasonable range
                distance_pct = (current_price - support_price) / current_price

                if 0.005 < distance_pct <= 0.12:  # 0.5% to 12% below current
                    # Count touches more accurately
                    touches = 0
                    for j in range(len(recent_prices)):
                        if abs(recent_lows[j] - support_price) / support_price < 0.012:  # 1.2%
                            touches += 1
                        elif abs(recent_prices[j] - support_price) / support_price < 0.012:
                            touches += 0.5  # Partial credit for close prices

                    if touches >= 1.5:  # At least 1.5 touches
                        # Calculate strength based on touches and prominence
                        prominence = trough_properties['prominences'][i] if i < len(trough_properties['prominences']) else price_std

                        base_strength = 0.5
                        touch_strength = min(touches * 0.1, 0.3)
                        prominence_strength = min(prominence / (price_std * 2), 0.2)

                        final_strength = min(base_strength + touch_strength + prominence_strength, 1.0)

                        levels.append({
                            'type': 'support',
                            'level': support_price,
                            'strength': final_strength,
                            'touches': int(touches),
                            'last_test': df.index[actual_idx] if actual_idx < len(df.index) else df.index[-1],
                            'method': 'historical'
                        })

            # Remove duplicates and filter by quality
            filtered_levels = []
            for level in levels:
                is_duplicate = False
                for existing in filtered_levels:
                    if (abs(level['level'] - existing['level']) / level['level'] < 0.008 and
                        level['type'] == existing['type']):  # Within 0.8%
                        # Keep the stronger level
                        if level['strength'] > existing['strength']:
                            filtered_levels.remove(existing)
                            filtered_levels.append(level)
                        is_duplicate = True
                        break

                if not is_duplicate and level['strength'] >= 0.4:  # Minimum quality threshold
                    filtered_levels.append(level)

            # Balance support and resistance levels
            support_levels = [l for l in filtered_levels if l['type'] == 'support']
            resistance_levels = [l for l in filtered_levels if l['type'] == 'resistance']

            # Sort by proximity to current price (most relevant first)
            support_levels.sort(key=lambda x: abs(current_price - x['level']))
            resistance_levels.sort(key=lambda x: abs(x['level'] - current_price))

            # Take balanced selection
            final_levels = []
            final_levels.extend(support_levels[:3])  # Top 3 support
            final_levels.extend(resistance_levels[:3])  # Top 3 resistance

            # Add calculated levels if we don't have enough quality levels
            if len(support_levels) < 2:
                for i, pct in enumerate([0.975, 0.95]):
                    support_level = current_price * pct
                    if support_level >= recent_low * 0.98:  # Within reasonable range
                        final_levels.append({
                            'type': 'support',
                            'level': support_level,
                            'strength': 0.45 - (i * 0.05),
                            'touches': 1,
                            'last_test': df.index[-1],
                            'method': 'calculated'
                        })

            if len(resistance_levels) < 2:
                for i, pct in enumerate([1.025, 1.05]):
                    resistance_level = current_price * pct
                    if resistance_level <= recent_high * 1.02:  # Within reasonable range
                        final_levels.append({
                            'type': 'resistance',
                            'level': resistance_level,
                            'strength': 0.45 - (i * 0.05),
                            'touches': 1,
                            'last_test': df.index[-1],
                            'method': 'calculated'
                        })

            # Final validation and sorting
            validated_levels = []
            for level in final_levels:
                # Strict validation with realistic constraints
                distance_pct = abs(level['level'] - current_price) / current_price

                if (distance_pct <= 0.15 and  # Within 15% range
                    ((level['type'] == 'support' and level['level'] < current_price) or
                     (level['type'] == 'resistance' and level['level'] > current_price))):
                    validated_levels.append(level)

            # Sort by strength and proximity (most important first)
            validated_levels.sort(key=lambda x: (x['strength'] * 0.7 + (1 - abs(current_price - x['level']) / current_price / 0.15) * 0.3), reverse=True)

        except Exception as e:
            logger.error(f"Error detecting support/resistance: {str(e)}")
            # Provide realistic fallback levels
            if len(df) > 0:
                current_price = df['Close'].iloc[-1]
                validated_levels = [
                    {
                        'type': 'resistance',
                        'level': current_price * 1.03,
                        'strength': 0.6,
                        'touches': 2,
                        'last_test': df.index[-1],
                        'method': 'fallback'
                    },
                    {
                        'type': 'support',
                        'level': current_price * 0.97,
                        'strength': 0.6,
                        'touches': 2,
                        'last_test': df.index[-1],
                        'method': 'fallback'
                    },
                    {
                        'type': 'resistance',
                        'level': current_price * 1.06,
                        'strength': 0.5,
                        'touches': 1,
                        'last_test': df.index[-1],
                        'method': 'fallback'
                    },
                    {
                        'type': 'support',
                        'level': current_price * 0.94,
                        'strength': 0.5,
                        'touches': 1,
                        'last_test': df.index[-1],
                        'method': 'fallback'
                    }
                ]

        return validated_levels[:6]  # Return top 6 levels for balanced display

    def _analyze_trend_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Analyze trend patterns and channels"""

        patterns = []

        try:
            if len(df) < 20:
                return patterns

            prices = df['Close'].values

            # Analyze different timeframes
            for window in [10, 20, 50]:
                if len(prices) >= window:
                    recent_prices = prices[-window:]

                    # Calculate trend slope
                    x = np.arange(len(recent_prices))
                    slope = np.polyfit(x, recent_prices, 1)[0]

                    # Determine trend strength
                    r_squared = np.corrcoef(x, recent_prices)[0, 1] ** 2

                    # Classify trend
                    if slope > 0 and r_squared > 0.7:
                        trend_type = "UPTREND"
                        strength = "STRONG" if r_squared > 0.85 else "MODERATE"
                    elif slope < 0 and r_squared > 0.7:
                        trend_type = "DOWNTREND"
                        strength = "STRONG" if r_squared > 0.85 else "MODERATE"
                    else:
                        trend_type = "SIDEWAYS"
                        strength = "WEAK"

                    patterns.append({
                        'timeframe': f"{window}_days",
                        'trend_type': trend_type,
                        'strength': strength,
                        'slope': slope,
                        'r_squared': r_squared,
                        'start_price': recent_prices[0],
                        'end_price': recent_prices[-1],
                        'price_change': (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                    })

        except Exception as e:
            logger.error(f"Error analyzing trend patterns: {str(e)}")

        return patterns

    def _analyze_volume_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Analyze volume patterns and anomalies"""

        patterns = []

        try:
            if 'Volume' not in df.columns or len(df) < 10:
                return patterns

            volumes = df['Volume'].values
            prices = df['Close'].values

            # Calculate volume moving average
            volume_ma = np.convolve(volumes, np.ones(10)/10, mode='valid')

            # Find volume spikes
            for i in range(len(volume_ma)):
                if i + 9 < len(volumes):  # Ensure we have enough data
                    current_volume = volumes[i + 9]
                    avg_volume = volume_ma[i]

                    if current_volume > avg_volume * 2:  # Volume spike
                        price_change = (prices[i + 9] - prices[i + 8]) / prices[i + 8] if i + 8 >= 0 else 0

                        patterns.append({
                            'type': 'volume_spike',
                            'date': df.index[i + 9],
                            'volume_ratio': current_volume / avg_volume,
                            'price_change': price_change,
                            'significance': 'HIGH' if current_volume > avg_volume * 3 else 'MEDIUM'
                        })

            # Volume trend analysis
            if len(volumes) >= 20:
                recent_volume_trend = np.polyfit(range(20), volumes[-20:], 1)[0]

                patterns.append({
                    'type': 'volume_trend',
                    'trend': 'INCREASING' if recent_volume_trend > 0 else 'DECREASING',
                    'slope': recent_volume_trend,
                    'timeframe': '20_days'
                })

        except Exception as e:
            logger.error(f"Error analyzing volume patterns: {str(e)}")

        return patterns

    def _calculate_fibonacci_levels(self, df: pd.DataFrame) -> List[Dict]:
        """Calculate Fibonacci retracement levels"""

        levels = []

        try:
            if len(df) < 20:
                return levels

            prices = df['Close'].values

            # Find significant high and low for Fibonacci calculation
            recent_high = np.max(prices[-50:]) if len(prices) >= 50 else np.max(prices)
            recent_low = np.min(prices[-50:]) if len(prices) >= 50 else np.min(prices)

            # Calculate Fibonacci levels
            fib_ratios = [0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0]

            for ratio in fib_ratios:
                if recent_high > recent_low:
                    # Uptrend retracement
                    level = recent_high - (recent_high - recent_low) * ratio
                    level_type = "retracement"
                else:
                    # Downtrend extension
                    level = recent_low + (recent_high - recent_low) * ratio
                    level_type = "extension"

                levels.append({
                    'ratio': ratio,
                    'level': level,
                    'type': level_type,
                    'high': recent_high,
                    'low': recent_low
                })

        except Exception as e:
            logger.error(f"Error calculating Fibonacci levels: {str(e)}")

        return levels

    # Helper methods for candlestick pattern detection
    def _is_doji(self, open_price: float, high: float, low: float, close: float) -> bool:
        """Check if candle is a Doji"""
        body_size = abs(close - open_price)
        total_range = high - low
        return body_size / total_range < 0.1 if total_range > 0 else False

    def _is_hammer(self, open_price: float, high: float, low: float, close: float) -> bool:
        """Check if candle is a Hammer"""
        body_size = abs(close - open_price)
        lower_shadow = min(open_price, close) - low
        upper_shadow = high - max(open_price, close)
        total_range = high - low

        if total_range == 0:
            return False

        return (lower_shadow > body_size * 2 and
                upper_shadow < body_size * 0.5 and
                body_size / total_range > 0.1)

    def _is_shooting_star(self, open_price: float, high: float, low: float, close: float) -> bool:
        """Check if candle is a Shooting Star"""
        body_size = abs(close - open_price)
        lower_shadow = min(open_price, close) - low
        upper_shadow = high - max(open_price, close)
        total_range = high - low

        if total_range == 0:
            return False

        return (upper_shadow > body_size * 2 and
                lower_shadow < body_size * 0.5 and
                body_size / total_range > 0.1)

    def _is_bullish_engulfing(self, opens: np.ndarray, highs: np.ndarray,
                            lows: np.ndarray, closes: np.ndarray) -> bool:
        """Check if pattern is Bullish Engulfing"""
        if len(opens) < 2:
            return False

        # First candle should be bearish
        first_bearish = closes[0] < opens[0]

        # Second candle should be bullish and engulf first
        second_bullish = closes[1] > opens[1]
        engulfs = opens[1] < closes[0] and closes[1] > opens[0]

        return first_bearish and second_bullish and engulfs

    def _is_bearish_engulfing(self, opens: np.ndarray, highs: np.ndarray,
                            lows: np.ndarray, closes: np.ndarray) -> bool:
        """Check if pattern is Bearish Engulfing"""
        if len(opens) < 2:
            return False

        # First candle should be bullish
        first_bullish = closes[0] > opens[0]

        # Second candle should be bearish and engulf first
        second_bearish = closes[1] < opens[1]
        engulfs = opens[1] > closes[0] and closes[1] < opens[0]

        return first_bullish and second_bearish and engulfs

    def _is_morning_star(self, opens: np.ndarray, highs: np.ndarray,
                       lows: np.ndarray, closes: np.ndarray) -> bool:
        """Check if pattern is Morning Star"""
        if len(opens) < 3:
            return False

        # First candle: bearish
        first_bearish = closes[0] < opens[0]

        # Second candle: small body (star)
        star_body = abs(closes[1] - opens[1])
        star_small = star_body < abs(closes[0] - opens[0]) * 0.5

        # Third candle: bullish and closes above first candle's midpoint
        third_bullish = closes[2] > opens[2]
        closes_high = closes[2] > (opens[0] + closes[0]) / 2

        return first_bearish and star_small and third_bullish and closes_high

    def _is_evening_star(self, opens: np.ndarray, highs: np.ndarray,
                       lows: np.ndarray, closes: np.ndarray) -> bool:
        """Check if pattern is Evening Star"""
        if len(opens) < 3:
            return False

        # First candle: bullish
        first_bullish = closes[0] > opens[0]

        # Second candle: small body (star)
        star_body = abs(closes[1] - opens[1])
        star_small = star_body < abs(closes[0] - opens[0]) * 0.5

        # Third candle: bearish and closes below first candle's midpoint
        third_bearish = closes[2] < opens[2]
        closes_low = closes[2] < (opens[0] + closes[0]) / 2

        return first_bullish and star_small and third_bearish and closes_low

    def _calculate_trend_slope(self, prices: List[float]) -> float:
        """Calculate trend line slope"""
        if len(prices) < 2:
            return 0.0

        x = np.arange(len(prices))
        slope = np.polyfit(x, prices, 1)[0]
        return slope

    def _check_volume_confirmation(self, df: pd.DataFrame, start_idx: int, end_idx: int) -> bool:
        """Check if volume confirms the pattern"""
        try:
            if 'Volume' not in df.columns:
                return False

            pattern_volume = df['Volume'].iloc[start_idx:end_idx].mean()
            avg_volume = df['Volume'].mean()

            return pattern_volume > avg_volume * 1.2  # 20% above average
        except:
            return False

    def _create_pattern_summary(self, pattern_results: Dict) -> Dict:
        """Create summary of all detected patterns"""

        summary = {
            'total_patterns': 0,
            'bullish_patterns': 0,
            'bearish_patterns': 0,
            'neutral_patterns': 0,
            'high_confidence_patterns': 0,
            'pattern_types': {},
            'strongest_signals': [],
            'key_levels': [],
            'overall_sentiment': 'NEUTRAL'
        }

        try:
            # Count chart patterns
            chart_patterns = pattern_results.get('chart_patterns', [])
            for pattern in chart_patterns:
                summary['total_patterns'] += 1

                if pattern.pattern_type == 'BULLISH':
                    summary['bullish_patterns'] += 1
                elif pattern.pattern_type == 'BEARISH':
                    summary['bearish_patterns'] += 1
                else:
                    summary['neutral_patterns'] += 1

                if pattern.confidence > 0.7:
                    summary['high_confidence_patterns'] += 1

                # Track pattern types
                pattern_type = pattern.pattern_name
                summary['pattern_types'][pattern_type] = summary['pattern_types'].get(pattern_type, 0) + 1

                # Add to strongest signals if high confidence
                if pattern.confidence > 0.7:
                    summary['strongest_signals'].append({
                        'pattern': pattern.pattern_name,
                        'type': pattern.pattern_type,
                        'confidence': pattern.confidence,
                        'target': pattern.target_price
                    })

            # Count candlestick patterns
            candlestick_patterns = pattern_results.get('candlestick_patterns', [])
            for pattern in candlestick_patterns:
                summary['total_patterns'] += 1

                if pattern.pattern_type == 'BULLISH':
                    summary['bullish_patterns'] += 1
                elif pattern.pattern_type == 'BEARISH':
                    summary['bearish_patterns'] += 1

                if pattern.confidence > 0.7:
                    summary['high_confidence_patterns'] += 1

            # Extract key levels
            support_resistance = pattern_results.get('support_resistance', [])
            for level in support_resistance[:5]:  # Top 5 levels
                summary['key_levels'].append({
                    'type': level['type'],
                    'level': level['level'],
                    'strength': level['strength']
                })

            # Determine overall sentiment
            if summary['bullish_patterns'] > summary['bearish_patterns'] * 1.5:
                summary['overall_sentiment'] = 'BULLISH'
            elif summary['bearish_patterns'] > summary['bullish_patterns'] * 1.5:
                summary['overall_sentiment'] = 'BEARISH'
            else:
                summary['overall_sentiment'] = 'NEUTRAL'

            # Sort strongest signals by confidence
            summary['strongest_signals'].sort(key=lambda x: x['confidence'], reverse=True)
            summary['strongest_signals'] = summary['strongest_signals'][:5]  # Top 5

        except Exception as e:
            logger.error(f"Error creating pattern summary: {str(e)}")

        return summary

    def _create_empty_pattern_result(self) -> Dict[str, List]:
        """Create empty pattern result as fallback"""

        return {
            'chart_patterns': [],
            'candlestick_patterns': [],
            'support_resistance': [],
            'trend_patterns': [],
            'volume_patterns': [],
            'fibonacci_levels': [],
            'pattern_summary': {
                'total_patterns': 0,
                'bullish_patterns': 0,
                'bearish_patterns': 0,
                'neutral_patterns': 0,
                'high_confidence_patterns': 0,
                'pattern_types': {},
                'strongest_signals': [],
                'key_levels': [],
                'overall_sentiment': 'NEUTRAL'
            }
        }

    def get_pattern_alerts(self, df: pd.DataFrame, alert_threshold: float = 0.7) -> List[Dict]:
        """Get high-confidence pattern alerts"""

        alerts = []

        try:
            pattern_results = self.analyze_patterns(df)

            # Check chart patterns for alerts
            for pattern in pattern_results['chart_patterns']:
                if pattern.confidence >= alert_threshold:
                    alerts.append({
                        'type': 'CHART_PATTERN',
                        'pattern': pattern.pattern_name,
                        'signal': pattern.pattern_type,
                        'confidence': pattern.confidence,
                        'entry_price': pattern.entry_price,
                        'target_price': pattern.target_price,
                        'stop_loss': pattern.stop_loss,
                        'message': f"{pattern.pattern_name} detected with {pattern.confidence:.1%} confidence",
                        'timestamp': datetime.now()
                    })

            # Check candlestick patterns for alerts
            for pattern in pattern_results['candlestick_patterns']:
                if pattern.confidence >= alert_threshold and pattern.reliability == 'HIGH':
                    alerts.append({
                        'type': 'CANDLESTICK_PATTERN',
                        'pattern': pattern.pattern_name,
                        'signal': pattern.pattern_type,
                        'confidence': pattern.confidence,
                        'message': f"{pattern.pattern_name} candlestick pattern detected",
                        'timestamp': datetime.now()
                    })

            # Check support/resistance breaks
            current_price = df['Close'].iloc[-1]
            for level in pattern_results['support_resistance']:
                if level['strength'] > 0.7:
                    price_diff = abs(current_price - level['level']) / level['level']
                    if price_diff < 0.02:  # Within 2% of key level
                        alerts.append({
                            'type': 'KEY_LEVEL',
                            'pattern': f"{level['type'].title()} Test",
                            'signal': 'NEUTRAL',
                            'confidence': level['strength'],
                            'level': level['level'],
                            'message': f"Price testing key {level['type']} at {level['level']:.2f}",
                            'timestamp': datetime.now()
                        })

        except Exception as e:
            logger.error(f"Error generating pattern alerts: {str(e)}")

        return alerts