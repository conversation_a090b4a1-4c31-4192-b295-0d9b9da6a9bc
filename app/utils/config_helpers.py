"""
Helper functions for working with configuration files and values.
"""

def clean_config_value(value):
    """
    Clean up a configuration value by removing quotes if present.

    Args:
        value (str): The configuration value to clean

    Returns:
        str: The cleaned value
    """
    if not isinstance(value, str):
        return value

    # Remove quotes if present
    if value.startswith('"') and value.endswith('"'):
        return value[1:-1]
    elif value.startswith("'") and value.endswith("'"):
        return value[1:-1]

    return value

def get_cleaned_config(config, section, key, fallback=""):
    """
    Get a configuration value and clean it by removing quotes if present.

    Args:
        config (ConfigParser): The configuration parser object
        section (str): The configuration section
        key (str): The configuration key
        fallback (str): The fallback value if the key is not found

    Returns:
        str: The cleaned configuration value
    """
    value = config.get(section, key, fallback=fallback)
    return clean_config_value(value)

def validate_ai_service_config(config, provider):
    """
    Validate the configuration for an AI service provider.

    Args:
        config (ConfigParser): The configuration parser object
        provider (str): The AI service provider name

    Returns:
        tuple: (is_valid, error_message)
    """
    provider = clean_config_value(provider)

    if provider == "none":
        return True, ""

    # Check common providers that need API keys
    if provider in ["openai", "azure", "anthropic", "custom"]:
        api_key = get_cleaned_config(config, provider, "api_key", fallback="")
        if not api_key:
            return False, f"API key for {provider} is not configured"

        # Check endpoint for Azure and custom
        if provider in ["azure", "custom"]:
            endpoint = get_cleaned_config(config, provider, "endpoint", fallback="")
            if not endpoint:
                return False, f"Endpoint for {provider} is not configured"

    # Check LangFlow configuration
    elif provider == "langflow":
        api_endpoint = get_cleaned_config(config, "langflow", "api_endpoint", fallback="")
        if not api_endpoint:
            return False, "LangFlow API endpoint is not configured"

        workflow_id = get_cleaned_config(config, "langflow", "workflow_id", fallback="")
        if not workflow_id:
            return False, "LangFlow workflow ID is not configured"

    # Check n8n configuration
    elif provider == "n8n":
        webhook_url = get_cleaned_config(config, "n8n", "webhook_url", fallback="")
        if not webhook_url:
            return False, "n8n webhook URL is not configured"

    # Mock provider (no configuration needed)
    elif provider == "mock":
        # Mock provider doesn't require any configuration
        return True, ""

    # Unknown provider
    else:
        return False, f"Unknown AI provider: {provider}"

    return True, ""
