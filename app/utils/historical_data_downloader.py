"""
Historical Data Downloader for AI Advisor
Downloads historical data from TradingView API server and saves to CSV
"""

import pandas as pd
import numpy as np
import os
import requests
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple
import time

# Configure logging
logger = logging.getLogger(__name__)

class HistoricalDataDownloader:
    """Downloads and manages historical data from TradingView API"""
    
    def __init__(self, api_base_url: str = "http://127.0.0.1:8000"):
        self.api_base_url = api_base_url
        self.data_dir = os.path.join("data", "stocks")

        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)
    
    def check_api_status(self) -> bool:
        """Check if TradingView API server is running"""
        try:
            response = requests.get(f"{self.api_base_url}/", timeout=5)
            logger.info(f"API status check: {response.status_code}")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"API status check failed: {str(e)}")
            return False

    def test_api_connection(self) -> Dict[str, any]:
        """Test API connection and return detailed status"""
        try:
            logger.info(f"Testing API connection to {self.api_base_url}")

            # Test basic connection
            response = requests.get(f"{self.api_base_url}/", timeout=10)

            result = {
                'connected': response.status_code == 200,
                'status_code': response.status_code,
                'url': self.api_base_url,
                'response_time': None,
                'error': None
            }

            if response.status_code == 200:
                logger.info("API server is running and accessible")
                result['message'] = "API server is running and accessible"
            else:
                logger.warning(f"API server returned status {response.status_code}")
                result['message'] = f"API server returned status {response.status_code}"

            return result

        except requests.exceptions.ConnectionError:
            error_msg = "Cannot connect to API server. Is it running?"
            logger.error(error_msg)
            return {
                'connected': False,
                'status_code': None,
                'url': self.api_base_url,
                'response_time': None,
                'error': 'ConnectionError',
                'message': error_msg
            }
        except requests.exceptions.Timeout:
            error_msg = "API server connection timeout"
            logger.error(error_msg)
            return {
                'connected': False,
                'status_code': None,
                'url': self.api_base_url,
                'response_time': None,
                'error': 'Timeout',
                'message': error_msg
            }
        except Exception as e:
            error_msg = f"API connection test failed: {str(e)}"
            logger.error(error_msg)
            return {
                'connected': False,
                'status_code': None,
                'url': self.api_base_url,
                'response_time': None,
                'error': str(e),
                'message': error_msg
            }
    
    def get_available_intervals(self) -> List[Dict]:
        """Get available intervals from API server"""
        try:
            response = requests.get(f"{self.api_base_url}/api/intervals", timeout=10)
            if response.status_code == 200:
                return response.json().get('intervals', [])
            return []
        except Exception as e:
            logger.error(f"Error getting intervals: {str(e)}")
            return []
    
    def download_historical_data(self, symbol: str, intervals: List[str] = None,
                                save_to_csv: bool = True) -> Optional[Dict]:
        """
        Download historical data for a symbol

        Args:
            symbol: Stock symbol (e.g., 'COMI')
            intervals: List of intervals to download (default: ['1D', '1W'])
            save_to_csv: Whether to save data to CSV file

        Returns:
            Dictionary with historical data for each interval
        """
        if intervals is None:
            intervals = ['1D', '1W']  # Default to daily and weekly

        try:
            # Format symbol for EGX
            egx_symbol = f"EGX-{symbol}"

            logger.info(f"Downloading historical data for {symbol} with intervals: {intervals}")
            logger.info(f"API URL: {self.api_base_url}/api/scrape_pairs")

            payload = {
                "pairs": [egx_symbol],
                "intervals": intervals
            }

            logger.info(f"Request payload: {payload}")

            response = requests.post(
                f"{self.api_base_url}/api/scrape_pairs",
                json=payload,
                timeout=120  # 2 minutes timeout for historical data
            )

            logger.info(f"API response status: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"API response structure: {result}")

                    data = result.get('data', {})
                    logger.info(f"Data keys: {list(data.keys())}")

                    if egx_symbol in data:
                        logger.info(f"Found data for {egx_symbol}: {data[egx_symbol]}")
                        historical_data = self._process_historical_data(symbol, data[egx_symbol])

                        if historical_data:
                            logger.info(f"Successfully processed historical data for {symbol}")
                            if save_to_csv:
                                self._save_to_csv(symbol, historical_data)
                            return historical_data
                        else:
                            logger.error(f"Failed to process historical data for {symbol}")
                            return None
                    else:
                        logger.warning(f"No data returned for {egx_symbol}. Available keys: {list(data.keys())}")
                        return None

                except Exception as json_error:
                    logger.error(f"Error parsing JSON response: {str(json_error)}")
                    logger.error(f"Raw response: {response.text[:500]}...")
                    return None

            else:
                logger.error(f"API request failed with status {response.status_code}")
                logger.error(f"Response text: {response.text[:500]}...")
                return None

        except requests.exceptions.Timeout:
            logger.error(f"Timeout error downloading data for {symbol}")
            return None
        except requests.exceptions.ConnectionError:
            logger.error(f"Connection error downloading data for {symbol}. Is the API server running?")
            return None
        except Exception as e:
            logger.error(f"Unexpected error downloading historical data for {symbol}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def _process_historical_data(self, symbol: str, raw_data: List[Dict]) -> Dict:
        """Process raw API data into structured historical data"""
        try:
            processed_data = {}

            logger.info(f"Processing {len(raw_data)} data points for {symbol}")
            logger.info(f"Raw data structure: {raw_data}")

            # Handle different API response structures
            if isinstance(raw_data, list) and len(raw_data) > 0:
                # Check if this is the expected TradingView API structure
                first_item = raw_data[0]
                logger.info(f"First item structure: {first_item}")

                # Handle case where raw_data is a list of interval data
                for i, interval_data in enumerate(raw_data):
                    try:
                        # Extract basic information
                        pair = interval_data.get('pair', f'EGX-{symbol}')
                        price = interval_data.get('price', 0)

                        # Try to determine interval from the data or use index-based mapping
                        # Common intervals: ['1D', '1W', '1M', '4h', '1h']
                        interval_mapping = ['1D', '1W', '1M', '4h', '1h']
                        interval = interval_mapping[i] if i < len(interval_mapping) else f'interval_{i}'

                        logger.info(f"Processing interval {interval}: pair={pair}, price={price}")

                        # Convert price from piasters to EGP if needed
                        if price > 1000:
                            price_egp = price / 1000.0
                            logger.info(f"Converted price from piasters: {price} -> {price_egp} EGP")
                        else:
                            price_egp = price
                            logger.info(f"Price already in EGP: {price_egp}")

                        # Validate price
                        if price_egp <= 0:
                            logger.warning(f"Invalid price for {symbol} at interval {interval}: {price_egp}")
                            continue

                        # Extract technical indicators safely
                        oscillators = interval_data.get('oscillators', [])
                        moving_averages = interval_data.get('moving_averages', [])
                        pivots = interval_data.get('pivots', [])

                        # Create structured data
                        processed_data[interval] = {
                            'symbol': symbol,
                            'interval': interval,
                            'current_price': price_egp,
                            'timestamp': datetime.now(),
                            'oscillators': oscillators,
                            'moving_averages': moving_averages,
                            'pivots': pivots,
                            'raw_data': interval_data
                        }

                        logger.info(f"Successfully processed {interval}: price={price_egp} EGP")

                    except Exception as interval_error:
                        logger.error(f"Error processing interval {i} for {symbol}: {str(interval_error)}")
                        continue

            else:
                logger.error(f"Unexpected raw_data structure for {symbol}: {type(raw_data)}")
                return {}

            if not processed_data:
                logger.error(f"No valid data processed for {symbol}")
                return {}

            logger.info(f"Successfully processed {len(processed_data)} intervals for {symbol}")
            return processed_data

        except Exception as e:
            logger.error(f"Error processing historical data for {symbol}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {}
    
    def _save_to_csv(self, symbol: str, historical_data: Dict):
        """Save historical data to CSV file"""
        try:
            csv_file = os.path.join(self.data_dir, f"{symbol}.csv")

            # Check if file exists
            if os.path.exists(csv_file):
                # Load existing data
                existing_df = pd.read_csv(csv_file)
                logger.info(f"Found existing CSV for {symbol} with {len(existing_df)} records")
            else:
                # Create new dataframe structure
                existing_df = pd.DataFrame(columns=[
                    'Date', 'Open', 'High', 'Low', 'Close', 'Volume'
                ])
                logger.info(f"Creating new CSV for {symbol}")

            # Add current data point
            current_time = datetime.now()
            daily_data = historical_data.get('1D', {})
            current_price = daily_data.get('current_price', 0)

            logger.info(f"Attempting to save CSV for {symbol}: price={current_price}")

            if current_price > 0:
                # Create new row with current data
                new_row = {
                    'Date': current_time.strftime('%Y-%m-%d'),
                    'Open': current_price * 0.999,  # Simulate open price
                    'High': current_price * 1.001,  # Simulate high price
                    'Low': current_price * 0.998,   # Simulate low price
                    'Close': current_price,
                    'Volume': 1000000  # Default volume
                }

                # Check if today's data already exists
                today_str = current_time.strftime('%Y-%m-%d')
                if not existing_df.empty and today_str in existing_df['Date'].values:
                    # Update existing row
                    existing_df.loc[existing_df['Date'] == today_str, 'Close'] = current_price
                    logger.info(f"Updated existing record for {symbol} on {today_str}")
                else:
                    # Add new row
                    new_df = pd.DataFrame([new_row])
                    existing_df = pd.concat([existing_df, new_df], ignore_index=True)
                    logger.info(f"Added new record for {symbol} on {today_str}")

                # Save to CSV
                existing_df.to_csv(csv_file, index=False)
                logger.info(f"Saved historical data for {symbol} to {csv_file}")
            else:
                logger.warning(f"Invalid price data for {symbol}: {current_price}. CSV not created.")
                logger.info(f"Available data keys: {list(historical_data.keys())}")
                for key, data in historical_data.items():
                    logger.info(f"  {key}: {data}")

        except Exception as e:
            logger.error(f"Error saving CSV for {symbol}: {str(e)}")

    def generate_synthetic_historical_data(self, symbol: str, current_price: float,
                                         timeframe_years: int = 3) -> pd.DataFrame:
        """
        Generate synthetic historical data based on current price
        This creates realistic-looking historical data for backtesting and analysis
        """
        try:
            logger.info(f"Generating {timeframe_years} years of synthetic data for {symbol}")

            # Calculate number of trading days (EGX trades ~250 days/year)
            trading_days_per_year = 250
            total_days = int(timeframe_years * trading_days_per_year)

            # Generate date range (excluding weekends)
            end_date = datetime.now()
            dates = []
            current_date = end_date

            while len(dates) < total_days:
                # Skip weekends (EGX trades Sunday-Thursday)
                if current_date.weekday() < 5:  # Monday=0, Sunday=6
                    dates.append(current_date)
                current_date -= timedelta(days=1)

            dates.reverse()  # Oldest first

            # Generate realistic price movement
            np.random.seed(42)  # For reproducible results

            # Start with a price 3 years ago (simulate growth/decline)
            start_price = current_price * np.random.uniform(0.7, 1.3)

            # Generate daily returns (realistic volatility for EGX)
            daily_volatility = 0.02  # 2% daily volatility
            returns = np.random.normal(0, daily_volatility, total_days)

            # Add trend component (gradual movement toward current price)
            trend_component = np.linspace(0, np.log(current_price / start_price), total_days)
            returns += trend_component / total_days

            # Calculate prices
            prices = [start_price]
            for i in range(1, total_days):
                new_price = prices[-1] * (1 + returns[i])
                prices.append(max(new_price, 0.01))  # Ensure positive prices

            # Adjust final price to match current price
            adjustment_factor = current_price / prices[-1]
            prices = [p * adjustment_factor for p in prices]

            # Generate OHLCV data
            historical_data = []

            for i, date in enumerate(dates):
                close_price = prices[i]

                # Generate realistic OHLC based on close price
                daily_range = close_price * np.random.uniform(0.01, 0.05)  # 1-5% daily range

                high = close_price + np.random.uniform(0, daily_range)
                low = close_price - np.random.uniform(0, daily_range)

                # Open price (influenced by previous close)
                if i == 0:
                    open_price = close_price * np.random.uniform(0.99, 1.01)
                else:
                    gap = np.random.uniform(-0.02, 0.02)  # Max 2% gap
                    open_price = prices[i-1] * (1 + gap)

                # Ensure OHLC logic (High >= max(O,C), Low <= min(O,C))
                high = max(high, open_price, close_price)
                low = min(low, open_price, close_price)

                # Generate volume (realistic for EGX)
                base_volume = 1000000
                volume_multiplier = np.random.uniform(0.5, 2.0)
                volume = int(base_volume * volume_multiplier)

                historical_data.append({
                    'Date': date.strftime('%Y-%m-%d'),
                    'Open': round(open_price, 2),
                    'High': round(high, 2),
                    'Low': round(low, 2),
                    'Close': round(close_price, 2),
                    'Volume': volume
                })

            df = pd.DataFrame(historical_data)
            logger.info(f"Generated {len(df)} days of synthetic historical data for {symbol}")

            return df

        except Exception as e:
            logger.error(f"Error generating synthetic data for {symbol}: {str(e)}")
            return pd.DataFrame()

    def download_and_generate_historical_data(self, symbol: str, timeframe_years: int = 3,
                                            intervals: List[str] = None) -> Optional[Dict]:
        """
        Download current data and generate comprehensive historical dataset
        """
        try:
            logger.info(f"Starting historical data generation for {symbol} ({timeframe_years} years)")
            logger.info(f"Requested intervals: {intervals}")

            # Check API server status first
            if not self.check_api_status():
                logger.error(f"API server is not available for {symbol}")
                return None

            # First, get current live data
            logger.info(f"Fetching current live data for {symbol}...")
            current_data = self.download_historical_data(symbol, intervals or ['1D'], save_to_csv=False)

            if not current_data:
                logger.error(f"Could not get current data for {symbol}")
                logger.error("This could be due to:")
                logger.error("1. Stock symbol not found on EGX")
                logger.error("2. API server connection issues")
                logger.error("3. Invalid stock symbol format")
                return None

            logger.info(f"Successfully retrieved current data for {symbol}: {current_data}")

            # Extract current price - try multiple intervals
            current_price = 0
            for interval in ['1D', '1W', '1M', '4h', '1h']:
                if interval in current_data:
                    interval_data = current_data[interval]
                    price = interval_data.get('current_price', 0)
                    if price > 0:
                        current_price = price
                        logger.info(f"Found valid price from {interval}: {current_price} EGP")
                        break

            if current_price <= 0:
                logger.error(f"Invalid current price for {symbol}: {current_price}")
                logger.error(f"Available data: {current_data}")
                return None

            logger.info(f"Using current price for {symbol}: {current_price} EGP")

            # Generate synthetic historical data
            logger.info(f"Generating {timeframe_years} years of synthetic historical data...")
            synthetic_df = self.generate_synthetic_historical_data(
                symbol, current_price, timeframe_years
            )

            if synthetic_df.empty:
                logger.error(f"Could not generate synthetic data for {symbol}")
                return None

            logger.info(f"Generated {len(synthetic_df)} days of synthetic data")

            # Save to CSV with proper path handling
            csv_file = os.path.join(self.data_dir, f"{symbol}.csv")
            csv_file = os.path.normpath(csv_file)  # Normalize path separators

            logger.info(f"Saving data to: {csv_file}")

            # Ensure directory exists before saving
            os.makedirs(os.path.dirname(csv_file), exist_ok=True)

            # Save the dataframe
            synthetic_df.to_csv(csv_file, index=False)
            logger.info(f"Saved {len(synthetic_df)} days of historical data to {csv_file}")

            # Verify file was created and has content
            if not os.path.exists(csv_file):
                logger.error(f"CSV file was not created: {csv_file}")
                return None

            # Check file size
            file_size = os.path.getsize(csv_file)
            logger.info(f"CSV file size: {file_size} bytes")

            if file_size == 0:
                logger.error(f"CSV file is empty: {csv_file}")
                return None

            # Verify data integrity by reading back
            try:
                verification_df = pd.read_csv(csv_file)
                logger.info(f"Verification: CSV contains {len(verification_df)} rows")
                if len(verification_df) != len(synthetic_df):
                    logger.warning(f"Row count mismatch: expected {len(synthetic_df)}, got {len(verification_df)}")
            except Exception as verify_error:
                logger.error(f"Error verifying saved CSV: {str(verify_error)}")

            # Return summary
            result = {
                'symbol': symbol,
                'current_price': current_price,
                'historical_days': len(synthetic_df),
                'date_range': f"{synthetic_df['Date'].iloc[0]} to {synthetic_df['Date'].iloc[-1]}",
                'price_range': f"{synthetic_df['Close'].min():.2f} - {synthetic_df['Close'].max():.2f} EGP",
                'csv_file': csv_file,
                'intervals': current_data
            }

            logger.info(f"Successfully completed historical data generation for {symbol}")
            return result

        except Exception as e:
            logger.error(f"Error downloading and generating historical data for {symbol}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def download_multiple_stocks(self, symbols: List[str], intervals: List[str] = None,
                                progress_callback=None) -> Dict[str, Dict]:
        """
        Download historical data for multiple stocks
        
        Args:
            symbols: List of stock symbols
            intervals: List of intervals to download
            progress_callback: Function to call with progress updates
            
        Returns:
            Dictionary with results for each symbol
        """
        if intervals is None:
            intervals = ['1D', '1W']
        
        results = {}
        total_stocks = len(symbols)
        
        for i, symbol in enumerate(symbols):
            try:
                if progress_callback:
                    progress_callback(i + 1, total_stocks, symbol)
                
                result = self.download_historical_data(symbol, intervals, save_to_csv=True)
                results[symbol] = result
                
                # Small delay to avoid overwhelming the API
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error downloading data for {symbol}: {str(e)}")
                results[symbol] = None
        
        return results
    
    def estimate_data_points(self, timeframe_years: int, interval: str) -> Tuple[int, bool]:
        """
        Estimate number of data points for given timeframe and interval
        
        Args:
            timeframe_years: Number of years of data wanted
            interval: Time interval (1D, 1W, 1M, etc.)
            
        Returns:
            Tuple of (estimated_points, fits_in_limit)
        """
        # TradingView limit is 5000 bars
        limit = 5000
        
        # Calculate approximate data points
        if interval == '1D':
            # ~250 trading days per year for EGX
            points = timeframe_years * 250
        elif interval == '1W':
            points = timeframe_years * 52
        elif interval == '1M':
            points = timeframe_years * 12
        elif interval == '1h':
            # ~6 hours trading per day, 250 days per year
            points = timeframe_years * 250 * 6
        elif interval == '4h':
            # ~1.5 sessions per day
            points = timeframe_years * 250 * 1.5
        else:
            # Conservative estimate
            points = timeframe_years * 365
        
        return int(points), points <= limit
    
    def get_recommended_intervals(self, timeframe_years: int) -> List[str]:
        """Get recommended intervals for given timeframe"""
        recommended = []

        # Always include daily if possible
        _, daily_fits = self.estimate_data_points(timeframe_years, '1D')
        if daily_fits:
            recommended.append('1D')

        # Include weekly for longer timeframes
        if timeframe_years >= 1:
            recommended.append('1W')

        # Include monthly for very long timeframes
        if timeframe_years >= 2:
            recommended.append('1M')

        # Include 4-hour for shorter timeframes
        if timeframe_years <= 2:
            _, four_hour_fits = self.estimate_data_points(timeframe_years, '4h')
            if four_hour_fits:
                recommended.append('4h')

        return recommended if recommended else ['1D']  # Fallback to daily
