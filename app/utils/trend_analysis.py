"""
Trend analysis module for the AI Stocks Bot.

This module provides functions for analyzing price trends, calculating momentum,
identifying support and resistance levels, and other technical analysis tools
that can be used to improve prediction accuracy.
"""
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Union, Tuple, Any
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

# Configure logging
logger = logging.getLogger(__name__)

# Flag to indicate that trend analysis is available
TREND_ANALYSIS_AVAILABLE = True

def detect_trend(df: pd.DataFrame, window: int = 20) -> Tuple[str, float]:
    """
    Detect the current price trend direction and strength.
    
    Args:
        df (pd.DataFrame): DataFrame with price data
        window (int): Window size for trend detection
        
    Returns:
        Tuple[str, float]: Trend direction ('up', 'down', 'neutral') and strength (0-1)
    """
    try:
        # Ensure we have enough data
        if len(df) < window:
            return "neutral", 0.0
        
        # Get the closing prices for the window
        close_prices = df['Close'].iloc[-window:].values
        
        # Calculate simple moving average
        sma = np.mean(close_prices)
        
        # Calculate the slope of the trend line
        x = np.arange(window)
        slope, _ = np.polyfit(x, close_prices, 1)
        
        # Normalize the slope to get trend strength (0-1)
        # We'll use the percentage change over the window as a reference
        price_range = np.max(close_prices) - np.min(close_prices)
        if price_range > 0:
            normalized_slope = min(abs(slope * window / price_range), 1.0)
        else:
            normalized_slope = 0.0
        
        # Determine trend direction
        if slope > 0:
            direction = "up"
        elif slope < 0:
            direction = "down"
        else:
            direction = "neutral"
        
        # Check if price is above or below SMA for confirmation
        last_price = close_prices[-1]
        if direction == "up" and last_price < sma:
            # Price below SMA in uptrend - weaken the signal
            normalized_slope *= 0.5
        elif direction == "down" and last_price > sma:
            # Price above SMA in downtrend - weaken the signal
            normalized_slope *= 0.5
        
        return direction, normalized_slope
    
    except Exception as e:
        logger.error(f"Error detecting trend: {str(e)}")
        return "neutral", 0.0


def calculate_momentum(df: pd.DataFrame, window: int = 14) -> float:
    """
    Calculate price momentum.
    
    Args:
        df (pd.DataFrame): DataFrame with price data
        window (int): Window size for momentum calculation
        
    Returns:
        float: Momentum value (-1 to 1, where positive values indicate upward momentum)
    """
    try:
        # Ensure we have enough data
        if len(df) < window:
            return 0.0
        
        # Get the closing prices
        close_prices = df['Close'].values
        
        # Calculate rate of change
        roc = (close_prices[-1] / close_prices[-window] - 1)
        
        # Normalize to -1 to 1 range (cap at ±20% for normalization)
        normalized_roc = np.clip(roc / 0.2, -1.0, 1.0)
        
        return normalized_roc
    
    except Exception as e:
        logger.error(f"Error calculating momentum: {str(e)}")
        return 0.0


def identify_support_resistance(df: pd.DataFrame, window: int = 20) -> Tuple[float, float]:
    """
    Identify support and resistance levels.
    
    Args:
        df (pd.DataFrame): DataFrame with price data
        window (int): Window size for support/resistance calculation
        
    Returns:
        Tuple[float, float]: Support and resistance levels
    """
    try:
        # Ensure we have enough data
        if len(df) < window:
            last_price = df['Close'].iloc[-1]
            return last_price * 0.95, last_price * 1.05
        
        # Get price data for the window
        price_data = df.iloc[-window:]
        
        # Find local minima and maxima
        highs = price_data['High'].values
        lows = price_data['Low'].values
        
        # Simple approach: use recent highs and lows
        support = np.percentile(lows, 25)  # 25th percentile of lows
        resistance = np.percentile(highs, 75)  # 75th percentile of highs
        
        # Ensure support is below current price and resistance is above
        last_price = df['Close'].iloc[-1]
        if support > last_price:
            support = last_price * 0.98
        if resistance < last_price:
            resistance = last_price * 1.02
        
        return support, resistance
    
    except Exception as e:
        logger.error(f"Error identifying support/resistance: {str(e)}")
        last_price = df['Close'].iloc[-1]
        return last_price * 0.95, last_price * 1.05


def calculate_volatility(df: pd.DataFrame, window: int = 20) -> float:
    """
    Calculate price volatility.
    
    Args:
        df (pd.DataFrame): DataFrame with price data
        window (int): Window size for volatility calculation
        
    Returns:
        float: Volatility as a percentage
    """
    try:
        # Ensure we have enough data
        if len(df) < window:
            return 0.0
        
        # Get the closing prices
        close_prices = df['Close'].iloc[-window:].values
        
        # Calculate daily returns
        returns = np.diff(close_prices) / close_prices[:-1]
        
        # Calculate volatility (standard deviation of returns)
        volatility = np.std(returns) * np.sqrt(252)  # Annualized
        
        return volatility
    
    except Exception as e:
        logger.error(f"Error calculating volatility: {str(e)}")
        return 0.0


def detect_breakout(df: pd.DataFrame, window: int = 20) -> Tuple[bool, str]:
    """
    Detect if a price breakout has occurred.
    
    Args:
        df (pd.DataFrame): DataFrame with price data
        window (int): Window size for breakout detection
        
    Returns:
        Tuple[bool, str]: Whether a breakout occurred and its direction ('up' or 'down')
    """
    try:
        # Ensure we have enough data
        if len(df) < window:
            return False, "none"
        
        # Get price data
        price_data = df.iloc[-window:]
        last_price = df['Close'].iloc[-1]
        
        # Calculate Bollinger Bands
        sma = price_data['Close'].mean()
        std = price_data['Close'].std()
        upper_band = sma + 2 * std
        lower_band = sma - 2 * std
        
        # Check for breakout
        if last_price > upper_band:
            return True, "up"
        elif last_price < lower_band:
            return True, "down"
        else:
            return False, "none"
    
    except Exception as e:
        logger.error(f"Error detecting breakout: {str(e)}")
        return False, "none"


def analyze_volume_trend(df: pd.DataFrame, window: int = 10) -> str:
    """
    Analyze volume trend to confirm price movements.
    
    Args:
        df (pd.DataFrame): DataFrame with price and volume data
        window (int): Window size for volume analysis
        
    Returns:
        str: Volume trend ('increasing', 'decreasing', 'stable')
    """
    try:
        # Ensure we have enough data and volume column exists
        if len(df) < window or 'Volume' not in df.columns:
            return "stable"
        
        # Get volume data
        volume_data = df['Volume'].iloc[-window:].values
        
        # Calculate volume moving average
        volume_ma = np.mean(volume_data)
        
        # Calculate volume trend
        volume_slope, _ = np.polyfit(np.arange(window), volume_data, 1)
        
        # Determine volume trend
        if volume_slope > 0 and volume_data[-1] > volume_ma:
            return "increasing"
        elif volume_slope < 0 and volume_data[-1] < volume_ma:
            return "decreasing"
        else:
            return "stable"
    
    except Exception as e:
        logger.error(f"Error analyzing volume trend: {str(e)}")
        return "stable"


def get_trend_analysis(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Get comprehensive trend analysis for a stock.
    
    Args:
        df (pd.DataFrame): DataFrame with price and volume data
        
    Returns:
        Dict[str, Any]: Dictionary with trend analysis results
    """
    try:
        # Perform various trend analyses
        trend_direction, trend_strength = detect_trend(df)
        momentum = calculate_momentum(df)
        support, resistance = identify_support_resistance(df)
        volatility = calculate_volatility(df)
        breakout, breakout_direction = detect_breakout(df)
        volume_trend = analyze_volume_trend(df)
        
        # Get the last price
        last_price = df['Close'].iloc[-1]
        
        # Calculate distance to support/resistance
        support_distance = (last_price - support) / last_price
        resistance_distance = (resistance - last_price) / last_price
        
        # Return results
        return {
            "trend_direction": trend_direction,
            "trend_strength": trend_strength,
            "momentum": momentum,
            "support": support,
            "resistance": resistance,
            "support_distance": support_distance,
            "resistance_distance": resistance_distance,
            "volatility": volatility,
            "breakout": breakout,
            "breakout_direction": breakout_direction,
            "volume_trend": volume_trend,
            "last_price": last_price
        }
    
    except Exception as e:
        logger.error(f"Error performing trend analysis: {str(e)}")
        return {}
