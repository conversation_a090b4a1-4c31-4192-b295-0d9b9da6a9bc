"""
Performance optimization utilities for the AI Stocks Bot app
"""
import streamlit as st
import pandas as pd
import logging
import time
import threading
from functools import wraps

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Cache for storing data to avoid redundant operations
_data_cache = {}

def timed_execution(func):
    """
    Decorator to measure execution time of functions
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"Function {func.__name__} executed in {end_time - start_time:.2f} seconds")
        return result
    return wrapper

def run_in_background(func):
    """
    Decorator to run a function in a background thread
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(target=func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
    return wrapper

def clear_cache():
    """
    Clear the data cache
    """
    global _data_cache
    _data_cache = {}
    # Also clear Streamlit's cache
    st.cache_data.clear()
    logger.info("Cache cleared")

def get_cache_info():
    """
    Get information about the cache
    """
    return {
        "items": len(_data_cache),
        "keys": list(_data_cache.keys())
    }

def monitor_memory_usage():
    """
    Monitor memory usage of the application
    """
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    return {
        "rss": memory_info.rss / (1024 * 1024),  # RSS in MB
        "vms": memory_info.vms / (1024 * 1024),  # VMS in MB
    }
