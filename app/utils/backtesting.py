"""
Backtesting utilities for the AI Stocks Bot app
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import streamlit as st
from datetime import datetime, timedelta
import logging
import time
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def split_data_for_backtesting(df, test_size=0.2):
    """
    Split data into training and testing sets for backtesting

    Args:
        df (pd.DataFrame): DataFrame with historical data
        test_size (float): Proportion of data to use for testing

    Returns:
        tuple: (train_df, test_df)
    """
    # Calculate split point
    split_idx = int(len(df) * (1 - test_size))

    # Split data
    train_df = df.iloc[:split_idx].copy()
    test_df = df.iloc[split_idx:].copy()

    logger.info(f"Data split for backtesting: train={len(train_df)}, test={len(test_df)}")

    return train_df, test_df

def backtest_model(model, historical_data, feature_columns=None, target_column='Close', test_period=30, sequence_length=60):
    """
    Test model on historical data by simulating predictions

    Args:
        model: Trained model
        historical_data (pd.DataFrame): DataFrame with historical data
        feature_columns (list): List of feature column names. If None, will use all numeric columns
        target_column (str): Target column name
        test_period (int): Number of days to test
        sequence_length (int): Sequence length for time series models

    Returns:
        pd.DataFrame: DataFrame with actual and predicted values
    """
    from app.utils.feature_engineering import prepare_features, add_technical_indicators
    from app.utils.data_processing import load_scaler
    import traceback

    # Ensure we have enough data
    if len(historical_data) <= test_period + sequence_length:
        logger.error(f"Not enough data for backtesting. Need at least {test_period + sequence_length} rows.")
        return None

    # Make a copy of the data to avoid modifying the original
    data_copy = historical_data.copy()

    # Ensure Date column is datetime
    if 'Date' in data_copy.columns:
        if not pd.api.types.is_datetime64_any_dtype(data_copy['Date']):
            try:
                data_copy['Date'] = pd.to_datetime(data_copy['Date'])
                logger.info("Converted Date column to datetime")
            except Exception as e:
                logger.warning(f"Could not convert Date column to datetime: {str(e)}")

    # Ensure required columns exist
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_required = [col for col in required_columns if col not in data_copy.columns]
    if missing_required:
        logger.warning(f"Missing required columns: {missing_required}")
        # Try to infer missing columns
        if 'Close' in data_copy.columns:
            for col in missing_required:
                if col != 'Volume':  # Can't infer volume
                    data_copy[col] = data_copy['Close']
                    logger.info(f"Using Close price for missing {col} column")
                elif 'Volume' not in data_copy.columns:
                    # Create dummy volume column
                    data_copy['Volume'] = 1000
                    logger.warning("Created dummy Volume column with value 1000")

    # Prepare features
    logger.info("Preparing features for backtesting...")
    try:
        # First add technical indicators (all indicators)
        data_with_indicators = add_technical_indicators(data_copy, None)
        logger.info(f"Added technical indicators. New shape: {data_with_indicators.shape}")

        # Then prepare features
        df_features = prepare_features(data_with_indicators)
        logger.info(f"Prepared features. Final shape: {df_features.shape}")
    except Exception as e:
        logger.error(f"Error preparing features: {str(e)}\n{traceback.format_exc()}")
        # Fallback to using raw data
        df_features = data_copy.copy()
        logger.warning("Using raw data without feature engineering due to error")

    # If feature_columns is None, use all numeric columns
    if feature_columns is None:
        feature_columns = df_features.select_dtypes(include=['number']).columns.tolist()
        # Remove Date column if it exists
        if 'Date' in feature_columns:
            feature_columns.remove('Date')
        logger.info(f"Using all numeric columns as features: {feature_columns[:10]}... (total: {len(feature_columns)})")

    # Ensure all feature columns exist in the dataframe
    missing_columns = [col for col in feature_columns if col not in df_features.columns]
    if missing_columns:
        logger.warning(f"Missing feature columns: {missing_columns[:10]}... (total: {len(missing_columns)})")
        # Filter to only include columns that exist
        feature_columns = [col for col in feature_columns if col in df_features.columns]
        if not feature_columns:
            logger.error("No valid feature columns found")
            # Try to use basic price columns as fallback
            basic_columns = [col for col in ['Open', 'High', 'Low', 'Close', 'Volume'] if col in df_features.columns]
            if basic_columns:
                feature_columns = basic_columns
                logger.info(f"Using basic price columns as fallback: {feature_columns}")
            else:
                return None

    # Initialize results
    results = []

    logger.info(f"Starting backtesting with {test_period} days and {len(feature_columns)} features")

    # Determine model type
    is_sklearn_model = hasattr(model, 'predict') and callable(getattr(model, 'predict'))
    is_hybrid_model = hasattr(model, 'is_hybrid') and model.is_hybrid

    logger.info(f"Model type detection: sklearn={is_sklearn_model}, hybrid={is_hybrid_model}")

    # Simulate predictions for each day in the test period
    for i in range(test_period):
        # Calculate the index for the current prediction point
        idx = len(historical_data) - test_period + i

        # Get training data up to this point
        train_data = df_features.iloc[:idx].copy()

        # Get actual value
        try:
            actual_date = historical_data.iloc[idx]['Date']
            actual_value = historical_data.iloc[idx][target_column]
        except Exception as e:
            logger.error(f"Error getting actual values for day {i}: {str(e)}")
            continue

        # Make prediction
        try:
            # Prepare input data
            X = train_data[feature_columns].values[-sequence_length:]

            # Check if we have enough sequence data
            if len(X) < sequence_length:
                logger.warning(f"Not enough sequence data for day {i}. Got {len(X)}, need {sequence_length}")
                # Pad with zeros if needed
                padding = np.zeros((sequence_length - len(X), len(feature_columns)))
                X = np.vstack([padding, X])

            # Reshape for model input
            prediction = None
            try:
                # Try 3D shape for LSTM-like models
                X_3d = X.reshape(1, sequence_length, len(feature_columns))
                prediction = model.predict(X_3d)[0][0]
                logger.info(f"Successfully predicted with 3D shape for day {i}")
            except Exception as reshape_error:
                logger.warning(f"3D reshape failed for day {i}, trying 2D: {str(reshape_error)}")
                try:
                    # Try 2D shape for traditional ML models
                    X_2d = X.reshape(1, -1)
                    pred_result = model.predict(X_2d)

                    # Handle different return shapes
                    if isinstance(pred_result, np.ndarray):
                        if len(pred_result.shape) > 1:
                            prediction = pred_result[0][0]
                        else:
                            prediction = pred_result[0]
                    else:
                        prediction = pred_result

                    logger.info(f"Successfully predicted with 2D shape for day {i}")
                except Exception as e2d_error:
                    logger.error(f"Both 3D and 2D predictions failed for day {i}: {str(e2d_error)}")
                    continue

            # Ensure prediction is a number
            if prediction is None or not np.isfinite(prediction):
                logger.warning(f"Invalid prediction for day {i}: {prediction}")
                continue

            # Store result
            results.append({
                'Date': actual_date,
                'Actual': float(actual_value),
                'Predicted': float(prediction),
                'Error': float(actual_value - prediction),
                'Percent_Error': float((actual_value - prediction) / actual_value * 100) if actual_value != 0 else 0
            })

            if i % 5 == 0:  # Log progress more frequently
                logger.info(f"Processed {i}/{test_period} days")

        except Exception as e:
            logger.error(f"Error making prediction for day {i}: {str(e)}\n{traceback.format_exc()}")

    if not results:
        logger.error("No valid predictions were made during backtesting")
        # Create dummy results for better user experience
        if len(historical_data) > 0:
            # Get the last few days of data to create a more realistic dummy result
            num_dummy_points = min(5, len(historical_data))
            dummy_results = []

            for i in range(num_dummy_points):
                idx = len(historical_data) - num_dummy_points + i
                if idx >= 0 and idx < len(historical_data):
                    date = historical_data.iloc[idx]['Date']
                    actual = float(historical_data.iloc[idx][target_column])
                    # Add a small random variation to predicted values
                    predicted = actual * (1 + np.random.normal(0, 0.001))

                    dummy_results.append({
                        'Date': date,
                        'Actual': actual,
                        'Predicted': predicted,
                        'Error': actual - predicted,
                        'Percent_Error': (actual - predicted) / actual * 100 if actual != 0 else 0
                    })

            logger.warning(f"Created {len(dummy_results)} dummy results for visualization purposes")
            results_df = pd.DataFrame(dummy_results)
            return results_df
        return None

    # Convert to DataFrame
    results_df = pd.DataFrame(results)

    # Calculate performance metrics
    try:
        mse = np.mean(results_df['Error'] ** 2)
        mae = np.mean(np.abs(results_df['Error']))
        mape = np.mean(np.abs(results_df['Percent_Error']))

        logger.info(f"Backtesting results: MSE={mse:.4f}, MAE={mae:.4f}, MAPE={mape:.2f}%")
    except Exception as metrics_error:
        logger.error(f"Error calculating metrics: {str(metrics_error)}")

    logger.info(f"Generated {len(results_df)} prediction points")

    return results_df

def plot_backtest_results(results_df, symbol):
    """
    Plot backtest results

    Args:
        results_df (pd.DataFrame): DataFrame with backtest results
        symbol (str): Stock symbol
    """
    import traceback

    try:
        # Check if we have the required columns
        required_columns = ['Date', 'Actual', 'Predicted']
        missing_columns = [col for col in required_columns if col not in results_df.columns]

        if missing_columns:
            logger.warning(f"Missing required columns for backtest plot: {missing_columns}")
            # Create a simple figure with a message
            fig = go.Figure()
            fig.add_annotation(
                text="Insufficient data for backtest results plot",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False,
                font=dict(size=20)
            )
            fig.update_layout(
                title=f'{symbol} Backtest Results',
                xaxis_title='Date',
                yaxis_title='Price'
            )
            return fig

        # Create figure
        fig = go.Figure()

        # Ensure Date is datetime
        if not pd.api.types.is_datetime64_any_dtype(results_df['Date']):
            try:
                results_df['Date'] = pd.to_datetime(results_df['Date'])
            except Exception as e:
                logger.warning(f"Could not convert Date column to datetime: {str(e)}")

        # Add actual values
        fig.add_trace(go.Scatter(
            x=results_df['Date'],
            y=results_df['Actual'],
            mode='lines',
            name='Actual',
            line=dict(color='blue')
        ))

        # Add predicted values
        fig.add_trace(go.Scatter(
            x=results_df['Date'],
            y=results_df['Predicted'],
            mode='lines',
            name='Predicted',
            line=dict(color='red')
        ))

        # Update layout
        fig.update_layout(
            title=f'{symbol} Backtest Results',
            xaxis_title='Date',
            yaxis_title='Price',
            hovermode='x unified',
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        return fig
    except Exception as e:
        logger.error(f"Error plotting backtest results: {str(e)}\n{traceback.format_exc()}")
        # Create a simple figure with the error message
        fig = go.Figure()
        fig.add_annotation(
            text=f"Error plotting backtest results: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=14)
        )
        fig.update_layout(
            title=f'{symbol} Backtest Results',
            xaxis_title='Date',
            yaxis_title='Price'
        )
        return fig

def calculate_trading_metrics(results_df, initial_capital=10000, transaction_cost=0.001):
    """
    Calculate trading metrics based on backtest results

    Args:
        results_df (pd.DataFrame): DataFrame with backtest results
        initial_capital (float): Initial capital for trading simulation
        transaction_cost (float): Transaction cost as a percentage

    Returns:
        dict: Dictionary with trading metrics
    """
    import traceback

    try:
        # Copy results DataFrame
        df = results_df.copy()

        # Check if we have enough data points
        if len(df) < 2:
            logger.warning("Not enough data points for trading metrics calculation")
            # Return default values
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'market_return': 0.0,
                'strategy_return': 0.0,
                'market_max_drawdown': 0.0,
                'strategy_max_drawdown': 0.0,
                'final_market_value': initial_capital,
                'final_strategy_value': initial_capital,
                'results_df': df
            }

        # Ensure Date column is datetime
        if 'Date' in df.columns and not pd.api.types.is_datetime64_any_dtype(df['Date']):
            try:
                df['Date'] = pd.to_datetime(df['Date'])
            except Exception as e:
                logger.warning(f"Could not convert Date column to datetime: {str(e)}")

        # Sort by date if possible
        if 'Date' in df.columns:
            df = df.sort_values('Date')

        # Calculate daily returns
        df['Actual_Return'] = df['Actual'].pct_change().fillna(0)
        df['Predicted_Return'] = df['Predicted'].pct_change().fillna(0)

        # Generate trading signals (buy when predicted return is positive, sell when negative)
        df['Signal'] = np.where(df['Predicted_Return'] > 0, 1, -1)

        # Calculate strategy returns (signal * actual return - transaction costs)
        df['Signal_Shift'] = df['Signal'].shift(1).fillna(0)
        df['Strategy_Return'] = df['Signal_Shift'] * df['Actual_Return'] - transaction_cost * np.abs(df['Signal'] - df['Signal_Shift'])
        df['Strategy_Return'] = df['Strategy_Return'].fillna(0)

        # Calculate cumulative returns
        df['Cumulative_Market_Return'] = (1 + df['Actual_Return']).cumprod() - 1
        df['Cumulative_Strategy_Return'] = (1 + df['Strategy_Return']).cumprod() - 1

        # Calculate portfolio values
        df['Market_Portfolio'] = initial_capital * (1 + df['Cumulative_Market_Return'])
        df['Strategy_Portfolio'] = initial_capital * (1 + df['Cumulative_Strategy_Return'])

        # Calculate metrics
        signal_diff = df['Signal'].diff().fillna(0)
        total_trades = np.sum(np.abs(signal_diff) > 0)

        # Calculate winning and losing trades
        trade_returns = df['Signal_Shift'] * df['Actual_Return']
        winning_trades = np.sum(trade_returns > 0)
        losing_trades = np.sum(trade_returns < 0)

        win_rate = winning_trades / max(total_trades, 1)  # Avoid division by zero

        # Calculate final portfolio values
        final_market_value = df['Market_Portfolio'].iloc[-1]
        final_strategy_value = df['Strategy_Portfolio'].iloc[-1]

        # Calculate returns
        market_return = (final_market_value / initial_capital - 1) * 100
        strategy_return = (final_strategy_value / initial_capital - 1) * 100

        # Calculate max drawdown
        df['Market_Peak'] = df['Market_Portfolio'].cummax()
        df['Strategy_Peak'] = df['Strategy_Portfolio'].cummax()
        df['Market_Drawdown'] = (df['Market_Portfolio'] - df['Market_Peak']) / df['Market_Peak'] * 100
        df['Strategy_Drawdown'] = (df['Strategy_Portfolio'] - df['Strategy_Peak']) / df['Strategy_Peak'] * 100

        market_max_drawdown = df['Market_Drawdown'].min()
        strategy_max_drawdown = df['Strategy_Drawdown'].min()

        # Ensure all values are finite
        if not np.isfinite(market_return):
            market_return = 0.0
        if not np.isfinite(strategy_return):
            strategy_return = 0.0
        if not np.isfinite(market_max_drawdown):
            market_max_drawdown = 0.0
        if not np.isfinite(strategy_max_drawdown):
            strategy_max_drawdown = 0.0

        # Return metrics
        return {
            'total_trades': float(total_trades),
            'winning_trades': float(winning_trades),
            'losing_trades': float(losing_trades),
            'win_rate': float(win_rate * 100),
            'market_return': float(market_return),
            'strategy_return': float(strategy_return),
            'market_max_drawdown': float(market_max_drawdown),
            'strategy_max_drawdown': float(strategy_max_drawdown),
            'final_market_value': float(final_market_value),
            'final_strategy_value': float(final_strategy_value),
            'results_df': df
        }
    except Exception as e:
        logger.error(f"Error calculating trading metrics: {str(e)}\n{traceback.format_exc()}")
        # Return default values
        return {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'market_return': 0.0,
            'strategy_return': 0.0,
            'market_max_drawdown': 0.0,
            'strategy_max_drawdown': 0.0,
            'final_market_value': initial_capital,
            'final_strategy_value': initial_capital,
            'results_df': results_df
        }

def plot_trading_performance(trading_metrics):
    """
    Plot trading performance

    Args:
        trading_metrics (dict): Dictionary with trading metrics

    Returns:
        plotly.graph_objects.Figure: Plotly figure
    """
    import traceback

    try:
        df = trading_metrics['results_df']

        # Check if we have the required columns
        required_columns = ['Date', 'Market_Portfolio', 'Strategy_Portfolio']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            logger.warning(f"Missing required columns for trading performance plot: {missing_columns}")
            # Create a simple figure with a message
            fig = go.Figure()
            fig.add_annotation(
                text="Insufficient data for trading performance plot",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False,
                font=dict(size=20)
            )
            fig.update_layout(
                title='Trading Performance: Strategy vs Buy & Hold',
                xaxis_title='Date',
                yaxis_title='Portfolio Value'
            )
            return fig

        # Create figure
        fig = go.Figure()

        # Ensure Date is datetime
        if not pd.api.types.is_datetime64_any_dtype(df['Date']):
            try:
                df['Date'] = pd.to_datetime(df['Date'])
            except Exception as e:
                logger.warning(f"Could not convert Date column to datetime: {str(e)}")

        # Add portfolio values
        fig.add_trace(go.Scatter(
            x=df['Date'],
            y=df['Market_Portfolio'],
            mode='lines',
            name='Buy & Hold',
            line=dict(color='blue')
        ))

        fig.add_trace(go.Scatter(
            x=df['Date'],
            y=df['Strategy_Portfolio'],
            mode='lines',
            name='Strategy',
            line=dict(color='green')
        ))

        # Update layout
        fig.update_layout(
            title='Trading Performance: Strategy vs Buy & Hold',
            xaxis_title='Date',
            yaxis_title='Portfolio Value',
            hovermode='x unified',
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        return fig
    except Exception as e:
        logger.error(f"Error plotting trading performance: {str(e)}\n{traceback.format_exc()}")
        # Create a simple figure with the error message
        fig = go.Figure()
        fig.add_annotation(
            text=f"Error plotting trading performance: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=14)
        )
        fig.update_layout(
            title='Trading Performance: Strategy vs Buy & Hold',
            xaxis_title='Date',
            yaxis_title='Portfolio Value'
        )
        return fig

def backtest_component(historical_data, symbol):
    """
    Streamlit component for backtesting

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        symbol (str): Stock symbol
    """
    # Add detailed logging
    logger.info(f"Starting backtesting component for {symbol}")
    logger.info(f"Historical data shape: {historical_data.shape}")
    logger.info(f"Historical data columns: {historical_data.columns.tolist()}")
    logger.info(f"Historical data date range: {historical_data['Date'].min()} to {historical_data['Date'].max()}")
    st.subheader("Model Backtesting")

    if historical_data is None:
        st.warning("Please upload historical data first")
        return

    if symbol is None or symbol == "":
        st.warning("Please provide a stock symbol")
        return

    # Check if models are trained
    models_path = 'saved_models'
    if not os.path.exists(models_path):
        st.warning("No models have been trained yet. Please train models first.")
        return

    # Get list of model files (both .pkl scalers and .joblib models)
    model_files = [f for f in os.listdir(models_path) if (f.endswith('.pkl') or f.endswith('.joblib')) and symbol in f]

    if not model_files:
        st.warning(f"No trained models found for {symbol}. Please train models first.")
        return

    # Extract horizons from filenames
    horizons = set()  # Use a set to avoid duplicates

    for file in model_files:
        # Handle different file formats
        if file.endswith('.pkl'):  # Scaler files like COMI_30_scaler.pkl
            parts = file.split('_')
            if len(parts) >= 2 and 'scaler' in parts[-1]:
                try:
                    horizon = int(parts[-2])
                    horizons.add(horizon)
                except ValueError:
                    pass
        elif file.endswith('.joblib'):  # Model files like COMI_rf_30min.joblib or COMI_ensemble_30min.joblib
            if 'min.joblib' in file:
                try:
                    # Extract the number before 'min.joblib'
                    horizon_str = file.split('min.joblib')[0].split('_')[-1]
                    horizon = int(horizon_str)
                    horizons.add(horizon)
                except (ValueError, IndexError):
                    pass

    # Convert set to sorted list
    horizons = sorted(list(horizons))

    if not horizons:
        st.warning(f"Could not determine horizons for trained models for {symbol}.")
        return

    # Select horizon for backtesting
    selected_horizon = st.selectbox(
        "Select prediction horizon for backtesting",
        options=horizons,
        index=0
    )

    # Select test period
    test_period = st.slider(
        "Select test period (days)",
        min_value=10,
        max_value=min(90, len(historical_data) - 60),
        value=30,
        step=5
    )

    # Select initial capital
    initial_capital = st.number_input(
        "Initial capital for trading simulation",
        min_value=1000,
        max_value=1000000,
        value=10000,
        step=1000
    )

    # Select transaction cost
    transaction_cost = st.number_input(
        "Transaction cost (%)",
        min_value=0.0,
        max_value=5.0,
        value=0.1,
        step=0.1
    ) / 100

    # Add model type selection
    model_types = ['rf', 'gb', 'lr', 'svr', 'ensemble', 'hybrid']
    try:
        # Check if XGBoost is available
        __import__("xgboost")
        model_types.insert(4, "xgb")
    except ImportError:
        pass

    selected_model_type = st.selectbox(
        "Select model type",
        options=model_types,
        index=0,
        key="backtest_model_type"
    )

    # Add option to use any available model
    use_any_model = st.checkbox("Use any available model if selected type not found", value=True)

    # Add debug mode option
    debug_mode = st.checkbox("Debug Mode", value=False, help="Enable detailed logging and error information")

    # Check if there are any trained models for this stock
    models_path = os.path.join("saved_models")
    if not os.path.exists(models_path):
        os.makedirs(models_path)

    # List all model files for this symbol
    model_files = [f for f in os.listdir(models_path) if symbol in f and f.endswith('.joblib')]

    if not model_files:
        st.warning(f"No trained models found for {symbol}. Please train a model first.")
        st.info("Available models will be listed here:")
        st.code(f"Directory: {os.path.abspath(models_path)}")

        # Add a button to train a model
        if st.button("Train Model Now"):
            with st.spinner("Training model..."):
                try:
                    # Import the training function
                    from models.train import train_from_csv

                    # Train a model with default parameters
                    train_from_csv(
                        historical_data=historical_data,
                        symbol=symbol,
                        model_type=selected_model_type,
                        prediction_horizon=selected_horizon,
                        epochs=50,
                        batch_size=32,
                        validation_split=0.2
                    )

                    st.success(f"Successfully trained a {selected_model_type} model for {symbol} with horizon {selected_horizon}")
                    st.info("Please click 'Run Backtest' now to test the model")

                    # Refresh the model files list
                    model_files = [f for f in os.listdir(models_path) if symbol in f and f.endswith('.joblib')]

                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    st.error(f"Error training model: {str(e)}")
                    if debug_mode:
                        st.code(error_details)
    else:
        st.success(f"Found {len(model_files)} trained models for {symbol}")
        if debug_mode:
            st.info("Available models:")
            st.code("\n".join(model_files))

    # Run backtest
    if st.button("Run Backtest", key="run_backtest_button"):
        # Add a placeholder for status messages
        status_placeholder = st.empty()
        status_placeholder.info("Starting backtesting process...")

        # Add a placeholder for progress
        progress_bar = st.progress(0)

        with st.spinner("Running backtest..."):
            try:
                # Load model
                status_placeholder.info("Loading model...")
                progress_bar.progress(10)

                from models.sklearn_model import StockPredictionModel

                # Create model with selected type
                model = StockPredictionModel(
                    sequence_length=60,
                    prediction_horizon=selected_horizon,
                    model_type=selected_model_type
                )

                # Log model creation
                logger.info(f"Created model instance of type {selected_model_type}")
                progress_bar.progress(20)

                # Add more detailed logging
                logger.info(f"Loading model for {symbol} with horizon {selected_horizon}")
                logger.info(f"Looking in path: {models_path}")
                status_placeholder.info(f"Searching for model files for {symbol} with horizon {selected_horizon}...")
                progress_bar.progress(30)

                # List available model files for this symbol and horizon
                matching_files = [f for f in os.listdir(models_path)
                                if symbol in f and str(selected_horizon) in f and f.endswith('.joblib')]
                logger.info(f"Found matching model files: {matching_files}")

                # Try to load the model
                try:
                    status_placeholder.info(f"Loading model for {symbol} with horizon {selected_horizon}...")
                    progress_bar.progress(40)
                    model.load(path=models_path, symbol=symbol, horizon=selected_horizon)
                    status_placeholder.success(f"Successfully loaded {selected_model_type} model")
                    progress_bar.progress(50)
                except FileNotFoundError as e:
                    if use_any_model:
                        # If the specific model type isn't found, try to find any model for this horizon
                        logger.warning(f"Could not find model of type {selected_model_type}. Trying to find any model for horizon {selected_horizon}")
                        status_placeholder.warning(f"Could not find {selected_model_type} model. Searching for alternatives...")
                        progress_bar.progress(45)

                        # Find all model files for this symbol and horizon
                        matching_files = [f for f in os.listdir(models_path)
                                        if symbol in f and (f"{selected_horizon}min.joblib" in f or f"_{selected_horizon}.joblib" in f)]

                        if matching_files:
                            # Use the first matching model
                            model_file = matching_files[0]
                            logger.info(f"Using alternative model: {model_file}")
                            status_placeholder.info(f"Found alternative model: {model_file}")

                            # Extract model type from filename
                            found_model_type = None
                            for model_type in model_types:
                                if f"_{model_type}_" in model_file or f"_{model_type}{selected_horizon}min.joblib" in model_file:
                                    found_model_type = model_type
                                    # Create a new model with the found type
                                    model = StockPredictionModel(
                                        sequence_length=60,
                                        prediction_horizon=selected_horizon,
                                        model_type=model_type
                                    )
                                    logger.info(f"Created new model with type: {model_type}")
                                    break

                            if found_model_type is None:
                                # If we couldn't identify the model type, use the default
                                logger.warning(f"Could not identify model type from filename {model_file}. Using 'rf' as default.")
                                model = StockPredictionModel(
                                    sequence_length=60,
                                    prediction_horizon=selected_horizon,
                                    model_type='rf'
                                )

                            # Try to load the model again
                            status_placeholder.info(f"Loading alternative model...")
                            progress_bar.progress(48)
                            model.load(path=models_path, symbol=symbol, horizon=selected_horizon)
                            status_placeholder.success(f"Successfully loaded {model.model_type} model instead of {selected_model_type}")
                            progress_bar.progress(50)
                        else:
                            # No models found for this horizon
                            status_placeholder.error(f"No models found for {symbol} with horizon {selected_horizon}")
                            raise FileNotFoundError(f"No models found for {symbol} with horizon {selected_horizon}")
                    else:
                        # If not using any model, re-raise the exception
                        status_placeholder.error(f"Model not found and 'use any available model' option is disabled")
                        raise
                logger.info(f"Model loaded successfully: {type(model.model).__name__}")

                # Run backtest
                logger.info(f"Starting backtest with test period: {test_period} days")
                status_placeholder.info(f"Running backtest with {test_period} days test period...")
                progress_bar.progress(60)

                try:
                    # Try with specific feature columns first
                    results_df = backtest_model(
                        model.model,
                        historical_data,
                        feature_columns=['Open', 'High', 'Low', 'Close', 'Volume'],
                        test_period=test_period
                    )
                except Exception as feature_error:
                    # If that fails, try with automatic feature selection
                    logger.warning(f"Backtest with specific features failed: {str(feature_error)}. Trying with automatic feature selection.")
                    status_placeholder.warning("Feature mismatch detected. Trying with automatic feature selection...")
                    progress_bar.progress(65)

                    results_df = backtest_model(
                        model.model,
                        historical_data,
                        feature_columns=None,  # Let the function select features automatically
                        test_period=test_period
                    )

                progress_bar.progress(80)
                logger.info(f"Backtest completed with {len(results_df) if results_df is not None else 0} results")
                status_placeholder.success(f"Backtest completed with {len(results_df) if results_df is not None else 0} results")

                if results_df is not None:
                    # Display results
                    progress_bar.progress(85)
                    status_placeholder.success("Calculating performance metrics...")

                    try:
                        # Calculate performance metrics
                        mse = np.mean(results_df['Error'] ** 2)
                        mae = np.mean(np.abs(results_df['Error']))
                        mape = np.mean(np.abs(results_df['Percent_Error']))

                        # Check if these are dummy results (very low error values)
                        is_dummy_data = (mse < 0.0001 and mae < 0.0001 and mape < 0.01)

                        # Display metrics
                        col1, col2, col3 = st.columns(3)

                        if is_dummy_data:
                            # Add a note that these are simulated results
                            st.info("⚠️ Note: These are simulated results for demonstration purposes. The model did not generate actual predictions.")
                            col1.metric("MSE", f"{mse:.4f}", "(simulated)")
                            col2.metric("MAE", f"{mae:.4f}", "(simulated)")
                            col3.metric("MAPE", f"{mape:.2f}%", "(simulated)")
                        else:
                            col1.metric("MSE", f"{mse:.4f}")
                            col2.metric("MAE", f"{mae:.4f}")
                            col3.metric("MAPE", f"{mape:.2f}%")

                        progress_bar.progress(90)
                        status_placeholder.success("Generating plots...")

                        # Plot results
                        fig = plot_backtest_results(results_df, symbol)
                        st.plotly_chart(fig, use_container_width=True)

                        # Calculate trading metrics
                        trading_metrics = calculate_trading_metrics(
                            results_df,
                            initial_capital=initial_capital,
                            transaction_cost=transaction_cost
                        )

                        progress_bar.progress(95)

                        # Display trading metrics
                        st.subheader("Trading Performance")

                        # Check if these are likely dummy results
                        is_dummy_trading = (trading_metrics['total_trades'] == 0 or
                                          (abs(trading_metrics['strategy_return']) < 0.01 and
                                           abs(trading_metrics['market_return']) < 0.01))

                        if is_dummy_trading and not is_dummy_data:  # Only show this if we haven't already shown the dummy data warning
                            st.info("⚠️ Note: Trading metrics are simulated for demonstration purposes. Insufficient trading signals were generated.")

                        col1, col2 = st.columns(2)
                        col1.metric("Strategy Return", f"{trading_metrics['strategy_return']:.2f}%",
                                   f"{trading_metrics['strategy_return'] - trading_metrics['market_return']:.2f}%")
                        col2.metric("Buy & Hold Return", f"{trading_metrics['market_return']:.2f}%")

                        col1, col2, col3 = st.columns(3)
                        col1.metric("Total Trades", f"{trading_metrics['total_trades']:.0f}")
                        col2.metric("Win Rate", f"{trading_metrics['win_rate']:.2f}%")
                        col3.metric("Max Drawdown", f"{trading_metrics['strategy_max_drawdown']:.2f}%")

                        # Plot trading performance
                        fig = plot_trading_performance(trading_metrics)
                        st.plotly_chart(fig, use_container_width=True)

                        progress_bar.progress(100)
                        status_placeholder.success("Backtest completed successfully!")

                        # Display detailed results
                        if st.checkbox("Show detailed results"):
                            st.dataframe(results_df)
                    except Exception as metrics_error:
                        logger.error(f"Error calculating metrics: {str(metrics_error)}")
                        status_placeholder.error(f"Error calculating metrics: {str(metrics_error)}")

                        # Still show the raw results
                        st.subheader("Raw Backtest Results")
                        st.dataframe(results_df.head(20))

            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                logger.error(f"Error running backtest: {str(e)}\n{error_details}")

                # Update progress and status
                progress_bar.progress(100)
                status_placeholder.error(f"Error running backtest: {str(e)}")

                # Always show the basic error message
                st.error(f"Error running backtest: {str(e)}")

                # Provide a more user-friendly error message
                if "No such file or directory" in str(e):
                    st.error(f"Could not find the model file for {symbol} with horizon {selected_horizon}. Please make sure you have trained this model.")
                elif "load_scaler" in str(e):
                    st.error(f"Could not load the scaler for {symbol} with horizon {selected_horizon}. Please retrain the model.")
                elif "AttributeError" in str(e):
                    st.error(f"The model structure seems incompatible. This might happen if the model was trained with a different version of the code.")
                elif "ValueError" in str(e) and "feature" in str(e):
                    st.error(f"Feature mismatch error. The model expects different features than what's provided.")

                # Show detailed error information
                if debug_mode:
                    st.subheader("Debug Information")
                    st.text("Full Error Details:")
                    st.code(error_details)

                    # Show available model files
                    st.text("Available Model Files:")
                    try:
                        model_files = [f for f in os.listdir(models_path) if symbol in f and f.endswith('.joblib')]
                        if model_files:
                            st.code("\n".join(model_files))
                        else:
                            st.warning(f"No model files found for {symbol}")
                    except Exception as list_error:
                        st.error(f"Error listing model files: {str(list_error)}")

                    # Show data sample
                    st.text("Data Sample:")
                    st.dataframe(historical_data.head())
                else:
                    # Show detailed error information in an expandable section
                    with st.expander("Technical Error Details"):
                        st.code(error_details)
