"""
Advanced AI Sentiment Analysis Engine
Analyzes market sentiment from multiple sources using AI/ML techniques
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SentimentScore:
    """Sentiment score data structure"""
    overall_sentiment: float  # -1 to 1 scale
    confidence: float  # 0 to 1 scale
    bullish_signals: int
    bearish_signals: int
    neutral_signals: int
    sources_analyzed: int
    timestamp: datetime
    key_themes: List[str]
    sentiment_breakdown: Dict[str, float]

class AIMarketSentimentAnalyzer:
    """Advanced AI-powered market sentiment analysis"""
    
    def __init__(self):
        self.sentiment_cache = {}
        self.cache_duration = timedelta(minutes=30)  # Cache for 30 minutes
        
        # Sentiment keywords and weights
        self.bullish_keywords = {
            'buy': 2.0, 'bull': 2.0, 'bullish': 2.0, 'rise': 1.5, 'up': 1.0,
            'gain': 1.5, 'profit': 1.5, 'growth': 1.5, 'strong': 1.0, 'positive': 1.0,
            'outperform': 2.0, 'upgrade': 2.0, 'target': 1.0, 'momentum': 1.5,
            'breakout': 2.0, 'rally': 2.0, 'surge': 2.0, 'boom': 1.5
        }
        
        self.bearish_keywords = {
            'sell': 2.0, 'bear': 2.0, 'bearish': 2.0, 'fall': 1.5, 'down': 1.0,
            'loss': 1.5, 'decline': 1.5, 'weak': 1.0, 'negative': 1.0,
            'underperform': 2.0, 'downgrade': 2.0, 'risk': 1.0, 'correction': 1.5,
            'crash': 2.5, 'dump': 2.0, 'collapse': 2.5, 'recession': 2.0
        }
        
        # EGX-specific terms
        self.egx_terms = {
            'egx': 1.0, 'egyptian': 1.0, 'cairo': 1.0, 'egypt': 1.0,
            'egp': 0.5, 'pound': 0.5, 'cib': 1.0, 'commercial': 1.0
        }

    def analyze_stock_sentiment(self, symbol: str, timeframe_days: int = 7) -> SentimentScore:
        """
        Analyze sentiment for a specific stock symbol
        """
        try:
            logger.info(f"Analyzing sentiment for {symbol} over {timeframe_days} days")
            
            # Check cache first
            cache_key = f"{symbol}_{timeframe_days}"
            if self._is_cache_valid(cache_key):
                logger.info(f"Returning cached sentiment for {symbol}")
                return self.sentiment_cache[cache_key]['data']
            
            # Gather sentiment data from multiple sources
            sentiment_data = self._gather_sentiment_data(symbol, timeframe_days)
            
            # Analyze sentiment using AI techniques
            sentiment_score = self._calculate_ai_sentiment(sentiment_data, symbol)
            
            # Cache the result
            self.sentiment_cache[cache_key] = {
                'data': sentiment_score,
                'timestamp': datetime.now()
            }
            
            logger.info(f"Sentiment analysis complete for {symbol}: {sentiment_score.overall_sentiment:.3f}")
            return sentiment_score
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment for {symbol}: {str(e)}")
            return self._create_neutral_sentiment(symbol)

    def _gather_sentiment_data(self, symbol: str, timeframe_days: int) -> Dict:
        """Gather sentiment data from multiple sources"""
        
        sentiment_data = {
            'news_headlines': [],
            'social_mentions': [],
            'analyst_reports': [],
            'market_data': {},
            'volume_analysis': {},
            'price_action': {}
        }
        
        try:
            # 1. Simulate news headlines analysis (in real implementation, use news APIs)
            sentiment_data['news_headlines'] = self._simulate_news_sentiment(symbol)
            
            # 2. Analyze price action sentiment
            sentiment_data['price_action'] = self._analyze_price_action_sentiment(symbol, timeframe_days)
            
            # 3. Volume analysis for sentiment
            sentiment_data['volume_analysis'] = self._analyze_volume_sentiment(symbol, timeframe_days)
            
            # 4. Technical indicator sentiment
            sentiment_data['technical_sentiment'] = self._analyze_technical_sentiment(symbol)
            
            # 5. Market correlation sentiment
            sentiment_data['correlation_sentiment'] = self._analyze_correlation_sentiment(symbol)
            
        except Exception as e:
            logger.error(f"Error gathering sentiment data: {str(e)}")
        
        return sentiment_data

    def _simulate_news_sentiment(self, symbol: str) -> List[Dict]:
        """Simulate news sentiment analysis (replace with real news API in production)"""
        
        # Simulate realistic news headlines and sentiment
        simulated_news = [
            {
                'headline': f'{symbol} shows strong quarterly performance with increased revenue',
                'sentiment': 0.7,
                'confidence': 0.8,
                'source': 'Financial News',
                'timestamp': datetime.now() - timedelta(hours=2)
            },
            {
                'headline': f'Analysts maintain positive outlook on {symbol} despite market volatility',
                'sentiment': 0.4,
                'confidence': 0.6,
                'source': 'Market Analysis',
                'timestamp': datetime.now() - timedelta(hours=6)
            },
            {
                'headline': f'EGX market conditions favor {symbol} sector growth',
                'sentiment': 0.3,
                'confidence': 0.7,
                'source': 'Sector Report',
                'timestamp': datetime.now() - timedelta(hours=12)
            },
            {
                'headline': f'{symbol} trading volume increases amid investor interest',
                'sentiment': 0.5,
                'confidence': 0.5,
                'source': 'Trading News',
                'timestamp': datetime.now() - timedelta(hours=18)
            }
        ]
        
        return simulated_news

    def _analyze_price_action_sentiment(self, symbol: str, timeframe_days: int) -> Dict:
        """Analyze price action to determine sentiment"""
        
        try:
            # Try to get real price data from CSV files
            csv_path = f"data/stocks/{symbol}.csv"
            
            try:
                df = pd.read_csv(csv_path)
                df['Date'] = pd.to_datetime(df['Date'])
                
                # Get recent data
                recent_data = df.tail(timeframe_days)
                
                if len(recent_data) > 1:
                    # Calculate price momentum
                    price_change = (recent_data['Close'].iloc[-1] - recent_data['Close'].iloc[0]) / recent_data['Close'].iloc[0]
                    
                    # Calculate volatility
                    volatility = recent_data['Close'].pct_change().std()
                    
                    # Calculate trend strength
                    trend_strength = abs(price_change)
                    
                    # Determine sentiment based on price action
                    if price_change > 0.05:  # 5% gain
                        sentiment = min(0.8, price_change * 10)
                    elif price_change < -0.05:  # 5% loss
                        sentiment = max(-0.8, price_change * 10)
                    else:
                        sentiment = price_change * 5
                    
                    return {
                        'price_sentiment': sentiment,
                        'price_change': price_change,
                        'volatility': volatility,
                        'trend_strength': trend_strength,
                        'confidence': min(0.9, trend_strength * 2)
                    }
                    
            except FileNotFoundError:
                logger.warning(f"CSV file not found for {symbol}, using simulated data")
            
            # Fallback to simulated data
            return self._simulate_price_sentiment()
            
        except Exception as e:
            logger.error(f"Error analyzing price action sentiment: {str(e)}")
            return self._simulate_price_sentiment()

    def _simulate_price_sentiment(self) -> Dict:
        """Simulate price sentiment when real data is not available"""
        
        # Generate realistic price sentiment
        price_change = np.random.normal(0, 0.03)  # 3% standard deviation
        volatility = abs(np.random.normal(0.02, 0.01))  # 2% average volatility
        
        sentiment = np.tanh(price_change * 10)  # Normalize to -1 to 1
        confidence = min(0.8, abs(price_change) * 20)
        
        return {
            'price_sentiment': sentiment,
            'price_change': price_change,
            'volatility': volatility,
            'trend_strength': abs(price_change),
            'confidence': confidence
        }

    def _analyze_volume_sentiment(self, symbol: str, timeframe_days: int) -> Dict:
        """Analyze volume patterns for sentiment indicators"""
        
        try:
            csv_path = f"data/stocks/{symbol}.csv"
            
            try:
                df = pd.read_csv(csv_path)
                recent_data = df.tail(timeframe_days * 2)  # Get more data for comparison
                
                if len(recent_data) > timeframe_days:
                    recent_volume = recent_data['Volume'].tail(timeframe_days).mean()
                    historical_volume = recent_data['Volume'].head(timeframe_days).mean()
                    
                    volume_ratio = recent_volume / historical_volume if historical_volume > 0 else 1.0
                    
                    # High volume with price increase = bullish
                    # High volume with price decrease = bearish
                    # Low volume = neutral/uncertain
                    
                    if volume_ratio > 1.2:  # 20% above average
                        volume_sentiment = 0.3 if volume_ratio < 2.0 else 0.6
                    elif volume_ratio < 0.8:  # 20% below average
                        volume_sentiment = -0.2
                    else:
                        volume_sentiment = 0.0
                    
                    return {
                        'volume_sentiment': volume_sentiment,
                        'volume_ratio': volume_ratio,
                        'confidence': min(0.7, abs(volume_ratio - 1.0))
                    }
                    
            except FileNotFoundError:
                pass
            
            # Fallback simulation
            volume_ratio = np.random.lognormal(0, 0.3)  # Log-normal distribution for volume
            volume_sentiment = np.tanh((volume_ratio - 1.0) * 2)
            
            return {
                'volume_sentiment': volume_sentiment,
                'volume_ratio': volume_ratio,
                'confidence': min(0.6, abs(volume_ratio - 1.0))
            }
            
        except Exception as e:
            logger.error(f"Error analyzing volume sentiment: {str(e)}")
            return {'volume_sentiment': 0.0, 'volume_ratio': 1.0, 'confidence': 0.0}

    def _analyze_technical_sentiment(self, symbol: str) -> Dict:
        """Analyze technical indicators for sentiment"""
        
        # Simulate technical indicator sentiment
        # In real implementation, calculate actual RSI, MACD, etc.
        
        rsi = np.random.normal(50, 15)  # RSI around 50
        rsi = max(0, min(100, rsi))  # Clamp to 0-100
        
        macd = np.random.normal(0, 0.5)  # MACD around 0
        
        # Convert technical indicators to sentiment
        rsi_sentiment = (rsi - 50) / 50  # -1 to 1 scale
        macd_sentiment = np.tanh(macd)  # -1 to 1 scale
        
        # Combine technical sentiments
        technical_sentiment = (rsi_sentiment + macd_sentiment) / 2
        
        return {
            'technical_sentiment': technical_sentiment,
            'rsi': rsi,
            'macd': macd,
            'confidence': 0.6
        }

    def _analyze_correlation_sentiment(self, symbol: str) -> Dict:
        """Analyze market correlation for sentiment"""
        
        # Simulate market correlation sentiment
        market_correlation = np.random.normal(0.7, 0.2)  # Usually positive correlation
        market_sentiment = np.random.normal(0, 0.3)  # Market sentiment
        
        correlation_sentiment = market_correlation * market_sentiment
        
        return {
            'correlation_sentiment': correlation_sentiment,
            'market_correlation': market_correlation,
            'market_sentiment': market_sentiment,
            'confidence': 0.5
        }

    def _calculate_ai_sentiment(self, sentiment_data: Dict, symbol: str) -> SentimentScore:
        """Calculate overall AI sentiment score using advanced algorithms"""
        
        try:
            # Extract individual sentiment components
            news_sentiment = self._calculate_news_sentiment(sentiment_data.get('news_headlines', []))
            price_sentiment = sentiment_data.get('price_action', {}).get('price_sentiment', 0.0)
            volume_sentiment = sentiment_data.get('volume_analysis', {}).get('volume_sentiment', 0.0)
            technical_sentiment = sentiment_data.get('technical_sentiment', {}).get('technical_sentiment', 0.0)
            correlation_sentiment = sentiment_data.get('correlation_sentiment', {}).get('correlation_sentiment', 0.0)
            
            # Weighted combination of sentiments
            weights = {
                'news': 0.25,
                'price': 0.30,
                'volume': 0.20,
                'technical': 0.15,
                'correlation': 0.10
            }
            
            overall_sentiment = (
                news_sentiment * weights['news'] +
                price_sentiment * weights['price'] +
                volume_sentiment * weights['volume'] +
                technical_sentiment * weights['technical'] +
                correlation_sentiment * weights['correlation']
            )
            
            # Clamp to -1 to 1 range
            overall_sentiment = max(-1.0, min(1.0, overall_sentiment))
            
            # Calculate confidence based on agreement between sources
            sentiments = [news_sentiment, price_sentiment, volume_sentiment, technical_sentiment, correlation_sentiment]
            sentiment_std = np.std(sentiments)
            confidence = max(0.1, 1.0 - sentiment_std)  # Higher agreement = higher confidence
            
            # Count signals
            bullish_signals = sum(1 for s in sentiments if s > 0.1)
            bearish_signals = sum(1 for s in sentiments if s < -0.1)
            neutral_signals = len(sentiments) - bullish_signals - bearish_signals
            
            # Extract key themes
            key_themes = self._extract_key_themes(sentiment_data, overall_sentiment)
            
            # Create sentiment breakdown
            sentiment_breakdown = {
                'news_sentiment': news_sentiment,
                'price_action': price_sentiment,
                'volume_analysis': volume_sentiment,
                'technical_indicators': technical_sentiment,
                'market_correlation': correlation_sentiment
            }
            
            return SentimentScore(
                overall_sentiment=overall_sentiment,
                confidence=confidence,
                bullish_signals=bullish_signals,
                bearish_signals=bearish_signals,
                neutral_signals=neutral_signals,
                sources_analyzed=len(sentiments),
                timestamp=datetime.now(),
                key_themes=key_themes,
                sentiment_breakdown=sentiment_breakdown
            )
            
        except Exception as e:
            logger.error(f"Error calculating AI sentiment: {str(e)}")
            return self._create_neutral_sentiment(symbol)

    def _calculate_news_sentiment(self, news_data: List[Dict]) -> float:
        """Calculate sentiment from news headlines"""
        
        if not news_data:
            return 0.0
        
        total_sentiment = 0.0
        total_weight = 0.0
        
        for news_item in news_data:
            sentiment = news_item.get('sentiment', 0.0)
            confidence = news_item.get('confidence', 0.5)
            
            # Weight by confidence and recency
            hours_old = (datetime.now() - news_item.get('timestamp', datetime.now())).total_seconds() / 3600
            recency_weight = max(0.1, 1.0 - (hours_old / 24))  # Decay over 24 hours
            
            weight = confidence * recency_weight
            total_sentiment += sentiment * weight
            total_weight += weight
        
        return total_sentiment / total_weight if total_weight > 0 else 0.0

    def _extract_key_themes(self, sentiment_data: Dict, overall_sentiment: float) -> List[str]:
        """Extract key themes from sentiment analysis"""
        
        themes = []
        
        # Add themes based on sentiment components
        if overall_sentiment > 0.3:
            themes.append("Bullish Momentum")
        elif overall_sentiment < -0.3:
            themes.append("Bearish Pressure")
        else:
            themes.append("Neutral Sentiment")
        
        # Add specific themes based on data
        price_data = sentiment_data.get('price_action', {})
        if price_data.get('volatility', 0) > 0.05:
            themes.append("High Volatility")
        
        volume_data = sentiment_data.get('volume_analysis', {})
        if volume_data.get('volume_ratio', 1.0) > 1.5:
            themes.append("Increased Volume")
        
        technical_data = sentiment_data.get('technical_sentiment', {})
        if technical_data.get('rsi', 50) > 70:
            themes.append("Overbought Conditions")
        elif technical_data.get('rsi', 50) < 30:
            themes.append("Oversold Conditions")
        
        return themes[:5]  # Limit to top 5 themes

    def _create_neutral_sentiment(self, symbol: str) -> SentimentScore:
        """Create a neutral sentiment score as fallback"""
        
        return SentimentScore(
            overall_sentiment=0.0,
            confidence=0.3,
            bullish_signals=0,
            bearish_signals=0,
            neutral_signals=1,
            sources_analyzed=1,
            timestamp=datetime.now(),
            key_themes=["Insufficient Data"],
            sentiment_breakdown={'fallback': 0.0}
        )

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached sentiment data is still valid"""
        
        if cache_key not in self.sentiment_cache:
            return False
        
        cache_time = self.sentiment_cache[cache_key]['timestamp']
        return datetime.now() - cache_time < self.cache_duration

    def get_market_sentiment_summary(self, symbols: List[str]) -> Dict:
        """Get overall market sentiment summary for multiple symbols"""
        
        try:
            logger.info(f"Analyzing market sentiment for {len(symbols)} symbols")
            
            market_sentiments = []
            symbol_sentiments = {}
            
            for symbol in symbols:
                sentiment = self.analyze_stock_sentiment(symbol)
                symbol_sentiments[symbol] = sentiment
                market_sentiments.append(sentiment.overall_sentiment)
            
            # Calculate market-wide metrics
            avg_sentiment = np.mean(market_sentiments)
            sentiment_volatility = np.std(market_sentiments)
            
            bullish_count = sum(1 for s in market_sentiments if s > 0.1)
            bearish_count = sum(1 for s in market_sentiments if s < -0.1)
            neutral_count = len(market_sentiments) - bullish_count - bearish_count
            
            return {
                'market_sentiment': avg_sentiment,
                'sentiment_volatility': sentiment_volatility,
                'bullish_stocks': bullish_count,
                'bearish_stocks': bearish_count,
                'neutral_stocks': neutral_count,
                'total_stocks': len(symbols),
                'symbol_sentiments': symbol_sentiments,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error calculating market sentiment summary: {str(e)}")
            return {
                'market_sentiment': 0.0,
                'sentiment_volatility': 0.0,
                'bullish_stocks': 0,
                'bearish_stocks': 0,
                'neutral_stocks': len(symbols),
                'total_stocks': len(symbols),
                'symbol_sentiments': {},
                'timestamp': datetime.now()
            }
