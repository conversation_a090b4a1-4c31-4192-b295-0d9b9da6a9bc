import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from typing import Dict, List, Optional, Union, Tuple, Any
import logging
from plotly.subplots import make_subplots

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """
    A class for calculating and visualizing technical indicators.
    """

    @staticmethod
    def add_moving_averages(df: pd.DataFrame, column: str = 'Close', periods: List[int] = [5, 10, 20, 50, 200]) -> pd.DataFrame:
        """
        Add Simple Moving Averages (SMA) to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            column (str): Column to calculate moving averages for
            periods (List[int]): List of periods to calculate

        Returns:
            pd.DataFrame: Dataframe with added moving averages
        """
        result = df.copy()

        for period in periods:
            result[f'SMA_{period}'] = result[column].rolling(window=period).mean()

        return result

    @staticmethod
    def add_exponential_moving_averages(df: pd.DataFrame, column: str = 'Close', periods: List[int] = [5, 10, 20, 50, 200]) -> pd.DataFrame:
        """
        Add Exponential Moving Averages (EMA) to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            column (str): Column to calculate moving averages for
            periods (List[int]): List of periods to calculate

        Returns:
            pd.DataFrame: Dataframe with added exponential moving averages
        """
        result = df.copy()

        for period in periods:
            result[f'EMA_{period}'] = result[column].ewm(span=period, adjust=False).mean()

        return result

    @staticmethod
    def add_weighted_moving_averages(df: pd.DataFrame, column: str = 'Close', periods: List[int] = [5, 10, 20]) -> pd.DataFrame:
        """
        Add Weighted Moving Averages (WMA) to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            column (str): Column to calculate moving averages for
            periods (List[int]): List of periods to calculate

        Returns:
            pd.DataFrame: Dataframe with added weighted moving averages
        """
        result = df.copy()

        for period in periods:
            weights = np.arange(1, period + 1)
            result[f'WMA_{period}'] = result[column].rolling(period).apply(
                lambda x: np.sum(weights * x) / weights.sum(), raw=True
            )

        return result

    @staticmethod
    def add_bollinger_bands(df: pd.DataFrame, column: str = 'Close', period: int = 20, std_dev: float = 2.0) -> pd.DataFrame:
        """
        Add Bollinger Bands to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            column (str): Column to calculate Bollinger Bands for
            period (int): Period for the moving average
            std_dev (float): Number of standard deviations for the bands

        Returns:
            pd.DataFrame: Dataframe with added Bollinger Bands
        """
        result = df.copy()

        # Calculate middle band (SMA)
        result[f'BB_Middle_{period}'] = result[column].rolling(window=period).mean()

        # Calculate standard deviation
        rolling_std = result[column].rolling(window=period).std()

        # Calculate upper and lower bands
        result[f'BB_Upper_{period}'] = result[f'BB_Middle_{period}'] + (rolling_std * std_dev)
        result[f'BB_Lower_{period}'] = result[f'BB_Middle_{period}'] - (rolling_std * std_dev)

        # Calculate bandwidth
        result[f'BB_Width_{period}'] = (result[f'BB_Upper_{period}'] - result[f'BB_Lower_{period}']) / result[f'BB_Middle_{period}']

        return result

    @staticmethod
    def add_rsi(df: pd.DataFrame, column: str = 'Close', period: int = 14) -> pd.DataFrame:
        """
        Add Relative Strength Index (RSI) to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            column (str): Column to calculate RSI for
            period (int): Period for RSI calculation

        Returns:
            pd.DataFrame: Dataframe with added RSI
        """
        result = df.copy()

        # Calculate price changes
        delta = result[column].diff()

        # Separate gains and losses
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # Calculate average gain and loss
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()

        # Calculate RS and RSI
        rs = avg_gain / avg_loss
        result[f'RSI_{period}'] = 100 - (100 / (1 + rs))

        return result

    @staticmethod
    def add_macd(df: pd.DataFrame, column: str = 'Close', fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> pd.DataFrame:
        """
        Add Moving Average Convergence Divergence (MACD) to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            column (str): Column to calculate MACD for
            fast_period (int): Period for the fast EMA
            slow_period (int): Period for the slow EMA
            signal_period (int): Period for the signal line

        Returns:
            pd.DataFrame: Dataframe with added MACD
        """
        result = df.copy()

        # Calculate fast and slow EMAs
        fast_ema = result[column].ewm(span=fast_period, adjust=False).mean()
        slow_ema = result[column].ewm(span=slow_period, adjust=False).mean()

        # Calculate MACD line
        result['MACD_Line'] = fast_ema - slow_ema

        # Calculate signal line
        result['MACD_Signal'] = result['MACD_Line'].ewm(span=signal_period, adjust=False).mean()

        # Calculate histogram
        result['MACD_Histogram'] = result['MACD_Line'] - result['MACD_Signal']

        return result

    @staticmethod
    def add_stochastic_oscillator(df: pd.DataFrame, high_col: str = 'High', low_col: str = 'Low', close_col: str = 'Close', k_period: int = 14, d_period: int = 3) -> pd.DataFrame:
        """
        Add Stochastic Oscillator to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            high_col (str): Column with high prices
            low_col (str): Column with low prices
            close_col (str): Column with close prices
            k_period (int): Period for %K
            d_period (int): Period for %D

        Returns:
            pd.DataFrame: Dataframe with added Stochastic Oscillator
        """
        result = df.copy()

        # Calculate %K
        lowest_low = result[low_col].rolling(window=k_period).min()
        highest_high = result[high_col].rolling(window=k_period).max()
        result['Stoch_%K'] = 100 * ((result[close_col] - lowest_low) / (highest_high - lowest_low))

        # Calculate %D
        result['Stoch_%D'] = result['Stoch_%K'].rolling(window=d_period).mean()

        return result

    @staticmethod
    def add_average_directional_index(df: pd.DataFrame, high_col: str = 'High', low_col: str = 'Low', close_col: str = 'Close', period: int = 14) -> pd.DataFrame:
        """
        Add Average Directional Index (ADX) to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            high_col (str): Column with high prices
            low_col (str): Column with low prices
            close_col (str): Column with close prices
            period (int): Period for ADX calculation

        Returns:
            pd.DataFrame: Dataframe with added ADX
        """
        result = df.copy()

        # Calculate True Range
        result['TR'] = np.maximum(
            np.maximum(
                result[high_col] - result[low_col],
                np.abs(result[high_col] - result[close_col].shift(1))
            ),
            np.abs(result[low_col] - result[close_col].shift(1))
        )

        # Calculate Directional Movement
        result['DM+'] = np.where(
            (result[high_col] - result[high_col].shift(1)) > (result[low_col].shift(1) - result[low_col]),
            np.maximum(result[high_col] - result[high_col].shift(1), 0),
            0
        )

        result['DM-'] = np.where(
            (result[low_col].shift(1) - result[low_col]) > (result[high_col] - result[high_col].shift(1)),
            np.maximum(result[low_col].shift(1) - result[low_col], 0),
            0
        )

        # Calculate Smoothed TR and DM
        result['ATR'] = result['TR'].rolling(window=period).mean()
        result['DM+_Smooth'] = result['DM+'].rolling(window=period).mean()
        result['DM-_Smooth'] = result['DM-'].rolling(window=period).mean()

        # Calculate Directional Indicators
        result['DI+'] = 100 * (result['DM+_Smooth'] / result['ATR'])
        result['DI-'] = 100 * (result['DM-_Smooth'] / result['ATR'])

        # Calculate Directional Index
        result['DX'] = 100 * np.abs(result['DI+'] - result['DI-']) / (result['DI+'] + result['DI-'])

        # Calculate ADX
        result[f'ADX_{period}'] = result['DX'].rolling(window=period).mean()

        # Clean up intermediate columns
        result = result.drop(columns=['TR', 'DM+', 'DM-', 'ATR', 'DM+_Smooth', 'DM-_Smooth', 'DI+', 'DI-', 'DX'])

        return result

    @staticmethod
    def add_on_balance_volume(df: pd.DataFrame, close_col: str = 'Close', volume_col: str = 'Volume') -> pd.DataFrame:
        """
        Add On-Balance Volume (OBV) to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price and volume data
            close_col (str): Column with close prices
            volume_col (str): Column with volume data

        Returns:
            pd.DataFrame: Dataframe with added OBV
        """
        result = df.copy()

        if volume_col not in result.columns:
            logger.warning(f"Volume column '{volume_col}' not found in dataframe. Skipping OBV calculation.")
            return result

        # Calculate price direction
        result['Direction'] = np.where(result[close_col] > result[close_col].shift(1), 1,
                                      np.where(result[close_col] < result[close_col].shift(1), -1, 0))

        # Calculate OBV
        result['OBV'] = (result['Direction'] * result[volume_col]).cumsum()

        # Clean up intermediate columns
        result = result.drop(columns=['Direction'])

        return result

    @staticmethod
    def add_money_flow_index(df: pd.DataFrame, high_col: str = 'High', low_col: str = 'Low', close_col: str = 'Close', volume_col: str = 'Volume', period: int = 14) -> pd.DataFrame:
        """
        Add Money Flow Index (MFI) to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price and volume data
            high_col (str): Column with high prices
            low_col (str): Column with low prices
            close_col (str): Column with close prices
            volume_col (str): Column with volume data
            period (int): Period for MFI calculation

        Returns:
            pd.DataFrame: Dataframe with added MFI
        """
        result = df.copy()

        if volume_col not in result.columns:
            logger.warning(f"Volume column '{volume_col}' not found in dataframe. Skipping MFI calculation.")
            return result

        # Calculate typical price
        result['TypicalPrice'] = (result[high_col] + result[low_col] + result[close_col]) / 3

        # Calculate raw money flow
        result['RawMoneyFlow'] = result['TypicalPrice'] * result[volume_col]

        # Calculate money flow direction
        result['MoneyFlowDirection'] = np.where(result['TypicalPrice'] > result['TypicalPrice'].shift(1), 1, -1)

        # Calculate positive and negative money flow
        result['PositiveMoneyFlow'] = np.where(result['MoneyFlowDirection'] > 0, result['RawMoneyFlow'], 0)
        result['NegativeMoneyFlow'] = np.where(result['MoneyFlowDirection'] < 0, result['RawMoneyFlow'], 0)

        # Calculate positive and negative money flow sum over period
        result['PositiveMoneyFlowSum'] = result['PositiveMoneyFlow'].rolling(window=period).sum()
        result['NegativeMoneyFlowSum'] = result['NegativeMoneyFlow'].rolling(window=period).sum()

        # Calculate money flow ratio
        result['MoneyFlowRatio'] = result['PositiveMoneyFlowSum'] / result['NegativeMoneyFlowSum']

        # Calculate MFI
        result[f'MFI_{period}'] = 100 - (100 / (1 + result['MoneyFlowRatio']))

        # Clean up intermediate columns
        result = result.drop(columns=[
            'TypicalPrice', 'RawMoneyFlow', 'MoneyFlowDirection',
            'PositiveMoneyFlow', 'NegativeMoneyFlow',
            'PositiveMoneyFlowSum', 'NegativeMoneyFlowSum', 'MoneyFlowRatio'
        ])

        return result

    @staticmethod
    def add_ichimoku_cloud(df: pd.DataFrame, high_col: str = 'High', low_col: str = 'Low', conversion_period: int = 9, base_period: int = 26, lagging_span_period: int = 52, displacement: int = 26) -> pd.DataFrame:
        """
        Add Ichimoku Cloud to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data
            high_col (str): Column with high prices
            low_col (str): Column with low prices
            conversion_period (int): Period for Tenkan-sen (Conversion Line)
            base_period (int): Period for Kijun-sen (Base Line)
            lagging_span_period (int): Period for Senkou Span B (Leading Span B)
            displacement (int): Displacement period

        Returns:
            pd.DataFrame: Dataframe with added Ichimoku Cloud
        """
        result = df.copy()

        # Calculate Tenkan-sen (Conversion Line)
        tenkan_high = result[high_col].rolling(window=conversion_period).max()
        tenkan_low = result[low_col].rolling(window=conversion_period).min()
        result['Ichimoku_Conversion_Line'] = (tenkan_high + tenkan_low) / 2

        # Calculate Kijun-sen (Base Line)
        kijun_high = result[high_col].rolling(window=base_period).max()
        kijun_low = result[low_col].rolling(window=base_period).min()
        result['Ichimoku_Base_Line'] = (kijun_high + kijun_low) / 2

        # Calculate Senkou Span A (Leading Span A)
        result['Ichimoku_Leading_Span_A'] = ((result['Ichimoku_Conversion_Line'] + result['Ichimoku_Base_Line']) / 2).shift(displacement)

        # Calculate Senkou Span B (Leading Span B)
        senkou_high = result[high_col].rolling(window=lagging_span_period).max()
        senkou_low = result[low_col].rolling(window=lagging_span_period).min()
        result['Ichimoku_Leading_Span_B'] = ((senkou_high + senkou_low) / 2).shift(displacement)

        # Calculate Chikou Span (Lagging Span)
        result['Ichimoku_Lagging_Span'] = result['Close'].shift(-displacement)

        return result

    @staticmethod
    def add_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """
        Add all technical indicators to the dataframe.

        Args:
            df (pd.DataFrame): Input dataframe with price data

        Returns:
            pd.DataFrame: Dataframe with all technical indicators
        """
        result = df.copy()

        # Add moving averages
        result = TechnicalIndicators.add_moving_averages(result)
        result = TechnicalIndicators.add_exponential_moving_averages(result)
        result = TechnicalIndicators.add_weighted_moving_averages(result)

        # Add Bollinger Bands
        result = TechnicalIndicators.add_bollinger_bands(result)

        # Add oscillators
        result = TechnicalIndicators.add_rsi(result)
        result = TechnicalIndicators.add_macd(result)
        result = TechnicalIndicators.add_stochastic_oscillator(result)

        # Add trend indicators
        result = TechnicalIndicators.add_average_directional_index(result)

        # Add volume indicators if volume data is available
        if 'Volume' in result.columns:
            result = TechnicalIndicators.add_on_balance_volume(result)
            result = TechnicalIndicators.add_money_flow_index(result)

        # Add Ichimoku Cloud
        result = TechnicalIndicators.add_ichimoku_cloud(result)

        return result

    @staticmethod
    def plot_indicators(df: pd.DataFrame, indicators: Dict[str, List[str]], title: str = 'Technical Indicators', figsize: Tuple[int, int] = (12, 8)) -> plt.Figure:
        """
        Plot technical indicators.

        Args:
            df (pd.DataFrame): Input dataframe with indicators
            indicators (Dict[str, List[str]]): Dictionary mapping subplot names to lists of indicators to plot
            title (str): Plot title
            figsize (Tuple[int, int]): Figure size

        Returns:
            plt.Figure: Matplotlib figure
        """
        n_subplots = len(indicators)
        fig, axes = plt.subplots(n_subplots, 1, figsize=figsize, sharex=True)

        if n_subplots == 1:
            axes = [axes]

        for i, (subplot_name, indicator_list) in enumerate(indicators.items()):
            for indicator in indicator_list:
                if indicator in df.columns:
                    axes[i].plot(df.index, df[indicator], label=indicator)

            axes[i].set_title(subplot_name)
            axes[i].legend()
            axes[i].grid(True)

        plt.suptitle(title)
        plt.tight_layout()

        return fig

    @staticmethod
    def plot_indicators_plotly(df: pd.DataFrame, indicators: Dict[str, List[str]], title: str = 'Technical Indicators') -> go.Figure:
        """
        Plot technical indicators using Plotly.

        Args:
            df (pd.DataFrame): Input dataframe with indicators
            indicators (Dict[str, List[str]]): Dictionary mapping subplot names to lists of indicators to plot
            title (str): Plot title

        Returns:
            go.Figure: Plotly figure
        """
        n_subplots = len(indicators)
        fig = go.Figure()

        # Create subplot specs
        subplot_titles = list(indicators.keys())
        fig = make_subplots(rows=n_subplots, cols=1, shared_xaxes=True, subplot_titles=subplot_titles)

        # Add traces for each indicator
        for i, (subplot_name, indicator_list) in enumerate(indicators.items()):
            for indicator in indicator_list:
                if indicator in df.columns:
                    fig.add_trace(
                        go.Scatter(
                            x=df.index,
                            y=df[indicator],
                            name=indicator,
                            mode='lines'
                        ),
                        row=i+1,
                        col=1
                    )

        # Update layout
        fig.update_layout(
            title=title,
            height=300 * n_subplots,
            showlegend=True,
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            template="plotly_dark"
        )

        return fig
