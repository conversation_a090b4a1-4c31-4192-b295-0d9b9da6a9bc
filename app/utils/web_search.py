"""
Web search utility for the AI Stocks Bot application.

This module provides functions to search the web for information about stocks,
particularly focused on the Egyptian Exchange (EGX).
"""

import requests
import logging
from bs4 import BeautifulSoup
from typing import List, Dict, Any, Optional
import re
import time
import random
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

def search_web(query: str, num_results: int = 3) -> List[Dict[str, str]]:
    """
    Search the web for information about the given query.

    Args:
        query: The search query
        num_results: Number of results to return

    Returns:
        List of dictionaries with title, url, and snippet
    """
    # Add "EGX" to the query if it's not already there and it looks like a stock query
    if "EGX" not in query and any(term in query.lower() for term in ["stock", "price", "market", "trading", "shares"]):
        query = f"{query} EGX Egypt"

    try:
        # Try multiple search engines and approaches
        results = []

        # First try direct search for financial websites
        financial_sites = [
            f"https://www.investing.com/search/?q={query.replace(' ', '+')}",
            f"https://www.reuters.com/search/news?blob={query.replace(' ', '+')}",
            f"https://www.bloomberg.com/search?query={query.replace(' ', '+')}"
        ]

        for site_url in financial_sites:
            if results:
                break  # Stop if we already have results

            try:
                # Set headers to mimic a browser
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Referer": "https://www.google.com/",
                    "DNT": "1",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                }

                # Make the request with a short timeout
                response = requests.get(site_url, headers=headers, timeout=5)
                response.raise_for_status()

                # Parse the HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # Extract search results based on the site
                if "investing.com" in site_url:
                    search_items = soup.select('.js-inner-all-results-quote-item')
                    for item in search_items[:num_results]:
                        link = item.find('a')
                        if link:
                            title = link.text.strip()
                            url = "https://www.investing.com" + link['href'] if link['href'].startswith('/') else link['href']
                            snippet = item.select_one('.js-inner-all-results-quote-item-desc')
                            snippet_text = snippet.text.strip() if snippet else "No description available"

                            results.append({
                                "title": title,
                                "url": url,
                                "snippet": snippet_text
                            })

                elif "reuters.com" in site_url:
                    search_items = soup.select('.search-result-content')
                    for item in search_items[:num_results]:
                        link = item.find('a')
                        if link:
                            title = link.text.strip()
                            url = "https://www.reuters.com" + link['href'] if link['href'].startswith('/') else link['href']
                            snippet = item.select_one('.search-result-snippet')
                            snippet_text = snippet.text.strip() if snippet else "No description available"

                            results.append({
                                "title": title,
                                "url": url,
                                "snippet": snippet_text
                            })

                elif "bloomberg.com" in site_url:
                    search_items = soup.select('.search-result')
                    for item in search_items[:num_results]:
                        link = item.find('a')
                        if link:
                            title = link.text.strip()
                            url = "https://www.bloomberg.com" + link['href'] if link['href'].startswith('/') else link['href']
                            snippet = item.select_one('.search-result__description')
                            snippet_text = snippet.text.strip() if snippet else "No description available"

                            results.append({
                                "title": title,
                                "url": url,
                                "snippet": snippet_text
                            })

            except Exception as e:
                logger.warning(f"Error searching {site_url}: {str(e)}")
                continue

        # If we still don't have results, try a general Google search
        if not results:
            # Use a simple Google search
            search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"

            # Set headers to mimic a browser
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Referer": "https://www.google.com/",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }

            # Make the request
            response = requests.get(search_url, headers=headers, timeout=10)
            response.raise_for_status()

            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # Try multiple selectors for Google search results
            selectors = [
                'div.g',  # Standard Google result container
                'div.xpd',  # Alternative container
                'div.Gx5Zad',  # Newer Google result container
                'div.tF2Cxc',  # Another container variant
                'div.yuRUbf',  # Title container in newer layouts
                'div.kCrYT'    # Mobile results container
            ]

            for selector in selectors:
                search_divs = soup.select(selector)
                if search_divs:
                    break

            for div in search_divs[:num_results]:
                try:
                    # Try multiple selectors for title
                    title_elem = None
                    for title_selector in ['h3', 'h3.LC20lb', 'div.vvjwJb']:
                        title_elem = div.select_one(title_selector)
                        if title_elem:
                            break

                    title = title_elem.text.strip() if title_elem else "No title"

                    # Try multiple selectors for links
                    link_elem = None
                    for link_selector in ['a', 'a.C8nzq', 'a.cz88Hc']:
                        link_elem = div.select_one(link_selector)
                        if link_elem and 'href' in link_elem.attrs:
                            break

                    url = link_elem['href'] if link_elem and 'href' in link_elem.attrs else ""

                    # Clean URL (remove Google redirects)
                    if url.startswith('/url?'):
                        match = re.search(r'url\?q=([^&]+)', url)
                        if match:
                            url = match.group(1)

                    # Try multiple selectors for snippets
                    snippet_elem = None
                    for snippet_selector in ['div.VwiC3b', 'span.aCOpRe', 'div.s3v9rd', 'div.Uroaid']:
                        snippet_elem = div.select_one(snippet_selector)
                        if snippet_elem:
                            break

                    snippet = snippet_elem.text.strip() if snippet_elem else "No description available"

                    # Only add if we have a valid URL
                    if url and not url.startswith('/'):
                        results.append({
                            "title": title,
                            "url": url,
                            "snippet": snippet
                        })

                    if len(results) >= num_results:
                        break
                except Exception as e:
                    logger.warning(f"Error extracting search result: {str(e)}")
                    continue

        # If we still don't have results, try a fallback approach with a simpler parser
        if not results:
            try:
                # Try a different search engine
                search_url = f"https://www.bing.com/search?q={query.replace(' ', '+')}"

                response = requests.get(search_url, headers=headers, timeout=10)
                response.raise_for_status()

                soup = BeautifulSoup(response.text, 'html.parser')

                # Bing search results
                search_divs = soup.select('li.b_algo')

                for div in search_divs[:num_results]:
                    try:
                        title_elem = div.select_one('h2 a')
                        title = title_elem.text.strip() if title_elem else "No title"

                        url = title_elem['href'] if title_elem and 'href' in title_elem.attrs else ""

                        snippet_elem = div.select_one('p')
                        snippet = snippet_elem.text.strip() if snippet_elem else "No description available"

                        if url:
                            results.append({
                                "title": title,
                                "url": url,
                                "snippet": snippet
                            })

                        if len(results) >= num_results:
                            break
                    except Exception as e:
                        logger.warning(f"Error extracting Bing search result: {str(e)}")
                        continue
            except Exception as e:
                logger.warning(f"Error with fallback search: {str(e)}")

        # If we still have no results, create a mock result
        if not results:
            results = [{
                "title": f"Information about {query}",
                "url": "https://www.egx.com.eg/en/homepage.aspx",
                "snippet": "Could not find specific information. Please check the Egyptian Exchange (EGX) official website for the most accurate information."
            }]

        return results

    except Exception as e:
        logger.error(f"Error searching the web: {str(e)}")
        # Return a fallback result
        return [{
            "title": f"Information about {query}",
            "url": "https://www.egx.com.eg/en/homepage.aspx",
            "snippet": "Could not find specific information due to a technical error. Please check the Egyptian Exchange (EGX) official website for the most accurate information."
        }]

def fetch_webpage_content(url: str) -> Optional[str]:
    """
    Fetch the content of a webpage.

    Args:
        url: The URL to fetch

    Returns:
        The content of the webpage as a string, or None if an error occurs
    """
    try:
        # Set headers to mimic a browser
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Referer": "https://www.google.com/",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }

        # Make the request
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # Parse the HTML
        soup = BeautifulSoup(response.text, 'html.parser')

        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.extract()

        # Get text
        text = soup.get_text()

        # Break into lines and remove leading and trailing space on each
        lines = (line.strip() for line in text.splitlines())

        # Break multi-headlines into a line each
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))

        # Drop blank lines
        text = '\n'.join(chunk for chunk in chunks if chunk)

        return text

    except Exception as e:
        logger.error(f"Error fetching webpage content: {str(e)}")
        return None

def get_stock_news(symbol: str, num_results: int = 3) -> List[Dict[str, str]]:
    """
    Get news about a specific stock.

    Args:
        symbol: The stock symbol
        num_results: Number of results to return

    Returns:
        List of dictionaries with title, url, and snippet
    """
    # Search for news about the stock
    query = f"{symbol} stock news EGX Egypt"

    try:
        results = search_web(query, num_results)
        return results
    except Exception as e:
        logger.error(f"Error getting stock news: {str(e)}")
        return []

def get_market_overview() -> str:
    """
    Get an overview of the Egyptian stock market.

    Returns:
        A string with market information
    """
    try:
        # Try to get market information from multiple sources
        sources = [
            "https://www.egx.com.eg/en/homepage.aspx",
            "https://www.mubasher.info/markets/EGX/indices/overview",
            "https://www.investing.com/indices/egypt-indices"
        ]

        market_info = []

        for source in sources:
            try:
                # Set headers to mimic a browser
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Referer": "https://www.google.com/",
                    "DNT": "1",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                }

                # Make the request with a short timeout
                response = requests.get(source, headers=headers, timeout=5)
                response.raise_for_status()

                # Parse the HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # Extract market information based on the source
                if "egx.com.eg" in source:
                    # Try to find the market summary on the EGX website
                    market_summary = soup.select_one('.market-summary')
                    if market_summary:
                        market_info.append("**EGX Official Website:**")
                        for item in market_summary.select('li'):
                            market_info.append(f"- {item.text.strip()}")

                elif "mubasher.info" in source:
                    # Try to find the market indices on Mubasher
                    indices = soup.select('.indices-table tr')
                    if indices:
                        market_info.append("\n**Mubasher Market Indices:**")
                        for index in indices[1:4]:  # Get the first 3 indices
                            cells = index.select('td')
                            if len(cells) >= 3:
                                name = cells[0].text.strip()
                                value = cells[1].text.strip()
                                change = cells[2].text.strip()
                                market_info.append(f"- {name}: {value} ({change})")

                elif "investing.com" in source:
                    # Try to find the market indices on Investing.com
                    indices = soup.select('.datatable_table__tbody tr')
                    if indices:
                        market_info.append("\n**Investing.com Market Indices:**")
                        for index in indices[:3]:  # Get the first 3 indices
                            cells = index.select('td')
                            if len(cells) >= 5:
                                name = cells[1].text.strip()
                                value = cells[2].text.strip()
                                change = cells[4].text.strip()
                                market_info.append(f"- {name}: {value} ({change})")

                # If we got some information, we can stop
                if len(market_info) > 1:
                    break

            except Exception as e:
                logger.warning(f"Error getting market information from {source}: {str(e)}")
                continue

        # If we couldn't get market information from any source, try a web search
        if not market_info:
            results = search_web("Egyptian stock market EGX30 current", 2)

            if results:
                market_info.append("**Market Information:**")
                for result in results:
                    market_info.append(f"- {result['title']}")
                    market_info.append(f"  {result['snippet']}")

        # If we still don't have any information, return a fallback message
        if not market_info:
            return "I couldn't find current information about the Egyptian market. Please check the Egyptian Exchange (EGX) official website for the most accurate information: https://www.egx.com.eg/en/homepage.aspx"

        # Add the current date and time
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        market_info.insert(0, f"**Market Overview as of {current_time}**\n")

        # Add a disclaimer
        market_info.append("\n*Note: This information may not be real-time and is provided for informational purposes only.*")

        return "\n".join(market_info)

    except Exception as e:
        logger.error(f"Error getting market overview: {str(e)}")
        return "I couldn't find current information about the Egyptian market due to a technical error. Please check the Egyptian Exchange (EGX) official website for the most accurate information: https://www.egx.com.eg/en/homepage.aspx"
