import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from typing import Dict, List, Optional, Union, Tuple, Any
import logging
from plotly.subplots import make_subplots

from app.utils.technical_indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class TradingStrategy:
    """
    Base class for trading strategies.
    """

    def __init__(self, name: str = "BaseStrategy"):
        """
        Initialize the trading strategy.

        Args:
            name (str): Name of the strategy
        """
        self.name = name
        self.positions = []
        self.trades = []

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals.

        Args:
            data (pd.DataFrame): Input dataframe with price data

        Returns:
            pd.DataFrame: Dataframe with added signals
        """
        # This method should be implemented by subclasses
        return data

    def backtest(self, data: pd.DataFrame, initial_capital: float = 10000.0, position_size: float = 1.0, commission: float = 0.0) -> Dict[str, Union[pd.DataFrame, float]]:
        """
        Backtest the strategy.

        Args:
            data (pd.DataFrame): Input dataframe with signals
            initial_capital (float): Initial capital
            position_size (float): Position size as a fraction of capital
            commission (float): Commission per trade

        Returns:
            Dict[str, Union[pd.DataFrame, float]]: Backtest results
        """
        # Make a copy of the data
        df = data.copy()

        # Ensure the dataframe has the required columns
        if 'Signal' not in df.columns:
            df = self.generate_signals(df)

        # Initialize portfolio and holdings
        df['Position'] = df['Signal'].shift(1).fillna(0)
        df['Holdings'] = df['Position'] * df['Close']

        # Calculate strategy returns
        df['Strategy_Returns'] = df['Position'].shift(1) * df['Close'].pct_change()

        # Calculate market returns (buy and hold)
        df['Market_Returns'] = df['Close'].pct_change()

        # Calculate portfolio value
        df['Portfolio_Value'] = initial_capital * (1 + df['Strategy_Returns'].fillna(0)).cumprod()
        df['Market_Value'] = initial_capital * (1 + df['Market_Returns'].fillna(0)).cumprod()

        # Calculate drawdowns
        df['Strategy_Peak'] = df['Portfolio_Value'].cummax()
        df['Strategy_Drawdown'] = (df['Portfolio_Value'] - df['Strategy_Peak']) / df['Strategy_Peak']

        df['Market_Peak'] = df['Market_Value'].cummax()
        df['Market_Drawdown'] = (df['Market_Value'] - df['Market_Peak']) / df['Market_Peak']

        # Calculate trade statistics
        trades = self._calculate_trades(df, commission)

        # Calculate performance metrics
        metrics = self._calculate_metrics(df, trades)

        return {
            'data': df,
            'trades': trades,
            'metrics': metrics
        }

    def _calculate_trades(self, data: pd.DataFrame, commission: float = 0.0) -> pd.DataFrame:
        """
        Calculate trade statistics.

        Args:
            data (pd.DataFrame): Input dataframe with positions
            commission (float): Commission per trade

        Returns:
            pd.DataFrame: Dataframe with trade statistics
        """
        # Find position changes
        position_changes = data['Position'].diff().fillna(data['Position'])

        # Find trade entry and exit points
        entries = data[position_changes != 0].copy()
        entries['Type'] = np.where(entries['Position'] > 0, 'Buy', 'Sell')

        # Calculate trade details
        trades = []

        for i in range(len(entries) - 1):
            if entries.iloc[i]['Type'] == 'Buy' and entries.iloc[i+1]['Type'] == 'Sell':
                entry_price = entries.iloc[i]['Close']
                exit_price = entries.iloc[i+1]['Close']
                entry_date = entries.index[i]
                exit_date = entries.index[i+1]

                # Calculate profit/loss
                profit = exit_price - entry_price - 2 * commission
                profit_pct = (profit / entry_price) * 100

                trades.append({
                    'Entry_Date': entry_date,
                    'Exit_Date': exit_date,
                    'Entry_Price': entry_price,
                    'Exit_Price': exit_price,
                    'Profit': profit,
                    'Profit_Pct': profit_pct,
                    'Duration': (exit_date - entry_date).days
                })

        if trades:
            return pd.DataFrame(trades)
        else:
            return pd.DataFrame(columns=['Entry_Date', 'Exit_Date', 'Entry_Price', 'Exit_Price', 'Profit', 'Profit_Pct', 'Duration'])

    def _calculate_metrics(self, data: pd.DataFrame, trades: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate performance metrics.

        Args:
            data (pd.DataFrame): Input dataframe with portfolio values
            trades (pd.DataFrame): Dataframe with trade statistics

        Returns:
            Dict[str, float]: Performance metrics
        """
        # Calculate strategy and market returns
        strategy_return = (data['Portfolio_Value'].iloc[-1] / data['Portfolio_Value'].iloc[0] - 1) * 100
        market_return = (data['Market_Value'].iloc[-1] / data['Market_Value'].iloc[0] - 1) * 100

        # Calculate maximum drawdowns
        strategy_max_drawdown = data['Strategy_Drawdown'].min() * 100
        market_max_drawdown = data['Market_Drawdown'].min() * 100

        # Calculate trade statistics
        total_trades = len(trades)
        winning_trades = len(trades[trades['Profit'] > 0])
        losing_trades = len(trades[trades['Profit'] <= 0])

        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

        # Calculate average profit/loss
        avg_profit = trades['Profit'].mean() if total_trades > 0 else 0
        avg_profit_pct = trades['Profit_Pct'].mean() if total_trades > 0 else 0

        # Calculate average trade duration
        avg_duration = trades['Duration'].mean() if total_trades > 0 else 0

        return {
            'strategy_return': strategy_return,
            'market_return': market_return,
            'strategy_max_drawdown': strategy_max_drawdown,
            'market_max_drawdown': market_max_drawdown,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_profit': avg_profit,
            'avg_profit_pct': avg_profit_pct,
            'avg_duration': avg_duration
        }

    def plot_backtest_results(self, results: Dict[str, Union[pd.DataFrame, float]], title: str = None) -> go.Figure:
        """
        Plot backtest results.

        Args:
            results (Dict[str, Union[pd.DataFrame, float]]): Backtest results
            title (str): Plot title

        Returns:
            go.Figure: Plotly figure
        """
        data = results['data']

        # Create figure
        fig = go.Figure()

        # Add portfolio value
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data['Portfolio_Value'],
            mode='lines',
            name=f'{self.name} Strategy',
            line=dict(color='green')
        ))

        # Add market value
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data['Market_Value'],
            mode='lines',
            name='Buy & Hold',
            line=dict(color='blue')
        ))

        # Add buy signals
        buy_signals = data[data['Signal'] == 1]
        fig.add_trace(go.Scatter(
            x=buy_signals.index,
            y=buy_signals['Close'],
            mode='markers',
            name='Buy Signal',
            marker=dict(color='green', size=10, symbol='triangle-up')
        ))

        # Add sell signals
        sell_signals = data[data['Signal'] == -1]
        fig.add_trace(go.Scatter(
            x=sell_signals.index,
            y=sell_signals['Close'],
            mode='markers',
            name='Sell Signal',
            marker=dict(color='red', size=10, symbol='triangle-down')
        ))

        # Update layout
        fig.update_layout(
            title=title or f'{self.name} Backtest Results',
            xaxis_title='Date',
            yaxis_title='Portfolio Value',
            hovermode='x unified',
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            template="plotly_dark"
        )

        return fig


class MovingAverageCrossoverStrategy(TradingStrategy):
    """
    Moving Average Crossover strategy.
    """

    def __init__(self, fast_period: int = 10, slow_period: int = 50, name: str = "MA Crossover"):
        """
        Initialize the Moving Average Crossover strategy.

        Args:
            fast_period (int): Period for the fast moving average
            slow_period (int): Period for the slow moving average
            name (str): Name of the strategy
        """
        super().__init__(name=name)
        self.fast_period = fast_period
        self.slow_period = slow_period

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals based on Moving Average Crossover.

        Args:
            data (pd.DataFrame): Input dataframe with price data

        Returns:
            pd.DataFrame: Dataframe with added signals
        """
        # Make a copy of the data
        df = data.copy()

        # Calculate moving averages
        df[f'MA_Fast'] = df['Close'].rolling(window=self.fast_period).mean()
        df[f'MA_Slow'] = df['Close'].rolling(window=self.slow_period).mean()

        # Generate signals
        df['Signal'] = 0
        df.loc[df[f'MA_Fast'] > df[f'MA_Slow'], 'Signal'] = 1
        df.loc[df[f'MA_Fast'] < df[f'MA_Slow'], 'Signal'] = -1

        return df


class RSIStrategy(TradingStrategy):
    """
    Relative Strength Index (RSI) strategy.
    """

    def __init__(self, period: int = 14, overbought: float = 70, oversold: float = 30, name: str = "RSI"):
        """
        Initialize the RSI strategy.

        Args:
            period (int): Period for RSI calculation
            overbought (float): Overbought threshold
            oversold (float): Oversold threshold
            name (str): Name of the strategy
        """
        super().__init__(name=name)
        self.period = period
        self.overbought = overbought
        self.oversold = oversold

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals based on RSI.

        Args:
            data (pd.DataFrame): Input dataframe with price data

        Returns:
            pd.DataFrame: Dataframe with added signals
        """
        # Make a copy of the data
        df = data.copy()

        # Calculate RSI
        df = TechnicalIndicators.add_rsi(df, period=self.period)

        # Generate signals
        df['Signal'] = 0
        df.loc[df[f'RSI_{self.period}'] < self.oversold, 'Signal'] = 1
        df.loc[df[f'RSI_{self.period}'] > self.overbought, 'Signal'] = -1

        return df


class MACDStrategy(TradingStrategy):
    """
    Moving Average Convergence Divergence (MACD) strategy.
    """

    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9, name: str = "MACD"):
        """
        Initialize the MACD strategy.

        Args:
            fast_period (int): Period for the fast EMA
            slow_period (int): Period for the slow EMA
            signal_period (int): Period for the signal line
            name (str): Name of the strategy
        """
        super().__init__(name=name)
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals based on MACD.

        Args:
            data (pd.DataFrame): Input dataframe with price data

        Returns:
            pd.DataFrame: Dataframe with added signals
        """
        # Make a copy of the data
        df = data.copy()

        # Calculate MACD
        df = TechnicalIndicators.add_macd(df, fast_period=self.fast_period, slow_period=self.slow_period, signal_period=self.signal_period)

        # Generate signals
        df['Signal'] = 0
        df.loc[df['MACD_Line'] > df['MACD_Signal'], 'Signal'] = 1
        df.loc[df['MACD_Line'] < df['MACD_Signal'], 'Signal'] = -1

        return df


class BollingerBandsStrategy(TradingStrategy):
    """
    Bollinger Bands strategy.
    """

    def __init__(self, period: int = 20, std_dev: float = 2.0, name: str = "Bollinger Bands"):
        """
        Initialize the Bollinger Bands strategy.

        Args:
            period (int): Period for the moving average
            std_dev (float): Number of standard deviations for the bands
            name (str): Name of the strategy
        """
        super().__init__(name=name)
        self.period = period
        self.std_dev = std_dev

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals based on Bollinger Bands.

        Args:
            data (pd.DataFrame): Input dataframe with price data

        Returns:
            pd.DataFrame: Dataframe with added signals
        """
        # Make a copy of the data
        df = data.copy()

        # Calculate Bollinger Bands
        df = TechnicalIndicators.add_bollinger_bands(df, period=self.period, std_dev=self.std_dev)

        # Generate signals
        df['Signal'] = 0
        df.loc[df['Close'] < df[f'BB_Lower_{self.period}'], 'Signal'] = 1
        df.loc[df['Close'] > df[f'BB_Upper_{self.period}'], 'Signal'] = -1

        return df


class CombinedStrategy(TradingStrategy):
    """
    Combined strategy that uses multiple sub-strategies.
    """

    def __init__(self, strategies: List[TradingStrategy], weights: Optional[List[float]] = None, name: str = "Combined Strategy"):
        """
        Initialize the Combined strategy.

        Args:
            strategies (List[TradingStrategy]): List of strategies to combine
            weights (Optional[List[float]]): Weights for each strategy (default: equal weights)
            name (str): Name of the strategy
        """
        super().__init__(name=name)
        self.strategies = strategies

        # Set equal weights if not provided
        if weights is None:
            self.weights = [1.0 / len(strategies)] * len(strategies)
        else:
            # Normalize weights to sum to 1
            total_weight = sum(weights)
            self.weights = [w / total_weight for w in weights]

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Generate trading signals based on the combined strategies.

        Args:
            data (pd.DataFrame): Input dataframe with price data

        Returns:
            pd.DataFrame: Dataframe with added signals
        """
        # Make a copy of the data
        df = data.copy()

        # Generate signals for each strategy
        for i, strategy in enumerate(self.strategies):
            strategy_df = strategy.generate_signals(df)
            df[f'Signal_{i}'] = strategy_df['Signal']

        # Calculate weighted signal
        df['Weighted_Signal'] = 0
        for i in range(len(self.strategies)):
            df['Weighted_Signal'] += df[f'Signal_{i}'] * self.weights[i]

        # Generate final signals
        df['Signal'] = 0
        df.loc[df['Weighted_Signal'] > 0.3, 'Signal'] = 1
        df.loc[df['Weighted_Signal'] < -0.3, 'Signal'] = -1

        return df


class StrategyFactory:
    """
    Factory class for creating trading strategies.
    """

    @staticmethod
    def get_strategy(strategy_type: str, **kwargs) -> TradingStrategy:
        """
        Get a strategy instance based on the strategy type.

        Args:
            strategy_type (str): The type of strategy to create
            **kwargs: Additional strategy parameters

        Returns:
            TradingStrategy: The strategy instance

        Raises:
            ValueError: If the strategy type is not supported
        """
        strategy_type = strategy_type.lower()

        if strategy_type == 'ma_crossover':
            return MovingAverageCrossoverStrategy(
                fast_period=kwargs.get('fast_period', 10),
                slow_period=kwargs.get('slow_period', 50),
                name=kwargs.get('name', 'MA Crossover')
            )
        elif strategy_type == 'rsi':
            return RSIStrategy(
                period=kwargs.get('period', 14),
                overbought=kwargs.get('overbought', 70),
                oversold=kwargs.get('oversold', 30),
                name=kwargs.get('name', 'RSI')
            )
        elif strategy_type == 'macd':
            return MACDStrategy(
                fast_period=kwargs.get('fast_period', 12),
                slow_period=kwargs.get('slow_period', 26),
                signal_period=kwargs.get('signal_period', 9),
                name=kwargs.get('name', 'MACD')
            )
        elif strategy_type == 'bollinger_bands':
            return BollingerBandsStrategy(
                period=kwargs.get('period', 20),
                std_dev=kwargs.get('std_dev', 2.0),
                name=kwargs.get('name', 'Bollinger Bands')
            )
        elif strategy_type == 'combined':
            strategies = kwargs.get('strategies', [])
            weights = kwargs.get('weights', None)
            return CombinedStrategy(
                strategies=strategies,
                weights=weights,
                name=kwargs.get('name', 'Combined Strategy')
            )
        else:
            logger.error(f"Unsupported strategy type: {strategy_type}")
            raise ValueError(f"Unsupported strategy type: {strategy_type}")

    @staticmethod
    def get_available_strategies() -> Dict[str, str]:
        """
        Get a dictionary of available strategies.

        Returns:
            Dict[str, str]: Dictionary mapping strategy keys to display names
        """
        return {
            'ma_crossover': 'Moving Average Crossover',
            'rsi': 'Relative Strength Index (RSI)',
            'macd': 'Moving Average Convergence Divergence (MACD)',
            'bollinger_bands': 'Bollinger Bands',
            'combined': 'Combined Strategy'
        }

    @staticmethod
    def get_strategy_description(strategy_type: str) -> str:
        """
        Get a description of the strategy.

        Args:
            strategy_type (str): The type of strategy

        Returns:
            str: Description of the strategy
        """
        strategy_type = strategy_type.lower()

        descriptions = {
            'ma_crossover': 'Generates buy signals when the fast moving average crosses above the slow moving average, and sell signals when it crosses below.',
            'rsi': 'Generates buy signals when the RSI falls below the oversold threshold, and sell signals when it rises above the overbought threshold.',
            'macd': 'Generates buy signals when the MACD line crosses above the signal line, and sell signals when it crosses below.',
            'bollinger_bands': 'Generates buy signals when the price falls below the lower Bollinger Band, and sell signals when it rises above the upper Bollinger Band.',
            'combined': 'Combines multiple strategies with weighted signals to generate more robust trading signals.'
        }

        return descriptions.get(strategy_type, 'No description available')

    @staticmethod
    def get_strategy_parameters(strategy_type: str) -> Dict[str, Any]:
        """
        Get default parameters for a strategy.

        Args:
            strategy_type (str): The type of strategy

        Returns:
            Dict[str, Any]: Default parameters for the strategy
        """
        strategy_type = strategy_type.lower()

        if strategy_type == 'ma_crossover':
            return {
                'fast_period': 10,
                'slow_period': 50
            }
        elif strategy_type == 'rsi':
            return {
                'period': 14,
                'overbought': 70,
                'oversold': 30
            }
        elif strategy_type == 'macd':
            return {
                'fast_period': 12,
                'slow_period': 26,
                'signal_period': 9
            }
        elif strategy_type == 'bollinger_bands':
            return {
                'period': 20,
                'std_dev': 2.0
            }
        elif strategy_type == 'combined':
            return {
                'strategies': [],
                'weights': []
            }
        else:
            return {}
