import numpy as np
import logging
import pandas as pd
import os
from app.utils.data_processing import is_model_trained

logger = logging.getLogger(__name__)

# Constants for performance data
PERFORMANCE_FILE = "data/metrics/model_performance.csv"

def calculate_technical_signals(df):
    """
    Calculate structured technical analysis signals similar to stocksmith-nemo.
    
    Args:
        df (DataFrame): Stock data with OHLCV columns.
        
    Returns:
        dict: Technical signals with trend, strength, momentum, volatility.
    """
    try:
        if len(df) < 20:
            return {'trend': 'unknown', 'strength': 'neutral', 'momentum': 'neutral', 'volatility': 'normal'}
        
        prices = df['Close'].values
        
        # Calculate SMA (20-day)
        sma_20 = df['Close'].rolling(window=20).mean().iloc[-1]
        current_price = prices[-1]
        
        # Calculate RSI (14-day)
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs)).iloc[-1]
        
        # Calculate EMA for MACD
        ema_12 = df['Close'].ewm(span=12).mean()
        ema_26 = df['Close'].ewm(span=26).mean()
        macd = (ema_12 - ema_26).iloc[-1]
        macd_signal = (ema_12 - ema_26).ewm(span=9).mean().iloc[-1]
        
        # Determine trend
        trend = 'bullish' if current_price > sma_20 else 'bearish'
        
        # Determine strength (RSI-based)
        if rsi > 70:
            strength = 'overbought'
        elif rsi < 30:
            strength = 'oversold'
        else:
            strength = 'neutral'
            
        # Determine momentum (MACD-based)
        momentum = 'positive' if macd > macd_signal else 'negative'
        
        # Determine volatility (rolling standard deviation)
        volatility_value = df['Close'].pct_change().rolling(window=20).std().iloc[-1]
        if volatility_value > 0.03:
            volatility = 'high'
        elif volatility_value < 0.015:
            volatility = 'low'
        else:
            volatility = 'normal'
            
        return {
            'trend': trend,
            'strength': strength, 
            'momentum': momentum,
            'volatility': volatility,
            'rsi': rsi,
            'sma_20': sma_20,
            'current_price': current_price
        }
        
    except Exception as e:
        logger.warning(f"Error calculating technical signals: {str(e)}")
        return {'trend': 'unknown', 'strength': 'neutral', 'momentum': 'neutral', 'volatility': 'normal'}

def get_model_performance_score(model_type, symbol):
    """
    Get performance score for a model based on actual historical performance.
    
    Args:
        model_type (str): Model type to score.
        symbol (str): Stock symbol.
    
    Returns:
        float: Performance score (higher is better).
    """
    try:
        # Load performance data if available
        if os.path.exists(PERFORMANCE_FILE):
            performance_df = pd.read_csv(PERFORMANCE_FILE)
            
            # Filter for the specific model and symbol
            model_perf = performance_df[
                (performance_df['model_type'] == model_type) & 
                (performance_df['symbol'] == symbol)
            ]
            
            if not model_perf.empty:
                # Get the most recent performance record
                latest_perf = model_perf.iloc[-1]
                
                # Calculate composite score
                # Lower MAPE is better (invert it)
                mape_score = max(0, 100 - latest_perf['mape']) / 100
                
                # Higher directional accuracy is better
                direction_score = latest_perf['directional_accuracy'] / 100
                
                # Consider sample size (more predictions = more reliable)
                sample_score = min(1.0, latest_perf['count'] / 50.0)  # Cap at 50 predictions
                
                # Weighted composite score
                composite_score = (
                    mape_score * 0.4 +           # 40% weight on price accuracy
                    direction_score * 0.4 +      # 40% weight on direction accuracy
                    sample_score * 0.2            # 20% weight on sample size
                ) * 100  # Scale to 0-100
                
                logger.info(f"Performance score for {model_type} on {symbol}: {composite_score:.1f} "
                           f"(MAPE: {latest_perf['mape']:.1f}%, Direction: {latest_perf['directional_accuracy']:.1f}%, "
                           f"Count: {latest_perf['count']})")
                
                return composite_score
                
    except Exception as e:
        logger.warning(f"Error loading performance data for {model_type}: {str(e)}")
    
    # Return None if no performance data available
    return None

def select_best_model(symbol, horizon, available_models):
    """
    Select the best model based on actual performance metrics and reliability.
    
    Args:
        symbol (str): Stock symbol.
        horizon (int): Prediction horizon in minutes.
        available_models (list): List of available model names.
    
    Returns:
        str: Name of the best model.
    """
    if not available_models:
        return 'rf'  # Default fallback
    
    # Detect market condition for enhanced selection
    market_condition = detect_market_condition(symbol)
    logger.info(f"Detected market condition for {symbol}: {market_condition}")
    
    # First, try to use actual performance data
    performance_scores = []
    for model in available_models:
        if is_model_trained(symbol, horizon, model):
            perf_score = get_model_performance_score(model, symbol)
            if perf_score is not None:
                # Apply market condition boost
                condition_boost = apply_market_condition_boost(model, market_condition)
                adjusted_score = perf_score * condition_boost
                
                performance_scores.append((model, adjusted_score, perf_score, condition_boost))
                logger.info(f"Performance score for {model}: {perf_score:.1f} -> {adjusted_score:.1f} "
                           f"(boost: {condition_boost:.2f}x for {market_condition})")
    
    # If we have performance data, use it
    if performance_scores:
        # Sort by adjusted performance score (highest first)
        performance_scores.sort(key=lambda x: x[1], reverse=True)
        best_model = performance_scores[0][0]
        best_score = performance_scores[0][1]
        original_score = performance_scores[0][2]
        boost_factor = performance_scores[0][3]
        
        logger.info(f"Selected {best_model} based on real performance data "
                   f"(original: {original_score:.1f}, adjusted: {best_score:.1f}, "
                   f"boost: {boost_factor:.2f}x) for {symbol} {horizon}min in {market_condition} market")
        return best_model
    
    # Fallback to priority-based selection if no performance data
    logger.info(f"No performance data available, using priority-based selection for {symbol} {horizon}min")
    
    # Priority order based on general model characteristics
    # This is used when no real performance data is available
    model_priority = {
        'ensemble': 95,  # Highest priority - combines multiple models
        'ensemble_stacking': 94,     # Advanced ensemble method
        'ensemble_lstm': 93,         # LSTM-based ensemble
        'ensemble_weighted_average': 92,  # Weighted ensemble
        'ensemble_performance_weighted': 91,  # Performance-weighted ensemble
        'ensemble_hybrid': 90,       # Hybrid ensemble
        'rf': 85,        # Reliable and stable
        'gb': 80,        # Good for medium-term predictions
        'hybrid': 78,    # Hybrid ARIMA-ML model
        'lstm': 75,      # Good for patterns but can be volatile
        'lr': 60,        # Simple but stable baseline
        'auto': 50       # Last resort
    }
    
    # Filter available models and select highest priority
    available_with_scores = []
    for model in available_models:
        if model in model_priority:
            # Verify model is actually trained
            if is_model_trained(symbol, horizon, model):
                available_with_scores.append((model, model_priority[model]))
            else:
                logger.warning(f"Model {model} claimed available but not trained for {symbol} {horizon}min")
    
    if not available_with_scores:
        logger.error(f"No valid trained models found for {symbol} {horizon}min")
        return available_models[0] if available_models else 'rf'
    
    # Select model with highest priority score
    best_model = max(available_with_scores, key=lambda x: x[1])[0]
    
    logger.info(f"Selected {best_model} using priority-based fallback for {symbol} {horizon}min (from {available_models})")
    return best_model

def detect_market_condition(symbol):
    """
    Detect current market condition using enhanced technical analysis.
    Uses live API data when available, falls back to CSV files.
    
    Args:
        symbol (str): Stock symbol.
    
    Returns:
        str: Market condition ('high_volatility', 'trending', 'sideways', 'overbought', 'oversold', 'unknown').
    """
    try:
        df = None
        data_source = "unknown"
        
        # First, try to get live data from API server
        try:
            from app.pages.predictions_consolidated import check_api_server_status, fetch_price_from_api_server
            
            if check_api_server_status():
                # Get live price data
                live_price_data = fetch_price_from_api_server(symbol)
                if live_price_data and isinstance(live_price_data, dict):
                    # We have live data, but we need historical data for technical analysis
                    # So we'll combine live data with recent CSV data
                    data_file = f"data/stocks/{symbol}.csv"
                    if os.path.exists(data_file):
                        df = pd.read_csv(data_file)
                        if len(df) >= 20:
                            # Update the latest price with live data
                            df = df.copy()
                            df.loc[df.index[-1], 'Close'] = live_price_data['price']
                            data_source = "API + Historical CSV"
                            logger.info(f"Using live API data ({live_price_data['price']:.2f}) with historical CSV for technical analysis")
        except Exception as e:
            logger.debug(f"Could not get live data: {str(e)}")
        
        # Fallback to CSV file if live data not available
        if df is None:
            data_file = f"data/stocks/{symbol}.csv"
            if os.path.exists(data_file):
                df = pd.read_csv(data_file)
                data_source = "Historical CSV only"
                logger.info(f"Using historical CSV data for technical analysis")
        
        if df is not None and len(df) >= 20:
            # Get technical signals using structured approach
            signals = calculate_technical_signals(df.tail(30))
            
            # Enhanced market condition classification
            # Priority: Extreme conditions first, then general trends
            if signals['strength'] == 'overbought':
                condition = 'overbought'
            elif signals['strength'] == 'oversold':
                condition = 'oversold'
            elif signals['volatility'] == 'high':
                condition = 'high_volatility'
            elif signals['trend'] == 'bullish' and signals['momentum'] == 'positive':
                condition = 'trending_up'
            elif signals['trend'] == 'bearish' and signals['momentum'] == 'negative':
                condition = 'trending_down'
            elif signals['trend'] in ['bullish', 'bearish']:
                condition = 'trending'
            else:
                condition = 'sideways'
            
            logger.info(f"Market condition for {symbol}: {condition} (Data source: {data_source})")
            return condition
                    
    except Exception as e:
        logger.warning(f"Error detecting market condition for {symbol}: {str(e)}")
    
    return 'unknown'

def apply_market_condition_boost(model, condition):
    """
    Apply performance boost based on enhanced market condition analysis.
    
    Args:
        model (str): Model type.
        condition (str): Market condition.
    
    Returns:
        float: Boost factor (multiplier for performance score).
    """
    # Enhanced model preferences for different market conditions
    condition_preferences = {
        'high_volatility': {
            'ensemble_lstm': 1.20,
            'lstm': 1.15,
            'ensemble': 1.10,
            'rf': 0.90,
            'lr': 0.85
        },
        'trending_up': {
            'rf': 1.20,
            'gb': 1.15,
            'ensemble': 1.10,
            'hybrid': 1.10,
            'lstm': 1.00
        },
        'trending_down': {
            'rf': 1.20,
            'gb': 1.15,
            'ensemble': 1.10,
            'lr': 1.05,
            'lstm': 0.95
        },
        'trending': {
            'rf': 1.15,
            'gb': 1.10,
            'ensemble': 1.05,
            'hybrid': 1.05,
            'lstm': 0.95
        },
        'overbought': {
            'lr': 1.25,  # Linear models handle reversals better
            'ensemble_weighted_average': 1.15,
            'ensemble': 1.10,
            'rf': 1.05,
            'lstm': 0.85,  # LSTM may continue the trend incorrectly
            'gb': 0.90
        },
        'oversold': {
            'lr': 1.25,  # Linear models handle reversals better
            'ensemble_weighted_average': 1.15,
            'ensemble': 1.10,
            'rf': 1.05,
            'lstm': 0.85,
            'gb': 0.90
        },
        'sideways': {
            'lr': 1.15,
            'ensemble_weighted_average': 1.10,
            'ensemble': 1.05,
            'lstm': 0.90,
            'gb': 0.95
        }
    }
    
    if condition in condition_preferences:
        boost = condition_preferences[condition].get(model, 1.0)
        logger.debug(f"Applied {boost:.2f}x boost for {model} in {condition} market")
        return boost
    
    return 1.0  # No boost for unknown conditions
