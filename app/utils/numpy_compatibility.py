"""
NumPy Compatibility Module

This module provides comprehensive fixes for NumPy compatibility issues,
particularly focusing on the BitGenerator and random number generation
components that can cause issues with scikit-learn and other libraries.

It addresses the error:
'<class 'app.utils.numpy_bitgenerator_fix.fix_numpy_bitgenerator.<locals>.FixedMT19937'> is not a known BitGenerator module.'
"""

import sys
import logging
import numpy as np
import random
import importlib
import types
import os

logger = logging.getLogger(__name__)

# Define a proper BitGenerator-compatible MT19937 class
class CompatibleMT19937:
    """A fully compatible MT19937 random number generator that works with scikit-learn"""
    
    def __init__(self, seed=None):
        self.seed = seed
        self._random_state = random.Random(seed)
        
    def jumped(self, *args, **kwargs):
        """Implement jumped method required by some algorithms"""
        return self
        
    def next_uint32(self):
        """Generate a random 32-bit unsigned integer"""
        return self._random_state.randint(0, 2**32-1)
        
    def next_uint64(self):
        """Generate a random 64-bit unsigned integer"""
        return self._random_state.randint(0, 2**64-1)
        
    def next_double(self):
        """Generate a random double precision float"""
        return self._random_state.random()
        
    def __repr__(self):
        return f"CompatibleMT19937(seed={self.seed})"
        
    # Add scikit-learn compatibility methods
    def random_raw(self, size=None):
        """Generate random numbers in the interval [0, 1)"""
        if size is None:
            return self.next_double()
        return np.array([self.next_double() for _ in range(np.prod(size))]).reshape(size)
    
    # Add any other methods that might be needed by scikit-learn
    def integers(self, low, high=None, size=None):
        """Generate random integers"""
        if high is None:
            high = low
            low = 0
        if size is None:
            return self._random_state.randint(low, high-1)
        return np.array([self._random_state.randint(low, high-1) for _ in range(np.prod(size))]).reshape(size)

def apply_comprehensive_numpy_fix():
    """
    Apply a comprehensive fix for NumPy compatibility issues.
    This addresses problems with BitGenerator, MT19937, and other random number generation components.
    """
    try:
        logger.info(f"Applying comprehensive NumPy compatibility fix (NumPy version: {np.__version__})")
        
        # Step 1: Fix numpy.random.bit_generator.BitGenerator if it exists
        if hasattr(np.random, 'bit_generator') and hasattr(np.random.bit_generator, 'BitGenerator'):
            original_bitgenerator = np.random.bit_generator.BitGenerator
            
            # Create a proper BitGenerator class if needed
            class CompatibleBitGenerator(original_bitgenerator):
                def __init__(self, seed=None):
                    if hasattr(super(), '__init__'):
                        try:
                            super().__init__(seed)
                        except Exception:
                            self.seed = seed
                    else:
                        self.seed = seed
            
            # Replace the BitGenerator class
            np.random.bit_generator.BitGenerator = CompatibleBitGenerator
            logger.info("Fixed numpy.random.bit_generator.BitGenerator")
        
        # Step 2: Fix numpy.random.BitGenerator if it exists
        if hasattr(np.random, 'BitGenerator'):
            original_bitgenerator = np.random.BitGenerator
            
            # Create a proper BitGenerator class if needed
            class CompatibleBitGenerator(original_bitgenerator):
                def __init__(self, seed=None):
                    if hasattr(super(), '__init__'):
                        try:
                            super().__init__(seed)
                        except Exception:
                            self.seed = seed
                    else:
                        self.seed = seed
            
            # Replace the BitGenerator class
            np.random.BitGenerator = CompatibleBitGenerator
            logger.info("Fixed numpy.random.BitGenerator")
        else:
            # Create BitGenerator if it doesn't exist
            class CompatibleBitGenerator:
                def __init__(self, seed=None):
                    self.seed = seed
            
            np.random.BitGenerator = CompatibleBitGenerator
            logger.info("Created numpy.random.BitGenerator")
        
        # Step 3: Fix numpy.random.MT19937
        if hasattr(np.random, 'MT19937'):
            # Save original for reference
            original_mt19937 = np.random.MT19937
            
            # Replace with our compatible version
            np.random.MT19937 = CompatibleMT19937
            logger.info("Fixed numpy.random.MT19937")
        else:
            # Create MT19937 if it doesn't exist
            np.random.MT19937 = CompatibleMT19937
            logger.info("Created numpy.random.MT19937")
        
        # Step 4: Fix numpy.random._mt19937.MT19937 if it exists
        if 'numpy.random._mt19937' in sys.modules:
            sys.modules['numpy.random._mt19937'].MT19937 = CompatibleMT19937
            logger.info("Fixed numpy.random._mt19937.MT19937")
        
        # Step 5: Fix any references to problematic classes in other modules
        for module_name in list(sys.modules.keys()):
            if module_name.startswith('app.utils.numpy'):
                module = sys.modules[module_name]
                if hasattr(module, 'FixedMT19937'):
                    module.FixedMT19937 = CompatibleMT19937
                    logger.info(f"Fixed {module_name}.FixedMT19937")
                if hasattr(module, 'MT19937'):
                    module.MT19937 = CompatibleMT19937
                    logger.info(f"Fixed {module_name}.MT19937")
        
        # Step 6: Add our class to this module's globals
        globals()['CompatibleMT19937'] = CompatibleMT19937
        
        # Step 7: Fix scikit-learn's random state if needed
        try:
            import sklearn
            sklearn_version = sklearn.__version__
            logger.info(f"Detected scikit-learn version: {sklearn_version}")
            
            # Fix sklearn.utils._random
            if 'sklearn.utils._random' in sys.modules:
                random_module = sys.modules['sklearn.utils._random']
                if hasattr(random_module, 'check_random_state'):
                    original_check_random_state = random_module.check_random_state
                    
                    def fixed_check_random_state(seed):
                        """Fixed version of check_random_state that handles our custom MT19937"""
                        if seed is None:
                            return np.random.mtrand._rand
                        if isinstance(seed, (int, np.integer)):
                            return np.random.RandomState(seed)
                        if isinstance(seed, np.random.RandomState):
                            return seed
                        if isinstance(seed, CompatibleMT19937):
                            return np.random.RandomState(seed.seed)
                        try:
                            return original_check_random_state(seed)
                        except Exception:
                            return np.random.RandomState(hash(seed) % 2**32)
                    
                    random_module.check_random_state = fixed_check_random_state
                    logger.info("Fixed sklearn.utils._random.check_random_state")
        except ImportError:
            logger.info("scikit-learn not found, skipping scikit-learn specific fixes")
        
        logger.info("Comprehensive NumPy compatibility fix successfully applied")
        return True
    
    except Exception as e:
        logger.error(f"Error applying comprehensive NumPy compatibility fix: {str(e)}")
        return False

# Apply the fix when this module is imported
fix_applied = apply_comprehensive_numpy_fix()
