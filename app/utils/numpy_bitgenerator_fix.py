"""
NumPy BitGenerator Fix

This module provides a fix for the BitGenerator compatibility issue between NumPy 1.23.5
and scikit-learn 1.6.1. The specific error being addressed is:
'<class 'app.utils.numpy_fix.FixedMT19937'> is not a known BitGenerator module.'

This fix properly registers the MT19937 random number generator with NumPy's BitGenerator
system to ensure compatibility with newer scikit-learn versions.
"""

import sys
import logging
import importlib
import types

logger = logging.getLogger(__name__)

def fix_numpy_bitgenerator():
    """
    Apply a fix for the BitGenerator compatibility issue by properly registering
    the MT19937 random number generator with NumPy's BitGenerator system.
    """
    try:
        # Import numpy
        import numpy as np
        
        # Check numpy version
        numpy_version = np.__version__
        logger.info(f"NumPy version: {numpy_version}")
        
        # Check if numpy.random._mt19937 exists
        try:
            import numpy.random._mt19937
            logger.info("numpy.random._mt19937 module exists")
            
            # Get the original MT19937 class
            original_mt19937 = numpy.random._mt19937.MT19937
            
            # Check if BitGenerator exists
            if hasattr(numpy.random, 'BitGenerator'):
                logger.info("BitGenerator exists")
                
                # Check if MT19937 is already a subclass of BitGenerator
                if issubclass(original_mt19937, numpy.random.BitGenerator):
                    logger.info("MT19937 is already a subclass of BitGenerator")
                else:
                    logger.warning("MT19937 is not a subclass of BitGenerator, fixing...")
                    
                    # Create a proper subclass of BitGenerator
                    class FixedMT19937(numpy.random.BitGenerator):
                        def __init__(self, seed=None):
                            super().__init__(seed)
                            self._mt19937 = original_mt19937(seed)
                            
                        def _next_uint32(self):
                            return self._mt19937.next_uint32()
                            
                        def _next_uint64(self):
                            return self._mt19937.next_uint64()
                            
                        def _next_double(self):
                            return self._mt19937.next_double()
                            
                        def jumped(self, *args, **kwargs):
                            self._mt19937 = self._mt19937.jumped(*args, **kwargs)
                            return self
                    
                    # Replace the MT19937 class
                    numpy.random._mt19937.MT19937 = FixedMT19937
                    numpy.random.MT19937 = FixedMT19937
                    
                    # Register with BitGenerator
                    try:
                        numpy.random.BitGenerator.register(FixedMT19937)
                        logger.info("Registered FixedMT19937 with BitGenerator")
                    except Exception as e:
                        logger.warning(f"Could not register FixedMT19937: {str(e)}")
            else:
                logger.warning("BitGenerator does not exist in numpy.random")
                
                # Create a BitGenerator class
                class BitGenerator:
                    def __init__(self, seed=None):
                        self.seed = seed
                        
                    def random(self):
                        import random
                        return random.random()
                
                # Add BitGenerator to numpy.random
                numpy.random.BitGenerator = BitGenerator
                
                # Make MT19937 inherit from BitGenerator
                class FixedMT19937(BitGenerator):
                    def __init__(self, seed=None):
                        super().__init__(seed)
                        self._mt19937 = original_mt19937(seed)
                        
                    def random(self):
                        return self._mt19937.next_double()
                
                # Replace the MT19937 class
                numpy.random._mt19937.MT19937 = FixedMT19937
                numpy.random.MT19937 = FixedMT19937
                
                logger.info("Created BitGenerator and FixedMT19937")
        except ImportError as e:
            logger.warning(f"numpy.random._mt19937 module not found: {str(e)}")
            
            # Create a mock module
            mock_mt19937 = types.ModuleType('numpy.random._mt19937')
            sys.modules['numpy.random._mt19937'] = mock_mt19937
            
            # Create a BitGenerator class if it doesn't exist
            if not hasattr(numpy.random, 'BitGenerator'):
                class BitGenerator:
                    def __init__(self, seed=None):
                        self.seed = seed
                        
                    def random(self):
                        import random
                        return random.random()
                
                numpy.random.BitGenerator = BitGenerator
            
            # Create a proper MT19937 class
            class MT19937(numpy.random.BitGenerator):
                def __init__(self, seed=None):
                    super().__init__(seed)
                    self.seed = seed
                    
                def _next_uint32(self):
                    import random
                    return random.randint(0, 2**32-1)
                    
                def _next_uint64(self):
                    import random
                    return random.randint(0, 2**64-1)
                    
                def _next_double(self):
                    import random
                    return random.random()
                    
                def jumped(self, *args, **kwargs):
                    return self
            
            # Add the class to the module
            mock_mt19937.MT19937 = MT19937
            numpy.random.MT19937 = MT19937
            
            # Register with BitGenerator
            try:
                numpy.random.BitGenerator.register(MT19937)
                logger.info("Registered MT19937 with BitGenerator")
            except Exception as e:
                logger.warning(f"Could not register MT19937: {str(e)}")
                
            logger.info("Created mock MT19937 module and class")
            
        # Fix for app.utils.numpy_fix.FixedMT19937
        if 'app.utils.numpy_fix' in sys.modules:
            logger.info("app.utils.numpy_fix module exists, updating FixedMT19937")
            if hasattr(numpy.random, 'MT19937'):
                sys.modules['app.utils.numpy_fix'].FixedMT19937 = numpy.random.MT19937
                logger.info("Updated app.utils.numpy_fix.FixedMT19937")
        
        logger.info("NumPy BitGenerator fix applied successfully")
        return True
    except Exception as e:
        logger.error(f"Error applying NumPy BitGenerator fix: {str(e)}")
        return False
