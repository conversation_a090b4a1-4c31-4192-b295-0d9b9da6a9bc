"""
Performance tracking utilities for the AI Stocks Bot application.

This module provides tools for tracking and analyzing performance metrics
for various operations in the application, including prediction performance.
"""

import time
import functools
import logging
import tracemalloc
import numpy as np
import pandas as pd
import os
import json
from typing import Callable, Any, Dict, List, Optional, Union, Tu<PERSON>
from datetime import datetime, timedelta
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

# Constants for prediction performance tracking
PERFORMANCE_DIR = "performance_data"
METRICS = ["mse", "mae", "mape", "r2", "hit_ratio"]

logger = logging.getLogger(__name__)

class PerformanceTracker:
    """Track performance metrics for functions and operations"""

    def __init__(self):
        self.metrics = {}
        self.memory_tracking = False

    def start_memory_tracking(self):
        """Start tracking memory allocations"""
        tracemalloc.start()
        self.memory_tracking = True

    def stop_memory_tracking(self):
        """Stop tracking memory allocations"""
        if self.memory_tracking:
            tracemalloc.stop()
            self.memory_tracking = False

    def track(self, category: str = None):
        """Decorator to track function performance"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Determine category
                cat = category or func.__name__

                # Start timing
                start_time = time.time()

                # Track memory if enabled
                if self.memory_tracking:
                    tracemalloc.clear_traces()
                    start_memory = tracemalloc.get_traced_memory()[0]

                # Call the function
                try:
                    result = func(*args, **kwargs)
                    success = True
                    exception = None
                except Exception as e:
                    result = None
                    success = False
                    exception = str(e)
                    raise  # Re-raise the exception
                finally:
                    # End timing
                    end_time = time.time()
                    duration = end_time - start_time

                    # Track memory if enabled
                    if self.memory_tracking:
                        end_memory = tracemalloc.get_traced_memory()[0]
                        memory_delta = end_memory - start_memory
                    else:
                        memory_delta = None

                    # Record metrics
                    if cat not in self.metrics:
                        self.metrics[cat] = []

                    metric = {
                        "timestamp": datetime.now(),
                        "duration": duration,
                        "success": success
                    }

                    if not success:
                        metric["exception"] = exception

                    if memory_delta is not None:
                        metric["memory_delta"] = memory_delta

                    self.metrics[cat].append(metric)

                    # Log performance
                    if success:
                        logger.debug(f"{cat} completed in {duration:.4f}s")
                        if memory_delta is not None:
                            logger.debug(f"{cat} memory delta: {memory_delta / 1024 / 1024:.2f}MB")
                    else:
                        logger.warning(f"{cat} failed after {duration:.4f}s: {exception}")

                return result

            return wrapper

        return decorator

    def get_metrics(self, category: Optional[str] = None) -> Dict:
        """Get performance metrics for a category or all categories"""
        if category:
            return self.metrics.get(category, [])
        return self.metrics

    def get_summary(self, category: Optional[str] = None) -> Dict:
        """Get a summary of performance metrics"""
        if category:
            categories = [category]
        else:
            categories = self.metrics.keys()

        summary = {}

        for cat in categories:
            metrics = self.metrics.get(cat, [])
            if not metrics:
                continue

            durations = [m["duration"] for m in metrics]
            successes = sum(1 for m in metrics if m.get("success", False))

            summary[cat] = {
                "count": len(metrics),
                "success_rate": successes / len(metrics) if metrics else 0,
                "avg_duration": sum(durations) / len(durations) if durations else 0,
                "min_duration": min(durations) if durations else 0,
                "max_duration": max(durations) if durations else 0
            }

            # Add memory metrics if available
            memory_deltas = [m.get("memory_delta") for m in metrics if "memory_delta" in m]
            if memory_deltas:
                summary[cat]["avg_memory_delta"] = sum(memory_deltas) / len(memory_deltas)
                summary[cat]["max_memory_delta"] = max(memory_deltas)

        return summary

    def reset(self, category: Optional[str] = None):
        """Reset metrics for a category or all categories"""
        if category:
            self.metrics[category] = []
        else:
            self.metrics = {}

# Create a global instance
performance_tracker = PerformanceTracker()

# Convenience decorator
def track_performance(category: str = None):
    """Decorator to track function performance"""
    return performance_tracker.track(category)

def get_performance_summary(category: Optional[str] = None) -> Dict:
    """Get a summary of performance metrics"""
    return performance_tracker.get_summary(category)

def reset_performance_metrics(category: Optional[str] = None):
    """Reset performance metrics"""
    performance_tracker.reset(category)

def start_memory_tracking():
    """Start tracking memory allocations"""
    performance_tracker.start_memory_tracking()

def stop_memory_tracking():
    """Stop tracking memory allocations"""
    performance_tracker.stop_memory_tracking()


# Prediction performance tracking functions

def ensure_performance_dir():
    """Ensure the performance directory exists"""
    if not os.path.exists(PERFORMANCE_DIR):
        os.makedirs(PERFORMANCE_DIR)


def evaluate_prediction(actual_value: float, predicted_value: float) -> Dict[str, float]:
    """
    Evaluate a single prediction against the actual value.

    Args:
        actual_value (float): The actual observed value
        predicted_value (float): The predicted value

    Returns:
        Dict[str, float]: Dictionary with performance metrics
    """
    try:
        # Calculate error
        error = actual_value - predicted_value
        abs_error = abs(error)

        # Calculate metrics
        mse = error ** 2
        mae = abs_error

        # Avoid division by zero
        if actual_value != 0:
            mape = abs_error / abs(actual_value) * 100
        else:
            mape = np.nan

        # Hit ratio (directional accuracy)
        hit = 1 if (predicted_value > actual_value and actual_value > 0) or \
                   (predicted_value < actual_value and actual_value < 0) else 0

        return {
            "mse": mse,
            "mae": mae,
            "mape": mape,
            "hit_ratio": hit,
            "error": error
        }
    except Exception as e:
        logger.error(f"Error evaluating prediction: {str(e)}")
        return {metric: np.nan for metric in METRICS}


def update_model_performance(symbol: str, model_name: str, horizon: int,
                            actual_value: float, predicted_value: float):
    """
    Update the performance record for a model.

    Args:
        symbol (str): Stock symbol
        model_name (str): Name of the model
        horizon (int): Prediction horizon in minutes
        actual_value (float): The actual observed value
        predicted_value (float): The predicted value
    """
    try:
        # Validate inputs to ensure they're reasonable
        if not isinstance(actual_value, (int, float)) or not isinstance(predicted_value, (int, float)):
            logger.warning(f"Invalid values for performance tracking: actual={actual_value}, predicted={predicted_value}")
            return

        # Check for unrealistic values (more than 100x difference)
        if actual_value > 0 and predicted_value > 0:
            ratio = max(actual_value, predicted_value) / min(actual_value, predicted_value)
            if ratio > 100:
                logger.warning(f"Unrealistic prediction detected: actual={actual_value}, predicted={predicted_value}, ratio={ratio:.2f}")
                # Adjust the predicted value to be at most 20% different from actual
                if predicted_value > actual_value:
                    predicted_value = actual_value * 1.2
                else:
                    predicted_value = actual_value * 0.8
                logger.info(f"Adjusted predicted value to {predicted_value:.2f} for performance tracking")

        # Calculate performance metrics
        metrics = evaluate_prediction(actual_value, predicted_value)

        # Create performance record
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        performance_record = {
            "timestamp": timestamp,
            "model": model_name,
            "symbol": symbol,
            "horizon": horizon,
            "actual": actual_value,
            "predicted": predicted_value,
            **metrics
        }

        # Save to performance tracking file
        ensure_performance_dir()
        performance_file = os.path.join(PERFORMANCE_DIR, f"{symbol}_performance.json")

        # Load existing performance records if file exists
        existing_records = []
        if os.path.exists(performance_file):
            try:
                with open(performance_file, 'r') as f:
                    existing_records = json.load(f)
            except Exception as e:
                logger.error(f"Error loading performance file: {str(e)}")

        # Add new record and save
        existing_records.append(performance_record)

        # Keep only the last 1000 records
        if len(existing_records) > 1000:
            existing_records = existing_records[-1000:]

        with open(performance_file, 'w') as f:
            json.dump(existing_records, f, indent=2)

        logger.info(f"Updated performance record for {symbol} using {model_name} at horizon {horizon}")
    except Exception as e:
        logger.error(f"Error updating model performance: {str(e)}")


def get_model_performance(symbol: str, model_name: Optional[str] = None,
                         horizon: Optional[int] = None) -> pd.DataFrame:
    """
    Get performance metrics for a model.

    Args:
        symbol (str): Stock symbol
        model_name (Optional[str]): Name of the model (None for all models)
        horizon (Optional[int]): Prediction horizon in minutes (None for all horizons)

    Returns:
        pd.DataFrame: DataFrame with performance metrics
    """
    try:
        # Load performance records
        ensure_performance_dir()
        performance_file = os.path.join(PERFORMANCE_DIR, f"{symbol}_performance.json")

        if not os.path.exists(performance_file):
            logger.warning(f"No performance data found for {symbol}")
            return pd.DataFrame()

        with open(performance_file, 'r') as f:
            records = json.load(f)

        # Convert to DataFrame
        df = pd.DataFrame(records)

        # Filter by model and horizon if specified
        if model_name:
            df = df[df['model'] == model_name]

        if horizon:
            df = df[df['horizon'] == horizon]

        return df
    except Exception as e:
        logger.error(f"Error getting model performance: {str(e)}")
        return pd.DataFrame()


def get_best_model(symbol: str, horizon: int, metric: str = 'mse') -> str:
    """
    Get the best performing model for a given symbol and horizon.

    Args:
        symbol (str): Stock symbol
        horizon (int): Prediction horizon in minutes
        metric (str): Metric to use for comparison ('mse', 'mae', 'mape', 'hit_ratio')

    Returns:
        str: Name of the best performing model
    """
    try:
        # Get performance data
        df = get_model_performance(symbol)

        if df.empty:
            logger.warning(f"No performance data found for {symbol}")
            return "ensemble"  # Default to ensemble if no data

        # Filter by horizon
        df = df[df['horizon'] == horizon]

        if df.empty:
            logger.warning(f"No performance data found for {symbol} at horizon {horizon}")
            return "ensemble"  # Default to ensemble if no data

        # Group by model and calculate average metric
        if metric in ['mse', 'mae', 'mape']:
            # Lower is better for these metrics
            avg_metrics = df.groupby('model')[metric].mean()
            best_model = avg_metrics.idxmin()
        else:
            # Higher is better for hit_ratio
            avg_metrics = df.groupby('model')[metric].mean()
            best_model = avg_metrics.idxmax()

        logger.info(f"Best model for {symbol} at horizon {horizon} based on {metric}: {best_model}")
        return best_model
    except Exception as e:
        logger.error(f"Error finding best model: {str(e)}")
        return "ensemble"  # Default to ensemble if error
