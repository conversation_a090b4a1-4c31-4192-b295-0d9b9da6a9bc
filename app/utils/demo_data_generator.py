"""
Demo Data Generator for Advanced Technical Analysis

This module generates realistic stock market data for demonstration purposes
when real data is not available or for testing advanced technical analysis features.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Configure logging
logger = logging.getLogger(__name__)

class DemoDataGenerator:
    """
    Generate realistic demo stock market data
    """
    
    @staticmethod
    def generate_realistic_stock_data(
        symbol: str = "DEMO",
        days: int = 365,
        start_price: float = 100.0,
        volatility: float = 0.02,
        trend: float = 0.0001
    ) -> pd.DataFrame:
        """
        Generate realistic stock market data with patterns
        
        Args:
            symbol (str): Stock symbol
            days (int): Number of days to generate
            start_price (float): Starting price
            volatility (float): Daily volatility
            trend (float): Daily trend (positive for uptrend)
            
        Returns:
            pd.DataFrame: Generated stock data with OHLCV
        """
        try:
            # Generate dates
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            
            # Initialize arrays
            n_days = len(dates)
            prices = np.zeros(n_days)
            volumes = np.zeros(n_days)
            
            # Set initial price
            prices[0] = start_price
            
            # Generate price series with realistic patterns
            for i in range(1, n_days):
                # Add trend
                trend_component = trend * i
                
                # Add cyclical component (market cycles)
                cycle_component = 0.01 * np.sin(2 * np.pi * i / 252)  # Annual cycle
                
                # Add random walk
                random_component = np.random.normal(0, volatility)
                
                # Add momentum (trending behavior)
                momentum = 0.1 * (prices[i-1] - prices[max(0, i-5)]) / prices[max(0, i-5)] if i > 5 else 0
                
                # Calculate price change
                price_change = trend_component + cycle_component + random_component + momentum
                
                # Apply price change
                prices[i] = prices[i-1] * (1 + price_change)
                
                # Ensure price doesn't go negative
                prices[i] = max(prices[i], 0.01)
            
            # Generate OHLC from close prices
            opens = np.zeros(n_days)
            highs = np.zeros(n_days)
            lows = np.zeros(n_days)
            closes = prices.copy()
            
            for i in range(n_days):
                if i == 0:
                    opens[i] = closes[i]
                else:
                    # Open is close of previous day with small gap
                    gap = np.random.normal(0, volatility * 0.3)
                    opens[i] = closes[i-1] * (1 + gap)
                
                # Generate intraday high and low
                daily_volatility = volatility * np.random.uniform(0.5, 2.0)
                intraday_range = closes[i] * daily_volatility
                
                highs[i] = max(opens[i], closes[i]) + np.random.uniform(0, intraday_range)
                lows[i] = min(opens[i], closes[i]) - np.random.uniform(0, intraday_range)
                
                # Ensure OHLC consistency
                highs[i] = max(highs[i], opens[i], closes[i])
                lows[i] = min(lows[i], opens[i], closes[i])
            
            # Generate volumes (higher volume on larger price moves)
            base_volume = 1000000
            for i in range(n_days):
                if i == 0:
                    price_change_pct = 0
                else:
                    price_change_pct = abs(closes[i] - closes[i-1]) / closes[i-1]
                
                # Volume increases with price volatility
                volume_multiplier = 1 + price_change_pct * 5
                volume_noise = np.random.uniform(0.5, 1.5)
                volumes[i] = base_volume * volume_multiplier * volume_noise
            
            # Create DataFrame
            data = pd.DataFrame({
                'Date': dates,
                'Open': opens,
                'High': highs,
                'Low': lows,
                'Close': closes,
                'Volume': volumes.astype(int)
            })
            
            # Add technical indicators for more realistic data
            data = DemoDataGenerator._add_technical_indicators(data)
            
            logger.info(f"Generated {len(data)} days of demo data for {symbol}")
            return data
            
        except Exception as e:
            logger.error(f"Error generating demo data: {str(e)}")
            return pd.DataFrame()
    
    @staticmethod
    def _add_technical_indicators(data: pd.DataFrame) -> pd.DataFrame:
        """Add basic technical indicators to the demo data"""
        try:
            # Simple Moving Averages
            data['SMA_20'] = data['Close'].rolling(window=20).mean()
            data['SMA_50'] = data['Close'].rolling(window=50).mean()
            
            # Exponential Moving Average
            data['EMA_12'] = data['Close'].ewm(span=12).mean()
            data['EMA_26'] = data['Close'].ewm(span=26).mean()
            
            # MACD
            data['MACD'] = data['EMA_12'] - data['EMA_26']
            data['MACD_Signal'] = data['MACD'].ewm(span=9).mean()
            data['MACD_Histogram'] = data['MACD'] - data['MACD_Signal']
            
            # RSI
            delta = data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['RSI'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            data['BB_Middle'] = data['Close'].rolling(window=20).mean()
            bb_std = data['Close'].rolling(window=20).std()
            data['BB_Upper'] = data['BB_Middle'] + (bb_std * 2)
            data['BB_Lower'] = data['BB_Middle'] - (bb_std * 2)
            
            # Volume indicators
            data['Volume_SMA'] = data['Volume'].rolling(window=20).mean()
            data['Volume_Ratio'] = data['Volume'] / data['Volume_SMA']
            
            # Price-Volume indicators
            typical_price = (data['High'] + data['Low'] + data['Close']) / 3
            data['VWAP'] = (typical_price * data['Volume']).cumsum() / data['Volume'].cumsum()
            
            # Volatility indicators
            data['ATR'] = DemoDataGenerator._calculate_atr(data)
            
            return data
            
        except Exception as e:
            logger.error(f"Error adding technical indicators: {str(e)}")
            return data
    
    @staticmethod
    def _calculate_atr(data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        try:
            high_low = data['High'] - data['Low']
            high_close = np.abs(data['High'] - data['Close'].shift())
            low_close = np.abs(data['Low'] - data['Close'].shift())
            
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            atr = true_range.rolling(window=period).mean()
            
            return atr
            
        except Exception as e:
            logger.error(f"Error calculating ATR: {str(e)}")
            return pd.Series([0] * len(data))
    
    @staticmethod
    def generate_pattern_data(pattern_type: str = "head_and_shoulders") -> pd.DataFrame:
        """
        Generate data with specific chart patterns for demonstration
        
        Args:
            pattern_type (str): Type of pattern to generate
            
        Returns:
            pd.DataFrame: Data with the specified pattern
        """
        try:
            base_data = DemoDataGenerator.generate_realistic_stock_data(days=100, volatility=0.015)
            
            if pattern_type == "head_and_shoulders":
                # Modify data to create head and shoulders pattern
                mid_point = len(base_data) // 2
                
                # Create left shoulder
                base_data.loc[mid_point-20:mid_point-15, 'Close'] *= 1.05
                base_data.loc[mid_point-20:mid_point-15, 'High'] *= 1.06
                
                # Create head (higher peak)
                base_data.loc[mid_point-5:mid_point+5, 'Close'] *= 1.12
                base_data.loc[mid_point-5:mid_point+5, 'High'] *= 1.15
                
                # Create right shoulder
                base_data.loc[mid_point+15:mid_point+20, 'Close'] *= 1.04
                base_data.loc[mid_point+15:mid_point+20, 'High'] *= 1.05
                
            elif pattern_type == "double_top":
                # Create double top pattern
                peak1 = len(base_data) // 3
                peak2 = 2 * len(base_data) // 3
                
                # First peak
                base_data.loc[peak1-5:peak1+5, 'Close'] *= 1.08
                base_data.loc[peak1-5:peak1+5, 'High'] *= 1.10
                
                # Second peak (similar height)
                base_data.loc[peak2-5:peak2+5, 'Close'] *= 1.07
                base_data.loc[peak2-5:peak2+5, 'High'] *= 1.09
                
            elif pattern_type == "triangle":
                # Create ascending triangle
                start_idx = len(base_data) // 4
                end_idx = 3 * len(base_data) // 4
                
                # Create resistance line (horizontal)
                resistance_level = base_data.loc[start_idx:end_idx, 'Close'].max() * 1.02
                
                # Gradually increase lows (ascending support)
                for i in range(start_idx, end_idx):
                    progress = (i - start_idx) / (end_idx - start_idx)
                    min_price = base_data.loc[start_idx, 'Close'] * (1 + progress * 0.05)
                    if base_data.loc[i, 'Low'] < min_price:
                        base_data.loc[i, 'Low'] = min_price
                        base_data.loc[i, 'Close'] = max(base_data.loc[i, 'Close'], min_price)
                
                # Cap highs at resistance
                base_data.loc[start_idx:end_idx, 'High'] = np.minimum(
                    base_data.loc[start_idx:end_idx, 'High'], 
                    resistance_level
                )
            
            # Recalculate OHLC consistency
            base_data['High'] = base_data[['Open', 'High', 'Low', 'Close']].max(axis=1)
            base_data['Low'] = base_data[['Open', 'High', 'Low', 'Close']].min(axis=1)
            
            logger.info(f"Generated demo data with {pattern_type} pattern")
            return base_data
            
        except Exception as e:
            logger.error(f"Error generating pattern data: {str(e)}")
            return DemoDataGenerator.generate_realistic_stock_data(days=100)
    
    @staticmethod
    def generate_high_volume_data() -> pd.DataFrame:
        """Generate data with interesting volume patterns"""
        try:
            data = DemoDataGenerator.generate_realistic_stock_data(days=180, volatility=0.025)
            
            # Add volume spikes at random intervals
            spike_days = np.random.choice(len(data), size=10, replace=False)
            
            for day in spike_days:
                # Create volume spike with corresponding price movement
                volume_multiplier = np.random.uniform(3, 8)
                price_movement = np.random.uniform(-0.05, 0.05)
                
                data.loc[day, 'Volume'] *= volume_multiplier
                data.loc[day, 'Close'] *= (1 + price_movement)
                data.loc[day, 'High'] = max(data.loc[day, 'High'], data.loc[day, 'Close'])
                data.loc[day, 'Low'] = min(data.loc[day, 'Low'], data.loc[day, 'Close'])
            
            logger.info("Generated demo data with volume patterns")
            return data
            
        except Exception as e:
            logger.error(f"Error generating volume data: {str(e)}")
            return DemoDataGenerator.generate_realistic_stock_data(days=180)
