"""
Advanced AI Risk Management System
Real-time risk assessment and management using AI/ML techniques
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RiskMetrics:
    """Risk assessment metrics"""
    var_95: float  # Value at Risk (95% confidence)
    var_99: float  # Value at Risk (99% confidence)
    expected_shortfall: float  # Expected Shortfall (CVaR)
    max_drawdown: float  # Maximum drawdown
    volatility: float  # Annualized volatility
    sharpe_ratio: float  # Risk-adjusted return
    beta: float  # Market beta
    correlation_risk: float  # Portfolio correlation risk
    concentration_risk: float  # Position concentration risk
    liquidity_risk: float  # Liquidity risk score
    overall_risk_score: float  # Overall risk rating (0-100)
    risk_level: str  # LOW, MEDIUM, HIGH, EXTREME
    timestamp: datetime

@dataclass
class PositionRisk:
    """Individual position risk assessment"""
    symbol: str
    position_size: float
    current_price: float
    entry_price: float
    unrealized_pnl: float
    position_risk: float  # Position-specific risk score
    stop_loss: Optional[float]
    take_profit: Optional[float]
    risk_reward_ratio: float
    time_in_position: timedelta
    volatility_risk: float
    correlation_risk: float
    liquidity_risk: float
    recommended_action: str  # HOLD, REDUCE, CLOSE, HEDGE

@dataclass
class RiskAlert:
    """Risk alert notification"""
    alert_type: str  # VaR_BREACH, DRAWDOWN, CONCENTRATION, CORRELATION
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    message: str
    affected_positions: List[str]
    recommended_actions: List[str]
    timestamp: datetime

class AIRiskManager:
    """Advanced AI-powered risk management system"""

    def __init__(self, risk_free_rate: float = 0.02):
        self.risk_free_rate = risk_free_rate
        self.risk_limits = {
            'max_position_size': 0.10,  # 10% max position size
            'max_sector_exposure': 0.30,  # 30% max sector exposure
            'max_correlation': 0.80,  # 80% max correlation
            'max_drawdown': 0.15,  # 15% max drawdown
            'min_liquidity_score': 0.30,  # 30% min liquidity
            'var_limit_95': 0.05,  # 5% daily VaR limit
            'var_limit_99': 0.08  # 8% daily VaR limit
        }

        self.risk_history = []
        self.alert_history = []

    def assess_portfolio_risk(self, portfolio_data: Dict, market_data: Dict) -> RiskMetrics:
        """
        Comprehensive portfolio risk assessment using AI techniques
        """
        try:
            logger.info("Starting comprehensive portfolio risk assessment")

            # Extract portfolio information
            positions = portfolio_data.get('positions', {})
            returns = portfolio_data.get('returns', [])
            portfolio_value = portfolio_data.get('total_value', 0)

            if not positions or portfolio_value == 0:
                return self._create_minimal_risk_metrics()

            # Calculate Value at Risk (VaR)
            var_95, var_99 = self._calculate_var(returns)

            # Calculate Expected Shortfall (CVaR)
            expected_shortfall = self._calculate_expected_shortfall(returns, var_95)

            # Calculate maximum drawdown
            max_drawdown = self._calculate_max_drawdown(returns)

            # Calculate volatility metrics
            volatility = self._calculate_volatility(returns)

            # Calculate Sharpe ratio
            sharpe_ratio = self._calculate_sharpe_ratio(returns, volatility)

            # Calculate market beta
            beta = self._calculate_portfolio_beta(positions, market_data)

            # Calculate correlation risk
            correlation_risk = self._calculate_correlation_risk(positions, market_data)

            # Calculate concentration risk
            concentration_risk = self._calculate_concentration_risk(positions, portfolio_value)

            # Calculate liquidity risk
            liquidity_risk = self._calculate_liquidity_risk(positions, market_data)

            # Calculate overall risk score
            overall_risk_score = self._calculate_overall_risk_score(
                var_95, max_drawdown, volatility, concentration_risk,
                correlation_risk, liquidity_risk
            )

            # Determine risk level
            risk_level = self._determine_risk_level(overall_risk_score)

            risk_metrics = RiskMetrics(
                var_95=var_95,
                var_99=var_99,
                expected_shortfall=expected_shortfall,
                max_drawdown=max_drawdown,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                beta=beta,
                correlation_risk=correlation_risk,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk,
                overall_risk_score=overall_risk_score,
                risk_level=risk_level,
                timestamp=datetime.now()
            )

            # Store risk history
            self.risk_history.append(risk_metrics)

            # Check for risk alerts
            self._check_risk_alerts(risk_metrics, positions)

            logger.info(f"Risk assessment completed. Overall risk: {risk_level} ({overall_risk_score:.1f})")
            return risk_metrics

        except Exception as e:
            logger.error(f"Error in portfolio risk assessment: {str(e)}")
            return self._create_minimal_risk_metrics()

    def _calculate_var(self, returns: List[float], confidence_levels: List[float] = [0.95, 0.99]) -> Tuple[float, float]:
        """Calculate Value at Risk using historical simulation"""

        try:
            if not returns or len(returns) < 10:
                return 0.05, 0.08  # Default values

            returns_array = np.array(returns)

            # Calculate VaR at different confidence levels
            var_95 = np.percentile(returns_array, 5)  # 5th percentile for 95% VaR
            var_99 = np.percentile(returns_array, 1)  # 1st percentile for 99% VaR

            # Convert to positive values (loss amounts)
            var_95 = abs(var_95) if var_95 < 0 else 0.01
            var_99 = abs(var_99) if var_99 < 0 else 0.02

            return var_95, var_99

        except Exception as e:
            logger.error(f"Error calculating VaR: {str(e)}")
            return 0.05, 0.08

    def _calculate_expected_shortfall(self, returns: List[float], var_95: float) -> float:
        """Calculate Expected Shortfall (Conditional VaR)"""

        try:
            if not returns:
                return var_95 * 1.3  # Approximate ES as 1.3x VaR

            returns_array = np.array(returns)

            # Find returns worse than VaR
            var_threshold = -var_95  # Convert back to negative for comparison
            tail_returns = returns_array[returns_array <= var_threshold]

            if len(tail_returns) > 0:
                expected_shortfall = abs(np.mean(tail_returns))
            else:
                expected_shortfall = var_95 * 1.3

            return expected_shortfall

        except Exception as e:
            logger.error(f"Error calculating Expected Shortfall: {str(e)}")
            return var_95 * 1.3

    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """Calculate maximum drawdown"""

        try:
            if not returns or len(returns) < 2:
                return 0.0

            # Calculate cumulative returns
            cumulative_returns = np.cumprod(1 + np.array(returns))

            # Calculate running maximum
            running_max = np.maximum.accumulate(cumulative_returns)

            # Calculate drawdown
            drawdown = (cumulative_returns - running_max) / running_max

            # Return maximum drawdown (positive value)
            max_drawdown = abs(np.min(drawdown))

            return max_drawdown

        except Exception as e:
            logger.error(f"Error calculating max drawdown: {str(e)}")
            return 0.0

    def _calculate_volatility(self, returns: List[float]) -> float:
        """Calculate annualized volatility"""

        try:
            if not returns or len(returns) < 2:
                return 0.20  # Default 20% volatility

            returns_array = np.array(returns)
            daily_vol = np.std(returns_array)

            # Annualize (assuming 252 trading days)
            annualized_vol = daily_vol * np.sqrt(252)

            return annualized_vol

        except Exception as e:
            logger.error(f"Error calculating volatility: {str(e)}")
            return 0.20

    def _calculate_sharpe_ratio(self, returns: List[float], volatility: float) -> float:
        """Calculate Sharpe ratio"""

        try:
            if not returns or volatility == 0:
                return 0.0

            avg_return = np.mean(returns) * 252  # Annualize
            excess_return = avg_return - self.risk_free_rate

            sharpe_ratio = excess_return / volatility

            return sharpe_ratio

        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {str(e)}")
            return 0.0

    def _calculate_portfolio_beta(self, positions: Dict, market_data: Dict) -> float:
        """Calculate portfolio beta relative to market"""

        try:
            if not positions:
                return 1.0  # Default beta

            # Simulate beta calculation (in real implementation, use actual market data)
            weighted_beta = 0.0
            total_weight = 0.0

            for symbol, position in positions.items():
                weight = position.get('weight', 0.0)

                # Simulate individual stock beta (normally calculated from historical data)
                individual_beta = np.random.normal(1.0, 0.3)  # Mean 1.0, std 0.3
                individual_beta = max(0.1, min(2.5, individual_beta))  # Clamp to reasonable range

                weighted_beta += weight * individual_beta
                total_weight += weight

            portfolio_beta = weighted_beta / total_weight if total_weight > 0 else 1.0

            return portfolio_beta

        except Exception as e:
            logger.error(f"Error calculating portfolio beta: {str(e)}")
            return 1.0

    def _calculate_correlation_risk(self, positions: Dict, market_data: Dict) -> float:
        """Calculate portfolio correlation risk"""

        try:
            if len(positions) < 2:
                return 0.0  # No correlation risk with single position

            symbols = list(positions.keys())
            n_stocks = len(symbols)

            # Simulate correlation matrix (in real implementation, calculate from historical data)
            correlation_matrix = np.random.uniform(0.3, 0.8, (n_stocks, n_stocks))
            np.fill_diagonal(correlation_matrix, 1.0)

            # Make matrix symmetric
            correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
            np.fill_diagonal(correlation_matrix, 1.0)

            # Calculate average correlation
            upper_triangle = correlation_matrix[np.triu_indices(n_stocks, k=1)]
            avg_correlation = np.mean(upper_triangle)

            # Convert to risk score (higher correlation = higher risk)
            correlation_risk = avg_correlation

            return correlation_risk

        except Exception as e:
            logger.error(f"Error calculating correlation risk: {str(e)}")
            return 0.5

    def _calculate_concentration_risk(self, positions: Dict, portfolio_value: float) -> float:
        """Calculate position concentration risk"""

        try:
            if not positions or portfolio_value == 0:
                return 0.0

            # Calculate position weights
            weights = []
            for symbol, position in positions.items():
                position_value = position.get('value', 0.0)
                weight = position_value / portfolio_value
                weights.append(weight)

            if not weights:
                return 0.0

            # Calculate Herfindahl-Hirschman Index (HHI)
            hhi = sum(w**2 for w in weights)

            # Convert to risk score (higher concentration = higher risk)
            # HHI ranges from 1/n (perfectly diversified) to 1 (fully concentrated)
            n_positions = len(weights)
            min_hhi = 1.0 / n_positions
            concentration_risk = (hhi - min_hhi) / (1.0 - min_hhi) if n_positions > 1 else 1.0

            return concentration_risk

        except Exception as e:
            logger.error(f"Error calculating concentration risk: {str(e)}")
            return 0.5

    def _calculate_liquidity_risk(self, positions: Dict, market_data: Dict) -> float:
        """Calculate portfolio liquidity risk"""

        try:
            if not positions:
                return 0.0

            total_liquidity_score = 0.0
            total_weight = 0.0

            for symbol, position in positions.items():
                weight = position.get('weight', 0.0)

                # Simulate liquidity score (in real implementation, use volume, bid-ask spread, etc.)
                # Higher score = more liquid = lower risk
                liquidity_score = np.random.uniform(0.3, 0.9)

                total_liquidity_score += weight * liquidity_score
                total_weight += weight

            avg_liquidity_score = total_liquidity_score / total_weight if total_weight > 0 else 0.5

            # Convert to risk (lower liquidity = higher risk)
            liquidity_risk = 1.0 - avg_liquidity_score

            return liquidity_risk

        except Exception as e:
            logger.error(f"Error calculating liquidity risk: {str(e)}")
            return 0.5

    def _calculate_overall_risk_score(self, var_95: float, max_drawdown: float,
                                    volatility: float, concentration_risk: float,
                                    correlation_risk: float, liquidity_risk: float) -> float:
        """Calculate overall risk score (0-100)"""

        try:
            # Normalize individual risk components to 0-1 scale
            var_score = min(1.0, var_95 / 0.10)  # Normalize by 10% VaR
            drawdown_score = min(1.0, max_drawdown / 0.30)  # Normalize by 30% drawdown
            volatility_score = min(1.0, volatility / 0.50)  # Normalize by 50% volatility

            # Weighted combination of risk factors
            risk_components = {
                'var': var_score * 0.25,
                'drawdown': drawdown_score * 0.20,
                'volatility': volatility_score * 0.20,
                'concentration': concentration_risk * 0.15,
                'correlation': correlation_risk * 0.10,
                'liquidity': liquidity_risk * 0.10
            }

            overall_risk = sum(risk_components.values())

            # Convert to 0-100 scale
            risk_score = overall_risk * 100

            return min(100.0, max(0.0, risk_score))

        except Exception as e:
            logger.error(f"Error calculating overall risk score: {str(e)}")
            return 50.0

    def _determine_risk_level(self, risk_score: float) -> str:
        """Determine risk level based on overall risk score"""

        if risk_score < 25:
            return "LOW"
        elif risk_score < 50:
            return "MEDIUM"
        elif risk_score < 75:
            return "HIGH"
        else:
            return "EXTREME"

    def _check_risk_alerts(self, risk_metrics: RiskMetrics, positions: Dict):
        """Check for risk limit breaches and generate alerts"""

        alerts = []

        # VaR breach alerts
        if risk_metrics.var_95 > self.risk_limits['var_limit_95']:
            alerts.append(RiskAlert(
                alert_type="VAR_BREACH",
                severity="HIGH",
                message=f"95% VaR ({risk_metrics.var_95:.2%}) exceeds limit ({self.risk_limits['var_limit_95']:.2%})",
                affected_positions=list(positions.keys()),
                recommended_actions=["Reduce position sizes", "Increase diversification"],
                timestamp=datetime.now()
            ))

        # Drawdown alerts
        if risk_metrics.max_drawdown > self.risk_limits['max_drawdown']:
            alerts.append(RiskAlert(
                alert_type="DRAWDOWN",
                severity="CRITICAL",
                message=f"Maximum drawdown ({risk_metrics.max_drawdown:.2%}) exceeds limit ({self.risk_limits['max_drawdown']:.2%})",
                affected_positions=list(positions.keys()),
                recommended_actions=["Review stop-loss levels", "Consider portfolio rebalancing"],
                timestamp=datetime.now()
            ))

        # Concentration risk alerts
        if risk_metrics.concentration_risk > 0.7:
            alerts.append(RiskAlert(
                alert_type="CONCENTRATION",
                severity="MEDIUM",
                message=f"High concentration risk detected ({risk_metrics.concentration_risk:.2%})",
                affected_positions=list(positions.keys()),
                recommended_actions=["Diversify holdings", "Reduce large positions"],
                timestamp=datetime.now()
            ))

        # Correlation risk alerts
        if risk_metrics.correlation_risk > self.risk_limits['max_correlation']:
            alerts.append(RiskAlert(
                alert_type="CORRELATION",
                severity="MEDIUM",
                message=f"High correlation risk ({risk_metrics.correlation_risk:.2%}) detected",
                affected_positions=list(positions.keys()),
                recommended_actions=["Add uncorrelated assets", "Review sector exposure"],
                timestamp=datetime.now()
            ))

        # Store alerts
        self.alert_history.extend(alerts)

        # Log alerts
        for alert in alerts:
            logger.warning(f"RISK ALERT [{alert.severity}]: {alert.message}")

    def _create_minimal_risk_metrics(self) -> RiskMetrics:
        """Create minimal risk metrics as fallback"""

        return RiskMetrics(
            var_95=0.01,
            var_99=0.02,
            expected_shortfall=0.015,
            max_drawdown=0.0,
            volatility=0.15,
            sharpe_ratio=0.0,
            beta=1.0,
            correlation_risk=0.0,
            concentration_risk=0.0,
            liquidity_risk=0.0,
            overall_risk_score=10.0,
            risk_level="LOW",
            timestamp=datetime.now()
        )