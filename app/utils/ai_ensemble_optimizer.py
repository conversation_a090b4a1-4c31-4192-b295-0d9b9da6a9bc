"""
Advanced AI Ensemble Model Optimizer
Automatically optimizes model combinations and weights using advanced ML techniques
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import joblib
import os
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.preprocessing import StandardScaler
import optuna
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelPerformance:
    """Model performance metrics"""
    model_name: str
    mse: float
    mae: float
    r2: float
    accuracy: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    avg_return: float
    volatility: float
    last_updated: datetime

@dataclass
class EnsembleConfig:
    """Ensemble configuration"""
    models: Dict[str, Any]
    weights: Dict[str, float]
    performance: Dict[str, ModelPerformance]
    optimization_history: List[Dict]
    last_optimized: datetime
    confidence_score: float

class AIEnsembleOptimizer:
    """Advanced AI ensemble model optimizer with automatic weight optimization"""
    
    def __init__(self, models_dir: str = "models/ensemble"):
        self.models_dir = models_dir
        self.ensure_models_dir()
        
        # Base models for ensemble
        self.base_models = {
            'random_forest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            ),
            'ridge_regression': Ridge(
                alpha=1.0,
                random_state=42
            ),
            'elastic_net': ElasticNet(
                alpha=1.0,
                l1_ratio=0.5,
                random_state=42
            ),
            'svr': SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale'
            ),
            'neural_network': MLPRegressor(
                hidden_layer_sizes=(100, 50),
                max_iter=500,
                random_state=42,
                early_stopping=True
            )
        }
        
        self.scaler = StandardScaler()
        self.ensemble_config = None
        self.optimization_study = None

    def ensure_models_dir(self):
        """Ensure models directory exists"""
        os.makedirs(self.models_dir, exist_ok=True)

    def optimize_ensemble(self, X: pd.DataFrame, y: pd.Series,
                         optimization_trials: int = 100) -> EnsembleConfig:
        """
        Optimize ensemble using advanced techniques
        """
        try:
            logger.info(f"Starting ensemble optimization with {optimization_trials} trials")
            
            # Prepare data
            X_scaled = self.scaler.fit_transform(X)
            
            # Train base models
            trained_models = self._train_base_models(X_scaled, y)
            
            # Optimize ensemble weights using Optuna
            optimized_weights = self._optimize_weights_optuna(
                trained_models, X_scaled, y, optimization_trials
            )
            
            # Evaluate ensemble performance
            performance_metrics = self._evaluate_ensemble_performance(
                trained_models, optimized_weights, X_scaled, y
            )
            
            # Create ensemble configuration
            ensemble_config = EnsembleConfig(
                models=trained_models,
                weights=optimized_weights,
                performance=performance_metrics,
                optimization_history=[],
                last_optimized=datetime.now(),
                confidence_score=self._calculate_confidence_score(performance_metrics)
            )
            
            # Save ensemble configuration
            self._save_ensemble_config(ensemble_config)
            
            self.ensemble_config = ensemble_config
            logger.info("Ensemble optimization completed successfully")
            
            return ensemble_config
            
        except Exception as e:
            logger.error(f"Error optimizing ensemble: {str(e)}")
            return self._create_default_ensemble_config()

    def _train_base_models(self, X: np.ndarray, y: pd.Series) -> Dict[str, Any]:
        """Train all base models"""
        
        trained_models = {}
        
        for model_name, model in self.base_models.items():
            try:
                logger.info(f"Training {model_name}")
                
                # Use time series cross-validation for better evaluation
                tscv = TimeSeriesSplit(n_splits=5)
                
                # Train model
                model.fit(X, y)
                trained_models[model_name] = model
                
                logger.info(f"Successfully trained {model_name}")
                
            except Exception as e:
                logger.error(f"Error training {model_name}: {str(e)}")
                continue
        
        return trained_models

    def _optimize_weights_optuna(self, models: Dict[str, Any], X: np.ndarray,
                                y: pd.Series, n_trials: int) -> Dict[str, float]:
        """Optimize ensemble weights using Optuna"""
        
        def objective(trial):
            # Generate weights for each model
            weights = {}
            for model_name in models.keys():
                weights[model_name] = trial.suggest_float(f'weight_{model_name}', 0.0, 1.0)
            
            # Normalize weights to sum to 1
            total_weight = sum(weights.values())
            if total_weight > 0:
                weights = {k: v/total_weight for k, v in weights.items()}
            else:
                # Fallback to equal weights
                weights = {k: 1.0/len(models) for k in models.keys()}
            
            # Calculate ensemble predictions
            ensemble_pred = self._calculate_ensemble_predictions(models, weights, X)
            
            # Calculate objective (minimize MSE)
            mse = mean_squared_error(y, ensemble_pred)
            
            return mse
        
        try:
            # Create Optuna study
            study = optuna.create_study(direction='minimize')
            study.optimize(objective, n_trials=n_trials, show_progress_bar=False)
            
            # Extract best weights
            best_weights = {}
            for model_name in models.keys():
                best_weights[model_name] = study.best_params[f'weight_{model_name}']
            
            # Normalize weights
            total_weight = sum(best_weights.values())
            if total_weight > 0:
                best_weights = {k: v/total_weight for k, v in best_weights.items()}
            else:
                best_weights = {k: 1.0/len(models) for k in models.keys()}
            
            self.optimization_study = study
            logger.info(f"Optimization completed. Best MSE: {study.best_value:.6f}")
            
            return best_weights
            
        except Exception as e:
            logger.error(f"Error in Optuna optimization: {str(e)}")
            # Fallback to equal weights
            return {model_name: 1.0/len(models) for model_name in models.keys()}

    def _calculate_ensemble_predictions(self, models: Dict[str, Any], 
                                      weights: Dict[str, float], X: np.ndarray) -> np.ndarray:
        """Calculate weighted ensemble predictions"""
        
        ensemble_pred = np.zeros(len(X))
        
        for model_name, model in models.items():
            try:
                pred = model.predict(X)
                weight = weights.get(model_name, 0.0)
                ensemble_pred += weight * pred
            except Exception as e:
                logger.error(f"Error getting predictions from {model_name}: {str(e)}")
                continue
        
        return ensemble_pred

    def _evaluate_ensemble_performance(self, models: Dict[str, Any],
                                     weights: Dict[str, float],
                                     X: np.ndarray, y: pd.Series) -> Dict[str, ModelPerformance]:
        """Evaluate performance of individual models and ensemble"""
        
        performance_metrics = {}
        
        # Evaluate individual models
        for model_name, model in models.items():
            try:
                pred = model.predict(X)
                performance = self._calculate_model_performance(y, pred, model_name)
                performance_metrics[model_name] = performance
            except Exception as e:
                logger.error(f"Error evaluating {model_name}: {str(e)}")
                continue
        
        # Evaluate ensemble
        try:
            ensemble_pred = self._calculate_ensemble_predictions(models, weights, X)
            ensemble_performance = self._calculate_model_performance(y, ensemble_pred, "ensemble")
            performance_metrics["ensemble"] = ensemble_performance
        except Exception as e:
            logger.error(f"Error evaluating ensemble: {str(e)}")
        
        return performance_metrics

    def _calculate_model_performance(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                   model_name: str) -> ModelPerformance:
        """Calculate comprehensive model performance metrics"""
        
        try:
            # Basic metrics
            mse = mean_squared_error(y_true, y_pred)
            mae = mean_absolute_error(y_true, y_pred)
            r2 = r2_score(y_true, y_pred)
            
            # Calculate returns for financial metrics
            returns_true = np.diff(y_true) / y_true[:-1]
            returns_pred = np.diff(y_pred) / y_pred[:-1]
            
            # Direction accuracy
            direction_true = np.sign(returns_true)
            direction_pred = np.sign(returns_pred)
            accuracy = np.mean(direction_true == direction_pred)
            
            # Financial metrics
            avg_return = np.mean(returns_pred)
            volatility = np.std(returns_pred)
            
            # Sharpe ratio (assuming risk-free rate = 0)
            sharpe_ratio = avg_return / volatility if volatility > 0 else 0.0
            
            # Maximum drawdown
            cumulative_returns = np.cumprod(1 + returns_pred)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdown)
            
            # Win rate
            win_rate = np.mean(returns_pred > 0)
            
            return ModelPerformance(
                model_name=model_name,
                mse=mse,
                mae=mae,
                r2=r2,
                accuracy=accuracy,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                avg_return=avg_return,
                volatility=volatility,
                last_updated=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error calculating performance for {model_name}: {str(e)}")
            return ModelPerformance(
                model_name=model_name,
                mse=float('inf'),
                mae=float('inf'),
                r2=-float('inf'),
                accuracy=0.0,
                sharpe_ratio=0.0,
                max_drawdown=-1.0,
                win_rate=0.0,
                avg_return=0.0,
                volatility=0.0,
                last_updated=datetime.now()
            )

    def _calculate_confidence_score(self, performance_metrics: Dict[str, ModelPerformance]) -> float:
        """Calculate overall confidence score for the ensemble"""
        
        try:
            if "ensemble" not in performance_metrics:
                return 0.0
            
            ensemble_perf = performance_metrics["ensemble"]
            
            # Combine multiple metrics for confidence
            r2_score = max(0, ensemble_perf.r2)  # R² score (0 to 1)
            accuracy_score = ensemble_perf.accuracy  # Direction accuracy (0 to 1)
            sharpe_score = min(1, max(0, (ensemble_perf.sharpe_ratio + 1) / 2))  # Normalized Sharpe
            
            # Weighted combination
            confidence = (
                r2_score * 0.4 +
                accuracy_score * 0.3 +
                sharpe_score * 0.3
            )
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating confidence score: {str(e)}")
            return 0.0

    def predict(self, X: pd.DataFrame) -> Dict[str, Any]:
        """Make predictions using the optimized ensemble"""
        
        try:
            if self.ensemble_config is None:
                logger.warning("No ensemble configuration loaded")
                return self._create_default_prediction(len(X))
            
            # Scale input data
            X_scaled = self.scaler.transform(X)
            
            # Get individual model predictions
            individual_predictions = {}
            for model_name, model in self.ensemble_config.models.items():
                try:
                    pred = model.predict(X_scaled)
                    individual_predictions[model_name] = pred
                except Exception as e:
                    logger.error(f"Error getting prediction from {model_name}: {str(e)}")
                    continue
            
            # Calculate ensemble prediction
            ensemble_pred = self._calculate_ensemble_predictions(
                self.ensemble_config.models,
                self.ensemble_config.weights,
                X_scaled
            )
            
            # Calculate prediction confidence
            prediction_confidence = self._calculate_prediction_confidence(individual_predictions)
            
            return {
                'ensemble_prediction': ensemble_pred,
                'individual_predictions': individual_predictions,
                'weights': self.ensemble_config.weights,
                'confidence': prediction_confidence,
                'model_performance': self.ensemble_config.performance,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error making ensemble prediction: {str(e)}")
            return self._create_default_prediction(len(X))

    def _calculate_prediction_confidence(self, individual_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """Calculate confidence for each prediction based on model agreement"""
        
        try:
            if not individual_predictions:
                return np.array([0.5])  # Default confidence
            
            # Stack predictions
            pred_matrix = np.column_stack(list(individual_predictions.values()))
            
            # Calculate standard deviation across models (lower = higher confidence)
            pred_std = np.std(pred_matrix, axis=1)
            
            # Normalize to 0-1 scale (higher = more confident)
            max_std = np.max(pred_std) if len(pred_std) > 0 else 1.0
            confidence = 1.0 - (pred_std / max_std) if max_std > 0 else np.ones_like(pred_std)
            
            return confidence
            
        except Exception as e:
            logger.error(f"Error calculating prediction confidence: {str(e)}")
            return np.array([0.5])

    def _save_ensemble_config(self, config: EnsembleConfig):
        """Save ensemble configuration to disk"""
        
        try:
            config_path = os.path.join(self.models_dir, "ensemble_config.joblib")
            joblib.dump(config, config_path)
            
            # Save scaler separately
            scaler_path = os.path.join(self.models_dir, "scaler.joblib")
            joblib.dump(self.scaler, scaler_path)
            
            logger.info(f"Ensemble configuration saved to {config_path}")
            
        except Exception as e:
            logger.error(f"Error saving ensemble configuration: {str(e)}")

    def load_ensemble_config(self) -> bool:
        """Load ensemble configuration from disk"""
        
        try:
            config_path = os.path.join(self.models_dir, "ensemble_config.joblib")
            scaler_path = os.path.join(self.models_dir, "scaler.joblib")
            
            if os.path.exists(config_path) and os.path.exists(scaler_path):
                self.ensemble_config = joblib.load(config_path)
                self.scaler = joblib.load(scaler_path)
                
                logger.info("Ensemble configuration loaded successfully")
                return True
            else:
                logger.warning("No saved ensemble configuration found")
                return False
                
        except Exception as e:
            logger.error(f"Error loading ensemble configuration: {str(e)}")
            return False

    def _create_default_ensemble_config(self) -> EnsembleConfig:
        """Create default ensemble configuration as fallback"""
        
        return EnsembleConfig(
            models={},
            weights={},
            performance={},
            optimization_history=[],
            last_optimized=datetime.now(),
            confidence_score=0.0
        )

    def _create_default_prediction(self, n_samples: int) -> Dict[str, Any]:
        """Create default prediction as fallback"""
        
        return {
            'ensemble_prediction': np.zeros(n_samples),
            'individual_predictions': {},
            'weights': {},
            'confidence': np.array([0.0] * n_samples),
            'model_performance': {},
            'timestamp': datetime.now()
        }

    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of optimization results"""
        
        try:
            if self.ensemble_config is None:
                return {"status": "No ensemble configuration available"}
            
            summary = {
                "optimization_date": self.ensemble_config.last_optimized,
                "confidence_score": self.ensemble_config.confidence_score,
                "model_weights": self.ensemble_config.weights,
                "model_count": len(self.ensemble_config.models),
                "performance_summary": {}
            }
            
            # Add performance summary
            if "ensemble" in self.ensemble_config.performance:
                ensemble_perf = self.ensemble_config.performance["ensemble"]
                summary["performance_summary"] = {
                    "r2_score": ensemble_perf.r2,
                    "accuracy": ensemble_perf.accuracy,
                    "sharpe_ratio": ensemble_perf.sharpe_ratio,
                    "win_rate": ensemble_perf.win_rate,
                    "max_drawdown": ensemble_perf.max_drawdown
                }
            
            # Add optimization study info if available
            if self.optimization_study:
                summary["optimization_trials"] = len(self.optimization_study.trials)
                summary["best_mse"] = self.optimization_study.best_value
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting optimization summary: {str(e)}")
            return {"status": "Error retrieving summary", "error": str(e)}
