"""
Standardized error handling for the AI Stocks Bot application.
Provides consistent error handling patterns across the application.

This module provides a comprehensive approach to error handling, including:
- Custom exception classes
- Error logging utilities
- Error recovery mechanisms
- Decorators for standardized error handling
"""
import logging
import functools
import traceback
import sys
import time
import types
from datetime import datetime
from typing import Callable, Any, Optional, Dict, List, Type, Union, Tuple

# Configure logging
logger = logging.getLogger(__name__)

class AppError(Exception):
    """Base exception class for application-specific errors."""
    def __init__(self, message, error_code=None, details=None):
        self.message = message
        self.error_code = error_code
        self.details = details
        self.timestamp = datetime.now()
        super().__init__(self.message)

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message

    def to_dict(self):
        """Convert error to dictionary for API responses or logging."""
        return {
            'error': self.message,
            'error_code': self.error_code,
            'details': self.details,
            'timestamp': self.timestamp.isoformat()
        }

class DataError(AppError):
    """Error related to data loading, processing, or validation."""
    def __init__(self, message, details=None):
        super().__init__(message, error_code='DATA_ERROR', details=details)

class ModelError(AppError):
    """Error related to model training, loading, or prediction."""
    def __init__(self, message, details=None):
        super().__init__(message, error_code='MODEL_ERROR', details=details)

class ScrapingError(AppError):
    """Error related to web scraping or external data sources."""
    def __init__(self, message, details=None):
        super().__init__(message, error_code='SCRAPING_ERROR', details=details)

class ConfigError(AppError):
    """Error related to configuration settings."""
    def __init__(self, message, details=None):
        super().__init__(message, error_code='CONFIG_ERROR', details=details)

class APIError(AppError):
    """Error related to API calls or responses."""
    def __init__(self, message, details=None):
        super().__init__(message, error_code='API_ERROR', details=details)

class DatabaseError(AppError):
    """Error related to database operations."""
    def __init__(self, message, details=None):
        super().__init__(message, error_code='DB_ERROR', details=details)

class ResourceError(AppError):
    """Error related to resource management (memory, files, connections)."""
    def __init__(self, message, details=None):
        super().__init__(message, error_code='RESOURCE_ERROR', details=details)

class ValidationError(AppError):
    """Error related to data validation."""
    def __init__(self, message, details=None):
        super().__init__(message, error_code='VALIDATION_ERROR', details=details)

def handle_errors(fallback_return=None, log_level=logging.ERROR):
    """
    Decorator for handling errors in functions.

    Args:
        fallback_return: Value to return if an error occurs
        log_level: Logging level for errors

    Returns:
        Decorated function with error handling
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except AppError as e:
                # Log application-specific errors
                logger.log(log_level, f"{type(e).__name__}: {str(e)}")
                if e.details:
                    logger.debug(f"Error details: {e.details}")
                return fallback_return
            except Exception as e:
                # Log unexpected errors with traceback
                logger.log(log_level, f"Unexpected error in {func.__name__}: {str(e)}")
                logger.debug(f"Traceback: {traceback.format_exc()}")
                return fallback_return
        return wrapper
    return decorator

def log_execution_time(func):
    """
    Decorator to log function execution time.

    Args:
        func: Function to decorate

    Returns:
        Decorated function that logs execution time
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = datetime.now()
        result = func(*args, **kwargs)
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"{func.__name__} executed in {execution_time:.2f} seconds")
        return result
    return wrapper

def log_exception(exc: Exception, level: int = logging.ERROR, include_traceback: bool = True) -> None:
    """
    Log an exception with standardized formatting.

    Args:
        exc (Exception): The exception to log
        level (int): Logging level (default: logging.ERROR)
        include_traceback (bool): Whether to include the traceback in the log
    """
    # Get exception details
    exc_type = type(exc).__name__
    exc_msg = str(exc)

    # Format the log message
    log_msg = f"{exc_type}: {exc_msg}"

    # Add traceback if requested
    if include_traceback:
        tb = traceback.format_exc()
        log_msg = f"{log_msg}\n{tb}"

    # Log the exception
    logger.log(level, log_msg)

    # If it's an AppError, log additional details
    if isinstance(exc, AppError) and exc.details:
        logger.log(level, f"Error details: {exc.details}")

def handle_exception(exc: Exception,
                     default_return_value: Any = None,
                     raise_exception: bool = False,
                     log_level: int = logging.ERROR) -> Any:
    """
    Handle an exception with standardized approach.

    Args:
        exc (Exception): The exception to handle
        default_return_value (Any): Value to return if not raising the exception
        raise_exception (bool): Whether to re-raise the exception
        log_level (int): Logging level for the exception

    Returns:
        Any: The default return value if not raising the exception

    Raises:
        Exception: The original exception if raise_exception is True
    """
    # Log the exception
    log_exception(exc, level=log_level)

    # Re-raise if requested
    if raise_exception:
        raise exc

    # Return default value
    return default_return_value

def retry_on_exception(max_retries: int = 3,
                       retry_delay: float = 1.0,
                       backoff_factor: float = 2.0,
                       exceptions_to_retry: Optional[Tuple[Type[Exception], ...]] = None,
                       exceptions_to_ignore: Optional[Tuple[Type[Exception], ...]] = None) -> Callable:
    """
    Decorator to retry a function on exception.

    Args:
        max_retries (int): Maximum number of retries
        retry_delay (float): Initial delay between retries in seconds
        backoff_factor (float): Factor to increase delay with each retry
        exceptions_to_retry (Tuple[Type[Exception], ...]): Exception types to retry on
        exceptions_to_ignore (Tuple[Type[Exception], ...]): Exception types to ignore

    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            last_exception = None
            delay = retry_delay

            for retry in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e

                    # Check if we should ignore this exception
                    if exceptions_to_ignore and isinstance(e, exceptions_to_ignore):
                        raise

                    # Check if we should retry this exception
                    if exceptions_to_retry and not isinstance(e, exceptions_to_retry):
                        raise

                    # Check if we've reached max retries
                    if retry >= max_retries:
                        logger.error(f"Max retries ({max_retries}) reached for {func.__name__}")
                        raise

                    # Log the retry
                    logger.warning(
                        f"Retry {retry + 1}/{max_retries} for {func.__name__} "
                        f"after error: {type(e).__name__}: {str(e)}. "
                        f"Waiting {delay:.2f}s"
                    )

                    # Wait before retrying
                    time.sleep(delay)

                    # Increase delay for next retry
                    delay *= backoff_factor

            # This should never be reached, but just in case
            if last_exception:
                raise last_exception
            return None

        return wrapper

    return decorator

def safe_execute(func: Callable,
                 *args: Any,
                 default_return_value: Any = None,
                 log_errors: bool = True,
                 **kwargs: Any) -> Any:
    """
    Safely execute a function and handle any exceptions.

    Args:
        func (Callable): Function to execute
        *args: Arguments to pass to the function
        default_return_value (Any): Value to return if an exception occurs
        log_errors (bool): Whether to log exceptions
        **kwargs: Keyword arguments to pass to the function

    Returns:
        Any: Result of the function or default_return_value on exception
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            log_exception(e)
        return default_return_value

# Context manager for error handling
class ErrorHandler:
    """
    Context manager for standardized error handling.

    Example:
        with ErrorHandler(default_return_value=None, raise_exception=False) as handler:
            result = risky_function()
            return result
    """

    def __init__(self,
                 default_return_value: Any = None,
                 raise_exception: bool = False,
                 log_level: int = logging.ERROR):
        """
        Initialize the error handler.

        Args:
            default_return_value (Any): Value to return if an exception occurs
            raise_exception (bool): Whether to re-raise exceptions
            log_level (int): Logging level for exceptions
        """
        self.default_return_value = default_return_value
        self.raise_exception = raise_exception
        self.log_level = log_level
        self.exception = None

    def __enter__(self) -> 'ErrorHandler':
        return self

    def __exit__(self, exc_type: Optional[Type[Exception]],
                 exc_val: Optional[Exception],
                 exc_tb: Optional[types.TracebackType]) -> bool:
        if exc_val:
            self.exception = exc_val
            log_exception(exc_val, level=self.log_level)
            return not self.raise_exception
        return True

    def result(self, value: Any) -> Any:
        """
        Return the provided value or default_return_value if an exception occurred.

        Args:
            value (Any): Value to return if no exception occurred

        Returns:
            Any: The provided value or default_return_value
        """
        if self.exception:
            return self.default_return_value
        return value

def handle_errors(fallback_return: Any = None,
                log_level: int = logging.ERROR,
                raise_app_errors: bool = False) -> Callable:
    """
    Decorator for standardized error handling.

    Args:
        fallback_return (Any): Value to return on exception
        log_level (int): Logging level for exceptions
        raise_app_errors (bool): Whether to re-raise AppError exceptions

    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Log the exception
                log_exception(e, level=log_level)

                # Check if it's an AppError
                if isinstance(e, AppError) and raise_app_errors:
                    # Re-raise AppErrors if requested
                    raise

                # Return fallback value for other exceptions
                return fallback_return
        return wrapper
    return decorator

def robust_function(max_retries: int = 3,
                   retry_delay: float = 1.0,
                   backoff_factor: float = 2.0,
                   fallback_return: Any = None,
                   exceptions_to_retry: Optional[Tuple[Type[Exception], ...]] = None,
                   exceptions_to_ignore: Optional[Tuple[Type[Exception], ...]] = None,
                   log_level: int = logging.ERROR,
                   log_execution: bool = True) -> Callable:
    """
    Comprehensive decorator that combines error handling, retry logic, and execution logging.

    This decorator provides a robust way to handle function execution with:
    - Automatic retries with exponential backoff
    - Standardized error handling
    - Execution time logging
    - Fallback return value on failure

    Args:
        max_retries (int): Maximum number of retries
        retry_delay (float): Initial delay between retries in seconds
        backoff_factor (float): Factor to increase delay with each retry
        fallback_return (Any): Value to return if all retries fail
        exceptions_to_retry (Tuple[Type[Exception], ...]): Exception types to retry on
        exceptions_to_ignore (Tuple[Type[Exception], ...]): Exception types to ignore
        log_level (int): Logging level for exceptions
        log_execution (bool): Whether to log execution time

    Returns:
        Callable: Decorated function with robust error handling
    """
    def decorator(func: Callable) -> Callable:
        # Apply the retry decorator
        retry_decorator = retry_on_exception(
            max_retries=max_retries,
            retry_delay=retry_delay,
            backoff_factor=backoff_factor,
            exceptions_to_retry=exceptions_to_retry,
            exceptions_to_ignore=exceptions_to_ignore
        )

        # Apply the error handling decorator
        error_decorator = handle_errors(
            fallback_return=fallback_return,
            log_level=log_level
        )

        # Combine the decorators
        decorated_func = error_decorator(retry_decorator(func))

        # Add execution time logging if requested
        if log_execution:
            decorated_func = log_execution_time(decorated_func)

        return decorated_func

    return decorator
