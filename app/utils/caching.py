"""
Caching utilities for the AI Stocks Bot application.

This module provides tools for caching function results and data
to improve performance and reduce redundant operations.
"""

import os
import pickle
import hashlib
import logging
import functools
from typing import Any, Callable, Optional, Dict, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class Cache:
    """Simple disk-based cache for function results"""

    def __init__(self, cache_dir: str = "cache", max_age: Optional[timedelta] = None):
        """
        Initialize the cache.

        Args:
            cache_dir: Directory to store cache files
            max_age: Maximum age of cache entries (None for no expiration)
        """
        self.cache_dir = cache_dir
        self.max_age = max_age

        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)

    def _get_cache_key(self, func_name: str, args: Tuple, kwargs: Dict) -> str:
        """Generate a cache key from function name and arguments"""
        try:
            # Try to create a string representation of the arguments
            arg_str = str(args) + str(sorted(kwargs.items()))

            # Create a hash of the arguments
            arg_hash = hashlib.md5(arg_str.encode()).hexdigest()

            # Combine function name and argument hash
            return f"{func_name}_{arg_hash}"
        except Exception as e:
            # Handle unhashable arguments
            logger.warning(f"Could not create cache key for {func_name}: {str(e)}")

            # Create a fallback key using the function name and timestamp
            fallback_key = f"{func_name}_fallback_{int(time.time())}"
            logger.info(f"Using fallback cache key: {fallback_key}")

            return fallback_key

    def _get_cache_path(self, key: str) -> str:
        """Get the file path for a cache key"""
        return os.path.join(self.cache_dir, f"{key}.pickle")

    def get(self, key: str) -> Tuple[bool, Any]:
        """
        Get a value from the cache.

        Args:
            key: Cache key

        Returns:
            Tuple of (hit, value) where hit is True if the key was found
        """
        cache_path = self._get_cache_path(key)

        # Check if cache file exists
        if not os.path.exists(cache_path):
            return False, None

        try:
            # Check if cache is expired
            if self.max_age is not None:
                file_time = datetime.fromtimestamp(os.path.getmtime(cache_path))
                if datetime.now() - file_time > self.max_age:
                    logger.debug(f"Cache expired for {key}")
                    return False, None

            # Load cache file
            with open(cache_path, "rb") as f:
                value = pickle.load(f)

            logger.debug(f"Cache hit for {key}")
            return True, value

        except Exception as e:
            logger.warning(f"Error reading cache for {key}: {str(e)}")
            return False, None

    def set(self, key: str, value: Any) -> bool:
        """
        Set a value in the cache.

        Args:
            key: Cache key
            value: Value to cache

        Returns:
            True if the value was cached successfully
        """
        cache_path = self._get_cache_path(key)

        try:
            # Save cache file
            with open(cache_path, "wb") as f:
                pickle.dump(value, f)

            logger.debug(f"Cached value for {key}")
            return True

        except Exception as e:
            logger.warning(f"Error writing cache for {key}: {str(e)}")
            return False

    def invalidate(self, key: str) -> bool:
        """
        Invalidate a cache entry.

        Args:
            key: Cache key

        Returns:
            True if the cache entry was invalidated
        """
        cache_path = self._get_cache_path(key)

        if os.path.exists(cache_path):
            try:
                os.remove(cache_path)
                logger.debug(f"Invalidated cache for {key}")
                return True
            except Exception as e:
                logger.warning(f"Error invalidating cache for {key}: {str(e)}")

        return False

    def clear(self) -> int:
        """
        Clear all cache entries.

        Returns:
            Number of cache entries cleared
        """
        count = 0

        for filename in os.listdir(self.cache_dir):
            if filename.endswith(".pickle"):
                try:
                    os.remove(os.path.join(self.cache_dir, filename))
                    count += 1
                except Exception as e:
                    logger.warning(f"Error clearing cache entry {filename}: {str(e)}")

        logger.info(f"Cleared {count} cache entries")
        return count

    def cached(self, func: Optional[Callable] = None, *, ttl: Optional[timedelta] = None):
        """
        Decorator to cache function results.

        Args:
            func: Function to decorate
            ttl: Time-to-live for cache entries (overrides max_age)

        Returns:
            Decorated function
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                key = self._get_cache_key(func.__name__, args, kwargs)

                # Check cache
                hit, value = self.get(key)
                if hit:
                    return value

                # Call function
                value = func(*args, **kwargs)

                # Cache result
                self.set(key, value)

                return value

            return wrapper

        if func is None:
            return decorator

        return decorator(func)

# Create a global cache instance
cache = Cache(max_age=timedelta(hours=24))

# Convenience decorator
def cached(ttl: Optional[timedelta] = None):
    """Decorator to cache function results"""
    return cache.cached(ttl=ttl)

def clear_cache() -> int:
    """Clear all cache entries"""
    return cache.clear()

def invalidate_cache(key: str) -> bool:
    """Invalidate a specific cache entry"""
    return cache.invalidate(key)
