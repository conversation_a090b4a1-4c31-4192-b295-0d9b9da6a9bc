"""
NumPy BitGenerator Fix for MT19937

This module provides fixes for NumPy's BitGenerator and MT19937 classes
to handle compatibility issues with different NumPy versions.
"""

import logging
import sys
import random

logger = logging.getLogger(__name__)

def fix_numpy_mt19937():
    """
    Fix NumPy's MT19937 BitGenerator to handle compatibility issues.
    This is specifically for the error:
    <class 'app.utils.numpy_fix.FixedMT19937'> is not a known BitGenerator module.
    """
    try:
        import numpy as np
        
        # Create a mock BitGenerator if needed
        if not hasattr(np.random, 'BitGenerator'):
            class MockBitGenerator:
                def __init__(self, seed=None):
                    self.seed = seed
                def __repr__(self):
                    return "MockBitGenerator()"
            
            np.random.BitGenerator = MockBitGenerator
            logger.info("Created mock BitGenerator class")
        
        # Create a mock MT19937 class
        class MockMT19937:
            def __init__(self, seed=None):
                self.seed = seed
            
            def jumped(self, *args, **kwargs):
                return self
            
            def __repr__(self):
                return "MockMT19937()"
            
            # Add methods that might be needed
            def next_uint64(self):
                return random.randint(0, 2**64-1)
            
            def next_uint32(self):
                return random.randint(0, 2**32-1)
            
            def next_double(self):
                return random.random()
        
        # Add to numpy.random
        np.random.MT19937 = MockMT19937
        
        # Add to numpy.random._mt19937 if it exists
        if 'numpy.random._mt19937' in sys.modules:
            sys.modules['numpy.random._mt19937'].MT19937 = MockMT19937
        
        # Fix FixedMT19937 specifically
        if 'app.utils.numpy_fix' in sys.modules:
            sys.modules['app.utils.numpy_fix'].FixedMT19937 = MockMT19937
        
        logger.info("Applied MT19937 BitGenerator fix")
        return True
    
    except Exception as e:
        logger.error(f"Error applying NumPy MT19937 fix: {str(e)}")
        return False

# Apply the fix when the module is imported
fix_applied = fix_numpy_mt19937()
