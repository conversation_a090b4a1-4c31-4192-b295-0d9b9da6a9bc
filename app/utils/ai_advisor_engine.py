"""
AI Advisor Analysis Engine
Shared utilities for comprehensive stock analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple

# Configure logging
logger = logging.getLogger(__name__)

def calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """Calculate comprehensive technical indicators - Enhanced with Trade Ideas logic"""
    try:
        # Make a copy to avoid modifying original data
        data = df.copy()

        # Moving Averages (same as Trade Ideas)
        data['SMA20'] = data['Close'].rolling(window=20).mean()
        data['SMA50'] = data['Close'].rolling(window=50).mean()
        data['EMA12'] = data['Close'].ewm(span=12).mean()
        data['EMA26'] = data['Close'].ewm(span=26).mean()

        # RSI (same calculation as Trade Ideas)
        delta = data['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['RSI'] = 100 - (100 / (1 + rs))

        # MACD (same calculation as Trade Ideas)
        data['MACD'] = data['EMA12'] - data['EMA26']
        data['MACD_Signal'] = data['MACD'].ewm(span=9).mean()
        data['MACD_Histogram'] = data['MACD'] - data['MACD_Signal']

        # Bollinger Bands
        data['BB_Middle'] = data['Close'].rolling(window=20).mean()
        bb_std = data['Close'].rolling(window=20).std()
        data['BB_Upper'] = data['BB_Middle'] + (bb_std * 2)
        data['BB_Lower'] = data['BB_Middle'] - (bb_std * 2)

        # ATR (Average True Range) - same calculation as Trade Ideas
        high_low = data['High'] - data['Low']
        high_close = np.abs(data['High'] - data['Close'].shift())
        low_close = np.abs(data['Low'] - data['Close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        data['ATR'] = true_range.rolling(window=14).mean()

        # Volume indicators (same as Trade Ideas)
        data['Volume_SMA'] = data['Volume'].rolling(window=20).mean()
        data['Volume_Ratio'] = data['Volume'] / data['Volume_SMA']

        return data

    except Exception as e:
        logger.error(f"Error calculating technical indicators: {str(e)}")
        return df

def analyze_technical_signals(df: pd.DataFrame, live_price: Optional[float] = None) -> Dict:
    """Analyze technical signals from the data - Using Trade Ideas proven logic"""
    try:
        signals = {}

        # Moving Average signals (exact Trade Ideas logic)
        if 'SMA20' in df.columns and 'SMA50' in df.columns:
            signals['ma_bullish'] = df['Close'].iloc[-1] > df['SMA20'].iloc[-1] > df['SMA50'].iloc[-1]
            signals['ma_bearish'] = df['Close'].iloc[-1] < df['SMA20'].iloc[-1] < df['SMA50'].iloc[-1]

        # RSI signals (exact Trade Ideas logic)
        if 'RSI' in df.columns:
            rsi = df['RSI'].iloc[-1]
            signals['rsi_oversold'] = rsi < 30
            signals['rsi_overbought'] = rsi > 70
            signals['rsi_bullish'] = 30 <= rsi <= 50
            signals['rsi_bearish'] = 50 <= rsi <= 70

        # MACD signals (exact Trade Ideas logic)
        if 'MACD' in df.columns and 'MACD_Signal' in df.columns:
            macd_diff = df['MACD'].iloc[-1] - df['MACD_Signal'].iloc[-1]
            prev_macd_diff = df['MACD'].iloc[-2] - df['MACD_Signal'].iloc[-2]
            signals['macd_bullish_cross'] = macd_diff > 0 and prev_macd_diff <= 0
            signals['macd_bearish_cross'] = macd_diff < 0 and prev_macd_diff >= 0

        # Volume analysis (exact Trade Ideas logic)
        avg_volume = df['Volume'].tail(20).mean()
        current_volume = df['Volume'].iloc[-1]
        signals['high_volume'] = current_volume > avg_volume * 1.5

        # Price momentum (exact Trade Ideas logic)
        price_change_5d = (df['Close'].iloc[-1] - df['Close'].iloc[-6]) / df['Close'].iloc[-6] * 100
        signals['strong_momentum'] = abs(price_change_5d) > 5
        signals['bullish_momentum'] = price_change_5d > 3
        signals['bearish_momentum'] = price_change_5d < -3

        # Support/Resistance levels (exact Trade Ideas logic)
        recent_high = df['High'].tail(20).max()
        recent_low = df['Low'].tail(20).min()
        current_price = df['Close'].iloc[-1]

        signals['near_resistance'] = current_price > recent_high * 0.98
        signals['near_support'] = current_price < recent_low * 1.02
        signals['breakout_potential'] = current_price > recent_high * 0.995

        # Live data validation (enhanced Trade Ideas logic)
        if live_price:
            csv_price = df['Close'].iloc[-1]
            price_diff_pct = ((live_price - csv_price) / csv_price) * 100

            # Add live price momentum signal
            if abs(price_diff_pct) > 1.0:  # Significant price movement since last CSV data
                if price_diff_pct > 0:
                    signals['live_bullish_momentum'] = True
                    signals['live_price_gap_up'] = price_diff_pct > 2.0
                else:
                    signals['live_bearish_momentum'] = True
                    signals['live_price_gap_down'] = price_diff_pct < -2.0

            # Validate support/resistance with live price
            if 'SMA20' in df.columns:
                sma20 = df['SMA20'].iloc[-1]
                signals['live_above_sma20'] = live_price > sma20
                signals['live_below_sma20'] = live_price < sma20

            # Add live data freshness indicator
            signals['live_data_available'] = True
            signals['price_difference_pct'] = price_diff_pct

        return signals

    except Exception as e:
        logger.error(f"Error analyzing technical signals: {str(e)}")
        return {}

def determine_trading_recommendation(signals: Dict, market_condition: str = "Auto-Detect") -> Tuple[str, float, str]:
    """Determine trading recommendation based on signals - Using Trade Ideas proven logic"""
    try:
        bullish_score = 0
        bearish_score = 0

        # Moving average signals (weight: 2) - exact Trade Ideas logic
        if signals.get('ma_bullish', False):
            bullish_score += 2
        if signals.get('ma_bearish', False):
            bearish_score += 2

        # RSI signals (weight: 1.5) - exact Trade Ideas logic
        if signals.get('rsi_oversold', False) or signals.get('rsi_bullish', False):
            bullish_score += 1.5
        if signals.get('rsi_overbought', False) or signals.get('rsi_bearish', False):
            bearish_score += 1.5

        # MACD signals (weight: 2) - exact Trade Ideas logic
        if signals.get('macd_bullish_cross', False):
            bullish_score += 2
        if signals.get('macd_bearish_cross', False):
            bearish_score += 2

        # Volume confirmation (weight: 1) - exact Trade Ideas logic
        if signals.get('high_volume', False):
            if signals.get('bullish_momentum', False):
                bullish_score += 1
            elif signals.get('bearish_momentum', False):
                bearish_score += 1

        # Momentum signals (weight: 1) - exact Trade Ideas logic
        if signals.get('bullish_momentum', False):
            bullish_score += 1
        if signals.get('bearish_momentum', False):
            bearish_score += 1

        # Breakout signals (weight: 1.5) - exact Trade Ideas logic
        if signals.get('breakout_potential', False):
            bullish_score += 1.5
        if signals.get('near_support', False):
            bullish_score += 0.5
        if signals.get('near_resistance', False):
            bearish_score += 0.5

        # Market condition adjustment - exact Trade Ideas logic
        if market_condition == "Bullish":
            bullish_score *= 1.2
        elif market_condition == "Bearish":
            bearish_score *= 1.2

        # Determine direction and confidence - exact Trade Ideas logic
        total_score = bullish_score + bearish_score
        if total_score == 0:
            return "HOLD", 0.0, "No clear signals detected"

        if bullish_score > bearish_score:
            confidence = min(bullish_score / (bullish_score + bearish_score), 0.95)
            return "BUY", confidence, f"Bullish signals detected (score: {bullish_score:.1f})"
        else:
            confidence = min(bearish_score / (bullish_score + bearish_score), 0.95)
            return "SELL", confidence, f"Bearish signals detected (score: {bearish_score:.1f})"

    except Exception as e:
        logger.error(f"Error determining trading recommendation: {str(e)}")
        return "HOLD", 0.0, "Error in analysis"

def generate_recommendation_reasoning(signals: Dict, recommendation: str, confidence: float) -> str:
    """Generate human-readable reasoning for the recommendation"""
    try:
        reasons = []
        
        # Key bullish signals
        if signals.get('golden_cross'):
            reasons.append("Golden cross detected (SMA20 > SMA50)")
        if signals.get('ma_bullish'):
            reasons.append("Price above moving averages")
        if signals.get('rsi_oversold'):
            reasons.append("RSI indicates oversold conditions")
        if signals.get('macd_bullish_cross'):
            reasons.append("MACD bullish crossover")
        if signals.get('live_bullish_momentum'):
            reasons.append("Live price showing bullish momentum")
        if signals.get('breakout_potential'):
            reasons.append("Price near resistance breakout")
        
        # Key bearish signals
        if signals.get('death_cross'):
            reasons.append("Death cross detected (SMA20 < SMA50)")
        if signals.get('ma_bearish'):
            reasons.append("Price below moving averages")
        if signals.get('rsi_overbought'):
            reasons.append("RSI indicates overbought conditions")
        if signals.get('macd_bearish_cross'):
            reasons.append("MACD bearish crossover")
        if signals.get('live_bearish_momentum'):
            reasons.append("Live price showing bearish momentum")
        if signals.get('breakdown_risk'):
            reasons.append("Price near support breakdown")
        
        # Volume confirmation
        if signals.get('high_volume'):
            reasons.append("High volume confirms the move")
        
        # Live data enhancement
        if signals.get('live_data_available'):
            reasons.append("Analysis enhanced with real-time data")
        
        if not reasons:
            return "Mixed signals with no clear directional bias"
        
        return f"Key factors: {', '.join(reasons[:4])}"  # Limit to top 4 reasons
        
    except Exception as e:
        logger.error(f"Error generating reasoning: {str(e)}")
        return "Analysis completed"

def calculate_price_targets(df: pd.DataFrame, recommendation: str, current_price: float, timeframe: str = "Short-term (1-5 days)") -> Dict:
    """Calculate comprehensive price targets, support/resistance levels - Enhanced Trade Ideas logic"""
    try:
        # Calculate ATR for volatility-based targets
        if 'ATR' in df.columns:
            atr = df['ATR'].iloc[-1]
        else:
            # Calculate simple ATR
            high_low = df['High'] - df['Low']
            atr = high_low.tail(14).mean()

        # Calculate support and resistance levels using pivot points
        support_levels, resistance_levels = calculate_support_resistance(df)

        # Timeframe multipliers
        timeframe_multipliers = {
            "Intraday": 1.0,
            "Short-term (1-5 days)": 1.5,
            "Medium-term (1-4 weeks)": 2.5
        }

        multiplier = timeframe_multipliers.get(timeframe, 1.5)

        targets = {
            'entry_price': current_price,
            'support': support_levels[0] if support_levels else current_price * 0.95,
            'resistance': resistance_levels[0] if resistance_levels else current_price * 1.05
        }

        if recommendation == "BUY":
            # Calculate multiple targets for BUY
            targets['target_1'] = current_price + (atr * multiplier * 1.5)  # Conservative target
            targets['target_2'] = current_price + (atr * multiplier * 3.0)  # Aggressive target
            targets['stop_loss'] = max(
                current_price - (atr * multiplier),
                support_levels[0] if support_levels else current_price * 0.95
            )

            # Use the first target as main target for backward compatibility
            targets['target'] = targets['target_1']

        elif recommendation == "SELL":
            # Calculate multiple targets for SELL
            targets['target_1'] = current_price - (atr * multiplier * 1.5)  # Conservative target
            targets['target_2'] = current_price - (atr * multiplier * 3.0)  # Aggressive target
            targets['stop_loss'] = min(
                current_price + (atr * multiplier),
                resistance_levels[0] if resistance_levels else current_price * 1.05
            )

            # Use the first target as main target for backward compatibility
            targets['target'] = targets['target_1']

        else:
            # HOLD recommendation
            targets['target'] = current_price
            targets['target_1'] = current_price
            targets['target_2'] = current_price
            targets['stop_loss'] = current_price

        # Calculate risk-reward ratios for both targets
        risk = abs(current_price - targets['stop_loss'])
        if risk > 0:
            reward_1 = abs(targets['target_1'] - current_price)
            reward_2 = abs(targets['target_2'] - current_price)
            targets['risk_reward'] = reward_1 / risk
            targets['risk_reward_1'] = reward_1 / risk
            targets['risk_reward_2'] = reward_2 / risk
        else:
            targets['risk_reward'] = 0
            targets['risk_reward_1'] = 0
            targets['risk_reward_2'] = 0

        # Add additional support/resistance levels if available
        if len(support_levels) > 1:
            targets['support_2'] = support_levels[1]
        if len(resistance_levels) > 1:
            targets['resistance_2'] = resistance_levels[1]

        return targets

    except Exception as e:
        logger.error(f"Error calculating price targets: {str(e)}")
        return {
            'entry_price': current_price,
            'target': current_price,
            'target_1': current_price,
            'target_2': current_price,
            'stop_loss': current_price,
            'support': current_price * 0.95,
            'resistance': current_price * 1.05,
            'risk_reward': 0,
            'risk_reward_1': 0,
            'risk_reward_2': 0
        }

def calculate_support_resistance(df: pd.DataFrame, window: int = 20) -> tuple:
    """Calculate support and resistance levels using pivot points and price action"""
    try:
        support_levels = []
        resistance_levels = []

        # Get recent price data
        recent_data = df.tail(window * 2)  # Use more data for better levels

        # Method 1: Pivot Points
        highs = recent_data['High'].values
        lows = recent_data['Low'].values
        closes = recent_data['Close'].values

        # Find local minima (support) and maxima (resistance)
        from scipy.signal import argrelextrema
        import numpy as np

        # Find local minima (support levels)
        min_indices = argrelextrema(lows, np.less, order=3)[0]
        if len(min_indices) > 0:
            support_candidates = [lows[i] for i in min_indices]
            # Sort and take the strongest levels (most recent and significant)
            support_levels = sorted(support_candidates, reverse=True)[:3]

        # Find local maxima (resistance levels)
        max_indices = argrelextrema(highs, np.greater, order=3)[0]
        if len(max_indices) > 0:
            resistance_candidates = [highs[i] for i in max_indices]
            # Sort and take the strongest levels
            resistance_levels = sorted(resistance_candidates)[:3]

        # Method 2: Moving Average Support/Resistance
        if 'SMA20' in df.columns and 'SMA50' in df.columns:
            sma20 = df['SMA20'].iloc[-1]
            sma50 = df['SMA50'].iloc[-1]

            # Add moving averages as dynamic support/resistance
            current_price = df['Close'].iloc[-1]
            if current_price > sma20:
                support_levels.append(sma20)
            else:
                resistance_levels.append(sma20)

            if current_price > sma50:
                support_levels.append(sma50)
            else:
                resistance_levels.append(sma50)

        # Method 3: Bollinger Bands Support/Resistance
        if 'BB_Upper' in df.columns and 'BB_Lower' in df.columns:
            bb_upper = df['BB_Upper'].iloc[-1]
            bb_lower = df['BB_Lower'].iloc[-1]
            resistance_levels.append(bb_upper)
            support_levels.append(bb_lower)

        # Clean and sort levels
        current_price = df['Close'].iloc[-1]

        # Filter support levels (below current price)
        support_levels = [level for level in support_levels if level < current_price]
        support_levels = sorted(set(support_levels), reverse=True)  # Closest to current price first

        # Filter resistance levels (above current price)
        resistance_levels = [level for level in resistance_levels if level > current_price]
        resistance_levels = sorted(set(resistance_levels))  # Closest to current price first

        # Ensure we have at least basic levels
        if not support_levels:
            support_levels = [current_price * 0.95, current_price * 0.90]
        if not resistance_levels:
            resistance_levels = [current_price * 1.05, current_price * 1.10]

        return support_levels[:3], resistance_levels[:3]  # Return top 3 levels each

    except Exception as e:
        logger.error(f"Error calculating support/resistance: {str(e)}")
        # Fallback to simple percentage-based levels
        current_price = df['Close'].iloc[-1]
        support_levels = [current_price * 0.95, current_price * 0.90]
        resistance_levels = [current_price * 1.05, current_price * 1.10]
        return support_levels, resistance_levels
