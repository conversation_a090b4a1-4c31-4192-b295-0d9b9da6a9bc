"""
Investment Strategy Simulation Module
Inspired by stocksmith<PERSON><PERSON><PERSON>'s investment simulation features.

This module provides tools to simulate different investment strategies
and optimize portfolio allocation based on AI predictions.
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class InvestmentSimulator:
    """
    Simulates different investment strategies based on AI predictions.
    """
    
    def __init__(self):
        # 2025 contribution limits
        self.ROTH_IRA_LIMIT = 7000  # Updated for 2025
        self.CONTRIB_401K_LIMIT = 23500  # Updated for 2025
        self.CATCHUP_CONTRIBUTION = 1000  # For 50+ years old
        
    def simulate_dca_strategy(self, predictions: List[Dict], 
                            annual_amount: float, 
                            frequency: str = 'biweekly') -> Dict:
        """
        Simulate Dollar Cost Averaging (DCA) strategy.
        
        Args:
            predictions: List of prediction results with prices and dates
            annual_amount: Total annual investment amount
            frequency: 'biweekly', 'monthly', 'quarterly'
            
        Returns:
            Dict with simulation results including total invested, final value, returns
        """
        try:
            # Calculate contribution schedule
            if frequency == 'biweekly':
                periods = 26
                contribution_per_period = annual_amount / periods
            elif frequency == 'monthly':
                periods = 12
                contribution_per_period = annual_amount / periods
            elif frequency == 'quarterly':
                periods = 4
                contribution_per_period = annual_amount / periods
            else:
                raise ValueError(f"Unsupported frequency: {frequency}")
            
            total_shares = 0.0
            total_invested = 0.0
            investment_log = []
            
            for i, prediction in enumerate(predictions[:periods]):
                purchase_price = prediction.get('predicted_price', prediction.get('current_price', 100))
                shares_bought = contribution_per_period / purchase_price
                total_shares += shares_bought
                total_invested += contribution_per_period
                
                investment_log.append({
                    'period': i + 1,
                    'date': prediction.get('date', datetime.now().strftime('%Y-%m-%d')),
                    'contribution': contribution_per_period,
                    'price': purchase_price,
                    'shares_bought': shares_bought,
                    'total_shares': total_shares,
                    'total_invested': total_invested
                })
            
            # Calculate final value using last prediction
            if predictions:
                final_price = predictions[-1].get('predicted_price', predictions[-1].get('current_price', 100))
                final_value = total_shares * final_price
                total_return = ((final_value - total_invested) / total_invested) * 100 if total_invested > 0 else 0
            else:
                final_value = total_invested
                total_return = 0
            
            return {
                'strategy': f'DCA_{frequency}',
                'total_invested': total_invested,
                'final_value': final_value,
                'total_return_pct': total_return,
                'total_shares': total_shares,
                'average_cost_per_share': total_invested / total_shares if total_shares > 0 else 0,
                'investment_log': investment_log
            }
            
        except Exception as e:
            logger.error(f"Error simulating DCA strategy: {str(e)}")
            return {'error': str(e)}
    
    def simulate_lump_sum_strategy(self, predictions: List[Dict], 
                                 annual_amount: float,
                                 timing: str = 'beginning') -> Dict:
        """
        Simulate lump sum investment strategy.
        
        Args:
            predictions: List of prediction results
            annual_amount: Total investment amount
            timing: 'beginning', 'end', 'optimal' (based on predictions)
            
        Returns:
            Dict with simulation results
        """
        try:
            if not predictions:
                return {'error': 'No predictions provided'}
            
            if timing == 'beginning':
                investment_price = predictions[0].get('predicted_price', predictions[0].get('current_price', 100))
                investment_date = predictions[0].get('date', datetime.now().strftime('%Y-%m-%d'))
            elif timing == 'end':
                investment_price = predictions[-1].get('predicted_price', predictions[-1].get('current_price', 100))
                investment_date = predictions[-1].get('date', datetime.now().strftime('%Y-%m-%d'))
            elif timing == 'optimal':
                # Find the lowest predicted price for optimal entry
                min_price_pred = min(predictions, key=lambda x: x.get('predicted_price', x.get('current_price', float('inf'))))
                investment_price = min_price_pred.get('predicted_price', min_price_pred.get('current_price', 100))
                investment_date = min_price_pred.get('date', datetime.now().strftime('%Y-%m-%d'))
            else:
                raise ValueError(f"Unsupported timing: {timing}")
            
            total_shares = annual_amount / investment_price
            final_price = predictions[-1].get('predicted_price', predictions[-1].get('current_price', 100))
            final_value = total_shares * final_price
            total_return = ((final_value - annual_amount) / annual_amount) * 100
            
            return {
                'strategy': f'lump_sum_{timing}',
                'total_invested': annual_amount,
                'final_value': final_value,
                'total_return_pct': total_return,
                'total_shares': total_shares,
                'investment_price': investment_price,
                'investment_date': investment_date,
                'final_price': final_price
            }
            
        except Exception as e:
            logger.error(f"Error simulating lump sum strategy: {str(e)}")
            return {'error': str(e)}
    
    def simulate_tactical_allocation(self, predictions: List[Dict], 
                                   annual_amount: float,
                                   confidence_threshold: float = 0.7) -> Dict:
        """
        Simulate tactical allocation based on prediction confidence.
        Invest more when confidence is high, less when confidence is low.
        
        Args:
            predictions: List of prediction results with confidence scores
            annual_amount: Total annual investment amount
            confidence_threshold: Minimum confidence to invest (0.0 to 1.0)
            
        Returns:
            Dict with simulation results
        """
        try:
            total_invested = 0.0
            total_shares = 0.0
            investment_log = []
            
            # Calculate base contribution per period
            base_contribution = annual_amount / len(predictions) if predictions else 0
            
            for i, prediction in enumerate(predictions):
                confidence = prediction.get('confidence', 0.5)
                predicted_price = prediction.get('predicted_price', prediction.get('current_price', 100))
                
                # Adjust contribution based on confidence
                if confidence >= confidence_threshold:
                    # High confidence: invest more (up to 2x base)
                    confidence_multiplier = 1 + confidence  # 1.0 to 2.0
                    contribution = min(base_contribution * confidence_multiplier, 
                                     annual_amount - total_invested)
                else:
                    # Low confidence: invest less (0.5x to 1x base)
                    confidence_multiplier = 0.5 + (confidence / confidence_threshold) * 0.5
                    contribution = base_contribution * confidence_multiplier
                
                if contribution > 0 and total_invested + contribution <= annual_amount:
                    shares_bought = contribution / predicted_price
                    total_shares += shares_bought
                    total_invested += contribution
                    
                    investment_log.append({
                        'period': i + 1,
                        'date': prediction.get('date', datetime.now().strftime('%Y-%m-%d')),
                        'confidence': confidence,
                        'contribution': contribution,
                        'price': predicted_price,
                        'shares_bought': shares_bought,
                        'total_shares': total_shares,
                        'total_invested': total_invested
                    })
            
            # Calculate final value
            if predictions:
                final_price = predictions[-1].get('predicted_price', predictions[-1].get('current_price', 100))
                final_value = total_shares * final_price
                total_return = ((final_value - total_invested) / total_invested) * 100 if total_invested > 0 else 0
            else:
                final_value = total_invested
                total_return = 0
            
            return {
                'strategy': 'tactical_allocation',
                'total_invested': total_invested,
                'final_value': final_value,
                'total_return_pct': total_return,
                'total_shares': total_shares,
                'average_confidence': np.mean([log['confidence'] for log in investment_log]) if investment_log else 0,
                'investment_log': investment_log
            }
            
        except Exception as e:
            logger.error(f"Error simulating tactical allocation: {str(e)}")
            return {'error': str(e)}
    
    def compare_strategies(self, predictions: List[Dict], 
                         annual_amount: float,
                         account_type: str = 'roth_ira') -> Dict:
        """
        Compare all investment strategies and recommend the best one.
        
        Args:
            predictions: List of prediction results
            annual_amount: Total investment amount (limited by account type)
            account_type: 'roth_ira', '401k', 'taxable'
            
        Returns:
            Dict with comparison results and recommendation
        """
        try:
            # Adjust amount based on account limits
            if account_type == 'roth_ira':
                annual_amount = min(annual_amount, self.ROTH_IRA_LIMIT)
            elif account_type == '401k':
                annual_amount = min(annual_amount, self.CONTRIB_401K_LIMIT)
            
            # Run all simulations
            strategies = {
                'dca_biweekly': self.simulate_dca_strategy(predictions, annual_amount, 'biweekly'),
                'dca_monthly': self.simulate_dca_strategy(predictions, annual_amount, 'monthly'),
                'lump_sum_beginning': self.simulate_lump_sum_strategy(predictions, annual_amount, 'beginning'),
                'lump_sum_optimal': self.simulate_lump_sum_strategy(predictions, annual_amount, 'optimal'),
                'tactical_allocation': self.simulate_tactical_allocation(predictions, annual_amount)
            }
            
            # Filter out strategies with errors
            valid_strategies = {k: v for k, v in strategies.items() if 'error' not in v}
            
            if not valid_strategies:
                return {'error': 'No valid strategies could be simulated'}
            
            # Find best strategy by total return
            best_strategy = max(valid_strategies.items(), 
                              key=lambda x: x[1].get('total_return_pct', 0))
            
            # Calculate strategy rankings
            strategy_rankings = sorted(valid_strategies.items(), 
                                     key=lambda x: x[1].get('total_return_pct', 0), 
                                     reverse=True)
            
            return {
                'account_type': account_type,
                'annual_limit': annual_amount,
                'strategies': valid_strategies,
                'best_strategy': {
                    'name': best_strategy[0],
                    'results': best_strategy[1]
                },
                'rankings': [(name, results['total_return_pct']) for name, results in strategy_rankings],
                'summary': {
                    'best_return': best_strategy[1].get('total_return_pct', 0),
                    'best_final_value': best_strategy[1].get('final_value', 0),
                    'comparison_date': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error comparing strategies: {str(e)}")
            return {'error': str(e)}

def generate_investment_report(comparison_results: Dict, symbol: str) -> str:
    """
    Generate a human-readable investment strategy report.
    
    Args:
        comparison_results: Results from compare_strategies()
        symbol: Stock symbol being analyzed
        
    Returns:
        Formatted string report
    """
    if 'error' in comparison_results:
        return f"Error generating report: {comparison_results['error']}"
    
    best_strategy = comparison_results['best_strategy']
    rankings = comparison_results['rankings']
    
    report = f"""
INVESTMENT STRATEGY ANALYSIS REPORT
Symbol: {symbol}
Account Type: {comparison_results['account_type'].upper()}
Annual Contribution Limit: ${comparison_results['annual_limit']:,.2f}
Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

RECOMMENDED STRATEGY: {best_strategy['name'].replace('_', ' ').title()}
Expected Return: {best_strategy['results']['total_return_pct']:.2f}%
Final Value: ${best_strategy['results']['final_value']:,.2f}
Total Invested: ${best_strategy['results']['total_invested']:,.2f}

STRATEGY RANKINGS:
"""
    
    for i, (strategy_name, return_pct) in enumerate(rankings, 1):
        report += f"{i}. {strategy_name.replace('_', ' ').title()}: {return_pct:.2f}% return\n"
    
    report += f"""
ANALYSIS INSIGHTS:
- Best strategy outperforms worst by {rankings[0][1] - rankings[-1][1]:.2f} percentage points
- Dollar-cost averaging reduces timing risk with consistent returns
- Tactical allocation adapts to AI prediction confidence levels
- Lump sum strategies depend heavily on market timing accuracy

RISK CONSIDERATIONS:
- All projections based on AI predictions (actual results may vary)
- Market volatility can significantly impact returns
- Past performance does not guarantee future results
- Consider diversification across multiple assets and strategies
"""
    
    return report
