import pandas as pd
import numpy as np
from datetime import datetime
import os
import logging
import gc
from typing import Dict, List, Optional, Union, Tuple, Any

# Import memory management utilities
try:
    from app.utils.memory_management import (
        monitor_memory_usage,
        optimize_dataframe_memory,
        chunk_dataframe,
        cleanup_large_objects,
        memory_limit_decorator
    )
    MEMORY_MANAGEMENT_AVAILABLE = True
except ImportError:
    MEMORY_MANAGEMENT_AVAILABLE = False
    logging.warning("Memory management utilities not available. Memory optimization will be limited.")

    # Define dummy decorators if memory management is not available
    def monitor_memory_usage(func):
        return func

    def memory_limit_decorator(max_memory_mb=1000.0):
        def decorator(func):
            return func
        return decorator

    # Define dummy functions if memory management is not available
    def optimize_dataframe_memory(df):
        return df

    def chunk_dataframe(df, chunk_size=1000):
        return [df]

    def cleanup_large_objects(obj_list=None):
        if obj_list:
            for obj in obj_list:
                obj = None
        gc.collect()

@monitor_memory_usage
def load_csv_data(file_path):
    """
    Load stock data from CSV file with memory optimization

    Args:
        file_path (str): Path to the CSV file

    Returns:
        pd.DataFrame: DataFrame containing the stock data
    """
    logger = logging.getLogger(__name__)

    try:
        # Read CSV file with optimized dtypes
        # Use dtype specification to reduce memory usage
        dtype_dict = {
            'Open': 'float32',
            'High': 'float32',
            'Low': 'float32',
            'Close': 'float32',
            'Volume': 'float32'
        }

        # Parse dates directly during loading
        df = pd.read_csv(
            file_path,
            dtype=dtype_dict,
            parse_dates=['Date']
        )

        # Check if required columns exist
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")

        # Sort by date
        df = df.sort_values('Date')

        # Add hour of day and day of week features
        df['Hour'] = df['Date'].dt.hour.astype('uint8')  # Use smaller integer type
        df['DayOfWeek'] = df['Date'].dt.dayofweek.astype('uint8')  # Use smaller integer type

        # Apply memory optimization if available
        if MEMORY_MANAGEMENT_AVAILABLE:
            logger.info(f"Optimizing memory usage for DataFrame with {len(df)} rows")
            df = optimize_dataframe_memory(df)

            # Log memory usage
            mem_usage = df.memory_usage(deep=True).sum() / 1024 / 1024
            logger.info(f"DataFrame memory usage: {mem_usage:.2f} MB")

            # If DataFrame is very large, consider chunking for future operations
            if len(df) > 10000:  # Arbitrary threshold
                logger.info(f"Large DataFrame detected ({len(df)} rows). Consider using chunking for processing.")

        return df

    except Exception as e:
        logger.error(f"Error loading CSV data: {str(e)}")
        raise Exception(f"Error loading CSV data: {str(e)}")

@monitor_memory_usage
@memory_limit_decorator(max_memory_mb=2000.0)  # Set a reasonable memory limit
def preprocess_data(df, sequence_length=60, chunk_size=5000):
    """
    Preprocess data for LSTM model with memory optimization

    Args:
        df (pd.DataFrame): DataFrame containing the stock data
        sequence_length (int): Number of time steps to look back
        chunk_size (int): Size of chunks to process large datasets

    Returns:
        tuple: (X, y, scaler) where X is the input sequences, y is the target values, and scaler is the fitted scaler
              Returns (None, None, None) if there's not enough data
    """
    logger = logging.getLogger(__name__)

    # Check if we have enough data
    if len(df) <= sequence_length:
        logger.error(f"Not enough data for preprocessing. Need more than {sequence_length} rows, but got {len(df)} rows.")
        return None, None, None

    # Select features
    try:
        # Use smaller data types to reduce memory usage
        feature_df = df[['Open', 'High', 'Low', 'Close', 'Volume', 'Hour', 'DayOfWeek']].copy()

        # Convert to smaller data types if not already done
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if feature_df[col].dtype != 'float32':
                feature_df[col] = feature_df[col].astype('float32')

        for col in ['Hour', 'DayOfWeek']:
            if feature_df[col].dtype != 'uint8':
                feature_df[col] = feature_df[col].astype('uint8')

        # Convert to numpy array
        data = feature_df.values

        # Check if data is empty
        if len(data) == 0:
            logger.error("Empty dataset after selecting features.")
            return None, None, None

        # Normalize data
        from sklearn.preprocessing import MinMaxScaler
        scaler = MinMaxScaler(feature_range=(0, 1))

        # Fit scaler on all data
        scaler.fit(data)

        # Process in chunks if the dataset is large
        if len(data) > chunk_size and MEMORY_MANAGEMENT_AVAILABLE:
            logger.info(f"Processing large dataset ({len(data)} rows) in chunks of {chunk_size}")

            # Initialize lists for sequences
            X_list = []
            y_list = []

            # Process data in chunks
            for i in range(0, len(data), chunk_size):
                # Get chunk
                chunk = data[i:i+chunk_size]

                # Transform chunk
                chunk_scaled = scaler.transform(chunk)

                # Create sequences from this chunk
                # Only if we have enough data in this chunk
                if len(chunk_scaled) > sequence_length:
                    for j in range(len(chunk_scaled) - sequence_length):
                        # Only add sequences that don't cross chunk boundaries
                        if i + j + sequence_length < len(data):
                            X_list.append(chunk_scaled[j:j + sequence_length])
                            y_list.append(chunk_scaled[j + sequence_length, 3])  # Close price

                # Clear memory
                del chunk
                del chunk_scaled
                gc.collect()

            # Convert lists to arrays
            X = np.array(X_list, dtype='float32')
            y = np.array(y_list, dtype='float32')

            # Clear lists to free memory
            del X_list
            del y_list
            gc.collect()
        else:
            # For smaller datasets, process all at once
            data_scaled = scaler.transform(data)

            # Create sequences
            X = []
            y = []

            for i in range(len(data_scaled) - sequence_length):
                X.append(data_scaled[i:i + sequence_length])
                y.append(data_scaled[i + sequence_length, 3])  # Close price

            # Convert to numpy arrays
            X = np.array(X, dtype='float32')
            y = np.array(y, dtype='float32')

            # Clean up
            del data_scaled
            gc.collect()

        # Check if we have any sequences
        if len(X) == 0:
            logger.error(f"No sequences created. Not enough data after applying sequence_length={sequence_length}.")
            return None, None, None

        # Log memory usage of the arrays
        x_size = X.nbytes / 1024 / 1024
        y_size = y.nbytes / 1024 / 1024
        logger.info(f"Created sequences: X shape={X.shape}, memory={x_size:.2f} MB; y shape={y.shape}, memory={y_size:.2f} MB")

        return X, y, scaler

    except Exception as e:
        logger.error(f"Error in preprocessing data: {str(e)}")
        # Clean up any large objects
        cleanup_large_objects()
        return None, None, None

@monitor_memory_usage
def save_scaler(scaler, symbol, horizon=None, model_type=None, path='saved_models'):
    """
    Save scaler for later use with multiple naming conventions for compatibility

    Args:
        scaler: The fitted scaler
        symbol (str): Stock symbol
        horizon (int, optional): Prediction horizon. If provided, will be included in the filename.
        model_type (str, optional): Model type. If provided, will be included in the filename.
        path (str): Path to save the scaler
    """
    import joblib
    logger = logging.getLogger(__name__)

    if not os.path.exists(path):
        os.makedirs(path)

    # Save with multiple naming conventions for compatibility
    filenames = []

    # Original naming convention
    if horizon is not None:
        filenames.append(f'{symbol}_{horizon}_scaler.pkl')
    else:
        filenames.append(f'{symbol}_scaler.pkl')

    # Add model-specific naming conventions
    if horizon is not None and model_type is not None:
        # Format used in some parts of the code
        filenames.append(f'{symbol}_{model_type.lower()}_scaler_{horizon}min.joblib')
        filenames.append(f'{symbol}_{model_type.lower()}_scaler{horizon}min.joblib')

    # Save with all naming conventions
    successful_saves = 0
    for filename in filenames:
        try:
            file_path = os.path.join(path, filename)

            # Use compression to reduce file size
            joblib.dump(scaler, file_path, compress=3)

            # Get file size
            file_size = os.path.getsize(file_path) / 1024  # KB
            logger.info(f"Saved scaler to {file_path} ({file_size:.2f} KB)")
            successful_saves += 1
        except Exception as e:
            logger.error(f"Error saving scaler to {filename}: {str(e)}")

    if successful_saves == 0:
        logger.warning(f"Failed to save scaler for {symbol} with any naming convention")

    # Force garbage collection after saving
    gc.collect()

    return filenames

@monitor_memory_usage
def load_scaler(symbol, horizon=None, model_type=None, path='saved_models'):
    """
    Load saved scaler with robust fallback options

    Args:
        symbol (str): Stock symbol
        horizon (int, optional): Prediction horizon. If provided, will be included in the filename.
        model_type (str, optional): Model type. If provided, will be used to try model-specific scaler names.
        path (str): Path where the scaler is saved

    Returns:
        The loaded scaler
    """
    import joblib
    import glob
    logger = logging.getLogger(__name__)

    # Try multiple naming conventions
    potential_paths = []

    # Original naming convention
    if horizon is not None:
        potential_paths.append(os.path.join(path, f'{symbol}_{horizon}_scaler.pkl'))

    # Generic scaler
    potential_paths.append(os.path.join(path, f'{symbol}_scaler.pkl'))

    # Model-specific naming conventions
    if horizon is not None:
        # Try with model type if provided
        if model_type is not None:
            potential_paths.append(os.path.join(path, f'{symbol}_{model_type.lower()}_scaler_{horizon}min.joblib'))
            potential_paths.append(os.path.join(path, f'{symbol}_{model_type.lower()}_scaler{horizon}min.joblib'))

        # Try with common model types as fallback
        for m_type in ['lstm', 'rf', 'gb', 'lr', 'svr', 'prophet', 'ensemble', 'transformer']:
            potential_paths.append(os.path.join(path, f'{symbol}_{m_type}_scaler_{horizon}min.joblib'))
            potential_paths.append(os.path.join(path, f'{symbol}_{m_type}_scaler{horizon}min.joblib'))

    # Try all potential paths
    for scaler_path in potential_paths:
        if os.path.exists(scaler_path):
            try:
                # Get file size
                file_size = os.path.getsize(scaler_path) / 1024  # KB
                logger.info(f"Found scaler at {scaler_path} ({file_size:.2f} KB)")

                # Load the scaler
                scaler = joblib.load(scaler_path)
                return scaler
            except Exception as e:
                logger.warning(f"Error loading scaler from {scaler_path}: {str(e)}")
                # Continue to next path

    # If no specific scaler found, try to find any scaler for this symbol using glob
    logger.warning(f"No exact scaler match found for {symbol} with horizon {horizon}. Trying to find any scaler...")
    scaler_pattern = os.path.join(path, f'{symbol}*scaler*.{{pkl,joblib}}')
    scaler_files = glob.glob(scaler_pattern)

    if scaler_files:
        # Use the first scaler found
        try:
            # Get file size
            file_size = os.path.getsize(scaler_files[0]) / 1024  # KB
            logger.info(f"Using alternative scaler: {scaler_files[0]} ({file_size:.2f} KB)")

            # Load the scaler
            scaler = joblib.load(scaler_files[0])
            return scaler
        except Exception as e:
            logger.warning(f"Error loading alternative scaler: {str(e)}")

    # If still no scaler found, create a new one as last resort and fit it with sample data
    logger.warning(f"No scaler found for {symbol}. Creating a new MinMaxScaler as fallback.")
    from sklearn.preprocessing import MinMaxScaler

    # Create a new scaler
    scaler = MinMaxScaler(feature_range=(0, 1))

    # Try to get some data to fit the scaler
    try:
        # Try to get original data for this symbol
        df = get_original_data(symbol, path=os.path.join(os.path.dirname(path), 'data'))

        if df is not None and len(df) > 0:
            # Select features for fitting with memory optimization
            feature_df = df[['Open', 'High', 'Low', 'Close', 'Volume', 'Hour', 'DayOfWeek']].copy()

            # Convert to smaller data types
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                if feature_df[col].dtype != 'float32':
                    feature_df[col] = feature_df[col].astype('float32')

            for col in ['Hour', 'DayOfWeek']:
                if feature_df[col].dtype != 'uint8':
                    feature_df[col] = feature_df[col].astype('uint8')

            # Convert to numpy array
            features = feature_df.values

            # Fit the scaler with the data
            scaler.fit(features)
            logger.info(f"Fitted new scaler with {len(features)} samples from {symbol} data")

            # Save the scaler for future use
            save_scaler(scaler, symbol, horizon=horizon, model_type=model_type, path=path)

            # Clean up
            del feature_df
            del features
            gc.collect()

            return scaler
    except Exception as e:
        logger.error(f"Error fitting new scaler: {str(e)}")

    # If we couldn't fit with real data, fit with dummy data
    try:
        # Create dummy data that covers a reasonable range for stock prices
        dummy_data = np.array([
            [10.0, 11.0, 9.0, 10.5, 1000000, 9, 1],  # Low values
            [100.0, 110.0, 90.0, 105.0, 10000000, 12, 3],  # Medium values
            [1000.0, 1100.0, 900.0, 1050.0, 100000000, 15, 5]  # High values
        ], dtype='float32')  # Use float32 to reduce memory usage

        # Fit the scaler with dummy data
        scaler.fit(dummy_data)
        logger.info("Fitted new scaler with dummy data")

        # Save the scaler for future use
        save_scaler(scaler, symbol, horizon=horizon, model_type=model_type, path=path)

        # Clean up
        del dummy_data
        gc.collect()
    except Exception as e:
        logger.error(f"Error fitting scaler with dummy data: {str(e)}")

    return scaler

@monitor_memory_usage
def get_original_data(symbol, path='data'):
    """
    Get the original data for a stock symbol with memory optimization

    Args:
        symbol (str): Stock symbol
        path (str): Path where data is stored

    Returns:
        pd.DataFrame: Original data or None if not found
    """
    logger = logging.getLogger(__name__)

    # Try to find the CSV file for the symbol
    csv_path = os.path.join(path, f'{symbol}.csv')

    if os.path.exists(csv_path):
        logger.info(f"Found data file at {csv_path}")
        return load_csv_data(csv_path)

    # If not found in the main directory, try looking in subdirectories
    for root, dirs, files in os.walk(path):
        for file in files:
            if file.lower() == f'{symbol.lower()}.csv':
                csv_path = os.path.join(root, file)
                logger.info(f"Found data file in subdirectory: {csv_path}")
                return load_csv_data(csv_path)

    # If still not found, try looking in the current directory
    current_dir = os.getcwd()
    csv_path = os.path.join(current_dir, 'data', f'{symbol}.csv')
    if os.path.exists(csv_path):
        logger.info(f"Found data file in current directory: {csv_path}")
        return load_csv_data(csv_path)

    # Try one more time with a direct path
    csv_path = f'data/{symbol}.csv'
    if os.path.exists(csv_path):
        logger.info(f"Found data file with direct path: {csv_path}")
        return load_csv_data(csv_path)

    logger.warning(f"No data file found for symbol: {symbol}")
    return None

def is_model_trained(symbol, horizon, model_type='rf', path='saved_models', horizon_unit='minutes'):
    """
    Check if a model has been trained for a specific stock and horizon

    Args:
        symbol (str): Stock symbol
        horizon (int): Prediction horizon
        model_type (str): Type of model ('rf', 'gb', 'lr', 'svr', 'lstm', 'bilstm', 'prophet', 'transformer', 'ensemble')
        path (str): Path where models are saved
        horizon_unit (str): Unit of time for horizons (minutes, days, weeks)

    Returns:
        bool: True if model exists, False otherwise
    """
    import glob
    logger = logging.getLogger(__name__)

    # Convert horizon to minutes if needed
    model_horizon = horizon
    if horizon_unit == 'days':
        model_horizon = horizon * 24 * 60  # days to minutes
    elif horizon_unit == 'weeks':
        model_horizon = horizon * 7 * 24 * 60  # weeks to minutes

    # For ensemble models, check if any base models exist
    if model_type.lower() == 'ensemble':
        # Check for any model with this horizon
        base_models = ['rf', 'gb', 'lr', 'svr', 'lstm', 'bilstm', 'prophet', 'transformer']
        for base_model in base_models:
            if is_model_trained(symbol, horizon, base_model, path, horizon_unit):
                return True
        return False

    # Use a more efficient approach to check for model files
    # First, check specific model paths based on model type
    if model_type.lower() in ['lstm', 'bilstm']:
        # TensorFlow model - check multiple possible paths
        model_paths = [
            os.path.join(path, f'{symbol}_{model_type.lower()}_{model_horizon}min.h5'),
            os.path.join(path, f'{symbol}_{model_type.lower()}{model_horizon}min.h5')
        ]
        if any(os.path.exists(p) for p in model_paths):
            logger.info(f"Found {model_type} model for {symbol} with {horizon} {horizon_unit} horizon")
            return True
    elif model_type.lower() == 'transformer':
        # Transformer model
        model_paths = [
            os.path.join(path, f'{symbol}_transformer_{model_horizon}min.h5'),
            os.path.join(path, f'{symbol}_transformer{model_horizon}min.h5')
        ]
        if any(os.path.exists(p) for p in model_paths):
            logger.info(f"Found transformer model for {symbol} with {horizon} {horizon_unit} horizon")
            return True
    elif model_type.lower() == 'prophet':
        # Prophet model
        model_paths = [
            os.path.join(path, f'{symbol}_prophet_{model_horizon}min.joblib'),
            os.path.join(path, f'{symbol}_prophet{model_horizon}min.joblib')
        ]
        if any(os.path.exists(p) for p in model_paths):
            logger.info(f"Found prophet model for {symbol} with {horizon} {horizon_unit} horizon")
            return True
    else:
        # Scikit-learn model
        model_paths = [
            os.path.join(path, f'{symbol}_{model_type.lower()}_{model_horizon}min.joblib'),
            os.path.join(path, f'{symbol}_{model_type.lower()}{model_horizon}min.joblib')
        ]
        if any(os.path.exists(p) for p in model_paths):
            logger.info(f"Found {model_type} model for {symbol} with {horizon} {horizon_unit} horizon")
            return True

    # If specific paths don't exist, try the glob approach
    # This is more expensive, so we do it only if the specific paths don't exist
    all_model_files = glob.glob(os.path.join(path, f'{symbol}_*_{model_horizon}min.*'))
    all_model_files += glob.glob(os.path.join(path, f'{symbol}_*{model_horizon}min.*'))

    # If we found any files, check if they match the model type
    if all_model_files:
        model_type_files = [f for f in all_model_files if model_type.lower() in f.lower()]
        if model_type_files:
            logger.info(f"Found {model_type} model for {symbol} with {horizon} {horizon_unit} horizon using glob")
            return True

        # For short horizons (4, 15, 30 minutes), we might have models trained with slightly different horizons
        # Check for models with similar horizons
        if model_horizon <= 60:  # Only for short horizons
            similar_horizons = [4, 15, 30, 60]
            for similar_horizon in similar_horizons:
                if similar_horizon != model_horizon:
                    similar_files = glob.glob(os.path.join(path, f'{symbol}_*_{similar_horizon}min.*'))
                    similar_files += glob.glob(os.path.join(path, f'{symbol}_*{similar_horizon}min.*'))
                    model_type_similar_files = [f for f in similar_files if model_type.lower() in f.lower()]
                    if model_type_similar_files:
                        logger.info(f"Using model trained for {similar_horizon} minutes instead of {model_horizon} minutes")
                        return True

    # Check for scaler as a last resort
    scaler_paths = [
        os.path.join(path, f'{symbol}_{model_horizon}_scaler.pkl'),
        os.path.join(path, f'{symbol}_{model_type.lower()}_scaler_{model_horizon}min.joblib'),
        os.path.join(path, f'{symbol}_{model_type.lower()}_scaler{model_horizon}min.joblib'),
        os.path.join(path, f'{symbol}_transformer_scaler_{model_horizon}min.joblib'),
        os.path.join(path, f'{symbol}_transformer_scaler{model_horizon}min.joblib')
    ]

    if any(os.path.exists(p) for p in scaler_paths):
        logger.info(f"Found scaler for {symbol} with {horizon} {horizon_unit} horizon, but no model file")
        # Having only a scaler doesn't mean the model is trained
        return False

    logger.info(f"No model found for {symbol} with {horizon} {horizon_unit} horizon using {model_type}")
    return False

@monitor_memory_usage
def cleanup_memory(objects_to_clean=None):
    """
    Clean up memory by deleting objects and running garbage collection

    Args:
        objects_to_clean (list, optional): List of objects to delete
    """
    logger = logging.getLogger(__name__)

    # Use the memory management utility if available
    if MEMORY_MANAGEMENT_AVAILABLE:
        cleanup_large_objects(objects_to_clean)
        return

    # Fallback implementation if memory management is not available
    if objects_to_clean:
        for obj in objects_to_clean:
            try:
                if hasattr(obj, 'close'):
                    obj.close()
            except Exception as e:
                logger.warning(f"Error closing object {type(obj)}: {str(e)}")

            # Set to None to help garbage collection
            obj = None

    # Run garbage collection
    collected = gc.collect()
    logger.info(f"Garbage collection: collected {collected} objects")

    # Log memory usage if psutil is available
    try:
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        rss_mb = memory_info.rss / 1024 / 1024
        vms_mb = memory_info.vms / 1024 / 1024
        logger.info(f"Current memory usage: RSS={rss_mb:.2f} MB, VMS={vms_mb:.2f} MB")
    except ImportError:
        logger.info("psutil not available, cannot report memory usage")

    # Additional memory optimization for pandas
    try:
        import pandas as pd
        # Clear pandas cache
        pd.DataFrame._metadata.clear()
        logger.debug("Cleared pandas metadata cache")
    except Exception as e:
        logger.debug(f"Error clearing pandas cache: {str(e)}")

    # Additional memory optimization for numpy
    try:
        import numpy as np
        # Clear numpy cache
        np.set_printoptions(threshold=np.inf)
        np.set_printoptions(threshold=1000)  # Reset to default
        logger.debug("Reset numpy print options")
    except Exception as e:
        logger.debug(f"Error resetting numpy options: {str(e)}")

    # Try to reduce memory usage further if we're still high
    try:
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        rss_mb = memory_info.rss / 1024 / 1024

        # If memory usage is still high, take more aggressive measures
        if rss_mb > 1000:  # More than 1GB
            logger.warning(f"Memory usage still high ({rss_mb:.2f} MB), taking aggressive measures")

            # Clear all module caches that we know about
            import sys
            for module_name in list(sys.modules.keys()):
                if module_name.startswith('pandas.') or module_name.startswith('numpy.'):
                    if hasattr(sys.modules[module_name], 'clear_cache'):
                        try:
                            sys.modules[module_name].clear_cache()
                        except:
                            pass

            # Run garbage collection again with more aggressive settings
            import gc
            gc.collect(2)  # Full collection

            # Log final memory usage
            memory_info = process.memory_info()
            rss_mb = memory_info.rss / 1024 / 1024
            logger.info(f"Memory usage after aggressive cleanup: RSS={rss_mb:.2f} MB")
    except ImportError:
        pass
