"""
Memory management utilities for monitoring and optimizing memory usage.

This module provides decorators and utilities for monitoring memory usage,
implementing data chunking, and cleaning up large objects to prevent memory leaks.
"""

import os
import gc
import logging
import functools
import time
from typing import Callable, Any, Optional, List, Dict, Union
import pandas as pd
import numpy as np

# Configure logging
logger = logging.getLogger(__name__)

# Try to import psutil for memory monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil not available. Memory monitoring will be limited.")

def get_process_memory_usage() -> Dict[str, float]:
    """
    Get current memory usage of the process.

    Returns:
        Dict[str, float]: Dictionary with memory usage in MB
            - rss: Resident Set Size (actual memory used)
            - vms: Virtual Memory Size
            - percent: Percentage of system memory used
    """
    if not PSUTIL_AVAILABLE:
        logger.warning("psutil not available. Cannot get detailed memory usage.")
        return {"rss": 0, "vms": 0, "percent": 0}

    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()

        # Convert to MB
        rss_mb = memory_info.rss / 1024 / 1024
        vms_mb = memory_info.vms / 1024 / 1024

        # Get percentage of system memory
        percent = process.memory_percent()

        return {
            "rss": rss_mb,
            "vms": vms_mb,
            "percent": percent
        }
    except Exception as e:
        logger.error(f"Error getting memory usage: {str(e)}")
        return {"rss": 0, "vms": 0, "percent": 0}

def monitor_memory_usage(func: Callable) -> Callable:
    """
    Decorator to monitor memory usage before and after a function call.

    Args:
        func (Callable): Function to monitor

    Returns:
        Callable: Decorated function
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        # Get memory usage before
        gc.collect()  # Force garbage collection
        mem_before = get_process_memory_usage()

        # Call the function
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        # Get memory usage after
        gc.collect()  # Force garbage collection
        mem_after = get_process_memory_usage()

        # Calculate difference
        mem_diff = {
            "rss": mem_after["rss"] - mem_before["rss"],
            "vms": mem_after["vms"] - mem_before["vms"],
            "percent": mem_after["percent"] - mem_before["percent"]
        }

        # Log memory usage
        logger.info(
            f"Memory usage for {func.__name__}: "
            f"RSS: {mem_diff['rss']:.2f} MB, "
            f"VMS: {mem_diff['vms']:.2f} MB, "
            f"Percent: {mem_diff['percent']:.2f}%, "
            f"Execution time: {end_time - start_time:.2f}s"
        )

        return result

    return wrapper

def chunk_dataframe(df: pd.DataFrame, chunk_size: int = 1000) -> List[pd.DataFrame]:
    """
    Split a DataFrame into chunks to reduce memory usage.

    Args:
        df (pd.DataFrame): DataFrame to split
        chunk_size (int): Number of rows per chunk

    Returns:
        List[pd.DataFrame]: List of DataFrame chunks
    """
    if len(df) <= chunk_size:
        return [df]

    chunks = []
    for i in range(0, len(df), chunk_size):
        chunks.append(df.iloc[i:i+chunk_size].copy())

    logger.info(f"Split DataFrame with {len(df)} rows into {len(chunks)} chunks of {chunk_size} rows")
    return chunks

def optimize_dataframe_memory(df: pd.DataFrame, aggressive: bool = False) -> pd.DataFrame:
    """
    Optimize memory usage of a DataFrame by downcasting numeric types.

    Args:
        df (pd.DataFrame): DataFrame to optimize
        aggressive (bool): Whether to use more aggressive optimization techniques

    Returns:
        pd.DataFrame: Optimized DataFrame
    """
    start_mem = df.memory_usage(deep=True).sum() / 1024 / 1024
    logger.info(f"DataFrame memory usage before optimization: {start_mem:.2f} MB")

    # Make a copy to avoid modifying the original
    result = df.copy()

    # Optimize numeric columns
    for col in result.select_dtypes(include=['int']).columns:
        result[col] = pd.to_numeric(result[col], downcast='integer')

    for col in result.select_dtypes(include=['float']).columns:
        result[col] = pd.to_numeric(result[col], downcast='float')

    # Convert object columns to categories if they have few unique values
    for col in result.select_dtypes(include=['object']).columns:
        unique_pct = result[col].nunique() / len(result)
        if unique_pct < 0.5:  # If less than 50% unique values
            result[col] = result[col].astype('category')
        elif aggressive and unique_pct < 0.7:  # If less than 70% unique values and aggressive mode
            result[col] = result[col].astype('category')

    # Convert datetime columns to more efficient representations
    for col in result.select_dtypes(include=['datetime']).columns:
        # Check if all dates are within a specific range that can be represented by smaller types
        min_date = result[col].min()
        max_date = result[col].max()

        # If all dates are after 1970-01-01, we can use int64 timestamp
        if min_date > pd.Timestamp('1970-01-01'):
            if aggressive:
                # Convert to int64 timestamp (seconds since epoch)
                result[col] = result[col].astype(np.int64) // 10**9
            else:
                # Keep as datetime but ensure it's the most efficient representation
                result[col] = pd.to_datetime(result[col])

    # Additional aggressive optimizations
    if aggressive:
        # For very large DataFrames, consider sampling for operations that don't need all data
        if len(result) > 100000:
            logger.info(f"Large DataFrame detected ({len(result)} rows). Consider using sampling for analysis.")

        # Force garbage collection to free memory
        import gc
        gc.collect()

    end_mem = result.memory_usage(deep=True).sum() / 1024 / 1024
    logger.info(f"DataFrame memory usage after optimization: {end_mem:.2f} MB")
    logger.info(f"Memory usage reduced by {100 * (start_mem - end_mem) / start_mem:.2f}%")

    return result

def cleanup_large_objects(obj_list: Optional[List[Any]] = None) -> None:
    """
    Force cleanup of large objects to free memory.

    Args:
        obj_list (Optional[List[Any]]): List of objects to clean up. If None, only runs gc.collect()
    """
    if obj_list:
        for obj in obj_list:
            if hasattr(obj, 'close'):
                try:
                    obj.close()
                except Exception as e:
                    logger.warning(f"Error closing object {type(obj)}: {str(e)}")

            # Set to None to help garbage collection
            obj = None

    # Run garbage collection
    collected = gc.collect()
    logger.info(f"Garbage collection: collected {collected} objects")

def cleanup_memory(objects_to_delete: Optional[List[Any]] = None) -> float:
    """
    Clean up memory by deleting objects and running garbage collection.

    Args:
        objects_to_delete: List of objects to delete

    Returns:
        Memory freed in MB
    """
    # Log initial memory usage
    mem_before = get_process_memory_usage()
    initial_memory = mem_before["rss"]
    logger.info(f"Memory before cleanup: {initial_memory:.2f} MB")

    # Delete specific objects if provided
    if objects_to_delete is not None:
        for obj in objects_to_delete:
            try:
                del obj
            except:
                pass

    # Run garbage collection
    collected = gc.collect()
    logger.info(f"Garbage collection: collected {collected} objects")

    # Log memory usage after cleanup
    mem_after = get_process_memory_usage()
    final_memory = mem_after["rss"]
    memory_freed = initial_memory - final_memory
    logger.info(f"Memory after cleanup: {final_memory:.2f} MB (freed {memory_freed:.2f} MB)")

    return memory_freed

def process_large_dataframe(df: pd.DataFrame,
                           process_func: Callable[[pd.DataFrame], Any],
                           chunk_size: int = 10000,
                           combine_func: Optional[Callable[[List[Any]], Any]] = None) -> Any:
    """
    Process a large DataFrame in chunks to reduce memory usage.

    Args:
        df: DataFrame to process
        process_func: Function to apply to each chunk
        chunk_size: Number of rows per chunk
        combine_func: Function to combine results from each chunk

    Returns:
        Result of processing
    """
    if len(df) <= chunk_size:
        return process_func(df)

    # Split DataFrame into chunks
    chunks = chunk_dataframe(df, chunk_size)
    logger.info(f"Processing large DataFrame with {len(df)} rows in {len(chunks)} chunks")

    # Process each chunk
    results = []
    for i, chunk in enumerate(chunks):
        logger.info(f"Processing chunk {i+1}/{len(chunks)} with {len(chunk)} rows")

        # Process the chunk
        result = process_func(chunk)
        results.append(result)

        # Clean up memory after each chunk
        if i < len(chunks) - 1:  # Don't clean up after the last chunk
            cleanup_memory([chunk])

    # Combine results if a combine function is provided
    if combine_func is not None:
        return combine_func(results)

    return results

def memory_limit_decorator(max_memory_mb: float = 1000.0) -> Callable:
    """
    Decorator to limit memory usage of a function.

    Args:
        max_memory_mb (float): Maximum memory usage in MB

    Returns:
        Callable: Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            if not PSUTIL_AVAILABLE:
                logger.warning("psutil not available. Cannot enforce memory limit.")
                return func(*args, **kwargs)

            # Get current memory usage
            mem_before = get_process_memory_usage()

            # Check if we're already over the limit
            if mem_before["rss"] > max_memory_mb:
                logger.warning(
                    f"Memory usage already over limit before calling {func.__name__}: "
                    f"{mem_before['rss']:.2f} MB > {max_memory_mb:.2f} MB"
                )

                # Try to free memory
                cleanup_large_objects()

                # Check again
                mem_after_gc = get_process_memory_usage()
                if mem_after_gc["rss"] > max_memory_mb:
                    logger.error(
                        f"Memory usage still over limit after garbage collection: "
                        f"{mem_after_gc['rss']:.2f} MB > {max_memory_mb:.2f} MB"
                    )

            # Call the function
            result = func(*args, **kwargs)

            # Check memory usage after
            mem_after = get_process_memory_usage()
            if mem_after["rss"] > max_memory_mb:
                logger.warning(
                    f"Memory usage exceeded limit after calling {func.__name__}: "
                    f"{mem_after['rss']:.2f} MB > {max_memory_mb:.2f} MB"
                )

                # Try to free memory
                cleanup_large_objects()

            return result

        return wrapper

    return decorator
