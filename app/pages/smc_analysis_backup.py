import os
import sys
import logging
import numpy as np
import pandas as pd
import streamlit as st
import requests
import plotly.graph_objects as go
import pytz
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

# Add the d:\AI Stocks Bot directory to the Python module search path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# Import SMC components
from app.components.smc_indicators import (
    detect_order_blocks, 
    detect_fvg, 
    detect_liquidity_zones,
    detect_market_structure,
    FairValueGap,
    OrderBlock,
    LiquidityZone
)
from app.components.smc_analysis_engine import SMCAnalysisEngine, SMCSignal

# Configure logging
logger = logging.getLogger(__name__)

# Define EGX_STOCKS mapping (same as advanced_technical_analysis.py)
EGX_STOCKS = {
    "COMI": "Commercial International Bank",
    "FWRY": "Fawry Banking Technology",
    "PHDC": "Palm Hills Development",
    "EFID": "Edita Food Industries",
    "UBEE": "United Bank Egypt",
    "GGRN": "GoGreen Agricultural",
    "OBRI": "Orascom Business Intelligence",
    "UTOP": "United Top"
}

# Define SMC_AVAILABLE flag
SMC_AVAILABLE = True

# TradingView API Configuration (same as Advanced Technical Analysis)
TRADINGVIEW_API_URL = "http://127.0.0.1:8000/api/scrape_pairs"

def get_dynamic_colors(structure, age_factor):
    """
    Generate dynamic colors for chart structures based on age and type
    
    Args:
        structure: The SMC structure (order block, FVG, etc.)
        age_factor: Age factor for color fading
        
    Returns:
        dict: Dictionary with color properties
    """
    base_opacity = max(0.2, min(0.9, 1.0 - age_factor))
    
    if hasattr(structure, 'ob_type'):
        # Order block
        if structure.ob_type == 'bullish':
            return {
                'fill': f'rgba(0, 128, 0, {base_opacity})',  # Green with opacity
                'line': f'rgba(0, 100, 0, {base_opacity + 0.1})'  # Darker green
            }
        else:
            return {
                'fill': f'rgba(178, 34, 34, {base_opacity})',  # Red with opacity
                'line': f'rgba(139, 0, 0, {base_opacity + 0.1})'  # Darker red
            }
    elif hasattr(structure, 'gap_type'):
        # Fair Value Gap
        if structure.gap_type == 'bullish':
            return {
                'fill': f'rgba(65, 105, 225, {base_opacity})',  # Royal blue with opacity
                'line': f'rgba(0, 0, 139, {base_opacity + 0.1})'  # Dark blue
            }
        else:
            return {
                'fill': f'rgba(255, 140, 0, {base_opacity})',  # Dark orange with opacity
                'line': f'rgba(255, 69, 0, {base_opacity + 0.1})'  # Red-orange
            }
    else:
        # Default colors
        return {
            'fill': f'rgba(128, 128, 128, {base_opacity})',  # Gray with opacity
            'line': f'rgba(64, 64, 64, {base_opacity + 0.1})'  # Darker gray
        }

def calculate_confluence(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params):
    """
    Calculate confluence score based on SMC structures
    
    Args:
        order_blocks: List of order blocks
        fvgs: List of fair value gaps
        liquidity_zones: List of liquidity zones
        market_structure: Market structure analysis results
        confluence_params: Parameters for confluence calculation
        
    Returns:
        dict: Confluence calculation results
    """
    # Simple implementation
    active_factors = 0
    total_score = 0.5  # Default moderate confidence
    
    # Count active structures
    if order_blocks:
        active_factors += 1
        total_score += 0.1
    
    if fvgs:
        active_factors += 1
        total_score += 0.1
        
    if liquidity_zones:
        active_factors += 1
        total_score += 0.1
        
    if market_structure and market_structure.get('trend') != 'sideways':
        active_factors += 1
        total_score += 0.1
    
    # Cap the score
    total_score = min(total_score, 1.0)
    
    return {
        'total_score': total_score,
        'active_factors': active_factors,
        'factors': {},
        'breakdown': {
            'order_blocks': len(order_blocks) > 0,
            'fvgs': len(fvgs) > 0,
            'liquidity_zones': len(liquidity_zones) > 0,
            'market_structure': market_structure.get('trend', 'sideways') != 'sideways' if market_structure else False
        }
    }

def calculate_confluence_enhanced(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params, data_quality, live_data=None):
    """
    Enhanced version of confluence calculation with data quality adjustments
    
    Args:
        order_blocks: List of order blocks
        fvgs: List of fair value gaps
        liquidity_zones: List of liquidity zones
        market_structure: Market structure analysis results
        confluence_params: Parameters for confluence calculation
        data_quality: Data quality metrics
        live_data: Live market data (optional)
        
    Returns:
        dict: Enhanced confluence calculation results
    """
    try:
        # Base confluence calculation
        base_confluence = calculate_confluence(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params)
        
        # Quality multiplier based on data_quality
        quality_multiplier = 1.0
        min_confidence = 0.25  # Minimum confidence threshold
        
        # Adjust based on data quality
        if data_quality['bars'] >= 180:  # 6+ months of data
            quality_multiplier = 1.05  # Slight boost
        elif data_quality['bars'] < 50:  # Less than 2 months
            quality_multiplier = 0.9  # Reduce confidence
            
        # Apply quality multiplier (capped at 1.0 maximum)
        enhanced_score = min(base_confluence['total_score'] * quality_multiplier, 1.0)
        
        return {
            'total_score': enhanced_score,
            'active_factors': base_confluence['active_factors'],
            'factors': base_confluence.get('factors', {}),
            'data_quality_score': data_quality.get('score', 0.5),
            'quality_multiplier': quality_multiplier,
            'min_confidence': min_confidence,
            'breakdown': base_confluence['breakdown']
        }
        
    except Exception as e:
        logger.error(f"Error in enhanced confluence calculation: {str(e)}")
        # Fallback to standard confluence calculation
        return calculate_confluence(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params)

def get_dynamic_colors(structure, age_factor):
    """
    Generate dynamic colors for chart structures based on age and type
    
    Args:
        structure: The SMC structure (order block, FVG, etc.)
        age_factor: Age factor for color fading
        
    Returns:
        dict: Dictionary with color properties
    """
    base_opacity = max(0.2, min(0.9, 1.0 - age_factor))
    
    if hasattr(structure, 'ob_type'):
        # Order block
        if structure.ob_type == 'bullish':
            return {
                'fill': f'rgba(0, 128, 0, {base_opacity})',  # Green with opacity
                'line': f'rgba(0, 100, 0, {base_opacity + 0.1})'  # Darker green
            }
        else:
            return {
                'fill': f'rgba(178, 34, 34, {base_opacity})',  # Red with opacity
                'line': f'rgba(139, 0, 0, {base_opacity + 0.1})'  # Darker red
            }
    elif hasattr(structure, 'gap_type'):
        # Fair Value Gap
        if structure.gap_type == 'bullish':
            return {
                'fill': f'rgba(65, 105, 225, {base_opacity})',  # Royal blue with opacity
                'line': f'rgba(0, 0, 139, {base_opacity + 0.1})'  # Dark blue
            }
        else:
            return {
                'fill': f'rgba(255, 140, 0, {base_opacity})',  # Dark orange with opacity
                'line': f'rgba(255, 69, 0, {base_opacity + 0.1})'  # Red-orange
            }
    else:
        # Default colors
        return {
            'fill': f'rgba(128, 128, 128, {base_opacity})',  # Gray with opacity
            'line': f'rgba(64, 64, 64, {base_opacity + 0.1})'  # Darker gray
        }

def get_available_stock_symbols():
    """Return a list of stock symbols based on CSV files in the data/stocks directory."""
    stocks_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/stocks'))
    symbols = []
    if os.path.exists(stocks_dir):
        for fname in os.listdir(stocks_dir):
            if fname.endswith('.csv'):
                symbols.append(os.path.splitext(fname)[0])
    return sorted(symbols)

def generate_smc_predictions(current_price, market_structure, confluence, order_blocks, fvgs, premium_discount):
    """Generate predictions based on SMC analysis."""
    predictions = []

    # Get market trend and strength
    trend = market_structure.get('trend', 'neutral')
    strength = market_structure.get('strength', 0.5)
    confluence_score = confluence.get('total_score', 0.5)

    # Calculate base prediction confidence from confluence
    base_confidence = min(confluence_score * 0.8 + 0.2, 0.95)  # 20-95% range

    # Generate predictions for different horizons
    horizons = [
        ('1D', 1, 0.02),    # 1 day, max 2% move
        ('3D', 3, 0.04),    # 3 days, max 4% move
        ('1W', 7, 0.06),    # 1 week, max 6% move
        ('2W', 14, 0.08)    # 2 weeks, max 8% move
    ]

    for horizon_name, days, max_move in horizons:
        # Calculate prediction based on trend and confluence
        if trend == 'bullish':
            # Bullish prediction
            move_factor = strength * max_move * (confluence_score + 0.3)
            predicted_price = current_price * (1 + move_factor)
            probability_up = min(0.5 + (strength * 0.4), 0.9)

        elif trend == 'bearish':
            # Bearish prediction
            move_factor = strength * max_move * (confluence_score + 0.3)
            predicted_price = current_price * (1 - move_factor)
            probability_up = max(0.5 - (strength * 0.4), 0.1)

        else:
            # Neutral/sideways prediction
            move_factor = 0.01 * days  # Small random walk
            predicted_price = current_price * (1 + (move_factor * (0.5 - confluence_score)))
            probability_up = 0.5

        # Adjust confidence based on horizon (shorter = more confident)
        horizon_confidence = base_confidence * (1 - (days / 30) * 0.2)

        # Add structure-based adjustments
        if order_blocks:
            # Nearby order blocks increase confidence
            nearest_ob = min(order_blocks, key=lambda ob: abs((ob.high + ob.low) / 2 - current_price))
            ob_distance = abs((nearest_ob.high + nearest_ob.low) / 2 - current_price) / current_price
            if ob_distance < 0.05:  # Within 5%
                horizon_confidence *= 1.1

        if premium_discount:
            # Premium/discount zone affects predictions
            if premium_discount.current_zone == 'premium' and trend == 'bearish':
                horizon_confidence *= 1.15  # More confident in bearish from premium
            elif premium_discount.current_zone == 'discount' and trend == 'bullish':
                horizon_confidence *= 1.15  # More confident in bullish from discount

        # Cap confidence
        horizon_confidence = min(horizon_confidence, 0.95)

        predictions.append({
            'time_horizon': horizon_name,
            'predicted_price': predicted_price,
            'confidence': horizon_confidence,
            'probability_up': probability_up,
            'model_type': "SMC"
        })

    # Debugging inputs and outputs
    logger.debug(f"Current Price: {current_price}")
    logger.debug(f"Market Structure: {market_structure}")
    logger.debug(f"Confluence: {confluence}")
    logger.debug(f"Order Blocks: {order_blocks}")
    logger.debug(f"Premium Discount Zone: {premium_discount}")
    logger.debug(f"Generated Predictions: {predictions}")

    logger.info(f"Generated {len(predictions)} SMC-based predictions")
    return predictions

def show_smc_analysis():
    """Enhanced SMC Analysis page with professional styling and comprehensive features"""

    # Page header with enhanced styling
    st.markdown("""
    <div style='background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                padding: 20px; border-radius: 15px; margin-bottom: 20px;
                border: 2px solid #4CAF50;'>
        <h1 style='color: white; text-align: center; margin: 0;'>
            🧠 Smart Money Concepts (SMC) Analysis
        </h1>
        <p style='color: #E3F2FD; text-align: center; margin: 10px 0 0 0; font-size: 18px;'>
            Professional SMC Analysis with Advanced Market Structure Detection
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Check if SMC components are available
    if not SMC_AVAILABLE:
        st.error("❌ SMC components are not available. Please check the installation.")
        st.info("💡 Make sure all required SMC modules are properly installed.")
        return

    # Main analysis interface
    st.markdown("### 📊 Analysis Configuration")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("🎯 Stock Selection")

        # Stock selection with enhanced options
        available_symbols = get_available_stock_symbols()
        stock_options = {k: EGX_STOCKS[k] if k in EGX_STOCKS else k for k in available_symbols}
        if not stock_options:
            st.error("No valid stock data found in data/stocks directory.")
            return
        selected_stock = st.selectbox(
            "Select Stock:",
            options=list(stock_options.keys()),
            format_func=lambda x: f"{x} - {EGX_STOCKS[x]}" if x in EGX_STOCKS else x,
            index=0,
            help="Choose a stock for SMC analysis"
        )

    with col2:
        st.subheader("⏰ Analysis Settings")

        # Time interval selection
        selected_interval = st.selectbox(
            "Timeframe:",
            options=["1D", "1W"],
            index=0,
            help="Select timeframe for SMC analysis"
        )

        # Number of bars for analysis
        bars_count = st.slider(
            "Analysis Period (bars):",
            min_value=100,
            max_value=500,
            value=200,
            step=50,
            help="Number of bars to analyze"
        )

    # Analysis button
    if st.button("🧠 Run SMC Analysis", type="primary", use_container_width=True):
        with st.spinner("🔄 Fetching data and running SMC analysis..."):

            # Fetch TradingView data
            egx_symbol = f"EGX-{selected_stock}"
            price_data = fetch_price_data_for_smc(egx_symbol, selected_interval)

            if price_data is not None:
                # Run SMC analysis
                smc_results = run_smc_analysis(price_data, selected_stock, selected_interval)

                if smc_results:
                    display_smc_results(smc_results, selected_stock, selected_interval, price_data)
                else:
                    st.error("❌ Failed to run SMC analysis")
            else:
                st.error("❌ Failed to fetch price data")

def check_api_status():
    """Check if TradingView API server is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_live_smc_data(symbol):
    """Fetch live data specifically for SMC analysis"""
    try:
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            TRADINGVIEW_API_URL,
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                api_data = stock_data[0]

                # Extract price from the API response
                raw_price = api_data.get('price', 0)

                # Convert from piasters to EGP (divide by 1000)
                current_price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                # Extract additional SMC-relevant data
                pivots = api_data.get('pivots', {})
                oscillators = api_data.get('oscillators', {})
                moving_averages = api_data.get('moving_averages', {})

                return {
                    'symbol': symbol,
                    'current_price': current_price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'pivots': pivots,
                    'oscillators': oscillators,
                    'moving_averages': moving_averages,
                    'market_status': determine_market_status(),
                    'full_data': api_data
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching live SMC data: {str(e)}")
        return None

def determine_market_status():
    """Determine current EGX market status"""
    try:
        cairo_tz = pytz.timezone('Africa/Cairo')
        now_cairo = datetime.now(cairo_tz)

        # EGX operates Sunday-Thursday, 10:00 AM - 2:30 PM
        is_weekday = now_cairo.weekday() < 4  # Monday=0, Thursday=3
        is_sunday = now_cairo.weekday() == 6  # Sunday=6
        is_trading_day = is_weekday or is_sunday

        current_time = now_cairo.time()
        market_open = current_time >= datetime.strptime("10:00", "%H:%M").time()
        market_close = current_time <= datetime.strptime("14:30", "%H:%M").time()
        is_market_hours = market_open and market_close

        if is_trading_day and is_market_hours:
            return "OPEN"
        elif is_trading_day:
            return "CLOSED_TODAY"
        else:
            return "WEEKEND"

    except Exception as e:
        logger.error(f"Error determining market status: {str(e)}")
        return "UNKNOWN"

def fetch_price_data_for_smc(symbol, interval):
    """Fetch price data from CSV files for enhanced SMC analysis"""
    try:
        # Remove EGX- prefix if present to match CSV filename
        csv_symbol = symbol.replace('EGX-', '')

        # Try multiple possible CSV locations
        csv_paths = [
            f'data/stocks/{csv_symbol}.csv',
            f'data/{csv_symbol}.csv',
            f'data/stocks/{csv_symbol}_new.csv'
        ]

        df = None
        for csv_path in csv_paths:
            if os.path.exists(csv_path):
                logger.info(f"Loading CSV data from: {csv_path}")
                df = pd.read_csv(csv_path)
                break

        if df is None:
            logger.warning(f"No CSV data found for {csv_symbol}, falling back to live data")
            return fetch_live_data_fallback(symbol, interval)

        # Convert to OHLCV format for SMC analysis
        df_smc = prepare_csv_for_smc(df, interval)

        if df_smc is not None and len(df_smc) > 50:  # Ensure sufficient data
            logger.info(f"Successfully loaded {len(df_smc)} bars of historical data for SMC analysis")
            return df_smc
        else:
            logger.warning(f"Insufficient CSV data ({len(df_smc) if df_smc is not None else 0} bars), falling back to live data")
            return fetch_live_data_fallback(symbol, interval)

    except Exception as e:
        logger.error(f"Error loading CSV data: {str(e)}")
        return fetch_live_data_fallback(symbol, interval)

def prepare_csv_for_smc(df, interval):
    """Convert CSV data to SMC-compatible OHLCV format"""
    try:
        # Ensure required columns exist
        required_cols = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        if not all(col in df.columns for col in required_cols):
            logger.error(f"Missing required columns. Available: {list(df.columns)}")
            return None

        # Convert Date column to datetime
        df['Date'] = pd.to_datetime(df['Date'])

        # Sort by date
        df = df.sort_values('Date')

        # Create SMC-compatible DataFrame
        df_smc = pd.DataFrame({
            'open': df['Open'].astype(float),
            'high': df['High'].astype(float),
            'low': df['Low'].astype(float),
            'close': df['Close'].astype(float),
            'volume': df['Volume'].astype(int)
        })

        # Set date as index
        df_smc.index = df['Date']

        # Filter based on interval if needed (for now, use daily data)
        # Future enhancement: implement interval filtering

        # Get recent data for analysis (last 6 months for better patterns)
        if len(df_smc) > 180:
            df_smc = df_smc.tail(180)  # Last 6 months

        logger.info(f"Prepared SMC data: {len(df_smc)} bars from {df_smc.index[0].date()} to {df_smc.index[-1].date()}")
        return df_smc

    except Exception as e:
        logger.error(f"Error preparing CSV data for SMC: {str(e)}")
        return None

def fetch_live_data_fallback(symbol, interval):
    """Fallback to live data if CSV data is not available with API server integration"""
    try:
        # Check if API server is available
        api_status = check_api_status()

        if api_status:
            logger.info("Using TradingView API server for live data fallback")
            # Get current price from TradingView API
            payload = {
                "pairs": [symbol],
                "intervals": [interval]
            }

            response = requests.post(
                TRADINGVIEW_API_URL,
                json=payload,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                stock_data = data.get(symbol, [])

                if stock_data:
                    current_price = stock_data[0].get('price', 80.0)
                    logger.info(f"Using live price {current_price} from API server for fallback data generation")

                    # Generate realistic OHLCV data for SMC analysis
                    df = generate_ohlcv_data(current_price, 200)
                    return df

        # Fallback to direct scraping if API server is not available
        logger.info("API server not available, using direct scraping for fallback")
        try:
            from scrapers.price_scraper import PriceScraper
            scraper = PriceScraper(source="tradingview")
            try:
                # Extract symbol without EGX- prefix for scraper
                clean_symbol = symbol.replace('EGX-', '')
                price_data = scraper.get_price(clean_symbol)

                if price_data and isinstance(price_data, dict):
                    current_price = price_data.get('price', 80.0)
                    logger.info(f"Using scraped price {current_price} for fallback data generation")

                    # Generate realistic OHLCV data for SMC analysis
                    df = generate_ohlcv_data(current_price, 200)
                    return df
            finally:
                scraper.close_driver()
        except Exception as scraper_error:
            logger.warning(f"Direct scraping failed: {str(scraper_error)}")

        # Ultimate fallback with default price
        logger.info("Using default price for fallback data generation")
        df = generate_ohlcv_data(80.0, 200)  # Default price
        return df

    except Exception as e:
        logger.error(f"Error in live data fallback: {str(e)}")
        # Ultimate fallback
        return generate_ohlcv_data(80.0, 200)

def generate_ohlcv_data(current_price, bars):
    """Generate realistic OHLCV data for SMC analysis"""
    np.random.seed(42)  # For consistent data
    
    # Generate price movements working backwards from current price
    volatility = 0.02  # 2% daily volatility
    returns = np.random.normal(0, volatility, bars)
    
    # Start from current price and work backwards
    prices = [current_price]
    for i in range(bars - 1):
        prev_price = prices[0] / (1 + returns[bars - 1 - i])
        prices.insert(0, max(prev_price, 1.0))
    
    # Create OHLCV data
    data = []
    for i in range(bars):
        close = prices[i]
        intraday_vol = close * 0.015  # 1.5% intraday volatility
        
        high = close + np.random.uniform(0, intraday_vol)
        low = close - np.random.uniform(0, intraday_vol)
        open_price = low + np.random.uniform(0, high - low)
        
        # Ensure OHLC relationships
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        volume = np.random.randint(100000, 2000000)
        
        data.append({
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.index = pd.date_range(end=datetime.now(), periods=bars, freq='D')
    
    return df

def run_smc_analysis(df, symbol, interval):
    """Run SMC analysis on the given data using the new SMC analysis engine"""
    try:
        # Initialize the SMC analysis engine
        smc_engine = SMCAnalysisEngine()
        
        # Run comprehensive SMC analysis
        smc_results = smc_engine.analyze_stock(df, symbol)
        
        # Extract individual components for compatibility with display functions
        results = {
            'order_blocks': smc_results.get('order_blocks', []),
            'fvgs': smc_results.get('fvgs', []),
            'liquidity_zones': smc_results.get('liquidity_zones', []),
            'market_structure': smc_results.get('market_structure', {}),
            'bos_signals': smc_results.get('bos_events', []),  # Map bos_events to bos_signals
            'premium_discount': smc_results.get('premium_discount_zones', {}),  # Map to premium_discount
            'confluence': {
                'total_score': smc_results.get('confluence_strength', 0.5),
                'active_factors': len([f for f in smc_results.get('confluence_factors', {}).values() if f > 0]),
                'factors': smc_results.get('confluence_factors', {}),
                'breakdown': smc_results.get('confluence_factors', {})
            },
            'signals': smc_results.get('trading_signal', []),
            'current_price': df['close'].iloc[-1] if not df.empty else None,
            'summary': smc_results.get('summary', {}),
            'risk_levels': smc_results.get('risk_levels', {}),
            'smc_engine': smc_engine  # Pass engine for additional analysis
        }

        return results

    except Exception as e:
        logger.error(f"Error running SMC analysis: {str(e)}")
        return {}

def display_smc_results(results, symbol, interval, df):
    """
    Display SMC analysis results in the Streamlit app
    
    Args:
        results: Dictionary with SMC analysis results
        symbol: Stock symbol
        interval: Analysis interval/timeframe
        df: Price data DataFrame
    """
    st.subheader(f"📊 SMC Analysis for {symbol} ({interval})")
    
    # Organization tabs for different analysis components
    tabs = st.tabs([
        "📊 Overview", 
        "⬜ Order Blocks", 
        "⚡ Fair Value Gaps",
        "💧 Liquidity Zones",
        "🔄 Break of Structure",
        "🏷️ Premium/Discount",
        "⏱️ Multi-Timeframe",
        "🧠 AI Patterns",
        "🛡️ Risk Management"
    ])
    
    # Overview Tab
    with tabs[0]:
        # Split into columns for layout
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # Simplified display for order blocks
            st.subheader("⬜ Order Blocks")
            if results.get('order_blocks', []):
                for i, ob in enumerate(results['order_blocks'][:3]):
                    # Handle different attribute structures
                    if hasattr(ob, 'block_type'):
                        st.write(f"OB-{i+1}: {ob.low:.2f} - {ob.high:.2f} ({ob.block_type})")
                    else:
                        st.write(f"OB-{i+1}: {ob.low:.2f} - {ob.high:.2f} ({getattr(ob, 'ob_type', 'unknown')})")
            else:
                st.info("No active order blocks detected")
                
            # Simplified display for FVGs
            st.subheader("⚡ Fair Value Gaps")
            if results.get('fvgs', []):
                for i, fvg in enumerate(results['fvgs'][:3]):
                    st.write(f"FVG-{i+1}: {fvg.low:.2f} - {fvg.high:.2f} ({fvg.gap_type})")
            else:
                st.info("No active fair value gaps detected")
        
        with col2:
            st.subheader("📈 Market Structure")
            # Market structure details
            if 'market_structure' in results:
                ms = results['market_structure']
                st.write(f"Trend: {ms.get('trend', 'N/A')}")
                st.write(f"Strength: {ms.get('strength', 0):.1%}")
            else:
                st.info("No market structure data available")
            
            # Confluence score
            if 'confluence' in results:
                conf_score = results['confluence'].get('total_score', 0.5)
                st.metric("Confluence Score", f"{conf_score:.1%}")
                
            # Current price
            if 'current_price' in results:
                st.metric("Current Price", f"{results['current_price']:.2f}")
    
    # Order Blocks Tab
    with tabs[1]:
        display_order_blocks(results.get('order_blocks', []))
        
    # Fair Value Gaps Tab
    with tabs[2]:
        display_fvgs(results.get('fvgs', []))
        
    # Liquidity Zones Tab
    with tabs[3]:
        display_liquidity_zones(results.get('liquidity_zones', []))
        
    # Break of Structure Tab
    with tabs[4]:
        display_break_of_structure(results.get('bos_signals', []))
        
    # Premium/Discount Tab
    with tabs[5]:
        display_premium_discount_zones_complete(results.get('premium_discount', {}))
        
    # Multi-Timeframe Tab
    with tabs[6]:
        display_multi_timeframe_analysis(results.get('multi_timeframe', {}))
        
    # AI Patterns Tab
    with tabs[7]:
        display_ai_pattern_recognition(results.get('pattern_matches', []))
        
    # Risk Management Tab
    with tabs[8]:
        # Generate predictions if not available
        predictions = results.get('risk_management', None)
        if predictions is None and 'current_price' in results:
            predictions = generate_smc_predictions(
                results['current_price'], 
                results.get('market_structure', {}),
                results.get('confluence', {}),
                results.get('order_blocks', []),
                results.get('fvgs', []),
                results.get('premium_discount', {})
            )
            
        display_risk_management(predictions, results.get('current_price', 0))
        
def display_fvgs(fvgs):
    """Display Fair Value Gaps information in table format"""

    if not fvgs:
        st.info("No active fair value gaps detected")
        return

    # Create table data
    table_data = []
    for i, fvg in enumerate(fvgs[:5]):
        # Ensure FVG object has all required attributes
        if not hasattr(fvg, 'filled'):
            fvg.filled = False
        if not hasattr(fvg, 'partially_filled'):
            fvg.partially_filled = False
            
        status_emoji = "🟢" if not fvg.filled else "🟡" if fvg.partially_filled else "🔴"
        status_text = "OPEN" if not fvg.filled else "PARTIAL" if fvg.partially_filled else "FILLED"

        table_data.append({
            "Level": f"FVG-{i+1}",
            "Type": fvg.gap_type.upper(),
            "High": f"{fvg.high:,.2f}",
            "Low": f"{fvg.low:,.2f}",
            "Strength": f"{fvg.strength:.1%}",
            "Status": f"{status_emoji} {status_text}"
        })

    # Display as DataFrame table
    if table_data:
        df_display = pd.DataFrame(table_data)
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Level": st.column_config.TextColumn("Level", width="small"),
                "Type": st.column_config.TextColumn("Type", width="small"),
                "High": st.column_config.TextColumn("High (EGP)", width="medium"),
                "Low": st.column_config.TextColumn("Low (EGP)", width="medium"),
                "Strength": st.column_config.TextColumn("Strength", width="small"),
                "Status": st.column_config.TextColumn("Status", width="medium")
            }
        )

def display_order_blocks(order_blocks):
    """Display Order Blocks information in table format"""
    
    if not order_blocks:
        st.info("No active order blocks detected")
        return
        
    # Create table data
    table_data = []
    for i, ob in enumerate(order_blocks[:5]):
        # Handle different attribute names: ob_type vs block_type
        block_type = getattr(ob, 'block_type', getattr(ob, 'ob_type', 'unknown'))
        
        # Handle price attribute vs high/low attributes
        if hasattr(ob, 'price'):
            price_display = f"{ob.price:,.2f}"
        else:
            # If we have high/low attributes instead of a price
            high = getattr(ob, 'high', 0)
            low = getattr(ob, 'low', 0)
            if high > 0 and low > 0:
                price_display = f"{low:,.2f} - {high:,.2f}"
            else:
                price_display = "N/A"
                
        # Check for age attribute
        age = getattr(ob, 'age', 'N/A')
        if age == 'N/A':
            age_display = "Recent"
        else:
            age_display = f"{age} bars"
            
        strength_emoji = "🟢" if ob.strength >= 0.7 else "🟡" if ob.strength >= 0.4 else "🔴"
        
        table_data.append({
            "Level": f"OB-{i+1}",
            "Type": block_type.upper(),
            "Price": price_display,
            "Strength": f"{strength_emoji} {ob.strength:.1%}",
            "Age": age_display
        })
    
    # Display as DataFrame table
    if table_data:
        df_display = pd.DataFrame(table_data)
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Level": st.column_config.TextColumn("Level", width="small"),
                "Type": st.column_config.TextColumn("Type", width="small"),
                "Price": st.column_config.TextColumn("Price (EGP)", width="medium"),
                "Strength": st.column_config.TextColumn("Strength", width="medium"),
                "Age": st.column_config.TextColumn("Age", width="small"),
            }
        )

def display_liquidity_zones(liquidity_zones):
    """Display Liquidity Zones information in table format"""
    
    if not liquidity_zones:
        st.info("No active liquidity zones detected")
        return
        
    # Create table data
    table_data = []
    for i, lz in enumerate(liquidity_zones[:5]):
        status_emoji = "🟢" if not getattr(lz, 'swept', False) else "🔴"
        status_text = "ACTIVE" if not getattr(lz, 'swept', False) else "SWEPT"
        
        table_data.append({
            "Level": f"LZ-{i+1}",
            "Type": lz.zone_type.value.upper().replace('_', ' '),
            "High": f"{lz.high:,.2f}",
            "Low": f"{lz.low:,.2f}",
            "Strength": f"{lz.strength:.1%}",
            "Status": f"{status_emoji} {status_text}"
        })
    
    # Display as DataFrame table
    if table_data:
        df_display = pd.DataFrame(table_data)
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Level": st.column_config.TextColumn("Level", width="small"),
                "Type": st.column_config.TextColumn("Type", width="medium"),
                "High": st.column_config.TextColumn("High (EGP)", width="medium"),
                "Low": st.column_config.TextColumn("Low (EGP)", width="medium"),
                "Strength": st.column_config.TextColumn("Strength", width="small"),
                "Status": st.column_config.TextColumn("Status", width="medium")
            }
        )

def display_risk_management(predictions, current_price):
    """Display risk management information including stop loss, take profit levels"""

    if not predictions:
        st.info("Insufficient data for risk management calculations")
        return

    try:
        # Extract values from the first prediction (assuming predictions is a list)
        first_prediction = predictions[0] if isinstance(predictions, list) and predictions else {}
        direction = first_prediction.get('direction', 'NEUTRAL')
        stop_loss = first_prediction.get('stop_loss', current_price * 0.95)  # Default to 5% below
        take_profit = first_prediction.get('take_profit', current_price * 1.05)  # Default to 5% above
        risk_reward = first_prediction.get('risk_reward', 1.0)
        confidence = first_prediction.get('confidence', 0.5)

        # Logging for debugging
        logger.debug(f"Risk Management - Direction: {direction}")
        logger.debug(f"Risk Management - Current Price: {current_price:.2f}")
        logger.debug(f"Risk Management - Stop Loss: {stop_loss:.2f}")
        logger.debug(f"Risk Management - Take Profit: {take_profit:.2f}")
        logger.debug(f"Risk Management - R:R: {risk_reward:.2f}")

        # Create risk management panel
        col1, col2 = st.columns(2)

        with col1:
            direction_color = "#22c55e" if direction == "BULLISH" else "#ef4444" if direction == "BEARISH" else "#cbd5e1"
            direction_emoji = "📈" if direction == "BULLISH" else "📉" if direction == "BEARISH" else "↔️"

            st.markdown(f"""
            <div style='background: linear-gradient(135deg, {direction_color}22 0%, {direction_color}44 100%);
                        padding: 15px; border-radius: 10px; border: 1px solid {direction_color};
                        text-align: center;'>
                <h3 style='margin: 0; color: {direction_color};'>{direction_emoji} {direction}</h3>
                <p style='margin: 5px 0 0 0;'>Bias Direction</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            confidence_color = "#22c55e" if confidence >= 0.7 else "#f59e0b" if confidence >= 0.4 else "#ef4444"
            confidence_emoji = "🟢" if confidence >= 0.7 else "🟡" if confidence >= 0.4 else "🔴"

            st.markdown(f"""
            <div style='background: linear-gradient(135deg, {confidence_color}22 0%, {confidence_color}44 100%);
                        padding: 15px; border-radius: 10px; border: 1px solid {confidence_color};
                        text-align: center;'>
                <h3 style='margin: 0; color: {confidence_color};'>{confidence_emoji} {confidence:.1%}</h3>
                <p style='margin: 5px 0 0 0;'>Signal Confidence</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Price levels and risk metrics
        st.markdown("### 📊 Key Price Levels")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            sl_pct = abs((stop_loss - current_price) / current_price * 100)
            sl_color = "#ef4444"  # Red for stop loss
            
            st.markdown(f"""
            <div style='background: linear-gradient(135deg, {sl_color}22 0%, {sl_color}44 100%);
                        padding: 15px; border-radius: 10px; border: 1px solid {sl_color};
                        text-align: center;'>
                <h4 style='margin: 0; color: {sl_color};'>🛑 STOP LOSS</h4>
                <h3 style='margin: 5px 0; color: {sl_color};'>{stop_loss:.2f} EGP</h3>
                <p style='margin: 0;'>({sl_pct:.2f}% from current)</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            price_color = "#3b82f6"  # Blue for current price
            
            st.markdown(f"""
            <div style='background: linear-gradient(135deg, {price_color}22 0%, {price_color}44 100%);
                        padding: 15px; border-radius: 10px; border: 1px solid {price_color};
                        text-align: center;'>
                <h4 style='margin: 0; color: {price_color};'>💰 CURRENT PRICE</h4>
                <h3 style='margin: 5px 0; color: {price_color};'>{current_price:.2f} EGP</h3>
                <p style='margin: 0;'>&nbsp;</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            tp_pct = abs((take_profit - current_price) / current_price * 100)
            tp_color = "#22c55e"  # Green for take profit
            
            st.markdown(f"""
            <div style='background: linear-gradient(135deg, {tp_color}22 0%, {tp_color}44 100%);
                        padding: 15px; border-radius: 10px; border: 1px solid {tp_color};
                        text-align: center;'>
                <h4 style='margin: 0; color: {tp_color};'>🎯 TAKE PROFIT</h4>
                <h3 style='margin: 5px 0; color: {tp_color};'>{take_profit:.2f} EGP</h3>
                <p style='margin: 0;'>({tp_pct:.2f}% from current)</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Risk reward ratio
        st.markdown("### ⚖️ Risk-Reward Analysis")
        
        rr_color = "#22c55e" if risk_reward >= 2 else "#f59e0b" if risk_reward >= 1 else "#ef4444"
        
        st.markdown(f"""
        <div style='background: linear-gradient(135deg, {rr_color}22 0%, {rr_color}44 100%);
                    padding: 15px; border-radius: 10px; border: 1px solid {rr_color};
                    text-align: center;'>
            <h3 style='margin: 0; color: {rr_color};'>1 : {risk_reward:.2f}</h3>
            <p style='margin: 5px 0 0 0;'>Risk-Reward Ratio</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Position sizing
        st.markdown("### 📏 Position Sizing Calculator")
        col1, col2 = st.columns(2)
        
        with col1:
            account_value = st.number_input("Account Value (EGP)", min_value=1000.0, value=100000.0, step=1000.0)
            risk_pct = st.slider("Risk Per Trade (%)", min_value=0.1, max_value=5.0, value=1.0, step=0.1)
        
        with col2:
            # Calculate position size
            risk_amount = account_value * (risk_pct / 100)
            risk_per_share = abs(current_price - stop_loss)
            
            if risk_per_share > 0:
                position_size = risk_amount / risk_per_share
                position_value = position_size * current_price
                
                st.metric(
                    label="Maximum Position Size (Shares)", 
                    value=f"{position_size:.2f}",
                    delta=f"{position_value:,.2f} EGP"
                )
                
                st.metric(
                    label="Potential Profit (EGP)", 
                    value=f"{position_size * abs(take_profit - current_price):,.2f}",
                    delta=f"{risk_reward:.2f}x your risk"
                )
            else:
                st.warning("Cannot calculate position size: Stop loss too close to current price")
    
    except Exception as e:
        logger.error(f"Error in risk management display: {str(e)}")
        st.error(f"Error calculating risk management metrics: {str(e)}")
        st.info("Try adjusting your analysis parameters or selecting a different security.")

def display_break_of_structure(bos_events):
    """Display Break of Structure events in table format"""
    
    if not bos_events:
        st.info("No Break of Structure events detected")
        return
    
    # Create table data
    table_data = []
    for i, bos in enumerate(bos_events[:5]):
        # Determine color and indicator based on direction and strength
        direction_emoji = "📈" if bos.direction == "bullish" else "📉"
        strength_emoji = "🟢" if bos.strength >= 0.7 else "🟡" if bos.strength >= 0.4 else "🔴"
        confirmed = "✅" if getattr(bos, 'confirmed', False) else "⏳"
        
        table_data.append({
            "Event": f"BOS-{i+1}",
            "Type": f"{bos.structure_type}",
            "Direction": f"{direction_emoji} {bos.direction.upper()}",
            "Price": f"{bos.price:,.2f}",
            "Previous": f"{bos.previous_level:,.2f}",
            "Strength": f"{strength_emoji} {bos.strength:.1%}",
            "Status": f"{confirmed}"
        })
    
    # Display as DataFrame table
    if table_data:
        df_display = pd.DataFrame(table_data)
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Event": st.column_config.TextColumn("Event", width="small"),
                "Type": st.column_config.TextColumn("Type", width="small"),
                "Direction": st.column_config.TextColumn("Direction", width="medium"),
                "Price": st.column_config.TextColumn("Price (EGP)", width="medium"),
                "Previous": st.column_config.TextColumn("Previous Level", width="medium"),
                "Strength": st.column_config.TextColumn("Strength", width="small"),
                "Status": st.column_config.TextColumn("Confirmed", width="small")
            }
        )

def display_multi_timeframe_analysis(multi_tf_analysis):
    """Display Multi-Timeframe Analysis results"""
    
    if not multi_tf_analysis:
        st.info("No multi-timeframe analysis data available")
        return
        
    st.subheader("⏱️ Multi-Timeframe Analysis")
    
    # Create tabs for different timeframe views
    tf_tabs = st.tabs([tf.capitalize() for tf in multi_tf_analysis.keys()])
    
    # For each timeframe tab, display trend information and key levels
    for i, (tf, tf_tab) in enumerate(zip(multi_tf_analysis.keys(), tf_tabs)):
        tf_data = multi_tf_analysis.get(tf, {})  # Ensure tf_data is accessed safely
        with tf_tab:
            col1, col2 = st.columns(2)

            with col1:
                # Trend information
                trend_dir = tf_data.get('trend_direction', 'NEUTRAL').upper()
                trend_strength = tf_data.get('trend_strength', 0.0)

                trend_color = "#22c55e" if trend_dir == "BULLISH" else "#ef4444" if trend_dir == "BEARISH" else "#f59e0b"
                trend_emoji = "📈" if trend_dir == "BULLISH" else "📉" if trend_dir == "BEARISH" else "↔️"

                st.markdown(f"""
                <div style='background: linear-gradient(135deg, {trend_color}22  0%, {trend_color}44 100%);
                            padding: 15px; border-radius: 10px; border: 1px solid {trend_color};'>
                    <h3 style='margin: 0; color: {trend_color};'>{trend_emoji} {trend_dir}</h3>
                    <p style='margin: 5px 0 0 0;'>Trend Direction</p>
                    <div style='margin-top: 10px; height: 10px; background: #e5e7eb; border-radius: 5px;'>
                        <div style='height: 10px; width: {trend_strength*100}%; background: {trend_color}; border-radius: 5px;'></div>
                    </div>
                    <p style='margin: 5px 0 0 0; text-align: right;'>{trend_strength:.1%} strength</p>
                </div>
                """, unsafe_allow_html=True)
                
                # Market structure score
                ms_score = tf_data.get('market_structure_score', 0)
                ms_color = "#22c55e" if ms_score >= 0.7 else "#f59e0b" if ms_score >= 0.4 else "#ef4444"
                
                st.markdown(f"""
                <div style='background: linear-gradient(135deg, {ms_color}22 0%, {ms_color}44 100%);
                            padding: 15px; border-radius: 10px; border: 1px solid {ms_color}; margin-top: 15px;'>
                    <h3 style='margin: 0; color: {ms_color};'>{ms_score:.1%}</h3>
                    <p style='margin: 5px 0 0 0;'>Market Structure Score</p>
                </div>
                """, unsafe_allow_html=True)
                
            with col2:
                # Key levels table
                st.markdown("#### 🔑 Key Levels")
                
                if tf_data.get('key_levels'):
                    # Format key levels
                    key_levels_data = []
                    for j, level in enumerate(tf_data['key_levels']):
                        key_levels_data.append({
                            "Level": f"L{j+1}",
                            "Price": f"{level:,.2f}"
                        })
                    
                    levels_df = pd.DataFrame(key_levels_data)
                    st.dataframe(
                        levels_df,
                        use_container_width=True,
                        hide_index=True
                    )
                else:
                    st.info("No key levels detected")
                
                # Confluence score
                conf_score = tf_data.get('confluence_score', 0.5)
                conf_color = "#22c55e" if conf_score >= 0.7 else "#f59e0b" if conf_score >= 0.4 else "#ef4444"
                
                st.markdown(f"""
                <div style='background: linear-gradient(135deg, {conf_color}22 0%, {conf_color}44 100%);
                            padding: 15px; border-radius: 10px; border: 1px solid {conf_color}; margin-top: 15px;'>
                    <h3 style='margin: 0; color: {conf_color};'>{conf_score:.1%}</h3>
                    <p style='margin: 5px 0 0 0;'>Confluence Score</p>
                </div>
                """, unsafe_allow_html=True)
    
    # Display multi-timeframe signal if available
    if 'signal' in multi_tf_analysis and multi_tf_analysis['signal']:
        signal = multi_tf_analysis['signal']
        
        st.subheader("📊 Multi-Timeframe Signal")
        
        # Signal Header Card
        signal_type = signal.signal_type.upper()
        signal_color = "#22c55e" if signal_type == "BUY" else "#ef4444" if signal_type == "SELL" else "#f59e0b"
        signal_emoji = "🟢" if signal_type == "BUY" else "🔴" if signal_type == "SELL" else "⚠️"
        
        st.markdown(f"""
        <div style='background: linear-gradient(135deg, {signal_color}22 0%, {signal_color}44 100%);
                    padding: 20px; border-radius: 10px; border: 2px solid {signal_color}; text-align: center;'>
            <h2 style='margin: 0; color: {signal_color};'>{signal_emoji} {signal_type} SIGNAL</h2>
            <p style='margin: 10px 0 0 0;'>Confidence: {signal.confidence:.1%} • Risk Level: {signal.risk_level.upper()}</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Signal Details
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Entry Price", f"{signal.entry_price:.2f} EGP")
        
        with col2:
            st.metric("Stop Loss", f"{signal.stop_loss:.2f} EGP", 
                     delta=f"{(signal.stop_loss - signal.entry_price) / signal.entry_price * 100:.2f}%")
        
        with col3:
            st.metric("Take Profit", f"{signal.take_profit_1:.2f} EGP", 
                     delta=f"{(signal.take_profit_1 - signal.entry_price) / signal.entry_price * 100:.2f}%")
        
        # Timeframe alignment
        st.markdown("#### ⏱️ Timeframe Alignment")
        alignment_data = []
        
        for tf, direction in signal.timeframe_alignment.items():
            dir_emoji = "📈" if direction.lower() == "bullish" else "📉" if direction.lower() == "bearish" else "↔️"
            dir_color = "#22c55e" if direction.lower() == "bullish" else "#ef4444" if direction.lower() == "bearish" else "#f59e0b"
            
            alignment_data.append({
                "Timeframe": tf.upper(),
                "Direction": f"<span style='color: {dir_color};'>{dir_emoji} {direction.upper()}</span>"
            })
        
        # Display alignment table
        if alignment_data:
            align_df = pd.DataFrame(alignment_data)
            st.dataframe(
                align_df,
                use_container_width=True,
                hide_index=True,
                column_config={
                    "Direction": st.column_config.Column("Direction", width="medium")
                }
            )
        
        # Supporting factors
        st.markdown("#### 🧩 Supporting Factors")
        if signal.supporting_factors:
            for i, factor in enumerate(signal.supporting_factors):
                st.markdown(f"- {factor}")
        else:
            st.info("No supporting factors provided")

def display_premium_discount_zones(premium_discount):
    """Display Premium/Discount Zones analysis"""
    
    if not premium_discount or not isinstance(premium_discount, dict):
        st.info("No premium/discount zones detected")
        return
    
    st.subheader("🏷️ Premium/Discount Zones")
    
    # Extract zone data
    range_high = premium_discount.get('range_high', 0)
    range_low = premium_discount.get('range_low', 0)
    range_mid = premium_discount.get('range_mid', 0)
    premium_threshold = premium_discount.get('premium_threshold', 0)
    discount_threshold = premium_discount.get('discount_threshold', 0)
    current_zone = premium_discount.get('current_zone', 'unknown')
    
    if range_high == 0 or range_low == 0:
        st.info("Insufficient data for premium/discount zone calculation")
        return
    
    col1, col2 = st.columns([3, 2])
    
    with col1:
        # Create zone visualization
        fig = go.Figure()
        
        # Add equilibrium level
        fig.add_shape(
            type="line",
            x0=0, x1=1,
            y0=range_mid, y1=range_mid,
            line=dict(color="#3b82f6", width=2, dash="dash"),
        )
        
        # Add premium zone
        fig.add_shape(
            type="rect",
            x0=0, x1=1,
            y0=premium_threshold, y1=range_high,
            fillcolor="rgba(239, 68, 68, 0.2)",
            line=dict(color="rgba(239, 68, 68, 0.5)", width=1),
        )
        
        # Add equilibrium zone
        fig.add_shape(
            type="rect",
            x0=0, x1=1,
            y0=discount_threshold, y1=premium_threshold,
            fillcolor="rgba(59, 130, 246, 0.2)",
            line=dict(color="rgba(59, 130, 246, 0.5)", width=1),
        )
        
        # Add discount zone
        fig.add_shape(
            type="rect",
            x0=0, x1=1,
            y0=range_low, y1=discount_threshold,
            fillcolor="rgba(34, 197, 94, 0.2)",
            line=dict(color="rgba(34, 197, 94, 0.5)", width=1),
        )
        
        # Add labels
        annotations = [
            dict(
                x=0.5, y=range_high,
                text=f"Range High: {range_high:.2f}",
                showarrow=False,
                yanchor="bottom",
                font=dict(color="#ef4444")
            ),
            dict(
                x=0.5, y=premium_threshold,
                text=f"Premium Zone: {premium_threshold:.2f}",
                showarrow=False,
                yanchor="bottom",
                font=dict(color="#ef4444")
            ),
            dict(
                x=0.5, y=range_mid,
                text=f"Equilibrium: {range_mid:.2f}",
                showarrow=False,
                yanchor="bottom",
                font=dict(color="#3b82f6")
            ),
            dict(
                x=0.5, y=discount_threshold,
                text=f"Discount Zone: {discount_threshold:.2f}",
                showarrow=False,
                yanchor="top",
                font=dict(color="#22c55e")
            ),
            dict(
                x=0.5, y=range_low,
                text=f"Range Low: {range_low:.2f}",
                showarrow=False,
                yanchor="top",
                font=dict(color="#22c55e")
            ),
        ]
        
        fig.update_layout(
            title="Premium/Discount Zones",
            height=400,
            xaxis=dict(visible=False),
            yaxis=dict(title="Price (EGP)"),
            margin=dict(l=20, r=20, t=50, b=20),
            annotations=annotations
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
    with col2:
        # Zone metrics and current status
        premium_range = range_high - premium_threshold
        equilibrium_range = premium_threshold - discount_threshold
        discount_range = discount_threshold - range_low
        total_range = range_high - range_low
        
        # Current zone status
        zone_color = {
            'premium': "#ef4444",
            'equilibrium': "#3b82f6", 
            'discount': "#22c55e",
            'unknown': "#6b7280"
        }.get(current_zone, "#6b7280")
        
        zone_emoji = {
            'premium': "🔴",
            'equilibrium': "🔵",
            'discount': "🟢",
            'unknown': "⚪"
        }.get(current_zone, "⚪")
        
        st.markdown(f"""
        <div style='background: linear-gradient(135deg, {zone_color}22 0%, {zone_color}44 100%);
                    padding: 20px; border-radius: 10px; border: 2px solid {zone_color};
                    text-align: center; margin-bottom: 20px;'>
            <h2 style='margin: 0; color: {zone_color};'>{zone_emoji} {current_zone.upper()}</h2>
            <p style='margin: 10px 0 0 0;'>Current Zone</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Zone breakdown
        if total_range > 0:
            premium_pct = (premium_range / total_range) * 100
            equilibrium_pct = (equilibrium_range / total_range) * 100
            discount_pct = (discount_range / total_range) * 100
            
            st.markdown("#### 📊 Zone Breakdown")
            
            zone_data = [
                {"Zone": "Premium", "Range": f"{premium_range:.2f}", "Percentage": f"{premium_pct:.1f}%"},
                {"Zone": "Equilibrium", "Range": f"{equilibrium_range:.2f}", "Percentage": f"{equilibrium_pct:.1f}%"},
                {"Zone": "Discount", "Range": f"{discount_range:.2f}", "Percentage": f"{discount_pct:.1f}%"}
            ]
            
            zone_df = pd.DataFrame(zone_data)
            st.dataframe(
                zone_df,
                use_container_width=True,
                hide_index=True
            )

def display_ai_pattern_recognition(pattern_matches):
    """Display AI-detected pattern matches and analysis"""
    
    if not pattern_matches:
        st.info("No pattern matches detected by AI")
        st.markdown("""
        **AI Pattern Recognition Features:**
        - Chart pattern detection
        - Historical pattern matching
        - Success probability analysis
        - Risk-reward optimization
        
        *This feature requires additional configuration and training data.*
        """)
        return
    
    st.subheader("🧠 AI Pattern Recognition")
    
    # Simplified display for now - can be enhanced later
    for i, pattern in enumerate(pattern_matches[:3]):
        if hasattr(pattern, 'pattern_name'):
            pattern_type_color = "#22c55e" if getattr(pattern, 'pattern_type', '') == "bullish" else "#ef4444"
            
            st.markdown(f"""
            <div style='background: linear-gradient(135deg, {pattern_type_color}22 0%, {pattern_type_color}44 100%);
                        padding: 15px; border-radius: 10px; border: 1px solid {pattern_type_color};'>
                <h4 style='margin: 0; color: {pattern_type_color};'>Pattern {i+1}: {pattern.pattern_name}</h4>
                <p style='margin: 5px 0 0 0;'>Confidence: {getattr(pattern, 'confidence', 0):.1%}</p>
            </div>
            """, unsafe_allow_html=True)

def display_multi_timeframe_analysis(multi_tf_analysis):
    """Display Multi-Timeframe Analysis results"""
    
    if not multi_tf_analysis:
        st.info("No multi-timeframe analysis data available")
        st.markdown("""
        **Multi-Timeframe Analysis Features:**
        - Cross-timeframe trend alignment
        - Higher timeframe bias
        - Multiple confluence factors
        - Enhanced signal validation
        
        *This feature analyzes multiple timeframes for stronger signals.*
        """)
        return
        
    st.subheader("⏱️ Multi-Timeframe Analysis")
    
    # Simplified display for dictionary-based data
    if isinstance(multi_tf_analysis, dict):
        for tf, data in multi_tf_analysis.items():
            if isinstance(data, dict):
                trend = data.get('trend_direction', 'Unknown')
                strength = data.get('trend_strength', 0)
                
                trend_color = "#22c55e" if trend.lower() == "bullish" else "#ef4444" if trend.lower() == "bearish" else "#f59e0b"
                
                st.markdown(f"""
                <div style='background: linear-gradient(135deg, {trend_color}22 0%, {trend_color}44 100%);
                            padding: 10px; border-radius: 10px; border: 1px solid {trend_color}; margin: 5px 0;'>
                    <h5 style='margin: 0; color: {trend_color};'>{tf.upper()}: {trend.upper()}</h5>
                    <p style='margin: 2px 0 0 0;'>Strength: {strength:.1%}</p>
                </div>
                """, unsafe_allow_html=True)

def detect_market_structure(df: pd.DataFrame) -> dict:
    """Detect market structure from price data"""
    structure = {
        "trend": "consolidation",
        "event": None,
        "highs": [],
        "lows": [],
        "strength": 0.5
    }

    try:
        df_copy = df.copy()
        df_copy["swing_high"] = df_copy["high"][
            (df_copy["high"] > df_copy["high"].shift(1)) & 
            (df_copy["high"] > df_copy["high"].shift(-1))
        ]
        df_copy["swing_low"] = df_copy["low"][
            (df_copy["low"] < df_copy["low"].shift(1)) & 
            (df_copy["low"] < df_copy["low"].shift(-1))
        ]

        highs = df_copy["swing_high"].dropna()
        lows = df_copy["swing_low"].dropna()

        if len(highs) > 1 and highs.iloc[-1] > highs.iloc[-2]:
            structure["event"] = "BOS"
            structure["trend"] = "bullish"
            structure["strength"] = 0.7
        elif len(lows) > 1 and lows.iloc[-1] < lows.iloc[-2]:
            structure["event"] = "CHoCH"
            structure["trend"] = "bearish"
            structure["strength"] = 0.7
        else:
            structure["trend"] = "consolidation"
            structure["strength"] = 0.5

        structure["highs"] = highs
        structure["lows"] = lows
        
    except Exception as e:
        logger.error(f"Error detecting market structure: {e}")

    return structure

def display_ai_pattern_recognition(pattern_matches):
    """Display AI-detected pattern matches and analysis"""
    
    if not pattern_matches:
        st.info("No AI pattern matches detected")
        return
    
    st.subheader("🧠 AI Pattern Recognition")
    
    # Simplified display for now - can be enhanced later
    for i, pattern in enumerate(pattern_matches[:3]):
        pattern_name = getattr(pattern, 'pattern_name', f'Pattern {i+1}')
        confidence = getattr(pattern, 'confidence', 0.5)
        pattern_type = getattr(pattern, 'pattern_type', 'Unknown')
        
        st.markdown(f"""
        **{pattern_name}** ({pattern_type})  
        Confidence: {confidence:.1%}
        """)

def display_premium_discount_zones_complete(premium_discount):
    """Complete implementation of premium/discount zones display"""
    
    if not premium_discount or not isinstance(premium_discount, dict):
        st.info("No premium/discount zones detected")
        return
    
    st.subheader("🏷️ Premium/Discount Zones")
    
    # Extract zone data with defaults
    range_high = premium_discount.get('range_high', 0)
    range_low = premium_discount.get('range_low', 0) 
    range_mid = premium_discount.get('range_mid', (range_high + range_low) / 2 if range_high and range_low else 0)
    current_zone = premium_discount.get('current_zone', 'unknown')
    
    if range_high == 0 or range_low == 0:
        st.info("Insufficient data for premium/discount zone calculation")
        return
    
    # Calculate zone boundaries
    total_range = range_high - range_low
    premium_threshold = range_mid + (total_range * 0.25)
    discount_threshold = range_mid - (total_range * 0.25)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Zone status
        zone_color = {
            'premium': "#ef4444",
            'equilibrium': "#3b82f6", 
            'discount': "#22c55e",
            'unknown': "#6b7280"
        }.get(current_zone, "#6b7280")
        
        zone_emoji = {
            'premium': "🔴",
            'equilibrium': "🔵", 
            'discount': "🟢",
            'unknown': "⚪"
        }.get(current_zone, "⚪")
        
        st.markdown(f"""
        <div style='background: linear-gradient(135deg, {zone_color}22 0%, {zone_color}44 100%);
                    padding: 15px; border-radius: 10px; border: 1px solid {zone_color};
                    text-align: center;'>
            <h3 style='margin: 0; color: {zone_color};'>{zone_emoji} {current_zone.upper()} ZONE</h3>
            <p style='margin: 5px 0 0 0;'>Current Price Location</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        # Zone metrics
        st.metric("Range High", f"{range_high:.2f} EGP")
        st.metric("Range Low", f"{range_low:.2f} EGP") 
        st.metric("Equilibrium", f"{range_mid:.2f} EGP")