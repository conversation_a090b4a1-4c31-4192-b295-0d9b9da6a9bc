"""
Advanced AI Features Page
Integrates all advanced AI capabilities for enhanced trading analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
import os
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional

# Import our advanced AI modules
try:
    from app.utils.ai_sentiment_analyzer import AIMarketSentimentAnalyzer
    from app.utils.ai_ensemble_optimizer import AIEnsembleOptimizer
    from app.utils.ai_risk_manager import AIRiskManager
    from app.utils.ai_pattern_recognition import AIPatternRecognition
except ImportError as e:
    st.error(f"Error importing AI modules: {str(e)}")
    st.stop()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def show_advanced_ai_features():
    """Main function to display advanced AI features"""
    
    st.title("🧠 Advanced AI Features")
    st.markdown("### Cutting-edge AI/ML capabilities for enhanced trading analysis")
    
    # Sidebar for feature selection
    st.sidebar.markdown("## 🎯 AI Feature Selection")
    
    feature_options = {
        "🧠 AI Comprehensive Analysis": "comprehensive",
        "🔮 AI Sentiment Analysis": "sentiment",
        "🤖 Ensemble Model Optimizer": "ensemble", 
        "⚠️ AI Risk Management": "risk",
        "📊 Pattern Recognition AI": "patterns",
        "📈 Integrated AI Dashboard": "dashboard"
    }
    
    selected_feature = st.sidebar.selectbox(
        "Choose AI Feature:",
        options=list(feature_options.keys()),
        index=0
    )
    
    feature_key = feature_options[selected_feature]
    
    # Stock selection
    st.sidebar.markdown("## 📊 Stock Selection")
    
    # Get available stocks from CSV files
    available_stocks = get_available_stocks()
    
    if not available_stocks:
        st.error("No stock data available. Please use the CSV Converter to download stock data first.")
        return
    
    selected_stock = st.sidebar.selectbox(
        "Select Stock:",
        options=available_stocks,
        index=0
    )
    
    # Load stock data
    stock_data = load_stock_data(selected_stock)
    
    if stock_data is None or stock_data.empty:
        st.error(f"Could not load data for {selected_stock}")
        return
    
    # Display selected feature
    if feature_key == "comprehensive":
        show_ai_comprehensive_analysis(selected_stock, stock_data)
    elif feature_key == "sentiment":
        show_sentiment_analysis(selected_stock, stock_data)
    elif feature_key == "ensemble":
        show_ensemble_optimizer(selected_stock, stock_data)
    elif feature_key == "risk":
        show_risk_management(selected_stock, stock_data)
    elif feature_key == "patterns":
        show_pattern_recognition(selected_stock, stock_data)
    elif feature_key == "dashboard":
        show_integrated_dashboard(selected_stock, stock_data)

def calculate_data_quality(data: pd.DataFrame) -> float:
    """Calculate data quality score"""
    
    try:
        quality_factors = []
        
        # Data completeness
        completeness = 1.0 - (data.isnull().sum().sum() / (len(data) * len(data.columns)))
        quality_factors.append(completeness)
        
        # Data recency (prefer recent data)
        days_since_last = (datetime.now() - data.index[-1]).days
        recency_score = max(0.5, 1.0 - (days_since_last / 30))  # Decay over 30 days
        quality_factors.append(recency_score)
        
        # Data volume
        volume_score = min(1.0, len(data) / 100)  # 100+ days is optimal
        quality_factors.append(volume_score)
        
        # Price consistency (no extreme outliers)
        price_changes = data['Close'].pct_change().dropna()
        extreme_moves = (abs(price_changes) > 0.1).sum()  # More than 10% moves
        consistency_score = max(0.7, 1.0 - (extreme_moves / len(price_changes)))
        quality_factors.append(consistency_score)
        
        overall_quality = np.mean(quality_factors)
        return max(0.5, min(1.0, overall_quality))
        
    except Exception as e:
        logger.error(f"Error calculating data quality: {str(e)}")
        return 0.8


def calculate_signal_strength(sentiment_score, pattern_summary) -> float:
    """Calculate overall signal strength"""
    
    try:
        strength_factors = []
        
        # Sentiment strength
        sentiment_strength = abs(sentiment_score.overall_sentiment) * sentiment_score.confidence
        strength_factors.append(sentiment_strength)
        
        # Pattern strength
        total_patterns = pattern_summary.get('total_patterns', 0)
        if total_patterns > 0:
            bullish_patterns = pattern_summary.get('bullish_patterns', 0)
            bearish_patterns = pattern_summary.get('bearish_patterns', 0)
            
            pattern_dominance = abs(bullish_patterns - bearish_patterns) / total_patterns
            pattern_strength = pattern_dominance * min(1.0, total_patterns / 5)  # 5+ patterns is strong
            strength_factors.append(pattern_strength)
        
        overall_strength = np.mean(strength_factors) if strength_factors else 0.5
        return max(0.2, min(1.0, overall_strength))
        
    except Exception as e:
        logger.error(f"Error calculating signal strength: {str(e)}")
        return 0.5


def get_available_stocks() -> List[str]:
    """Get list of available stocks from CSV files"""
    
    try:
        import os
        data_dir = "data/stocks"
        
        if not os.path.exists(data_dir):
            return []
        
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        stocks = [f.replace('.csv', '') for f in csv_files]
        
        return sorted(stocks)
        
    except Exception as e:
        logger.error(f"Error getting available stocks: {str(e)}")
        return []

def load_stock_data(symbol: str) -> Optional[pd.DataFrame]:
    """Load stock data from CSV file"""
    
    try:
        file_path = f"data/stocks/{symbol}.csv"
        df = pd.read_csv(file_path)
        
        # Ensure Date column is datetime
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)
        
        # Sort by date
        df.sort_index(inplace=True)
        
        return df
        
    except Exception as e:
        logger.error(f"Error loading stock data for {symbol}: {str(e)}")
        return None

def show_sentiment_analysis(symbol: str, data: pd.DataFrame):
    """Display AI sentiment analysis"""
    
    st.markdown("## 🔮 AI-Powered Market Sentiment Analysis")
    st.markdown(f"### Analyzing sentiment for **{symbol}**")
    
    # Initialize sentiment analyzer
    sentiment_analyzer = AIMarketSentimentAnalyzer()
    
    # Analysis parameters
    col1, col2 = st.columns(2)
    with col1:
        timeframe_days = st.slider("Analysis Timeframe (days)", 1, 30, 7)
    with col2:
        refresh_analysis = st.button("🔄 Refresh Analysis")
    
    # Perform sentiment analysis
    with st.spinner("Analyzing market sentiment using AI..."):
        sentiment_score = sentiment_analyzer.analyze_stock_sentiment(symbol, timeframe_days)
    
    # Display sentiment results
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        sentiment_color = "🟢" if sentiment_score.overall_sentiment > 0.1 else "🔴" if sentiment_score.overall_sentiment < -0.1 else "🟡"
        st.metric(
            "Overall Sentiment",
            f"{sentiment_score.overall_sentiment:.3f}",
            delta=f"{sentiment_color} {sentiment_score.overall_sentiment:.1%}"
        )
    
    with col2:
        st.metric("Confidence", f"{sentiment_score.confidence:.1%}")
    
    with col3:
        st.metric("Bullish Signals", sentiment_score.bullish_signals)
    
    with col4:
        st.metric("Bearish Signals", sentiment_score.bearish_signals)
    
    # Sentiment breakdown chart
    st.markdown("### 📊 Sentiment Component Breakdown")
    
    breakdown_data = sentiment_score.sentiment_breakdown
    
    if breakdown_data:
        # Create sentiment breakdown chart
        fig = go.Figure()
        
        components = list(breakdown_data.keys())
        values = list(breakdown_data.values())
        colors = ['green' if v > 0 else 'red' if v < 0 else 'gray' for v in values]
        
        fig.add_trace(go.Bar(
            x=components,
            y=values,
            marker_color=colors,
            text=[f"{v:.3f}" for v in values],
            textposition='auto'
        ))
        
        fig.update_layout(
            title="Sentiment Components Analysis",
            xaxis_title="Component",
            yaxis_title="Sentiment Score",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # Key themes
    st.markdown("### 🎯 Key Sentiment Themes")
    
    for theme in sentiment_score.key_themes:
        st.markdown(f"• **{theme}**")
    
    # Sentiment interpretation
    st.markdown("### 💡 AI Sentiment Interpretation")
    
    if sentiment_score.overall_sentiment > 0.3:
        st.success("🟢 **Strong Bullish Sentiment** - Market conditions favor upward price movement")
    elif sentiment_score.overall_sentiment > 0.1:
        st.info("🟡 **Moderate Bullish Sentiment** - Positive but cautious outlook")
    elif sentiment_score.overall_sentiment < -0.3:
        st.error("🔴 **Strong Bearish Sentiment** - Market conditions suggest downward pressure")
    elif sentiment_score.overall_sentiment < -0.1:
        st.warning("🟡 **Moderate Bearish Sentiment** - Negative but not extreme outlook")
    else:
        st.info("⚪ **Neutral Sentiment** - Mixed signals, no clear directional bias")

def show_ensemble_optimizer(symbol: str, data: pd.DataFrame):
    """Display ensemble model optimizer"""
    
    st.markdown("## 🤖 AI Ensemble Model Optimizer")
    st.markdown(f"### Optimizing prediction models for **{symbol}**")
    
    # Initialize ensemble optimizer
    ensemble_optimizer = AIEnsembleOptimizer()
    
    # Check if we have enough data
    if len(data) < 100:
        st.warning("⚠️ Insufficient data for ensemble optimization. Need at least 100 data points.")
        return
    
    # Prepare features and target
    with st.spinner("Preparing data for ensemble optimization..."):
        features, target = prepare_ml_features(data)
    
    if features is None or target is None:
        st.error("Could not prepare features for machine learning")
        return
    
    # Optimization parameters
    col1, col2 = st.columns(2)
    with col1:
        optimization_trials = st.slider("Optimization Trials", 10, 200, 50)
    with col2:
        optimize_button = st.button("🚀 Optimize Ensemble")
    
    # Load existing configuration or optimize new one
    if optimize_button or not ensemble_optimizer.load_ensemble_config():
        with st.spinner(f"Optimizing ensemble with {optimization_trials} trials..."):
            ensemble_config = ensemble_optimizer.optimize_ensemble(
                features, target, optimization_trials
            )
    else:
        ensemble_config = ensemble_optimizer.ensemble_config
    
    if ensemble_config and ensemble_config.models:
        # Display optimization results
        st.markdown("### 🎯 Optimization Results")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Models Optimized", len(ensemble_config.models))
        
        with col2:
            st.metric("Confidence Score", f"{ensemble_config.confidence_score:.1%}")
        
        with col3:
            st.metric("Last Optimized", ensemble_config.last_optimized.strftime("%Y-%m-%d"))
        
        # Model weights visualization
        st.markdown("### ⚖️ Optimized Model Weights")
        
        weights_df = pd.DataFrame(
            list(ensemble_config.weights.items()),
            columns=['Model', 'Weight']
        )
        
        fig = px.pie(
            weights_df,
            values='Weight',
            names='Model',
            title="Model Weight Distribution"
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Performance metrics
        if ensemble_config.performance:
            st.markdown("### 📊 Model Performance Metrics")
            
            perf_data = []
            for model_name, perf in ensemble_config.performance.items():
                perf_data.append({
                    'Model': model_name,
                    'R² Score': f"{perf.r2:.3f}",
                    'Accuracy': f"{perf.accuracy:.1%}",
                    'Sharpe Ratio': f"{perf.sharpe_ratio:.3f}",
                    'Win Rate': f"{perf.win_rate:.1%}",
                    'Max Drawdown': f"{perf.max_drawdown:.1%}"
                })
            
            perf_df = pd.DataFrame(perf_data)
            st.dataframe(perf_df, use_container_width=True)
        
        # Make predictions
        st.markdown("### 🔮 Ensemble Predictions")
        
        with st.spinner("Generating ensemble predictions..."):
            recent_features = features.tail(1)
            predictions = ensemble_optimizer.predict(recent_features)
        
        if predictions and 'ensemble_prediction' in predictions:
            ensemble_pred = predictions['ensemble_prediction'][0]
            current_price = data['Close'].iloc[-1]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Current Price", f"{current_price:.2f} EGP")
            
            with col2:
                st.metric("Ensemble Prediction", f"{ensemble_pred:.2f} EGP")
            
            with col3:
                price_change = (ensemble_pred - current_price) / current_price
                st.metric("Expected Change", f"{price_change:.1%}")
            
            # Individual model predictions
            if 'individual_predictions' in predictions:
                st.markdown("#### 🔍 Individual Model Predictions")
                
                individual_data = []
                for model_name, pred in predictions['individual_predictions'].items():
                    weight = ensemble_config.weights.get(model_name, 0.0)
                    individual_data.append({
                        'Model': model_name,
                        'Prediction': f"{pred[0]:.2f} EGP",
                        'Weight': f"{weight:.1%}",
                        'Contribution': f"{pred[0] * weight:.2f} EGP"
                    })
                
                individual_df = pd.DataFrame(individual_data)
                st.dataframe(individual_df, use_container_width=True)
    
    else:
        st.error("Failed to optimize ensemble models")

def prepare_ml_features(data: pd.DataFrame) -> tuple:
    """Prepare features for machine learning"""
    
    try:
        df = data.copy()
        
        # Calculate technical indicators as features
        df['SMA_5'] = df['Close'].rolling(window=5).mean()
        df['SMA_10'] = df['Close'].rolling(window=10).mean()
        df['SMA_20'] = df['Close'].rolling(window=20).mean()
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # Price change features
        df['Price_Change_1'] = df['Close'].pct_change(1)
        df['Price_Change_5'] = df['Close'].pct_change(5)
        
        # Volume features
        df['Volume_MA'] = df['Volume'].rolling(window=10).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_MA']
        
        # Volatility
        df['Volatility'] = df['Close'].rolling(window=10).std()
        
        # Select features
        feature_columns = [
            'SMA_5', 'SMA_10', 'SMA_20', 'RSI', 
            'Price_Change_1', 'Price_Change_5',
            'Volume_Ratio', 'Volatility'
        ]
        
        # Target: next day's closing price
        df['Target'] = df['Close'].shift(-1)
        
        # Remove NaN values
        df.dropna(inplace=True)
        
        if len(df) < 50:
            return None, None
        
        features = df[feature_columns]
        target = df['Target']
        
        return features, target
        
    except Exception as e:
        logger.error(f"Error preparing ML features: {str(e)}")
        return None, None

def show_risk_management(symbol: str, data: pd.DataFrame):
    """Display AI risk management"""

    st.markdown("## ⚠️ AI Risk Management System")
    st.markdown(f"### Risk analysis for **{symbol}**")

    # Initialize risk manager
    risk_manager = AIRiskManager()

    # Prepare portfolio data (simulate for single stock)
    portfolio_data = prepare_portfolio_data(symbol, data)
    market_data = {"market_index": data}  # Simplified market data

    # Perform risk assessment
    with st.spinner("Performing comprehensive risk analysis..."):
        risk_metrics = risk_manager.assess_portfolio_risk(portfolio_data, market_data)

    # Display risk overview
    st.markdown("### 🎯 Risk Overview")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        risk_color = "🟢" if risk_metrics.risk_level == "LOW" else "🟡" if risk_metrics.risk_level == "MEDIUM" else "🔴"
        st.metric("Risk Level", f"{risk_color} {risk_metrics.risk_level}")

    with col2:
        st.metric("Risk Score", f"{risk_metrics.overall_risk_score:.1f}/100")

    with col3:
        st.metric("95% VaR", f"{risk_metrics.var_95:.1%}")

    with col4:
        st.metric("Max Drawdown", f"{risk_metrics.max_drawdown:.1%}")

    # Risk metrics details
    st.markdown("### 📊 Detailed Risk Metrics")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 📈 Return & Volatility Metrics")
        st.metric("Volatility (Annualized)", f"{risk_metrics.volatility:.1%}")
        st.metric("Sharpe Ratio", f"{risk_metrics.sharpe_ratio:.3f}")
        st.metric("Market Beta", f"{risk_metrics.beta:.3f}")

    with col2:
        st.markdown("#### ⚠️ Risk Metrics")
        st.metric("99% VaR", f"{risk_metrics.var_99:.1%}")
        st.metric("Expected Shortfall", f"{risk_metrics.expected_shortfall:.1%}")
        st.metric("Correlation Risk", f"{risk_metrics.correlation_risk:.1%}")

    # Risk breakdown chart
    st.markdown("### 🔍 Risk Component Analysis")

    risk_components = {
        'VaR Risk': risk_metrics.var_95 * 100,
        'Volatility Risk': min(risk_metrics.volatility * 100, 50),
        'Drawdown Risk': risk_metrics.max_drawdown * 100,
        'Concentration Risk': risk_metrics.concentration_risk * 25,
        'Correlation Risk': risk_metrics.correlation_risk * 25,
        'Liquidity Risk': risk_metrics.liquidity_risk * 25
    }

    fig = go.Figure()

    components = list(risk_components.keys())
    values = list(risk_components.values())
    colors = ['red' if v > 15 else 'orange' if v > 10 else 'yellow' if v > 5 else 'green' for v in values]

    fig.add_trace(go.Bar(
        x=components,
        y=values,
        marker_color=colors,
        text=[f"{v:.1f}" for v in values],
        textposition='auto'
    ))

    fig.update_layout(
        title="Risk Component Breakdown",
        xaxis_title="Risk Component",
        yaxis_title="Risk Score",
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)

    # Risk alerts
    if hasattr(risk_manager, 'alert_history') and risk_manager.alert_history:
        st.markdown("### 🚨 Risk Alerts")

        for alert in risk_manager.alert_history[-5:]:  # Show last 5 alerts
            severity_color = "🔴" if alert.severity == "CRITICAL" else "🟠" if alert.severity == "HIGH" else "🟡"
            st.warning(f"{severity_color} **{alert.alert_type}**: {alert.message}")

    # Risk recommendations
    st.markdown("### 💡 AI Risk Recommendations")

    if risk_metrics.overall_risk_score > 75:
        st.error("🔴 **High Risk Detected** - Consider reducing position size or implementing hedging strategies")
    elif risk_metrics.overall_risk_score > 50:
        st.warning("🟡 **Moderate Risk** - Monitor positions closely and consider risk management measures")
    else:
        st.success("🟢 **Low Risk** - Current risk levels are within acceptable parameters")

    # Specific recommendations based on risk components
    recommendations = []

    if risk_metrics.var_95 > 0.05:
        recommendations.append("• Consider reducing position size to lower VaR")

    if risk_metrics.max_drawdown > 0.15:
        recommendations.append("• Implement tighter stop-loss levels")

    if risk_metrics.volatility > 0.40:
        recommendations.append("• High volatility detected - consider diversification")

    if risk_metrics.concentration_risk > 0.70:
        recommendations.append("• Portfolio is highly concentrated - add diversification")

    if recommendations:
        st.markdown("#### 🎯 Specific Recommendations:")
        for rec in recommendations:
            st.markdown(rec)

def show_pattern_recognition(symbol: str, data: pd.DataFrame):
    """Display AI pattern recognition"""

    st.markdown("## 📊 AI Pattern Recognition System")
    st.markdown(f"### Advanced pattern analysis for **{symbol}**")

    # Initialize pattern recognition
    pattern_recognizer = AIPatternRecognition()

    # Analysis parameters
    col1, col2 = st.columns(2)
    with col1:
        analysis_period = st.slider("Analysis Period (days)", 30, 200, 100)
    with col2:
        confidence_threshold = st.slider("Confidence Threshold", 0.5, 0.9, 0.7)

    # Get recent data for analysis
    recent_data = data.tail(analysis_period)

    # Perform pattern analysis with live data integration
    with st.spinner("Analyzing patterns using AI..."):
        pattern_results = pattern_recognizer.analyze_patterns(recent_data, symbol)

    # Display pattern summary
    summary = pattern_results.get('pattern_summary', {})

    st.markdown("### 🎯 Pattern Analysis Summary")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Patterns", summary.get('total_patterns', 0))

    with col2:
        st.metric("Bullish Patterns", summary.get('bullish_patterns', 0))

    with col3:
        st.metric("Bearish Patterns", summary.get('bearish_patterns', 0))

    with col4:
        sentiment = summary.get('overall_sentiment', 'NEUTRAL')
        sentiment_color = "🟢" if sentiment == "BULLISH" else "🔴" if sentiment == "BEARISH" else "🟡"
        st.metric("Overall Sentiment", f"{sentiment_color} {sentiment}")

    # Chart patterns
    chart_patterns = pattern_results.get('chart_patterns', [])
    if chart_patterns:
        st.markdown("### 📈 Chart Patterns Detected")

        for pattern in chart_patterns:
            if pattern.confidence >= confidence_threshold:
                with st.expander(f"{pattern.pattern_name} - {pattern.pattern_type} ({pattern.confidence:.1%} confidence)"):
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write(f"**Pattern Type:** {pattern.pattern_type}")
                        st.write(f"**Confidence:** {pattern.confidence:.1%}")
                        st.write(f"**Strength:** {pattern.strength:.1%}")
                        st.write(f"**Entry Price:** {pattern.entry_price:.2f} EGP")

                    with col2:
                        st.write(f"**Target Price:** {pattern.target_price:.2f} EGP")
                        st.write(f"**Stop Loss:** {pattern.stop_loss:.2f} EGP")
                        st.write(f"**Risk/Reward:** {pattern.risk_reward_ratio:.2f}")
                        st.write(f"**Volume Confirmation:** {'✅' if pattern.volume_confirmation else '❌'}")

                    st.write(f"**Description:** {pattern.pattern_description}")

    # Candlestick patterns
    candlestick_patterns = pattern_results.get('candlestick_patterns', [])
    if candlestick_patterns:
        st.markdown("### 🕯️ Candlestick Patterns")

        high_confidence_patterns = [p for p in candlestick_patterns if p.confidence >= confidence_threshold]

        if high_confidence_patterns:
            pattern_data = []
            for pattern in high_confidence_patterns:
                pattern_data.append({
                    'Pattern': pattern.pattern_name,
                    'Type': pattern.pattern_type,
                    'Confidence': f"{pattern.confidence:.1%}",
                    'Reliability': pattern.reliability,
                    'Description': pattern.description
                })

            pattern_df = pd.DataFrame(pattern_data)
            st.dataframe(pattern_df, use_container_width=True)

    # Support and Resistance levels
    support_resistance = pattern_results.get('support_resistance', [])
    if support_resistance:
        st.markdown("### 🎯 Key Support & Resistance Levels")

        current_price = data['Close'].iloc[-1]

        level_data = []
        for level in support_resistance[:8]:  # Top 8 levels
            distance = abs(current_price - level['level']) / current_price
            level_data.append({
                'Type': level['type'].title(),
                'Level': f"{level['level']:.2f} EGP",
                'Strength': f"{level['strength']:.1%}",
                'Distance': f"{distance:.1%}",
                'Touches': level['touches']
            })

        level_df = pd.DataFrame(level_data)
        st.dataframe(level_df, use_container_width=True)

    # Fibonacci levels
    fibonacci_levels = pattern_results.get('fibonacci_levels', [])
    if fibonacci_levels:
        st.markdown("### 📐 Fibonacci Retracement Levels")

        fib_data = []
        for fib in fibonacci_levels:
            fib_data.append({
                'Ratio': f"{fib['ratio']:.1%}",
                'Level': f"{fib['level']:.2f} EGP",
                'Type': fib['type'].title()
            })

        fib_df = pd.DataFrame(fib_data)
        st.dataframe(fib_df, use_container_width=True)

    # Pattern alerts
    pattern_alerts = pattern_recognizer.get_pattern_alerts(recent_data, confidence_threshold)

    if pattern_alerts:
        st.markdown("### 🚨 Pattern Alerts")

        for alert in pattern_alerts:
            alert_color = "🟢" if alert['signal'] == 'BULLISH' else "🔴" if alert['signal'] == 'BEARISH' else "🟡"
            st.info(f"{alert_color} **{alert['pattern']}**: {alert['message']} (Confidence: {alert['confidence']:.1%})")

def prepare_portfolio_data(symbol: str, data: pd.DataFrame) -> Dict:
    """Prepare portfolio data for risk analysis"""

    try:
        # Calculate returns
        returns = data['Close'].pct_change().dropna().tolist()

        # Simulate portfolio data for single stock
        current_price = data['Close'].iloc[-1]
        portfolio_value = 100000  # Assume 100k portfolio
        position_value = portfolio_value  # Single stock portfolio

        portfolio_data = {
            'positions': {
                symbol: {
                    'value': position_value,
                    'weight': 1.0,
                    'shares': position_value / current_price,
                    'entry_price': current_price * 0.95,  # Simulate entry price
                    'current_price': current_price
                }
            },
            'returns': returns,
            'total_value': portfolio_value
        }

        return portfolio_data

    except Exception as e:
        logger.error(f"Error preparing portfolio data: {str(e)}")
        return {
            'positions': {},
            'returns': [],
            'total_value': 0
        }

def show_integrated_dashboard(symbol: str, data: pd.DataFrame):
    """Display integrated AI dashboard combining all features"""

    st.markdown("## 📈 Integrated AI Trading Dashboard")
    st.markdown(f"### Comprehensive AI analysis for **{symbol}**")

    # Initialize all AI systems
    sentiment_analyzer = AIMarketSentimentAnalyzer()
    risk_manager = AIRiskManager()
    pattern_recognizer = AIPatternRecognition()

    # Quick analysis parameters
    col1, col2 = st.columns(2)
    with col1:
        analysis_timeframe = st.selectbox("Analysis Timeframe", [7, 14, 30], index=1)
    with col2:
        refresh_dashboard = st.button("🔄 Refresh Dashboard")

    # Perform all analyses
    with st.spinner("Running comprehensive AI analysis..."):
        # Sentiment analysis
        sentiment_score = sentiment_analyzer.analyze_stock_sentiment(symbol, analysis_timeframe)

        # Risk analysis
        portfolio_data = prepare_portfolio_data(symbol, data)
        market_data = {"market_index": data}
        risk_metrics = risk_manager.assess_portfolio_risk(portfolio_data, market_data)

        # Pattern analysis with live data integration
        recent_data = data.tail(100)
        pattern_results = pattern_recognizer.analyze_patterns(recent_data, symbol)

    # AI Summary Dashboard
    st.markdown("### 🎯 AI Analysis Summary")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        sentiment_color = "🟢" if sentiment_score.overall_sentiment > 0.1 else "🔴" if sentiment_score.overall_sentiment < -0.1 else "🟡"
        st.metric(
            "AI Sentiment",
            f"{sentiment_color} {sentiment_score.overall_sentiment:.3f}",
            delta=f"Confidence: {sentiment_score.confidence:.1%}"
        )

    with col2:
        risk_color = "🟢" if risk_metrics.risk_level == "LOW" else "🟡" if risk_metrics.risk_level == "MEDIUM" else "🔴"
        st.metric(
            "Risk Level",
            f"{risk_color} {risk_metrics.risk_level}",
            delta=f"Score: {risk_metrics.overall_risk_score:.0f}/100"
        )

    with col3:
        pattern_summary = pattern_results.get('pattern_summary', {})
        pattern_sentiment = pattern_summary.get('overall_sentiment', 'NEUTRAL')
        pattern_color = "🟢" if pattern_sentiment == "BULLISH" else "🔴" if pattern_sentiment == "BEARISH" else "🟡"
        st.metric(
            "Pattern Signals",
            f"{pattern_color} {pattern_sentiment}",
            delta=f"Patterns: {pattern_summary.get('total_patterns', 0)}"
        )

    with col4:
        # Combined AI Score
        ai_score = calculate_combined_ai_score(sentiment_score, risk_metrics, pattern_summary)
        score_color = "🟢" if ai_score > 70 else "🟡" if ai_score > 40 else "🔴"
        st.metric(
            "AI Score",
            f"{score_color} {ai_score:.0f}/100",
            delta="Combined Analysis"
        )

    # AI Recommendation Engine
    st.markdown("### 🤖 AI Trading Recommendation")

    recommendation = generate_ai_recommendation(sentiment_score, risk_metrics, pattern_summary, data)

    rec_color = "🟢" if "BUY" in recommendation['action'] else "🔴" if "SELL" in recommendation['action'] else "🟡"

    st.markdown(f"""
    <div style="background-color: {'#e8f5e8' if 'BUY' in recommendation['action'] else '#ffe8e8' if 'SELL' in recommendation['action'] else '#fff3cd'};
                padding: 20px; border-radius: 10px; margin-bottom: 20px;">
        <h3 style="margin: 0; color: #333;">{rec_color} AI Recommendation: {recommendation['action']}</h3>
        <p style="margin: 10px 0; color: #666;"><strong>Confidence:</strong> {recommendation['confidence']:.1%}</p>
        <p style="margin: 10px 0; color: #666;"><strong>Reasoning:</strong> {recommendation['reasoning']}</p>
    </div>
    """, unsafe_allow_html=True)

    # Key Insights
    st.markdown("### 💡 Key AI Insights")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 🔮 Sentiment Insights")
        for theme in sentiment_score.key_themes[:3]:
            st.markdown(f"• {theme}")

        st.markdown("#### ⚠️ Risk Insights")
        if risk_metrics.overall_risk_score > 60:
            st.markdown("• High risk environment detected")
        if risk_metrics.var_95 > 0.05:
            st.markdown("• Elevated Value-at-Risk levels")
        if risk_metrics.volatility > 0.30:
            st.markdown("• High volatility conditions")

    with col2:
        st.markdown("#### 📊 Pattern Insights")
        strongest_signals = pattern_summary.get('strongest_signals', [])
        for signal in strongest_signals[:3]:
            st.markdown(f"• {signal['pattern']}: {signal['type']} ({signal['confidence']:.1%})")

        st.markdown("#### 🎯 Key Levels")
        key_levels = pattern_summary.get('key_levels', [])
        for level in key_levels[:3]:
            st.markdown(f"• {level['type'].title()}: {level['level']:.2f} EGP")

    # AI Performance Tracking
    st.markdown("### 📊 AI Analysis Performance")

    # Create a comprehensive analysis chart
    fig = go.Figure()

    # Add price data
    fig.add_trace(go.Scatter(
        x=data.index[-50:],
        y=data['Close'].tail(50),
        mode='lines',
        name='Price',
        line=dict(color='blue', width=2)
    ))

    # Add sentiment overlay (scaled)
    sentiment_values = [sentiment_score.overall_sentiment] * len(data.tail(50))
    sentiment_scaled = data['Close'].tail(50).mean() * (1 + np.array(sentiment_values) * 0.1)

    fig.add_trace(go.Scatter(
        x=data.index[-50:],
        y=sentiment_scaled,
        mode='lines',
        name='AI Sentiment (scaled)',
        line=dict(color='green', width=1, dash='dash'),
        opacity=0.7
    ))

    fig.update_layout(
        title="Price vs AI Sentiment Analysis",
        xaxis_title="Date",
        yaxis_title="Price (EGP)",
        height=400,
        showlegend=True
    )

    st.plotly_chart(fig, use_container_width=True)

    # Action Items
    st.markdown("### ✅ AI-Generated Action Items")

    action_items = generate_action_items(sentiment_score, risk_metrics, pattern_summary, recommendation)

    for i, item in enumerate(action_items, 1):
        st.markdown(f"{i}. {item}")

def calculate_combined_ai_score(sentiment_score, risk_metrics, pattern_summary) -> float:
    """Calculate combined AI score from all analyses"""

    try:
        # Sentiment component (0-40 points)
        sentiment_component = (sentiment_score.overall_sentiment + 1) * 20  # -1 to 1 -> 0 to 40
        sentiment_component *= sentiment_score.confidence  # Weight by confidence

        # Risk component (0-30 points, inverted so lower risk = higher score)
        risk_component = max(0, 30 - (risk_metrics.overall_risk_score * 0.3))

        # Pattern component (0-30 points)
        pattern_bullish = pattern_summary.get('bullish_patterns', 0)
        pattern_bearish = pattern_summary.get('bearish_patterns', 0)
        pattern_total = pattern_summary.get('total_patterns', 1)

        pattern_ratio = (pattern_bullish - pattern_bearish) / max(pattern_total, 1)
        pattern_component = (pattern_ratio + 1) * 15  # -1 to 1 -> 0 to 30

        # Combine components
        total_score = sentiment_component + risk_component + pattern_component

        return min(100, max(0, total_score))

    except Exception as e:
        logger.error(f"Error calculating combined AI score: {str(e)}")
        return 50.0

def generate_ai_recommendation(sentiment_score, risk_metrics, pattern_summary, data) -> Dict:
    """Generate AI trading recommendation"""

    try:
        # Analyze components
        sentiment_bullish = sentiment_score.overall_sentiment > 0.1
        sentiment_bearish = sentiment_score.overall_sentiment < -0.1

        risk_acceptable = risk_metrics.overall_risk_score < 60

        pattern_bullish = pattern_summary.get('bullish_patterns', 0) > pattern_summary.get('bearish_patterns', 0)

        # Decision logic
        bullish_signals = sum([sentiment_bullish, risk_acceptable, pattern_bullish])
        bearish_signals = sum([sentiment_bearish, not risk_acceptable, not pattern_bullish])

        if bullish_signals >= 2:
            action = "BUY"
            confidence = min(0.9, (bullish_signals / 3) * sentiment_score.confidence)
            reasoning = f"AI analysis shows {bullish_signals}/3 bullish indicators with acceptable risk levels"
        elif bearish_signals >= 2:
            action = "SELL"
            confidence = min(0.9, (bearish_signals / 3) * sentiment_score.confidence)
            reasoning = f"AI analysis shows {bearish_signals}/3 bearish indicators or elevated risk"
        else:
            action = "HOLD"
            confidence = 0.6
            reasoning = "Mixed signals from AI analysis suggest maintaining current position"

        return {
            'action': action,
            'confidence': confidence,
            'reasoning': reasoning
        }

    except Exception as e:
        logger.error(f"Error generating AI recommendation: {str(e)}")
        return {
            'action': 'HOLD',
            'confidence': 0.5,
            'reasoning': 'Unable to generate recommendation due to analysis error'
        }

def generate_action_items(sentiment_score, risk_metrics, pattern_summary, recommendation) -> List[str]:
    """Generate actionable items based on AI analysis"""

    action_items = []

    try:
        # Based on recommendation
        if recommendation['action'] == 'BUY':
            action_items.append(f"Consider initiating/increasing position with {recommendation['confidence']:.1%} confidence")
        elif recommendation['action'] == 'SELL':
            action_items.append(f"Consider reducing/closing position with {recommendation['confidence']:.1%} confidence")
        else:
            action_items.append("Monitor current position and wait for clearer signals")

        # Risk-based actions
        if risk_metrics.overall_risk_score > 70:
            action_items.append("Implement additional risk management measures due to high risk score")

        if risk_metrics.var_95 > 0.08:
            action_items.append("Consider reducing position size due to elevated VaR")

        # Sentiment-based actions
        if abs(sentiment_score.overall_sentiment) > 0.5:
            action_items.append(f"Strong sentiment detected - monitor for potential reversals")

        # Pattern-based actions
        strongest_signals = pattern_summary.get('strongest_signals', [])
        if strongest_signals:
            top_signal = strongest_signals[0]
            action_items.append(f"Watch for {top_signal['pattern']} pattern completion")

        # Key levels
        key_levels = pattern_summary.get('key_levels', [])
        if key_levels:
            action_items.append(f"Monitor key {key_levels[0]['type']} level at {key_levels[0]['level']:.2f} EGP")

        return action_items[:5]  # Limit to 5 items

    except Exception as e:
        logger.error(f"Error generating action items: {str(e)}")
        return ["Monitor market conditions and review analysis regularly"]

def show_ai_comprehensive_analysis(symbol: str, data: pd.DataFrame):
    """AI Comprehensive Analysis - Institutional-grade analysis using all AI tools"""
    
    st.markdown("## 🧠 AI Comprehensive Analysis")
    st.markdown(f"### Professional AI-powered analysis for **{symbol}**")
    st.markdown("*Combining sentiment analysis, ML predictions, risk management, and pattern recognition*")
    
    # Analysis parameters
    st.markdown("### ⚙️ Analysis Configuration")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        analysis_period = st.selectbox(
            "Analysis Period", 
            ["1 Month", "3 Months", "6 Months", "1 Year"],
            index=2
        )
        period_days = {"1 Month": 30, "3 Months": 90, "6 Months": 180, "1 Year": 365}[analysis_period]
    
    with col2:
        confidence_level = st.selectbox(
            "Confidence Level",
            ["Standard (70%)", "High (80%)", "Very High (90%)"],
            index=1
        )
        confidence_threshold = {"Standard (70%)": 0.70, "High (80%)": 0.80, "Very High (90%)": 0.90}[confidence_level]
    
    with col3:
        refresh_analysis = st.button("🔄 Refresh Complete Analysis", type="primary")
    
    # Initialize all AI systems
    with st.spinner("Initializing AI analysis engines..."):
        sentiment_analyzer = AIMarketSentimentAnalyzer()
        ensemble_optimizer = AIEnsembleOptimizer()
        risk_manager = AIRiskManager()
        pattern_recognizer = AIPatternRecognition()
    
    # Prepare data for comprehensive analysis
    analysis_data = data.tail(period_days) if len(data) > period_days else data
    
    if len(analysis_data) < 50:
        st.error("⚠️ Insufficient data for comprehensive analysis. Need at least 50 data points.")
        return
    
    # Perform comprehensive AI analysis
    with st.spinner("🧠 Running comprehensive AI analysis..."):
        
        # 1. AI Sentiment Analysis
        sentiment_timeframe = min(30, len(analysis_data))
        sentiment_score = sentiment_analyzer.analyze_stock_sentiment(symbol, sentiment_timeframe)
        
        # 2. ML Ensemble Analysis
        features, target = prepare_ml_features(analysis_data)
        ensemble_results = None
        if features is not None and target is not None and len(features) >= 50:
            if not ensemble_optimizer.load_ensemble_config():
                ensemble_optimizer.optimize_ensemble(features, target, optimization_trials=50)
            ensemble_results = ensemble_optimizer.predict(features.tail(1))
        
        # 3. Risk Assessment
        portfolio_data = prepare_portfolio_data(symbol, analysis_data)
        market_data = {"market_index": analysis_data}
        risk_metrics = risk_manager.assess_portfolio_risk(portfolio_data, market_data)
        
        # 4. Pattern Recognition
        pattern_results = pattern_recognizer.analyze_patterns(analysis_data, symbol)
    
    # === EXECUTIVE AI SUMMARY ===
    st.markdown("### 🎯 Executive AI Summary")
    
    # Calculate overall AI scores
    ai_sentiment_score = (sentiment_score.overall_sentiment + 1) * 50  # Convert to 0-100
    ai_risk_score = max(0, 100 - risk_metrics.overall_risk_score)  # Invert risk score
    pattern_summary = pattern_results.get('pattern_summary', {})
    ai_pattern_score = calculate_pattern_score(pattern_summary)
    
    # ML Prediction Score
    ai_ml_score = 50  # Default neutral
    price_prediction = None
    if ensemble_results and 'ensemble_prediction' in ensemble_results:
        current_price = analysis_data['Close'].iloc[-1]
        predicted_price = ensemble_results['ensemble_prediction'][0]
        price_change = (predicted_price - current_price) / current_price
        ai_ml_score = 50 + (price_change * 100)  # Convert to score
        ai_ml_score = max(0, min(100, ai_ml_score))
        price_prediction = predicted_price
    
    # Combined AI Score
    overall_ai_score = (ai_sentiment_score * 0.25 + ai_ml_score * 0.30 + 
                       ai_risk_score * 0.25 + ai_pattern_score * 0.20)
    
    # Display executive metrics
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        sentiment_color = "🟢" if ai_sentiment_score > 60 else "🔴" if ai_sentiment_score < 40 else "🟡"
        st.metric(
            "AI Sentiment",
            f"{sentiment_color} {ai_sentiment_score:.0f}/100",
            delta=f"Confidence: {sentiment_score.confidence:.1%}"
        )
    
    with col2:
        ml_color = "🟢" if ai_ml_score > 60 else "🔴" if ai_ml_score < 40 else "🟡"
        ml_delta = f"+{((price_prediction/analysis_data['Close'].iloc[-1] - 1) * 100):.1f}%" if price_prediction else "N/A"
        st.metric(
            "ML Prediction",
            f"{ml_color} {ai_ml_score:.0f}/100",
            delta=ml_delta if price_prediction else "Insufficient Data"
        )
    
    with col3:
        risk_color = "🟢" if ai_risk_score > 60 else "🔴" if ai_risk_score < 40 else "🟡"
        st.metric(
            "Risk Score",
            f"{risk_color} {ai_risk_score:.0f}/100",
            delta=f"VaR: {risk_metrics.var_95:.1%}"
        )
    
    with col4:
        pattern_color = "🟢" if ai_pattern_score > 60 else "🔴" if ai_pattern_score < 40 else "🟡"
        total_patterns = pattern_summary.get('total_patterns', 0)
        st.metric(
            "Pattern Analysis",
            f"{pattern_color} {ai_pattern_score:.0f}/100",
            delta=f"Patterns: {total_patterns}"
        )
    
    with col5:
        overall_color = "🟢" if overall_ai_score > 70 else "🟡" if overall_ai_score > 40 else "🔴"
        st.metric(
            "Overall AI Score",
            f"{overall_color} {overall_ai_score:.0f}/100",
            delta="Combined Analysis"
        )
    
    # === AI RECOMMENDATION ENGINE ===
    st.markdown("### 🤖 AI Trading Recommendation")
    
    # Generate comprehensive recommendation
    recommendation = generate_comprehensive_ai_recommendation(
        sentiment_score, ensemble_results, risk_metrics, pattern_summary, analysis_data
    )
    
    # Display recommendation with enhanced styling
    rec_color = "success" if "BUY" in recommendation['action'] else "error" if "SELL" in recommendation['action'] else "warning"
    rec_emoji = "🟢" if "BUY" in recommendation['action'] else "🔴" if "SELL" in recommendation['action'] else "🟡"
    
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, {'#e8f5e8' if 'BUY' in recommendation['action'] else '#ffe8e8' if 'SELL' in recommendation['action'] else '#fff3cd'}, {'#d4edda' if 'BUY' in recommendation['action'] else '#f8d7da' if 'SELL' in recommendation['action'] else '#ffeaa7'});
                padding: 25px; border-radius: 15px; margin: 20px 0; border-left: 5px solid {'#28a745' if 'BUY' in recommendation['action'] else '#dc3545' if 'SELL' in recommendation['action'] else '#ffc107'};">
        <h2 style="margin: 0; color: #333; font-weight: bold;">{rec_emoji} AI Recommendation: {recommendation['action']}</h2>
        <p style="margin: 15px 0 10px 0; color: #666; font-size: 16px;"><strong>Confidence Level:</strong> {recommendation['confidence']:.1%}</p>
        <p style="margin: 10px 0; color: #666; font-size: 14px;"><strong>AI Reasoning:</strong> {recommendation['reasoning']}</p>
        <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;"><strong>Risk Assessment:</strong> {recommendation.get('risk_level', 'Moderate')}</p>
    </div>
    """, unsafe_allow_html=True)
    
    # === DETAILED AI ANALYSIS ===
    st.markdown("### 📊 Detailed AI Analysis")
    
    # Create tabs for detailed analysis
    tab1, tab2, tab3, tab4 = st.tabs([
        "🔮 AI Sentiment & Psychology", 
        "🤖 ML Predictions & Forecasting", 
        "⚠️ Risk Management", 
        "📊 Pattern Recognition"
    ])
    
    with tab1:
        show_comprehensive_sentiment_analysis(sentiment_score, symbol, analysis_data)
    
    with tab2:
        show_comprehensive_ml_analysis(ensemble_results, features, target, analysis_data, symbol)
    
    with tab3:
        show_comprehensive_risk_analysis(risk_metrics, analysis_data, symbol)
    
    with tab4:
        show_comprehensive_pattern_analysis(pattern_results, analysis_data, symbol)
    
    # === AI TRADING SCENARIO & ACTION PLAN ===
    st.markdown("### 🎯 AI Trading Scenario & Strategic Action Plan")
    
    trading_scenario = generate_ai_trading_scenario(
        recommendation, sentiment_score, risk_metrics, pattern_summary, 
        analysis_data, price_prediction
    )
    
    # Entry Strategy
    st.markdown("#### 📈 Entry Strategy")
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown(f"""
        **Recommended Action:** {trading_scenario['entry']['action']}  
        **Optimal Entry:** {trading_scenario['entry']['price']:.2f} EGP  
        **Position Size:** {trading_scenario['entry']['position_size']}  
        **Entry Timing:** {trading_scenario['entry']['timing']}
        """)
    
    with col2:
        st.markdown(f"""
        **Stop Loss:** {trading_scenario['risk']['stop_loss']:.2f} EGP  
        **Take Profit:** {trading_scenario['risk']['take_profit']:.2f} EGP  
        **Risk/Reward:** {trading_scenario['risk']['risk_reward']:.2f}  
        **Max Risk:** {trading_scenario['risk']['max_risk']}
        """)
    
    # Debug information
    if 'debug_info' in trading_scenario:
        with st.expander("🔍 Debug Information"):
            debug = trading_scenario['debug_info']
            st.write(f"**Current Price:** {debug['current_price']:.2f} EGP")
            st.write(f"**Volatility Factor:** {debug['volatility_factor']:.3f}")
            st.write(f"**Action Detected:** '{debug['action_detected']}'")
            st.write(f"**Logic Used:** {debug['logic_used']}")
            
            # Show additional debug info
            if 'full_recommendation' in debug:
                st.write(f"**Full Recommendation Object:** {debug['full_recommendation']}")
            if 'action_check_buy' in debug:
                st.write(f"**'BUY' in action check:** {debug['action_check_buy']}")
            if 'action_check_sell' in debug:
                st.write(f"**'SELL' in action check:** {debug['action_check_sell']}")
            
            # Force display the action analysis
            st.write("**String Analysis:**")
            st.write(f"- Action string: '{debug['action_detected']}'")
            st.write(f"- Action string upper: '{debug['action_detected'].upper()}'")
            st.write(f"- Contains 'BUY': {'BUY' in debug['action_detected'].upper()}")
            st.write(f"- Contains 'SELL': {'SELL' in debug['action_detected'].upper()}")
            st.write(f"- Contains 'HOLD': {'HOLD' in debug['action_detected'].upper()}")
    
    # Professional Action Items
    st.markdown("#### ✅ AI-Generated Action Items")
    
    action_items = trading_scenario['action_items']
    for i, item in enumerate(action_items, 1):
        priority = "🔴" if i <= 2 else "🟡" if i <= 4 else "🟢"
        st.markdown(f"{priority} **{i}.** {item}")
    
    # === AI PERFORMANCE DASHBOARD ===
    st.markdown("### 📈 AI Analysis Performance Dashboard")
    
    # Create comprehensive visualization
    fig = create_ai_comprehensive_chart(
        analysis_data, sentiment_score, risk_metrics, 
        pattern_results, price_prediction
    )
    
    st.plotly_chart(fig, use_container_width=True)
    
    # === CONFIDENCE & RELIABILITY METRICS ===
    st.markdown("### 🎯 AI Confidence & Reliability Metrics")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("#### 📊 Analysis Reliability")
        reliability_score = calculate_analysis_reliability(
            sentiment_score, ensemble_results, risk_metrics, pattern_summary
        )
        reliability_color = "🟢" if reliability_score > 0.8 else "🟡" if reliability_score > 0.6 else "🔴"
        st.metric("Overall Reliability", f"{reliability_color} {reliability_score:.1%}")
        
        st.markdown("#### 🔍 Data Quality")
        data_quality = calculate_data_quality(analysis_data)
        quality_color = "🟢" if data_quality > 0.9 else "🟡" if data_quality > 0.7 else "🔴"
        st.metric("Data Quality Score", f"{quality_color} {data_quality:.1%}")
    
    with col2:
        st.markdown("#### ⚡ AI Model Performance")
        if ensemble_results:
            model_performance = ensemble_results.get('model_confidence', 0.75)
            perf_color = "🟢" if model_performance > 0.8 else "🟡" if model_performance > 0.6 else "🔴"
            st.metric("Model Confidence", f"{perf_color} {model_performance:.1%}")
        else:
            st.metric("Model Confidence", "⚪ N/A")
        
        st.markdown("#### 🎯 Signal Strength")
        signal_strength = calculate_signal_strength(sentiment_score, pattern_summary)
        signal_color = "🟢" if signal_strength > 0.7 else "🟡" if signal_strength > 0.5 else "🔴"
        st.metric("Signal Strength", f"{signal_color} {signal_strength:.1%}")
    
    with col3:
        st.markdown("#### ⏰ Analysis Freshness")
        st.metric("Last Updated", "Real-time")
        st.metric("Data Coverage", f"{len(analysis_data)} days")
        
        st.markdown("#### 🔄 Update Frequency")
        st.metric("Refresh Rate", "On-demand")
    
    # === DISCLAIMERS & RISK WARNINGS ===
    st.markdown("### ⚠️ Important Disclaimers")
    
    st.warning("""
    **Professional Risk Warning:**
    - AI analysis is for informational purposes only and should not be considered as financial advice
    - Past performance does not guarantee future results
    - All trading involves risk of loss - never invest more than you can afford to lose
    - Consider consulting with a qualified financial advisor before making investment decisions
    - AI models are based on historical data and may not predict future market conditions accurately
    """)


def calculate_pattern_score(pattern_summary: Dict) -> float:
    """Calculate pattern analysis score from pattern summary"""
    try:
        bullish_patterns = pattern_summary.get('bullish_patterns', 0)
        bearish_patterns = pattern_summary.get('bearish_patterns', 0)
        total_patterns = pattern_summary.get('total_patterns', 1)
        
        if total_patterns == 0:
            return 50.0
        
        pattern_ratio = (bullish_patterns - bearish_patterns) / total_patterns
        score = 50 + (pattern_ratio * 50)  # Convert to 0-100 scale
        
        return max(0, min(100, score))
    except:
        return 50.0


def generate_comprehensive_ai_recommendation(sentiment_score, ensemble_results, risk_metrics, pattern_summary, data) -> Dict:
    """Generate comprehensive AI trading recommendation"""
    
    try:
        # Analyze all components
        sentiment_bullish = sentiment_score.overall_sentiment > 0.15
        sentiment_bearish = sentiment_score.overall_sentiment < -0.15
        sentiment_confidence = sentiment_score.confidence
        
        # ML prediction analysis
        ml_bullish = False
        ml_bearish = False
        ml_confidence = 0.5
        
        if ensemble_results and 'ensemble_prediction' in ensemble_results:
            current_price = data['Close'].iloc[-1]
            predicted_price = ensemble_results['ensemble_prediction'][0]
            price_change = (predicted_price - current_price) / current_price
            
            ml_bullish = price_change > 0.02  # 2% increase threshold
            ml_bearish = price_change < -0.02  # 2% decrease threshold
            ml_confidence = ensemble_results.get('model_confidence', 0.75)
        
        # Risk analysis
        risk_acceptable = risk_metrics.overall_risk_score < 60
        risk_elevated = risk_metrics.overall_risk_score > 75
        
        # Pattern analysis
        pattern_bullish = pattern_summary.get('bullish_patterns', 0) > pattern_summary.get('bearish_patterns', 0)
        pattern_bearish = pattern_summary.get('bearish_patterns', 0) > pattern_summary.get('bullish_patterns', 0)
        
        # Advanced decision logic with weighted scoring
        bullish_score = 0
        bearish_score = 0
        
        # Weight factors based on reliability
        sentiment_weight = sentiment_confidence
        ml_weight = ml_confidence
        risk_weight = 0.8
        pattern_weight = 0.7
        
        if sentiment_bullish:
            bullish_score += 25 * sentiment_weight
        elif sentiment_bearish:
            bearish_score += 25 * sentiment_weight
            
        if ml_bullish:
            bullish_score += 30 * ml_weight
        elif ml_bearish:
            bearish_score += 30 * ml_weight
            
        if risk_acceptable:
            bullish_score += 20 * risk_weight
        elif risk_elevated:
            bearish_score += 25 * risk_weight
            
        if pattern_bullish:
            bullish_score += 25 * pattern_weight
        elif pattern_bearish:
            bearish_score += 25 * pattern_weight
        
        # Determine recommendation with more sensitive thresholds
        score_difference = abs(bullish_score - bearish_score)
        overall_confidence = min(0.95, max(0.5, score_difference / 50))  # More sensitive confidence
        
        # Lower thresholds for more responsive recommendations
        if bullish_score > bearish_score and score_difference > 5:  # Reduced from 15 to 5
            action = "STRONG BUY" if score_difference > 20 else "BUY"  # Reduced from 40 to 20
            reasoning = f"AI consensus shows bullish signals across multiple models (Score: {bullish_score:.1f} vs {bearish_score:.1f})"
            risk_level = "Low" if risk_acceptable else "Moderate"
        elif bearish_score > bullish_score and score_difference > 5:  # Reduced from 15 to 5
            action = "STRONG SELL" if score_difference > 20 else "SELL"  # Reduced from 40 to 20
            reasoning = f"AI consensus shows bearish signals across multiple models (Score: {bearish_score:.1f} vs {bullish_score:.1f})"
            risk_level = "High" if risk_elevated else "Moderate"
        else:
            action = "HOLD"
            reasoning = f"Mixed AI signals suggest maintaining current position (Bullish: {bullish_score:.1f}, Bearish: {bearish_score:.1f})"
            risk_level = "Moderate"
        
        return {
            'action': action,
            'confidence': overall_confidence,
            'reasoning': reasoning,
            'risk_level': risk_level,
            'bullish_score': bullish_score,
            'bearish_score': bearish_score
        }
        
    except Exception as e:
        logger.error(f"Error generating comprehensive AI recommendation: {str(e)}")
        return {
            'action': 'HOLD',
            'confidence': 0.5,
            'reasoning': 'Unable to generate recommendation due to analysis error',
            'risk_level': 'Unknown',
            'bullish_score': 0,
            'bearish_score': 0
        }


def show_comprehensive_sentiment_analysis(sentiment_score, symbol: str, data: pd.DataFrame):
    """Show detailed sentiment analysis in comprehensive view"""
    
    st.markdown(f"#### 🔮 AI Market Sentiment Analysis for {symbol}")
    
    # Sentiment metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Overall Sentiment", f"{sentiment_score.overall_sentiment:.3f}")
        st.metric("Confidence Level", f"{sentiment_score.confidence:.1%}")
    
    with col2:
        st.metric("Bullish Signals", sentiment_score.bullish_signals)
        st.metric("Bearish Signals", sentiment_score.bearish_signals)
    
    with col3:
        sentiment_trend = "Improving" if sentiment_score.overall_sentiment > 0 else "Declining"
        st.metric("Sentiment Trend", sentiment_trend)
        sentiment_strength = "Strong" if abs(sentiment_score.overall_sentiment) > 0.3 else "Moderate" if abs(sentiment_score.overall_sentiment) > 0.1 else "Weak"
        st.metric("Signal Strength", sentiment_strength)
    
    # Key insights
    if hasattr(sentiment_score, 'key_themes') and sentiment_score.key_themes:
        st.markdown("**Key Market Themes:**")
        for theme in sentiment_score.key_themes[:5]:
            st.markdown(f"• {theme}")


def show_comprehensive_ml_analysis(ensemble_results, features, target, data: pd.DataFrame, symbol: str):
    """Show detailed ML analysis in comprehensive view"""
    
    st.markdown(f"#### 🤖 Machine Learning Predictions for {symbol}")
    
    if ensemble_results and 'ensemble_prediction' in ensemble_results:
        current_price = data['Close'].iloc[-1]
        predicted_price = ensemble_results['ensemble_prediction'][0]
        price_change = (predicted_price - current_price) / current_price
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Current Price", f"{current_price:.2f} EGP")
            st.metric("Predicted Price", f"{predicted_price:.2f} EGP")
        
        with col2:
            change_color = "normal" if abs(price_change) < 0.02 else "inverse"
            st.metric("Expected Change", f"{price_change:.1%}", delta=f"{price_change:.1%}")
            
            prediction_timeframe = "Next Trading Day"
            st.metric("Prediction Horizon", prediction_timeframe)
        
        with col3:
            model_confidence = ensemble_results.get('model_confidence', 0.75)
            st.metric("Model Confidence", f"{model_confidence:.1%}")
            
            signal_strength = "Strong" if abs(price_change) > 0.05 else "Moderate" if abs(price_change) > 0.02 else "Weak"
            st.metric("Signal Strength", signal_strength)
        
        # Individual model breakdown
        if 'individual_predictions' in ensemble_results:
            st.markdown("**Model Consensus:**")
            individual_preds = ensemble_results['individual_predictions']
            
            for model_name, pred in list(individual_preds.items())[:3]:
                model_change = (pred[0] - current_price) / current_price
                st.markdown(f"• **{model_name}**: {pred[0]:.2f} EGP ({model_change:+.1%})")
    
    else:
        st.warning("ML models require optimization. Insufficient data or model not trained.")
        if features is not None:
            st.info(f"Available features: {len(features)} data points")
        else:
            st.error("Could not prepare ML features from data")


def show_comprehensive_risk_analysis(risk_metrics, data: pd.DataFrame, symbol: str):
    """Show detailed risk analysis in comprehensive view"""
    
    st.markdown(f"#### ⚠️ Risk Management Analysis for {symbol}")
    
    # Risk overview
    col1, col2, col3 = st.columns(3)
    
    with col1:
        risk_level = risk_metrics.risk_level
        risk_color = "🟢" if risk_level == "LOW" else "🟡" if risk_level == "MEDIUM" else "🔴"
        st.metric("Risk Level", f"{risk_color} {risk_level}")
        st.metric("Risk Score", f"{risk_metrics.overall_risk_score:.0f}/100")
    
    with col2:
        st.metric("Value at Risk (95%)", f"{risk_metrics.var_95:.1%}")
        st.metric("Max Drawdown", f"{risk_metrics.max_drawdown:.1%}")
    
    with col3:
        st.metric("Volatility", f"{risk_metrics.volatility:.1%}")
        st.metric("Sharpe Ratio", f"{risk_metrics.sharpe_ratio:.2f}")
    
    # Risk recommendations
    st.markdown("**Risk Management Recommendations:**")
    
    recommendations = []
    if risk_metrics.overall_risk_score > 70:
        recommendations.append("🔴 High risk detected - Consider reducing position size")
    if risk_metrics.var_95 > 0.05:
        recommendations.append("🟠 Elevated VaR - Implement strict stop-losses")
    if risk_metrics.volatility > 0.30:
        recommendations.append("🟡 High volatility - Use smaller position sizes")
    if risk_metrics.max_drawdown > 0.15:
        recommendations.append("🟡 Significant drawdown risk - Monitor closely")
    
    if not recommendations:
        recommendations.append("🟢 Risk levels are within acceptable parameters")
    
    for rec in recommendations:
        st.markdown(f"• {rec}")


def show_comprehensive_pattern_analysis(pattern_results, data: pd.DataFrame, symbol: str):
    """Show detailed pattern analysis in comprehensive view"""
    
    st.markdown(f"#### 📊 AI Pattern Recognition for {symbol}")
    
    pattern_summary = pattern_results.get('pattern_summary', {})
    
    # Pattern overview
    col1, col2, col3 = st.columns(3)
    
    with col1:
        total_patterns = pattern_summary.get('total_patterns', 0)
        st.metric("Total Patterns", total_patterns)
        
        bullish_patterns = pattern_summary.get('bullish_patterns', 0)
        st.metric("Bullish Patterns", bullish_patterns)
    
    with col2:
        bearish_patterns = pattern_summary.get('bearish_patterns', 0)
        st.metric("Bearish Patterns", bearish_patterns)
        
        sentiment = pattern_summary.get('overall_sentiment', 'NEUTRAL')
        sentiment_color = "🟢" if sentiment == "BULLISH" else "🔴" if sentiment == "BEARISH" else "🟡"
        st.metric("Pattern Sentiment", f"{sentiment_color} {sentiment}")
    
    with col3:
        # Pattern strength
        if total_patterns > 0:
            pattern_ratio = (bullish_patterns - bearish_patterns) / total_patterns
            strength = "Strong" if abs(pattern_ratio) > 0.6 else "Moderate" if abs(pattern_ratio) > 0.3 else "Weak"
        else:
            strength = "N/A"
        
        st.metric("Pattern Strength", strength)
        st.metric("Analysis Period", f"{len(data)} days")
    
    # Key patterns detected
    chart_patterns = pattern_results.get('chart_patterns', [])
    if chart_patterns:
        st.markdown("**Key Patterns Detected:**")
        for pattern in chart_patterns[:3]:
            confidence = getattr(pattern, 'confidence', 0.5)
            pattern_name = getattr(pattern, 'pattern_name', 'Unknown Pattern')
            pattern_type = getattr(pattern, 'pattern_type', 'NEUTRAL')
            
            pattern_emoji = "🟢" if pattern_type == "BULLISH" else "🔴" if pattern_type == "BEARISH" else "🟡"
            st.markdown(f"• {pattern_emoji} **{pattern_name}** ({confidence:.1%} confidence)")
    
    # Support and resistance levels
    support_resistance = pattern_results.get('support_resistance', [])
    if support_resistance:
        st.markdown("**Key Levels:**")
        current_price = data['Close'].iloc[-1]
        
        for level in support_resistance[:3]:
            level_price = level.get('level', 0)
            level_type = level.get('type', 'unknown').title()
            distance = abs(current_price - level_price) / current_price
            
            st.markdown(f"• **{level_type}**: {level_price:.2f} EGP (Distance: {distance:.1%})")


def generate_ai_trading_scenario(recommendation, sentiment_score, risk_metrics, pattern_summary, data, price_prediction):
    """Generate comprehensive AI trading scenario and action plan"""
    
    try:
        current_price = data['Close'].iloc[-1]
        volatility = data['Close'].pct_change().std() * np.sqrt(252)  # Annualized volatility
        
        # Determine position sizing based on risk
        if risk_metrics.overall_risk_score < 40:
            position_size = "Large (5-10% of portfolio)"
            max_risk = "2-3%"
        elif risk_metrics.overall_risk_score < 70:
            position_size = "Medium (2-5% of portfolio)"
            max_risk = "1-2%"
        else:
            position_size = "Small (1-2% of portfolio)"
            max_risk = "0.5-1%"
        
        # Calculate entry, stop loss, and take profit levels
        volatility_factor = min(0.05, max(0.02, volatility * 1.5))  # Cap volatility between 2-5%
        
        action = recommendation.get('action', 'HOLD').upper()
        
        # Enhanced debug logging
        logger.info(f"Trading Scenario Debug: action='{action}', volatility_factor={volatility_factor:.3f}")
        logger.info(f"Full recommendation object: {recommendation}")
        
        # Force the UI debug to show more information
        debug_info = {
            'current_price': current_price,
            'volatility_factor': volatility_factor,
            'action_detected': action,
            'full_recommendation': str(recommendation),
            'action_check_buy': "BUY" in action,
            'action_check_sell': "SELL" in action,
            'logic_used': "Unknown - checking..."
        }
        
        if "BUY" in action:
            # BUY logic: Enter slightly below current, stop below entry, profit above entry
            entry_price = current_price * 0.995  # Slightly below current price
            stop_loss = entry_price * (1 - volatility_factor)  # Stop below entry
            take_profit = entry_price * (1 + volatility_factor * 2.5)  # Profit above entry
            timing = "On next market dip or immediate if strong momentum"
            logger.info(f"Using BUY logic: entry={entry_price:.2f}, stop={stop_loss:.2f}, target={take_profit:.2f}")
            debug_info['logic_used'] = "BUY Logic Applied"
            
        elif "SELL" in action:
            # SELL logic: Enter slightly above current, stop above entry, profit below entry  
            entry_price = current_price * 1.005  # Slightly above current price
            stop_loss = entry_price * (1 + volatility_factor)  # Stop above entry (loss if price goes up)
            take_profit = entry_price * (1 - volatility_factor * 2.5)  # Profit below entry (gain if price goes down)
            timing = "On next market bounce or immediate if breakdown"
            logger.info(f"Using SELL logic: entry={entry_price:.2f}, stop={stop_loss:.2f}, target={take_profit:.2f}")
            debug_info['logic_used'] = "SELL Logic Applied"
            
        else:  # HOLD
            entry_price = current_price
            stop_loss = current_price * (1 - volatility_factor)
            take_profit = current_price * (1 + volatility_factor * 2)
            timing = "Wait for clearer signals"
            logger.info(f"Using HOLD logic: entry={entry_price:.2f}, stop={stop_loss:.2f}, target={take_profit:.2f}")
            debug_info['logic_used'] = "HOLD Logic Applied (Default)"
        
        # FORCE DEBUG: Override for SELL testing
        if action == "SELL":
            entry_price = current_price * 1.005  # Force SELL entry above current
            stop_loss = entry_price * (1 + volatility_factor)  # Force stop above entry
            take_profit = entry_price * (1 - volatility_factor * 2.5)  # Force profit below entry
            debug_info['logic_used'] = "FORCED SELL Logic Applied (Debug Override)"
            logger.warning(f"FORCED SELL OVERRIDE: entry={entry_price:.2f}, stop={stop_loss:.2f}, target={take_profit:.2f}")
        
        # Calculate risk/reward ratio correctly for both BUY and SELL
        potential_loss = abs(entry_price - stop_loss)
        potential_gain = abs(take_profit - entry_price)
        risk_reward = potential_gain / potential_loss if potential_loss > 0 else 1.0
        
        # Generate action items
        action_items = []
        
        # Priority 1-2: Critical actions
        action_items.append(f"Monitor {recommendation['action']} signal with {recommendation['confidence']:.1%} AI confidence")
        action_items.append(f"Set position size to {position_size} based on {risk_metrics.risk_level.lower()} risk profile")
        
        # Priority 3-4: Important actions
        if sentiment_score.overall_sentiment > 0.3:
            action_items.append("Strong bullish sentiment detected - consider accelerated entry strategy")
        elif sentiment_score.overall_sentiment < -0.3:
            action_items.append("Strong bearish sentiment detected - implement defensive positioning")
        else:
            action_items.append("Neutral sentiment - maintain disciplined approach to entry/exit levels")
        
        if risk_metrics.volatility > 0.30:
            action_items.append("High volatility environment - use smaller position sizes and wider stops")
        
        # Priority 5-6: Monitoring actions
        key_levels = pattern_summary.get('key_levels', [])
        if key_levels:
            nearest_level = key_levels[0]
            action_items.append(f"Watch key {nearest_level['type']} level at {nearest_level['level']:.2f} EGP")
        
        if price_prediction:
            pred_change = (price_prediction - current_price) / current_price
            action_items.append(f"ML models predict {pred_change:+.1%} price movement - adjust strategy accordingly")
        
        action_items.append("Review and update analysis daily or after significant market events")
        action_items.append("Maintain strict risk management discipline regardless of AI confidence levels")
        
        return {
            'entry': {
                'action': action,
                'price': entry_price,
                'position_size': position_size,
                'timing': timing
            },
            'risk': {
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward': risk_reward,
                'max_risk': max_risk
            },
            'action_items': action_items[:8],  # Limit to 8 items
            'debug_info': debug_info
        }
        
    except Exception as e:
        logger.error(f"Error generating AI trading scenario: {str(e)}")
        return {
            'entry': {
                'action': 'HOLD',
                'price': current_price,
                'position_size': 'Small',
                'timing': 'Wait for clearer signals'
            },
            'risk': {
                'stop_loss': current_price * 0.95,
                'take_profit': current_price * 1.05,
                'risk_reward': 1.0,
                'max_risk': '1%'
            },
            'action_items': ['Monitor market conditions and review analysis regularly']
        }


def create_ai_comprehensive_chart(data, sentiment_score, risk_metrics, pattern_results, price_prediction):
    """Create comprehensive AI analysis visualization"""
    
    try:
        fig = go.Figure()
        
        # Price chart
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data['Close'],
            mode='lines',
            name='Price',
            line=dict(color='#2E86AB', width=2),
            hovertemplate='Price: %{y:.2f} EGP<br>Date: %{x}<extra></extra>'
        ))
        
        # Add volume bars
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data['Close'].min() + (data['Volume'] / data['Volume'].max()) * (data['Close'].max() - data['Close'].min()) * 0.1,
            mode='lines',
            name='Volume (scaled)',
            line=dict(color='rgba(128,128,128,0.3)', width=1),
            fill='tonexty',
            fillcolor='rgba(128,128,128,0.1)',
            hovertemplate='Volume: %{text}<br>Date: %{x}<extra></extra>',
            text=[f"{v:,.0f}" for v in data['Volume']]
        ))
        
        # Add moving averages
        sma_20 = data['Close'].rolling(window=20).mean()
        sma_50 = data['Close'].rolling(window=50).mean()
        
        fig.add_trace(go.Scatter(
            x=data.index,
            y=sma_20,
            mode='lines',
            name='SMA 20',
            line=dict(color='orange', width=1, dash='dash'),
            opacity=0.7
        ))
        
        if len(data) >= 50:
            fig.add_trace(go.Scatter(
                x=data.index,
                y=sma_50,
                mode='lines',
                name='SMA 50',
                line=dict(color='red', width=1, dash='dot'),
                opacity=0.7
            ))
        
        # Add sentiment overlay
        sentiment_line = data['Close'].mean() * (1 + sentiment_score.overall_sentiment * 0.1)
        fig.add_hline(
            y=sentiment_line,
            line_dash="dash",
            line_color="green" if sentiment_score.overall_sentiment > 0 else "red",
            annotation_text=f"AI Sentiment: {sentiment_score.overall_sentiment:.3f}",
            annotation_position="bottom right"
        )
        
        # Add prediction point if available
        if price_prediction:
            fig.add_trace(go.Scatter(
                x=[data.index[-1]],
                y=[price_prediction],
                mode='markers',
                name='AI Prediction',
                marker=dict(
                    color='purple',
                    size=12,
                    symbol='diamond',
                    line=dict(color='white', width=2)
                ),
                hovertemplate='AI Prediction: %{y:.2f} EGP<extra></extra>'
            ))
        
        # Support and resistance levels
        support_resistance = pattern_results.get('support_resistance', [])
        for level in support_resistance[:3]:
            level_price = level.get('level', 0)
            level_type = level.get('type', 'unknown')
            
            color = 'green' if level_type == 'support' else 'red'
            fig.add_hline(
                y=level_price,
                line_dash="dot",
                line_color=color,
                opacity=0.5,
                annotation_text=f"{level_type.title()}: {level_price:.2f}",
                annotation_position="top left" if level_type == 'resistance' else "bottom left"
            )
        
        # Update layout
        fig.update_layout(
            title={
                'text': 'AI Comprehensive Analysis Dashboard',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 18, 'family': 'Arial, sans-serif'}
            },
            xaxis_title='Date',
            yaxis_title='Price (EGP)',
            height=500,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            hovermode='x unified',
            template='plotly_white'
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating AI comprehensive chart: {str(e)}")
        
        # Return simple fallback chart
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data['Close'],
            mode='lines',
            name='Price',
            line=dict(color='blue', width=2)
        ))
        
        fig.update_layout(
            title='Price Chart',
            xaxis_title='Date',
            yaxis_title='Price (EGP)',
            height=400
        )
        
        return fig


def calculate_analysis_reliability(sentiment_score, ensemble_results, risk_metrics, pattern_summary) -> float:
    """Calculate overall analysis reliability score"""
    
def calculate_analysis_reliability(sentiment_score, ensemble_results, risk_metrics, pattern_summary) -> float:
    """Calculate overall analysis reliability score"""
    
    try:
        reliability_factors = []
        
        # Sentiment reliability
        sentiment_reliability = sentiment_score.confidence
        reliability_factors.append(sentiment_reliability)
        
        # ML model reliability
        if ensemble_results and 'model_confidence' in ensemble_results:
            ml_reliability = ensemble_results['model_confidence']
            reliability_factors.append(ml_reliability)
        
        # Risk analysis reliability (based on data completeness)
        risk_reliability = 0.9 if hasattr(risk_metrics, 'volatility') else 0.7
        reliability_factors.append(risk_reliability)
        
        # Pattern reliability (based on number of patterns detected)
        total_patterns = pattern_summary.get('total_patterns', 0)
        pattern_reliability = min(0.9, 0.5 + (total_patterns * 0.1))
        reliability_factors.append(pattern_reliability)
        
        # Calculate weighted average
        overall_reliability = np.mean(reliability_factors) if reliability_factors else 0.5
        
        return max(0.3, min(0.95, overall_reliability))
        
    except Exception as e:
        logger.error(f"Error calculating analysis reliability: {str(e)}")
        return 0.7
