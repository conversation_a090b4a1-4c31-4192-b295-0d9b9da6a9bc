"""
AI Advisor - Your Personal Trading Intelligence
Advanced AI-Powered Stock Analysis & Predictions
"""

import streamlit as st
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import time
from functools import wraps

# Configure logging
logger = logging.getLogger(__name__)

# Cache decorator for performance optimization
def cache_predictions(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        cache_key = f"prediction_cache_{args[0]}_{datetime.now().strftime('%Y-%m-%d')}"
        
        # Check cache
        if cache_key in st.session_state:
            cache_data = st.session_state[cache_key]
            cache_time = cache_data['timestamp']
            # Cache valid for 30 minutes
            if datetime.now() - cache_time < timedelta(minutes=30):
                return cache_data['predictions']
        
        # Get fresh predictions
        start_time = time.time()
        predictions = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        # Store in cache with metadata
        st.session_state[cache_key] = {
            'predictions': predictions,
            'timestamp': datetime.now(),
            'execution_time': execution_time
        }
        
        # Track performance metrics
        if 'model_performance_metrics' not in st.session_state:
            st.session_state.model_performance_metrics = []
            
        st.session_state.model_performance_metrics.append({
            'timestamp': datetime.now(),
            'symbol': args[0],
            'execution_time': execution_time,
            'models_used': predictions.get('models_used', []),
            'cache_key': cache_key
        })
        
        return predictions
    return wrapper

def check_api_server_status():
    """Check if TradingView API server is running"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_price_from_api_server(symbol: str) -> Optional[Dict]:
    """Fetch price data from TradingView API server"""
    try:
        import requests
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                # Extract price from the API response
                raw_price = stock_data[0].get('price', 0)
                
                # Convert from piasters to EGP (divide by 1000)
                # API returns prices in piasters (81500 = 81.50 EGP)
                price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                return {
                    'symbol': symbol,
                    'price': price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'api_data': stock_data[0]  # Store full API data for enhanced analysis
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching from API server: {str(e)}")
        return None

def get_available_stocks() -> List[str]:
    """Get list of available stocks from data directory"""
    try:
        data_dir = "data/stocks"
        if not os.path.exists(data_dir):
            return []
        
        stocks = []
        for file in os.listdir(data_dir):
            if file.endswith('.csv'):
                stock_symbol = file.replace('.csv', '')
                stocks.append(stock_symbol)
        
        return sorted(stocks)  # Sort alphabetically
    except Exception as e:
        logger.error(f"Error getting available stocks: {str(e)}")
        return []

def render_enhanced_navigation():
    """Render enhanced navigation with portfolio features"""

    st.markdown("---")

    # Real-time status bar
    render_realtime_status_bar()

    st.markdown("### 🚀 **Enhanced Features**")

    # Create navigation tabs with enhanced styling
    nav_col1, nav_col2, nav_col3, nav_col4 = st.columns(4)

    with nav_col1:
        if st.button("📊 Portfolio Dashboard", help="View your watchlist and portfolio", key="nav_portfolio", use_container_width=True):
            st.session_state.show_portfolio = True
            st.session_state.show_alerts_center = False
            st.session_state.show_comparison_tool = False
            st.session_state.show_ai_chat = False

    with nav_col2:
        if st.button("🔔 Alerts Center", help="Manage your price alerts", key="nav_alerts", use_container_width=True):
            st.session_state.show_alerts_center = True
            st.session_state.show_portfolio = False
            st.session_state.show_comparison_tool = False
            st.session_state.show_ai_chat = False

    with nav_col3:
        if st.button("📈 Compare Stocks", help="Compare multiple stocks", key="nav_compare", use_container_width=True):
            st.session_state.show_comparison_tool = True
            st.session_state.show_portfolio = False
            st.session_state.show_alerts_center = False
            st.session_state.show_ai_chat = False

    with nav_col4:
        if st.button("🤖 AI Assistant", help="Chat with AI trading assistant", key="nav_chat", use_container_width=True):
            st.session_state.show_ai_chat = True
            st.session_state.show_portfolio = False
            st.session_state.show_alerts_center = False
            st.session_state.show_comparison_tool = False

    # Auto-refresh controls
    render_auto_refresh_controls()

    # Display active features
    if st.session_state.get('show_portfolio', False):
        display_portfolio_dashboard()

    if st.session_state.get('show_alerts_center', False):
        display_alerts_center()

    if st.session_state.get('show_comparison_tool', False):
        display_comparison_tool()

    if st.session_state.get('show_ai_chat', False):
        display_ai_chat_interface()

def render_realtime_status_bar():
    """Render real-time status bar with live indicators"""
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        api_status = check_api_server_status()
        if api_status:
            st.success("🟢 Live Data Active")
        else:
            st.error("🔴 Live Data Offline")

    with col2:
        current_time = datetime.now()
        market_open = current_time.replace(hour=10, minute=0, second=0, microsecond=0)
        market_close = current_time.replace(hour=14, minute=30, second=0, microsecond=0)

        if market_open <= current_time <= market_close:
            st.success("🟢 EGX Open")
        else:
            st.info("🔴 EGX Closed")

    with col3:
        # Show last update time
        last_update = st.session_state.get('last_price_update', 'Never')
        if isinstance(last_update, datetime):
            time_diff = (datetime.now() - last_update).total_seconds()
            if time_diff < 60:
                st.info(f"🔄 Updated {int(time_diff)}s ago")
            else:
                st.warning(f"🔄 Updated {int(time_diff/60)}m ago")
        else:
            st.warning("🔄 No Updates")

    with col4:
        # Auto-refresh status
        auto_refresh = st.session_state.get('auto_refresh_enabled', False)
        if auto_refresh:
            st.success("⚡ Auto-Refresh ON")
        else:
            st.info("⚡ Auto-Refresh OFF")

def render_auto_refresh_controls():
    """Render auto-refresh controls"""
    st.markdown("#### ⚡ Real-Time Controls")

    col1, col2, col3 = st.columns(3)

    with col1:
        auto_refresh = st.toggle("Auto-Refresh",
                                value=st.session_state.get('auto_refresh_enabled', False),
                                help="Automatically refresh data every 30 seconds")
        st.session_state.auto_refresh_enabled = auto_refresh

    with col2:
        refresh_interval = st.selectbox("Refresh Interval",
                                       options=[15, 30, 60, 120],
                                       index=1,
                                       format_func=lambda x: f"{x} seconds",
                                       help="How often to refresh data")
        st.session_state.refresh_interval = refresh_interval

    with col3:
        if st.button("🔄 Refresh Now", help="Manually refresh all data", use_container_width=True):
            st.session_state.force_refresh = True
            st.session_state.last_price_update = datetime.now()
            st.rerun()

    # Auto-refresh logic (simplified for now)
    if auto_refresh and st.session_state.get('selected_stock'):
        # Update last refresh time
        st.session_state.last_price_update = datetime.now()

def display_portfolio_dashboard():
    """Display portfolio dashboard with watchlist and performance tracking"""
    st.markdown("### 📊 Portfolio Dashboard")

    # Watchlist section
    st.markdown("#### 📋 Watchlist")

    col1, col2 = st.columns([3, 1])

    with col1:
        new_stock = st.text_input("Add stock to watchlist:", placeholder="e.g., COMI", key="add_watchlist_stock")

    with col2:
        if st.button("➕ Add", key="add_to_watchlist"):
            if new_stock and new_stock.upper() not in st.session_state.watchlist:
                st.session_state.watchlist.append(new_stock.upper())
                st.success(f"Added {new_stock.upper()} to watchlist")
                st.rerun()

    # Display watchlist
    if st.session_state.watchlist:
        watchlist_data = []
        for stock in st.session_state.watchlist:
            # Get live price if available
            live_data = fetch_price_from_api_server(stock)
            if live_data:
                price = live_data['price']
                change = "N/A"  # Could calculate from previous price
                watchlist_data.append({
                    'Symbol': stock,
                    'Price': f"{price:.2f} EGP",
                    'Change': change,
                    'Action': '🗑️'
                })
            else:
                watchlist_data.append({
                    'Symbol': stock,
                    'Price': "N/A",
                    'Change': "N/A",
                    'Action': '🗑️'
                })

        if watchlist_data:
            df_watchlist = pd.DataFrame(watchlist_data)
            st.dataframe(df_watchlist, use_container_width=True, hide_index=True)
    else:
        st.info("Your watchlist is empty. Add stocks to track them here.")

def display_alerts_center():
    """Display alerts center for price notifications"""
    st.markdown("### 🔔 Alerts Center")

    # Current alerts
    st.markdown("#### 📢 Active Alerts")

    if st.session_state.price_alerts:
        alerts_data = []
        for stock, alerts in st.session_state.price_alerts.items():
            for alert in alerts:
                alerts_data.append({
                    'Stock': stock,
                    'Type': alert['type'],
                    'Price': f"{alert['price']:.2f} EGP",
                    'Status': alert['status']
                })

        if alerts_data:
            df_alerts = pd.DataFrame(alerts_data)
            st.dataframe(df_alerts, use_container_width=True, hide_index=True)
    else:
        st.info("No active alerts. Set price alerts below.")

    # Add new alert
    st.markdown("#### ➕ Add New Alert")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        alert_stock = st.selectbox("Stock", options=get_available_stocks(), key="alert_stock_select")

    with col2:
        alert_type = st.selectbox("Alert Type", options=["Above", "Below"], key="alert_type_select")

    with col3:
        alert_price = st.number_input("Price (EGP)", min_value=0.0, step=0.1, key="alert_price_input")

    with col4:
        if st.button("🔔 Set Alert", key="set_alert_btn"):
            if alert_stock and alert_price > 0:
                if alert_stock not in st.session_state.price_alerts:
                    st.session_state.price_alerts[alert_stock] = []

                st.session_state.price_alerts[alert_stock].append({
                    'type': alert_type,
                    'price': alert_price,
                    'status': 'Active',
                    'created': datetime.now()
                })

                st.success(f"Alert set: {alert_stock} {alert_type.lower()} {alert_price:.2f} EGP")
                st.rerun()

def display_comparison_tool():
    """Display stock comparison tool"""
    st.markdown("### 📈 Stock Comparison Tool")

    # Stock selection for comparison
    st.markdown("#### 🔍 Select Stocks to Compare")

    available_stocks = get_available_stocks()

    col1, col2 = st.columns([3, 1])

    with col1:
        selected_stocks = st.multiselect(
            "Choose stocks to compare:",
            options=available_stocks,
            default=st.session_state.get('comparison_stocks', [])[:3],
            max_selections=5,
            key="comparison_stock_select"
        )

    with col2:
        if st.button("📊 Compare", key="compare_stocks_btn"):
            st.session_state.comparison_stocks = selected_stocks
            st.rerun()

    # Display comparison
    if selected_stocks:
        st.markdown("#### 📊 Comparison Results")

        comparison_data = []
        for stock in selected_stocks:
            # Get basic data for comparison
            df = load_stock_data(stock)
            live_data = fetch_price_from_api_server(stock)

            if df is not None:
                current_price = live_data['price'] if live_data else df['Close'].iloc[-1]
                prev_price = df['Close'].iloc[-2] if len(df) > 1 else current_price
                change_pct = ((current_price - prev_price) / prev_price * 100) if prev_price > 0 else 0

                comparison_data.append({
                    'Stock': stock,
                    'Current Price': f"{current_price:.2f} EGP",
                    'Change %': f"{change_pct:+.2f}%",
                    'Volume': f"{df['Volume'].iloc[-1]:,.0f}" if 'Volume' in df.columns else "N/A",
                    'Data Source': "Live + CSV" if live_data else "CSV Only"
                })

        if comparison_data:
            df_comparison = pd.DataFrame(comparison_data)
            st.dataframe(df_comparison, use_container_width=True, hide_index=True)
    else:
        st.info("Select stocks above to compare their performance.")

def display_ai_chat_interface():
    """Display AI chat interface for trading assistance"""
    st.markdown("### 🤖 AI Trading Assistant")

    # Chat history
    st.markdown("#### 💬 Chat History")

    chat_container = st.container()

    with chat_container:
        if st.session_state.chat_history:
            for i, message in enumerate(st.session_state.chat_history[-10:]):  # Show last 10 messages
                if message['type'] == 'user':
                    st.markdown(f"**You:** {message['content']}")
                else:
                    st.markdown(f"**AI:** {message['content']}")
        else:
            st.info("Start a conversation with your AI trading assistant!")

    # Chat input
    st.markdown("#### ✍️ Ask Your AI Assistant")

    col1, col2 = st.columns([4, 1])

    with col1:
        user_message = st.text_input(
            "Ask about stocks, market analysis, or trading strategies:",
            placeholder="e.g., What's the outlook for COMI?",
            key="chat_input"
        )

    with col2:
        if st.button("📤 Send", key="send_chat"):
            if user_message:
                # Add user message
                st.session_state.chat_history.append({
                    'type': 'user',
                    'content': user_message,
                    'timestamp': datetime.now()
                })

                # Generate AI response (simplified for now)
                ai_response = generate_ai_response(user_message)

                # Add AI response
                st.session_state.chat_history.append({
                    'type': 'ai',
                    'content': ai_response,
                    'timestamp': datetime.now()
                })

                st.rerun()

    # Quick action buttons
    st.markdown("#### 🚀 Quick Actions")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📊 Market Summary", key="market_summary_btn"):
            ai_response = "Current EGX market shows mixed signals with moderate volatility. Key stocks to watch include COMI, QNBE, and ABUK."
            st.session_state.chat_history.append({
                'type': 'ai',
                'content': ai_response,
                'timestamp': datetime.now()
            })
            st.rerun()

    with col2:
        if st.button("💡 Trading Tips", key="trading_tips_btn"):
            ai_response = "Key trading tips: 1) Always use stop-losses, 2) Diversify your portfolio, 3) Follow the trend, 4) Manage your risk, 5) Stay informed about market news."
            st.session_state.chat_history.append({
                'type': 'ai',
                'content': ai_response,
                'timestamp': datetime.now()
            })
            st.rerun()

    with col3:
        if st.button("🎯 Risk Analysis", key="risk_analysis_btn"):
            ai_response = "Current market risk is moderate. Consider position sizing of 2-3% per trade and maintain cash reserves for opportunities."
            st.session_state.chat_history.append({
                'type': 'ai',
                'content': ai_response,
                'timestamp': datetime.now()
            })
            st.rerun()

def generate_ai_response(user_message: str) -> str:
    """Generate AI response to user message (simplified implementation)"""
    # This is a simplified implementation
    # In a real scenario, you'd integrate with an LLM API

    message_lower = user_message.lower()

    if any(word in message_lower for word in ['price', 'cost', 'value']):
        return "I can help you analyze current prices and trends. Please specify which stock you're interested in, and I'll provide detailed price analysis with technical indicators."

    elif any(word in message_lower for word in ['buy', 'sell', 'trade']):
        return "For trading decisions, I recommend considering: 1) Technical analysis signals, 2) Market regime, 3) Risk management, 4) Position sizing. Would you like me to analyze a specific stock?"

    elif any(word in message_lower for word in ['risk', 'safe', 'danger']):
        return "Risk management is crucial. Consider: 1) Never risk more than 2% per trade, 2) Use stop-losses, 3) Diversify across sectors, 4) Monitor market volatility. What's your risk tolerance?"

    elif any(word in message_lower for word in ['predict', 'forecast', 'future']):
        return "I use multiple AI models for predictions including LSTM, Random Forest, and Ensemble methods. Predictions include confidence intervals and multiple time horizons. Which stock would you like predictions for?"

    else:
        return "I'm here to help with stock analysis, trading strategies, risk management, and market insights. Feel free to ask about specific stocks, technical indicators, or trading advice!"

def load_stock_data(stock: str) -> Optional[pd.DataFrame]:
    """Load stock data from CSV file"""
    try:
        file_path = f"data/stocks/{stock}.csv"
        if not os.path.exists(file_path):
            return None
        
        df = pd.read_csv(file_path)
        
        # Ensure required columns exist
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        if not all(col in df.columns for col in required_columns):
            return None
        
        # Convert Date column
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.sort_values('Date').reset_index(drop=True)
        
        return df
        
    except Exception as e:
        logger.error(f"Error loading data for {stock}: {str(e)}")
        return None

def show_ai_advisor():
    """Main AI Advisor page"""
    
    # Page header with custom styling
    st.markdown("""
    <style>
    .ai-advisor-header {
        background: linear-gradient(90deg, #1f4e79, #2e8b57);
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    .ai-advisor-title {
        color: white;
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0;
    }
    .ai-advisor-subtitle {
        color: #e8f4f8;
        font-size: 1.2rem;
        margin: 5px 0 0 0;
    }
    .stock-selector {
        background-color: blue;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Main header
    st.markdown("""
    <div class="ai-advisor-header">
        <h1 class="ai-advisor-title">🤖 AI Advisor</h1>
        <p class="ai-advisor-subtitle">Your Personal Trading Intelligence</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize session state
    if 'selected_stock' not in st.session_state:
        st.session_state.selected_stock = None
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = None
    if 'watchlist' not in st.session_state:
        st.session_state.watchlist = []
    if 'price_alerts' not in st.session_state:
        st.session_state.price_alerts = {}
    if 'user_preferences' not in st.session_state:
        st.session_state.user_preferences = {
            'theme': 'default',
            'auto_refresh': False,
            'notification_sound': True,
            'default_timeframe': '1D'
        }
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    if 'comparison_stocks' not in st.session_state:
        st.session_state.comparison_stocks = []
    
    # Enhanced Navigation
    render_enhanced_navigation()

    # Stock Selection Section
    render_stock_selector()

    # Main Analysis Section
    if st.session_state.selected_stock:
        render_comprehensive_analysis()
    else:
        render_welcome_screen()

def render_stock_selector():
    """Render the stock selection interface"""
    
    st.markdown("""
    <div class="stock-selector">
        <h3>🔍 Select Stock for Analysis</h3>
    </div>
    """, unsafe_allow_html=True)
    
    # Get available stocks
    available_stocks = get_available_stocks()
    
    if not available_stocks:
        st.error("❌ No stock data found. Please upload CSV data to the data/stocks/ directory.")
        return
    
    # Create columns for stock selection
    col1, col2, col3 = st.columns([2, 2, 1])
    
    with col1:
        # Dropdown selection
        selected_stock = st.selectbox(
            "Choose from available stocks:",
            options=[""] + available_stocks,
            index=0,
            key="stock_dropdown",
            help="Select a stock from your available data"
        )
    
    with col2:
        # Manual input with search
        manual_input = st.text_input(
            "Or type stock symbol:",
            placeholder="e.g., COMI",
            key="manual_stock_input",
            help="Enter stock symbol manually"
        ).upper()
    
    with col3:
        # Analyze button
        analyze_clicked = st.button(
            "🚀 Analyze",
            type="primary",
            use_container_width=True,
            help="Start comprehensive AI analysis"
        )
    
    # Determine selected stock with enhanced validation
    final_stock = None
    stock_source = None

    if manual_input:
        # Check if manually entered stock exists in CSV data
        if manual_input in available_stocks:
            final_stock = manual_input
            stock_source = "CSV + Live Data"
        else:
            # Check if API server is available for live-only analysis
            api_status = check_api_server_status()
            if api_status:
                final_stock = manual_input
                stock_source = "Live Data Only"
            else:
                final_stock = None
                stock_source = "Not Available"
    elif selected_stock:
        final_stock = selected_stock
        stock_source = "CSV + Live Data"

    # Handle analysis trigger with enhanced validation
    if analyze_clicked and final_stock:
        st.session_state.selected_stock = final_stock
        st.session_state.stock_source = stock_source
        st.session_state.analysis_results = None  # Reset previous results
        st.rerun()
    elif analyze_clicked and not final_stock:
        if manual_input and stock_source == "Not Available":
            st.warning(f"⚠️ Stock '{manual_input}' not found in CSV data and API server is not available. Please check the symbol or start the API server.")
        else:
            st.warning("⚠️ Please select or enter a valid stock symbol.")
    
    # Show current selection with data source info
    if final_stock:
        if stock_source == "CSV + Live Data":
            st.info(f"📊 Ready to analyze: **{final_stock}** (Historical + Live data)")
        elif stock_source == "Live Data Only":
            st.info(f"🔥 Ready to analyze: **{final_stock}** (Live data only - no historical CSV)")

    # Show available stocks info and search capabilities
    col1, col2 = st.columns(2)

    with col1:
        st.markdown(f"**CSV Data Available:** {', '.join(available_stocks[:5])}" +
                    (f" and {len(available_stocks)-5} more..." if len(available_stocks) > 5 else ""))

    with col2:
        api_status = check_api_server_status()
        if api_status:
            st.markdown("**🔥 Live Analysis:** Any EGX stock symbol (API server active)")
        else:
            st.markdown("**📊 Analysis Mode:** CSV data only (API server offline)")

    # Quick Analysis Buttons for Popular Stocks
    if available_stocks:
        st.markdown("**🚀 Quick Analysis:**")
        popular_stocks = available_stocks[:6]  # Show first 6 stocks
        cols = st.columns(len(popular_stocks))

        for i, stock in enumerate(popular_stocks):
            with cols[i]:
                if st.button(f"📈 {stock}", key=f"quick_{stock}", help=f"Quick analysis for {stock}"):
                    st.session_state.selected_stock = stock
                    st.session_state.stock_source = "CSV + Live Data"
                    st.session_state.analysis_results = None
                    st.rerun()

def render_welcome_screen():
    """Render welcome screen when no stock is selected"""

    st.markdown("---")

    # Quick Stats Section
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        available_stocks = get_available_stocks()
        st.metric("📊 Available Stocks", len(available_stocks))

    with col2:
        api_status = check_api_server_status()
        status_text = "🟢 Online" if api_status else "🔴 Offline"
        st.metric("🔥 Live Data", status_text)

    with col3:
        # Show cache performance if available
        if 'model_performance_metrics' in st.session_state:
            avg_time = np.mean([m['execution_time'] for m in st.session_state.model_performance_metrics[-10:]])
            st.metric("⚡ Avg Analysis Time", f"{avg_time:.1f}s")
        else:
            st.metric("⚡ Analysis Ready", "Fast")

    with col4:
        # Show recent analysis count
        recent_count = len(st.session_state.get('model_performance_metrics', []))
        st.metric("📈 Analyses Today", recent_count)

    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown("""
        ### 🎯 Welcome to AI Advisor

        **Your comprehensive trading intelligence platform that combines:**

        🔍 **Smart Stock Analysis**
        - Real-time price data integration
        - Advanced technical indicators
        - Live market validation

        🤖 **AI-Powered Predictions**
        - Multiple prediction models
        - Confidence scoring
        - Time horizon analysis

        💡 **Intelligent Recommendations**
        - Buy/Sell/Hold decisions
        - Entry and exit strategies
        - Risk management guidance

        📈 **Enhanced Visualizations**
        - Interactive price charts
        - Prediction overlays
        - Signal indicators

        ---

        **🚀 Get Started:**
        Select a stock symbol above to begin your comprehensive AI-powered analysis!
        """)

    # Recent Analysis History
    if 'model_performance_metrics' in st.session_state and st.session_state.model_performance_metrics:
        st.markdown("### 📊 Recent Analysis History")
        recent_analyses = st.session_state.model_performance_metrics[-5:]

        for analysis in reversed(recent_analyses):
            col1, col2, col3 = st.columns([2, 2, 1])
            with col1:
                st.write(f"**{analysis['symbol']}**")
            with col2:
                st.write(f"{analysis['timestamp'].strftime('%H:%M:%S')}")
            with col3:
                st.write(f"{analysis['execution_time']:.1f}s")

    # Market Status Widget
    st.markdown("### 🌍 Market Status")
    col1, col2 = st.columns(2)

    with col1:
        current_time = datetime.now()
        # EGX trading hours: 10:00 AM - 2:30 PM Cairo time (UTC+2)
        market_open = current_time.replace(hour=10, minute=0, second=0, microsecond=0)
        market_close = current_time.replace(hour=14, minute=30, second=0, microsecond=0)

        if market_open <= current_time <= market_close:
            st.success("🟢 EGX Market is OPEN")
        else:
            st.info("🔴 EGX Market is CLOSED")

    with col2:
        if api_status:
            st.success("🔥 Live data available")
        else:
            st.warning("📊 Historical data only")

def render_comprehensive_analysis():
    """Render the main comprehensive analysis interface"""
    
    stock = st.session_state.selected_stock
    
    # Show loading spinner while analyzing
    with st.spinner(f"🤖 AI Advisor analyzing {stock}..."):
        
        # Check API server status
        api_status = check_api_server_status()
        
        # Load and analyze data
        analysis_results = perform_comprehensive_analysis(stock, api_status)
        
        if analysis_results:
            st.session_state.analysis_results = analysis_results
        else:
            st.error(f"❌ Could not analyze {stock}. Please check if the data file exists.")
            return
    
    # Display results
    display_analysis_results(analysis_results)

@cache_predictions
def perform_comprehensive_analysis(stock: str, api_available: bool) -> Optional[Dict]:
    """Perform comprehensive analysis combining multiple data sources"""

    try:
        # Import analysis engine
        from app.utils.ai_advisor_engine import (
            calculate_technical_indicators,
            analyze_technical_signals,
            determine_trading_recommendation,
            calculate_price_targets
        )

        # Check if we have CSV data or need live-only analysis
        df = load_stock_data(stock)
        has_csv_data = df is not None and len(df) >= 50

        # Get live price if API available
        live_price = None
        live_data = None
        if api_available:
            live_data = fetch_price_from_api_server(stock)
            if live_data:
                live_price = live_data['price']

        # Handle different data scenarios
        if has_csv_data:
            # Scenario 1: CSV + Live data (best case)
            enhanced_df = calculate_technical_indicators(df)
            current_price = live_price if live_price else enhanced_df['Close'].iloc[-1]
            signals = analyze_technical_signals(enhanced_df, live_price)
            data_source = 'Live + Historical' if live_price else 'Historical'
            csv_price = enhanced_df['Close'].iloc[-1]

        elif live_price:
            # Scenario 2: Live data only (no CSV)
            enhanced_df = create_minimal_dataframe_for_live_analysis(stock, live_price)
            current_price = live_price
            signals = analyze_live_only_signals(live_data)
            data_source = 'Live Data Only'
            csv_price = None

        else:
            # Scenario 3: No data available
            return None

        # Get trading recommendation
        recommendation, confidence, reasoning = determine_trading_recommendation(signals)

        # Calculate price targets with timeframe
        targets = calculate_price_targets(enhanced_df, recommendation, current_price, "Short-term (1-5 days)")

        # Get predictions
        predictions = get_ai_predictions(stock, enhanced_df) if has_csv_data else get_live_only_predictions(stock, live_price)

        # Calculate price difference
        price_difference = 0
        if csv_price and live_price:
            price_difference = ((current_price - csv_price) / csv_price * 100)

        # Generate technical analysis summary
        technical_analysis = generate_technical_analysis_summary(enhanced_df, signals)

        # Generate trading strategy
        strategy = generate_trading_strategy(recommendation, confidence, targets, signals)

        # Compile comprehensive results
        analysis_results = {
            'stock': stock,
            'timestamp': datetime.now(),
            'api_available': api_available,
            'has_csv_data': has_csv_data,
            'live_data': live_data,
            'current_price': current_price,
            'csv_price': csv_price,
            'price_difference': price_difference,
            'data_source': data_source,
            'technical_data': enhanced_df,
            'signals': signals,
            'recommendation': recommendation,
            'confidence': confidence,
            'reasoning': reasoning,
            'targets': targets,
            'predictions': predictions,
            'technical_analysis': technical_analysis,
            'strategy': strategy,
            'analysis_quality': calculate_analysis_quality(signals, confidence, live_price is not None)
        }

        return analysis_results

    except Exception as e:
        logger.error(f"Error in comprehensive analysis for {stock}: {str(e)}")
        return None

def generate_technical_analysis_summary(df: pd.DataFrame, signals: Dict) -> Dict:
    """Generate technical analysis summary for display"""
    try:
        technical_analysis = {}

        # Current values of key indicators
        if 'RSI' in df.columns:
            technical_analysis['RSI'] = df['RSI'].iloc[-1]

        if 'MACD' in df.columns:
            technical_analysis['MACD'] = df['MACD'].iloc[-1]

        if 'MACD_Signal' in df.columns:
            technical_analysis['MACD_Signal'] = df['MACD_Signal'].iloc[-1]

        if 'SMA20' in df.columns:
            technical_analysis['SMA_20'] = df['SMA20'].iloc[-1]

        if 'SMA50' in df.columns:
            technical_analysis['SMA_50'] = df['SMA50'].iloc[-1]

        if 'BB_Upper' in df.columns:
            technical_analysis['Bollinger_Upper'] = df['BB_Upper'].iloc[-1]

        if 'BB_Lower' in df.columns:
            technical_analysis['Bollinger_Lower'] = df['BB_Lower'].iloc[-1]

        if 'ATR' in df.columns:
            technical_analysis['ATR'] = df['ATR'].iloc[-1]

        if 'Volume_Ratio' in df.columns:
            technical_analysis['Volume_Ratio'] = df['Volume_Ratio'].iloc[-1]

        # Add signal interpretations
        technical_analysis['RSI_Status'] = get_rsi_status(technical_analysis.get('RSI', 50))
        technical_analysis['MACD_Status'] = get_macd_status(signals)
        technical_analysis['Trend_Status'] = get_trend_status(signals)
        technical_analysis['Volume_Status'] = get_volume_status(signals)

        return technical_analysis

    except Exception as e:
        logger.error(f"Error generating technical analysis summary: {str(e)}")
        return {}

def generate_trading_strategy(recommendation: str, confidence: float, targets: Dict, signals: Dict) -> Dict:
    """Generate trading strategy based on analysis"""
    try:
        strategy = {
            'recommendation': recommendation,
            'confidence': confidence,
            'entry_strategy': get_entry_strategy(recommendation, signals),
            'risk_management': get_risk_management(targets),
            'position_sizing': get_position_sizing(confidence),
            'time_horizon': get_time_horizon(signals),
            'key_levels': get_key_levels(targets),
            'exit_strategy': get_exit_strategy(recommendation, targets)
        }

        return strategy

    except Exception as e:
        logger.error(f"Error generating trading strategy: {str(e)}")
        return {}

def create_minimal_dataframe_for_live_analysis(stock: str, live_price: float) -> pd.DataFrame:
    """Create minimal dataframe for live-only analysis"""
    try:
        # Create a minimal dataframe with current price
        # This allows basic analysis even without historical data
        current_time = datetime.now()

        # Generate some basic price points around current price for minimal technical analysis
        price_variation = live_price * 0.02  # 2% variation

        data = {
            'Date': [current_time - timedelta(days=i) for i in range(20, 0, -1)],
            'Open': [live_price + np.random.uniform(-price_variation, price_variation) for _ in range(20)],
            'High': [live_price + np.random.uniform(0, price_variation) for _ in range(20)],
            'Low': [live_price - np.random.uniform(0, price_variation) for _ in range(20)],
            'Close': [live_price + np.random.uniform(-price_variation, price_variation) for _ in range(20)],
            'Volume': [1000000 + np.random.uniform(-500000, 500000) for _ in range(20)]
        }

        # Set the last close to actual live price
        data['Close'][-1] = live_price

        df = pd.DataFrame(data)
        df['Date'] = pd.to_datetime(df['Date'])

        # Calculate basic technical indicators
        from app.utils.ai_advisor_engine import calculate_technical_indicators
        df = calculate_technical_indicators(df)

        return df

    except Exception as e:
        logger.error(f"Error creating minimal dataframe: {str(e)}")
        # Return very basic dataframe
        return pd.DataFrame({
            'Date': [datetime.now()],
            'Open': [live_price],
            'High': [live_price],
            'Low': [live_price],
            'Close': [live_price],
            'Volume': [1000000]
        })

def analyze_live_only_signals(live_data: Dict) -> Dict:
    """Analyze signals for live-only data"""
    try:
        signals = {}

        # Basic live data signals
        signals['live_data_available'] = True
        signals['real_time_price'] = True

        # Extract additional signals from API data if available
        if 'api_data' in live_data:
            api_info = live_data['api_data']

            # Add any technical indicators from API
            # This is where you could extract more sophisticated signals
            # from the TradingView API response

            signals['api_enhanced'] = True

        # Since we don't have historical data, we'll have limited signals
        # but we can still provide basic analysis
        signals['limited_history'] = True
        signals['live_analysis_mode'] = True

        return signals

    except Exception as e:
        logger.error(f"Error analyzing live-only signals: {str(e)}")
        return {'live_data_available': True, 'limited_analysis': True}

def get_live_only_predictions(stock: str, live_price: float) -> Dict:
    """Get basic predictions for live-only analysis"""
    try:
        # Since we don't have historical data, provide basic trend predictions
        horizons = [30, 60, 240, 1440]  # 30min, 1hr, 4hr, 1day
        predictions = {}

        for horizon in horizons:
            # Simple random walk prediction (can be enhanced with more sophisticated models)
            volatility = 0.02  # 2% daily volatility assumption
            time_factor = horizon / 1440  # Convert to days

            # Random walk with slight upward bias
            price_change = np.random.normal(0.001 * time_factor, volatility * np.sqrt(time_factor))
            predicted_price = live_price * (1 + price_change)

            predictions[horizon] = {
                'price': predicted_price,
                'horizon_label': format_horizon_label(horizon),
                'confidence': 0.4  # Lower confidence for live-only predictions
            }

        return predictions

    except Exception as e:
        logger.error(f"Error getting live-only predictions: {str(e)}")
        return {}

@cache_predictions
def get_ai_predictions(stock: str, df: pd.DataFrame) -> Dict:
    """Get enhanced AI predictions using multiple models and advanced techniques"""
    try:
        # Import advanced prediction functions
        try:
            from app.models.advanced_prediction import generate_multi_model_predictions
        except ImportError:
            logger.warning("Advanced prediction module not available, using fallback")
            generate_multi_model_predictions = None

        try:
            from app.models.hybrid_predict import hybrid_predict_future_prices
        except ImportError:
            logger.warning("Hybrid prediction module not available, using fallback")
            hybrid_predict_future_prices = None

        # Define prediction horizons (in minutes for consistency)
        horizons = [30, 60, 240, 1440, 4320]  # 30min, 1hr, 4hr, 1day, 3days

        # Initialize predictions structure
        predictions = {
            'multi_model': {},
            'hybrid': {},
            'market_regime': {},
            'consensus': {},
            'confidence_intervals': {}
        }

        # 1. Generate multi-model predictions
        if generate_multi_model_predictions:
            try:
                logger.info(f"Generating multi-model predictions for {stock}")
                multi_model_results = generate_multi_model_predictions(
                    historical_data=df,
                    live_data=None,  # Will be handled by the function
                    symbol=stock,
                    horizons=horizons,
                    models=["lstm", "rf", "gb", "ensemble"],
                    include_confidence=True,
                    confidence_level=0.95
                )
                predictions['multi_model'] = multi_model_results
                logger.info(f"Multi-model predictions generated: {len(multi_model_results)} models")
            except Exception as e:
                logger.warning(f"Multi-model predictions failed: {str(e)}")

        # 2. Generate hybrid predictions (combining ML, DL, and statistical models)
        if hybrid_predict_future_prices:
            try:
                logger.info(f"Generating hybrid predictions for {stock}")
                hybrid_results = hybrid_predict_future_prices(
                    df=df,
                    symbol=stock,
                    horizons=horizons,
                    use_statistical=True,
                    use_ml=True,
                    use_deep_learning=True,
                    use_trend_analysis=True,
                    blend_method='weighted'
                )

                # Convert to expected format
                predictions['hybrid'] = {}
                for horizon, price in hybrid_results.items():
                    predictions['hybrid'][horizon] = {
                        'price': price,
                        'horizon_label': format_horizon_label(horizon),
                        'confidence': 0.85,  # Hybrid models typically have higher confidence
                        'model_type': 'hybrid'
                    }
                logger.info(f"Hybrid predictions generated for {len(hybrid_results)} horizons")
            except Exception as e:
                logger.warning(f"Hybrid predictions failed: {str(e)}")

        # 3. Fallback to basic LSTM predictions if advanced models fail
        if not predictions['multi_model'] and not predictions['hybrid']:
            try:
                logger.info(f"Using fallback LSTM predictions for {stock}")
                from app.models.predict import predict_future_prices

                # Use basic LSTM model
                future_prices = predict_future_prices(df, stock, horizons=horizons)

                # Format as multi-model predictions
                if 'multi_model' not in predictions:
                    predictions['multi_model'] = {}
                predictions['multi_model']['lstm'] = {}
                for horizon, price in future_prices.items():
                    predictions['multi_model']['lstm'][horizon] = {
                        'prediction': price,
                        'confidence': 0.75
                    }
                logger.info(f"Fallback LSTM predictions generated for {len(future_prices)} horizons")
            except Exception as e:
                logger.warning(f"Fallback LSTM predictions failed: {str(e)}")

        # 4. Market regime detection
        try:
            logger.info(f"Detecting market regime for {stock}")
            market_regime = detect_market_regime_simple(df)
            predictions['market_regime'] = market_regime
            logger.info(f"Market regime detected: {market_regime.get('regime', 'unknown')}")
        except Exception as e:
            logger.warning(f"Market regime detection failed: {str(e)}")
            predictions['market_regime'] = {
                'regime': 'normal',
                'confidence': 0.5,
                'volatility': 0.02,
                'trend_strength': 0.5,
                'description': 'Normal market conditions (fallback)'
            }

        # 4.5. Ensure we always have multi-model predictions for display
        if not predictions['multi_model']:
            # Create basic predictions for multi-model display
            current_price = df['Close'].iloc[-1]

            # Create multiple basic models for better display
            basic_models = {
                'trend_model': {},
                'momentum_model': {},
                'mean_reversion_model': {}
            }

            for horizon in horizons:
                time_factor = horizon / 1440  # Convert to days

                # Trend-based model
                recent_returns = df['Close'].pct_change().tail(10).mean()
                trend_prediction = current_price * (1 + recent_returns * time_factor)
                basic_models['trend_model'][horizon] = {
                    'prediction': trend_prediction,
                    'confidence': 0.6
                }

                # Momentum model
                momentum = df['Close'].pct_change().tail(5).mean()
                momentum_prediction = current_price * (1 + momentum * time_factor * 1.2)
                basic_models['momentum_model'][horizon] = {
                    'prediction': momentum_prediction,
                    'confidence': 0.55
                }

                # Mean reversion model
                ma_20 = df['Close'].rolling(20).mean().iloc[-1]
                reversion_factor = (ma_20 - current_price) / current_price * 0.5
                reversion_prediction = current_price * (1 + reversion_factor * time_factor)
                basic_models['mean_reversion_model'][horizon] = {
                    'prediction': reversion_prediction,
                    'confidence': 0.5
                }

            predictions['multi_model'] = basic_models
            logger.info("Created enhanced basic fallback predictions with 3 models")

        # 4.6. If we have hybrid predictions but no multi-model, extract individual models from hybrid
        elif predictions['hybrid'] and not predictions['multi_model']:
            # Extract component models from hybrid predictions for multi-model display
            predictions['multi_model'] = {
                'hybrid_ensemble': {}
            }

            for horizon, hybrid_data in predictions['hybrid'].items():
                predictions['multi_model']['hybrid_ensemble'][horizon] = {
                    'prediction': hybrid_data['price'],
                    'confidence': hybrid_data['confidence']
                }

            logger.info("Extracted hybrid predictions for multi-model display")

        # 5. Generate consensus predictions from all models
        try:
            consensus_predictions = generate_consensus_predictions(predictions, horizons)
            predictions['consensus'] = consensus_predictions
            logger.info(f"Consensus predictions generated for {len(consensus_predictions)} horizons")
        except Exception as e:
            logger.warning(f"Consensus generation failed: {str(e)}")

        # 6. Calculate confidence intervals
        try:
            confidence_intervals = calculate_prediction_confidence_intervals(predictions, horizons)
            predictions['confidence_intervals'] = confidence_intervals
        except Exception as e:
            logger.warning(f"Confidence interval calculation failed: {str(e)}")

        # 7. Create fallback if no predictions available
        if not any([predictions['multi_model'], predictions['hybrid'], predictions['consensus']]):
            logger.warning("No advanced predictions available, creating fallback")
            predictions = create_fallback_predictions(df, horizons)

        return predictions

    except Exception as e:
        logger.error(f"Error getting enhanced AI predictions: {str(e)}")
        return create_fallback_predictions(df, [30, 60, 240, 1440])

def detect_market_regime_simple(df: pd.DataFrame) -> Dict:
    """Simple market regime detection based on price action"""
    try:
        # Calculate returns and volatility
        returns = df['Close'].pct_change().dropna()
        volatility = returns.std()
        trend = returns.mean()

        # Calculate trend strength using moving averages
        if len(df) >= 50:
            ma_short = df['Close'].rolling(20).mean().iloc[-1]
            ma_long = df['Close'].rolling(50).mean().iloc[-1]
            current_price = df['Close'].iloc[-1]

            # Determine regime
            regime = 'sideways'
            confidence = 0.6

            if current_price > ma_short > ma_long and trend > 0.001:
                regime = 'bull'
                confidence = 0.8
            elif current_price < ma_short < ma_long and trend < -0.001:
                regime = 'bear'
                confidence = 0.8
            elif volatility > 0.03:  # High volatility threshold
                regime = 'volatile'
                confidence = 0.7

            description = f"Market showing {regime} characteristics based on price action and moving averages"
        else:
            # Fallback for insufficient data
            regime = 'normal'
            confidence = 0.5
            description = "Insufficient data for comprehensive regime analysis"

        return {
            'regime': regime,
            'confidence': confidence,
            'volatility': float(volatility),
            'trend_strength': abs(float(trend)),
            'description': description
        }

    except Exception as e:
        logger.error(f"Error in market regime detection: {str(e)}")
        return {
            'regime': 'normal',
            'confidence': 0.5,
            'volatility': 0.02,
            'trend_strength': 0.5,
            'description': 'Error in regime analysis - using default'
        }

def generate_consensus_predictions(predictions: Dict, horizons: List[int]) -> Dict:
    """Generate consensus predictions from multiple models"""
    try:
        consensus = {}

        for horizon in horizons:
            horizon_predictions = []
            horizon_confidences = []

            # Collect predictions from all models for this horizon
            if predictions.get('multi_model'):
                for model_name, model_results in predictions['multi_model'].items():
                    if horizon in model_results:
                        pred_data = model_results[horizon]
                        if isinstance(pred_data, dict) and 'prediction' in pred_data:
                            horizon_predictions.append(pred_data['prediction'])
                            horizon_confidences.append(pred_data.get('confidence', 0.5))
                        elif isinstance(pred_data, (int, float)):
                            horizon_predictions.append(pred_data)
                            horizon_confidences.append(0.5)

            if predictions.get('hybrid') and horizon in predictions['hybrid']:
                hybrid_data = predictions['hybrid'][horizon]
                horizon_predictions.append(hybrid_data['price'])
                horizon_confidences.append(hybrid_data['confidence'])

            # Calculate weighted consensus
            if horizon_predictions:
                if horizon_confidences:
                    # Weighted average based on confidence
                    weights = np.array(horizon_confidences)
                    weights = weights / weights.sum()  # Normalize weights
                    consensus_price = np.average(horizon_predictions, weights=weights)
                    consensus_confidence = np.mean(horizon_confidences)
                else:
                    # Simple average
                    consensus_price = np.mean(horizon_predictions)
                    consensus_confidence = 0.6

                consensus[horizon] = {
                    'price': consensus_price,
                    'horizon_label': format_horizon_label(horizon),
                    'confidence': consensus_confidence,
                    'model_count': len(horizon_predictions),
                    'price_std': np.std(horizon_predictions) if len(horizon_predictions) > 1 else 0
                }

        return consensus

    except Exception as e:
        logger.error(f"Error generating consensus predictions: {str(e)}")
        return {}

def calculate_prediction_confidence_intervals(predictions: Dict, horizons: List[int]) -> Dict:
    """Calculate confidence intervals for predictions"""
    try:
        confidence_intervals = {}

        for horizon in horizons:
            horizon_predictions = []

            # Collect all predictions for this horizon
            if predictions.get('multi_model'):
                for model_results in predictions['multi_model'].values():
                    if horizon in model_results:
                        pred_data = model_results[horizon]
                        if isinstance(pred_data, dict):
                            if 'prediction' in pred_data:
                                horizon_predictions.append(pred_data['prediction'])
                            elif 'price' in pred_data:
                                horizon_predictions.append(pred_data['price'])
                        elif isinstance(pred_data, (int, float)):
                            horizon_predictions.append(pred_data)

            if predictions.get('hybrid') and horizon in predictions['hybrid']:
                horizon_predictions.append(predictions['hybrid'][horizon]['price'])

            if predictions.get('consensus') and horizon in predictions['consensus']:
                horizon_predictions.append(predictions['consensus'][horizon]['price'])

            # Calculate confidence intervals
            if len(horizon_predictions) >= 2:
                mean_pred = np.mean(horizon_predictions)
                std_pred = np.std(horizon_predictions)

                # 95% confidence interval
                confidence_intervals[horizon] = {
                    'mean': mean_pred,
                    'lower_95': mean_pred - 1.96 * std_pred,
                    'upper_95': mean_pred + 1.96 * std_pred,
                    'lower_68': mean_pred - std_pred,
                    'upper_68': mean_pred + std_pred,
                    'std': std_pred,
                    'sample_size': len(horizon_predictions)
                }

        return confidence_intervals

    except Exception as e:
        logger.error(f"Error calculating confidence intervals: {str(e)}")
        return {}

def create_fallback_predictions(df: pd.DataFrame, horizons: List[int]) -> Dict:
    """Create fallback predictions when advanced models fail"""
    try:
        current_price = df['Close'].iloc[-1]

        # Simple trend analysis for fallback
        recent_prices = df['Close'].tail(20).values
        price_changes = np.diff(recent_prices) / recent_prices[:-1]
        avg_change = np.mean(price_changes)
        volatility = np.std(price_changes)

        fallback_predictions = {
            'consensus': {},
            'market_regime': {
                'regime': 'normal',
                'confidence': 0.5,
                'volatility': volatility,
                'trend_strength': abs(avg_change),
                'description': 'Fallback analysis based on recent price action'
            }
        }

        for horizon in horizons:
            # Time-scaled trend projection
            time_factor = horizon / 1440  # Convert to days
            expected_change = avg_change * time_factor

            # Add some randomness based on volatility
            random_factor = np.random.normal(0, volatility * np.sqrt(time_factor))
            predicted_price = current_price * (1 + expected_change + random_factor)

            fallback_predictions['consensus'][horizon] = {
                'price': predicted_price,
                'horizon_label': format_horizon_label(horizon),
                'confidence': 0.4,  # Lower confidence for fallback
                'model_count': 1,
                'price_std': volatility * current_price
            }

        return fallback_predictions

    except Exception as e:
        logger.error(f"Error creating fallback predictions: {str(e)}")
        return {'consensus': {}, 'market_regime': {}}

def format_horizon_label(horizon_minutes: int) -> str:
    """Format horizon in minutes to readable label"""
    if horizon_minutes < 60:
        return f"{horizon_minutes}min"
    elif horizon_minutes < 1440:
        hours = horizon_minutes // 60
        return f"{hours}hr"
    else:
        days = horizon_minutes // 1440
        return f"{days}day"

def calculate_analysis_quality(signals: Dict, confidence: float, has_live_data: bool) -> str:
    """Calculate overall analysis quality score"""
    try:
        quality_score = 0

        # Base confidence contribution (0-40 points)
        quality_score += confidence * 40

        # Signal count contribution (0-30 points)
        active_signals = sum(1 for v in signals.values() if v)
        signal_score = min(active_signals * 3, 30)
        quality_score += signal_score

        # Live data bonus (0-20 points)
        if has_live_data:
            quality_score += 20

        # Data completeness (0-10 points)
        if signals.get('live_data_available'):
            quality_score += 10

        # Convert to grade
        if quality_score >= 85:
            return "🟢 Excellent"
        elif quality_score >= 70:
            return "🟡 Good"
        elif quality_score >= 55:
            return "🟠 Fair"
        else:
            return "🔴 Limited"

    except Exception as e:
        logger.error(f"Error calculating analysis quality: {str(e)}")
        return "🟠 Unknown"

def display_enhanced_header(results: Dict):
    """Display enhanced header with key metrics and alerts"""

    # Main header with stock info
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        st.markdown(f"## 📈 {results['stock']} Analysis")
        st.markdown(f"**Data Source:** {results['data_source']}")
        st.markdown(f"**Analysis Quality:** {results.get('analysis_quality', 'Unknown')}")

    with col2:
        current_price = results.get('current_price', 0)
        price_diff = results.get('price_difference', 0)

        if price_diff != 0:
            delta_color = "normal" if price_diff > 0 else "inverse"
            st.metric("Current Price", f"{current_price:.2f} EGP", f"{price_diff:+.2f}%", delta_color=delta_color)
        else:
            st.metric("Current Price", f"{current_price:.2f} EGP")

    with col3:
        recommendation = results.get('recommendation', 'HOLD')
        confidence = results.get('confidence', 0)

        # Color code recommendation
        if recommendation == 'BUY':
            st.success(f"🟢 {recommendation}")
        elif recommendation == 'SELL':
            st.error(f"🔴 {recommendation}")
        else:
            st.info(f"🟡 {recommendation}")

        st.metric("Confidence", f"{confidence:.1%}")

def display_quick_actions(results: Dict):
    """Display quick action buttons"""

    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        if st.button("🔄 Refresh Analysis", help="Get latest analysis"):
            st.session_state.analysis_results = None
            st.rerun()

    with col2:
        if st.button("📊 Add to Watchlist", help="Add to monitoring list"):
            add_to_watchlist(results['stock'])

    with col3:
        if st.button("📈 Compare Stocks", help="Compare with other stocks"):
            st.session_state.show_comparison = True

    with col4:
        if st.button("🔔 Set Alert", help="Set price alerts"):
            st.session_state.show_alerts = True

    with col5:
        if st.button("📱 Share Analysis", help="Share analysis"):
            st.session_state.show_share = True

def add_to_watchlist(stock: str):
    """Add stock to watchlist"""
    if 'watchlist' not in st.session_state:
        st.session_state.watchlist = []

    if stock not in st.session_state.watchlist:
        st.session_state.watchlist.append(stock)
        st.success(f"✅ {stock} added to watchlist!")
    else:
        st.info(f"📊 {stock} is already in your watchlist")

def display_realtime_monitor(results: Dict):
    """Display real-time monitoring dashboard"""

    st.markdown("### ⚡ Real-time Monitor")

    # Auto-refresh toggle
    col1, col2 = st.columns([1, 3])

    with col1:
        auto_refresh = st.toggle("Auto-refresh", help="Automatically refresh data every 30 seconds")

    with col2:
        refresh_interval = st.selectbox("Refresh Interval", [30, 60, 120, 300], index=0, help="Seconds between updates")

    if auto_refresh:
        # Add auto-refresh logic here
        st.info(f"🔄 Auto-refreshing every {refresh_interval} seconds")
        time.sleep(1)  # Placeholder for actual refresh logic

    # Current market status
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Live Price", f"{results.get('current_price', 0):.2f} EGP")

    with col2:
        # Calculate price change from yesterday (placeholder)
        daily_change = np.random.uniform(-2, 2)  # Placeholder
        st.metric("Daily Change", f"{daily_change:+.2f}%", delta=f"{daily_change:+.2f}%")

    with col3:
        # Volume indicator (placeholder)
        volume_status = "🟢 High" if np.random.random() > 0.5 else "🟡 Normal"
        st.metric("Volume", volume_status)

    # Price alerts section
    st.markdown("#### 🔔 Price Alerts")

    col1, col2, col3 = st.columns(3)

    with col1:
        upper_alert = st.number_input("Upper Alert (EGP)", value=results.get('current_price', 0) * 1.05, step=0.1)

    with col2:
        lower_alert = st.number_input("Lower Alert (EGP)", value=results.get('current_price', 0) * 0.95, step=0.1)

    with col3:
        if st.button("Set Alerts"):
            st.success("✅ Alerts set successfully!")

    # Recent price movements (placeholder chart)
    st.markdown("#### 📊 Recent Price Movements")

    # Generate sample intraday data
    times = pd.date_range(start=datetime.now().replace(hour=9, minute=0),
                         end=datetime.now(), freq='5min')
    prices = results.get('current_price', 100) + np.cumsum(np.random.randn(len(times)) * 0.1)

    chart_data = pd.DataFrame({'Time': times, 'Price': prices})
    st.line_chart(chart_data.set_index('Time'))

def display_analysis_results(results: Dict):
    """Display comprehensive analysis results with enhanced features"""

    # Enhanced header with key metrics
    display_enhanced_header(results)

    # Quick action buttons
    display_quick_actions(results)

    # Create enhanced tabs for different analysis sections
    predictions_tab, technical_tab, strategy_tab, monitor_tab, download_tab = st.tabs([
        "🔮 AI Predictions",
        "📈 Technical Analysis",
        "💡 Trading Strategy",
        "⚡ Real-time Monitor",
        "📥 Download Data"
    ])

    with predictions_tab:
        display_predictions_section(results)

    with technical_tab:
        display_technical_section(results)

    with strategy_tab:
        display_strategy_section(results)

    with monitor_tab:
        display_realtime_monitor(results)

    # Note: Visual Analysis tab was removed to eliminate chart-related errors

    with download_tab:
        display_download_data_section(results)

    # Show additional features if enabled
    if st.session_state.get('show_comparison', False):
        display_stock_comparison()

    if st.session_state.get('show_alerts', False):
        display_alert_setup(results)

    if st.session_state.get('show_share', False):
        display_share_options(results)

def display_predictions_section(results: Dict):
    """Display enhanced AI predictions section with multi-model analysis"""

    predictions = results['predictions']
    current_price = results['current_price']
    stock = results['stock']

    if not predictions:
        st.warning("⚠️ No AI predictions available. Please ensure prediction models are trained.")
        return

    st.markdown("### 🤖 Enhanced AI Price Predictions")

    # Display market regime first
    if 'market_regime' in predictions and predictions['market_regime']:
        display_market_regime(predictions['market_regime'])

    # Create tabs for different prediction views
    tab1, tab2, tab3, tab4 = st.tabs([
        "🎯 Consensus Predictions",
        "🔬 Multi-Model Analysis",
        "🚀 Hybrid Models",
        "📊 Confidence Analysis"
    ])

    with tab1:
        display_consensus_predictions(predictions.get('consensus', {}), current_price)

    with tab2:
        display_multi_model_predictions(predictions.get('multi_model', {}), current_price)

    with tab3:
        display_hybrid_predictions(predictions.get('hybrid', {}), current_price)

    with tab4:
        display_confidence_analysis(predictions.get('confidence_intervals', {}),
                                   predictions.get('consensus', {}), current_price)

    # Overall prediction summary
    display_enhanced_prediction_summary(predictions, current_price, stock)

def display_market_regime(market_regime: Dict):
    """Display market regime analysis"""
    try:
        regime = market_regime.get('regime', 'normal')
        confidence = market_regime.get('confidence', 0.5)
        volatility = market_regime.get('volatility', 0.02)
        trend_strength = market_regime.get('trend_strength', 0.5)
        description = market_regime.get('description', 'Market analysis')

        # Color coding for different regimes
        regime_colors = {
            'bull': '#e8f5e8',
            'bear': '#ffe8e8',
            'sideways': '#f0f0f0',
            'volatile': '#fff3cd',
            'normal': '#e7f3ff'
        }

        regime_emojis = {
            'bull': '🐂',
            'bear': '🐻',
            'sideways': '↔️',
            'volatile': '⚡',
            'normal': '📊'
        }

        color = regime_colors.get(regime, '#f0f0f0')
        emoji = regime_emojis.get(regime, '📊')

        st.markdown(f"""
        <div style="background-color: {color}; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
            <h4 style="margin: 0; color: #333;">{emoji} Market Regime: {regime.title()}</h4>
            <p style="margin: 5px 0; color: #666;">Confidence: {confidence:.1%} | Volatility: {volatility:.1%} | Trend Strength: {trend_strength:.1%}</p>
            <p style="margin: 0; color: #666; font-style: italic;">{description}</p>
        </div>
        """, unsafe_allow_html=True)

    except Exception as e:
        logger.error(f"Error displaying market regime: {str(e)}")

def display_consensus_predictions(consensus_predictions: Dict, current_price: float):
    """Display consensus predictions from all models"""
    if not consensus_predictions:
        st.warning("⚠️ No consensus predictions available.")
        return

    st.markdown("#### 🎯 AI Consensus Forecasts")
    st.markdown("*Combined predictions from multiple AI models*")

    # Create prediction cards
    horizons = sorted(consensus_predictions.keys())
    if len(horizons) <= 4:
        cols = st.columns(len(horizons))
    else:
        # Split into two rows if more than 4 horizons
        cols1 = st.columns(min(4, len(horizons)))
        if len(horizons) > 4:
            cols2 = st.columns(len(horizons) - 4)
            cols = list(cols1) + list(cols2)
        else:
            cols = cols1

    for i, horizon in enumerate(horizons):
        pred_data = consensus_predictions[horizon]

        with cols[i % len(cols)]:
            predicted_price = pred_data['price']
            horizon_label = pred_data['horizon_label']
            confidence = pred_data['confidence']
            model_count = pred_data.get('model_count', 1)
            price_std = pred_data.get('price_std', 0)

            # Calculate change
            price_change = ((predicted_price - current_price) / current_price) * 100
            change_color = "🟢" if price_change > 0 else "🔴" if price_change < 0 else "🟡"

            # Create enhanced prediction card
            st.markdown(f"""
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid #dee2e6;">
                <h4 style="margin: 0; color: #333;">{horizon_label}</h4>
                <h3 style="margin: 5px 0; color: #333;">{predicted_price:.2f} EGP</h3>
                <p style="margin: 0; color: #666;">{change_color} {price_change:+.1f}%</p>
                <small style="color: #888;">Confidence: {confidence:.1%}</small><br>
                <small style="color: #888;">Models: {model_count} | Std: ±{price_std:.2f}</small>
            </div>
            """, unsafe_allow_html=True)

def display_multi_model_predictions(multi_model_predictions: Dict, current_price: float):
    """Display predictions from individual models"""
    if not multi_model_predictions:
        st.warning("⚠️ No multi-model predictions available.")
        return

    st.markdown("#### 🔬 Individual Model Predictions")

    # Check if we have advanced models or basic models
    model_names = list(multi_model_predictions.keys())
    has_advanced_models = any(name in ['lstm', 'bilstm', 'rf', 'gb', 'ensemble'] for name in model_names)
    has_basic_models = any(name in ['trend_model', 'momentum_model', 'mean_reversion_model', 'basic'] for name in model_names)

    if has_basic_models and not has_advanced_models:
        st.info("📊 Using basic prediction models. Train advanced models for better accuracy.")
    elif has_advanced_models:
        st.success("🚀 Using advanced AI models for predictions.")

    # Create a table for model comparison
    model_data = []

    for model_name, model_results in multi_model_predictions.items():
        # Get model display name
        model_display_names = {
            'trend_model': 'Trend Analysis',
            'momentum_model': 'Momentum',
            'mean_reversion_model': 'Mean Reversion',
            'lstm': 'LSTM Neural Network',
            'bilstm': 'BiLSTM Neural Network',
            'rf': 'Random Forest',
            'gb': 'Gradient Boosting',
            'ensemble': 'Ensemble Model',
            'basic': 'Basic Model'
        }
        display_name = model_display_names.get(model_name, model_name.upper())

        for horizon, pred_data in model_results.items():
            if isinstance(pred_data, dict):
                price = pred_data.get('prediction', pred_data.get('price', 0))
                confidence = pred_data.get('confidence', 0.5)
            else:
                price = pred_data
                confidence = 0.5

            price_change = ((price - current_price) / current_price) * 100 if price > 0 else 0

            # Color code the change
            if price_change > 0:
                change_display = f"🟢 +{price_change:.1f}%"
            elif price_change < 0:
                change_display = f"🔴 {price_change:.1f}%"
            else:
                change_display = f"🟡 {price_change:.1f}%"

            model_data.append({
                'Model': display_name,
                'Horizon': format_horizon_label(horizon),
                'Predicted Price': f"{price:.2f} EGP",
                'Change': change_display,
                'Confidence': f"{confidence:.1%}"
            })

    if model_data:
        df_models = pd.DataFrame(model_data)
        st.dataframe(df_models, use_container_width=True, hide_index=True)

        # Show model summary
        unique_models = len(set(row['Model'] for row in model_data))
        unique_horizons = len(set(row['Horizon'] for row in model_data))
        avg_confidence = sum(float(row['Confidence'].rstrip('%')) for row in model_data) / len(model_data)

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Models Used", unique_models)
        with col2:
            st.metric("Time Horizons", unique_horizons)
        with col3:
            st.metric("Avg Confidence", f"{avg_confidence:.1f}%")
    else:
        st.info("📊 Model predictions are being processed...")

def display_hybrid_predictions(hybrid_predictions: Dict, current_price: float):
    """Display hybrid model predictions"""
    if not hybrid_predictions:
        st.warning("⚠️ No hybrid predictions available.")
        return

    st.markdown("#### 🚀 Hybrid Model Forecasts")
    st.markdown("*Advanced ensemble combining ML, Deep Learning, and Statistical models*")

    # Display hybrid predictions in a clean format
    for horizon, pred_data in sorted(hybrid_predictions.items()):
        predicted_price = pred_data['price']
        confidence = pred_data['confidence']
        horizon_label = pred_data['horizon_label']

        price_change = ((predicted_price - current_price) / current_price) * 100
        change_color = "🟢" if price_change > 0 else "🔴" if price_change < 0 else "🟡"

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Timeframe", horizon_label)
        with col2:
            st.metric("Predicted Price", f"{predicted_price:.2f} EGP")
        with col3:
            st.metric("Expected Change", f"{price_change:+.1f}%")
        with col4:
            st.metric("Model Confidence", f"{confidence:.1%}")

def display_confidence_analysis(confidence_intervals: Dict, consensus_predictions: Dict, current_price: float):
    """Display confidence interval analysis"""
    if not confidence_intervals and not consensus_predictions:
        st.warning("⚠️ No confidence analysis available.")
        return

    st.markdown("#### 📊 Prediction Confidence Analysis")

    if confidence_intervals:
        st.markdown("**95% Confidence Intervals:**")

        for horizon, ci_data in sorted(confidence_intervals.items()):
            horizon_label = format_horizon_label(horizon)
            mean_price = ci_data['mean']
            lower_95 = ci_data['lower_95']
            upper_95 = ci_data['upper_95']
            std_dev = ci_data['std']
            sample_size = ci_data['sample_size']

            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown(f"**{horizon_label}**")
                st.markdown(f"Mean: {mean_price:.2f} EGP")

            with col2:
                st.markdown(f"**95% Range**")
                st.markdown(f"{lower_95:.2f} - {upper_95:.2f} EGP")

            with col3:
                st.markdown(f"**Statistics**")
                st.markdown(f"Std: ±{std_dev:.2f} | N: {sample_size}")

    # Show prediction reliability summary
    if consensus_predictions:
        st.markdown("---")
        st.markdown("**Prediction Reliability Summary:**")

        total_predictions = len(consensus_predictions)
        high_confidence = sum(1 for p in consensus_predictions.values() if p['confidence'] > 0.7)
        medium_confidence = sum(1 for p in consensus_predictions.values() if 0.5 < p['confidence'] <= 0.7)
        low_confidence = total_predictions - high_confidence - medium_confidence

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("High Confidence", f"{high_confidence}/{total_predictions}",
                     help="Predictions with >70% confidence")
        with col2:
            st.metric("Medium Confidence", f"{medium_confidence}/{total_predictions}",
                     help="Predictions with 50-70% confidence")
        with col3:
            st.metric("Low Confidence", f"{low_confidence}/{total_predictions}",
                     help="Predictions with <50% confidence")

def display_enhanced_prediction_summary(predictions: Dict, current_price: float, stock: str):
    """Display enhanced prediction summary with advanced analytics"""
    st.markdown("---")
    st.markdown("### 📈 Advanced Prediction Analytics")

    # Get consensus predictions for summary
    consensus_predictions = predictions.get('consensus', {})
    market_regime = predictions.get('market_regime', {})

    if not consensus_predictions:
        st.info("📊 Prediction analytics will be available once models generate forecasts.")
        return

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 🎯 Trend Analysis")

        # Calculate short-term vs long-term trend
        horizons = sorted(consensus_predictions.keys())
        if len(horizons) >= 2:
            short_term_price = consensus_predictions[horizons[0]]['price']
            long_term_price = consensus_predictions[horizons[-1]]['price']

            short_term_change = ((short_term_price - current_price) / current_price) * 100
            long_term_change = ((long_term_price - current_price) / current_price) * 100

            st.markdown(f"**Short-term ({format_horizon_label(horizons[0])}):** {short_term_change:+.1f}%")
            st.markdown(f"**Long-term ({format_horizon_label(horizons[-1])}):** {long_term_change:+.1f}%")

            # Trend consistency
            if abs(short_term_change - long_term_change) < 2:
                trend_consistency = "🟢 Consistent"
            elif abs(short_term_change - long_term_change) < 5:
                trend_consistency = "🟡 Moderate"
            else:
                trend_consistency = "🔴 Divergent"

            st.markdown(f"**Trend Consistency:** {trend_consistency}")

    with col2:
        st.markdown("#### 📊 Model Performance")

        # Calculate average confidence across all predictions
        all_confidences = [p['confidence'] for p in consensus_predictions.values()]
        avg_confidence = np.mean(all_confidences) if all_confidences else 0

        # Model count information
        model_counts = [p.get('model_count', 1) for p in consensus_predictions.values()]
        avg_models = np.mean(model_counts) if model_counts else 1

        st.markdown(f"**Average Confidence:** {avg_confidence:.1%}")
        st.markdown(f"**Average Models per Prediction:** {avg_models:.1f}")

        # Market regime impact
        regime = market_regime.get('regime', 'normal')
        regime_confidence = market_regime.get('confidence', 0.5)
        st.markdown(f"**Market Regime:** {regime.title()} ({regime_confidence:.1%})")

    # Prediction quality assessment
    st.markdown("#### 🏆 Overall Prediction Quality")

    quality_score = calculate_prediction_quality_score(predictions)
    quality_grade = get_quality_grade(quality_score)

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Quality Score", f"{quality_score:.0f}/100")
    with col2:
        st.metric("Grade", quality_grade)
    with col3:
        st.metric("Prediction Count", len(consensus_predictions))

def calculate_prediction_quality_score(predictions: Dict) -> float:
    """Calculate overall prediction quality score"""
    try:
        score = 0

        # Base score for having predictions
        if predictions.get('consensus'):
            score += 30

        # Multi-model bonus
        if predictions.get('multi_model'):
            model_count = len(predictions['multi_model'])
            score += min(model_count * 5, 25)  # Up to 25 points for multiple models

        # Hybrid model bonus
        if predictions.get('hybrid'):
            score += 20

        # Confidence intervals bonus
        if predictions.get('confidence_intervals'):
            score += 15

        # Market regime analysis bonus
        if predictions.get('market_regime'):
            regime_confidence = predictions['market_regime'].get('confidence', 0)
            score += regime_confidence * 10

        return min(score, 100)  # Cap at 100

    except Exception as e:
        logger.error(f"Error calculating prediction quality score: {str(e)}")
        return 50

def get_quality_grade(score: float) -> str:
    """Convert quality score to grade"""
    if score >= 90:
        return "🟢 Excellent"
    elif score >= 75:
        return "🟡 Good"
    elif score >= 60:
        return "🟠 Fair"
    else:
        return "🔴 Limited"

def display_technical_section(results: Dict):
    """Display technical analysis section"""
    if not results.get('technical_analysis'):
        st.warning("⚠️ No technical analysis available.")
        return

    st.markdown("### 📈 Technical Analysis")

    technical = results['technical_analysis']

    # Key Technical Indicators
    st.markdown("#### 🔍 Key Technical Indicators")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if 'RSI' in technical:
            rsi_color = "🟢" if technical.get('RSI_Status') == "Oversold" else "🔴" if technical.get('RSI_Status') == "Overbought" else "🟡"
            st.metric("RSI", f"{technical['RSI']:.1f}", help=f"Status: {technical.get('RSI_Status', 'Unknown')}")
            st.write(f"{rsi_color} {technical.get('RSI_Status', 'Unknown')}")

    with col2:
        if 'MACD' in technical:
            macd_color = "🟢" if "Bullish" in technical.get('MACD_Status', '') else "🔴" if "Bearish" in technical.get('MACD_Status', '') else "🟡"
            st.metric("MACD", f"{technical['MACD']:.3f}")
            st.write(f"{macd_color} {technical.get('MACD_Status', 'Unknown')}")

    with col3:
        if 'SMA_20' in technical and 'SMA_50' in technical:
            trend_color = "🟢" if technical.get('Trend_Status') == "Bullish" else "🔴" if technical.get('Trend_Status') == "Bearish" else "🟡"
            st.metric("SMA 20", f"{technical['SMA_20']:.2f}")
            st.write(f"{trend_color} {technical.get('Trend_Status', 'Unknown')}")

    with col4:
        if 'Volume_Ratio' in technical:
            volume_color = "🟢" if technical.get('Volume_Status') == "High Volume" else "🔴" if technical.get('Volume_Status') == "Low Volume" else "🟡"
            st.metric("Volume Ratio", f"{technical['Volume_Ratio']:.2f}")
            st.write(f"{volume_color} {technical.get('Volume_Status', 'Unknown')}")

    # Additional Technical Details
    st.markdown("#### 📊 Additional Technical Details")
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Moving Averages:**")
        if 'SMA_20' in technical:
            st.write(f"• SMA 20: {technical['SMA_20']:.2f} EGP")
        if 'SMA_50' in technical:
            st.write(f"• SMA 50: {technical['SMA_50']:.2f} EGP")

        st.markdown("**Momentum Indicators:**")
        if 'RSI' in technical:
            st.write(f"• RSI: {technical['RSI']:.1f} ({technical.get('RSI_Status', 'Unknown')})")
        if 'MACD' in technical and 'MACD_Signal' in technical:
            st.write(f"• MACD: {technical['MACD']:.3f}")
            st.write(f"• MACD Signal: {technical['MACD_Signal']:.3f}")

    with col2:
        st.markdown("**Volatility & Support/Resistance:**")
        if 'ATR' in technical:
            st.write(f"• ATR: {technical['ATR']:.2f} EGP")
        if 'Bollinger_Upper' in technical and 'Bollinger_Lower' in technical:
            st.write(f"• Bollinger Upper: {technical['Bollinger_Upper']:.2f} EGP")
            st.write(f"• Bollinger Lower: {technical['Bollinger_Lower']:.2f} EGP")

        st.markdown("**Volume Analysis:**")
        if 'Volume_Ratio' in technical:
            st.write(f"• Volume Ratio: {technical['Volume_Ratio']:.2f}x")
            st.write(f"• Status: {technical.get('Volume_Status', 'Unknown')}")

def display_strategy_section(results: Dict):
    """Display trading strategy section"""
    if not results.get('strategy'):
        st.warning("⚠️ No trading strategy available.")
        return

    st.markdown("### 💡 Trading Strategy")

    strategy = results['strategy']

    # Main Recommendation
    recommendation = strategy.get('recommendation', 'HOLD')
    confidence = strategy.get('confidence', 0.0)

    # Color code the recommendation
    if "BUY" in recommendation:
        rec_color = "🟢"
        rec_bg = "#e8f5e8"
    elif "SELL" in recommendation:
        rec_color = "🔴"
        rec_bg = "#ffe8e8"
    else:
        rec_color = "🟡"
        rec_bg = "#fff3cd"

    st.markdown(f"""
    <div style="background-color: {rec_bg}; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
        <h4 style="margin: 0; color: #333;">{rec_color} Recommendation: {recommendation}</h4>
        <p style="margin: 5px 0; color: #666;">Confidence: {confidence:.1%}</p>
    </div>
    """, unsafe_allow_html=True)

    # Strategy Details
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 🎯 Entry & Risk Management")

        if 'entry_strategy' in strategy:
            st.write(f"**Entry Strategy:** {strategy['entry_strategy']}")

        if 'risk_management' in strategy:
            st.write(f"**Risk Management:** {strategy['risk_management']}")

        if 'position_sizing' in strategy:
            st.write(f"**Position Sizing:** {strategy['position_sizing']}")

        if 'time_horizon' in strategy:
            st.write(f"**Time Horizon:** {strategy['time_horizon']}")

    with col2:
        st.markdown("#### 📊 Price Levels & Exit")

        if 'key_levels' in strategy:
            st.write(f"**Key Levels:** {strategy['key_levels']}")

        if 'exit_strategy' in strategy:
            st.write(f"**Exit Strategy:** {strategy['exit_strategy']}")

    # Support and Resistance Levels
    if results.get('targets'):
        targets = results['targets']

        st.markdown("#### 📊 Key Price Levels")

        # Support and Resistance Display
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**🟢 Support Levels**")
            if 'support' in targets:
                st.metric("Primary Support", f"{targets['support']:.2f} EGP",
                         help="Key support level - price may bounce from here")
            if 'support_2' in targets:
                st.metric("Secondary Support", f"{targets['support_2']:.2f} EGP",
                         help="Additional support level")

        with col2:
            st.markdown("**🔴 Resistance Levels**")
            if 'resistance' in targets:
                st.metric("Primary Resistance", f"{targets['resistance']:.2f} EGP",
                         help="Key resistance level - price may face rejection here")
            if 'resistance_2' in targets:
                st.metric("Secondary Resistance", f"{targets['resistance_2']:.2f} EGP",
                         help="Additional resistance level")

        st.markdown("#### 🎯 Price Targets")

        if "BUY" in recommendation:
            st.success("📈 **LONG POSITION Strategy**: Buy low, sell high to profit from price increase")

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                if 'entry_price' in targets:
                    st.metric("🟢 Buy Entry", f"{targets['entry_price']:.2f} EGP",
                             help="Price to buy at")
            with col2:
                if 'target_1' in targets:
                    profit_1 = targets['target_1'] - targets['entry_price']
                    profit_pct_1 = (profit_1 / targets['entry_price']) * 100
                    st.metric("🎯 Sell Target 1", f"{targets['target_1']:.2f} EGP",
                             delta=f"+{profit_1:.2f} EGP ({profit_pct_1:.1f}% profit)",
                             help="Conservative sell target for profit")
            with col3:
                if 'target_2' in targets:
                    profit_2 = targets['target_2'] - targets['entry_price']
                    profit_pct_2 = (profit_2 / targets['entry_price']) * 100
                    st.metric("🚀 Sell Target 2", f"{targets['target_2']:.2f} EGP",
                             delta=f"+{profit_2:.2f} EGP ({profit_pct_2:.1f}% profit)",
                             help="Aggressive sell target for higher profit")
            with col4:
                if 'stop_loss' in targets:
                    loss = targets['stop_loss'] - targets['entry_price']
                    loss_pct = (loss / targets['entry_price']) * 100
                    st.metric("🛑 Stop Loss", f"{targets['stop_loss']:.2f} EGP",
                             delta=f"{loss:.2f} EGP ({loss_pct:.1f}% loss)",
                             delta_color="inverse",
                             help="Sell here to limit losses")

            # Add explanation for BUY strategy
            st.markdown("""
            **📚 How Long Position Works:**
            1. **Buy** at {:.2f} EGP (purchase shares)
            2. **Sell** at Target 1 ({:.2f} EGP) = **Profit: {:.2f} EGP**
            3. **Sell** at Target 2 ({:.2f} EGP) = **Profit: {:.2f} EGP**
            4. **Stop Loss** at {:.2f} EGP = **Loss: {:.2f} EGP**
            """.format(
                targets.get('entry_price', 0),
                targets.get('target_1', 0),
                targets.get('target_1', 0) - targets.get('entry_price', 0),
                targets.get('target_2', 0),
                targets.get('target_2', 0) - targets.get('entry_price', 0),
                targets.get('stop_loss', 0),
                targets.get('stop_loss', 0) - targets.get('entry_price', 0)
            ))

        elif "SELL" in recommendation:
            st.info("📉 **SHORT SELLING Strategy**: Sell high, buy back low to profit from price decline")

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                if 'entry_price' in targets:
                    st.metric("🔴 Sell Entry", f"{targets['entry_price']:.2f} EGP",
                             help="Price to sell/short at")
            with col2:
                if 'target_1' in targets:
                    profit_1 = targets['entry_price'] - targets['target_1']
                    profit_pct_1 = (profit_1 / targets['entry_price']) * 100
                    st.metric("🎯 Buy Back Target 1", f"{targets['target_1']:.2f} EGP",
                             delta=f"+{profit_1:.2f} EGP ({profit_pct_1:.1f}% profit)",
                             help="Conservative buy-back target for profit")
            with col3:
                if 'target_2' in targets:
                    profit_2 = targets['entry_price'] - targets['target_2']
                    profit_pct_2 = (profit_2 / targets['entry_price']) * 100
                    st.metric("🚀 Buy Back Target 2", f"{targets['target_2']:.2f} EGP",
                             delta=f"+{profit_2:.2f} EGP ({profit_pct_2:.1f}% profit)",
                             help="Aggressive buy-back target for higher profit")
            with col4:
                if 'stop_loss' in targets:
                    loss = targets['stop_loss'] - targets['entry_price']
                    loss_pct = (loss / targets['entry_price']) * 100
                    st.metric("🛑 Stop Loss", f"{targets['stop_loss']:.2f} EGP",
                             delta=f"{loss:.2f} EGP ({loss_pct:.1f}% loss)",
                             delta_color="inverse",
                             help="Buy back here to limit losses")

            # Add explanation for SELL strategy
            st.markdown("""
            **📚 How Short Selling Works:**
            1. **Sell** at {:.2f} EGP (borrow and sell shares)
            2. **Buy back** at Target 1 ({:.2f} EGP) = **Profit: {:.2f} EGP**
            3. **Buy back** at Target 2 ({:.2f} EGP) = **Profit: {:.2f} EGP**
            4. **Stop Loss** at {:.2f} EGP = **Loss: {:.2f} EGP**
            """.format(
                targets.get('entry_price', 0),
                targets.get('target_1', 0),
                targets.get('entry_price', 0) - targets.get('target_1', 0),
                targets.get('target_2', 0),
                targets.get('entry_price', 0) - targets.get('target_2', 0),
                targets.get('stop_loss', 0),
                targets.get('stop_loss', 0) - targets.get('entry_price', 0)
            ))

        else:  # HOLD recommendation
            st.info("📊 **HOLD Strategy**: Monitor key levels for potential breakout or breakdown")
            col1, col2 = st.columns(2)
            with col1:
                if 'support' in targets:
                    st.metric("Watch Support", f"{targets['support']:.2f} EGP",
                             help="If price breaks below, consider selling")
            with col2:
                if 'resistance' in targets:
                    st.metric("Watch Resistance", f"{targets['resistance']:.2f} EGP",
                             help="If price breaks above, consider buying")

        # Risk-Reward Analysis
        if 'risk_reward_1' in targets and 'risk_reward_2' in targets:
            st.markdown("#### ⚖️ Risk-Reward Analysis")

            # Calculate potential profit/loss percentages
            current_price = targets.get('entry_price', results.get('current_price', 0))

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Risk-Reward Ratio (Target 1)", f"{targets['risk_reward_1']:.2f}:1",
                         help="Risk vs reward for conservative target")
            with col2:
                st.metric("Risk-Reward Ratio (Target 2)", f"{targets['risk_reward_2']:.2f}:1",
                         help="Risk vs reward for aggressive target")
            with col3:
                # Calculate maximum risk percentage
                if 'stop_loss' in targets and current_price > 0:
                    max_risk_pct = abs((targets['stop_loss'] - current_price) / current_price * 100)
                    st.metric("Maximum Risk", f"{max_risk_pct:.1f}%",
                             help="Maximum potential loss percentage")

        # Trading Plan Summary
        st.markdown("#### 📋 Trading Plan Summary")

        # Calculate profit/loss for summary
        entry_price = targets.get('entry_price', 0)
        target_1 = targets.get('target_1', 0)
        target_2 = targets.get('target_2', 0)
        stop_loss = targets.get('stop_loss', 0)

        if "BUY" in recommendation:
            profit_1 = target_1 - entry_price
            profit_2 = target_2 - entry_price
            loss = stop_loss - entry_price
            strategy_type = "LONG POSITION (Buy Low, Sell High)"
        elif "SELL" in recommendation:
            profit_1 = entry_price - target_1
            profit_2 = entry_price - target_2
            loss = stop_loss - entry_price
            strategy_type = "SHORT POSITION (Sell High, Buy Low)"
        else:
            profit_1 = 0
            profit_2 = 0
            loss = 0
            strategy_type = "HOLD (Wait for Clear Signal)"

        plan_text = f"""
📈 Strategy: {strategy_type}
🎯 Entry: {entry_price:.2f} EGP
🟢 Support: {targets.get('support', 0):.2f} EGP
🔴 Resistance: {targets.get('resistance', 0):.2f} EGP
🛑 Stop Loss: {stop_loss:.2f} EGP (Risk: {loss:.2f} EGP)
🎯 Target 1: {target_1:.2f} EGP (Profit: +{profit_1:.2f} EGP)
🚀 Target 2: {target_2:.2f} EGP (Profit: +{profit_2:.2f} EGP)

💰 Potential Outcomes:
   • Conservative Profit: +{profit_1:.2f} EGP ({(profit_1/entry_price*100):.1f}%)
   • Aggressive Profit: +{profit_2:.2f} EGP ({(profit_2/entry_price*100):.1f}%)
   • Maximum Loss: {loss:.2f} EGP ({(loss/entry_price*100):.1f}%)
        """

        st.code(plan_text, language=None)

def display_download_data_section(results: Dict):
    """Display data download section"""
    st.markdown("### 📥 Download Analysis Data")
    
    if st.button("Download Analysis Results"):
        # Prepare data for download
        download_data = {
            'stock': results['stock'],
            'timestamp': datetime.now().isoformat(),
            'predictions': results.get('predictions', {}),
            'technical': results.get('technical_analysis', {}),
            'strategy': results.get('strategy', {})
        }
        
        # Convert to DataFrame
        df = pd.DataFrame([download_data])
        
        # Create download button
        st.download_button(
            label="Click to Download CSV",
            data=df.to_csv(index=False),
            file_name=f"{results['stock']}_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

# Helper functions for technical analysis and strategy generation
def get_rsi_status(rsi: float) -> str:
    """Get RSI status interpretation"""
    if rsi < 30:
        return "Oversold"
    elif rsi > 70:
        return "Overbought"
    elif 45 <= rsi <= 55:
        return "Neutral"
    elif rsi < 45:
        return "Bearish"
    else:
        return "Bullish"

def get_macd_status(signals: Dict) -> str:
    """Get MACD status interpretation"""
    if signals.get('macd_bullish_cross'):
        return "Bullish Cross"
    elif signals.get('macd_bearish_cross'):
        return "Bearish Cross"
    elif signals.get('macd_above_zero'):
        return "Above Zero"
    elif signals.get('macd_below_zero'):
        return "Below Zero"
    else:
        return "Neutral"

def get_trend_status(signals: Dict) -> str:
    """Get trend status interpretation"""
    if signals.get('golden_cross'):
        return "Golden Cross"
    elif signals.get('death_cross'):
        return "Death Cross"
    elif signals.get('ma_bullish'):
        return "Bullish"
    elif signals.get('ma_bearish'):
        return "Bearish"
    else:
        return "Sideways"

def get_volume_status(signals: Dict) -> str:
    """Get volume status interpretation"""
    if signals.get('high_volume'):
        return "High Volume"
    elif signals.get('low_volume'):
        return "Low Volume"
    else:
        return "Normal Volume"

def get_entry_strategy(recommendation: str, signals: Dict) -> str:
    """Get entry strategy based on recommendation"""
    if "BUY" in recommendation:
        if signals.get('bb_oversold'):
            return "Enter on bounce from oversold levels"
        elif signals.get('breakout_potential'):
            return "Enter on breakout confirmation"
        else:
            return "Enter on pullback to support"
    elif "SELL" in recommendation:
        if signals.get('bb_overbought'):
            return "Enter on rejection from overbought levels"
        elif signals.get('breakdown_risk'):
            return "Enter on breakdown confirmation"
        else:
            return "Enter on rally to resistance"
    else:
        return "Wait for clear directional signal"

def get_risk_management(targets: Dict) -> str:
    """Get risk management strategy"""
    if 'stop_loss' in targets and 'entry_price' in targets:
        risk_pct = abs((targets['stop_loss'] - targets['entry_price']) / targets['entry_price'] * 100)
        return f"Stop loss at {targets['stop_loss']:.2f} EGP ({risk_pct:.1f}% risk)"
    else:
        return "Use 2-3% position risk"

def get_position_sizing(confidence: float) -> str:
    """Get position sizing recommendation"""
    if confidence >= 0.8:
        return "Full position (2-3% account risk)"
    elif confidence >= 0.6:
        return "Half position (1-1.5% account risk)"
    else:
        return "Small position (0.5-1% account risk)"

def get_time_horizon(signals: Dict) -> str:
    """Get recommended time horizon"""
    if signals.get('strong_bullish_momentum') or signals.get('strong_bearish_momentum'):
        return "Short-term (1-5 days)"
    elif signals.get('golden_cross') or signals.get('death_cross'):
        return "Medium-term (1-4 weeks)"
    else:
        return "Flexible (monitor signals)"

def get_key_levels(targets: Dict) -> str:
    """Get key price levels"""
    levels = []
    if 'support' in targets:
        levels.append(f"Support: {targets['support']:.2f}")
    if 'resistance' in targets:
        levels.append(f"Resistance: {targets['resistance']:.2f}")
    if 'target_1' in targets:
        levels.append(f"Target 1: {targets['target_1']:.2f}")
    if 'target_2' in targets:
        levels.append(f"Target 2: {targets['target_2']:.2f}")

    return " | ".join(levels) if levels else "Monitor price action"

def get_exit_strategy(recommendation: str, targets: Dict) -> str:
    """Get exit strategy"""
    if "BUY" in recommendation:
        if 'target_1' in targets and 'target_2' in targets:
            return f"Take 50% profit at {targets['target_1']:.2f}, remainder at {targets['target_2']:.2f}"
        else:
            return "Take profits on resistance test"
    elif "SELL" in recommendation:
        if 'target_1' in targets and 'target_2' in targets:
            return f"Take 50% profit at {targets['target_1']:.2f}, remainder at {targets['target_2']:.2f}"
        else:
            return "Take profits on support test"
    else:
        return "Exit on clear directional break"

def display_visual_analysis_placeholder(results: Dict):
    """Placeholder for visual analysis - feature temporarily disabled"""
    st.markdown("### 📊 Visual Analysis")
    st.info("📈 Advanced charting features are being enhanced and will be available soon!")

    # Show basic price information instead
    current_price = results.get('current_price', 0)
    recommendation = results.get('recommendation', 'HOLD')
    confidence = results.get('confidence', 0)

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Current Price", f"{current_price:.2f} EGP")

    with col2:
        st.metric("Recommendation", recommendation)

    with col3:
        st.metric("Confidence", f"{confidence:.1%}")

    st.markdown("**📊 Available Analysis:**")
    st.markdown("- ✅ AI Predictions")
    st.markdown("- ✅ Technical Analysis")
    st.markdown("- ✅ Trading Strategy")
    st.markdown("- ✅ Real-time Monitor")
    st.markdown("- 🔄 Advanced Charts (Coming Soon)")

# Chart functions removed - Visual Analysis feature temporarily disabled

# Chart-related functions removed - Visual Analysis feature temporarily disabled

def display_stock_comparison():
    """Display stock comparison interface"""

    st.markdown("### 📊 Stock Comparison")

    available_stocks = get_available_stocks()

    col1, col2, col3 = st.columns([2, 2, 1])

    with col1:
        compare_stock = st.selectbox("Select stock to compare:", available_stocks)

    with col2:
        comparison_metric = st.selectbox("Comparison metric:",
                                       ["Price Performance", "Volatility", "Technical Indicators"])

    with col3:
        if st.button("Compare"):
            st.info(f"Comparing with {compare_stock} - Feature coming soon!")

    if st.button("Close Comparison"):
        st.session_state.show_comparison = False
        st.rerun()

def display_alert_setup(results: Dict):
    """Display alert setup interface"""

    st.markdown("### 🔔 Price Alert Setup")

    current_price = results.get('current_price', 0)

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### Upper Alert")
        upper_threshold = st.number_input("Alert when price goes above:",
                                        value=current_price * 1.05, step=0.1)
        upper_enabled = st.checkbox("Enable upper alert")

    with col2:
        st.markdown("#### Lower Alert")
        lower_threshold = st.number_input("Alert when price goes below:",
                                        value=current_price * 0.95, step=0.1)
        lower_enabled = st.checkbox("Enable lower alert")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("Save Alerts"):
            # Save alert settings
            if 'price_alerts' not in st.session_state:
                st.session_state.price_alerts = {}

            st.session_state.price_alerts[results['stock']] = {
                'upper': upper_threshold if upper_enabled else None,
                'lower': lower_threshold if lower_enabled else None,
                'enabled': upper_enabled or lower_enabled
            }
            st.success("✅ Alerts saved successfully!")

    with col2:
        if st.button("Test Alerts"):
            st.info("🔔 Test notification sent!")

    with col3:
        if st.button("Close"):
            st.session_state.show_alerts = False
            st.rerun()

def display_share_options(results: Dict):
    """Display sharing options"""

    st.markdown("### 📱 Share Analysis")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📋 Copy Link"):
            st.success("✅ Analysis link copied to clipboard!")

    with col2:
        if st.button("📧 Email Report"):
            st.info("📧 Email feature coming soon!")

    with col3:
        if st.button("📱 Export PDF"):
            st.info("📄 PDF export feature coming soon!")

    # Generate shareable summary
    st.markdown("#### 📝 Shareable Summary")

    summary = f"""
    **{results['stock']} Analysis Summary**

    📈 Current Price: {results.get('current_price', 0):.2f} EGP
    💡 Recommendation: {results.get('recommendation', 'HOLD')}
    🎯 Confidence: {results.get('confidence', 0):.1%}
    📊 Data Source: {results.get('data_source', 'Unknown')}
    🕒 Analysis Time: {results.get('timestamp', datetime.now()).strftime('%Y-%m-%d %H:%M')}

    Generated by AI Advisor 🤖
    """

    st.text_area("Copy this summary:", summary, height=200)

    if st.button("Close Share"):
        st.session_state.show_share = False
        st.rerun()

def display_portfolio_dashboard():
    """Display comprehensive portfolio dashboard"""

    st.markdown("---")
    st.markdown("## 📊 Portfolio Dashboard")

    # Portfolio overview
    col1, col2, col3 = st.columns(3)

    with col1:
        watchlist_count = len(st.session_state.get('watchlist', []))
        st.metric("📋 Watchlist", f"{watchlist_count} stocks")

    with col2:
        alerts_count = len(st.session_state.get('price_alerts', {}))
        st.metric("🔔 Active Alerts", f"{alerts_count} alerts")

    with col3:
        analyses_today = len(st.session_state.get('model_performance_metrics', []))
        st.metric("📈 Analyses Today", f"{analyses_today}")

    # Watchlist management
    st.markdown("### 📋 Your Watchlist")

    if st.session_state.get('watchlist', []):
        # Display watchlist in a nice format
        for i, stock in enumerate(st.session_state.watchlist):
            col1, col2, col3, col4 = st.columns([2, 2, 2, 1])

            with col1:
                st.write(f"**{stock}**")

            with col2:
                # Try to get current price
                try:
                    api_status = check_api_server_status()
                    if api_status:
                        live_data = fetch_price_from_api_server(stock)
                        if live_data:
                            st.write(f"{live_data['price']:.2f} EGP")
                        else:
                            st.write("Price N/A")
                    else:
                        st.write("API Offline")
                except:
                    st.write("Price N/A")

            with col3:
                if st.button(f"📈 Analyze", key=f"analyze_{stock}_{i}"):
                    st.session_state.selected_stock = stock
                    st.session_state.analysis_results = None
                    st.rerun()

            with col4:
                if st.button(f"🗑️", key=f"remove_{stock}_{i}", help="Remove from watchlist"):
                    st.session_state.watchlist.remove(stock)
                    st.rerun()
    else:
        st.info("📝 Your watchlist is empty. Add stocks to track them here!")

    # Add new stock to watchlist
    st.markdown("#### ➕ Add to Watchlist")
    available_stocks = get_available_stocks()

    col1, col2 = st.columns([3, 1])
    with col1:
        new_stock = st.selectbox("Select stock to add:", [""] + available_stocks, key="add_to_watchlist")

    with col2:
        if st.button("Add to Watchlist") and new_stock:
            if new_stock not in st.session_state.watchlist:
                st.session_state.watchlist.append(new_stock)
                st.success(f"✅ {new_stock} added to watchlist!")
                st.rerun()
            else:
                st.warning(f"📊 {new_stock} is already in your watchlist")

    # Close button
    if st.button("Close Portfolio Dashboard"):
        st.session_state.show_portfolio = False
        st.rerun()

def display_alerts_center():
    """Display comprehensive alerts management center"""

    st.markdown("---")
    st.markdown("## 🔔 Alerts Center")

    # Alerts overview
    col1, col2, col3 = st.columns(3)

    with col1:
        total_alerts = len(st.session_state.get('price_alerts', {}))
        st.metric("📊 Total Alerts", total_alerts)

    with col2:
        active_alerts = sum(1 for alert in st.session_state.get('price_alerts', {}).values()
                           if alert.get('enabled', False))
        st.metric("🟢 Active Alerts", active_alerts)

    with col3:
        # Simulated triggered alerts count
        triggered_today = 0  # This would be calculated from actual alert history
        st.metric("🔥 Triggered Today", triggered_today)

    # Current alerts display
    st.markdown("### 📋 Your Price Alerts")

    if st.session_state.get('price_alerts', {}):
        for stock, alert_config in st.session_state.price_alerts.items():
            with st.expander(f"📈 {stock} Alerts", expanded=True):
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.write("**Upper Alert:**")
                    if alert_config.get('upper'):
                        st.write(f"🔴 {alert_config['upper']:.2f} EGP")
                    else:
                        st.write("Not set")

                with col2:
                    st.write("**Lower Alert:**")
                    if alert_config.get('lower'):
                        st.write(f"🟢 {alert_config['lower']:.2f} EGP")
                    else:
                        st.write("Not set")

                with col3:
                    status = "🟢 Active" if alert_config.get('enabled', False) else "🔴 Inactive"
                    st.write(f"**Status:** {status}")

                with col4:
                    if st.button(f"🗑️ Delete", key=f"delete_alert_{stock}"):
                        del st.session_state.price_alerts[stock]
                        st.rerun()
    else:
        st.info("📝 No price alerts set. Create alerts to monitor your stocks!")

    # Create new alert
    st.markdown("### ➕ Create New Alert")

    available_stocks = get_available_stocks()

    col1, col2, col3 = st.columns(3)

    with col1:
        alert_stock = st.selectbox("Select Stock:", [""] + available_stocks, key="new_alert_stock")

    with col2:
        alert_type = st.selectbox("Alert Type:", ["Price Above", "Price Below", "Both"], key="alert_type")

    with col3:
        alert_price = st.number_input("Alert Price (EGP):", min_value=0.0, step=0.1, key="alert_price")

    col1, col2 = st.columns(2)

    with col1:
        notification_method = st.multiselect("Notification Method:",
                                           ["Browser Notification", "Email", "Sound"],
                                           default=["Browser Notification"])

    with col2:
        if st.button("Create Alert") and alert_stock and alert_price > 0:
            if alert_stock not in st.session_state.price_alerts:
                st.session_state.price_alerts[alert_stock] = {}

            if alert_type == "Price Above":
                st.session_state.price_alerts[alert_stock]['upper'] = alert_price
            elif alert_type == "Price Below":
                st.session_state.price_alerts[alert_stock]['lower'] = alert_price
            else:  # Both
                st.session_state.price_alerts[alert_stock]['upper'] = alert_price * 1.05
                st.session_state.price_alerts[alert_stock]['lower'] = alert_price * 0.95

            st.session_state.price_alerts[alert_stock]['enabled'] = True
            st.session_state.price_alerts[alert_stock]['notification_methods'] = notification_method

            st.success(f"✅ Alert created for {alert_stock}!")
            st.rerun()

    # Alert settings
    st.markdown("### ⚙️ Alert Settings")

    col1, col2 = st.columns(2)

    with col1:
        sound_enabled = st.checkbox("🔊 Sound Notifications",
                                   value=st.session_state.user_preferences.get('notification_sound', True))
        st.session_state.user_preferences['notification_sound'] = sound_enabled

    with col2:
        check_interval = st.selectbox("Check Interval:", ["1 minute", "5 minutes", "15 minutes", "1 hour"],
                                     index=1)

    # Close button
    if st.button("Close Alerts Center"):
        st.session_state.show_alerts_center = False
        st.rerun()

def display_comparison_tool():
    """Display advanced stock comparison tool"""

    st.markdown("---")
    st.markdown("## 📈 Stock Comparison Tool")

    available_stocks = get_available_stocks()

    # Stock selection for comparison
    st.markdown("### 🔍 Select Stocks to Compare")

    col1, col2, col3 = st.columns(3)

    with col1:
        stock1 = st.selectbox("First Stock:", [""] + available_stocks, key="compare_stock1")

    with col2:
        stock2 = st.selectbox("Second Stock:", [""] + available_stocks, key="compare_stock2")

    with col3:
        comparison_metric = st.selectbox("Comparison Metric:",
                                       ["Current Price", "Price Performance", "Volatility", "Technical Indicators"],
                                       key="comparison_metric_select")

    if stock1 and stock2 and stock1 != stock2:
        st.markdown(f"### 📊 Comparing {stock1} vs {stock2}")

        # Get data for both stocks
        try:
            # Get current prices
            api_status = check_api_server_status()

            stock1_price = None
            stock2_price = None

            if api_status:
                stock1_data = fetch_price_from_api_server(stock1)
                stock2_data = fetch_price_from_api_server(stock2)

                if stock1_data:
                    stock1_price = stock1_data['price']
                if stock2_data:
                    stock2_price = stock2_data['price']

            # Fallback to CSV data if API not available
            if not stock1_price:
                df1 = load_stock_data(stock1)
                if df1 is not None and not df1.empty:
                    stock1_price = df1['Close'].iloc[-1]

            if not stock2_price:
                df2 = load_stock_data(stock2)
                if df2 is not None and not df2.empty:
                    stock2_price = df2['Close'].iloc[-1]

            # Display comparison
            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown(f"#### 📈 {stock1}")
                if stock1_price:
                    st.metric("Current Price", f"{stock1_price:.2f} EGP")
                else:
                    st.write("Price data not available")

            with col2:
                st.markdown("#### ⚖️ Comparison")
                if stock1_price and stock2_price:
                    price_diff = ((stock1_price - stock2_price) / stock2_price) * 100
                    if price_diff > 0:
                        st.success(f"{stock1} is {price_diff:.1f}% higher")
                    elif price_diff < 0:
                        st.error(f"{stock1} is {abs(price_diff):.1f}% lower")
                    else:
                        st.info("Prices are equal")
                else:
                    st.write("Cannot compare - missing data")

            with col3:
                st.markdown(f"#### 📈 {stock2}")
                if stock2_price:
                    st.metric("Current Price", f"{stock2_price:.2f} EGP")
                else:
                    st.write("Price data not available")

            # Advanced comparison metrics
            if comparison_metric == "Technical Indicators":
                st.markdown("### 🔧 Technical Indicators Comparison")

                # This would require loading full technical analysis for both stocks
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown(f"**{stock1} Technical Analysis**")
                    # Placeholder for technical indicators
                    st.info("📊 Technical analysis comparison coming soon!")

                with col2:
                    st.markdown(f"**{stock2} Technical Analysis**")
                    # Placeholder for technical indicators
                    st.info("📊 Technical analysis comparison coming soon!")

            elif comparison_metric == "Price Performance":
                st.markdown("### 📈 Performance Comparison")

                # Calculate performance metrics if historical data available
                df1 = load_stock_data(stock1)
                df2 = load_stock_data(stock2)

                if df1 is not None and df2 is not None and not df1.empty and not df2.empty:
                    # Calculate returns
                    stock1_return_1d = ((df1['Close'].iloc[-1] - df1['Close'].iloc[-2]) / df1['Close'].iloc[-2]) * 100 if len(df1) > 1 else 0
                    stock2_return_1d = ((df2['Close'].iloc[-1] - df2['Close'].iloc[-2]) / df2['Close'].iloc[-2]) * 100 if len(df2) > 1 else 0

                    stock1_return_7d = ((df1['Close'].iloc[-1] - df1['Close'].iloc[-8]) / df1['Close'].iloc[-8]) * 100 if len(df1) > 7 else 0
                    stock2_return_7d = ((df2['Close'].iloc[-1] - df2['Close'].iloc[-8]) / df2['Close'].iloc[-8]) * 100 if len(df2) > 7 else 0

                    col1, col2 = st.columns(2)

                    with col1:
                        st.metric(f"{stock1} - 1 Day Return", f"{stock1_return_1d:.2f}%")
                        st.metric(f"{stock1} - 7 Day Return", f"{stock1_return_7d:.2f}%")

                    with col2:
                        st.metric(f"{stock2} - 1 Day Return", f"{stock2_return_1d:.2f}%")
                        st.metric(f"{stock2} - 7 Day Return", f"{stock2_return_7d:.2f}%")
                else:
                    st.info("📊 Historical data needed for performance comparison")

            # Quick action buttons
            st.markdown("### 🚀 Quick Actions")
            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button(f"📈 Analyze {stock1}"):
                    st.session_state.selected_stock = stock1
                    st.session_state.analysis_results = None
                    st.session_state.show_comparison_tool = False
                    st.rerun()

            with col2:
                if st.button(f"📈 Analyze {stock2}"):
                    st.session_state.selected_stock = stock2
                    st.session_state.analysis_results = None
                    st.session_state.show_comparison_tool = False
                    st.rerun()

            with col3:
                if st.button("➕ Add Both to Watchlist"):
                    if stock1 not in st.session_state.watchlist:
                        st.session_state.watchlist.append(stock1)
                    if stock2 not in st.session_state.watchlist:
                        st.session_state.watchlist.append(stock2)
                    st.success("✅ Both stocks added to watchlist!")

        except Exception as e:
            st.error(f"❌ Error comparing stocks: {str(e)}")

    elif stock1 and stock2 and stock1 == stock2:
        st.warning("⚠️ Please select two different stocks to compare")

    else:
        st.info("📝 Select two stocks above to start comparison")

    # Close button
    if st.button("Close Comparison Tool"):
        st.session_state.show_comparison_tool = False
        st.rerun()

def display_ai_chat_interface():
    """Display AI trading assistant chat interface"""

    st.markdown("---")
    st.markdown("## 🤖 AI Trading Assistant")
    st.markdown("*Ask me anything about trading, stocks, or market analysis!*")

    # Chat history display
    chat_container = st.container()

    with chat_container:
        if st.session_state.get('chat_history', []):
            for i, message in enumerate(st.session_state.chat_history):
                if message['role'] == 'user':
                    st.markdown(f"**👤 You:** {message['content']}")
                else:
                    st.markdown(f"**🤖 AI Assistant:** {message['content']}")
                st.markdown("---")
        else:
            st.info("💬 Start a conversation! Ask me about stocks, trading strategies, or market analysis.")

    # Chat input
    st.markdown("### 💬 Ask the AI Assistant")

    # Predefined quick questions
    st.markdown("**🚀 Quick Questions:**")
    quick_questions = [
        "What's the best trading strategy for beginners?",
        "How do I read technical indicators?",
        "What should I look for in a good stock?",
        "How do I manage risk in trading?",
        "What are the current market trends?"
    ]

    cols = st.columns(len(quick_questions))
    for i, question in enumerate(quick_questions):
        with cols[i]:
            if st.button(f"❓", key=f"quick_q_{i}", help=question):
                process_ai_chat_message(question)
                st.rerun()

    # Custom question input
    col1, col2 = st.columns([4, 1])

    with col1:
        user_question = st.text_input("Type your question:", placeholder="e.g., Should I buy AAIB stock?", key="chat_input")

    with col2:
        if st.button("Send 📤") and user_question.strip():
            process_ai_chat_message(user_question)
            st.rerun()

    # Chat features
    st.markdown("### 🛠️ Chat Features")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🗑️ Clear Chat"):
            st.session_state.chat_history = []
            st.rerun()

    with col2:
        if st.button("💾 Save Chat"):
            if st.session_state.get('chat_history', []):
                chat_text = "\n".join([f"{msg['role']}: {msg['content']}" for msg in st.session_state.chat_history])
                st.download_button(
                    label="📥 Download Chat",
                    data=chat_text,
                    file_name=f"ai_chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                    mime="text/plain"
                )
            else:
                st.info("No chat history to save")

    with col3:
        if st.button("🎯 Get Stock Recommendation"):
            if st.session_state.get('selected_stock'):
                stock = st.session_state.selected_stock
                recommendation_question = f"Based on current market conditions, should I buy, sell, or hold {stock}? Please provide a detailed analysis."
                process_ai_chat_message(recommendation_question)
                st.rerun()
            else:
                st.warning("Please select a stock first")

    # Close button
    if st.button("Close AI Assistant"):
        st.session_state.show_ai_chat = False
        st.rerun()

def process_ai_chat_message(user_message: str):
    """Process user message and generate AI response"""

    # Add user message to history
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []

    st.session_state.chat_history.append({
        'role': 'user',
        'content': user_message,
        'timestamp': datetime.now()
    })

    # Generate AI response (this is a simplified version)
    ai_response = generate_ai_response(user_message)

    # Add AI response to history
    st.session_state.chat_history.append({
        'role': 'assistant',
        'content': ai_response,
        'timestamp': datetime.now()
    })

def generate_ai_response(user_message: str) -> str:
    """Generate AI response based on user message"""

    # Convert to lowercase for easier matching
    message_lower = user_message.lower()

    # Context-aware responses based on current analysis
    current_stock = st.session_state.get('selected_stock')
    analysis_results = st.session_state.get('analysis_results')

    # Stock-specific responses
    if current_stock and any(word in message_lower for word in ['buy', 'sell', 'hold', 'recommend']):
        if analysis_results:
            recommendation = analysis_results.get('recommendation', 'HOLD')
            confidence = analysis_results.get('confidence', 0)
            current_price = analysis_results.get('current_price', 0)

            return f"""Based on my analysis of {current_stock}:

📊 **Current Recommendation:** {recommendation}
🎯 **Confidence Level:** {confidence:.1%}
💰 **Current Price:** {current_price:.2f} EGP

**Reasoning:**
{analysis_results.get('reasoning', 'Analysis based on technical indicators and market conditions.')}

**Key Factors:**
- Technical indicators suggest {recommendation.lower()} signal
- Market conditions are being monitored
- Risk management is important - consider your portfolio allocation

Would you like me to explain any specific aspect of this analysis?"""
        else:
            return f"I'd be happy to help with {current_stock}! Please run an analysis first so I can provide specific recommendations based on current data."

    # General trading questions
    elif any(word in message_lower for word in ['strategy', 'trading', 'how to']):
        return """Here are some key trading strategies to consider:

🎯 **For Beginners:**
- Start with paper trading to practice
- Focus on large-cap, liquid stocks
- Use stop-losses to manage risk
- Don't invest more than you can afford to lose

📈 **Technical Analysis:**
- Learn to read candlestick patterns
- Understand support and resistance levels
- Use moving averages for trend identification
- RSI and MACD are great momentum indicators

💡 **Risk Management:**
- Never risk more than 2% of your portfolio on a single trade
- Diversify across different sectors
- Set clear entry and exit points
- Keep emotions in check

Would you like me to elaborate on any of these points?"""

    elif any(word in message_lower for word in ['indicator', 'technical', 'rsi', 'macd']):
        return """📊 **Technical Indicators Explained:**

**RSI (Relative Strength Index):**
- Measures momentum (0-100 scale)
- Above 70: Potentially overbought
- Below 30: Potentially oversold

**MACD (Moving Average Convergence Divergence):**
- Shows relationship between two moving averages
- MACD line crossing above signal line: Bullish signal
- MACD line crossing below signal line: Bearish signal

**Moving Averages:**
- SMA20: Short-term trend
- SMA50: Medium-term trend
- Price above MA: Uptrend
- Price below MA: Downtrend

**Bollinger Bands:**
- Show volatility and potential support/resistance
- Price touching upper band: Potential resistance
- Price touching lower band: Potential support

Need help interpreting any specific indicator for your stock?"""

    elif any(word in message_lower for word in ['risk', 'manage', 'loss']):
        return """🛡️ **Risk Management Essentials:**

**Position Sizing:**
- Risk only 1-2% of your total portfolio per trade
- Use the formula: (Account Size × Risk %) ÷ (Entry Price - Stop Loss)

**Stop Losses:**
- Always set stop losses before entering a trade
- Technical stop: Below support levels
- Percentage stop: 5-10% below entry price

**Diversification:**
- Don't put all eggs in one basket
- Spread investments across different sectors
- Consider correlation between holdings

**Portfolio Heat:**
- Monitor total risk across all positions
- Avoid having too many correlated positions
- Regular portfolio review and rebalancing

**Emotional Control:**
- Stick to your trading plan
- Don't chase losses with bigger bets
- Take profits when targets are hit

Remember: Preservation of capital is more important than making profits!"""

    elif any(word in message_lower for word in ['market', 'trend', 'economy']):
        return """🌍 **Current Market Insights:**

**EGX Market Conditions:**
- Egyptian Exchange operates 10:00 AM - 2:30 PM Cairo time
- Focus on blue-chip stocks for stability
- Monitor economic indicators and political developments

**Key Factors to Watch:**
- Central Bank of Egypt policy decisions
- Currency stability (EGP/USD)
- Government economic reforms
- Global market sentiment

**Market Trends:**
- Technology and fintech sectors showing growth
- Banking sector remains important
- Real estate and construction cyclical
- Consumer goods defensive play

**Analysis Tips:**
- Use both technical and fundamental analysis
- Monitor volume for confirmation
- Watch for sector rotation patterns
- Consider seasonal effects

Would you like specific insights about any particular sector or stock?"""

    else:
        # Default helpful response
        return """I'm here to help with your trading and investment questions!

🤖 **I can assist you with:**
- Stock analysis and recommendations
- Technical indicator explanations
- Trading strategy advice
- Risk management guidance
- Market trend insights
- Portfolio optimization tips

💡 **Try asking me:**
- "Should I buy [stock name]?"
- "How do I read RSI indicators?"
- "What's a good trading strategy for beginners?"
- "How do I manage risk in my portfolio?"

Feel free to ask anything about trading, stocks, or market analysis!"""
