"""
Stock Management Page - Consolidated Select Stock + Manage Stocks functionality
"""

import streamlit as st
import pandas as pd
import os
import logging
from typing import Optional, Tuple

# Import utilities
from app.utils.state_manager import load_stock_data, get_available_stock_files, load_and_process_data
from app.utils.feature_engineering import prepare_features
from app.components.stock_manager import stock_manager_component

# Configure logging
logger = logging.getLogger(__name__)

def show_stock_management():
    """Main function to display the consolidated Stock Management page"""

    st.title("📊 Stock Management")
    st.markdown("### Select, View, and Manage Your Stock Data")

    # Create tabs for different functionalities
    tab1, tab2, tab3 = st.tabs(["📈 Select Stock", "🗂️ Manage Files", "📋 Stock Information"])

    with tab1:
        show_stock_selection()

    with tab2:
        show_file_management()

    with tab3:
        show_stock_information()

def show_stock_selection():
    """Stock selection functionality (from original Select Stock page)"""

    st.header("Select Stock Data")

    # Get available stock files
    available_stocks = get_available_stock_files()

    if not available_stocks:
        st.warning("No stock data files found. Please upload some data first.")
        st.info("Go to the 'Upload Data' page to upload stock data.")

        # Add quick navigation button
        if st.button("📤 Go to Upload Data", type="primary"):
            st.session_state.page = "Upload Data"
            st.rerun()
        return

    st.subheader("Available Stocks")

    # Create a dropdown for stock selection
    selected_stock = st.selectbox(
        "Select a stock",
        options=available_stocks,
        key="stock_management_select_dropdown"
    )

    # Add a load button
    if st.button("Load Selected Stock", key="stock_management_load_button"):
        # Show a spinner while loading
        with st.spinner(f"Loading and processing data for {selected_stock}..."):
            # Use the cached function to load and process data
            df_features = load_and_process_data(selected_stock)
            if df_features is not None:
                # Store both raw and processed data
                df = load_stock_data(selected_stock)  # This will use the cached version
                st.session_state.historical_data = df
                st.session_state.processed_data = df_features
                st.session_state.symbol = selected_stock
                st.success(f"✅ Loaded and processed data for {selected_stock}")

    # Display currently selected stock
    st.markdown("---")
    st.subheader("Currently Selected Stock")

    if st.session_state.symbol is not None and st.session_state.historical_data is not None:
        # Create info columns
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Selected Stock", st.session_state.symbol)

        with col2:
            df = st.session_state.historical_data
            st.metric("Total Rows", len(df))

        with col3:
            date_range = f"{df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}"
            st.metric("Date Range", "")
            st.caption(date_range)

        # Display a preview of the data
        st.subheader("📊 Data Preview")

        # Sort by date in descending order to show the most recent data first
        df_display = df.sort_values('Date', ascending=False).head(10)
        st.dataframe(df_display, use_container_width=True)

        # Expandable sections for more details
        with st.expander("📈 View More Data"):
            num_rows = st.slider(
                "Number of rows to display",
                min_value=10,
                max_value=min(100, len(df)),
                value=20,
                key="stock_management_rows_slider"
            )
            df_display_more = df.sort_values('Date', ascending=False).head(num_rows)
            st.dataframe(df_display_more, use_container_width=True)

        with st.expander("📊 Summary Statistics"):
            # Create a copy of the DataFrame with only numeric columns
            df_numeric = df.select_dtypes(include=['number'])

            # Add a checkbox to show statistics for the most recent data
            show_recent = st.checkbox(
                "Show statistics for most recent data only",
                key="stock_management_recent_stats"
            )

            if show_recent:
                # Get data from 2025 only
                df_recent = df[df['Date'].dt.year >= 2025]
                if not df_recent.empty:
                    st.write("**Statistics for 2025 data:**")
                    st.dataframe(df_recent.select_dtypes(include=['number']).describe())
                else:
                    st.warning("No data from 2025 found.")
                    st.dataframe(df_numeric.describe())
            else:
                st.write("**Statistics for all data:**")
                st.dataframe(df_numeric.describe())

        with st.expander("🔧 Technical Indicators Preview"):
            df_features = prepare_features(df)
            st.subheader("Data with Technical Indicators")

            # Sort by date in descending order to show the most recent data first
            df_features_display = df_features.sort_values('Date', ascending=False).head(10)
            st.dataframe(df_features_display, use_container_width=True)

            # Option to view more data with technical indicators
            if st.checkbox("View more data with technical indicators", key="stock_management_more_features"):
                num_rows_features = st.slider(
                    "Number of rows with indicators to display",
                    min_value=10,
                    max_value=min(100, len(df_features)),
                    value=20,
                    key="stock_management_features_slider"
                )
                df_features_more = df_features.sort_values('Date', ascending=False).head(num_rows_features)
                st.dataframe(df_features_more, use_container_width=True)

        # Quick action buttons
        st.markdown("---")
        st.subheader("🚀 Quick Actions")

        action_cols = st.columns(4)

        with action_cols[0]:
            if st.button("📈 View Dashboard", use_container_width=True):
                st.session_state.page = "Dashboard"
                st.rerun()

        with action_cols[1]:
            if st.button("🤖 Train Model", use_container_width=True):
                st.session_state.page = "Train Model"
                st.rerun()

        with action_cols[2]:
            if st.button("🔮 Make Predictions", use_container_width=True):
                st.session_state.page = "Predictions"
                st.rerun()

        with action_cols[3]:
            if st.button("📊 Live Trading", use_container_width=True):
                st.session_state.page = "Live Trading"
                st.rerun()

    else:
        st.info("👆 Please select a stock from the dropdown above to get started.")

def show_file_management():
    """File management functionality (from original Manage Stocks page)"""

    st.header("Manage Stock Files")
    st.markdown("Rename, delete, backup, and organize your stock data files.")

    # Use the existing stock manager component
    stock_manager_component()

def show_stock_information():
    """Enhanced stock information and analytics"""

    st.header("Stock Information & Analytics")

    if st.session_state.symbol is None or st.session_state.historical_data is None:
        st.info("Please select a stock first to view detailed information.")
        return

    df = st.session_state.historical_data
    symbol = st.session_state.symbol

    # Stock overview
    st.subheader(f"📊 {symbol} - Stock Overview")

    # Key metrics
    latest_price = df['Close'].iloc[-1]
    price_change = df['Close'].iloc[-1] - df['Close'].iloc[-2] if len(df) > 1 else 0
    price_change_pct = (price_change / df['Close'].iloc[-2] * 100) if len(df) > 1 and df['Close'].iloc[-2] != 0 else 0

    # Display metrics in columns
    metric_cols = st.columns(4)

    with metric_cols[0]:
        st.metric(
            "Latest Price",
            f"{latest_price:.2f} EGP",
            delta=f"{price_change:+.2f} ({price_change_pct:+.1f}%)"
        )

    with metric_cols[1]:
        high_52w = df['High'].max()
        st.metric("52W High", f"{high_52w:.2f} EGP")

    with metric_cols[2]:
        low_52w = df['Low'].min()
        st.metric("52W Low", f"{low_52w:.2f} EGP")

    with metric_cols[3]:
        avg_volume = df['Volume'].mean() if 'Volume' in df.columns else 0
        st.metric("Avg Volume", f"{avg_volume:,.0f}")

    # Price chart
    st.subheader("📈 Price Chart")
    st.line_chart(df.set_index('Date')['Close'])

    # Volume chart (if available)
    if 'Volume' in df.columns:
        st.subheader("📊 Volume Chart")
        st.bar_chart(df.set_index('Date')['Volume'])

    # Data quality assessment
    st.subheader("🔍 Data Quality Assessment")

    quality_cols = st.columns(2)

    with quality_cols[0]:
        st.write("**Data Completeness:**")
        missing_data = df.isnull().sum()
        if missing_data.sum() == 0:
            st.success("✅ No missing data found")
        else:
            st.warning(f"⚠️ Found {missing_data.sum()} missing values")
            st.write(missing_data[missing_data > 0])

    with quality_cols[1]:
        st.write("**Data Freshness:**")
        latest_date = df['Date'].max()
        days_old = (pd.Timestamp.now() - latest_date).days

        if days_old <= 7:
            st.success(f"✅ Data is fresh ({days_old} days old)")
        elif days_old <= 30:
            st.warning(f"⚠️ Data is {days_old} days old")
        else:
            st.error(f"❌ Data is outdated ({days_old} days old)")
