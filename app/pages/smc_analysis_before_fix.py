import os
import sys
import logging
import numpy as np
import pandas as pd
import streamlit as st
import requests
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

# Import SMC components
from app.components.smc_indicators import (
    detect_order_blocks, 
    detect_fvg, 
    detect_liquidity_zones,
    FairValueGap
)
from app.components.advanced_smc_features import (
    detect_break_of_structure,
    detect_liquidity_sweeps,
    calculate_premium_discount_zones
)
from app.components.multi_timeframe_analysis import (
    analyze_multiple_timeframes,
    generate_multi_timeframe_signal
)
from app.components.ai_pattern_recognition import AIPatternRecognizer
# Removed non-existent imports
from app.components.trade_management import TradeManager
from app.components.egx_smc_parameters import get_stock_specific_parameters
from app.components.trade_management import TradeManager

# Configure logging
logger = logging.getLogger(__name__)

# Define EGX_STOCKS mapping (same as advanced_technical_analysis.py)
EGX_STOCKS = {
    "COMI": "Commercial International Bank",
    "FWRY": "Fawry Banking Technology",
    "PHDC": "Palm Hills Development",
    "EFID": "Edita Food Industries",
    "UBEE": "United Bank Egypt",
    "GGRN": "GoGreen Agricultural",
    "OBRI": "Orascom Business Intelligence",
    "UTOP": "United Top"
}

# Define SMC_AVAILABLE flag
SMC_AVAILABLE = True

# TradingView API Configuration (same as Advanced Technical Analysis)
TRADINGVIEW_API_URL = "http://127.0.0.1:8000/api/scrape_pairs"

def get_dynamic_colors(structure, age_factor):
    """
    Generate dynamic colors for chart structures based on age and type
    
    Args:
        structure: The SMC structure (order block, FVG, etc.)
        age_factor: Age factor for color fading
        
    Returns:
        dict: Dictionary with color properties
    """
    base_opacity = max(0.2, min(0.9, 1.0 - age_factor))
    
    if hasattr(structure, 'ob_type'):
        # Order block
        if structure.ob_type == 'bullish':
            return {
                'fill': f'rgba(0, 128, 0, {base_opacity})',  # Green with opacity
                'line': f'rgba(0, 100, 0, {base_opacity + 0.1})'  # Darker green
            }
        else:
            return {
                'fill': f'rgba(178, 34, 34, {base_opacity})',  # Red with opacity
                'line': f'rgba(139, 0, 0, {base_opacity + 0.1})'  # Darker red
            }
    elif hasattr(structure, 'gap_type'):
        # Fair Value Gap
        if structure.gap_type == 'bullish':
            return {
                'fill': f'rgba(65, 105, 225, {base_opacity})',  # Royal blue with opacity
                'line': f'rgba(0, 0, 139, {base_opacity + 0.1})'  # Dark blue
            }
        else:
            return {
                'fill': f'rgba(255, 140, 0, {base_opacity})',  # Dark orange with opacity
                'line': f'rgba(255, 69, 0, {base_opacity + 0.1})'  # Red-orange
            }
    else:
        # Default colors
        return {
            'fill': f'rgba(128, 128, 128, {base_opacity})',  # Gray with opacity
            'line': f'rgba(64, 64, 64, {base_opacity + 0.1})'  # Darker gray
        }

def calculate_confluence(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params):
    """
    Calculate confluence score based on SMC structures
    
    Args:
        order_blocks: List of order blocks
        fvgs: List of fair value gaps
        liquidity_zones: List of liquidity zones
        market_structure: Market structure analysis results
        confluence_params: Parameters for confluence calculation
        
    Returns:
        dict: Confluence calculation results
    """
    # Simple implementation
    active_factors = 0
    total_score = 0.5  # Default moderate confidence
    
    # Count active structures
    if order_blocks:
        active_factors += 1
        total_score += 0.1
    
    if fvgs:
        active_factors += 1
        total_score += 0.1
        
    if liquidity_zones:
        active_factors += 1
        total_score += 0.1
        
    if market_structure and market_structure.get('trend') != 'sideways':
        active_factors += 1
        total_score += 0.1
    
    # Cap the score
    total_score = min(total_score, 1.0)
    
    return {
        'total_score': total_score,
        'active_factors': active_factors,
        'factors': {},
        'breakdown': {
            'order_blocks': len(order_blocks) > 0,
            'fvgs': len(fvgs) > 0,
            'liquidity_zones': len(liquidity_zones) > 0,
            'market_structure': market_structure.get('trend', 'sideways') != 'sideways' if market_structure else False
        }
    }

def calculate_confluence_enhanced(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params, data_quality, live_data=None):
    """
    Enhanced version of confluence calculation with data quality adjustments
    
    Args:
        order_blocks: List of order blocks
        fvgs: List of fair value gaps
        liquidity_zones: List of liquidity zones
        market_structure: Market structure analysis results
        confluence_params: Parameters for confluence calculation
        data_quality: Data quality metrics
        live_data: Live market data (optional)
        
    Returns:
        dict: Enhanced confluence calculation results
    """
    try:
        # Base confluence calculation
        base_confluence = calculate_confluence(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params)
        
        # Quality multiplier based on data_quality
        quality_multiplier = 1.0
        min_confidence = 0.25  # Minimum confidence threshold
        
        # Adjust based on data quality
        if data_quality['bars'] >= 180:  # 6+ months of data
            quality_multiplier = 1.05  # Slight boost
        elif data_quality['bars'] < 50:  # Less than 2 months
            quality_multiplier = 0.9  # Reduce confidence
            
        # Apply quality multiplier (capped at 1.0 maximum)
        enhanced_score = min(base_confluence['total_score'] * quality_multiplier, 1.0)
        
        return {
            'total_score': enhanced_score,
            'active_factors': base_confluence['active_factors'],
            'factors': base_confluence.get('factors', {}),
            'data_quality_score': data_quality.get('score', 0.5),
            'quality_multiplier': quality_multiplier,
            'min_confidence': min_confidence,
            'breakdown': base_confluence['breakdown']
        }
        
    except Exception as e:
        logger.error(f"Error in enhanced confluence calculation: {str(e)}")
        # Fallback to standard confluence calculation
        return calculate_confluence(order_blocks, fvgs, liquidity_zones, market_structure, confluence_params)

def get_dynamic_colors(structure, age_factor):
    """
    Generate dynamic colors for chart structures based on age and type
    
    Args:
        structure: The SMC structure (order block, FVG, etc.)
        age_factor: Age factor for color fading
        
    Returns:
        dict: Dictionary with color properties
    """
    base_opacity = max(0.2, min(0.9, 1.0 - age_factor))
    
    if hasattr(structure, 'ob_type'):
        # Order block
        if structure.ob_type == 'bullish':
            return {
                'fill': f'rgba(0, 128, 0, {base_opacity})',  # Green with opacity
                'line': f'rgba(0, 100, 0, {base_opacity + 0.1})'  # Darker green
            }
        else:
            return {
                'fill': f'rgba(178, 34, 34, {base_opacity})',  # Red with opacity
                'line': f'rgba(139, 0, 0, {base_opacity + 0.1})'  # Darker red
            }
    elif hasattr(structure, 'gap_type'):
        # Fair Value Gap
        if structure.gap_type == 'bullish':
            return {
                'fill': f'rgba(65, 105, 225, {base_opacity})',  # Royal blue with opacity
                'line': f'rgba(0, 0, 139, {base_opacity + 0.1})'  # Dark blue
            }
        else:
            return {
                'fill': f'rgba(255, 140, 0, {base_opacity})',  # Dark orange with opacity
                'line': f'rgba(255, 69, 0, {base_opacity + 0.1})'  # Red-orange
            }
    else:
        # Default colors
        return {
            'fill': f'rgba(128, 128, 128, {base_opacity})',  # Gray with opacity
            'line': f'rgba(64, 64, 64, {base_opacity + 0.1})'  # Darker gray
        }

def get_available_stock_symbols():
    """Return a list of stock symbols based on CSV files in the data/stocks directory."""
    stocks_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/stocks'))
    symbols = []
    if os.path.exists(stocks_dir):
        for fname in os.listdir(stocks_dir):
            if fname.endswith('.csv'):
                symbols.append(os.path.splitext(fname)[0])
    return sorted(symbols)

def generate_smc_predictions(current_price, market_structure, confluence, order_blocks, fvgs, premium_discount):
    """Generate predictions based on SMC analysis."""
    predictions = []

    # Get market trend and strength
    trend = market_structure.get('trend', 'neutral')
    strength = market_structure.get('strength', 0.5)
    confluence_score = confluence.get('total_score', 0.5)

    # Calculate base prediction confidence from confluence
    base_confidence = min(confluence_score * 0.8 + 0.2, 0.95)  # 20-95% range

    # Generate predictions for different horizons
    horizons = [
        ('1D', 1, 0.02),    # 1 day, max 2% move
        ('3D', 3, 0.04),    # 3 days, max 4% move
        ('1W', 7, 0.06),    # 1 week, max 6% move
        ('2W', 14, 0.08)    # 2 weeks, max 8% move
    ]

    for horizon_name, days, max_move in horizons:
        # Calculate prediction based on trend and confluence
        if trend == 'bullish':
            # Bullish prediction
            move_factor = strength * max_move * (confluence_score + 0.3)
            predicted_price = current_price * (1 + move_factor)
            probability_up = min(0.5 + (strength * 0.4), 0.9)

        elif trend == 'bearish':
            # Bearish prediction
            move_factor = strength * max_move * (confluence_score + 0.3)
            predicted_price = current_price * (1 - move_factor)
            probability_up = max(0.5 - (strength * 0.4), 0.1)

        else:
            # Neutral/sideways prediction
            move_factor = 0.01 * days  # Small random walk
            predicted_price = current_price * (1 + (move_factor * (0.5 - confluence_score)))
            probability_up = 0.5

        # Adjust confidence based on horizon (shorter = more confident)
        horizon_confidence = base_confidence * (1 - (days / 30) * 0.2)

        # Add structure-based adjustments
        if order_blocks:
            # Nearby order blocks increase confidence
            nearest_ob = min(order_blocks, key=lambda ob: abs((ob.high + ob.low) / 2 - current_price))
            ob_distance = abs((nearest_ob.high + nearest_ob.low) / 2 - current_price) / current_price
            if ob_distance < 0.05:  # Within 5%
                horizon_confidence *= 1.1

        if premium_discount:
            # Premium/discount zone affects predictions
            if premium_discount.current_zone == 'premium' and trend == 'bearish':
                horizon_confidence *= 1.15  # More confident in bearish from premium
            elif premium_discount.current_zone == 'discount' and trend == 'bullish':
                horizon_confidence *= 1.15  # More confident in bullish from discount

        # Cap confidence
        horizon_confidence = min(horizon_confidence, 0.95)

        predictions.append({
            'time_horizon': horizon_name,
            'predicted_price': predicted_price,
            'confidence': horizon_confidence,
            'probability_up': probability_up,
            'model_type': "SMC"
        })

    # Debugging inputs and outputs
    logger.debug(f"Current Price: {current_price}")
    logger.debug(f"Market Structure: {market_structure}")
    logger.debug(f"Confluence: {confluence}")
    logger.debug(f"Order Blocks: {order_blocks}")
    logger.debug(f"Premium Discount Zone: {premium_discount}")
    logger.debug(f"Generated Predictions: {predictions}")

    logger.info(f"Generated {len(predictions)} SMC-based predictions")
    return predictions

def show_smc_analysis():
    """Enhanced SMC Analysis page with professional styling and comprehensive features"""

    # Page header with enhanced styling
    st.markdown("""
    <div style='background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                padding: 20px; border-radius: 15px; margin-bottom: 20px;
                border: 2px solid #4CAF50;'>
        <h1 style='color: white; text-align: center; margin: 0;'>
            🧠 Smart Money Concepts (SMC) Analysis
        </h1>
        <p style='color: #E3F2FD; text-align: center; margin: 10px 0 0 0; font-size: 18px;'>
            Professional SMC Analysis with Advanced Market Structure Detection
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Check if SMC components are available
    if not SMC_AVAILABLE:
        st.error("❌ SMC components are not available. Please check the installation.")
        st.info("💡 Make sure all required SMC modules are properly installed.")
        return

    # Main analysis interface
    st.markdown("### 📊 Analysis Configuration")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("🎯 Stock Selection")

        # Stock selection with enhanced options
        available_symbols = get_available_stock_symbols()
        stock_options = {k: EGX_STOCKS[k] if k in EGX_STOCKS else k for k in available_symbols}
        if not stock_options:
            st.error("No valid stock data found in data/stocks directory.")
            return
        selected_stock = st.selectbox(
            "Select Stock:",
            options=list(stock_options.keys()),
            format_func=lambda x: f"{x} - {EGX_STOCKS[x]}" if x in EGX_STOCKS else x,
            index=0,
            help="Choose a stock for SMC analysis"
        )

    with col2:
        st.subheader("⏰ Analysis Settings")

        # Time interval selection
        selected_interval = st.selectbox(
            "Timeframe:",
            options=["1D", "1W"],
            index=0,
            help="Select timeframe for SMC analysis"
        )

        # Number of bars for analysis
        bars_count = st.slider(
            "Analysis Period (bars):",
            min_value=100,
            max_value=500,
            value=200,
            step=50,
            help="Number of bars to analyze"
        )

    # Analysis button
    if st.button("🧠 Run SMC Analysis", type="primary", use_container_width=True):
        with st.spinner("🔄 Fetching data and running SMC analysis..."):

            # Fetch TradingView data
            egx_symbol = f"EGX-{selected_stock}"
            price_data = fetch_price_data_for_smc(egx_symbol, selected_interval)

            if price_data is not None:
                # Run SMC analysis
                smc_results = run_smc_analysis(price_data, selected_stock, selected_interval)

                if smc_results:
                    display_smc_results(smc_results, selected_stock, selected_interval, price_data)
                else:
                    st.error("❌ Failed to run SMC analysis")
            else:
                st.error("❌ Failed to fetch price data")

def check_api_status():
    """Check if TradingView API server is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_live_smc_data(symbol):
    """Fetch live data specifically for SMC analysis"""
    try:
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            TRADINGVIEW_API_URL,
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                api_data = stock_data[0]

                # Extract price from the API response
                raw_price = api_data.get('price', 0)

                # Convert from piasters to EGP (divide by 1000)
                current_price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                # Extract additional SMC-relevant data
                pivots = api_data.get('pivots', {})
                oscillators = api_data.get('oscillators', {})
                moving_averages = api_data.get('moving_averages', {})

                return {
                    'symbol': symbol,
                    'current_price': current_price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'pivots': pivots,
                    'oscillators': oscillators,
                    'moving_averages': moving_averages,
                    'market_status': determine_market_status(),
                    'full_data': api_data
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching live SMC data: {str(e)}")
        return None

def determine_market_status():
    """Determine current EGX market status"""
    try:
        from datetime import datetime
        import pytz

        cairo_tz = pytz.timezone('Africa/Cairo')
        now_cairo = datetime.now(cairo_tz)

        # EGX operates Sunday-Thursday, 10:00 AM - 2:30 PM
        is_weekday = now_cairo.weekday() < 4  # Monday=0, Thursday=3
        is_sunday = now_cairo.weekday() == 6  # Sunday=6
        is_trading_day = is_weekday or is_sunday

        current_time = now_cairo.time()
        market_open = current_time >= datetime.strptime("10:00", "%H:%M").time()
        market_close = current_time <= datetime.strptime("14:30", "%H:%M").time()
        is_market_hours = market_open and market_close

        if is_trading_day and is_market_hours:
            return "OPEN"
        elif is_trading_day:
            return "CLOSED_TODAY"
        else:
            return "WEEKEND"

    except Exception as e:
        logger.error(f"Error determining market status: {str(e)}")
        return "UNKNOWN"

def fetch_price_data_for_smc(symbol, interval):
    """Fetch price data from CSV files for enhanced SMC analysis"""
    try:
        # Remove EGX- prefix if present to match CSV filename
        csv_symbol = symbol.replace('EGX-', '')

        # Try multiple possible CSV locations
        csv_paths = [
            f'data/stocks/{csv_symbol}.csv',
            f'data/{csv_symbol}.csv',
            f'data/stocks/{csv_symbol}_new.csv'
        ]

        df = None
        for csv_path in csv_paths:
            if os.path.exists(csv_path):
                logger.info(f"Loading CSV data from: {csv_path}")
                df = pd.read_csv(csv_path)
                break

        if df is None:
            logger.warning(f"No CSV data found for {csv_symbol}, falling back to live data")
            return fetch_live_data_fallback(symbol, interval)

        # Convert to OHLCV format for SMC analysis
        df_smc = prepare_csv_for_smc(df, interval)

        if df_smc is not None and len(df_smc) > 50:  # Ensure sufficient data
            logger.info(f"Successfully loaded {len(df_smc)} bars of historical data for SMC analysis")
            return df_smc
        else:
            logger.warning(f"Insufficient CSV data ({len(df_smc) if df_smc is not None else 0} bars), falling back to live data")
            return fetch_live_data_fallback(symbol, interval)

    except Exception as e:
        logger.error(f"Error loading CSV data: {str(e)}")
        return fetch_live_data_fallback(symbol, interval)

def prepare_csv_for_smc(df, interval):
    """Convert CSV data to SMC-compatible OHLCV format"""
    try:
        # Ensure required columns exist
        required_cols = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        if not all(col in df.columns for col in required_cols):
            logger.error(f"Missing required columns. Available: {list(df.columns)}")
            return None

        # Convert Date column to datetime
        df['Date'] = pd.to_datetime(df['Date'])

        # Sort by date
        df = df.sort_values('Date')

        # Create SMC-compatible DataFrame
        df_smc = pd.DataFrame({
            'open': df['Open'].astype(float),
            'high': df['High'].astype(float),
            'low': df['Low'].astype(float),
            'close': df['Close'].astype(float),
            'volume': df['Volume'].astype(int)
        })

        # Set date as index
        df_smc.index = df['Date']

        # Filter based on interval if needed (for now, use daily data)
        # Future enhancement: implement interval filtering

        # Get recent data for analysis (last 6 months for better patterns)
        if len(df_smc) > 180:
            df_smc = df_smc.tail(180)  # Last 6 months

        logger.info(f"Prepared SMC data: {len(df_smc)} bars from {df_smc.index[0].date()} to {df_smc.index[-1].date()}")
        return df_smc

    except Exception as e:
        logger.error(f"Error preparing CSV data for SMC: {str(e)}")
        return None

def fetch_live_data_fallback(symbol, interval):
    """Fallback to live data if CSV data is not available with API server integration"""
    try:
        # Check if API server is available
        api_status = check_api_status()

        if api_status:
            logger.info("Using TradingView API server for live data fallback")
            # Get current price from TradingView API
            payload = {
                "pairs": [symbol],
                "intervals": [interval]
            }

            response = requests.post(
                TRADINGVIEW_API_URL,
                json=payload,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                stock_data = data.get(symbol, [])

                if stock_data:
                    current_price = stock_data[0].get('price', 80.0)
                    logger.info(f"Using live price {current_price} from API server for fallback data generation")

                    # Generate realistic OHLCV data for SMC analysis
                    df = generate_ohlcv_data(current_price, 200)
                    return df

        # Fallback to direct scraping if API server is not available
        logger.info("API server not available, using direct scraping for fallback")
        try:
            from scrapers.price_scraper import PriceScraper
            scraper = PriceScraper(source="tradingview")
            try:
                # Extract symbol without EGX- prefix for scraper
                clean_symbol = symbol.replace('EGX-', '')
                price_data = scraper.get_price(clean_symbol)

                if price_data and isinstance(price_data, dict):
                    current_price = price_data.get('price', 80.0)
                    logger.info(f"Using scraped price {current_price} for fallback data generation")

                    # Generate realistic OHLCV data for SMC analysis
                    df = generate_ohlcv_data(current_price, 200)
                    return df
            finally:
                scraper.close_driver()
        except Exception as scraper_error:
            logger.warning(f"Direct scraping failed: {str(scraper_error)}")

        # Ultimate fallback with default price
        logger.info("Using default price for fallback data generation")
        df = generate_ohlcv_data(80.0, 200)  # Default price
        return df

    except Exception as e:
        logger.error(f"Error in live data fallback: {str(e)}")
        # Ultimate fallback
        return generate_ohlcv_data(80.0, 200)

def generate_ohlcv_data(current_price, bars):
    """Generate realistic OHLCV data for SMC analysis"""
    np.random.seed(42)  # For consistent data
    
    # Generate price movements working backwards from current price
    volatility = 0.02  # 2% daily volatility
    returns = np.random.normal(0, volatility, bars)
    
    # Start from current price and work backwards
    prices = [current_price]
    for i in range(bars - 1):
        prev_price = prices[0] / (1 + returns[bars - 1 - i])
        prices.insert(0, max(prev_price, 1.0))
    
    # Create OHLCV data
    data = []
    for i in range(bars):
        close = prices[i]
        intraday_vol = close * 0.015  # 1.5% intraday volatility
        
        high = close + np.random.uniform(0, intraday_vol)
        low = close - np.random.uniform(0, intraday_vol)
        open_price = low + np.random.uniform(0, high - low)
        
        # Ensure OHLC relationships
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        volume = np.random.randint(100000, 2000000)
        
        data.append({
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.index = pd.date_range(end=datetime.now(), periods=bars, freq='D')
    
    return df

def run_smc_analysis(df, symbol, interval):
    """Run comprehensive SMC analysis with live data integration"""
    try:
        # Get EGX parameters
        params = get_stock_specific_parameters(symbol)

        # Try to get live data for enhanced analysis
        live_data = None
        if check_api_status():
            live_data = fetch_live_smc_data(symbol)
            if live_data:
                current_price = live_data['current_price']
                logger.info(f"✅ Using live price {current_price:.2f} EGP from API for SMC analysis of {symbol}")
                logger.info(f"📊 Market Status: {live_data['market_status']}")
            else:
                current_price = float(df['close'].iloc[-1])
                logger.info(f"📊 Using CSV price {current_price:.2f} EGP for SMC analysis of {symbol}")
        else:
            current_price = float(df['close'].iloc[-1])
            logger.info(f"⚠️ API not available, using CSV price {current_price:.2f} EGP for SMC analysis of {symbol}")

        # Enhanced data quality check
        data_quality = assess_data_quality(df)
        logger.info(f"SMC Data Quality: {data_quality['score']:.1%} ({data_quality['bars']} bars, {data_quality['timespan']} days)")

        # Detect SMC structures with enhanced parameters and live data context
        # Use original functions with live data enhancement
        order_blocks = detect_order_blocks(
            df,
            lookback=params['order_blocks']['lookback_periods'],
            min_strength=params['order_blocks']['min_strength']
        )

        fvgs = detect_fvg(
            df,
            min_gap_size=params['fvg']['min_gap_size_pct']
        )

        liquidity_zones = detect_liquidity_zones(
            df,
            lookback=params['liquidity_zones']['lookback_periods']
        )

        # Enhance structures with live data context
        if live_data:
            order_blocks = enhance_structures_with_live_data(order_blocks, live_data, 'order_blocks')
            fvgs = enhance_structures_with_live_data(fvgs, live_data, 'fvgs')
            liquidity_zones = enhance_structures_with_live_data(liquidity_zones, live_data, 'liquidity_zones')

        # Filter active structures with live market context
        active_order_blocks = filter_active_structures_enhanced(
            order_blocks, current_price, params['distance_filters']['order_blocks_pct'], live_data
        )

        active_fvgs = filter_active_structures_enhanced(
            fvgs, current_price, params['distance_filters']['fvg_pct'], live_data
        )

        active_liquidity_zones = filter_active_structures_enhanced(
            liquidity_zones, current_price, params['distance_filters']['liquidity_zones_pct'], live_data
        )

        # Calculate market structure with enhanced analysis and live data
        market_structure = analyze_market_structure_enhanced(df, live_data)

        # Calculate confluence with data quality weighting and live market context
        confluence = calculate_confluence_enhanced(
            active_order_blocks, active_fvgs, active_liquidity_zones,
            market_structure, params['confluence'], data_quality, live_data
        )

        # Advanced SMC Features
        bos_events = detect_break_of_structure(df, lookback=20)
        liquidity_sweeps = detect_liquidity_sweeps(df, active_liquidity_zones, lookback=10)
        premium_discount = calculate_premium_discount_zones(df, period=50)

        # Multi-timeframe Analysis
        mtf_analyses = analyze_multiple_timeframes(df, symbol)
        mtf_signal = generate_multi_timeframe_signal(mtf_analyses, current_price) if mtf_analyses else None

        # AI Pattern Recognition
        pattern_recognizer = AIPatternRecognizer()
        ai_patterns = pattern_recognizer.detect_patterns(df, {
            'order_blocks': active_order_blocks,
            'fvgs': active_fvgs,
            'liquidity_zones': active_liquidity_zones,
            'bos_events': bos_events,
            'liquidity_sweeps': liquidity_sweeps,
            'premium_discount': premium_discount,
            'confluence': confluence,
            'market_structure': market_structure
        })

        # Quick Price Analysis with SMC-based predictions
        try:
            # Calculate simple trend analysis
            recent_change = (current_price - df['close'].iloc[-5]) / df['close'].iloc[-5] * 100
            volatility = df['close'].pct_change().tail(20).std() * 100

            # Generate SMC-based predictions
            predictions = generate_smc_predictions(
                current_price, market_structure, confluence,
                active_order_blocks, active_fvgs, premium_discount
            )

        except Exception as e:
            logger.error(f"Error in quick analysis: {str(e)}")
            predictions = []
            
        # Create results dictionary for debugging
        results = {
            'order_blocks': active_order_blocks,
            'fvgs': active_fvgs,
            'liquidity_zones': active_liquidity_zones, 
            'market_structure': market_structure,
            'confluence': confluence if 'confluence' in locals() else {},
            'current_price': current_price
        }
            
        # Debugging intermediate outputs
        logger.debug(f"Order Blocks: {results['order_blocks']}")
        logger.debug(f"Market Structure: {results['market_structure']}")
        logger.debug(f"Confluence: {results['confluence']}")
        logger.debug(f"Current Price: {results['current_price']}")

        return {
            'symbol': symbol,
            'interval': interval,
            'current_price': current_price,
            'order_blocks': active_order_blocks,
            'fvgs': active_fvgs,
            'liquidity_zones': active_liquidity_zones,
            'market_structure': market_structure,
            'confluence': confluence,
            'bos_events': bos_events,
            'liquidity_sweeps': liquidity_sweeps,
            'premium_discount': premium_discount,
            'mtf_analyses': mtf_analyses,
            'mtf_signal': mtf_signal,
            'ai_patterns': ai_patterns,
            'predictions': predictions,
            'parameters': params,
            'live_data': live_data,  # Include live data context
            'data_quality': data_quality,  # Include data quality assessment
            'live_enhanced': live_data is not None  # Flag for live enhancement
        }
        
    except Exception as e:
        logger.error(f"Error in SMC analysis: {str(e)}")
        return None

def filter_active_structures(structures, current_price, max_distance_pct):
    """Filter structures within distance threshold"""
    active = []
    max_distance = current_price * (max_distance_pct / 100)

    for structure in structures:
        if hasattr(structure, 'high') and hasattr(structure, 'low'):
            # Check if structure is within distance
            distance_to_high = abs(structure.high - current_price)
            distance_to_low = abs(structure.low - current_price)
            min_distance = min(distance_to_high, distance_to_low)

            if min_distance <= max_distance:
                active.append(structure)

    return active

def enhance_structures_with_live_data(structures, live_data, structure_type):
    """Enhance SMC structures with live market data context"""
    try:
        if not live_data or not structures:
            return structures

        current_price = live_data['current_price']
        market_status = live_data.get('market_status', 'UNKNOWN')
        pivots = live_data.get('pivots', {})
        oscillators = live_data.get('oscillators', {})

        enhanced_structures = []

        for structure in structures:
            # Create enhanced copy of structure
            enhanced_structure = structure

            # Add live market context
            if hasattr(structure, 'strength'):
                # Enhance strength based on live market conditions
                live_strength_multiplier = calculate_live_strength_multiplier(
                    structure, current_price, market_status, pivots, oscillators
                )
                enhanced_structure.strength = min(structure.strength * live_strength_multiplier, 1.0)

            # Add live market relevance score
            if hasattr(structure, 'high') and hasattr(structure, 'low'):
                relevance_score = calculate_structure_relevance(
                    structure, current_price, market_status, structure_type
                )
                enhanced_structure.live_relevance = relevance_score
                enhanced_structure.market_status = market_status
                enhanced_structure.live_enhanced = True

            enhanced_structures.append(enhanced_structure)

        # Sort by live relevance if available
        enhanced_structures.sort(
            key=lambda x: getattr(x, 'live_relevance', 0.5) * getattr(x, 'strength', 0.5),
            reverse=True
        )

        logger.info(f"Enhanced {len(enhanced_structures)} {structure_type} with live data context")
        return enhanced_structures

    except Exception as e:
        logger.error(f"Error enhancing structures with live data: {str(e)}")
        return structures

def calculate_live_strength_multiplier(structure, current_price, market_status, pivots, oscillators):
    """Calculate strength multiplier based on live market conditions"""
    try:
        multiplier = 1.0

        # Market status boost
        if market_status == "OPEN":
            multiplier *= 1.1  # 10% boost during market hours
        elif market_status == "CLOSED_TODAY":
            multiplier *= 0.95  # 5% reduction when market closed

        # Pivot alignment boost
        if pivots and hasattr(structure, 'high') and hasattr(structure, 'low'):
            structure_mid = (structure.high + structure.low) / 2

            # Handle different pivot data structures
            if isinstance(pivots, list) and len(pivots) > 0:
                pivot_data = pivots[0] if isinstance(pivots[0], dict) else {}
            elif isinstance(pivots, dict):
                pivot_data = pivots
            else:
                pivot_data = {}

            # Check alignment with pivot levels
            pivot_levels = [
                pivot_data.get('R3', 0), pivot_data.get('R2', 0), pivot_data.get('R1', 0),
                pivot_data.get('PP', 0),
                pivot_data.get('S1', 0), pivot_data.get('S2', 0), pivot_data.get('S3', 0)
            ]

            # Convert from piasters if needed
            pivot_levels = [p/1000 if p > 1000 else p for p in pivot_levels if p > 0]

            # Check if structure aligns with any pivot level (within 2%)
            for pivot_level in pivot_levels:
                if abs(structure_mid - pivot_level) / pivot_level < 0.02:
                    multiplier *= 1.15  # 15% boost for pivot alignment
                    break

        # Oscillator confirmation
        if oscillators:
            # Handle different oscillator data structures
            if isinstance(oscillators, list) and len(oscillators) > 0:
                osc_data = oscillators[0] if isinstance(oscillators[0], dict) else {}
            elif isinstance(oscillators, dict):
                osc_data = oscillators
            else:
                osc_data = {}

            osc_buy = osc_data.get('buy', 0)
            osc_sell = osc_data.get('sell', 0)
            total_osc = osc_buy + osc_sell

            if total_osc > 0:
                # Boost structures that align with oscillator signals
                if hasattr(structure, 'block_type'):
                    if structure.block_type == 'bullish' and osc_buy > osc_sell:
                        multiplier *= 1.05  # 5% boost for aligned bullish signals
                    elif structure.block_type == 'bearish' and osc_sell > osc_buy:
                        multiplier *= 1.05  # 5% boost for aligned bearish signals

        return min(multiplier, 1.3)  # Cap at 30% boost

    except Exception as e:
        logger.error(f"Error calculating live strength multiplier: {str(e)}")
        return 1.0

def calculate_structure_relevance(structure, current_price, market_status, structure_type):
    """Calculate structure relevance based on current market conditions"""
    try:
        relevance = 0.5  # Base relevance

        # Distance relevance (closer = more relevant)
        if hasattr(structure, 'high') and hasattr(structure, 'low'):
            structure_mid = (structure.high + structure.low) / 2
            distance_pct = abs(structure_mid - current_price) / current_price

            if distance_pct <= 0.02:  # Within 2%
                relevance += 0.3
            elif distance_pct <= 0.05:  # Within 5%
                relevance += 0.2
            elif distance_pct <= 0.10:  # Within 10%
                relevance += 0.1

        # Market status relevance
        if market_status == "OPEN":
            relevance += 0.1  # More relevant during trading hours

        # Structure type specific relevance
        if structure_type == 'order_blocks':
            # Order blocks are more relevant when untested
            if hasattr(structure, 'tested') and not structure.tested:
                relevance += 0.1
        elif structure_type == 'fvgs':
            # FVGs are more relevant when unfilled
            if hasattr(structure, 'filled') and not structure.filled:
                relevance += 0.1
        elif structure_type == 'liquidity_zones':
            # Liquidity zones are more relevant when unswept
            if hasattr(structure, 'swept') and not structure.swept:
                relevance += 0.1

        return min(relevance, 1.0)  # Cap at 1.0

    except Exception as e:
        logger.error(f"Error calculating structure relevance: {str(e)}")
        return 0.5

def calculate_live_market_multiplier(live_data, market_structure):
    """Calculate multiplier based on live market conditions"""
    try:
        multiplier = 1.0

        market_status = live_data.get('market_status', 'UNKNOWN')
        oscillators = live_data.get('oscillators', {})
        moving_averages = live_data.get('moving_averages', {})

        # Market status multiplier
        if market_status == "OPEN":
            multiplier *= 1.15  # 15% boost during active trading
        elif market_status == "CLOSED_TODAY":
            multiplier *= 0.95  # 5% reduction when market closed today
        elif market_status == "WEEKEND":
            multiplier *= 0.85  # 15% reduction during weekend

        # Oscillator alignment multiplier
        if oscillators:
            # Handle different oscillator data structures
            if isinstance(oscillators, list) and len(oscillators) > 0:
                osc_data = oscillators[0] if isinstance(oscillators[0], dict) else {}
            elif isinstance(oscillators, dict):
                osc_data = oscillators
            else:
                osc_data = {}

            osc_buy = osc_data.get('buy', 0)
            osc_sell = osc_data.get('sell', 0)
            total_osc = osc_buy + osc_sell

            if total_osc > 0:
                trend = market_structure.get('trend', 'neutral')

                # Boost when oscillators align with market structure
                if trend == 'bullish' and osc_buy > osc_sell:
                    multiplier *= 1.1  # 10% boost for aligned bullish signals
                elif trend == 'bearish' and osc_sell > osc_buy:
                    multiplier *= 1.1  # 10% boost for aligned bearish signals
                elif trend != 'neutral' and abs(osc_buy - osc_sell) < 2:
                    multiplier *= 0.9  # 10% reduction for conflicting signals

        # Moving average alignment multiplier
        if moving_averages:
            # Handle different moving average data structures
            if isinstance(moving_averages, list) and len(moving_averages) > 0:
                ma_data = moving_averages[0] if isinstance(moving_averages[0], dict) else {}
            elif isinstance(moving_averages, dict):
                ma_data = moving_averages
            else:
                ma_data = {}

            ma_buy = ma_data.get('buy', 0)
            ma_sell = ma_data.get('sell', 0)
            total_ma = ma_buy + ma_sell

            if total_ma > 0:
                trend = market_structure.get('trend', 'neutral')

                # Strong boost for MA alignment (more reliable than oscillators)
                if trend == 'bullish' and ma_buy > ma_sell:
                    multiplier *= 1.15  # 15% boost for aligned bullish MA signals
                elif trend == 'bearish' and ma_sell > ma_buy:
                    multiplier *= 1.15  # 15% boost for aligned bearish MA signals

        # Cap the multiplier
        return min(max(multiplier, 0.7), 1.3)  # Between 0.7 and 1.3

    except Exception as e:
        logger.error(f"Error calculating live market multiplier: {str(e)}")
        return 1.0

def filter_active_structures_enhanced(structures, current_price, max_distance_pct, live_data=None):
    """Enhanced filtering with live market context"""
    try:
        # Start with basic filtering
        active = filter_active_structures(structures, current_price, max_distance_pct)

        if not live_data or not active:
            return active

        market_status = live_data.get('market_status', 'UNKNOWN')

        # Enhanced filtering based on live market conditions
        enhanced_active = []

        for structure in active:
            # Base inclusion criteria
            include_structure = True

            # Market hours filtering
            if market_status == "WEEKEND":
                # During weekends, only include very strong structures
                if hasattr(structure, 'strength') and structure.strength < 0.7:
                    include_structure = False

            # Live relevance filtering
            if hasattr(structure, 'live_relevance'):
                if structure.live_relevance < 0.3:  # Low relevance threshold
                    include_structure = False

            if include_structure:
                enhanced_active.append(structure)

        # Sort by combined score (strength * relevance)
        enhanced_active.sort(
            key=lambda x: (getattr(x, 'strength', 0.5) * getattr(x, 'live_relevance', 0.5)),
            reverse=True
        )

        # Limit to top structures to avoid clutter
        max_structures = 5 if market_status == "OPEN" else 3
        enhanced_active = enhanced_active[:max_structures]

        logger.info(f"Enhanced filtering: {len(enhanced_active)}/{len(active)} structures selected for market status: {market_status}")
        return enhanced_active

    except Exception as e:
        logger.error(f"Error in enhanced structure filtering: {str(e)}")
        return filter_active_structures(structures, current_price, max_distance_pct)

def assess_data_quality(df):
    """Assess the quality of data for SMC analysis"""
    try:
        bars = len(df)
        timespan = (df.index[-1] - df.index[0]).days

        # Calculate quality score based on data characteristics
        score = 0.0

        # Data quantity (more bars = better)
        if bars >= 180:  # 6+ months
            score += 0.4
        elif bars >= 90:  # 3+ months
            score += 0.3
        elif bars >= 50:  # 1.5+ months
            score += 0.2
        else:
            score += 0.1

        # Data consistency (no missing values)
        if not df.isnull().any().any():
            score += 0.2

        # Price volatility (reasonable range)
        price_std = df['close'].std()
        price_mean = df['close'].mean()
        cv = price_std / price_mean if price_mean > 0 else 0
        if 0.01 <= cv <= 0.1:  # 1-10% coefficient of variation
            score += 0.2

        # Volume data availability
        if 'volume' in df.columns and df['volume'].sum() > 0:
            score += 0.1

        # Recent data (within last 30 days)
        days_since_last = (datetime.now() - df.index[-1]).days
        if days_since_last <= 7:
            score += 0.1
        elif days_since_last <= 30:
            score += 0.05

        return {
            'score': min(score, 1.0),
            'bars': bars,
            'timespan': timespan,
            'days_since_last': days_since_last,
            'coefficient_variation': cv
        }

    except Exception as e:
        logger.error(f"Error assessing data quality: {str(e)}")
        return {'score': 0.5, 'bars': len(df), 'timespan': 0, 'days_since_last': 999, 'coefficient_variation': 0}

def analyze_market_structure(df):
    """Analyze market structure (trend, swing points, etc.)"""
    if len(df) < 20:
        return {'trend': 'unknown', 'strength': 0}

    # Simple trend analysis using moving averages
    short_ma = df['close'].rolling(10).mean().iloc[-1]
    long_ma = df['close'].rolling(20).mean().iloc[-1]
    current_price = df['close'].iloc[-1]

    if current_price > short_ma > long_ma:
        trend = 'bullish'
        strength = min((current_price - long_ma) / long_ma * 10, 1.0)
    elif current_price < short_ma < long_ma:
        trend = 'bearish'
        strength = min((long_ma - current_price) / long_ma * 10, 1.0)
    else:
        trend = 'sideways'
        strength = 0.5

    return {
        'trend': trend,
        'strength': strength,
        'short_ma': short_ma,
        'long_ma': long_ma
    }

def analyze_market_structure_enhanced(df, live_data=None):
    """Enhanced market structure analysis with live data integration"""
    try:
        if len(df) < 50:
            return analyze_market_structure(df)  # Fallback to simple analysis

        # Use live price if available
        if live_data:
            current_price = live_data['current_price']
            logger.info(f"Using live price {current_price:.2f} for market structure analysis")
        else:
            current_price = df['close'].iloc[-1]

        # Multiple moving averages for better trend detection
        ma_5 = df['close'].rolling(5).mean().iloc[-1]
        ma_10 = df['close'].rolling(10).mean().iloc[-1]
        ma_20 = df['close'].rolling(20).mean().iloc[-1]
        ma_50 = df['close'].rolling(50).mean().iloc[-1] if len(df) >= 50 else ma_20

        # Trend strength calculation
        ma_alignment_score = 0
        if current_price > ma_5 > ma_10 > ma_20:
            ma_alignment_score = 1.0  # Strong bullish
        elif current_price > ma_5 > ma_10:
            ma_alignment_score = 0.7  # Moderate bullish
        elif current_price > ma_10:
            ma_alignment_score = 0.4  # Weak bullish
        elif current_price < ma_5 < ma_10 < ma_20:
            ma_alignment_score = -1.0  # Strong bearish
        elif current_price < ma_5 < ma_10:
            ma_alignment_score = -0.7  # Moderate bearish
        elif current_price < ma_10:
            ma_alignment_score = -0.4  # Weak bearish
        else:
            ma_alignment_score = 0  # Sideways

        # Determine trend and strength
        if ma_alignment_score > 0.3:
            trend = 'bullish'
            strength = ma_alignment_score
        elif ma_alignment_score < -0.3:
            trend = 'bearish'
            strength = abs(ma_alignment_score)
        else:
            trend = 'sideways'
            strength = 0.5

        # Additional metrics
        price_momentum = (current_price - ma_20) / ma_20 if ma_20 > 0 else 0
        volatility = df['close'].rolling(20).std().iloc[-1] / current_price if current_price > 0 else 0

        return {
            'trend': trend,
            'strength': strength,
            'ma_alignment_score': ma_alignment_score,
            'price_momentum': price_momentum,
            'volatility': volatility,
            'short_ma': ma_10,
            'long_ma': ma_20,
            'ma_5': ma_5,
            'ma_50': ma_50
        }

    except Exception as e:
        logger.error(f"Error in enhanced market structure analysis: {str(e)}")
        return analyze_market_structure(df)  # Fallback to simple analysis

def display_smc_results(results, symbol, interval, df):
    """
    Display SMC analysis results in the Streamlit app
    
    Args:
        results: Dictionary with SMC analysis results
        symbol: Stock symbol
        interval: Analysis interval/timeframe
        df: Price data DataFrame
    """
    st.subheader(f"📊 SMC Analysis for {symbol} ({interval})")
    
    # Split into columns for layout
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Simplified display for order blocks
        st.subheader("⬜ Order Blocks")
        if results.get('order_blocks', []):
            for i, ob in enumerate(results['order_blocks'][:3]):
                st.write(f"OB-{i+1}: {ob.low:.2f} - {ob.high:.2f} ({ob.ob_type})")
        else:
            st.info("No active order blocks detected")
            
        # Simplified display for FVGs
        st.subheader("⚡ Fair Value Gaps")
        if results.get('fvgs', []):
            for i, fvg in enumerate(results['fvgs'][:3]):
                st.write(f"FVG-{i+1}: {fvg.low:.2f} - {fvg.high:.2f} ({fvg.gap_type})")
        else:
            st.info("No active fair value gaps detected")
    
    with col2:
        # Confluence score
        if 'confluence' in results:
            conf_score = results['confluence'].get('total_score', 0.5)
            st.metric("Confluence Score", f"{conf_score:.1%}")
            
        # Current price
        if 'current_price' in results:
            st.metric("Current Price", f"{results['current_price']:.2f}")
            
    # Display risk management if available
    if 'risk_management' in results:
        st.subheader("🛡️ Risk Management")
        risk = results['risk_management']
        st.write(f"Stop Loss: {risk.get('stop_loss', 'N/A')}")
        st.write(f"Take Profit: {risk.get('take_profit', 'N/A')}")
        st.write(f"Risk-Reward Ratio: {risk.get('risk_reward_ratio', 'N/A')}")