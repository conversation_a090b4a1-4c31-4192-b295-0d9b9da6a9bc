"""
CSV Converter - Generate Historical Data CSV Files
This tool converts current stock data into comprehensive historical CSV files for technical analysis.
"""

import streamlit as st
import pandas as pd
import os
import sys
from typing import List, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def show_csv_converter():
    """Main CSV Converter interface"""
    
    st.title("📊 CSV Converter")
    st.markdown("Convert current stock data into comprehensive historical CSV files for technical analysis.")
    
    # Add some styling
    st.markdown("""
    <style>
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        margin: 1rem 0;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Test API connection first
    st.header("🔌 API Connection Status")
    
    try:
        from app.utils.historical_data_downloader import HistoricalDataDownloader
        downloader = HistoricalDataDownloader()
        
        # Test API connection
        api_test = downloader.test_api_connection()
        
        if api_test['connected']:
            st.success(f"✅ API Server Connected: {api_test['message']}")
        else:
            st.error(f"❌ API Server Not Available: {api_test['message']}")
            st.info("💡 **How to fix:**")
            st.info("1. Start the TradingView API server: `cd scrapers && python api_server.py`")
            st.info("2. Ensure it's running on http://127.0.0.1:8000")
            st.info("3. Check firewall settings")
            return
            
    except ImportError as e:
        st.error(f"❌ Import Error: {str(e)}")
        return
    except Exception as e:
        st.error(f"❌ Unexpected Error: {str(e)}")
        return
    
    st.markdown("---")
    
    # Main conversion interface
    st.header("📈 Stock Data Converter")
    
    # Input section
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 🎯 Stock Selection")
        stock_symbol = st.text_input(
            "Enter Stock Symbol:",
            value="COMI",
            help="Enter EGX stock symbol (e.g., COMI, TMGH, ABUK)"
        ).upper().strip()
        
        # Validate stock symbol
        if stock_symbol and len(stock_symbol) < 2:
            st.warning("⚠️ Please enter a valid stock symbol")
        
    with col2:
        st.markdown("#### ⏰ Timeframe Selection")
        timeframe_years = st.selectbox(
            "Years of historical data:",
            options=[0.5, 1, 2, 3, 5],
            index=2,  # Default to 2 years
            format_func=lambda x: f"{x} year{'s' if x != 1 else ''}"
        )
    
    # Intervals selection
    st.markdown("#### 📊 Data Intervals")
    
    col1, col2, col3 = st.columns(3)
    
    intervals = []
    with col1:
        if st.checkbox("📅 Daily (1D)", value=True):
            intervals.append("1D")
    with col2:
        if st.checkbox("📅 Weekly (1W)", value=True):
            intervals.append("1W")
    with col3:
        if st.checkbox("📅 Monthly (1M)", value=False):
            intervals.append("1M")
    
    if not intervals:
        st.warning("⚠️ Please select at least one interval")
        return
    
    # Information display
    st.markdown("#### 📋 Conversion Details")
    
    info_col1, info_col2, info_col3 = st.columns(3)
    
    with info_col1:
        st.info(f"**Stock:** {stock_symbol}")
        st.info(f"**Timeframe:** {timeframe_years} years")
    
    with info_col2:
        st.info(f"**Intervals:** {', '.join(intervals)}")
        st.info(f"**Estimated Days:** ~{int(timeframe_years * 250)}")
    
    with info_col3:
        st.info(f"**Output:** CSV file")
        st.info(f"**Location:** data/stocks/{stock_symbol}.csv")
    
    st.markdown("---")
    
    # Conversion buttons
    st.header("🚀 Generate CSV File")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🧪 Test Current Data", type="secondary"):
            test_current_data(stock_symbol, intervals, downloader)
    
    with col2:
        if st.button("📊 Generate Historical CSV", type="primary"):
            generate_historical_csv(stock_symbol, timeframe_years, intervals, downloader)
    
    # Show existing CSV files
    show_existing_csv_files()

def test_current_data(stock_symbol: str, intervals: List[str], downloader):
    """Test current data download for the stock"""
    
    if not stock_symbol:
        st.warning("⚠️ Please enter a stock symbol")
        return
    
    with st.spinner(f"🧪 Testing current data for {stock_symbol}..."):
        try:
            result = downloader.download_historical_data(
                stock_symbol, 
                intervals=intervals, 
                save_to_csv=False
            )
            
            if result:
                st.success(f"✅ Current data test successful for {stock_symbol}")
                
                # Show current prices
                st.markdown("**💰 Current Prices:**")
                for interval, data in result.items():
                    price = data.get('current_price', 0)
                    st.markdown(f"• **{interval}**: {price:.2f} EGP")
                
                st.info("🎯 Stock is ready for historical data generation!")
                
            else:
                st.error(f"❌ Current data test failed for {stock_symbol}")
                st.info("💡 **Possible reasons:**")
                st.info("• Stock symbol not found on EGX")
                st.info("• API server connection issues")
                st.info("• Invalid stock symbol format")
                
        except Exception as e:
            st.error(f"❌ Test failed: {str(e)}")

def generate_historical_csv(stock_symbol: str, timeframe_years: float, intervals: List[str], downloader):
    """Generate historical CSV file for the stock"""
    
    if not stock_symbol:
        st.warning("⚠️ Please enter a stock symbol")
        return
    
    with st.spinner(f"📊 Generating {timeframe_years} years of historical data for {stock_symbol}..."):
        try:
            # Show progress steps
            progress_container = st.container()
            
            with progress_container:
                st.info("🔌 Step 1: Testing API connection...")
                st.info("📥 Step 2: Fetching current price data...")
                st.info("🔄 Step 3: Generating historical data...")
                st.info("💾 Step 4: Saving CSV file...")
            
            result = downloader.download_and_generate_historical_data(
                stock_symbol,
                timeframe_years=int(timeframe_years),
                intervals=intervals
            )
            
            progress_container.empty()
            
            if result:
                st.success(f"🎉 Successfully generated historical CSV for {stock_symbol}!")
                
                # Show detailed results
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown("**📊 Generated Data:**")
                    st.markdown(f"• **Symbol**: {result['symbol']}")
                    st.markdown(f"• **Current Price**: {result['current_price']:.2f} EGP")
                    st.markdown(f"• **Historical Days**: {result['historical_days']:,}")
                
                with col2:
                    st.markdown("**📁 File Details:**")
                    st.markdown(f"• **Date Range**: {result['date_range']}")
                    st.markdown(f"• **Price Range**: {result['price_range']}")
                    
                    # File info
                    if os.path.exists(result['csv_file']):
                        file_size = os.path.getsize(result['csv_file'])
                        file_size_mb = file_size / (1024 * 1024)
                        st.markdown(f"• **File Size**: {file_size_mb:.2f} MB")
                        st.markdown(f"• **Location**: `{os.path.basename(result['csv_file'])}`")
                
                # Show sample data
                if os.path.exists(result['csv_file']):
                    try:
                        sample_df = pd.read_csv(result['csv_file']).tail(10)
                        st.markdown("**📋 Sample Data (Last 10 Days):**")
                        st.dataframe(sample_df, use_container_width=True)
                        
                        st.success("✅ CSV file is ready for use in AI Advisor!")
                        st.info("💡 You can now use this stock in the AI Advisor for full technical analysis.")
                        
                    except Exception as sample_error:
                        st.warning(f"Could not display sample data: {str(sample_error)}")
                
            else:
                st.error(f"❌ Failed to generate historical CSV for {stock_symbol}")
                st.markdown("**🔍 Troubleshooting:**")
                st.info("• Verify the stock symbol exists on EGX")
                st.info("• Check API server logs for errors")
                st.info("• Try with a different stock symbol")
                
        except Exception as e:
            st.error(f"❌ Error generating CSV: {str(e)}")
            with st.expander("🔍 Error Details"):
                st.code(str(e))

def show_existing_csv_files():
    """Show existing CSV files in the data directory"""
    
    st.markdown("---")
    st.header("📁 Existing CSV Files")
    
    data_dir = "data/stocks"
    
    if os.path.exists(data_dir):
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        
        if csv_files:
            st.success(f"✅ Found {len(csv_files)} CSV files")
            
            # Show files in a nice format
            for i, filename in enumerate(sorted(csv_files)):
                file_path = os.path.join(data_dir, filename)
                file_size = os.path.getsize(file_path)
                file_size_mb = file_size / (1024 * 1024)
                
                symbol = filename.replace('.csv', '')
                
                col1, col2, col3 = st.columns([2, 1, 1])
                
                with col1:
                    st.markdown(f"**{symbol}**")
                with col2:
                    st.markdown(f"{file_size_mb:.2f} MB")
                with col3:
                    if st.button("👁️ Preview", key=f"preview_{i}"):
                        show_csv_preview(file_path, symbol)
        else:
            st.info("📂 No CSV files found. Generate some using the converter above!")
    else:
        st.info("📂 Data directory doesn't exist yet. It will be created when you generate your first CSV file.")

def show_csv_preview(file_path: str, symbol: str):
    """Show preview of a CSV file"""
    
    try:
        df = pd.read_csv(file_path)
        
        st.markdown(f"**📊 Preview of {symbol}.csv**")
        st.markdown(f"• **Rows**: {len(df):,}")
        st.markdown(f"• **Columns**: {', '.join(df.columns)}")
        st.markdown(f"• **Date Range**: {df['Date'].min()} to {df['Date'].max()}")
        st.markdown(f"• **Price Range**: {df['Close'].min():.2f} - {df['Close'].max():.2f} EGP")
        
        # Show sample data
        st.dataframe(df.head(10), use_container_width=True)
        
    except Exception as e:
        st.error(f"Error reading CSV file: {str(e)}")

if __name__ == "__main__":
    show_csv_converter()
