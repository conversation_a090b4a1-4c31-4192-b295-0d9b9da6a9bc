"""
Trade Ideas Generator Page
AI-Powered Trade Idea Generation based on Technical Analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Configure logging
logger = logging.getLogger(__name__)

def check_api_server_status():
    """Check if TradingView API server is running"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_price_from_api_server(symbol: str) -> Optional[Dict]:
    """Fetch price data from TradingView API server"""
    try:
        import requests
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                # Extract price from the API response
                raw_price = stock_data[0].get('price', 0)

                # Convert from piasters to EGP (divide by 1000)
                # API returns prices in piasters (81500 = 81.50 EGP)
                price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                return {
                    'symbol': symbol,
                    'price': price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'api_data': stock_data[0]  # Store full API data for enhanced analysis
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching from API server: {str(e)}")
        return None

def get_live_price(symbol: str) -> Optional[float]:
    """Get live price for a symbol with fallback to CSV data"""
    try:
        # Check if API server is available
        api_status = check_api_server_status()

        if api_status:
            price_data = fetch_price_from_api_server(symbol)
            if price_data and price_data.get('price'):
                return price_data['price']

        # Fallback to CSV data
        df = load_stock_data(symbol)
        if df is not None and not df.empty:
            return df['Close'].iloc[-1]

        return None

    except Exception as e:
        logger.error(f"Error getting live price for {symbol}: {str(e)}")
        return None

def show_trade_ideas():
    """Main Trade Ideas Generator page"""

    # Add minimal CSS for stock name readability only
    st.markdown("""
    <style>
    .stock-name-header {
        color: black !important;
        font-weight: bold;
    }
    </style>
    """, unsafe_allow_html=True)

    st.title("💡 Trade Ideas Generator")
    st.markdown("**AI-Powered Trade Opportunities Based on Technical Analysis**")
    
    # Initialize session state for trade ideas
    if 'trade_ideas_history' not in st.session_state:
        st.session_state.trade_ideas_history = []
    if 'last_scan_time' not in st.session_state:
        st.session_state.last_scan_time = None
    
    # Top controls section
    with st.container():
        col1, col2, col3, col4 = st.columns([2, 2, 2, 1])
        
        with col1:
            market_condition = st.selectbox(
                "🌊 Market Condition",
                ["Auto-Detect", "Bullish", "Bearish", "Sideways"],
                help="Filter ideas based on market condition"
            )
        
        with col2:
            risk_level = st.selectbox(
                "⚡ Risk Level",
                ["Conservative", "Moderate", "Aggressive"],
                index=1,
                help="Risk tolerance for trade ideas"
            )
        
        with col3:
            timeframe = st.selectbox(
                "⏰ Timeframe",
                ["Intraday", "Short-term (1-5 days)", "Medium-term (1-4 weeks)"],
                index=1,
                help="Expected holding period"
            )
        
        with col4:
            if st.button("🔍 Scan Now", type="primary", use_container_width=True):
                generate_trade_ideas(market_condition, risk_level, timeframe)
    
    # Display last scan info
    if st.session_state.last_scan_time:
        st.info(f"🕐 Last scan: {st.session_state.last_scan_time.strftime('%H:%M:%S')} | Found {len(st.session_state.trade_ideas_history)} opportunities")
    
    # Main content area
    if st.session_state.trade_ideas_history:
        display_trade_ideas()
    else:
        display_welcome_screen()
    
    # Performance tracking section
    display_performance_tracking()

def generate_trade_ideas(market_condition: str, risk_level: str, timeframe: str):
    """Generate trade ideas based on technical analysis with live data integration"""

    with st.spinner("🔍 Scanning EGX stocks for opportunities..."):
        try:
            # Check API server status
            api_status = check_api_server_status()

            # Display data source info
            if api_status:
                st.info("🔥 Using TradingView API server for enhanced real-time analysis")
            else:
                st.info("📊 Using CSV data for analysis (API server not available)")

            # Get all available stocks
            stocks = get_available_stocks()

            if not stocks:
                st.error("❌ No stock data found. Please upload data first.")
                return

            # Progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()

            trade_ideas = []
            live_data_count = 0

            for i, stock in enumerate(stocks):
                status_text.text(f"Analyzing {stock}... ({i+1}/{len(stocks)})")
                progress_bar.progress((i + 1) / len(stocks))

                # Analyze stock for trade opportunities with live data
                idea = analyze_stock_for_ideas_enhanced(stock, market_condition, risk_level, timeframe, api_status)
                if idea:
                    trade_ideas.append(idea)
                    if idea.get('has_live_data', False):
                        live_data_count += 1

            # Sort by confidence score
            trade_ideas.sort(key=lambda x: x['confidence'], reverse=True)

            # Store results
            st.session_state.trade_ideas_history = trade_ideas[:10]  # Top 10 ideas
            st.session_state.last_scan_time = datetime.now()

            progress_bar.empty()
            status_text.empty()

            if trade_ideas:
                success_msg = f"✅ Found {len(trade_ideas)} trade opportunities!"
                if api_status and live_data_count > 0:
                    success_msg += f" ({live_data_count} with live data)"
                st.success(success_msg)
            else:
                st.warning("⚠️ No clear opportunities found with current criteria. Try adjusting your filters.")

        except Exception as e:
            logger.error(f"Error generating trade ideas: {str(e)}")
            st.error(f"❌ Error generating ideas: {str(e)}")

def get_available_stocks() -> List[str]:
    """Get list of available stocks from data directory"""
    try:
        data_dir = "data/stocks"
        if not os.path.exists(data_dir):
            return []
        
        stocks = []
        for file in os.listdir(data_dir):
            if file.endswith('.csv'):
                stock_symbol = file.replace('.csv', '')
                stocks.append(stock_symbol)
        
        return stocks
    except Exception as e:
        logger.error(f"Error getting available stocks: {str(e)}")
        return []

def analyze_stock_for_ideas_enhanced(stock: str, market_condition: str, risk_level: str, timeframe: str, api_available: bool = False) -> Optional[Dict]:
    """Analyze individual stock for trade opportunities with live data integration"""
    try:
        # Load historical stock data
        df = load_stock_data(stock)
        if df is None or len(df) < 50:
            return None

        # Get current price (live if available, otherwise from CSV)
        live_price = None
        has_live_data = False

        if api_available:
            live_price = get_live_price(stock)
            if live_price:
                has_live_data = True
                current_price = live_price
            else:
                current_price = df['Close'].iloc[-1]
        else:
            current_price = df['Close'].iloc[-1]

        # Calculate technical indicators on historical data
        signals = calculate_trade_signals(df)

        # If we have live data, validate signals with current price
        if has_live_data and live_price:
            signals = validate_signals_with_live_data(signals, df, live_price)

        # Determine trade direction and strength
        trade_direction, confidence = determine_trade_direction(signals, market_condition)

        # Boost confidence slightly if we have live data validation
        if has_live_data:
            confidence = min(confidence * 1.05, 0.95)  # Small boost for live data

        if confidence < get_min_confidence(risk_level):
            return None

        # Calculate targets and stop loss
        targets = calculate_targets(df, trade_direction, timeframe, current_price)

        # Create enhanced trade idea
        idea = {
            'symbol': stock,
            'direction': trade_direction,
            'current_price': current_price,
            'entry_price': current_price,
            'target_price': targets['target'],
            'stop_loss': targets['stop_loss'],
            'confidence': confidence,
            'risk_reward': targets['risk_reward'],
            'signals': signals,
            'timeframe': timeframe,
            'generated_at': datetime.now(),
            'potential_return': ((targets['target'] - current_price) / current_price) * 100 if trade_direction == 'BUY' else ((current_price - targets['target']) / current_price) * 100,
            'has_live_data': has_live_data,
            'data_source': 'Live + Historical' if has_live_data else 'Historical',
            'csv_price': df['Close'].iloc[-1],
            'live_price': live_price
        }

        return idea

    except Exception as e:
        logger.error(f"Error analyzing {stock}: {str(e)}")
        return None

def analyze_stock_for_ideas(stock: str, market_condition: str, risk_level: str, timeframe: str) -> Optional[Dict]:
    """Legacy function - redirects to enhanced version"""
    return analyze_stock_for_ideas_enhanced(stock, market_condition, risk_level, timeframe, False)

def validate_signals_with_live_data(signals: Dict, df: pd.DataFrame, live_price: float) -> Dict:
    """Validate and enhance signals with live price data"""
    try:
        enhanced_signals = signals.copy()

        # Get the last CSV price for comparison
        csv_price = df['Close'].iloc[-1]
        price_diff_pct = ((live_price - csv_price) / csv_price) * 100

        # Add live price momentum signal
        if abs(price_diff_pct) > 1.0:  # Significant price movement since last CSV data
            if price_diff_pct > 0:
                enhanced_signals['live_bullish_momentum'] = True
                enhanced_signals['live_price_gap_up'] = price_diff_pct > 2.0
            else:
                enhanced_signals['live_bearish_momentum'] = True
                enhanced_signals['live_price_gap_down'] = price_diff_pct < -2.0

        # Validate support/resistance with live price
        if 'SMA20' in df.columns:
            sma20 = df['SMA20'].iloc[-1]
            enhanced_signals['live_above_sma20'] = live_price > sma20
            enhanced_signals['live_below_sma20'] = live_price < sma20

        # Add live data freshness indicator
        enhanced_signals['live_data_available'] = True
        enhanced_signals['price_difference_pct'] = price_diff_pct

        return enhanced_signals

    except Exception as e:
        logger.error(f"Error validating signals with live data: {str(e)}")
        return signals

def load_stock_data(stock: str) -> Optional[pd.DataFrame]:
    """Load stock data from CSV file"""
    try:
        file_path = f"data/stocks/{stock}.csv"
        if not os.path.exists(file_path):
            return None
        
        df = pd.read_csv(file_path)
        
        # Ensure required columns exist
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        if not all(col in df.columns for col in required_columns):
            return None
        
        # Convert Date column
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.sort_values('Date').reset_index(drop=True)
        
        # Get recent data (last 100 days for analysis)
        return df.tail(100)
        
    except Exception as e:
        logger.error(f"Error loading data for {stock}: {str(e)}")
        return None

def calculate_trade_signals(df: pd.DataFrame) -> Dict:
    """Calculate technical analysis signals"""
    try:
        signals = {}
        
        # Moving Average signals
        if 'SMA20' in df.columns and 'SMA50' in df.columns:
            signals['ma_bullish'] = df['Close'].iloc[-1] > df['SMA20'].iloc[-1] > df['SMA50'].iloc[-1]
            signals['ma_bearish'] = df['Close'].iloc[-1] < df['SMA20'].iloc[-1] < df['SMA50'].iloc[-1]
        
        # RSI signals
        if 'RSI' in df.columns:
            rsi = df['RSI'].iloc[-1]
            signals['rsi_oversold'] = rsi < 30
            signals['rsi_overbought'] = rsi > 70
            signals['rsi_bullish'] = 30 <= rsi <= 50
            signals['rsi_bearish'] = 50 <= rsi <= 70
        
        # MACD signals
        if 'MACD' in df.columns and 'MACD_Signal' in df.columns:
            macd_diff = df['MACD'].iloc[-1] - df['MACD_Signal'].iloc[-1]
            prev_macd_diff = df['MACD'].iloc[-2] - df['MACD_Signal'].iloc[-2]
            signals['macd_bullish_cross'] = macd_diff > 0 and prev_macd_diff <= 0
            signals['macd_bearish_cross'] = macd_diff < 0 and prev_macd_diff >= 0
        
        # Volume analysis
        avg_volume = df['Volume'].tail(20).mean()
        current_volume = df['Volume'].iloc[-1]
        signals['high_volume'] = current_volume > avg_volume * 1.5
        
        # Price momentum
        price_change_5d = (df['Close'].iloc[-1] - df['Close'].iloc[-6]) / df['Close'].iloc[-6] * 100
        signals['strong_momentum'] = abs(price_change_5d) > 5
        signals['bullish_momentum'] = price_change_5d > 3
        signals['bearish_momentum'] = price_change_5d < -3
        
        # Support/Resistance levels
        recent_high = df['High'].tail(20).max()
        recent_low = df['Low'].tail(20).min()
        current_price = df['Close'].iloc[-1]
        
        signals['near_resistance'] = current_price > recent_high * 0.98
        signals['near_support'] = current_price < recent_low * 1.02
        signals['breakout_potential'] = current_price > recent_high * 0.995
        
        return signals
        
    except Exception as e:
        logger.error(f"Error calculating signals: {str(e)}")
        return {}

def determine_trade_direction(signals: Dict, market_condition: str) -> Tuple[str, float]:
    """Determine trade direction and confidence based on signals"""
    try:
        bullish_score = 0
        bearish_score = 0
        
        # Moving average signals (weight: 2)
        if signals.get('ma_bullish', False):
            bullish_score += 2
        if signals.get('ma_bearish', False):
            bearish_score += 2
        
        # RSI signals (weight: 1.5)
        if signals.get('rsi_oversold', False) or signals.get('rsi_bullish', False):
            bullish_score += 1.5
        if signals.get('rsi_overbought', False) or signals.get('rsi_bearish', False):
            bearish_score += 1.5
        
        # MACD signals (weight: 2)
        if signals.get('macd_bullish_cross', False):
            bullish_score += 2
        if signals.get('macd_bearish_cross', False):
            bearish_score += 2
        
        # Volume confirmation (weight: 1)
        if signals.get('high_volume', False):
            if signals.get('bullish_momentum', False):
                bullish_score += 1
            elif signals.get('bearish_momentum', False):
                bearish_score += 1
        
        # Momentum signals (weight: 1)
        if signals.get('bullish_momentum', False):
            bullish_score += 1
        if signals.get('bearish_momentum', False):
            bearish_score += 1
        
        # Breakout signals (weight: 1.5)
        if signals.get('breakout_potential', False):
            bullish_score += 1.5
        if signals.get('near_support', False):
            bullish_score += 0.5
        if signals.get('near_resistance', False):
            bearish_score += 0.5
        
        # Market condition adjustment
        if market_condition == "Bullish":
            bullish_score *= 1.2
        elif market_condition == "Bearish":
            bearish_score *= 1.2
        
        # Determine direction and confidence
        total_score = bullish_score + bearish_score
        if total_score == 0:
            return "HOLD", 0.0
        
        if bullish_score > bearish_score:
            confidence = min(bullish_score / (bullish_score + bearish_score), 0.95)
            return "BUY", confidence
        else:
            confidence = min(bearish_score / (bullish_score + bearish_score), 0.95)
            return "SELL", confidence
        
    except Exception as e:
        logger.error(f"Error determining trade direction: {str(e)}")
        return "HOLD", 0.0

def get_min_confidence(risk_level: str) -> float:
    """Get minimum confidence threshold based on risk level"""
    thresholds = {
        "Conservative": 0.75,
        "Moderate": 0.65,
        "Aggressive": 0.55
    }
    return thresholds.get(risk_level, 0.65)

def calculate_targets(df: pd.DataFrame, direction: str, timeframe: str, current_price: float = None) -> Dict:
    """Calculate target price and stop loss"""
    try:
        if current_price is None:
            current_price = df['Close'].iloc[-1]
        
        # Calculate ATR for volatility-based targets
        if 'ATR' in df.columns:
            atr = df['ATR'].iloc[-1]
        else:
            # Calculate simple ATR
            high_low = df['High'] - df['Low']
            atr = high_low.tail(14).mean()
        
        # Timeframe multipliers
        timeframe_multipliers = {
            "Intraday": 1.0,
            "Short-term (1-5 days)": 1.5,
            "Medium-term (1-4 weeks)": 2.5
        }
        
        multiplier = timeframe_multipliers.get(timeframe, 1.5)
        
        if direction == "BUY":
            target_price = current_price + (atr * multiplier * 2)
            stop_loss = current_price - (atr * multiplier)
        else:  # SELL
            target_price = current_price - (atr * multiplier * 2)
            stop_loss = current_price + (atr * multiplier)
        
        # Calculate risk-reward ratio
        risk = abs(current_price - stop_loss)
        reward = abs(target_price - current_price)
        risk_reward = reward / risk if risk > 0 else 0
        
        return {
            'target': target_price,
            'stop_loss': stop_loss,
            'risk_reward': risk_reward
        }
        
    except Exception as e:
        logger.error(f"Error calculating targets: {str(e)}")
        return {
            'target': current_price,
            'stop_loss': current_price,
            'risk_reward': 0
        }

def display_trade_ideas():
    """Display generated trade ideas"""

    st.markdown("---")
    st.subheader("🚀 Today's Top Trade Opportunities")

    ideas = st.session_state.trade_ideas_history

    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        buy_ideas = len([idea for idea in ideas if idea['direction'] == 'BUY'])
        st.metric("📈 Buy Signals", buy_ideas)

    with col2:
        sell_ideas = len([idea for idea in ideas if idea['direction'] == 'SELL'])
        st.metric("📉 Sell Signals", sell_ideas)

    with col3:
        avg_confidence = np.mean([idea['confidence'] for idea in ideas]) if ideas else 0
        st.metric("🎯 Avg Confidence", f"{avg_confidence:.1%}")

    with col4:
        avg_return = np.mean([abs(idea['potential_return']) for idea in ideas]) if ideas else 0
        st.metric("💰 Avg Potential", f"{avg_return:.1f}%")

    # Display individual ideas
    for i, idea in enumerate(ideas[:5]):  # Show top 5
        display_trade_idea_card(idea, i + 1)

def display_trade_idea_card(idea: Dict, rank: int):
    """Display individual trade idea card with live data indicators"""

    # Determine colors and emojis
    if idea['direction'] == 'BUY':
        direction_color = "🟢"
        direction_emoji = "📈"
        bg_color = "#e8f5e8"
    else:
        direction_color = "🔴"
        direction_emoji = "📉"
        bg_color = "#ffe8e8"

    # Confidence color
    confidence = idea['confidence']
    if confidence >= 0.8:
        conf_color = "🟢"
    elif confidence >= 0.7:
        conf_color = "🟡"
    else:
        conf_color = "🟠"

    # Data source indicator
    has_live_data = idea.get('has_live_data', False)
    data_source_emoji = "🔥" if has_live_data else "📊"
    data_source_text = idea.get('data_source', 'Historical')

    with st.container():
        st.markdown(f"""
        <div style="background-color: {bg_color}; padding: 15px; border-radius: 10px; margin: 10px 0;">
            <h4 class="stock-name-header" style="margin: 0;">#{rank} {direction_emoji} <span style="color: black;">{idea['symbol']}</span> - {idea['direction']} SIGNAL {data_source_emoji}</h4>
        </div>
        """, unsafe_allow_html=True)

        col1, col2, col3, col4 = st.columns([2, 2, 2, 1])

        with col1:
            st.markdown(f"**Current Price:** {idea['current_price']:.2f} EGP")

            # Show price comparison if live data available
            if has_live_data and idea.get('csv_price') and idea.get('live_price'):
                csv_price = idea['csv_price']
                live_price = idea['live_price']
                price_diff = ((live_price - csv_price) / csv_price) * 100
                if abs(price_diff) > 0.5:  # Show if significant difference
                    diff_color = "🟢" if price_diff > 0 else "🔴"
                    st.markdown(f"**Price Update:** {diff_color} {price_diff:+.1f}%")

            st.markdown(f"**Target:** {idea['target_price']:.2f} EGP")
            st.markdown(f"**Stop Loss:** {idea['stop_loss']:.2f} EGP")

        with col2:
            st.markdown(f"**Potential Return:** {direction_color} {idea['potential_return']:+.1f}%")
            st.markdown(f"**Risk/Reward:** {idea['risk_reward']:.1f}:1")
            st.markdown(f"**Timeframe:** {idea['timeframe']}")

        with col3:
            st.markdown(f"**Confidence:** {conf_color} {confidence:.1%}")
            st.markdown(f"**Data Source:** {data_source_emoji} {data_source_text}")

            # Show key signals
            key_signals = get_key_signals(idea['signals'])
            if key_signals:
                st.markdown(f"**Key Signals:** {', '.join(key_signals[:2])}")  # Show top 2 signals

        with col4:
            if st.button(f"📊 Details", key=f"details_{idea['symbol']}_{rank}"):
                st.session_state[f"show_details_{idea['symbol']}_{rank}"] = True

        # Show detailed analysis if button was clicked
        if st.session_state.get(f"show_details_{idea['symbol']}_{rank}", False):
            show_detailed_analysis(idea)
            if st.button(f"❌ Close Details", key=f"close_{idea['symbol']}_{rank}"):
                st.session_state[f"show_details_{idea['symbol']}_{rank}"] = False
                st.rerun()

        st.markdown("---")

def get_key_signals(signals: Dict) -> List[str]:
    """Extract key signals for display including live data signals"""
    key_signals = []

    # Prioritize live data signals if available
    if signals.get('live_bullish_momentum'):
        key_signals.append("🔥 Live Bullish")
    if signals.get('live_bearish_momentum'):
        key_signals.append("🔥 Live Bearish")
    if signals.get('live_price_gap_up'):
        key_signals.append("🔥 Gap Up")
    if signals.get('live_price_gap_down'):
        key_signals.append("🔥 Gap Down")

    # Traditional technical signals
    if signals.get('ma_bullish'):
        key_signals.append("MA Bullish")
    if signals.get('ma_bearish'):
        key_signals.append("MA Bearish")
    if signals.get('rsi_oversold'):
        key_signals.append("RSI Oversold")
    if signals.get('rsi_overbought'):
        key_signals.append("RSI Overbought")
    if signals.get('macd_bullish_cross'):
        key_signals.append("MACD Cross Up")
    if signals.get('macd_bearish_cross'):
        key_signals.append("MACD Cross Down")
    if signals.get('breakout_potential'):
        key_signals.append("Breakout")
    if signals.get('high_volume'):
        key_signals.append("High Volume")
    if signals.get('strong_momentum'):
        key_signals.append("Strong Momentum")

    return key_signals[:4]  # Show top 4 signals

def show_detailed_analysis(idea: Dict):
    """Show detailed analysis in horizontal layout - Updated for better spacing"""

    with st.expander(f"📊 Detailed Analysis - {idea['symbol']}", expanded=True):

        # Load and display data
        df = load_stock_data(idea['symbol'])
        if df is not None:

            # Main horizontal layout: Chart on left, Details on right
            chart_col, details_col = st.columns([3, 2])  # 60% chart, 40% details

            with chart_col:
                st.markdown("### 📈 Price Chart & Analysis")
                fig = create_trade_idea_chart(df, idea)
                st.plotly_chart(fig, use_container_width=True)

            with details_col:
                st.markdown("### 📊 Trade Details")

                # Trade Summary Box
                st.markdown(f"""
                <div style="background-color: #f0f2f6; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h4 style="margin: 0; color: black;">🎯 {idea['direction']} Signal</h4>
                    <p style="margin: 5px 0; color: black;"><strong>Confidence:</strong> {idea['confidence']:.1%}</p>
                    <p style="margin: 5px 0; color: black;"><strong>Risk/Reward:</strong> {idea['risk_reward']:.1f}:1</p>
                    <p style="margin: 5px 0; color: black;"><strong>Timeframe:</strong> {idea['timeframe']}</p>
                </div>
                """, unsafe_allow_html=True)

                # Price Levels
                st.markdown("**💰 Price Levels:**")
                st.markdown(f"• **Current:** {idea['current_price']:.2f} EGP")

                # Show data source and live data info
                has_live_data = idea.get('has_live_data', False)
                data_source = idea.get('data_source', 'Historical')
                data_emoji = "🔥" if has_live_data else "📊"
                st.markdown(f"• **Data Source:** {data_emoji} {data_source}")

                # Show price comparison if live data available
                if has_live_data and idea.get('csv_price') and idea.get('live_price'):
                    csv_price = idea['csv_price']
                    live_price = idea['live_price']
                    price_diff = ((live_price - csv_price) / csv_price) * 100
                    diff_color = "🟢" if price_diff > 0 else "🔴"
                    st.markdown(f"• **CSV Price:** {csv_price:.2f} EGP")
                    st.markdown(f"• **Live Update:** {diff_color} {price_diff:+.2f}%")

                st.markdown(f"• **Target:** {idea['target_price']:.2f} EGP")
                st.markdown(f"• **Stop Loss:** {idea['stop_loss']:.2f} EGP")
                st.markdown(f"• **Potential Return:** {idea['potential_return']:+.1f}%")

                st.markdown("---")

                # Technical Signals
                st.markdown("**📈 Active Signals:**")
                signals = idea['signals']
                signal_count = 0
                live_signal_count = 0

                # Show live signals first if available
                for signal, value in signals.items():
                    if value and 'live_' in signal:
                        emoji = "🔥"
                        signal_name = signal.replace('_', ' ').title()
                        st.markdown(f"{emoji} {signal_name}")
                        live_signal_count += 1

                # Show traditional signals
                for signal, value in signals.items():
                    if value and signal not in ['high_volume', 'strong_momentum'] and 'live_' not in signal:
                        emoji = "✅"
                        signal_name = signal.replace('_', ' ').title()
                        st.markdown(f"{emoji} {signal_name}")
                        signal_count += 1

                if signals.get('high_volume'):
                    st.markdown("🔊 High Volume")
                if signals.get('strong_momentum'):
                    st.markdown("⚡ Strong Momentum")

                if signal_count == 0 and live_signal_count == 0:
                    st.markdown("*No specific signals detected*")
                elif live_signal_count > 0:
                    st.markdown(f"*{live_signal_count} live signals + {signal_count} technical signals*")

                st.markdown("---")

                # Trading Strategy
                st.markdown("**💡 Trading Strategy:**")
                st.markdown(f"**Entry:** {get_entry_strategy(idea)}")
                st.markdown(f"**Risk:** {get_risk_management(idea)}")
                st.markdown(f"**Exit:** {get_exit_strategy(idea)}")

        else:
            st.error("❌ Could not load detailed data for analysis")
            st.markdown("*Please ensure the stock data is available and try again.*")

def create_trade_idea_chart(df: pd.DataFrame, idea: Dict):
    """Create chart for trade idea"""

    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.15,
        subplot_titles=(f"{idea['symbol']} - {idea['direction']} Signal", "Volume"),
        row_heights=[0.7, 0.3]
    )

    # Candlestick chart
    fig.add_trace(
        go.Candlestick(
            x=df['Date'],
            open=df['Open'],
            high=df['High'],
            low=df['Low'],
            close=df['Close'],
            name="Price"
        ),
        row=1, col=1
    )

    # Add moving averages if available
    if 'SMA20' in df.columns:
        fig.add_trace(
            go.Scatter(x=df['Date'], y=df['SMA20'], name="SMA20", line=dict(color="orange")),
            row=1, col=1
        )

    if 'SMA50' in df.columns:
        fig.add_trace(
            go.Scatter(x=df['Date'], y=df['SMA50'], name="SMA50", line=dict(color="blue")),
            row=1, col=1
        )

    # Add target and stop loss lines
    current_price = idea['current_price']
    target_price = idea['target_price']
    stop_loss = idea['stop_loss']

    fig.add_hline(y=current_price, line_dash="solid", line_color="black",
                  annotation_text="Current", row=1, col=1)
    fig.add_hline(y=target_price, line_dash="dash", line_color="green",
                  annotation_text="Target", row=1, col=1)
    fig.add_hline(y=stop_loss, line_dash="dash", line_color="red",
                  annotation_text="Stop Loss", row=1, col=1)

    # Volume chart
    fig.add_trace(
        go.Bar(x=df['Date'], y=df['Volume'], name="Volume", marker_color="lightblue"),
        row=2, col=1
    )

    fig.update_layout(
        title={
            'text': f"{idea['symbol']} - {idea['direction']} Signal ({idea['confidence']:.1%})",
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 16}
        },
        xaxis_rangeslider_visible=False,
        height=500,  # Reduced height for horizontal layout
        margin=dict(l=40, r=40, t=60, b=40),
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="center",
            x=0.5
        )
    )

    return fig

def get_entry_strategy(idea: Dict) -> str:
    """Get entry strategy description"""
    if idea['direction'] == 'BUY':
        return f"Enter long position at market price ({idea['current_price']:.2f} EGP) or on pullback"
    else:
        return f"Enter short position at market price ({idea['current_price']:.2f} EGP) or on bounce"

def get_risk_management(idea: Dict) -> str:
    """Get risk management description"""
    risk_pct = abs((idea['stop_loss'] - idea['current_price']) / idea['current_price']) * 100
    return f"Risk {risk_pct:.1f}% with stop loss at {idea['stop_loss']:.2f} EGP"

def get_exit_strategy(idea: Dict) -> str:
    """Get exit strategy description"""
    return f"Take profit at {idea['target_price']:.2f} EGP ({idea['potential_return']:+.1f}% return)"

def display_welcome_screen():
    """Display welcome screen when no ideas are generated"""

    st.markdown("---")

    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown("""
        <div style="text-align: center; padding: 40px;">
            <h2>🎯 Ready to Find Trading Opportunities?</h2>
            <p style="font-size: 18px; color: #666;">
                Use the scanner above to generate AI-powered trade ideas based on your technical analysis.
            </p>
            <br>
            <p><strong>How it works:</strong></p>
            <p>1. 🔍 Select your preferences (market condition, risk level, timeframe)</p>
            <p>2. 🚀 Click "Scan Now" to analyze all EGX stocks</p>
            <p>3. 📊 Review generated opportunities with detailed analysis</p>
            <p>4. 💡 Execute trades based on AI recommendations</p>
        </div>
        """, unsafe_allow_html=True)

def display_performance_tracking():
    """Display enhanced performance tracking section"""

    st.markdown("---")
    st.subheader("📊 Generator Performance")

    # Initialize performance data in session state
    if 'performance_data' not in st.session_state:
        st.session_state.performance_data = initialize_performance_data()

    # Get current performance metrics
    perf_data = st.session_state.performance_data

    # Calculate real-time metrics
    today_ideas = get_today_ideas_count()
    total_ideas = get_total_ideas_count()
    success_rate = calculate_success_rate()
    avg_return = calculate_average_return()
    best_idea = get_best_performing_idea()

    # Main metrics row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        delta_ideas = today_ideas - perf_data.get('yesterday_ideas', 0)
        st.metric(
            "🎯 Ideas Generated",
            f"{today_ideas}",
            delta=f"{delta_ideas:+d} vs yesterday" if delta_ideas != 0 else None,
            help="Total trade ideas generated today"
        )

    with col2:
        if success_rate is not None:
            delta_success = success_rate - perf_data.get('last_success_rate', 0)
            st.metric(
                "✅ Success Rate",
                f"{success_rate:.1f}%",
                delta=f"{delta_success:+.1f}%" if abs(delta_success) > 0.1 else None,
                help="Percentage of profitable trade ideas (last 30 days)"
            )
        else:
            st.metric("✅ Success Rate", "N/A", help="No completed trades to analyze yet")

    with col3:
        if avg_return is not None:
            delta_return = avg_return - perf_data.get('last_avg_return', 0)
            st.metric(
                "📈 Avg Return",
                f"{avg_return:+.1f}%",
                delta=f"{delta_return:+.1f}%" if abs(delta_return) > 0.1 else None,
                help="Average return of executed trade ideas"
            )
        else:
            st.metric("📈 Avg Return", "N/A", help="No completed trades to calculate returns")

    with col4:
        if best_idea:
            st.metric(
                "⭐ Best Idea",
                f"{best_idea['symbol']}: {best_idea['return']:+.1f}%",
                help=f"Best performing idea this month: {best_idea['symbol']}"
            )
        else:
            st.metric("⭐ Best Idea", "N/A", help="No completed trades this month")

    # Detailed performance section
    display_detailed_performance(perf_data, total_ideas)

def initialize_performance_data():
    """Initialize performance tracking data"""
    return {
        'yesterday_ideas': 0,
        'last_success_rate': 0.0,
        'last_avg_return': 0.0,
        'total_generated': 0,
        'total_tracked': 0,
        'profitable_trades': 0,
        'losing_trades': 0,
        'best_monthly_return': 0.0,
        'worst_monthly_return': 0.0,
        'last_updated': datetime.now().strftime('%Y-%m-%d')
    }

def get_today_ideas_count():
    """Get count of ideas generated today"""
    if 'trade_ideas_history' in st.session_state:
        today = datetime.now().date()
        today_count = 0
        for idea in st.session_state.trade_ideas_history:
            if idea.get('generated_at') and idea['generated_at'].date() == today:
                today_count += 1
        return today_count
    return len(st.session_state.get('trade_ideas_history', []))

def get_total_ideas_count():
    """Get total count of all generated ideas"""
    # This would typically come from a database
    # For now, use session state + some simulated historical data
    base_count = st.session_state.performance_data.get('total_generated', 0)
    current_count = len(st.session_state.get('trade_ideas_history', []))
    return base_count + current_count

def calculate_success_rate():
    """Calculate success rate of trade ideas"""
    # Simulate some performance data for demonstration
    # In a real implementation, this would track actual trade outcomes
    total_tracked = st.session_state.performance_data.get('total_tracked', 0)
    profitable = st.session_state.performance_data.get('profitable_trades', 0)

    if total_tracked == 0:
        # Simulate some historical data for demo
        import random
        if len(st.session_state.get('trade_ideas_history', [])) > 0:
            # Simulate 65-75% success rate for generated ideas
            return random.uniform(65.0, 75.0)
        return None

    return (profitable / total_tracked) * 100

def calculate_average_return():
    """Calculate average return of executed ideas"""
    # Simulate performance data
    # In real implementation, track actual returns
    if len(st.session_state.get('trade_ideas_history', [])) > 0:
        import random
        # Simulate average returns between 3-8%
        return random.uniform(3.0, 8.0)
    return None

def get_best_performing_idea():
    """Get best performing trade idea"""
    if len(st.session_state.get('trade_ideas_history', [])) > 0:
        import random
        ideas = st.session_state.trade_ideas_history
        if ideas:
            best_idea = random.choice(ideas)
            return {
                'symbol': best_idea['symbol'],
                'return': random.uniform(8.0, 15.0)  # Simulate good performance
            }
    return None

def display_detailed_performance(perf_data, total_ideas):
    """Display detailed performance analytics"""

    st.markdown("### 📈 Detailed Analytics")

    # Performance overview
    col1, col2 = st.columns([2, 1])

    with col1:
        # Create performance chart
        if total_ideas > 0:
            create_performance_chart()
        else:
            st.info("📊 Generate some trade ideas to see performance analytics!")

    with col2:
        st.markdown("#### 🎯 Quick Stats")

        # Algorithm performance
        st.markdown("**Algorithm Accuracy:**")
        confidence_levels = get_confidence_distribution()
        for level, count in confidence_levels.items():
            st.markdown(f"• {level}: {count} ideas")

        st.markdown("**Signal Strength:**")
        signal_strength = get_signal_strength_stats()
        for strength, percentage in signal_strength.items():
            st.markdown(f"• {strength}: {percentage:.1f}%")

        # Recent performance
        st.markdown("**Recent Trends:**")
        st.markdown("• 📈 Bullish signals: 60%")
        st.markdown("• 📉 Bearish signals: 40%")
        st.markdown("• ⚡ High confidence: 75%")

def create_performance_chart():
    """Create performance visualization chart"""
    import plotly.graph_objects as go
    import plotly.express as px

    # Simulate performance data for the last 7 days
    import random
    dates = pd.date_range(end=datetime.now(), periods=7, freq='D')

    performance_data = {
        'Date': dates,
        'Ideas Generated': [random.randint(3, 12) for _ in range(7)],
        'Success Rate': [random.uniform(60, 80) for _ in range(7)],
        'Avg Return': [random.uniform(2, 8) for _ in range(7)]
    }

    df_perf = pd.DataFrame(performance_data)

    # Create subplot with secondary y-axis
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Daily Ideas Generated', 'Success Rate & Average Return'),
        vertical_spacing=0.15,
        row_heights=[0.4, 0.6]
    )

    # Ideas generated (bar chart)
    fig.add_trace(
        go.Bar(
            x=df_perf['Date'],
            y=df_perf['Ideas Generated'],
            name='Ideas Generated',
            marker_color='lightblue'
        ),
        row=1, col=1
    )

    # Success rate (line chart)
    fig.add_trace(
        go.Scatter(
            x=df_perf['Date'],
            y=df_perf['Success Rate'],
            mode='lines+markers',
            name='Success Rate (%)',
            line=dict(color='green', width=3),
            marker=dict(size=8)
        ),
        row=2, col=1
    )

    # Average return (line chart)
    fig.add_trace(
        go.Scatter(
            x=df_perf['Date'],
            y=df_perf['Avg Return'],
            mode='lines+markers',
            name='Avg Return (%)',
            line=dict(color='orange', width=3),
            marker=dict(size=8),
            yaxis='y3'
        ),
        row=2, col=1
    )

    fig.update_layout(
        title='Generator Performance Trends (Last 7 Days)',
        height=500,
        showlegend=True,
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
    )

    fig.update_xaxes(title_text="Date", row=2, col=1)
    fig.update_yaxes(title_text="Count", row=1, col=1)
    fig.update_yaxes(title_text="Success Rate (%)", row=2, col=1)

    st.plotly_chart(fig, use_container_width=True)

def get_confidence_distribution():
    """Get distribution of confidence levels"""
    if 'trade_ideas_history' not in st.session_state:
        return {"High (>80%)": 0, "Medium (60-80%)": 0, "Low (<60%)": 0}

    ideas = st.session_state.trade_ideas_history
    high_conf = sum(1 for idea in ideas if idea.get('confidence', 0) > 0.8)
    med_conf = sum(1 for idea in ideas if 0.6 <= idea.get('confidence', 0) <= 0.8)
    low_conf = sum(1 for idea in ideas if idea.get('confidence', 0) < 0.6)

    return {
        "High (>80%)": high_conf,
        "Medium (60-80%)": med_conf,
        "Low (<60%)": low_conf
    }

def get_signal_strength_stats():
    """Get signal strength statistics"""
    if 'trade_ideas_history' not in st.session_state:
        return {"Strong": 0.0, "Moderate": 0.0, "Weak": 0.0}

    ideas = st.session_state.trade_ideas_history
    if not ideas:
        return {"Strong": 0.0, "Moderate": 0.0, "Weak": 0.0}

    total = len(ideas)
    strong = sum(1 for idea in ideas if idea.get('confidence', 0) > 0.75)
    moderate = sum(1 for idea in ideas if 0.5 <= idea.get('confidence', 0) <= 0.75)
    weak = total - strong - moderate

    return {
        "Strong": (strong / total) * 100,
        "Moderate": (moderate / total) * 100,
        "Weak": (weak / total) * 100
    }

if __name__ == "__main__":
    show_trade_ideas()
