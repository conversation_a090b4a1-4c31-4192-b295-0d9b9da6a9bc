import os
import sys
import logging
import numpy as np
import pandas as pd
import streamlit as st
import requests
import plotly.graph_objects as go
import pytz
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

# Add the d:\AI Stocks Bot directory to the Python module search path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# Import SMC components
from app.components.smc_indicators import (
    detect_order_blocks, 
    detect_fvg, 
    detect_liquidity_zones,
    detect_market_structure,
    FairValueGap,
    OrderBlock,
    LiquidityZone
)
from app.components.smc_analysis_engine import SMCAnalysisEngine, SMCSignal

# Configure logging
logger = logging.getLogger(__name__)

# Define EGX_STOCKS mapping (same as advanced_technical_analysis.py)
EGX_STOCKS = {
    "COMI": "Commercial International Bank",
    "FWRY": "Fawry Banking Technology",
    "PHDC": "Palm Hills Development",
    "EFID": "Edita Food Industries",
    "UBEE": "United Bank Egypt",
    "GGRN": "GoGreen Agricultural",
    "OBRI": "Orascom Business Intelligence",
    "UTOP": "United Top"
}

# Define SMC_AVAILABLE flag
SMC_AVAILABLE = True

# TradingView API Configuration (same as Advanced Technical Analysis)
TRADINGVIEW_API_URL = "http://127.0.0.1:8000/api/scrape_pairs"

def get_available_stock_symbols():
    """Return a list of stock symbols based on CSV files in the data/stocks directory."""
    stocks_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/stocks'))
    symbols = []
    if os.path.exists(stocks_dir):
        for fname in os.listdir(stocks_dir):
            if fname.endswith('.csv'):
                symbols.append(os.path.splitext(fname)[0])
    return sorted(symbols)

def check_api_status():
    """Check if TradingView API server is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def determine_market_status():
    """Determine current EGX market status"""
    try:
        cairo_tz = pytz.timezone('Africa/Cairo')
        now_cairo = datetime.now(cairo_tz)

        # EGX operates Sunday-Thursday, 10:00 AM - 2:30 PM
        is_weekday = now_cairo.weekday() < 4  # Monday=0, Thursday=3
        is_sunday = now_cairo.weekday() == 6  # Sunday=6
        is_trading_day = is_weekday or is_sunday

        current_time = now_cairo.time()
        market_open = current_time >= datetime.strptime("10:00", "%H:%M").time()
        market_close = current_time <= datetime.strptime("14:30", "%H:%M").time()
        is_market_hours = market_open and market_close

        if is_trading_day and is_market_hours:
            return "OPEN"
        elif is_trading_day:
            return "CLOSED_TODAY"
        else:
            return "WEEKEND"

    except Exception as e:
        logger.error(f"Error determining market status: {str(e)}")
        return "UNKNOWN"

def generate_ohlcv_data(current_price, bars):
    """Generate realistic OHLCV data for SMC analysis"""
    np.random.seed(42)  # For consistent data
    
    # Generate price movements working backwards from current price
    volatility = 0.02  # 2% daily volatility
    returns = np.random.normal(0, volatility, bars)
    
    # Start from current price and work backwards
    prices = [current_price]
    for i in range(bars - 1):
        prev_price = prices[0] / (1 + returns[bars - 1 - i])
        prices.insert(0, max(prev_price, 1.0))
    
    # Create OHLCV data
    data = []
    for i in range(bars):
        close = prices[i]
        intraday_vol = close * 0.015  # 1.5% intraday volatility
        
        high = close + np.random.uniform(0, intraday_vol)
        low = close - np.random.uniform(0, intraday_vol)
        open_price = low + np.random.uniform(0, high - low)
        
        # Ensure OHLC relationships
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        volume = np.random.randint(100000, 2000000)
        
        data.append({
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.index = pd.date_range(end=datetime.now(), periods=bars, freq='D')
    
    return df

def prepare_csv_for_smc(df, interval):
    """Convert CSV data to SMC-compatible OHLCV format"""
    try:
        # Ensure required columns exist
        required_cols = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        if not all(col in df.columns for col in required_cols):
            logger.error(f"Missing required columns. Available: {list(df.columns)}")
            return None

        # Convert Date column to datetime
        df['Date'] = pd.to_datetime(df['Date'])

        # Sort by date
        df = df.sort_values('Date')

        # Create SMC-compatible DataFrame
        df_smc = pd.DataFrame({
            'open': df['Open'].astype(float),
            'high': df['High'].astype(float),
            'low': df['Low'].astype(float),
            'close': df['Close'].astype(float),
            'volume': df['Volume'].astype(int)
        })

        # Set date as index
        df_smc.index = df['Date']

        # Get recent data for analysis (last 6 months for better patterns)
        if len(df_smc) > 180:
            df_smc = df_smc.tail(180)  # Last 6 months

        logger.info(f"Prepared SMC data: {len(df_smc)} bars from {df_smc.index[0].date()} to {df_smc.index[-1].date()}")
        return df_smc

    except Exception as e:
        logger.error(f"Error preparing CSV data for SMC: {str(e)}")
        return None

def fetch_price_data_for_smc(symbol, interval):
    """Fetch price data from CSV files for enhanced SMC analysis"""
    try:
        # Remove EGX- prefix if present to match CSV filename
        csv_symbol = symbol.replace('EGX-', '')

        # Try multiple possible CSV locations
        csv_paths = [
            f'data/stocks/{csv_symbol}.csv',
            f'data/{csv_symbol}.csv',
            f'data/stocks/{csv_symbol}_new.csv'
        ]

        df = None
        for csv_path in csv_paths:
            if os.path.exists(csv_path):
                logger.info(f"Loading CSV data from: {csv_path}")
                df = pd.read_csv(csv_path)
                break

        if df is None:
            logger.warning(f"No CSV data found for {csv_symbol}, falling back to live data")
            return generate_ohlcv_data(80.0, 200)

        # Convert to OHLCV format for SMC analysis
        df_smc = prepare_csv_for_smc(df, interval)

        if df_smc is not None and len(df_smc) > 50:  # Ensure sufficient data
            logger.info(f"Successfully loaded {len(df_smc)} bars of historical data for SMC analysis")
            return df_smc
        else:
            logger.warning(f"Insufficient CSV data, falling back to generated data")
            return generate_ohlcv_data(80.0, 200)

    except Exception as e:
        logger.error(f"Error loading CSV data: {str(e)}")
        return generate_ohlcv_data(80.0, 200)

def run_smc_analysis(df, symbol, interval):
    """Run SMC analysis on the given data using the new SMC analysis engine"""
    try:
        # Initialize the SMC analysis engine
        smc_engine = SMCAnalysisEngine()
        
        # Run comprehensive SMC analysis
        smc_results = smc_engine.analyze_stock(df, symbol)
        
        # Extract individual components for compatibility with display functions
        results = {
            'order_blocks': smc_results.get('order_blocks', []),
            'fvgs': smc_results.get('fvgs', []),
            'liquidity_zones': smc_results.get('liquidity_zones', []),
            'market_structure': smc_results.get('market_structure', {}),
            'bos_signals': smc_results.get('bos_events', []),  # Map bos_events to bos_signals
            'premium_discount': smc_results.get('premium_discount_zones', {}),  # Map to premium_discount
            'confluence': {
                'total_score': smc_results.get('confluence_strength', 0.5),
                'active_factors': len([f for f in smc_results.get('confluence_factors', {}).values() if f > 0]),
                'factors': smc_results.get('confluence_factors', {}),
                'breakdown': smc_results.get('confluence_factors', {})
            },
            'signals': smc_results.get('trading_signal', []),
            'current_price': df['close'].iloc[-1] if not df.empty else None,
            'summary': smc_results.get('summary', {}),
            'risk_levels': smc_results.get('risk_levels', {}),
            'smc_engine': smc_engine  # Pass engine for additional analysis
        }

        return results

    except Exception as e:
        logger.error(f"Error running SMC analysis: {str(e)}")
        return {}

def show_smc_analysis():
    """Enhanced SMC Analysis page with professional styling and comprehensive features"""

    # Page header with enhanced styling
    st.markdown("""
    <div style='background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                padding: 20px; border-radius: 15px; margin-bottom: 20px;
                border: 2px solid #4CAF50;'>
        <h1 style='color: white; text-align: center; margin: 0;'>
            🧠 Smart Money Concepts (SMC) Analysis
        </h1>
        <p style='color: #E3F2FD; text-align: center; margin: 10px 0 0 0; font-size: 18px;'>
            Professional SMC Analysis with Advanced Market Structure Detection
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Check if SMC components are available
    if not SMC_AVAILABLE:
        st.error("❌ SMC components are not available. Please check the installation.")
        st.info("💡 Make sure all required SMC modules are properly installed.")
        return

    # Main analysis interface
    st.markdown("### 📊 Analysis Configuration")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("🎯 Stock Selection")

        # Stock selection with enhanced options
        available_symbols = get_available_stock_symbols()
        stock_options = {k: EGX_STOCKS[k] if k in EGX_STOCKS else k for k in available_symbols}
        if not stock_options:
            st.error("No valid stock data found in data/stocks directory.")
            return
        selected_stock = st.selectbox(
            "Select Stock:",
            options=list(stock_options.keys()),
            format_func=lambda x: f"{x} - {EGX_STOCKS[x]}" if x in EGX_STOCKS else x,
            index=0,
            help="Choose a stock for SMC analysis"
        )

    with col2:
        st.subheader("⏰ Analysis Settings")

        # Time interval selection
        selected_interval = st.selectbox(
            "Timeframe:",
            options=["1D", "1W"],
            index=0,
            help="Select timeframe for SMC analysis"
        )

        # Number of bars for analysis
        bars_count = st.slider(
            "Analysis Period (bars):",
            min_value=100,
            max_value=500,
            value=200,
            step=50,
            help="Number of bars to analyze"
        )

    # Analysis button
    if st.button("🧠 Run SMC Analysis", type="primary", use_container_width=True):
        with st.spinner("🔄 Fetching data and running SMC analysis..."):

            # Fetch TradingView data
            egx_symbol = f"EGX-{selected_stock}"
            price_data = fetch_price_data_for_smc(egx_symbol, selected_interval)

            if price_data is not None:
                # Run SMC analysis
                smc_results = run_smc_analysis(price_data, selected_stock, selected_interval)

                if smc_results:
                    display_smc_results(smc_results, selected_stock, selected_interval, price_data)
                else:
                    st.error("❌ Failed to run SMC analysis")
            else:
                st.error("❌ Failed to fetch price data")

def display_smc_results(results, symbol, interval, df):
    """Display SMC analysis results in the Streamlit app"""
    st.subheader(f"📊 SMC Analysis for {symbol} ({interval})")
    
    # Overview section
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Order Blocks", len(results.get('order_blocks', [])))
        
    with col2:
        st.metric("Fair Value Gaps", len(results.get('fvgs', [])))
        
    with col3:
        st.metric("Liquidity Zones", len(results.get('liquidity_zones', [])))
    
    # Market Structure Overview
    st.subheader("📈 Market Structure")
    market_structure = results.get('market_structure', {})
    trend = market_structure.get('trend', 'N/A')
    strength = market_structure.get('strength', 0)
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Trend", trend.upper() if trend != 'N/A' else 'N/A')
    with col2:
        st.metric("Strength", f"{strength:.1%}" if strength else "N/A")
    with col3:
        confluence = results.get('confluence', {})
        conf_score = confluence.get('total_score', 0.5)
        st.metric("Confluence Score", f"{conf_score:.1%}")
    
    # Current Price
    if 'current_price' in results and results['current_price']:
        st.metric("Current Price", f"{results['current_price']:.2f} EGP")
    
    # Detailed tabs
    tabs = st.tabs([
        "📋 Summary", 
        "⬜ Order Blocks", 
        "⚡ Fair Value Gaps",
        "💧 Liquidity Zones",
        "🔄 Market Structure",
        "🛡️ Risk Management"
    ])
    
    with tabs[0]:
        st.subheader("📋 Analysis Summary")
        
        summary = results.get('summary', {})
        if summary:
            for key, value in summary.items():
                st.write(f"**{key.replace('_', ' ').title()}:** {value}")
        else:
            st.info("Summary data will be displayed here after analysis")
    
    with tabs[1]:
        display_order_blocks_simple(results.get('order_blocks', []))
        
    with tabs[2]:
        display_fvgs_simple(results.get('fvgs', []))
        
    with tabs[3]:
        display_liquidity_zones_simple(results.get('liquidity_zones', []))
        
    with tabs[4]:
        display_market_structure_simple(results.get('market_structure', {}))
        
    with tabs[5]:
        display_risk_management_simple(results.get('current_price', 0), results.get('market_structure', {}))

def display_order_blocks_simple(order_blocks):
    """Simple display for order blocks"""
    if not order_blocks:
        st.info("No active order blocks detected")
        return
        
    st.subheader("⬜ Order Blocks")
    
    for i, ob in enumerate(order_blocks[:5]):
        with st.expander(f"Order Block {i+1}"):
            col1, col2 = st.columns(2)
            with col1:
                st.write(f"**Type:** {getattr(ob, 'ob_type', 'Unknown')}")
                st.write(f"**Strength:** {ob.strength:.1%}")
            with col2:
                st.write(f"**High:** {ob.high:.2f}")
                st.write(f"**Low:** {ob.low:.2f}")

def display_fvgs_simple(fvgs):
    """Simple display for FVGs"""
    if not fvgs:
        st.info("No active fair value gaps detected")
        return
        
    st.subheader("⚡ Fair Value Gaps")
    
    for i, fvg in enumerate(fvgs[:5]):
        with st.expander(f"FVG {i+1}"):
            col1, col2 = st.columns(2)
            with col1:
                st.write(f"**Type:** {fvg.gap_type}")
                st.write(f"**Strength:** {fvg.strength:.1%}")
            with col2:
                st.write(f"**High:** {fvg.high:.2f}")
                st.write(f"**Low:** {fvg.low:.2f}")

def display_liquidity_zones_simple(liquidity_zones):
    """Simple display for liquidity zones"""
    if not liquidity_zones:
        st.info("No active liquidity zones detected")
        return
        
    st.subheader("💧 Liquidity Zones")
    
    for i, lz in enumerate(liquidity_zones[:5]):
        with st.expander(f"Liquidity Zone {i+1}"):
            col1, col2 = st.columns(2)
            with col1:
                st.write(f"**Type:** {lz.zone_type.value if hasattr(lz.zone_type, 'value') else str(lz.zone_type)}")
                st.write(f"**Strength:** {lz.strength:.1%}")
            with col2:
                st.write(f"**High:** {lz.high:.2f}")
                st.write(f"**Low:** {lz.low:.2f}")

def display_market_structure_simple(market_structure):
    """Simple display for market structure"""
    if not market_structure:
        st.info("No market structure data available")
        return
        
    st.subheader("🔄 Market Structure Details")
    
    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**Trend:** {market_structure.get('trend', 'N/A')}")
        st.write(f"**Event:** {market_structure.get('event', 'N/A')}")
    with col2:
        st.write(f"**Strength:** {market_structure.get('strength', 0):.1%}")
        
    # Display highs and lows if available
    highs = market_structure.get('highs', [])
    lows = market_structure.get('lows', [])
    
    if len(highs) > 0 or len(lows) > 0:
        st.write("**Recent Swing Points:**")
        if len(highs) > 0:
            st.write(f"Last High: {highs.iloc[-1]:.2f}" if hasattr(highs, 'iloc') else f"Highs detected: {len(highs)}")
        if len(lows) > 0:
            st.write(f"Last Low: {lows.iloc[-1]:.2f}" if hasattr(lows, 'iloc') else f"Lows detected: {len(lows)}")

def display_risk_management_simple(current_price, market_structure):
    """Simple risk management display"""
    if not current_price:
        st.info("No price data available for risk management")
        return
        
    st.subheader("🛡️ Risk Management")
    
    # Basic risk levels based on price
    stop_loss_pct = 0.05  # 5% stop loss
    take_profit_pct = 0.10  # 10% take profit
    
    stop_loss = current_price * (1 - stop_loss_pct)
    take_profit = current_price * (1 + take_profit_pct)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Stop Loss", f"{stop_loss:.2f} EGP", f"-{stop_loss_pct:.1%}")
        
    with col2:
        st.metric("Current Price", f"{current_price:.2f} EGP")
        
    with col3:
        st.metric("Take Profit", f"{take_profit:.2f} EGP", f"+{take_profit_pct:.1%}")
    
    # Risk-reward ratio
    risk_amount = current_price - stop_loss
    reward_amount = take_profit - current_price
    risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
    
    st.metric("Risk:Reward Ratio", f"1:{risk_reward_ratio:.2f}")
    
    # Position sizing calculator
    st.subheader("📏 Position Sizing")
    col1, col2 = st.columns(2)
    
    with col1:
        account_value = st.number_input("Account Value (EGP)", min_value=1000.0, value=100000.0, step=1000.0)
        risk_pct = st.slider("Risk Per Trade (%)", min_value=0.5, max_value=5.0, value=2.0, step=0.5)
    
    with col2:
        risk_amount_egp = account_value * (risk_pct / 100)
        position_size = risk_amount_egp / risk_amount if risk_amount > 0 else 0
        
        st.metric("Risk Amount", f"{risk_amount_egp:,.2f} EGP")
        st.metric("Position Size", f"{position_size:.0f} shares")
