"""
Consolidated Predictions Page - Combines all prediction functionality
"""

import streamlit as st
import pandas as pd
import numpy as np
import logging
import math
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

# Import prediction components
from app.components.prediction import prediction_component
from app.components.advanced_prediction import advanced_prediction_component
from app.components.enhanced_prediction import enhanced_prediction_component
from app.components.ensemble_predictions import ensemble_predictions_component

# Import utilities
from app.utils.data_processing import is_model_trained
from app.utils.state_manager import get_available_stock_files
from app.utils.session_state import get_session_value, set_session_value
from app.utils.select_best_model import select_best_model

# Configure logging
logger = logging.getLogger(__name__)

# Helper functions for enhanced error handling and validation
def validate_prediction_requirements() -> List[str]:
    """Validate all requirements for making predictions"""
    errors = []

    # Check historical data
    if not hasattr(st.session_state, 'historical_data') or st.session_state.historical_data is None:
        errors.append("Historical data is required")
    elif st.session_state.historical_data.empty:
        errors.append("Historical data is empty")
    elif len(st.session_state.historical_data) < 60:
        errors.append("Insufficient historical data (minimum 60 records required)")

    # Check symbol
    if not hasattr(st.session_state, 'symbol') or not st.session_state.symbol:
        errors.append("Stock symbol is required")

    # Check data quality
    if hasattr(st.session_state, 'historical_data') and st.session_state.historical_data is not None:
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in st.session_state.historical_data.columns]
        if missing_columns:
            errors.append(f"Missing required columns: {', '.join(missing_columns)}")

    return errors

def display_validation_errors(errors: List[str]):
    """Display validation errors with helpful guidance"""
    st.error("⚠️ **Cannot proceed with predictions due to the following issues:**")

    for i, error in enumerate(errors, 1):
        st.markdown(f"{i}. {error}")

    st.markdown("---")
    st.markdown("### 🔧 **Quick Solutions:**")

    col1, col2 = st.columns(2)
    with col1:
        if st.button("📤 Upload Data", type="primary", use_container_width=True, key="upload_data_nav_btn"):
            st.session_state.page = "Upload Data"
            st.rerun()
    with col2:
        if st.button("📊 Select Stock", use_container_width=True, key="select_stock_nav_btn"):
            st.session_state.page = "Stock Management"
            st.rerun()

def display_stock_info(symbol: str):
    """Display enhanced stock information"""
    st.info(f"📈 **Current Stock:** {symbol}")

    # Add data quality indicators
    if hasattr(st.session_state, 'historical_data') and st.session_state.historical_data is not None:
        df = st.session_state.historical_data

        # Quick stats
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Records", len(df))
        with col2:
            latest_price = df['Close'].iloc[-1] if not df.empty else 0
            st.metric("Latest Price", f"${latest_price:.2f}")
        with col3:
            date_range = (df['Date'].max() - df['Date'].min()).days if not df.empty else 0
            st.metric("Data Range", f"{date_range} days")
        with col4:
            data_quality = "Good" if len(df) >= 100 else "Limited" if len(df) >= 60 else "Poor"
            st.metric("Data Quality", data_quality)

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_trained_models_cache(symbol: str) -> Dict[str, List[int]]:
    """Get trained models with caching for performance"""
    common_horizons = [4, 15, 30, 60, 1440, 10080]
    model_types = ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']

    trained_models = {}
    for model_type in model_types:
        trained_horizons = []
        for horizon in common_horizons:
            if is_model_trained(symbol, horizon, model_type):
                trained_horizons.append(horizon)
        if trained_horizons:
            trained_models[model_type] = trained_horizons

    return trained_models

def check_api_server_status():
    """Check if TradingView API server is running"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_price_from_api_server(symbol: str) -> Optional[Dict]:
    """Fetch price data from TradingView API server"""
    try:
        import requests
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                # Extract price from the API response
                raw_price = stock_data[0].get('price', 0)

                # Convert from piasters to EGP (divide by 1000)
                # API returns prices in piasters (81500 = 81.50 EGP)
                price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                return {
                    'symbol': symbol,
                    'price': price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'api_data': stock_data[0]  # Store full API data for advanced analysis
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching from API server: {str(e)}")
        return None

def safe_get_live_data(symbol: str) -> Optional[pd.DataFrame]:
    """Safely fetch live data with comprehensive error handling and API server integration"""
    try:
        from scrapers.price_scraper import PriceScraper
        from datetime import datetime

        with st.spinner("Fetching latest price data..."):
            # Check if API server is available
            api_status = check_api_server_status()

            if api_status:
                st.info("🔥 Using TradingView API server for enhanced data")
                price_data = fetch_price_from_api_server(symbol)
                source_used = "TradingView API"
            else:
                st.info("📡 Using direct TradingView scraping")
                scraper = PriceScraper(source="tradingview")
                try:
                    price_data = scraper.get_price(symbol)
                    source_used = "TradingView Direct"
                finally:
                    scraper.close_driver()

            if price_data and isinstance(price_data, dict):
                # Convert scraper data format to prediction-compatible format
                current_time = datetime.now()
                price = price_data.get('price', 0)

                # Create DataFrame with required columns for predictions
                converted_data = {
                    'Date': current_time,
                    'Open': price,
                    'High': price * 1.001,  # Simulate small high variation
                    'Low': price * 0.999,   # Simulate small low variation
                    'Close': price,         # Main price for predictions
                    'Volume': 1000000,      # Default volume
                    'symbol': price_data.get('symbol', symbol),
                    'currency': price_data.get('currency', 'EGP'),
                    'timestamp': price_data.get('timestamp', current_time.isoformat()),
                    'source': source_used,
                    'real_time': price_data.get('real_time', False)
                }

                df = pd.DataFrame([converted_data])

                # Ensure Date is datetime
                df['Date'] = pd.to_datetime(df['Date'])

                # Check if this is sample data
                is_sample = price_data.get('source', '').lower() == 'sample data'
                is_real_time = price_data.get('real_time', False)
                is_api = source_used == "TradingView API"

                if is_sample:
                    st.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                elif is_api:
                    st.success(f"✅ Price fetched from TradingView API: {price:.2f} EGP")
                    st.info("🔥 Enhanced data with technical analysis available")
                elif is_real_time:
                    st.success(f"🔴 Real-time price fetched: {price:.2f} EGP")
                else:
                    st.success(f"⏱️ Live price fetched: {price:.2f} EGP (15-min delay)")

                return df
            else:
                st.warning("⚠️ Could not fetch live price data")
                return None

    except Exception as e:
        st.warning(f"⚠️ Error fetching live data: {str(e)}")
        logger.error(f"Live data fetch error: {str(e)}")
        return None

def validate_prediction_inputs(symbol: str, horizon: int, model_type: str) -> List[str]:
    """Validate prediction inputs comprehensively"""
    errors = []

    # Check if model is trained
    if model_type.lower() == 'auto':
        # For auto mode, check if any model is available for this horizon
        available_models = []
        for mt in ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']:
            if is_model_trained(symbol, horizon, mt, 'saved_models', 'minutes'):
                available_models.append(mt)

        if not available_models:
            errors.append(f"No trained models found for {horizon} minute horizon. Please train models first.")
    else:
        if not is_model_trained(symbol, horizon, model_type, 'saved_models', 'minutes'):
            errors.append(f"No trained {model_type.upper()} model found for {horizon} minute horizon")

    # Check data recency
    if hasattr(st.session_state, 'historical_data') and st.session_state.historical_data is not None:
        df = st.session_state.historical_data
        if not df.empty and 'Date' in df.columns:
            latest_date = pd.to_datetime(df['Date'].max())
            days_old = (datetime.now() - latest_date).days
            if days_old > 30:
                errors.append(f"Historical data is {days_old} days old - predictions may be less accurate")

    return errors

def calculate_model_agreement_metrics(prices: List[float], current_price: float) -> Dict[str, Any]:
    """
    Calculate comprehensive model agreement metrics

    Args:
        prices: List of predicted prices from different models
        current_price: Current stock price for reference

    Returns:
        Dictionary containing various agreement metrics
    """
    if not prices or len(prices) < 2:
        return {
            'agreement_level': 'N/A',
            'confidence_level': 'N/A',
            'cv': 0,
            'range_pct': 0,
            'explanation': 'Insufficient models for comparison'
        }

    # Basic statistics
    mean_price = np.mean(prices)
    std_dev = np.std(prices)
    price_range = np.max(prices) - np.min(prices)

    # Coefficient of variation (normalized measure)
    cv = std_dev / mean_price if mean_price > 0 else 1.0
    cv_pct = cv * 100

    # Range as percentage of current price
    range_pct = (price_range / current_price) * 100 if current_price > 0 else 0

    # Agreement classification with more nuanced thresholds
    if cv < 0.003:  # Less than 0.3% variation
        agreement_level = "Very High"
        confidence_level = "Excellent"
        agreement_color = "🟢"
        explanation = "Models are in exceptional agreement. Very reliable prediction."
    elif cv < 0.008:  # Less than 0.8% variation
        agreement_level = "High"
        confidence_level = "Good"
        agreement_color = "🟢"
        explanation = "Models show strong agreement. Reliable prediction."
    elif cv < 0.015:  # Less than 1.5% variation
        agreement_level = "Medium"
        confidence_level = "Moderate"
        agreement_color = "🟡"
        explanation = "Models show moderate agreement. Consider ensemble result."
    elif cv < 0.025:  # Less than 2.5% variation
        agreement_level = "Low"
        confidence_level = "Caution"
        agreement_color = "🟠"
        explanation = "Models disagree moderately. Use caution and consider market conditions."
    else:  # More than 2.5% variation
        agreement_level = "Very Low"
        confidence_level = "High Risk"
        agreement_color = "🔴"
        explanation = "Models disagree significantly. High uncertainty - consider waiting or reducing position size."

    return {
        'agreement_level': agreement_level,
        'confidence_level': confidence_level,
        'agreement_color': agreement_color,
        'cv': cv,
        'cv_pct': cv_pct,
        'range_pct': range_pct,
        'std_dev': std_dev,
        'price_range': price_range,
        'explanation': explanation,
        'mean_price': mean_price
    }

def get_disagreement_causes() -> List[str]:
    """
    Return common causes of model disagreement for user education
    """
    return [
        "**Market Volatility**: High volatility periods cause models to diverge",
        "**Different Model Types**: Neural networks vs tree-based models see different patterns",
        "**Training Data Differences**: Models trained on different time periods",
        "**Feature Sensitivity**: Some models are more sensitive to recent price movements",
        "**Market Regime Changes**: New market conditions not seen in training data",
        "**News/Events**: Unexpected events that models can't predict",
        "**Low Liquidity**: Thin trading can cause erratic price movements"
    ]

def format_egp_price(price: float) -> str:
    """
    Format price in EGP currency format

    Args:
        price: Price value to format

    Returns:
        Formatted price string like "81.50 EGP"
    """
    if price is None or np.isnan(price):
        return "N/A EGP"

    # Display price as-is without scaling
    return f"{price:.2f} EGP"

def format_egp_change(change: float) -> str:
    """
    Format price change in EGP currency format with sign

    Args:
        change: Price change value to format

    Returns:
        Formatted change string like "+5.23 EGP" or "-2.15 EGP"
    """
    if change is None or np.isnan(change):
        return "N/A EGP"

    # Display change as-is without scaling
    return f"{change:+.2f} EGP"

def validate_prediction(predicted_price: float, current_price: float, horizon: int) -> float:
    """
    Validate and correct unrealistic predictions to ensure they are reasonable.
    STRICTER validation to prevent wild predictions.

    Args:
        predicted_price: The raw prediction from the model
        current_price: The current market price
        horizon: Prediction horizon in minutes

    Returns:
        Corrected prediction price
    """
    if predicted_price is None or np.isnan(predicted_price) or predicted_price <= 0:
        logger.warning(f"Invalid prediction: {predicted_price}, using current price")
        return current_price

    # Calculate the percentage change
    if current_price > 0:
        percent_change = abs(predicted_price - current_price) / current_price
    else:
        return current_price

    # STRICTER limits based on horizon - Egyptian stock market realistic moves
    if horizon <= 60:  # 1 hour or less
        max_reasonable_change = 0.02  # 2% max for short term
    elif horizon <= 240:  # 4 hours or less  
        max_reasonable_change = 0.05  # 5% max for medium term
    elif horizon <= 1440:  # 1 day or less
        max_reasonable_change = 0.08  # 8% max for daily
    else:  # Longer term
        max_reasonable_change = 0.12  # 12% max for long term

    # If prediction is unrealistic, apply correction
    if percent_change > max_reasonable_change:
        logger.warning(f"UNREALISTIC prediction detected: {predicted_price:.2f} vs current {current_price:.2f} "
                      f"({percent_change:.1%} change, limit: {max_reasonable_change:.1%}). Applying correction.")

        # Apply a conservative correction within reasonable bounds
        direction = 1 if predicted_price > current_price else -1
        corrected_change = max_reasonable_change * 0.7  # Use 70% of max allowed change
        corrected_price = current_price * (1 + direction * corrected_change)

        logger.info(f"Corrected prediction: {predicted_price:.2f} -> {corrected_price:.2f} (change: {direction * corrected_change:.1%})")
        return corrected_price

    return predicted_price

def calculate_prediction_confidence(historical_data: pd.DataFrame, symbol: str, model_type: str, horizon: int) -> float:
    """Calculate confidence score for predictions based on historical performance"""
    try:
        # Simple confidence calculation based on data quality and model type
        data_quality_score = min(len(historical_data) / 100, 1.0)  # More data = higher confidence

        # Model-specific confidence adjustments
        model_confidence = {
            'rf': 0.85,
            'lstm': 0.80,
            'gb': 0.82,
            'lr': 0.70,
            'ensemble': 0.90
        }

        base_confidence = model_confidence.get(model_type, 0.75)

        # Adjust for horizon (shorter horizons generally more accurate)
        horizon_factor = max(0.5, 1.0 - (horizon / 1440) * 0.3)  # Decrease confidence for longer horizons

        final_confidence = base_confidence * data_quality_score * horizon_factor
        return min(final_confidence, 0.95)  # Cap at 95%

    except Exception:
        return 0.75  # Default confidence

def generate_prediction_insights(current_price: float, predicted_price: float, horizon: int) -> Dict[str, str]:
    """Generate insights about the prediction"""
    price_change = predicted_price - current_price
    percent_change = (price_change / current_price) * 100

    insights = {}

    # Direction insight
    if abs(percent_change) < 0.5:
        insights['direction'] = "📊 **Sideways Movement** - Price expected to remain relatively stable"
    elif percent_change > 0:
        insights['direction'] = "📈 **Bullish Signal** - Price expected to increase"
    else:
        insights['direction'] = "📉 **Bearish Signal** - Price expected to decrease"

    # Magnitude insight
    if abs(percent_change) < 1:
        insights['magnitude'] = "🔹 **Low Volatility** - Small price movement expected"
    elif abs(percent_change) < 3:
        insights['magnitude'] = "🔸 **Moderate Movement** - Noticeable price change expected"
    else:
        insights['magnitude'] = "🔶 **High Volatility** - Significant price movement expected"

    # Time-based insight
    if horizon <= 15:
        insights['timeframe'] = "⚡ **Short-term** - Suitable for scalping strategies"
    elif horizon <= 240:
        insights['timeframe'] = "🕐 **Intraday** - Good for day trading decisions"
    else:
        insights['timeframe'] = "📅 **Longer-term** - Consider for swing trading"

    return insights

def unified_prediction_engine(symbol: str, horizons: List[int], model_type: str, use_live_data: bool = True) -> Dict[int, float]:
    """
    Unified prediction engine to ensure consistent results across all prediction tabs.
    This is the SINGLE SOURCE OF TRUTH for all predictions in the app.
    """
    try:
        # Get historical data
        historical_data = st.session_state.historical_data
        if historical_data is None or historical_data.empty:
            raise ValueError("No historical data available")

        # Validate data quality
        if len(historical_data) < 60:
            raise ValueError(f"Insufficient historical data: {len(historical_data)} rows (minimum 60 required)")

        # Handle auto model selection with CONSISTENT logic
        if model_type.lower() == 'auto':
            # Find ALL available models for this horizon
            available_models = []
            for mt in ['ensemble', 'rf', 'gb', 'lstm', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']:  # Priority order
                if is_model_trained(symbol, horizons[0], mt, 'saved_models', 'minutes'):
                    available_models.append(mt)

            if not available_models:
                raise ValueError(f"No trained models found for {horizons[0]} minute horizon")

            # Use deterministic selection (not random!)
            # Priority: ensemble > rf > gb > lstm > lr
            model_type = available_models[0]  # First in priority order
            logger.info(f"CONSISTENT auto mode selected: {model_type} from {available_models}")

        # Enforce model type consistency
        final_model_type = model_type.lower()
        
        # Additional validation - ensure model is actually trained
        if not is_model_trained(symbol, horizons[0], final_model_type, 'saved_models', 'minutes'):
            # Fallback to available model
            fallback_models = ['ensemble', 'rf', 'gb', 'lstm', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']
            for fallback in fallback_models:
                if is_model_trained(symbol, horizons[0], fallback, 'saved_models', 'minutes'):
                    logger.warning(f"Model {final_model_type} not available, using fallback: {fallback}")
                    final_model_type = fallback
                    break
            else:
                raise ValueError(f"No trained models available for {symbol} {horizons[0]}min")

        # Get live data if requested
        current_live_data = None
        if use_live_data:
            current_live_data = safe_get_live_data(symbol)

        # Log prediction parameters for debugging
        logger.info(f"UNIFIED PREDICTION: symbol={symbol}, horizon={horizons[0]}, model={final_model_type}, live_data={current_live_data is not None}")

        # Make prediction using the appropriate method
        if current_live_data is not None and not current_live_data.empty:
            # Use predict_from_live_data for consistency
            from models.predict import predict_from_live_data
            predictions = predict_from_live_data(
                live_data=current_live_data,
                historical_data=historical_data,
                symbol=symbol,
                horizons=horizons,
                model_type=final_model_type,
                models_path='saved_models'
            )
            current_price = current_live_data['Close'].iloc[-1]
            st.info(f"🔴 **Live Data Used**: Current price {format_egp_price(current_price)} | Model: {final_model_type.upper()}")
        else:
            # Use historical data only
            from models.predict import predict_future_prices
            predictions = predict_future_prices(
                historical_data,
                symbol,
                horizons=horizons,
                model_type=final_model_type,
                models_path='saved_models'
            )
            current_price = historical_data['Close'].iloc[-1]
            st.info(f"📊 **Historical Data Used**: Latest price {format_egp_price(current_price)} | Model: {final_model_type.upper()}")

        # CRITICAL: Apply consistent validation to all predictions
        validated_predictions = {}
        for horizon, predicted_price in predictions.items():
            validated_price = validate_prediction(predicted_price, current_price, horizon)
            validated_predictions[horizon] = validated_price
            
            if validated_price != predicted_price:
                logger.warning(f"Prediction corrected: {predicted_price:.2f} -> {validated_price:.2f} for {horizon}min")

        return validated_predictions

    except Exception as e:
        logger.error(f"Unified prediction engine error: {str(e)}")
        raise

def explain_prediction_differences():
    """Explain why different prediction methods might give different results"""
    with st.expander("🤔 **Why do different prediction tabs give different results?**"):
        st.markdown("""
        **This is NORMAL and expected! Here's why:**

        ### **🔬 Different Algorithms**
        - **Quick Predictions**: Uses single models (RF, LSTM, etc.)
        - **Advanced Analysis**: May use different preprocessing or parameters
        - **Ensemble Models**: Combines multiple models (averaging results)
        - **Enhanced Predictions**: Uses adaptive model selection

        ### **📊 Different Data Processing**
        - **Feature Engineering**: Some tabs may use additional technical indicators
        - **Data Normalization**: Different scaling methods
        - **Sequence Length**: Different lookback periods

        ### **⚙️ Different Parameters**
        - **Model Configuration**: Different hyperparameters
        - **Training Data**: Some models may use different training sets
        - **Prediction Horizons**: Internal horizon conversions

        ### **💡 Which Result to Trust?**
        1. **Ensemble Models** - Generally most reliable (combines multiple models)
        2. **Enhanced Predictions** - Uses adaptive selection
        3. **Quick Predictions** - Good for fast estimates
        4. **Advanced Analysis** - Best for detailed analysis

        ### **🎯 Recommendation**
        Use **Ensemble Models** for final trading decisions as they provide the most robust predictions.
        """)

def show_predictions_consolidated():
    """Main function to display the consolidated Predictions page"""

    st.title("🔮 AI Predictions")
    st.markdown("### Advanced Stock Price Forecasting with Multiple AI Models")

    # 🚀 NEW FEATURES NOTIFICATION
    st.info("🆕 **New AI Predictions Available!** Check out the **� AI Predictions** tab above for:\n"
            "• 🤖 Real AI-driven price forecasts using trained models\n"
            "• 📊 Multiple prediction horizons (15min to 2 hours)\n"
            "• 🎯 Advanced model selection (LSTM, Random Forest, Ensemble, etc.)\n"
            "• 📈 Visual prediction charts and insights")
    
    # Enhanced validation with detailed error handling
    validation_errors = validate_prediction_requirements()
    if validation_errors:
        display_validation_errors(validation_errors)
        return

    # Display current stock info with enhanced details
    symbol = get_session_value('symbol')
    if symbol:
        display_stock_info(symbol)

    # Check for trained models with caching
    if symbol:
        trained_models_dict = get_trained_models_cache(symbol)

        if not trained_models_dict:
            st.warning("⚠️ No trained models found for this stock.")
            st.info("You need to train models before making predictions.")

            if st.button("🤖 Train Models Now", type="primary", key="train_models_nav_btn"):
                st.session_state.page = "Train Model"
                st.rerun()
            return
        else:
            total_models = sum(len(horizons) for horizons in trained_models_dict.values())
            st.success(f"✅ Found {len(trained_models_dict)} model types with {total_models} trained horizons")

            # Show available models summary
            with st.expander("📋 Available Models Summary"):
                for model_type, horizons in trained_models_dict.items():
                    st.write(f"**{model_type.upper()}**: {len(horizons)} horizons ({', '.join(map(str, horizons))} min)")

    # Add explanation about prediction differences
    explain_prediction_differences()

    # Create tabs for consolidated prediction types
    tab1, tab2, tab3, tab4 = st.tabs([
        "🚀 Quick Predictions",
        "🎯 Advanced Predictions", 
        "� AI Predictions",
        "�📊 Analysis & Comparison"
    ])

    with tab1:
        show_quick_predictions()

    with tab2:
        show_advanced_consolidated_predictions()
        
    with tab3:
        show_ai_predictions_section()

    with tab4:
        show_model_comparison_enhanced()

def show_quick_predictions():
    """Quick and simple predictions interface"""

    st.header("🚀 Quick Predictions")
    st.markdown("Get fast predictions with minimal configuration.")

    # Simple interface
    col1, col2 = st.columns(2)

    with col1:
        # Prediction horizon selection
        horizon_type = st.selectbox(
            "Prediction Timeframe",
            ["Short-term (minutes)", "Medium-term (hours)", "Long-term (days)"],
            key="quick_pred_timeframe_unique"
        )

        if horizon_type == "Short-term (minutes)":
            horizons = [5, 15, 30, 60]
            unit = "minutes"
        elif horizon_type == "Medium-term (hours)":
            horizons = [120, 240, 480, 720]  # 2h, 4h, 8h, 12h
            unit = "minutes"
        else:  # Long-term
            horizons = [1440, 2880, 7200, 10080]  # 1d, 2d, 5d, 1w
            unit = "minutes"

        selected_horizon = st.selectbox(
            f"Select horizon ({unit})",
            horizons,
            key="quick_pred_horizon_unique"
        )

    with col2:
        # Model selection with ALL available models
        model_options = [
            "Auto (Best Available)",
            "Ensemble",
            "Random Forest",
            "LSTM",
            "Gradient Boosting",
            "Linear Regression",
            "XGBoost",
            "Support Vector Regression",
            "Prophet",
            "Hybrid"
        ]

        model_type = st.selectbox(
            "Model Type",
            model_options,
            key="quick_pred_model_unique",
            help="All 9 trained models are now available! Ensemble combines multiple models for best results."
        )

        # Convert display names to internal names
        model_mapping = {
            "Auto (Best Available)": "auto",
            "Ensemble": "ensemble",
            "Random Forest": "rf",
            "LSTM": "lstm",
            "Gradient Boosting": "gb",
            "Linear Regression": "lr",
            "XGBoost": "xgb",
            "Support Vector Regression": "svr",
            "Prophet": "prophet",
            "Hybrid": "hybrid"
        }

        internal_model = model_mapping[model_type]

    # Check if we have the required data
    if not st.session_state.symbol:
        st.warning("Please select a stock first.")
        return

    if st.session_state.historical_data is None:
        st.warning("Please load historical data first.")
        return

    # Prediction button
    if st.button("🔮 Generate Prediction", type="primary", use_container_width=True, key="quick_pred_generate_btn"):
        with st.spinner("Generating prediction..."):
            try:
                # Import required modules
                import plotly.graph_objects as go

                # Enhanced validation
                validation_errors = validate_prediction_inputs(st.session_state.symbol, selected_horizon, internal_model)
                if validation_errors:
                    for error in validation_errors:
                        st.error(f"❌ {error}")
                    st.info("💡 Please address the issues above before generating predictions.")
                    return

                # Use the unified prediction engine for consistency
                predictions = unified_prediction_engine(
                    symbol=st.session_state.symbol,
                    horizons=[selected_horizon],
                    model_type=internal_model,
                    use_live_data=True
                )

                if selected_horizon in predictions:
                    predicted_price = predictions[selected_horizon]

                    # Calculate prediction time
                    current_time = datetime.now()
                    pred_time = current_time + timedelta(minutes=selected_horizon)

                    # Get current price (check if live data was used in prediction)
                    if hasattr(st.session_state, 'live_data') and st.session_state.live_data is not None:
                        current_price = st.session_state.live_data['Close'].iloc[-1]
                    else:
                        current_price = st.session_state.historical_data['Close'].iloc[-1]

                    # Validate and correct unrealistic predictions
                    predicted_price = validate_prediction(predicted_price, current_price, selected_horizon)

                    # Calculate change
                    price_change = predicted_price - current_price
                    percent_change = (price_change / current_price) * 100

                    # Calculate confidence and insights
                    confidence = calculate_prediction_confidence(
                        st.session_state.historical_data,
                        st.session_state.symbol,
                        internal_model,
                        selected_horizon
                    )
                    insights = generate_prediction_insights(current_price, predicted_price, selected_horizon)

                    # Display results with enhanced information + DEBUG INFO
                    st.success("🎯 Prediction generated successfully!")

                    # Show debug information for transparency
                    with st.expander("🔍 **Prediction Debug Information**"):
                        st.write(f"**Selected Model**: {internal_model}")
                        st.write(f"**Horizon**: {selected_horizon} minutes")
                        st.write(f"**Raw Prediction**: {predictions[selected_horizon]:.2f} EGP")
                        st.write(f"**Validated Prediction**: {predicted_price:.2f} EGP")
                        st.write(f"**Current Price**: {current_price:.2f} EGP")
                        st.write(f"**Validation Applied**: {'Yes' if predictions[selected_horizon] != predicted_price else 'No'}")
                        
                        # Show model selection process
                        all_available = []
                        for mt in ['ensemble', 'rf', 'gb', 'lstm', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']:
                            if is_model_trained(st.session_state.symbol, selected_horizon, mt):
                                all_available.append(mt)
                        st.write(f"**Available Models**: {', '.join(all_available)}")
                        st.write(f"**Selection Method**: {'Auto (first priority)' if internal_model == 'auto' else 'User selected'}")

                    # Create columns for results
                    result_col1, result_col2, result_col3, result_col4 = st.columns(4)

                    with result_col1:
                        st.metric(
                            "Current Price",
                            format_egp_price(current_price),
                            delta=None
                        )

                    with result_col2:
                        st.metric(
                            f"Predicted Price ({selected_horizon} min)",
                            format_egp_price(predicted_price),
                            delta=format_egp_change(price_change)
                        )

                    with result_col3:
                        st.metric(
                            "Expected Change",
                            f"{percent_change:+.2f}%",
                            delta=None
                        )

                    with result_col4:
                        confidence_color = "🟢" if confidence > 0.8 else "🟡" if confidence > 0.6 else "🔴"
                        st.metric(
                            "Confidence",
                            f"{confidence_color} {confidence:.1%}",
                            delta=None
                        )

                    # Display insights
                    st.markdown("### 🧠 **Prediction Insights**")
                    insight_cols = st.columns(3)

                    with insight_cols[0]:
                        st.markdown(insights['direction'])
                    with insight_cols[1]:
                        st.markdown(insights['magnitude'])
                    with insight_cols[2]:
                        st.markdown(insights['timeframe'])

                    # Show prediction details
                    st.info(f"**Prediction Details:**\n"
                           f"- Stock: {st.session_state.symbol}\n"
                           f"- Model: {model_type}\n"
                           f"- Horizon: {selected_horizon} minutes\n"
                           f"- Prediction Time: {pred_time.strftime('%Y-%m-%d %H:%M:%S')}")

                    # Create a simple chart
                    import plotly.graph_objects as go

                    fig = go.Figure()

                    # Add historical data (last 30 points)
                    hist_data = st.session_state.historical_data.tail(30)
                    fig.add_trace(go.Scatter(
                        x=hist_data['Date'],
                        y=hist_data['Close'],
                        mode='lines',
                        name='Historical',
                        line=dict(color='blue')
                    ))

                    # Add current price point if live data exists
                    if hasattr(st.session_state, 'live_data') and st.session_state.live_data is not None:
                        fig.add_trace(go.Scatter(
                            x=st.session_state.live_data['Date'],
                            y=st.session_state.live_data['Close'],
                            mode='markers',
                            name='Current',
                            marker=dict(color='green', size=10)
                        ))

                    # Add prediction point
                    fig.add_trace(go.Scatter(
                        x=[pred_time],
                        y=[predicted_price],
                        mode='markers',
                        name='Prediction',
                        marker=dict(color='red', size=12, symbol='star')
                    ))

                    # Add trend line
                    if hasattr(st.session_state, 'live_data') and st.session_state.live_data is not None:
                        last_time = st.session_state.live_data['Date'].iloc[-1]
                        last_price = st.session_state.live_data['Close'].iloc[-1]
                    else:
                        last_time = st.session_state.historical_data['Date'].iloc[-1]
                        last_price = st.session_state.historical_data['Close'].iloc[-1]

                    fig.add_trace(go.Scatter(
                        x=[last_time, pred_time],
                        y=[last_price, predicted_price],
                        mode='lines',
                        name='Trend',
                        line=dict(color='red', dash='dot')
                    ))

                    fig.update_layout(
                        title=f'{st.session_state.symbol} Quick Prediction',
                        xaxis_title='Date',
                        yaxis_title='Price',
                        hovermode='x unified'
                    )

                    st.plotly_chart(fig, use_container_width=True)

                    # Store prediction in session state
                    st.session_state.last_quick_prediction = {
                        'symbol': st.session_state.symbol,
                        'model': model_type,
                        'horizon': selected_horizon,
                        'current_price': current_price,
                        'predicted_price': predicted_price,
                        'prediction_time': pred_time,
                        'generated_at': current_time
                    }

                else:
                    st.error("Failed to generate prediction. Please try again.")

            except Exception as e:
                st.error(f"Error generating prediction: {str(e)}")
                logger.error(f"Quick prediction error: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")

                # Show more specific error information
                if "Ensemble" in str(e):
                    st.info("**Ensemble Model Issue:** This might be due to missing ensemble model files. Try using a different model type or train an ensemble model first.")
                elif "model" in str(e).lower():
                    st.info("**Model Loading Issue:** The selected model might not be trained for this horizon. Please train the model first.")
                else:
                    st.info("**General Error:** Please check the logs for more details or try with different settings.")

def show_model_comparison_enhanced():
    """Compare different models and their performance with enhanced live comparison"""

    st.header("📊 Enhanced Model Comparison")
    st.markdown("**Compare predictions from different models side-by-side with advanced insights.**")

    if not st.session_state.symbol:
        st.warning("Please select a stock first.")
        return

    symbol = st.session_state.symbol

    # Model performance comparison
    st.subheader("🏆 Model Availability Matrix")

    # Check available models
    model_types = ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']
    horizons = [5, 15, 30, 60, 1440]

    # Create performance matrix
    performance_data = []

    for model in model_types:
        model_row = {'Model': model.upper()}
        for horizon in horizons:
            if is_model_trained(symbol, horizon, model):
                model_row[f'{horizon}min'] = '✅ Trained'
            else:
                model_row[f'{horizon}min'] = '❌ Not Trained'
        performance_data.append(model_row)

    if performance_data:
        df_performance = pd.DataFrame(performance_data)
        st.dataframe(df_performance, use_container_width=True)

    # Live Model Comparison
    st.subheader("🔬 Live Model Comparison")
    st.markdown("**Test different models with the same parameters to see how they differ:**")

    # Selection for comparison
    col1, col2 = st.columns(2)

    with col1:
        comparison_horizon = st.selectbox(
            "Select horizon for comparison",
            [5, 15, 30, 60],
            index=1,
            key="comparison_horizon_unique"
        )

    with col2:
        available_models = []
        for model in model_types:
            if is_model_trained(symbol, comparison_horizon, model):
                available_models.append(model)

        if len(available_models) < 2:
            st.warning(f"Need at least 2 trained models for {comparison_horizon}min horizon")
            return

        st.info(f"✅ {len(available_models)} models available for comparison")

    # Compare models button
    if st.button("🔍 Compare All Available Models", type="primary", use_container_width=True, key="model_comparison_btn"):
        with st.spinner("Generating predictions from all models..."):
            try:
                comparison_results = {}

                for model_type in available_models:
                    try:
                        predictions = unified_prediction_engine(
                            symbol=symbol,
                            horizons=[comparison_horizon],
                            model_type=model_type,
                            use_live_data=True
                        )
                        if comparison_horizon in predictions:
                            comparison_results[model_type] = predictions[comparison_horizon]
                    except Exception as e:
                        st.warning(f"Failed to get prediction from {model_type}: {str(e)}")

                if comparison_results:
                    # Display comparison results
                    st.subheader("📈 Prediction Comparison Results")

                    # Get current price for reference
                    current_price = st.session_state.historical_data['Close'].iloc[-1]

                    # Create comparison table with validation
                    comparison_data = []
                    validated_results = {}
                    for model, predicted_price in comparison_results.items():
                        # Validate and correct unrealistic predictions
                        validated_price = validate_prediction(predicted_price, current_price, comparison_horizon)
                        validated_results[model] = validated_price

                        price_change = validated_price - current_price
                        percent_change = (price_change / current_price) * 100

                        comparison_data.append({
                            'Model': model.upper(),
                            'Predicted Price': format_egp_price(validated_price),
                            'Price Change': format_egp_change(price_change),
                            'Percent Change': f"{percent_change:+.2f}%",
                            'Direction': "📈 Bullish" if price_change > 0 else "📉 Bearish" if price_change < 0 else "📊 Neutral"
                        })

                    df_comparison = pd.DataFrame(comparison_data)
                    st.dataframe(df_comparison, use_container_width=True)

                    # Statistical analysis
                    st.subheader("📊 Statistical Analysis")

                    prices = list(validated_results.values())
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("Average Prediction", format_egp_price(np.mean(prices)))
                    with col2:
                        st.metric("Prediction Range", format_egp_price(np.max(prices) - np.min(prices)))
                    with col3:
                        st.metric("Standard Deviation", format_egp_price(np.std(prices)))
                    with col4:
                        # Use the new comprehensive agreement calculation
                        agreement_metrics = calculate_model_agreement_metrics(prices, current_price)
                        st.metric("Model Agreement", f"{agreement_metrics['agreement_color']} {agreement_metrics['agreement_level']}")

                    # Enhanced Interpretation with more details
                    st.subheader("🧠 Interpretation")

                    # Display the main interpretation
                    if agreement_metrics['agreement_level'] in ['Very High', 'High']:
                        st.success(f"🎯 **{agreement_metrics['agreement_level']} Agreement**: {agreement_metrics['explanation']}")
                    elif agreement_metrics['agreement_level'] == 'Medium':
                        st.warning(f"⚖️ **{agreement_metrics['agreement_level']} Agreement**: {agreement_metrics['explanation']}")
                    else:
                        st.error(f"⚠️ **{agreement_metrics['agreement_level']} Agreement**: {agreement_metrics['explanation']}")

                    # Additional analysis
                    st.write("**Detailed Analysis:**")
                    col_a, col_b = st.columns(2)

                    with col_a:
                        st.write(f"• **Price Range**: {format_egp_price(agreement_metrics['price_range'])} ({agreement_metrics['range_pct']:.2f}% of current price)")
                        st.write(f"• **Coefficient of Variation**: {agreement_metrics['cv_pct']:.2f}%")
                        st.write(f"• **Number of Models**: {len(prices)}")

                    with col_b:
                        st.write(f"• **Confidence Level**: {agreement_metrics['agreement_color']} {agreement_metrics['confidence_level']}")
                        direction_consensus = "Bullish" if agreement_metrics['mean_price'] > current_price else "Bearish"
                        st.write(f"• **Direction Consensus**: {direction_consensus}")
                        st.write(f"• **Prediction Spread**: {((np.max(prices) - np.min(prices)) / agreement_metrics['mean_price'] * 100):.2f}%")

                    # Recommendation with enhanced logic
                    ensemble_available = 'ensemble' in validated_results
                    if ensemble_available:
                        ensemble_price = validated_results['ensemble']
                        st.info(f"💡 **Recommendation**: Use the Ensemble model result ({format_egp_price(ensemble_price)}) as it combines all models and reduces individual model bias.")
                    else:
                        avg_prediction = agreement_metrics['mean_price']
                        st.info(f"💡 **Recommendation**: Consider the average prediction ({format_egp_price(avg_prediction)}) as a consensus estimate.")

                    # Risk assessment with more nuanced warnings
                    if agreement_metrics['agreement_level'] in ['Low', 'Very Low']:
                        st.warning("⚠️ **Risk Warning**: Model disagreement suggests increased market uncertainty. Consider waiting for more stable conditions or using smaller position sizes.")

                    # Educational section about disagreement causes
                    if agreement_metrics['agreement_level'] in ['Low', 'Very Low']:
                        with st.expander("🤔 **Why are models disagreeing?**"):
                            st.write("**Common causes of model disagreement:**")
                            causes = get_disagreement_causes()
                            for cause in causes:
                                st.write(f"• {cause}")

                            st.write("\n**What you can do:**")
                            st.write("• Wait for more stable market conditions")
                            st.write("• Use smaller position sizes to reduce risk")
                            st.write("• Consider the ensemble result as it balances all models")
                            st.write("• Check recent news or events that might affect the stock")
                            st.write("• Look at multiple timeframes for confirmation")

                else:
                    st.error("No successful predictions generated for comparison.")

            except Exception as e:
                st.error(f"Error during model comparison: {str(e)}")
                logger.error(f"Model comparison error: {str(e)}")

    # Model recommendations
    st.subheader("💡 Model Recommendations")

    recommendations = {
        "Short-term (< 1 hour)": {
            "Best Models": ["LSTM", "Random Forest"],
            "Reason": "Neural networks excel at capturing short-term patterns"
        },
        "Medium-term (1-24 hours)": {
            "Best Models": ["Ensemble", "Gradient Boosting"],
            "Reason": "Ensemble methods provide stability for medium-term forecasts"
        },
        "Long-term (> 1 day)": {
            "Best Models": ["Random Forest", "Linear Regression"],
            "Reason": "Traditional ML models handle long-term trends well"
        }
    }

    for timeframe, info in recommendations.items():
        with st.expander(f"📈 {timeframe}"):
            st.write(f"**Recommended Models:** {', '.join(info['Best Models'])}")
            st.write(f"**Reason:** {info['Reason']}")

    # Quick training suggestions
    st.subheader("🚀 Quick Actions")

    action_cols = st.columns(3)

    with action_cols[0]:
        if st.button("🤖 Train More Models", use_container_width=True, key="train_more_models_btn"):
            st.session_state.page = "Train Model"
            st.rerun()

    with action_cols[1]:
        if st.button("📊 View Performance Metrics", use_container_width=True, key="view_performance_btn"):
            st.session_state.page = "Performance Metrics"
            st.rerun()

    with action_cols[2]:
        if st.button("📈 Live Trading", use_container_width=True, key="live_trading_btn"):
            st.session_state.page = "Live Trading"
            st.rerun()

def show_advanced_consolidated_predictions():
    """Advanced consolidated predictions combining ensemble and enhanced features with additional improvements"""

    st.header("🎯 Advanced Predictions")
    st.markdown("Comprehensive predictions with adaptive model selection, confidence intervals, and hybrid ensembles.")

    if not st.session_state.symbol:
        st.warning("Please select a stock first.")
        return

    symbol = st.session_state.symbol

    # Prediction horizon selection
    horizon = st.selectbox(
        "Select prediction horizon (minutes):",
        [5, 15, 30, 60, 240, 1440],
        index=3,
        key="advanced_pred_horizon"
    )

    # Adaptive model selection - Use SAME logic as other tabs
    st.subheader("🔍 Consistent Model Selection")
    available_models = [model for model in ['ensemble', 'rf', 'gb', 'lstm', 'lr', 'xgb', 'svr', 'prophet', 'hybrid'] if is_model_trained(symbol, horizon, model)]

    if not available_models:
        st.warning("No trained models available for the selected horizon.")
        return

    # Use the SAME deterministic selection as unified engine
    best_model = available_models[0]  # First in priority order (ensemble > rf > gb > lstm > lr)
    st.info(f"✅ Model selected (priority-based): {best_model.upper()}")
    st.write(f"**Available models**: {', '.join([m.upper() for m in available_models])}")
    st.write(f"**Selection logic**: Using highest priority model (ensemble > rf > gb > lstm > lr)")

    # Generate predictions
    if st.button("🔮 Generate Advanced Prediction", type="primary", use_container_width=True, key="advanced_pred_generate_btn"):
        with st.spinner("Generating prediction..."):
            try:
                predictions = unified_prediction_engine(
                    symbol=symbol,
                    horizons=[horizon],
                    model_type=best_model,
                    use_live_data=True
                )

                if horizon in predictions:
                    predicted_price = predictions[horizon]

                    # Confidence intervals
                    lower_bound = predicted_price * 0.95  # Example: 5% lower
                    upper_bound = predicted_price * 1.05  # Example: 5% higher

                    # Display results
                    st.success("🎯 Prediction generated successfully!")

                    # Show debug information for transparency
                    with st.expander("🔍 **Advanced Prediction Debug Information**"):
                        st.write(f"**Selected Model**: {best_model}")
                        st.write(f"**Horizon**: {horizon} minutes") 
                        st.write(f"**Raw Prediction**: {predictions[horizon]:.2f} EGP")
                        st.write(f"**Validated Prediction**: {predicted_price:.2f} EGP")
                        current_ref_price = st.session_state.historical_data['Close'].iloc[-1]
                        st.write(f"**Reference Price**: {current_ref_price:.2f} EGP")
                        st.write(f"**Validation Applied**: {'Yes' if predictions[horizon] != predicted_price else 'No'}")
                        st.write(f"**Available Models**: {', '.join([m.upper() for m in available_models])}")
                        st.write(f"**Selection Method**: Priority-based (deterministic)")

                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.metric("Current Price", format_egp_price(st.session_state.historical_data['Close'].iloc[-1]))
                    with col2:
                        st.metric("Predicted Price", format_egp_price(predicted_price))
                    with col3:
                        st.metric("Confidence Interval", f"{format_egp_price(lower_bound)} - {format_egp_price(upper_bound)}")

                    # Hybrid ensemble explanation
                    st.markdown("### 🧠 Hybrid Ensemble Insights")
                    st.markdown("Combining predictions from multiple models reduces bias and improves reliability.")

                else:
                    st.error("Failed to generate prediction. Please try again.")

            except Exception as e:
                st.error(f"Error generating prediction: {str(e)}")
                logger.error(f"Advanced prediction error: {str(e)}")

def show_ai_predictions_section():
    """
    Simple AI predictions section to replace the complex enhanced predictions page.
    """
    st.markdown("### 🔮 AI Price Predictions")
    st.markdown("*Generate actual AI-driven price forecasts*")
    
    col1, col2 = st.columns(2)
    
    with col1:
        models = ['ensemble', 'lstm', 'rf', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']

        # Model descriptions for better user understanding
        model_descriptions = {
            'ensemble': 'ENSEMBLE - Combines multiple models (Recommended)',
            'lstm': 'LSTM - Long Short-Term Memory (Neural Network)',
            'rf': 'RF - Random Forest (Tree-based)',
            'gb': 'GB - Gradient Boosting (Tree-based)',
            'lr': 'LR - Linear Regression (Linear)',
            'xgb': 'XGB - XGBoost (Advanced Tree-based)',
            'svr': 'SVR - Support Vector Regression (Kernel-based)',
            'prophet': 'PROPHET - Facebook Prophet (Time Series)',
            'hybrid': 'HYBRID - ARIMA + ML Hybrid (Advanced)'
        }

        selected_model = st.selectbox(
            "Select AI Model",
            models,
            index=0,
            format_func=lambda x: model_descriptions.get(x, x.upper()),
            help="All 9 trained models are now available for AI predictions!"
        )
    
    with col2:
        horizons = [15, 30, 60, 120]
        selected_horizons = st.multiselect(
            "Prediction Horizons (minutes)",
            horizons,
            default=[30, 60]
        )
    
    if selected_horizons and st.button("🚀 Generate AI Predictions", type="primary"):
        try:
            with st.spinner("Generating predictions..."):
                from models.predict import predict_future_prices
                
                # Get current data
                if not hasattr(st.session_state, 'historical_data') or st.session_state.historical_data is None:
                    st.error("Please load stock data first")
                    return
                
                df = st.session_state.historical_data
                symbol = getattr(st.session_state, 'symbol', 'Unknown')
                current_price = df['Close'].iloc[-1]
                
                # Generate predictions
                predictions = predict_future_prices(
                    df,
                    symbol,
                    horizons=selected_horizons,
                    model_type=selected_model
                )
                
                if predictions:
                    st.success("✅ Predictions generated successfully!")
                    
                    # Display predictions
                    cols = st.columns(len(selected_horizons))
                    for i, horizon in enumerate(selected_horizons):
                        if horizon in predictions:
                            predicted_price = predictions[horizon]
                            change = predicted_price - current_price
                            change_pct = (change / current_price) * 100
                            
                            with cols[i]:
                                st.metric(
                                    f"{horizon}min",
                                    f"{predicted_price:,.0f} EGP",
                                    f"{change_pct:+.1f}%"
                                )
                    
                    # Summary
                    st.markdown("#### 📊 Prediction Summary")
                    avg_change = sum(predictions[h] - current_price for h in selected_horizons if h in predictions) / len([h for h in selected_horizons if h in predictions])
                    avg_change_pct = (avg_change / current_price) * 100
                    
                    direction = "📈 Bullish" if avg_change > 0 else "📉 Bearish"
                    st.info(f"**{selected_model.upper()} Model**: {direction} - Average change: {avg_change_pct:+.1f}%")
                    
                else:
                    st.warning("No predictions generated. Model may not be trained for this stock/horizon.")
                    
        except Exception as e:
            st.error(f"Prediction error: {str(e)}")
