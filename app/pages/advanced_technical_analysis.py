import streamlit as st
import pandas as pd
import requests
import numpy as np
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
import os
import sys

# Try to import TA-Lib for advanced technical indicators
try:
    import talib  # type: ignore
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    talib = None  # Set to None for type checking

# Add path for SMC components
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# Configure logging
logger = logging.getLogger(__name__)

# Note: AI predictions are handled by dedicated prediction functions
# This page focuses purely on professional technical analysis
logger.info("📊 Advanced Technical Analysis - Pure Technical Analysis Mode")

# Log TA-Lib availability
if not TALIB_AVAILABLE:
    logger.warning("TA-Lib not available. Some advanced indicators will use simplified calculations.")

# Market regime detection is implemented locally in this module
MARKET_REGIME_AVAILABLE = True

# Advanced TA Comprehensive Analysis will be implemented here
TA_COMPREHENSIVE_AVAILABLE = True

# TradingView API Configuration
TRADINGVIEW_API_URL = "http://127.0.0.1:8000/api/scrape_pairs"

# EGX Stock symbols
EGX_STOCKS = {
    "COMI": "Commercial International Bank",
    "FWRY": "Fawry Banking Technology",
    "PHDC": "Palm Hills Development",
    "EFID": "Edita Food Industries",
    "UBEE": "United Bank Egypt",
    "GGRN": "GoGreen Agricultural",
    "OBRI": "Orascom Business Intelligence",
    "UTOP": "United Top"
}

@dataclass
class MarketRegimeAnalysis:
    """Market regime analysis result"""
    regime: str  # bull, bear, sideways, volatile, normal
    confidence: float
    volatility: float
    trend_strength: float
    description: str

# Currency formatting functions (for display)
def format_egp_price(price: float, is_api_data=True) -> str:
    """Format price in EGP currency format
    
    Args:
        price: The price value
        is_api_data: True if from API (needs division by 1000), False if from CSV (already in EGP)
    """
    if price is None or np.isnan(price):
        return "N/A EGP"

    if is_api_data:
        # Convert API price to EGP (API returns price * 1000)
        # Example: 78620.0 -> 78.62 EGP
        egp_price = price / 1000
    else:
        # CSV data is already in EGP
        egp_price = price
    
    return f"{egp_price:.2f} EGP"

def get_available_stock_symbols():
    """Return a list of stock symbols based on CSV files in the data/stocks directory."""
    stocks_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/stocks'))
    symbols = []
    if os.path.exists(stocks_dir):
        for fname in os.listdir(stocks_dir):
            if fname.endswith('.csv'):
                symbols.append(os.path.splitext(fname)[0])
    return sorted(symbols)

def show_advanced_technical_analysis():
    """Display enhanced TradingView + AI technical analysis"""

    st.title("📊 Advanced Technical Analysis")
    st.markdown("### Professional TradingView Technical Analysis & Trading Signals")
    st.info("🎯 **Professional Analysis**: Comprehensive technical indicators, market regime detection, and professional trading signals!")

    # Check API server status
    api_status = check_api_status()
    if not api_status:
        st.error("❌ TradingView API server is not running. Please start the server first.")
        st.info("💡 To start the TradingView API server, please check the documentation for setup instructions.")
        return

    st.success("✅ TradingView API server is running")

    # Enhanced interface with AI options
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        st.subheader("📈 Select Stocks for Analysis")

        # Get available stocks from data directory
        available_symbols = get_available_stock_symbols()
        # Build options: use friendly name if available, else just symbol
        stock_options = {k: EGX_STOCKS[k] if k in EGX_STOCKS else k for k in available_symbols}
        if not stock_options:
            st.error("No valid stock data found in data/stocks directory.")
            return
        selected_stocks = st.multiselect(
            "Choose EGX stocks to analyze:",
            options=list(stock_options.keys()),
            default=[list(stock_options.keys())[0]],
            format_func=lambda x: f"{x} - {EGX_STOCKS[x]}" if x in EGX_STOCKS else x,
            help="Select one or more stocks for comprehensive technical analysis"
        )

    with col2:
        st.subheader("⏰ Time Intervals")

        # Time interval selection
        selected_intervals = st.multiselect(
            "Analysis timeframes:",
            options=["1D", "1W"],
            default=["1D", "1W"],
            help="Select timeframes for technical analysis"
        )

    with col3:
        st.subheader("🎭 Market Analysis")

        # Market regime analysis option
        enable_market_regime = st.checkbox(
            "🎭 Market Regime",
            value=MARKET_REGIME_AVAILABLE,
            disabled=not MARKET_REGIME_AVAILABLE,
            help="Detect current market regime"
        )

    if not selected_stocks:
        st.warning("⚠️ Please select at least one stock for analysis.")
        return

    if not selected_intervals:
        st.warning("⚠️ Please select at least one time interval.")
        return

    # Enhanced analysis button
    if st.button("🚀 Run Enhanced Analysis", type="primary", use_container_width=True):
        with st.spinner("🔄 Running comprehensive TradingView + AI analysis..."):

            # Prepare API request for TradingView data
            egx_symbols = [f"EGX-{stock}" for stock in selected_stocks]
            analysis_data = fetch_technical_analysis(egx_symbols, selected_intervals)

            if analysis_data:
                # Run enhanced analysis with market regime detection
                display_enhanced_analysis_results(
                    analysis_data,
                    selected_stocks,
                    selected_intervals,
                    enable_market_regime
                )
            else:
                st.error("❌ Failed to fetch technical analysis data. Please try again.")

def get_stock_data(symbol: str) -> Optional[pd.DataFrame]:
    """Get historical stock data for AI analysis (same format as training data)"""
    try:
        import os
        data_path = f"data/stocks/{symbol}.csv"
        if os.path.exists(data_path):
            df = pd.read_csv(data_path)

            # Keep original format - don't modify column names or structure
            # This ensures compatibility with trained models
            if 'Date' in df.columns:
                df['Date'] = pd.to_datetime(df['Date'])
            elif 'date' in df.columns:
                df['Date'] = pd.to_datetime(df['date'])

            # Return recent data for prediction (same format as training)
            return df.tail(250)  # Last 250 days with all original features
        return None
    except Exception as e:
        logger.error(f"Error loading historical stock data: {e}")
        return None

def detect_market_regime(df: pd.DataFrame) -> MarketRegimeAnalysis:
    """Detect current market regime using simple technical analysis"""
    try:
        # Use capitalized column names to match CSV
        close_col = 'Close' if 'Close' in df.columns else 'close'
        returns = df[close_col].pct_change().dropna()
        volatility = returns.rolling(window=20).std().iloc[-1] if len(returns) >= 20 else returns.std()
        
        # Calculate trend over different periods
        if len(df) >= 20:
            trend_20 = (df[close_col].iloc[-1] - df[close_col].iloc[-20]) / df[close_col].iloc[-20]
        else:
            trend_20 = (df[close_col].iloc[-1] - df[close_col].iloc[0]) / df[close_col].iloc[0]
            
        # Determine regime based on volatility and trend
        if volatility > 0.025:  # High volatility threshold
            regime = 'volatile'
            confidence = min(0.9, volatility * 40)
            description = f"High volatility period ({volatility:.1%} daily)"
        elif trend_20 > 0.1:  # Strong uptrend
            regime = 'bull'
            confidence = min(0.9, abs(trend_20) * 10)
            description = f"Strong upward trend ({trend_20:+.1%})"
        elif trend_20 < -0.1:  # Strong downtrend
            regime = 'bear'
            confidence = min(0.9, abs(trend_20) * 10)
            description = f"Strong downward trend ({trend_20:+.1%})"
        elif abs(trend_20) < 0.05:  # Sideways movement
            regime = 'sideways'
            confidence = 0.7
            description = "Consolidation phase with limited directional movement"
        else:
            regime = 'normal'
            confidence = 0.6
            description = "Normal market conditions"

        return MarketRegimeAnalysis(
            regime=regime,
            confidence=confidence,
            volatility=volatility,
            trend_strength=abs(trend_20),
            description=description
        )

    except Exception as e:
        logger.error(f"Error detecting market regime: {e}")
        return MarketRegimeAnalysis(
            regime='normal',
            confidence=0.5,
            volatility=0.02,
            trend_strength=0.0,
            description="Unable to determine market regime"
        )

# AI predictions are handled by dedicated prediction functions
# This page focuses on pure technical analysis

def check_api_status():
    """Check if TradingView API server is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_technical_analysis(symbols, intervals):
    """Fetch technical analysis data from TradingView API"""
    try:
        payload = {
            "pairs": symbols,
            "intervals": intervals
        }
        
        response = requests.post(
            TRADINGVIEW_API_URL,
            json=payload,
            timeout=120  # 2 minutes timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            # The API returns data in 'data' field, not 'result'
            return result.get('data', {})
        else:
            logger.error(f"API request failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"Error fetching technical analysis: {str(e)}")
        return None

def display_enhanced_analysis_results(data, selected_stocks, selected_intervals, enable_market_regime):
    """Display enhanced technical analysis results with AI components"""

    if not data:
        st.warning("📭 No analysis data received")
        return

    st.success("🎯 Enhanced Technical Analysis Complete!")

    # Display results for each stock
    for i, stock in enumerate(selected_stocks):
        egx_symbol = f"EGX-{stock}"
        stock_data = data.get(egx_symbol, [])

        if not stock_data:
            st.warning(f"❌ No data available for {stock}")
            continue

        # Create expandable section for each stock
        with st.expander(f"📊 {stock} - {EGX_STOCKS.get(stock, stock)}", expanded=(i == 0)):
            display_enhanced_stock_analysis(
                stock, stock_data, selected_intervals, enable_market_regime
            )

def display_analysis_results(data, selected_stocks, selected_intervals):
    """Display comprehensive technical analysis results (legacy function)"""

    if not data:
        st.warning("📭 No analysis data received")
        return

    st.success("🎯 Technical Analysis Complete!")

    # Display results for each stock
    for i, stock in enumerate(selected_stocks):
        egx_symbol = f"EGX-{stock}"
        stock_data = data.get(egx_symbol, [])

        if not stock_data:
            st.warning(f"❌ No data available for {stock}")
            continue

        # Create expandable section for each stock
        with st.expander(f"📊 {stock} - {EGX_STOCKS.get(stock, stock)}", expanded=(i == 0)):
            display_stock_analysis(stock, stock_data, selected_intervals)

def display_enhanced_stock_analysis(stock, stock_data, intervals, enable_market_regime):
    """Display enhanced analysis for a single stock with AI components"""

    # Get current price from first timeframe data (API)
    current_price = stock_data[0].get('price', 'N/A') if stock_data else 'N/A'

    # Enhanced price header
    col1, col2 = st.columns([2, 1])

    with col1:
        if isinstance(current_price, (int, float)):
            st.markdown(f"### 💰 Current Price: **{format_egp_price(current_price)}**")
        else:
            st.markdown(f"### 💰 Current Price: **{current_price} EGP**")

    with col2:
        if enable_market_regime and isinstance(current_price, (int, float)):
            # Get stock data for regime analysis
            df = get_stock_data(stock)
            if df is not None:
                regime_analysis = detect_market_regime(df)

                # Display market regime
                regime_color = {
                    'bull': '🟢', 'bear': '🔴', 'volatile': '🟡',
                    'sideways': '⚪', 'normal': '🔵'
                }.get(regime_analysis.regime, '⚪')

                st.metric(
                    "🎭 Market Regime",
                    f"{regime_color} {regime_analysis.regime.title()}",
                    delta=f"{regime_analysis.confidence:.0%} confidence"
                )

    # Note: AI predictions are handled by dedicated prediction functions

    # Technical Analysis Section
    st.markdown("---")
    st.subheader("📊 Professional Technical Analysis")

    # Create tabs for different timeframes + Comprehensive Analysis
    if len(intervals) > 1:
        tab_names = [f"📈 {interval} Analysis" for interval in intervals]
        if TA_COMPREHENSIVE_AVAILABLE:
            tab_names.append("🎯 Comprehensive Analysis")
        
        tabs = st.tabs(tab_names)

        # Display timeframe analysis tabs
        for i, interval in enumerate(intervals):
            with tabs[i]:
                timeframe_data = next((data for data in stock_data
                                     if data.get('oscillators', [{}])[0].get('interval') == interval), None)
                if timeframe_data:
                    display_timeframe_analysis(stock, interval, timeframe_data)
                else:
                    st.warning(f"No data available for {interval} timeframe")
        
        # Add Comprehensive Analysis tab
        if TA_COMPREHENSIVE_AVAILABLE:
            with tabs[-1]:  # Last tab
                display_ta_comprehensive_analysis_tab(stock, current_price, stock_data, intervals)
                
    else:
        # Single timeframe + Comprehensive Analysis
        if TA_COMPREHENSIVE_AVAILABLE:
            tab_names = [f"📈 {intervals[0]} Analysis", "🎯 Comprehensive Analysis"]
            tabs = st.tabs(tab_names)
            
            with tabs[0]:
                interval = intervals[0]
                timeframe_data = stock_data[0] if stock_data else None
                if timeframe_data:
                    display_timeframe_analysis(stock, interval, timeframe_data)
                    
            with tabs[1]:
                display_ta_comprehensive_analysis_tab(stock, current_price, stock_data, intervals)
        else:
            # Only technical analysis
            interval = intervals[0]
            timeframe_data = stock_data[0] if stock_data else None
            if timeframe_data:
                display_timeframe_analysis(stock, interval, timeframe_data)

def display_stock_analysis(stock, stock_data, intervals):
    """Display detailed analysis for a single stock (legacy function)"""

    # Get current price from first timeframe data
    current_price = stock_data[0].get('price', 'N/A') if stock_data else 'N/A'

    # Price header
    if isinstance(current_price, (int, float)):
        st.markdown(f"### 💰 Current Price: **{format_egp_price(current_price)}**")
    else:
        st.markdown(f"### 💰 Current Price: **{current_price} EGP**")

    # Create tabs for different timeframes
    if len(intervals) > 1:
        tabs = st.tabs([f"📈 {interval} Analysis" for interval in intervals])

        for i, interval in enumerate(intervals):
            with tabs[i]:
                timeframe_data = next((data for data in stock_data
                                     if data.get('oscillators', [{}])[0].get('interval') == interval), None)
                if timeframe_data:
                    display_timeframe_analysis(stock, interval, timeframe_data)
                else:
                    st.warning(f"No data available for {interval} timeframe")
    else:
        # Single timeframe
        interval = intervals[0]
        timeframe_data = stock_data[0] if stock_data else None
        if timeframe_data:
            display_timeframe_analysis(stock, interval, timeframe_data)

def display_timeframe_analysis(_stock, _interval, data):
    """Display analysis for a specific timeframe"""

    oscillators = data.get('oscillators', [])
    moving_averages = data.get('moving_averages', [])
    pivots = data.get('pivots', [])  # Changed from pivot_points to pivots

    # Overall signal calculation
    osc_signals = calculate_signal_summary(oscillators)
    ma_signals = calculate_signal_summary(moving_averages)

    # Signal summary cards
    col1, col2, col3 = st.columns(3)

    with col1:
        total_buy = osc_signals['buy'] + ma_signals['buy']
        total_sell = osc_signals['sell'] + ma_signals['sell']
        # total_neutral = osc_signals['neutral'] + ma_signals['neutral']  # Commented out unused variable

        if total_buy > total_sell:
            overall_signal = "🟢 BULLISH"
            signal_color = "green"
        elif total_sell > total_buy:
            overall_signal = "🔴 BEARISH"
            signal_color = "red"
        else:
            overall_signal = "⚪ NEUTRAL"
            signal_color = "gray"

        st.markdown(f"""
        <div style="padding: 1rem; border-radius: 0.5rem; border: 2px solid {signal_color}; text-align: center;">
            <h3 style="margin: 0; color: {signal_color};">{overall_signal}</h3>
            <p style="margin: 0.5rem 0 0 0;">Overall Signal</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.metric(
            "🔄 Oscillators",
            f"{osc_signals['buy']} Buy, {osc_signals['sell']} Sell",
            delta=f"{osc_signals['neutral']} Neutral"
        )

    with col3:
        st.metric(
            "📈 Moving Averages",
            f"{ma_signals['buy']} Buy, {ma_signals['sell']} Sell",
            delta=f"{ma_signals['neutral']} Neutral"
        )

    # Detailed indicators in two columns
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("🔄 Oscillators")
        display_indicators_table(oscillators)

    with col2:
        st.subheader("📈 Moving Averages")
        display_indicators_table(moving_averages)

    # Pivot Points
    if pivots:  # Changed from pivot_points to pivots
        st.subheader("🎯 Pivot Points")
        display_pivot_points(pivots)

def calculate_signal_summary(indicators):
    """Calculate signal summary for indicators"""
    signals = {'buy': 0, 'sell': 0, 'neutral': 0}
    
    for indicator in indicators:
        action = indicator.get('action', '').lower()
        if action == 'buy':
            signals['buy'] += 1
        elif action == 'sell':
            signals['sell'] += 1
        else:
            signals['neutral'] += 1
    
    return signals

def display_indicators_table(indicators):
    """Display indicators in a formatted table"""

    if not indicators:
        st.write("No data available")
        return

    # Create DataFrame for better display
    indicator_data = []
    for indicator in indicators:
        name = indicator.get('name', 'Unknown')
        value = indicator.get('value', 'N/A')
        action = indicator.get('action', 'N/A')

        # Format value
        if isinstance(value, (int, float)) and value != 'N/A':
            if value > 1000:
                formatted_value = f"{value:,.0f}"
            else:
                formatted_value = f"{value:.2f}"
        else:
            formatted_value = str(value)

        # Color code action
        if action.lower() == 'buy':
            action_display = "🟢 Buy"
        elif action.lower() == 'sell':
            action_display = "🔴 Sell"
        else:
            action_display = "⚪ Neutral"

        indicator_data.append({
            'Indicator': name,
            'Value': formatted_value,
            'Signal': action_display
        })

    # Display as DataFrame
    if indicator_data:
        df = pd.DataFrame(indicator_data)
        st.dataframe(df, use_container_width=True, hide_index=True)

def display_pivot_points(pivots):
    """Display pivot points in a formatted table matching the TradingView structure"""

    if not pivots:
        st.write("No pivot point data available")
        return

    # Create the pivot points table data
    pivot_data = []

    for pivot in pivots:
        # Extract values from the PivotDTO structure
        level = pivot.get('pivot', 'Unknown')
        classic = pivot.get('classic')
        fibo = pivot.get('fibo')
        camarilla = pivot.get('camarilla')
        woodie = pivot.get('woodie')
        dm = pivot.get('dm')

        # Format values (handle None values)
        def format_value(val):
            if val is None or val == 0:
                return "-"
            return f"{val:,.0f}" if isinstance(val, (int, float)) else str(val)

        pivot_data.append({
            'Level': level,
            'Classic': format_value(classic),
            'Fibonacci': format_value(fibo),
            'Camarilla': format_value(camarilla),
            'Woodie': format_value(woodie),
            'DM': format_value(dm)
        })

    if pivot_data:
        # Create DataFrame
        df = pd.DataFrame(pivot_data)

        # Display with custom styling to match the screenshot
        st.markdown("""
        <style>
        .pivot-table {
            background-color: #f0f2f6;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .pivot-header {
            color: #e74c3c;
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }
        </style>
        """, unsafe_allow_html=True)

        # Display the table with enhanced styling
        st.markdown('<div class="pivot-table">', unsafe_allow_html=True)
        st.markdown('<div class="pivot-header">🎯 Pivot Points</div>', unsafe_allow_html=True)

        # Display the dataframe
        st.dataframe(
            df,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Level": st.column_config.TextColumn("Level", width="small"),
                "Classic": st.column_config.TextColumn("Classic", width="medium"),
                "Fibonacci": st.column_config.TextColumn("Fibonacci", width="medium"),
                "Camarilla": st.column_config.TextColumn("Camarilla", width="medium"),
                "Woodie": st.column_config.TextColumn("Woodie", width="medium"),
                "DM": st.column_config.TextColumn("DM", width="medium")
            }
        )

        st.markdown('</div>', unsafe_allow_html=True)

        # Add explanation
        st.info("""
        **ℹ️ Understanding Pivot Points**: Technical analysis indicators used to determine potential support and resistance levels:

        - **R3, R2, R1**: Resistance levels (potential selling points)
        - **P**: Main pivot point (key support/resistance level)
        - **S1, S2, S3**: Support levels (potential buying points)

        **Methods**: Classic (traditional), Fibonacci (ratios), Camarilla (intraday), Woodie (closing weight), DM (Demark)
        """)
    else:
        st.write("No pivot point data available")

def run_comprehensive_ta_analysis(stock_data: List[Dict], intervals: List[str], current_price: Optional[float] = None) -> Dict:
    """Run comprehensive Traditional Technical Analysis across all timeframes and indicators"""
    try:
        analysis_results = {
            'oscillator_analysis': {},
            'moving_average_analysis': {},
            'pivot_analysis': {},
            'confluence_score': 0.0,
            'overall_signal': 'NEUTRAL',
            'signal_strength': 0.5,
            'risk_assessment': 'MEDIUM',
            'trading_recommendation': '',
            'support_resistance_levels': [],
            'current_price': current_price,
            'summary': {}
        }
        
        # Collect all indicators from all timeframes
        all_oscillators = []
        all_moving_averages = []
        all_pivots = []
        
        for data in stock_data:
            oscillators = data.get('oscillators', [])
            moving_averages = data.get('moving_averages', [])
            pivots = data.get('pivots', [])
            
            all_oscillators.extend(oscillators)
            all_moving_averages.extend(moving_averages)
            all_pivots.extend(pivots)
        
        # Analyze Oscillators
        osc_analysis = analyze_oscillator_confluence(all_oscillators)
        analysis_results['oscillator_analysis'] = osc_analysis
        
        # Analyze Moving Averages
        ma_analysis = analyze_moving_average_confluence(all_moving_averages)
        analysis_results['moving_average_analysis'] = ma_analysis
        
        # Analyze Pivot Points
        pivot_analysis = analyze_pivot_confluence(all_pivots, current_price)
        analysis_results['pivot_analysis'] = pivot_analysis
        
        # Calculate overall confluence score
        confluence_score = calculate_ta_confluence_score(osc_analysis, ma_analysis, pivot_analysis)
        analysis_results['confluence_score'] = confluence_score
        
        # Generate overall signal and recommendation
        overall_signal, signal_strength = generate_ta_overall_signal(osc_analysis, ma_analysis, pivot_analysis)
        analysis_results['overall_signal'] = overall_signal
        analysis_results['signal_strength'] = signal_strength
        
        # Risk assessment
        risk_level = assess_ta_risk_level(confluence_score, signal_strength)
        analysis_results['risk_assessment'] = risk_level
        
        # Generate trading recommendation
        trading_rec = generate_ta_trading_recommendation(overall_signal, signal_strength, confluence_score)
        analysis_results['trading_recommendation'] = trading_rec
        
        # Generate summary
        summary = generate_ta_comprehensive_summary(analysis_results)
        analysis_results['summary'] = summary
        
        logger.info(f"✅ TA Comprehensive analysis completed")
        return analysis_results
        
    except Exception as e:
        logger.error(f"Error running TA comprehensive analysis: {str(e)}")
        return {'error': f'TA comprehensive analysis failed: {str(e)}'}

def display_ta_comprehensive_analysis_tab(stock: str, current_price: Optional[float], stock_data: List[Dict], intervals: List[str]):
    """Display comprehensive Traditional Technical Analysis tab using historical CSV data"""
    try:
        st.markdown("### 🎯 Advanced Technical Analysis Comprehensive Engine")
        st.markdown("*Professional institutional-grade analysis using 3-year historical data and Traditional Technical Analysis tools*")
        
        with st.spinner("🔄 Running comprehensive TA analysis on historical data..."):
            # Use CSV-based comprehensive analysis instead of API data
            ta_results = run_csv_comprehensive_analysis(stock)
            
            if 'error' in ta_results:
                st.error(f"❌ {ta_results['error']}")
                return
                
            # Display data source info
            st.info(f"""
            📊 **Data Source**: {ta_results.get('data_source', 'Historical CSV')}  
            📅 **Analysis Period**: {ta_results.get('date_range', {}).get('start', 'N/A')} to {ta_results.get('date_range', {}).get('end', 'N/A')}  
            📈 **Data Points**: {ta_results.get('data_points', 0)} trading days
            """)
                
            # Display TA overview metrics
            st.markdown("#### 📊 Technical Analysis Overview")
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                osc_score = ta_results['oscillator_analysis'].get('confluence_score', 0.0)
                # Handle None or invalid values
                if osc_score is None or not isinstance(osc_score, (int, float)):
                    osc_score = 0.0
                st.metric("🔄 Oscillator Confluence", f"{osc_score:.1%}")
                
            with col2:
                ma_score = ta_results['moving_average_analysis'].get('confluence_score', 0.0)
                # Handle None or invalid values
                if ma_score is None or not isinstance(ma_score, (int, float)):
                    ma_score = 0.0
                st.metric("📈 MA Confluence", f"{ma_score:.1%}")
                
            with col3:
                pivot_score = ta_results.get('pivot_analysis', {}).get('confluence_score', 0.0)
                # Handle None or invalid values
                if pivot_score is None or not isinstance(pivot_score, (int, float)):
                    pivot_score = 0.0
                st.metric("🎯 S/R Analysis", f"{pivot_score:.1%}")
                
            with col4:
                overall_confluence = ta_results.get('confluence_score', 0.0)
                # Handle None or invalid values
                if overall_confluence is None or not isinstance(overall_confluence, (int, float)):
                    overall_confluence = 0.0
                st.metric("🏆 Overall Confluence", f"{overall_confluence:.1%}")
            
            # Trading Signal & Risk Assessment
            st.markdown("#### 📈 Trading Signal & Risk Assessment")
            col1, col2 = st.columns(2)
            
            with col1:
                signal = ta_results.get('overall_signal', 'NEUTRAL')
                strength = ta_results.get('signal_strength', 0.5)
                # Handle None or invalid values
                if strength is None or not isinstance(strength, (int, float)):
                    strength = 0.5
                
                signal_color = {
                    'STRONG_BUY': '🟢', 'BUY': '🟢', 'STRONG_SELL': '🔴', 
                    'SELL': '🔴', 'NEUTRAL': '⚪', 'HOLD': '⚪'
                }.get(signal, '⚪')
                
                st.markdown(f"**Signal:** {signal_color} {signal}")
                st.markdown(f"**Strength:** {strength:.1%}")
                
            with col2:
                risk_level = ta_results.get('risk_assessment', 'MEDIUM')
                trading_rec = ta_results.get('trading_recommendation', 'Hold position')
                
                risk_color = {
                    'LOW': '🟢', 'MEDIUM': '🟡', 'HIGH': '🔴'
                }.get(risk_level, '🟡')
                
                st.markdown(f"**Risk Level:** {risk_color} {risk_level}")
                st.markdown(f"**Recommendation:** {trading_rec}")
            
            # Price Information
            current_price_csv = ta_results.get('current_price', 0)
            if current_price_csv:
                formatted_price = format_egp_price(current_price_csv, is_api_data=False)  # CSV data is already in EGP
                st.markdown(f"**Current Price (Latest CSV):** {formatted_price}")
            
            # Historical Trend Analysis
            trend_analysis = ta_results.get('trend_analysis', {})
            if trend_analysis and 'trend_analysis' in trend_analysis:
                st.markdown("#### 📈 Historical Trend Analysis")
                trend_data = trend_analysis['trend_analysis']
                
                trend_cols = st.columns(len(trend_data))
                for i, (period, data) in enumerate(trend_data.items()):
                    with trend_cols[i]:
                        direction = data.get('direction', 'SIDEWAYS')
                        return_pct = data.get('return', 0)
                        
                        direction_color = {
                            'BULLISH': '🟢', 'BEARISH': '🔴', 'SIDEWAYS': '⚪'
                        }.get(direction, '⚪')
                        
                        st.metric(
                            f"{period} Trend",
                            f"{direction_color} {direction}",
                            delta=f"{return_pct:+.1f}%"
                        )
            
            # Volatility and Performance Metrics
            if 'volatility_regime' in trend_analysis or 'performance_metrics' in trend_analysis:
                st.markdown("#### 🎢 Volatility & Performance Analysis")
                col1, col2 = st.columns(2)
                
                with col1:
                    vol_regime = trend_analysis.get('volatility_regime', {})
                    if vol_regime:
                        vol_level = vol_regime.get('regime', 'NORMAL')
                        current_vol = vol_regime.get('current_volatility', 0)
                        
                        vol_color = {
                            'HIGH': '🔴', 'NORMAL': '🟡', 'LOW': '🟢'
                        }.get(vol_level, '🟡')
                        
                        st.markdown(f"**Volatility Regime:** {vol_color} {vol_level}")
                        st.markdown(f"**Current Volatility:** {current_vol:.1f}%")
                
                with col2:
                    perf_metrics = trend_analysis.get('performance_metrics', {})
                    if perf_metrics:
                        sharpe = perf_metrics.get('sharpe_ratio', 0)
                        max_dd = perf_metrics.get('max_drawdown_pct', 0)
                        win_rate = perf_metrics.get('win_rate_pct', 0)
                        
                        st.markdown(f"**Sharpe Ratio:** {sharpe:.2f}")
                        st.markdown(f"**Max Drawdown:** {max_dd:.1f}%")
                        st.markdown(f"**Win Rate:** {win_rate:.1f}%")
            
            # Support & Resistance Levels
            support_resistance = ta_results.get('support_resistance_levels', [])
            if support_resistance:
                st.markdown("#### 🎯 Key Support & Resistance Levels")
                sr_df_data = []
                for level in support_resistance:
                    level_type = level.get('type', 'Unknown').title()
                    level_name = level.get('level', 'Unknown')
                    price = level.get('price', 0)
                    
                    sr_df_data.append({
                        'Level': level_name,
                        'Type': level_type,
                        'Price': f"{price:.2f} EGP",
                        'Distance': f"{((price - current_price_csv) / current_price_csv * 100):+.1f}%" if current_price_csv > 0 else "N/A"
                    })
                
                if sr_df_data:
                    sr_df = pd.DataFrame(sr_df_data)
                    st.dataframe(sr_df, use_container_width=True, hide_index=True)
            
            # Trading Scenario & Action Plan
            st.markdown("---")
            st.markdown("#### 🎯 Trading Scenario & Action Plan")
            scenario_plan = generate_trading_scenario_action_plan(ta_results, current_price_csv)
            display_trading_scenario_action_plan(scenario_plan, current_price_csv)
            
            # Detailed Analysis Sections
            st.markdown("---")
            st.markdown("#### 📋 Comprehensive Analysis Summary")
            summary = ta_results.get('summary', {})
            if summary:
                for key, value in summary.items():
                    clean_key = key.replace('_', ' ').title()
                    st.markdown(f"**{clean_key}:** {value}")
            
            # Integration Note
            st.markdown("---")
            st.success("""
            💡 **Historical Deep Analysis**: This comprehensive engine analyzes 3 years of historical data 
            to provide institutional-grade confluence analysis and trading insights using proven Technical 
            Analysis methodologies. This deeper historical context enables more reliable trend analysis, 
            support/resistance identification, and risk assessment compared to real-time API data alone.
            """)
            
    except Exception as e:
        st.error(f"❌ Error displaying comprehensive TA analysis: {str(e)}")
        logger.error(f"Error in comprehensive TA analysis tab: {e}")

# Helper functions for TA Comprehensive Analysis Engine

def analyze_oscillator_confluence(oscillators: List[Dict]) -> Dict:
    """Analyze confluence across all oscillator indicators"""
    try:
        if not oscillators:
            return {'signals': [], 'confluence_score': 0.0, 'dominant_signal': 'NEUTRAL'}
        
        # Oscillator signal mapping and weights
        oscillator_weights = {
            'RSI': 1.2,
            'Stochastic': 1.1,
            'CCI': 1.0,
            'Williams %R': 1.0,
            'Momentum': 0.9,
            'MACD': 1.3,
            'Ultimate Oscillator': 1.0,
            'ROC': 0.8
        }
        
        signals = []
        total_weight = 0
        weighted_score = 0
        
        for osc in oscillators:
            name = osc.get('name', 'Unknown')
            # The API uses 'action' field
            action = osc.get('action', 'Neutral')
            value = osc.get('value')
            interval = osc.get('interval', '1D')
            
            # Get timeframe multiplier (longer timeframes have higher weight)
            timeframe_multiplier = get_timeframe_weight(interval)
            
            # Get oscillator weight based on indicator name
            # Extract the actual indicator name from the full name
            indicator_key = 'Unknown'
            for key in oscillator_weights.keys():
                if key.lower() in name.lower():
                    indicator_key = key
                    break
            
            weight = oscillator_weights.get(indicator_key, 1.0) * timeframe_multiplier
            
            # Convert action to numeric score - handle the actual API values
            action_upper = str(action).upper()
            if action_upper in ['BUY', 'STRONG_BUY']:
                score = 1.0 if action_upper == 'STRONG_BUY' else 0.7
            elif action_upper in ['SELL', 'STRONG_SELL']:
                score = -1.0 if action_upper == 'STRONG_SELL' else -0.7
            else:
                score = 0.0  # Neutral
            
            weighted_score += score * weight
            total_weight += weight
            
            signals.append({
                'name': name,
                'signal': action,  # Use action instead of signal
                'value': value,
                'interval': interval,
                'weight': weight,
                'score': score
            })
        
        # Calculate overall confluence score
        confluence_score = (weighted_score / total_weight) if total_weight > 0 else 0.0
        
        # Determine dominant signal
        if confluence_score >= 0.3:
            dominant_signal = 'BUY'
        elif confluence_score <= -0.3:
            dominant_signal = 'SELL'
        else:
            dominant_signal = 'NEUTRAL'
        
        return {
            'signals': signals,
            'confluence_score': confluence_score,
            'dominant_signal': dominant_signal,
            'total_indicators': len(signals),
            'bullish_count': len([s for s in signals if s['score'] > 0]),
            'bearish_count': len([s for s in signals if s['score'] < 0]),
            'neutral_count': len([s for s in signals if s['score'] == 0])
        }
        
    except Exception as e:
        logger.error(f"Error analyzing oscillator confluence: {str(e)}")
        return {'signals': [], 'confluence_score': 0.0, 'dominant_signal': 'NEUTRAL'}

def analyze_moving_average_confluence(moving_averages: List[Dict]) -> Dict:
    """Analyze confluence across all moving average indicators"""
    try:
        if not moving_averages:
            return {'signals': [], 'confluence_score': 0.0, 'dominant_signal': 'NEUTRAL'}
        
        # Moving average weights (shorter periods have less weight for confluence)
        ma_weights = {
            'EMA10': 0.8,
            'EMA20': 1.0,
            'EMA30': 1.1,
            'EMA50': 1.3,
            'EMA100': 1.4,
            'EMA200': 1.5,
            'SMA10': 0.7,
            'SMA20': 0.9,
            'SMA30': 1.0,
            'SMA50': 1.2,
            'SMA100': 1.3,
            'SMA200': 1.4,
            'HullMA': 1.1,
            'VWMA': 1.2
        }
        
        signals = []
        total_weight = 0
        weighted_score = 0
        
        for ma in moving_averages:
            name = ma.get('name', 'Unknown')
            # The API uses 'action' field
            action = ma.get('action', 'Neutral')
            value = ma.get('value')
            interval = ma.get('interval', '1D')
            
            # Get timeframe multiplier
            timeframe_multiplier = get_timeframe_weight(interval)
            
            # Get MA weight based on indicator name
            # Extract the actual indicator name from the full name
            indicator_key = 'Unknown'
            for key in ma_weights.keys():
                if key.lower() in name.lower():
                    indicator_key = key
                    break
            
            weight = ma_weights.get(indicator_key, 1.0) * timeframe_multiplier
            
            # Convert action to numeric score - handle the actual API values
            action_upper = str(action).upper()
            if action_upper in ['BUY', 'STRONG_BUY']:
                score = 1.0 if action_upper == 'STRONG_BUY' else 0.7
            elif action_upper in ['SELL', 'STRONG_SELL']:
                score = -1.0 if action_upper == 'STRONG_SELL' else -0.7
            else:
                score = 0.0  # Neutral
            
            weighted_score += score * weight
            total_weight += weight
            
            signals.append({
                'name': name,
                'signal': action,  # Use action instead of signal
                'value': value,
                'interval': interval,
                'weight': weight,
                'score': score
            })
        
        # Calculate overall confluence score
        confluence_score = (weighted_score / total_weight) if total_weight > 0 else 0.0
        
        # Determine dominant signal
        if confluence_score >= 0.3:
            dominant_signal = 'BUY'
        elif confluence_score <= -0.3:
            dominant_signal = 'SELL'
        else:
            dominant_signal = 'NEUTRAL'
        
        return {
            'signals': signals,
            'confluence_score': confluence_score,
            'dominant_signal': dominant_signal,
            'total_indicators': len(signals),
            'bullish_count': len([s for s in signals if s['score'] > 0]),
            'bearish_count': len([s for s in signals if s['score'] < 0]),
            'neutral_count': len([s for s in signals if s['score'] == 0])
        }
        
    except Exception as e:
        logger.error(f"Error analyzing moving average confluence: {str(e)}")
        return {'signals': [], 'confluence_score': 0.0, 'dominant_signal': 'NEUTRAL'}

def analyze_pivot_confluence(pivots: List[Dict], current_price: Optional[float] = None) -> Dict:
    """Analyze pivot points and support/resistance confluence"""
    try:
        if not pivots:
            return {'signals': [], 'confluence_score': 0.0, 'support_levels': [], 'resistance_levels': []}
        
        support_levels = []
        resistance_levels = []
        signals = []
        
        for pivot in pivots:
            interval = pivot.get('interval', '1h')
            timeframe_multiplier = get_timeframe_weight(interval)
            
            # Extract pivot levels - check multiple possible field structures
            pivot_point = pivot.get('pivot_point') or pivot.get('classic')
            s1 = pivot.get('s1')
            s2 = pivot.get('s2') 
            s3 = pivot.get('s3')
            r1 = pivot.get('r1')
            r2 = pivot.get('r2')
            r3 = pivot.get('r3')

            # If standard fields don't exist, try to extract from pivot structure
            if pivot_point is None:
                # Try to find pivot data in different formats
                for key, value in pivot.items():
                    if 'pivot' in key.lower() and isinstance(value, (int, float)):
                        pivot_point = value
                        break
            
            # Add support levels
            for level, name in [(s1, 'S1'), (s2, 'S2'), (s3, 'S3')]:
                if level is not None:
                    support_levels.append({
                        'level': level,
                        'name': name,
                        'interval': interval,
                        'weight': timeframe_multiplier
                    })
            
            # Add resistance levels
            for level, name in [(r1, 'R1'), (r2, 'R2'), (r3, 'R3')]:
                if level is not None:
                    resistance_levels.append({
                        'level': level,
                        'name': name,
                        'interval': interval,
                        'weight': timeframe_multiplier
                    })
            
            # Add pivot point
            if pivot_point is not None:
                signals.append({
                    'name': f'Pivot_{interval}',
                    'level': pivot_point,
                    'interval': interval,
                    'weight': timeframe_multiplier
                })
        
        # Calculate price position relative to pivots if current price available
        confluence_score = 0.0
        if current_price is not None and signals:
            # CSV data is already in EGP, no need to convert
            price_egp = current_price
            above_pivots = sum(1 for sig in signals if price_egp > (sig['level'] if sig['level'] else 0))
            below_pivots = len(signals) - above_pivots
            
            if above_pivots > below_pivots:
                confluence_score = 0.5  # Above most pivots (bullish)
            elif below_pivots > above_pivots:
                confluence_score = -0.5  # Below most pivots (bearish)
            else:
                confluence_score = 0.0  # Neutral
        
        return {
            'signals': signals,
            'confluence_score': confluence_score,
            'support_levels': sorted(support_levels, key=lambda x: x['level'], reverse=True),
            'resistance_levels': sorted(resistance_levels, key=lambda x: x['level']),
            'total_pivots': len(signals),
            'current_price': current_price
        }
        
    except Exception as e:
        logger.error(f"Error analyzing pivot confluence: {str(e)}")
        return {'signals': [], 'confluence_score': 0.0, 'support_levels': [], 'resistance_levels': []}

def get_timeframe_weight(interval: str) -> float:
    """Get weight multiplier based on timeframe (longer timeframes get higher weight)"""
    timeframe_weights = {
        '1m': 0.3,
        '5m': 0.5,
        '15m': 0.7,
        '30m': 0.8,
        '1h': 1.0,
        '4h': 1.3,
        '1d': 1.5,
        '1D': 1.5,  # API uses uppercase D
        '1w': 1.7,
        '1W': 1.7,  # API might use uppercase W
        '1M': 2.0
    }
    return timeframe_weights.get(interval, 1.0)

def calculate_ta_confluence_score(osc_analysis: Dict, ma_analysis: Dict, pivot_analysis: Dict) -> float:
    """Calculate overall confluence score from all TA components"""
    try:
        # Weights for different analysis types
        oscillator_weight = 0.4
        ma_weight = 0.4
        pivot_weight = 0.2
        
        osc_score = osc_analysis.get('confluence_score', 0.0)
        ma_score = ma_analysis.get('confluence_score', 0.0)
        pivot_score = pivot_analysis.get('confluence_score', 0.0)
        
        # Calculate weighted average
        total_score = (osc_score * oscillator_weight + 
                      ma_score * ma_weight + 
                      pivot_score * pivot_weight)
        
        # Normalize to [-1, 1] range
        return max(-1.0, min(1.0, total_score))
        
    except Exception as e:
        logger.error(f"Error calculating confluence score: {e}")
        return 0.0

def generate_ta_overall_signal(osc_analysis: Dict, ma_analysis: Dict, pivot_analysis: Dict) -> tuple:
    """Generate overall signal and strength from TA confluence"""
    try:
        confluence_score = calculate_ta_confluence_score(osc_analysis, ma_analysis, pivot_analysis)
        
        # Determine signal based on confluence score
        if confluence_score >= 0.6:
            signal = 'STRONG_BUY'
            strength = min(1.0, abs(confluence_score))
        elif confluence_score >= 0.3:
            signal = 'BUY'
            strength = abs(confluence_score)
        elif confluence_score <= -0.6:
            signal = 'STRONG_SELL'
            strength = min(1.0, abs(confluence_score))
        elif confluence_score <= -0.3:
            signal = 'SELL'
            strength = abs(confluence_score)
        else:
            signal = 'NEUTRAL'
            strength = 0.5
        
        return signal, strength
        
    except Exception as e:
        logger.error(f"Error generating overall signal: {e}")
        return 'NEUTRAL', 0.5

def assess_ta_risk_level(confluence_score: float, signal_strength: float) -> str:
    """Assess risk level based on confluence and signal strength"""
    try:
        # High confluence + high strength = lower risk
        if abs(confluence_score) >= 0.6 and signal_strength >= 0.7:
            return 'LOW'
        elif abs(confluence_score) >= 0.3 and signal_strength >= 0.5:
            return 'MEDIUM'
        else:
            return 'HIGH'
            
    except Exception as e:
        logger.error(f"Error assessing risk level: {e}")
        return 'HIGH'

def generate_ta_trading_recommendation(signal: str, strength: float, confluence_score: float) -> str:
    """Generate detailed trading recommendation"""
    try:
        if signal == 'STRONG_BUY':
            return f"Strong buy signal with {strength:.1%} confidence. Multiple indicators align bullishly. Consider position sizing accordingly."
        elif signal == 'BUY':
            return f"Buy signal with {strength:.1%} confidence. Most indicators suggest upward momentum. Monitor closely."
        elif signal == 'STRONG_SELL':
            return f"Strong sell signal with {strength:.1%} confidence. Multiple indicators align bearishly. Consider reducing exposure."
        elif signal == 'SELL':
            return f"Sell signal with {strength:.1%} confidence. Most indicators suggest downward pressure. Exercise caution."
        else:
            return "Neutral signal. Mixed indicators suggest sideways movement. Wait for clearer direction."
            
    except Exception as e:
        logger.error(f"Error generating trading recommendation: {e}")
        return "Unable to generate recommendation due to analysis error."

def generate_ta_comprehensive_summary(analysis_results: Dict) -> Dict:
    """Generate comprehensive summary of all TA analysis"""
    try:
        osc_analysis = analysis_results.get('oscillator_analysis', {})
        ma_analysis = analysis_results.get('moving_average_analysis', {})
        pivot_analysis = analysis_results.get('pivot_analysis', {})
        
        summary = {
            'oscillator_summary': f"Analyzed {osc_analysis.get('total_indicators', 0)} oscillators. "
                                f"Bullish: {osc_analysis.get('bullish_count', 0)}, "
                                f"Bearish: {osc_analysis.get('bearish_count', 0)}, "
                                f"Neutral: {osc_analysis.get('neutral_count', 0)}. "
                                f"Dominant signal: {osc_analysis.get('dominant_signal', 'NEUTRAL')}",
            
            'ma_summary': f"Analyzed {ma_analysis.get('total_indicators', 0)} moving averages. "
                         f"Bullish: {ma_analysis.get('bullish_count', 0)}, "
                         f"Bearish: {ma_analysis.get('bearish_count', 0)}, "
                         f"Neutral: {ma_analysis.get('neutral_count', 0)}. "
                         f"Dominant signal: {ma_analysis.get('dominant_signal', 'NEUTRAL')}",
            
            'pivot_summary': f"Analyzed {pivot_analysis.get('total_pivots', 0)} pivot points across timeframes. "
                           f"Support levels: {len(pivot_analysis.get('support_levels', []))}, "
                           f"Resistance levels: {len(pivot_analysis.get('resistance_levels', []))}",
            
            'confluence_summary': f"Overall confluence score: {analysis_results.get('confluence_score', 0):.2f}. "
                                f"Signal: {analysis_results.get('overall_signal', 'NEUTRAL')} "
                                f"with {analysis_results.get('signal_strength', 0.5):.1%} strength. "
                                f"Risk level: {analysis_results.get('risk_assessment', 'MEDIUM')}"
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"Error generating comprehensive summary: {e}")
        return {'error': 'Unable to generate summary'}

# Historical Data Analysis Functions for Comprehensive Analysis
import os
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

def load_historical_stock_data(stock: str) -> Optional[pd.DataFrame]:
    """Load 3-year historical CSV data for comprehensive analysis"""
    try:
        data_path = os.path.join("data", "stocks", f"{stock}.csv")
        if not os.path.exists(data_path):
            logger.error(f"CSV file not found for stock {stock}: {data_path}")
            return None
            
        df = pd.read_csv(data_path)
        
        # Ensure proper column names and data types
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'])
        elif 'date' in df.columns:
            df['Date'] = pd.to_datetime(df['date'])
            
        # Ensure required OHLCV columns exist
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in required_columns:
            if col not in df.columns and col.lower() in df.columns:
                df[col] = df[col.lower()]
                
        # Sort by date and reset index
        df = df.sort_values('Date').reset_index(drop=True)
        
        # Filter to last 3 years of data
        cutoff_date = datetime.now() - timedelta(days=3*365)
        df = df[df['Date'] >= cutoff_date]
        
        logger.info(f"Loaded {len(df)} days of historical data for {stock}")
        return df
        
    except Exception as e:
        logger.error(f"Error loading historical data for {stock}: {e}")
        return None

def calculate_comprehensive_technical_indicators(df: pd.DataFrame) -> Dict:
    """Calculate comprehensive technical indicators from historical CSV data"""
    try:
        if df is None or len(df) < 50:
            return {'error': 'Insufficient historical data for comprehensive analysis'}
            
        # Extract OHLCV data
        high = df['High'].values
        low = df['Low'].values
        close = df['Close'].values
        open_prices = df['Open'].values
        volume = df['Volume'].values
        
        indicators = {
            'oscillators': [],
            'moving_averages': [],
            'trend_indicators': [],
            'volume_indicators': [],
            'volatility_indicators': [],
            'support_resistance': [],
            'current_price': close[-1] if len(close) > 0 else 0
        }
        
        if TALIB_AVAILABLE:
            # Use TA-Lib for precise calculations
            indicators = calculate_talib_indicators(high, low, close, open_prices, volume, indicators)
        else:
            # Use simplified calculations as fallback
            indicators = calculate_simplified_indicators(high, low, close, open_prices, volume, indicators)
        
        return indicators
        
    except Exception as e:
        logger.error(f"Error calculating comprehensive technical indicators: {e}")
        return {'error': f'Failed to calculate indicators: {str(e)}'}

def calculate_talib_indicators(high, low, close, open_prices, volume, indicators):
    """Calculate indicators using TA-Lib library"""
    try:
        # OSCILLATORS
        # RSI (14-period)
        rsi = talib.RSI(close, timeperiod=14)
        if not np.isnan(rsi[-1]):
            rsi_signal = 'BUY' if rsi[-1] < 30 else 'SELL' if rsi[-1] > 70 else 'NEUTRAL'
            indicators['oscillators'].append({
                'name': 'RSI (14)',
                'value': rsi[-1],
                'action': rsi_signal,
                'timeframe': 'Historical',
                'strength': abs(rsi[-1] - 50) / 50  # Distance from neutral
            })
        
        # Stochastic %K and %D
        stoch_k, stoch_d = talib.STOCH(high, low, close, fastk_period=14, slowk_period=3, slowd_period=3)
        if not np.isnan(stoch_k[-1]) and not np.isnan(stoch_d[-1]):
            stoch_signal = 'BUY' if stoch_k[-1] < 20 and stoch_d[-1] < 20 else 'SELL' if stoch_k[-1] > 80 and stoch_d[-1] > 80 else 'NEUTRAL'
            indicators['oscillators'].append({
                'name': 'Stochastic %K',
                'value': stoch_k[-1],
                'action': stoch_signal,
                'timeframe': 'Historical'
            })
        
        # MACD
        macd_line, macd_signal, macd_hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
        if not np.isnan(macd_line[-1]) and not np.isnan(macd_signal[-1]):
            macd_action = 'BUY' if macd_line[-1] > macd_signal[-1] and macd_hist[-1] > 0 else 'SELL' if macd_line[-1] < macd_signal[-1] and macd_hist[-1] < 0 else 'NEUTRAL'
            indicators['oscillators'].append({
                'name': 'MACD',
                'value': macd_line[-1],
                'action': macd_action,
                'timeframe': 'Historical'
            })
        
        # Williams %R
        willr = talib.WILLR(high, low, close, timeperiod=14)
        if not np.isnan(willr[-1]):
            willr_signal = 'BUY' if willr[-1] < -80 else 'SELL' if willr[-1] > -20 else 'NEUTRAL'
            indicators['oscillators'].append({
                'name': 'Williams %R',
                'value': willr[-1],
                'action': willr_signal,
                'timeframe': 'Historical'
            })
        
        # CCI (Commodity Channel Index)
        cci = talib.CCI(high, low, close, timeperiod=14)
        if not np.isnan(cci[-1]):
            cci_signal = 'BUY' if cci[-1] < -100 else 'SELL' if cci[-1] > 100 else 'NEUTRAL'
            indicators['oscillators'].append({
                'name': 'CCI (14)',
                'value': cci[-1],
                'action': cci_signal,
                'timeframe': 'Historical'
            })
        
        # MOVING AVERAGES
        ma_periods = [5, 10, 20, 50, 100, 200]
        for period in ma_periods:
            if len(close) >= period:
                sma = talib.SMA(close, timeperiod=period)
                ema = talib.EMA(close, timeperiod=period)
                
                if not np.isnan(sma[-1]):
                    sma_signal = 'BUY' if close[-1] > sma[-1] else 'SELL'
                    indicators['moving_averages'].append({
                        'name': f'SMA ({period})',
                        'value': sma[-1],
                        'action': sma_signal,
                        'timeframe': 'Historical'
                    })
                
                if not np.isnan(ema[-1]):
                    ema_signal = 'BUY' if close[-1] > ema[-1] else 'SELL'
                    indicators['moving_averages'].append({
                        'name': f'EMA ({period})',
                        'value': ema[-1],
                        'action': ema_signal,
                        'timeframe': 'Historical'
                    })
        
        # TREND INDICATORS
        # ADX (Average Directional Index)
        adx = talib.ADX(high, low, close, timeperiod=14)
        if not np.isnan(adx[-1]):
            adx_strength = 'STRONG' if adx[-1] > 25 else 'WEAK'
            indicators['trend_indicators'].append({
                'name': 'ADX (Trend Strength)',
                'value': adx[-1],
                'strength': adx_strength,
                'timeframe': 'Historical'
            })
        
        # Parabolic SAR
        sar = talib.SAR(high, low, acceleration=0.02, maximum=0.2)
        if not np.isnan(sar[-1]):
            sar_signal = 'BUY' if close[-1] > sar[-1] else 'SELL'
            indicators['trend_indicators'].append({
                'name': 'Parabolic SAR',
                'value': sar[-1],
                'action': sar_signal,
                'timeframe': 'Historical'
            })
        
        # VOLUME INDICATORS
        # On Balance Volume
        obv = talib.OBV(close, volume)
        if not np.isnan(obv[-1]) and len(obv) >= 20:
            obv_trend = 'BULLISH' if obv[-1] > obv[-20] else 'BEARISH'
            indicators['volume_indicators'].append({
                'name': 'On Balance Volume',
                'value': obv[-1],
                'trend': obv_trend,
                'timeframe': 'Historical'
            })
        
        # Volume SMA
        vol_sma = talib.SMA(volume.astype(float), timeperiod=20)
        if not np.isnan(vol_sma[-1]):
            vol_signal = 'HIGH' if volume[-1] > vol_sma[-1] * 1.5 else 'NORMAL'
            indicators['volume_indicators'].append({
                'name': 'Volume vs 20-day Average',
                'value': volume[-1] / vol_sma[-1] if vol_sma[-1] > 0 else 1.0,
                'signal': vol_signal,
                'timeframe': 'Historical'
            })
        
        # VOLATILITY INDICATORS
        # Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
        if not np.isnan(bb_upper[-1]) and not np.isnan(bb_lower[-1]):
            bb_position = (close[-1] - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1]) if bb_upper[-1] != bb_lower[-1] else 0.5
            bb_signal = 'OVERBOUGHT' if bb_position > 0.8 else 'OVERSOLD' if bb_position < 0.2 else 'NORMAL'
            
            indicators['volatility_indicators'].append({
                'name': 'Bollinger Bands Position',
                'value': bb_position,
                'signal': bb_signal,
                'upper': bb_upper[-1],
                'middle': bb_middle[-1],
                'lower': bb_lower[-1],
                'timeframe': 'Historical'
            })
        
        # Average True Range (ATR)
        atr = talib.ATR(high, low, close, timeperiod=14)
        if not np.isnan(atr[-1]):
            atr_pct = (atr[-1] / close[-1]) * 100 if close[-1] > 0 else 0
            volatility_level = 'HIGH' if atr_pct > 3 else 'MEDIUM' if atr_pct > 1.5 else 'LOW'
            indicators['volatility_indicators'].append({
                'name': 'Average True Range (14)',
                'value': atr[-1],
                'percentage': atr_pct,
                'level': volatility_level,
                'timeframe': 'Historical'
            })
        
        # SUPPORT & RESISTANCE LEVELS (common for both TA-Lib and simplified)
        # Calculate pivot points from recent highs/lows
        recent_days = min(50, len(close))
        
        # Simple pivot calculation
        pivot = (high[-1] + low[-1] + close[-1]) / 3
        r1 = 2 * pivot - low[-1]
        s1 = 2 * pivot - high[-1]
        r2 = pivot + (high[-1] - low[-1])
        s2 = pivot - (high[-1] - low[-1])
        
        indicators['support_resistance'] = [
            {'level': 'R2', 'price': r2, 'type': 'resistance'},
            {'level': 'R1', 'price': r1, 'type': 'resistance'},
            {'level': 'Pivot', 'price': pivot, 'type': 'pivot'},
            {'level': 'S1', 'price': s1, 'type': 'support'},
            {'level': 'S2', 'price': s2, 'type': 'support'}
        ]
        
        return indicators
        
    except Exception as e:
        logger.error(f"Error calculating TA-Lib indicators: {e}")
        return indicators

def calculate_simplified_indicators(high, low, close, open_prices, volume, indicators):
    """Calculate simplified indicators when TA-Lib is not available"""
    try:
        # Enhanced RSI calculation using Wilder's smoothing
        def simple_rsi(prices, period=14):
            if len(prices) < period + 1:
                return np.nan
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            # Use Wilder's smoothing (exponential moving average with alpha=1/period)
            alpha = 1.0 / period
            avg_gain = np.mean(gains[:period])  # Initial average
            avg_loss = np.mean(losses[:period])  # Initial average
            
            # Apply Wilder's smoothing for remaining periods
            for i in range(period, len(gains)):
                avg_gain = alpha * gains[i] + (1 - alpha) * avg_gain
                avg_loss = alpha * losses[i] + (1 - alpha) * avg_loss
            
            if avg_loss == 0:
                return 100
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        # Simplified moving averages
        def simple_ma(prices, period):
            if len(prices) < period:
                return np.nan
            return np.mean(prices[-period:])
        
        def simple_ema(prices, period):
            if len(prices) < period:
                return np.nan
            alpha = 2 / (period + 1)
            ema = prices[0]
            for price in prices[1:]:
                ema = alpha * price + (1 - alpha) * ema
            return ema
        
        # RSI with enhanced sensitivity and market-realistic thresholds
        rsi_value = simple_rsi(close, 14)
        if not np.isnan(rsi_value):
            # More nuanced RSI interpretation reflecting real trading thresholds
            if rsi_value < 30:
                rsi_signal = 'STRONG_BUY'
            elif rsi_value < 45:
                rsi_signal = 'BUY'
            elif rsi_value > 70:
                rsi_signal = 'STRONG_SELL'
            elif rsi_value > 55:
                rsi_signal = 'SELL'
            else:
                rsi_signal = 'NEUTRAL'
                
            indicators['oscillators'].append({
                'name': 'RSI (14)',
                'value': rsi_value,
                'action': rsi_signal,
                'timeframe': 'Historical',
                'strength': abs(rsi_value - 50) / 50  # Distance from neutral
            })
        
        # Enhanced Stochastic with more market-realistic sensitivity
        if len(high) >= 14 and len(low) >= 14:
            period = 14
            lowest_low = np.min(low[-period:])
            highest_high = np.max(high[-period:])
            if highest_high != lowest_low:
                stoch_k = ((close[-1] - lowest_low) / (highest_high - lowest_low)) * 100
                
                # Market-realistic Stochastic interpretation
                if stoch_k < 20:
                    stoch_signal = 'STRONG_BUY'
                elif stoch_k < 40:
                    stoch_signal = 'BUY'
                elif stoch_k > 80:
                    stoch_signal = 'STRONG_SELL'
                elif stoch_k > 60:
                    stoch_signal = 'SELL'
                else:
                    stoch_signal = 'NEUTRAL'
                    
                indicators['oscillators'].append({
                    'name': 'Stochastic %K',
                    'value': stoch_k,
                    'action': stoch_signal,
                    'timeframe': 'Historical',
                    'strength': abs(stoch_k - 50) / 50  # Distance from neutral
                })
        
        # Add Williams %R (simplified)
        if len(high) >= 14 and len(low) >= 14:
            period = 14
            highest_high = np.max(high[-period:])
            lowest_low = np.min(low[-period:])
            if highest_high != lowest_low:
                willr = ((highest_high - close[-1]) / (highest_high - lowest_low)) * -100
                
                if willr < -85:
                    willr_signal = 'STRONG_BUY'
                elif willr < -65:
                    willr_signal = 'BUY'
                elif willr > -15:
                    willr_signal = 'STRONG_SELL'
                elif willr > -35:
                    willr_signal = 'SELL'
                else:
                    willr_signal = 'NEUTRAL'
                    
                indicators['oscillators'].append({
                    'name': 'Williams %R (14)',
                    'value': willr,
                    'action': willr_signal,
                    'timeframe': 'Historical',
                    'strength': abs(willr + 50) / 50  # Distance from neutral (-50)
                })
        
        # Add CCI (Commodity Channel Index) simplified
        if len(high) >= 20 and len(low) >= 20:
            period = 20
            typical_prices = (high[-period:] + low[-period:] + close[-period:]) / 3
            sma_tp = np.mean(typical_prices)
            mean_deviation = np.mean(np.abs(typical_prices - sma_tp))
            if mean_deviation != 0:
                cci = (typical_prices[-1] - sma_tp) / (0.015 * mean_deviation)
                
                if cci < -150:
                    cci_signal = 'STRONG_BUY'
                elif cci < -50:
                    cci_signal = 'BUY'
                elif cci > 150:
                    cci_signal = 'STRONG_SELL'
                elif cci > 50:
                    cci_signal = 'SELL'
                else:
                    cci_signal = 'NEUTRAL'
                    
                indicators['oscillators'].append({
                    'name': 'CCI (20)',
                    'value': cci,
                    'action': cci_signal,
                    'timeframe': 'Historical',
                    'strength': min(1.0, abs(cci) / 200)  # Normalize strength
                })
        
        # Add Momentum oscillator
        if len(close) >= 10:
            momentum = (close[-1] / close[-10] - 1) * 100  # 10-period momentum as percentage
            
            if momentum < -5:
                momentum_signal = 'STRONG_SELL'
            elif momentum < -2:
                momentum_signal = 'SELL'
            elif momentum > 5:
                momentum_signal = 'STRONG_BUY'
            elif momentum > 2:
                momentum_signal = 'BUY'
            else:
                momentum_signal = 'NEUTRAL'
                
            indicators['oscillators'].append({
                'name': 'Momentum (10)',
                'value': momentum,
                'action': momentum_signal,
                'timeframe': 'Historical',
                'strength': min(1.0, abs(momentum) / 10)  # Normalize strength
            })
        
        # Moving Averages
        ma_periods = [5, 10, 20, 50, 100, 200]
        for period in ma_periods:
            if len(close) >= period:
                sma = simple_ma(close, period)
                ema = simple_ema(close, period)
                
                if not np.isnan(sma):
                    sma_signal = 'BUY' if close[-1] > sma else 'SELL'
                    indicators['moving_averages'].append({
                        'name': f'SMA ({period})',
                        'value': sma,
                        'action': sma_signal,
                        'timeframe': 'Historical'
                    })
                
                if not np.isnan(ema):
                    ema_signal = 'BUY' if close[-1] > ema else 'SELL'
                    indicators['moving_averages'].append({
                        'name': f'EMA ({period})',
                        'value': ema,
                        'action': ema_signal,
                        'timeframe': 'Historical'
                    })
        
        # Simplified Bollinger Bands
        if len(close) >= 20:
            sma_20 = simple_ma(close, 20)
            std_20 = np.std(close[-20:])
            bb_upper = sma_20 + (2 * std_20)
            bb_lower = sma_20 - (2 * std_20)
            bb_position = (close[-1] - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
            bb_signal = 'OVERBOUGHT' if bb_position > 0.8 else 'OVERSOLD' if bb_position < 0.2 else 'NORMAL'
            
            indicators['volatility_indicators'].append({
                'name': 'Bollinger Bands Position',
                'value': bb_position,
                'signal': bb_signal,
                'upper': bb_upper,
                'middle': sma_20,
                'lower': bb_lower,
                'timeframe': 'Historical'
            })
        
        # Volume analysis
        if len(volume) >= 20:
            vol_sma = simple_ma(volume, 20)
            if vol_sma > 0:
                vol_signal = 'HIGH' if volume[-1] > vol_sma * 1.5 else 'NORMAL'
                indicators['volume_indicators'].append({
                    'name': 'Volume vs 20-day Average',
                    'value': volume[-1] / vol_sma,
                    'signal': vol_signal,
                    'timeframe': 'Historical'
                })
        
        # SUPPORT & RESISTANCE LEVELS (common for both TA-Lib and simplified)
        # Calculate pivot points from recent highs/lows
        recent_days = min(50, len(close))
        
        # Simple pivot calculation
        pivot = (high[-1] + low[-1] + close[-1]) / 3
        r1 = 2 * pivot - low[-1]
        s1 = 2 * pivot - high[-1]
        r2 = pivot + (high[-1] - low[-1])
        s2 = pivot - (high[-1] - low[-1])
        
        indicators['support_resistance'] = [
            {'level': 'R2', 'price': r2, 'type': 'resistance'},
            {'level': 'R1', 'price': r1, 'type': 'resistance'},
            {'level': 'Pivot', 'price': pivot, 'type': 'pivot'},
            {'level': 'S1', 'price': s1, 'type': 'support'},
            {'level': 'S2', 'price': s2, 'type': 'support'}
        ]
        
        return indicators
        
    except Exception as e:
        logger.error(f"Error calculating simplified indicators: {e}")
        return indicators

def analyze_historical_trends(df: pd.DataFrame) -> Dict:
    """Analyze long-term trends and patterns from historical data"""
    try:
        if df is None or len(df) < 100:
            return {'error': 'Insufficient data for trend analysis'}
        
        close = df['Close'].values
        dates = df['Date'].values
        
        analysis = {
            'trend_analysis': {},
            'price_levels': {},
            'volatility_regime': {},
            'seasonal_patterns': {},
            'performance_metrics': {}
        }
        
        # TREND ANALYSIS
        # Calculate various period returns
        periods = [30, 90, 180, 365]  # 1M, 3M, 6M, 1Y
        for period in periods:
            if len(close) >= period:
                period_return = (close[-1] - close[-period]) / close[-period] * 100
                period_label = f"{period}d"
                if period == 30:
                    period_label = "1M"
                elif period == 90:
                    period_label = "3M"
                elif period == 180:
                    period_label = "6M"
                elif period == 365:
                    period_label = "1Y"
                
                analysis['trend_analysis'][period_label] = {
                    'return': period_return,
                    'direction': 'BULLISH' if period_return > 5 else 'BEARISH' if period_return < -5 else 'SIDEWAYS'
                }
        
        # PRICE LEVELS
        # 52-week high/low
        year_high = np.max(close[-252:]) if len(close) >= 252 else np.max(close)
        year_low = np.min(close[-252:]) if len(close) >= 252 else np.min(close)
        current_price = close[-1]
        
        # Distance from highs/lows
        distance_from_high = (current_price - year_high) / year_high * 100
        distance_from_low = (current_price - year_low) / year_low * 100
        
        analysis['price_levels'] = {
            '52_week_high': year_high,
            '52_week_low': year_low,
            'distance_from_high_pct': distance_from_high,
            'distance_from_low_pct': distance_from_low,
            'position_in_range': (current_price - year_low) / (year_high - year_low) if year_high != year_low else 0.5
        }
        
        # VOLATILITY REGIME
        # Calculate rolling volatility
        returns = np.diff(close) / close[:-1]
        recent_vol = np.std(returns[-30:]) * np.sqrt(252) if len(returns) >= 30 else np.std(returns) * np.sqrt(252)
        historical_vol = np.std(returns) * np.sqrt(252)
        
        vol_percentile = np.percentile(np.array([np.std(returns[i:i+30]) for i in range(len(returns)-30)]) * np.sqrt(252), 
                                     [recent_vol]) if len(returns) >= 60 else [50]
        
        analysis['volatility_regime'] = {
            'current_volatility': recent_vol * 100,
            'historical_average': historical_vol * 100,
            'volatility_percentile': vol_percentile[0] if len(vol_percentile) > 0 else 50,
            'regime': 'HIGH' if recent_vol > historical_vol * 1.5 else 'LOW' if recent_vol < historical_vol * 0.7 else 'NORMAL'
        }
        
        # PERFORMANCE METRICS
        # Sharpe ratio (simplified, using 0% risk-free rate)
        if len(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
            max_drawdown = calculate_max_drawdown(close)
            win_rate = np.sum(returns > 0) / len(returns) * 100
            
            analysis['performance_metrics'] = {
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown_pct': max_drawdown,
                'win_rate_pct': win_rate,
                'total_return_pct': (close[-1] - close[0]) / close[0] * 100 if close[0] > 0 else 0
            }
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing historical trends: {e}")
        return {'error': f'Failed to analyze trends: {str(e)}'}

def calculate_max_drawdown(prices: np.ndarray) -> float:
    """Calculate maximum drawdown percentage"""
    try:
        cumulative = np.cumprod(1 + np.diff(prices) / prices[:-1])
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return abs(np.min(drawdown)) * 100
    except:
        return 0.0

def run_csv_comprehensive_analysis(stock: str) -> Dict:
    """Run comprehensive analysis using historical CSV data instead of API data"""
    try:
        # Load historical data
        df = load_historical_stock_data(stock)
        if df is None:
            return {'error': f'Could not load historical data for {stock}'}
        
        # Calculate technical indicators
        technical_indicators = calculate_comprehensive_technical_indicators(df)
        if 'error' in technical_indicators:
            return technical_indicators
        
        # Analyze historical trends
        trend_analysis = analyze_historical_trends(df)
        if 'error' in trend_analysis:
            return trend_analysis
        
        # Generate comprehensive analysis using historical data
        analysis_results = {
            'data_source': 'Historical CSV (3 years)',
            'data_points': len(df),
            'date_range': {
                'start': df['Date'].min().strftime('%Y-%m-%d'),
                'end': df['Date'].max().strftime('%Y-%m-%d')
            },
            'current_price': technical_indicators.get('current_price', 0),
            'technical_indicators': technical_indicators,
            'trend_analysis': trend_analysis,
            'oscillator_analysis': {},
            'moving_average_analysis': {},
            'support_resistance_levels': technical_indicators.get('support_resistance', []),
            'confluence_score': 0.0,
            'overall_signal': 'NEUTRAL',
            'signal_strength': 0.5,
            'risk_assessment': 'MEDIUM',
            'trading_recommendation': '',
            'summary': {}
        }
        
        # Analyze oscillator confluence using CSV-calculated indicators
        oscillators = technical_indicators.get('oscillators', [])
        osc_analysis = analyze_csv_oscillator_confluence(oscillators)
        analysis_results['oscillator_analysis'] = osc_analysis
        
        # Analyze moving average confluence
        moving_averages = technical_indicators.get('moving_averages', [])
        ma_analysis = analyze_csv_moving_average_confluence(moving_averages)
        analysis_results['moving_average_analysis'] = ma_analysis
        
        # Generate pivot analysis from support/resistance levels
        pivot_analysis = analyze_csv_pivot_confluence(technical_indicators.get('support_resistance', []), 
                                                    technical_indicators.get('current_price', 0))
        analysis_results['pivot_analysis'] = pivot_analysis
        
        # Calculate overall confluence score
        confluence_score = calculate_csv_confluence_score(osc_analysis, ma_analysis, pivot_analysis)
        analysis_results['confluence_score'] = confluence_score
        
        # Generate overall signal and recommendation
        overall_signal, signal_strength = generate_csv_overall_signal(osc_analysis, ma_analysis, 
                                                                    trend_analysis, confluence_score)
        analysis_results['overall_signal'] = overall_signal
        analysis_results['signal_strength'] = signal_strength
        
        # Risk assessment
        risk_level = assess_csv_risk_level(confluence_score, signal_strength, trend_analysis)
        analysis_results['risk_assessment'] = risk_level
        
        # Generate trading recommendation
        trading_rec = generate_csv_trading_recommendation(overall_signal, risk_level, confluence_score)
        analysis_results['trading_recommendation'] = trading_rec
        
        # Generate summary
        summary = generate_csv_analysis_summary(analysis_results)
        analysis_results['summary'] = summary
        
        return analysis_results
        
    except Exception as e:
        logger.error(f"Error in CSV comprehensive analysis: {e}")
        return {'error': f'Analysis failed: {str(e)}'}

# Helper functions for CSV-based analysis

def analyze_csv_oscillator_confluence(oscillators: List[Dict]) -> Dict:
    """Analyze oscillator confluence from CSV-calculated indicators"""
    try:
        if not oscillators:
            return {'signals': [], 'confluence_score': 0.0, 'dominant_signal': 'NEUTRAL'}
        
        bullish_signals = len([osc for osc in oscillators if osc.get('action') == 'BUY'])
        bearish_signals = len([osc for osc in oscillators if osc.get('action') == 'SELL'])
        total_signals = len(oscillators)
        
        # Calculate confluence score
        if total_signals > 0:
            confluence_score = (bullish_signals - bearish_signals) / total_signals
        else:
            confluence_score = 0.0
        
        # Determine dominant signal
        if confluence_score >= 0.3:
            dominant_signal = 'BUY'
        elif confluence_score <= -0.3:
            dominant_signal = 'SELL'
        else:
            dominant_signal = 'NEUTRAL'
        
        return {
            'signals': oscillators,
            'confluence_score': abs(confluence_score),  # Return positive value for display
            'dominant_signal': dominant_signal,
            'total_indicators': total_signals,
            'bullish_count': bullish_signals,
            'bearish_count': bearish_signals,
            'neutral_count': total_signals - bullish_signals - bearish_signals
        }
        
    except Exception as e:
        logger.error(f"Error analyzing oscillator confluence: {e}")
        return {'signals': [], 'confluence_score': 0.0, 'dominant_signal': 'NEUTRAL'}

def analyze_csv_moving_average_confluence(moving_averages: List[Dict]) -> Dict:
    """Analyze moving average confluence from CSV-calculated indicators"""
    try:
        if not moving_averages:
            return {'signals': [], 'confluence_score': 0.0, 'dominant_signal': 'NEUTRAL'}
        
        bullish_signals = len([ma for ma in moving_averages if ma.get('action') == 'BUY'])
        bearish_signals = len([ma for ma in moving_averages if ma.get('action') == 'SELL'])
        total_signals = len(moving_averages)
        
        # Calculate confluence score
        if total_signals > 0:
            confluence_score = (bullish_signals - bearish_signals) / total_signals
        else:
            confluence_score = 0.0
        
        # Determine dominant signal
        if confluence_score >= 0.3:
            dominant_signal = 'BUY'
        elif confluence_score <= -0.3:
            dominant_signal = 'SELL'
        else:
            dominant_signal = 'NEUTRAL'
        
        return {
            'signals': moving_averages,
            'confluence_score': abs(confluence_score),  # Return positive value for display
            'dominant_signal': dominant_signal,
            'total_indicators': total_signals,
            'bullish_count': bullish_signals,
            'bearish_count': bearish_signals,
            'neutral_count': total_signals - bullish_signals - bearish_signals
        }
        
    except Exception as e:
        logger.error(f"Error analyzing MA confluence: {e}")
        return {'signals': [], 'confluence_score': 0.0, 'dominant_signal': 'NEUTRAL'}

def analyze_csv_pivot_confluence(support_resistance: List[Dict], current_price: float) -> Dict:
    """Analyze pivot/support-resistance confluence from CSV-calculated levels"""
    try:
        if not support_resistance or current_price <= 0:
            return {'levels': [], 'confluence_score': 0.5, 'position': 'NEUTRAL'}
        
        # Analyze price position relative to support/resistance levels
        resistance_levels = [level for level in support_resistance if level.get('type') == 'resistance']
        support_levels = [level for level in support_resistance if level.get('type') == 'support']
        
        # Check how many resistance/support levels are above/below current price
        resistances_above = len([r for r in resistance_levels if r.get('price', 0) > current_price])
        supports_below = len([s for s in support_levels if s.get('price', 0) < current_price])
        
        total_levels = len(support_resistance)
        if total_levels > 0:
            # Higher score if price is closer to support (bullish) vs resistance (bearish)
            confluence_score = (supports_below + resistances_above) / total_levels
       
        else:
            confluence_score = 0.5
        
        # Determine position
        if confluence_score >= 0.6:
            position = 'BULLISH'  # More supports below than resistances above
        elif confluence_score <= 0.4:
            position = 'BEARISH'  # More resistances above than supports below
        else:
            position = 'NEUTRAL'
        
        return {
            'levels': support_resistance,
            'confluence_score': confluence_score,
            'position': position,
            'current_price': current_price,
            'resistances_above': resistances_above,
            'supports_below': supports_below
        }
        
    except Exception as e:
        logger.error(f"Error analyzing pivot confluence: {e}")
        return {'levels': [], 'confluence_score': 0.5, 'position': 'NEUTRAL'}

def calculate_csv_confluence_score(osc_analysis: Dict, ma_analysis: Dict, pivot_analysis: Dict) -> float:
    """Calculate overall confluence score from CSV-based analysis"""
    try:
        # Get raw confluence scores (can be negative)
        osc_score = osc_analysis.get('raw_confluence', 0) if 'raw_confluence' in osc_analysis else (
            osc_analysis.get('confluence_score', 0) * (1 if osc_analysis.get('dominant_signal') == 'BUY' else -1 if osc_analysis.get('dominant_signal') == 'SELL' else 0)
        )
        
        ma_score = ma_analysis.get('raw_confluence', 0) if 'raw_confluence' in ma_analysis else (
            ma_analysis.get('confluence_score', 0) * (1 if ma_analysis.get('dominant_signal') == 'BUY' else -1 if ma_analysis.get('dominant_signal') == 'SELL' else 0)
        )
        
        pivot_score = pivot_analysis.get('confluence_score', 0.5)
        # Convert pivot score to range [-1, 1]
        pivot_score = (pivot_score - 0.5) * 2
        
        # Weighted average (oscillators and MAs are more important than pivots)
        weights = {'oscillator': 0.4, 'ma': 0.4, 'pivot': 0.2}
        
        overall_score = (
            osc_score * weights['oscillator'] +
            ma_score * weights['ma'] +
            pivot_score * weights['pivot']
        )
        
        # Return absolute value for display, but keep sign information
        return abs(overall_score)
        
    except Exception as e:
        logger.error(f"Error calculating confluence score: {e}")
        return 0.5

def generate_csv_overall_signal(osc_analysis: Dict, ma_analysis: Dict, trend_analysis: Dict, confluence_score: float) -> Tuple[str, float]:
    """Generate overall signal from CSV-based analysis"""
    try:
        # Get individual signals
        osc_signal = osc_analysis.get('dominant_signal', 'NEUTRAL')
        ma_signal = ma_analysis.get('dominant_signal', 'NEUTRAL')
        
        # Get trend information
        trend_info = trend_analysis.get('trend_analysis', {})
        long_term_trend = trend_info.get('3M', {}).get('direction', 'SIDEWAYS')
        
        # Calculate signal strength based on confluence
        signal_strength = min(0.95, max(0.05, confluence_score))
        
        # Determine overall signal
        bullish_signals = sum([1 for signal in [osc_signal, ma_signal] if signal == 'BUY'])
        bearish_signals = sum([1 for signal in [osc_signal, ma_signal] if signal == 'SELL'])
        
        if bullish_signals >= 2:
            overall_signal = 'STRONG_BUY' if signal_strength > 0.7 else 'BUY'
        elif bearish_signals >= 2:
            overall_signal = 'STRONG_SELL' if signal_strength > 0.7 else 'SELL'
        elif bullish_signals > bearish_signals:
            overall_signal = 'BUY'
        elif bearish_signals > bullish_signals:
            overall_signal = 'SELL'
        else:
            overall_signal = 'NEUTRAL'
        
        # Adjust based on long-term trend
        if long_term_trend == 'BEARISH' and overall_signal in ['BUY', 'STRONG_BUY']:
            overall_signal = 'NEUTRAL'  # Caution against trend
        elif long_term_trend == 'BULLISH' and overall_signal in ['SELL', 'STRONG_SELL']:
            overall_signal = 'NEUTRAL'  # Caution against trend
        
        return overall_signal, signal_strength
        
    except Exception as e:
        logger.error(f"Error generating overall signal: {e}")
        return 'NEUTRAL', 0.5

def assess_csv_risk_level(confluence_score: float, signal_strength: float, trend_analysis: Dict) -> str:
    """Assess risk level from CSV-based analysis"""
    try:
        # Get volatility information
        vol_regime = trend_analysis.get('volatility_regime', {})
        current_vol = vol_regime.get('regime', 'NORMAL')
        
        # Base risk assessment
        if confluence_score > 0.7 and signal_strength > 0.7:
            base_risk = 'LOW'
        elif confluence_score < 0.3 or signal_strength < 0.3:
            base_risk = 'HIGH'
        else:
            base_risk = 'MEDIUM'
        
        # Adjust for volatility
        if current_vol == 'HIGH':
            if base_risk == 'LOW':
                return 'MEDIUM'
            else:
                return 'HIGH'
        elif current_vol == 'LOW' and base_risk == 'HIGH':
            return 'MEDIUM'
        
        return base_risk
        
    except Exception as e:
        logger.error(f"Error assessing risk level: {e}")
        return 'MEDIUM'

def generate_csv_trading_recommendation(overall_signal: str, risk_level: str, confluence_score: float) -> str:
    """Generate trading recommendation from CSV-based analysis"""
    try:
        risk_qualifier = {
            'LOW': 'with conservative position sizing',
            'MEDIUM': 'with moderate position sizing',
            'HIGH': 'with small position sizing or avoid'
        }.get(risk_level, 'with appropriate risk management')
        
        if overall_signal == 'STRONG_BUY':
            return f"Strong buy signal - Consider entering long position {risk_qualifier}"
        elif overall_signal == 'BUY':
            return f"Buy signal - Consider entering long position {risk_qualifier}"
        elif overall_signal == 'STRONG_SELL':
            return f"Strong sell signal - Consider exiting/shorting {risk_qualifier}"
        elif overall_signal == 'SELL':
            return f"Sell signal - Consider reducing position {risk_qualifier}"
        else:
            return f"Neutral signal - Hold current position or wait for clearer signals"
            
    except Exception as e:
        logger.error(f"Error generating trading recommendation: {e}")
        return "Hold position and monitor for signal changes"

def generate_csv_analysis_summary(analysis_results: Dict) -> Dict:
    """Generate comprehensive summary from CSV-based analysis"""
    try:
        summary = {}
        
        # Data information
        summary['data_source'] = analysis_results.get('data_source', 'Historical CSV')
        summary['analysis_period'] = f"{analysis_results.get('data_points', 0)} trading days"
        
        # Technical summary
        osc_analysis = analysis_results.get('oscillator_analysis', {})
        ma_analysis = analysis_results.get('moving_average_analysis', {})
        
        summary['oscillator_consensus'] = f"{osc_analysis.get('bullish_count', 0)} bullish, {osc_analysis.get('bearish_count', 0)} bearish signals"
        summary['moving_average_consensus'] = f"{ma_analysis.get('bullish_count', 0)} bullish, {ma_analysis.get('bearish_count', 0)} bearish signals"
        
        # Trend summary
        trend_analysis = analysis_results.get('trend_analysis', {})
        trend_info = trend_analysis.get('trend_analysis', {})
        
        trend_summary = []
        for period, info in trend_info.items():
            direction = info.get('direction', 'SIDEWAYS')
            return_pct = info.get('return', 0)
            trend_summary.append(f"{period}: {direction} ({return_pct:+.1f}%)")
        
        summary['trend_summary'] = "; ".join(trend_summary) if trend_summary else "No trend data available"
        
        # Risk and volatility
        vol_regime = trend_analysis.get('volatility_regime', {})
        summary['volatility_regime'] = vol_regime.get('regime', 'NORMAL')
        summary['current_volatility'] = f"{vol_regime.get('current_volatility', 0):.1f}% annualized"
        
        # Performance metrics
        perf_metrics = trend_analysis.get('performance_metrics', {})
        if perf_metrics:
            summary['sharpe_ratio'] = f"{perf_metrics.get('sharpe_ratio', 0):.2f}"
            summary['max_drawdown'] = f"{perf_metrics.get('max_drawdown_pct', 0):.1f}%"
            summary['win_rate'] = f"{perf_metrics.get('win_rate_pct', 0):.1f}%"
        
        return summary
        
    except Exception as e:
        logger.error(f"Error generating analysis summary: {e}")
        return {'error': 'Could not generate summary'}

def generate_trading_scenario_action_plan(analysis_results: Dict, current_price: float) -> Dict:
    """Generate comprehensive trading scenario and action plan"""
    try:
        signal = analysis_results.get('overall_signal', 'NEUTRAL')
        confluence_score = analysis_results.get('confluence_score', 0.0)
        risk_level = analysis_results.get('risk_assessment', 'MEDIUM')
        
        # Determine scenario type
        if signal in ['BUY', 'STRONG_BUY']:
            scenario_type = 'BULLISH'
            scenario_color = '🟢'
        elif signal in ['SELL', 'STRONG_SELL']:
            scenario_type = 'BEARISH'
            scenario_color = '🔴'
        else:
            scenario_type = 'NEUTRAL'
            scenario_color = '⚪'
        
        # Calculate entry and target prices based on current price and confluence
        risk_reward_ratio = 1.5 if abs(confluence_score) > 0.5 else 1.2
        risk_per_share = current_price * 0.02  # 2% risk per share
        
        if scenario_type == 'BULLISH':
            entry_price = current_price * 0.995  # Slight pullback entry
            stop_loss = current_price * 0.92  # 8% stop loss
            target_1 = current_price * 1.04  # 4% target
            target_2 = current_price * 1.08  # 8% target
            entry_type = "Market/Limit Order"
            confirmation = "Volume increase, bullish reversal"
        elif scenario_type == 'BEARISH':
            entry_price = current_price * 1.005  # Slight bounce entry for short
            stop_loss = current_price * 1.08  # 8% stop loss
            target_1 = current_price * 0.96  # 4% target
            target_2 = current_price * 0.92  # 8% target
            entry_type = "Market/Limit Order"
            confirmation = "Volume increase, bearish breakdown"
        else:
            entry_price = current_price
            stop_loss = current_price * 0.95  # 5% stop loss
            target_1 = current_price * 1.02  # 2% target
            target_2 = current_price * 1.04  # 4% target
            entry_type = "Wait for clearer signal"
            confirmation = "Wait for directional breakout"
        
        # Position sizing calculations
        account_risk = 0.01 if risk_level == 'LOW' else 0.015 if risk_level == 'MEDIUM' else 0.02
        risk_per_share_calc = abs(entry_price - stop_loss)
        
        # Conservative and moderate position sizes
        conservative_shares = int((account_risk * 0.5) / (risk_per_share_calc / current_price)) if risk_per_share_calc > 0 else 100
        moderate_shares = int((account_risk) / (risk_per_share_calc / current_price)) if risk_per_share_calc > 0 else 200
        
        # Risk-reward calculation
        potential_gain_1 = abs(target_1 - entry_price)
        potential_loss = abs(entry_price - stop_loss)
        actual_rr_ratio = potential_gain_1 / potential_loss if potential_loss > 0 else 1.0
        
        # Risk warning
        risk_warning = None
        if actual_rr_ratio < 1.5:
            risk_warning = f"Current risk-reward ratio is below 1.5:1. Consider waiting for better setup with higher reward potential."
        
        return {
            'scenario_type': scenario_type,
            'scenario_color': scenario_color,
            'entry_strategy': {
                'entry_price': entry_price,
                'entry_type': entry_type,
                'confirmation': confirmation
            },
            'profit_targets': {
                'target_1': target_1,
                'target_2': target_2,
                'rr_ratio_1': actual_rr_ratio,
                'rr_ratio_2': (abs(target_2 - entry_price) / potential_loss) if potential_loss > 0 else 1.0,
                'partial_close': "50% at Target 1"
            },
            'risk_management': {
                'stop_loss': stop_loss,
                'risk_per_share': risk_per_share_calc,
                'max_risk': f"{account_risk:.1%} of account"
            },
            'position_sizing': {
                'conservative': {
                    'shares': conservative_shares,
                    'position_value': conservative_shares * current_price,
                    'percentage': '1%'
                },
                'moderate': {
                    'shares': moderate_shares,
                    'position_value': moderate_shares * current_price,
                    'percentage': '2%'
                }
            },
            'risk_warning': risk_warning,
            'professional_tips': [
                "Patience Pays: Wait for high-probability setups with clear confluence",
                "Risk First: Never risk more than 1-2% of your account per trade"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error generating trading scenario: {e}")
        return {'error': 'Failed to generate trading scenario'}

def display_trading_scenario_action_plan(scenario_plan: Dict, current_price: float):
    """Display comprehensive Trading Scenario & Action Plan"""
    try:
        if 'error' in scenario_plan:
            st.error(f"❌ {scenario_plan['error']}")
            return
            
        scenario_type = scenario_plan.get('scenario_type', 'NEUTRAL')
        scenario_color = scenario_plan.get('scenario_color', '⚪')
        
        # Header
        st.markdown(f"### {scenario_color} Trading Scenario & Action Plan")
        
        # Main scenario type
        st.markdown(f"## {scenario_color} **{scenario_type} SCENARIO**")
        
        # Create layout similar to SMC
        col1, col2 = st.columns(2)
        
        with col1:
            # Entry Strategy
            st.markdown("#### 📍 Entry Strategy")
            entry_data = scenario_plan.get('entry_strategy', {})
            
            entry_info = f"""
            • **Entry Price:** {entry_data.get('entry_price', 0):.2f} EGP
            • **Entry Type:** {entry_data.get('entry_type', 'Market Order')}
            • **Confirmation:** {entry_data.get('confirmation', 'Volume increase, directional breakout')}
            """
            st.markdown(entry_info)
            
            # Risk Management
            st.markdown("#### 🛡️ Risk Management")
            risk_data = scenario_plan.get('risk_management', {})
            
            risk_info = f"""
            • **Stop Loss:** {risk_data.get('stop_loss', 0):.2f} EGP
            • **Risk per Share:** {risk_data.get('risk_per_share', 0):.2f} EGP
            • **Max Risk:** {risk_data.get('max_risk', '2.0% of account')}
            """
            st.markdown(risk_info)
        
        with col2:
            # Profit Targets
            st.markdown("#### 🎯 Profit Targets")
            targets = scenario_plan.get('profit_targets', {})
            
            target_info = f"""
            • **Target 1:** {targets.get('target_1', 0):.2f} EGP (R:R = 1:{targets.get('rr_ratio_1', 1.0):.1f})
            • **Target 2:** {targets.get('target_2', 0):.2f} EGP (R:R = 1:{targets.get('rr_ratio_2', 1.0):.1f})
            • **Partial Close:** {targets.get('partial_close', '50% at Target 1')}
            """
            st.markdown(target_info)
            
            # Position Sizing
            st.markdown("#### 📊 Position Sizing")
            position_data = scenario_plan.get('position_sizing', {})
            
            conservative = position_data.get('conservative', {})
            moderate = position_data.get('moderate', {})
            
            position_info = f"""
            • **Conservative ({conservative.get('percentage', '1%')}):** {conservative.get('shares', 0)} shares
            • **Moderate ({moderate.get('percentage', '2%')}):** {moderate.get('shares', 0)} shares
            • **Position Value:** {moderate.get('position_value', 0):.0f} EGP
            """
            st.markdown(position_info)
        
        # Risk Warning
        risk_warning = scenario_plan.get('risk_warning')
        if risk_warning:
            st.warning(f"⚠️ **Risk Warning**\n\n{risk_warning}")
        
        # Professional Trading Tips
        st.markdown("#### 💡 Professional Trading Tips")
        tips = scenario_plan.get('professional_tips', [])
        for tip in tips:
            st.markdown(f"• **{tip.split(':')[0]}:** {':'.join(tip.split(':')[1:])}")
            
    except Exception as e:
        st.error(f"❌ Error displaying trading scenario: {e}")
        logger.error(f"Error displaying trading scenario: {e}")
