import streamlit as st
import pandas as pd
import os
import logging
from datetime import datetime
from typing import List, Optional

# Configure logging
logger = logging.getLogger(__name__)

def show_model_training():
    """Display model training interface"""
    
    st.title("🧠 Model Training Center")
    st.markdown("### Train AI Models for Better Predictions")
    
    # Check available stock data
    stock_files = []
    data_dir = "data/stocks"
    if os.path.exists(data_dir):
        stock_files = [f.replace('.csv', '') for f in os.listdir(data_dir) if f.endswith('.csv')]
    
    if not stock_files:
        st.error("❌ No stock data files found in data/stocks/ directory")
        st.info("💡 Please upload stock data first using the Data Upload page")
        return
    
    st.success(f"✅ Found {len(stock_files)} stock datasets: {', '.join(stock_files)}")
    
    # Training Configuration
    st.subheader("⚙️ Training Configuration")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Stock selection
        selected_stocks = st.multiselect(
            "📈 Select Stocks to Train:",
            options=stock_files,
            default=stock_files[:3] if len(stock_files) >= 3 else stock_files,
            help="Select which stocks to train models for"
        )
        
        # Model types
        model_types = st.multiselect(
            "🤖 Select Model Types:",
            options=['rf', 'gb', 'lstm', 'svr', 'lr'],
            default=['rf', 'gb'],
            help="rf=RandomForest, gb=GradientBoosting, lstm=LSTM, svr=SupportVector, lr=LinearRegression"
        )
    
    with col2:
        # Time horizons (minutes)
        minute_horizons = st.multiselect(
            "⏰ Prediction Horizons (minutes):",
            options=[5, 15, 30, 60],
            default=[5, 15, 30],
            help="How many minutes ahead to predict"
        )
        
        # Training parameters
        epochs = st.slider("Epochs (for LSTM)", 10, 200, 50, 10)
        batch_size = st.selectbox("Batch Size", [16, 32, 64], index=1)
    
    # Advanced options
    with st.expander("🔧 Advanced Options"):
        sequence_length = st.slider("Sequence Length", 10, 100, 60, 10, 
                                   help="Number of historical days to look back")
        validation_split = st.slider("Validation Split", 0.1, 0.3, 0.2, 0.05,
                                    help="Percentage of data for validation")
        save_models = st.checkbox("💾 Save Trained Models", value=True,
                                 help="Save models to disk for future use")
    
    # Training summary
    if selected_stocks and model_types and minute_horizons:
        total_models = len(selected_stocks) * len(model_types) * len(minute_horizons)
        st.info(f"📊 **Training Summary:** {total_models} models will be trained")
        
        # Estimate training time
        time_per_model = {"rf": 2, "gb": 3, "lstm": 10, "svr": 5, "lr": 1}
        estimated_time = sum(time_per_model.get(mt, 5) for mt in model_types) * len(selected_stocks) * len(minute_horizons)
        st.info(f"⏱️ **Estimated Time:** ~{estimated_time} minutes")
    
    # Training buttons
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🚀 Start Training", type="primary", disabled=not (selected_stocks and model_types and minute_horizons)):
            train_models(selected_stocks, model_types, minute_horizons, epochs, batch_size, 
                        sequence_length, validation_split, save_models)
    
    with col2:
        if st.button("🔄 Quick Train (RF only)", disabled=not selected_stocks):
            # Quick training with RandomForest only
            train_models(selected_stocks, ['rf'], [1, 7], 50, 32, 60, 0.2, True)
    
    with col3:
        if st.button("📊 Check Model Status"):
            check_model_status(stock_files)

def train_models(stocks: List[str], model_types: List[str], horizons: List[int], 
                epochs: int, batch_size: int, sequence_length: int, 
                validation_split: float, save_models: bool):
    """Train models with the specified configuration"""
    
    try:
        from models.train import train_from_csv
        
        # Create progress tracking
        total_models = len(stocks) * len(model_types) * len(horizons)
        progress_bar = st.progress(0)
        status_text = st.empty()
        results_container = st.container()
        
        completed = 0
        results = []
        
        for stock in stocks:
            for model_type in model_types:
                status_text.text(f"Training {model_type.upper()} model for {stock}...")
                
                try:
                    # Train model
                    csv_path = f"data/stocks/{stock}.csv"
                    
                    if not os.path.exists(csv_path):
                        st.error(f"❌ Data file not found: {csv_path}")
                        continue
                    
                    # Train for all horizons
                    trained_models = train_from_csv(
                        csv_path=csv_path,
                        symbol=stock,
                        horizons=horizons,
                        sequence_length=sequence_length,
                        model_type=model_type,
                        epochs=epochs,
                        batch_size=batch_size
                    )
                    
                    results.append({
                        'stock': stock,
                        'model_type': model_type,
                        'horizons': horizons,
                        'status': '✅ Success',
                        'models_trained': len(trained_models) if trained_models else 0
                    })
                    
                except Exception as e:
                    logger.error(f"Error training {model_type} for {stock}: {str(e)}")
                    results.append({
                        'stock': stock,
                        'model_type': model_type,
                        'horizons': horizons,
                        'status': f'❌ Error: {str(e)[:50]}...',
                        'models_trained': 0
                    })
                
                completed += len(horizons)
                progress_bar.progress(completed / total_models)
        
        # Display results
        status_text.text("✅ Training completed!")
        
        with results_container:
            st.subheader("📊 Training Results")
            results_df = pd.DataFrame(results)
            st.dataframe(results_df, use_container_width=True)
            
            # Summary
            successful = len([r for r in results if '✅' in r['status']])
            failed = len(results) - successful
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("✅ Successful", successful)
            with col2:
                st.metric("❌ Failed", failed)
            with col3:
                total_models_trained = sum(r['models_trained'] for r in results)
                st.metric("🤖 Total Models", total_models_trained)
        
        if successful > 0:
            st.success(f"🎉 Training completed! {successful} model types trained successfully.")
            st.info("💡 You can now use the trained models in Buy/Sell Recommendations for better predictions!")
        
    except Exception as e:
        st.error(f"❌ Training failed: {str(e)}")
        logger.error(f"Training error: {str(e)}")

def check_model_status(stock_files: List[str]):
    """Check the status of existing models"""
    
    st.subheader("📊 Model Status")
    
    model_dir = "saved_models"
    if not os.path.exists(model_dir):
        st.warning("⚠️ No saved models directory found")
        return
    
    # Check for model files
    model_files = []
    for root, dirs, files in os.walk(model_dir):
        for file in files:
            if file.endswith(('.pkl', '.h5', '.joblib')):
                model_files.append(os.path.join(root, file))
    
    if not model_files:
        st.warning("⚠️ No trained models found")
        st.info("💡 Click 'Start Training' to train your first models!")
        return
    
    # Parse model information
    model_info = []
    for model_file in model_files:
        try:
            # Extract info from filename/path
            rel_path = os.path.relpath(model_file, model_dir)
            parts = rel_path.replace('\\', '/').split('/')
            
            if len(parts) >= 3:
                stock = parts[0]
                model_type = parts[1]
                filename = parts[2]
                
                # Extract horizon from filename
                horizon = "Unknown"
                if '_' in filename:
                    try:
                        horizon_part = filename.split('_')[-1].split('.')[0]
                        if horizon_part.isdigit():
                            horizon = f"{horizon_part} days"
                    except:
                        pass
                
                # Get file info
                file_size = os.path.getsize(model_file)
                file_size_mb = file_size / (1024 * 1024)
                mod_time = datetime.fromtimestamp(os.path.getmtime(model_file))
                
                model_info.append({
                    'Stock': stock,
                    'Model Type': model_type.upper(),
                    'Horizon': horizon,
                    'Size (MB)': f"{file_size_mb:.2f}",
                    'Last Modified': mod_time.strftime('%Y-%m-%d %H:%M'),
                    'File': os.path.basename(model_file)
                })
        except Exception as e:
            logger.warning(f"Error parsing model file {model_file}: {str(e)}")
    
    if model_info:
        st.success(f"✅ Found {len(model_info)} trained models")
        df = pd.DataFrame(model_info)
        st.dataframe(df, use_container_width=True)
        
        # Summary by stock
        if len(model_info) > 0:
            st.subheader("📈 Models by Stock")
            summary = df.groupby('Stock').size().reset_index(name='Model Count')
            st.dataframe(summary, use_container_width=True)
    else:
        st.warning("⚠️ No valid model files found")
