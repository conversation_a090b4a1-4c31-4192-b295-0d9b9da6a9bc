import logging
import pandas as pd
import numpy as np
import os
import joblib
from typing import <PERSON><PERSON>, Dict, Any
from datetime import datetime, timedelta
from app.models.base_model import BaseModel

logger = logging.getLogger(__name__)

class ProphetModel(BaseModel):
    """
    Prophet model for time series forecasting
    """

    def __init__(self, target_column='Close', **kwargs):
        """
        Initialize the Prophet model

        Args:
            target_column (str): The column to predict
            **kwargs: Additional model parameters
        """
        super().__init__(target_column=target_column)
        self.model = None
        self.last_date = None
        self.frequency = 'min'  # Default to minutes
        self.prediction_horizon = kwargs.get('prediction_horizon', 1)
        self.feature_scaler = None
        self.target_scaler = None

    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Preprocess the data for the Prophet model.

        Args:
            data (pd.DataFrame): The input data

        Returns:
            Tuple[pd.<PERSON>Frame, pd.DataFrame]: Processed features and target
        """
        # Create a copy to avoid modifying the original
        prophet_df = data.copy()

        # Rename columns for Prophet
        prophet_df['ds'] = prophet_df['Date']
        prophet_df['y'] = prophet_df[self.target_column]

        # Keep only necessary columns
        keep_cols = ['ds', 'y']

        # Add regressors if available
        for col in ['Open', 'High', 'Low', 'Volume']:
            if col in prophet_df.columns:
                keep_cols.append(col)

        prophet_df = prophet_df[keep_cols]

        # For compatibility with the interface, return features and target
        # For Prophet, we'll return the whole dataframe as features and the target column
        return prophet_df, prophet_df[['y']]

    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train the Prophet model.

        Args:
            data (pd.DataFrame): The training data
            **kwargs: Additional training parameters

        Returns:
            Dict[str, Any]: Training metrics
        """
        # Preprocess data
        prophet_df, _ = self.preprocess_data(data)

        # Train the model
        return self._fit_prophet_model(prophet_df)

    def _fit_prophet_model(self, df):
        """
        Internal method to fit the Prophet model

        Args:
            df (pd.DataFrame): DataFrame with 'ds' and 'y' columns

        Returns:
            Dict: Training metrics
        """
        try:
            from prophet import Prophet
        except ImportError:
            logger.error("Prophet is not installed. Please install it with 'pip install prophet'")
            raise ImportError("Prophet is not installed. Please install it with 'pip install prophet'")

        # Store the last date for future predictions
        self.last_date = df['ds'].max()

        # Determine frequency
        if len(df) > 1:
            time_diff = (df['ds'].iloc[1] - df['ds'].iloc[0]).total_seconds()
            if time_diff < 60*60:  # Less than an hour
                self.frequency = 'min'
            elif time_diff < 24*60*60:  # Less than a day
                self.frequency = 'H'
            else:
                self.frequency = 'D'

        logger.info(f"Training Prophet model with {len(df)} data points, frequency: {self.frequency}")

        # Initialize and fit the model
        self.model = Prophet(
            daily_seasonality=True,
            yearly_seasonality=True,
            weekly_seasonality=True,
            changepoint_prior_scale=0.05,
            interval_width=0.95
        )

        # Add additional regressors if available
        for col in ['Open', 'High', 'Low', 'Volume']:
            if col in df.columns:
                self.model.add_regressor(col)

        # Fit the model
        self.model.fit(df)

        return {
            'model': 'Prophet',
            'frequency': self.frequency,
            'data_points': len(df)
        }

    def fit(self, X, y=None, df=None):
        """
        Fit the Prophet model (legacy method for compatibility)

        Args:
            X: Not used for Prophet (included for compatibility)
            y: Not used for Prophet (included for compatibility)
            df (pd.DataFrame): DataFrame with 'ds' (date) and 'y' (target) columns

        Returns:
            self: Fitted model
        """
        try:
            from prophet import Prophet
        except ImportError:
            logger.error("Prophet is not installed. Please install it with 'pip install prophet'")
            raise ImportError("Prophet is not installed. Please install it with 'pip install prophet'")

        if df is None:
            raise ValueError("DataFrame must be provided for Prophet model")

        # Ensure df has the required columns
        if 'ds' not in df.columns or 'y' not in df.columns:
            raise ValueError("DataFrame must have 'ds' and 'y' columns")

        # Store the last date for future predictions
        self.last_date = df['ds'].max()

        # Determine frequency
        if len(df) > 1:
            time_diff = (df['ds'].iloc[1] - df['ds'].iloc[0]).total_seconds()
            if time_diff < 60*60:  # Less than an hour
                self.frequency = 'min'
            elif time_diff < 24*60*60:  # Less than a day
                self.frequency = 'H'
            else:
                self.frequency = 'D'

        logger.info(f"Training Prophet model with {len(df)} data points, frequency: {self.frequency}")

        # Initialize and fit the model
        self.model = Prophet(
            daily_seasonality=True,
            yearly_seasonality=True,
            weekly_seasonality=True,
            changepoint_prior_scale=0.05,
            interval_width=0.95
        )

        # Add additional regressors if available
        if 'Open' in df.columns:
            self.model.add_regressor('Open')
        if 'High' in df.columns:
            self.model.add_regressor('High')
        if 'Low' in df.columns:
            self.model.add_regressor('Low')
        if 'Volume' in df.columns:
            self.model.add_regressor('Volume')

        # Fit the model
        self.model.fit(df)

        return self

    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions using the Prophet model.

        Args:
            data (pd.DataFrame): The input data

        Returns:
            np.ndarray: Predicted values
        """
        if self.model is None:
            raise ValueError("Model not trained")

        # Preprocess data if needed
        if 'ds' not in data.columns:
            # This is raw data, preprocess it
            prophet_df, _ = self.preprocess_data(data)
        else:
            # Data is already preprocessed
            prophet_df = data

        # Create future dataframe for prediction
        future = self.model.make_future_dataframe(
            periods=self.prediction_horizon,
            freq=self.frequency
        )

        # Add regressors if they were used during training
        for regressor in self.model.extra_regressors:
            if regressor['name'] not in future.columns:
                # Use the last value from the input data if available
                if regressor['name'] in prophet_df.columns:
                    future[regressor['name']] = prophet_df[regressor['name']].iloc[-1]
                else:
                    # Use the last value from the training data
                    future[regressor['name']] = self.model.history[regressor['name']].iloc[-1]

        # Make predictions
        forecast = self.model.predict(future)

        # Return the predictions for the future periods
        return forecast['yhat'].values[-self.prediction_horizon:]

    def predict_legacy(self, X=None, future_periods=None, df_future=None):
        """
        Legacy method for making predictions with the Prophet model (for compatibility)

        Args:
            X: Not used for Prophet (included for compatibility)
            future_periods (int): Number of periods to predict
            df_future (pd.DataFrame): DataFrame with future dates and regressors

        Returns:
            np.ndarray: Predictions
        """
        if self.model is None:
            raise ValueError("Model not trained")

        # If df_future is provided, use it
        if df_future is not None:
            future = df_future
        else:
            # Otherwise, create a future dataframe
            periods = future_periods or self.prediction_horizon
            future = self.model.make_future_dataframe(
                periods=periods,
                freq=self.frequency
            )

            # Add regressors if they were used during training
            for regressor in self.model.extra_regressors:
                if regressor['name'] not in future.columns:
                    # Use the last value for each regressor
                    future[regressor['name']] = self.model.history[regressor['name']].iloc[-1]

        # Make predictions
        forecast = self.model.predict(future)

        # For compatibility with the rest of the code, return the last prediction
        # If X is a 3D array (as expected by the prediction code), return a compatible format
        if X is not None and isinstance(X, np.ndarray) and len(X.shape) == 3:
            # Return in the format expected by the prediction code
            return np.array([forecast['yhat'].values[-1]])
        else:
            # Return the last prediction value
            return np.array([forecast['yhat'].values[-1]])

    def save(self, path: str) -> None:
        """
        Save the model to disk.

        Args:
            path (str): The path to save the model
        """
        self.save_legacy(path=path)

    def save_legacy(self, path='saved_models', symbol='stock', horizon=None):
        """
        Legacy method to save the Prophet model

        Args:
            path (str): Path to save the model
            symbol (str): Stock symbol
            horizon (int): Prediction horizon
        """
        if self.model is None:
            raise ValueError("Model not trained")

        if not os.path.exists(path):
            os.makedirs(path)

        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Save model components
        model_data = {
            'model': self.model,
            'last_date': self.last_date,
            'frequency': self.frequency,
            'prediction_horizon': self.prediction_horizon
        }

        # Save model
        model_path = os.path.join(path, f"{symbol}_prophet{horizon_str}.joblib")
        joblib.dump(model_data, model_path)
        logger.info(f"Prophet model saved to {model_path}")

    def load(self, path: str) -> None:
        """
        Load the model from disk.

        Args:
            path (str): The path to load the model from
        """
        # For the standard interface, we assume the path is the full path to the model file
        if not os.path.exists(path):
            raise FileNotFoundError(f"Model not found at {path}")

        # Load model components
        model_data = joblib.load(path)

        self.model = model_data['model']
        self.last_date = model_data.get('last_date')
        self.frequency = model_data.get('frequency', 'min')
        self.prediction_horizon = model_data.get('prediction_horizon', 1)

    def load_legacy(self, path='saved_models', symbol='stock', horizon=None):
        """
        Legacy method to load a saved Prophet model

        Args:
            path (str): Path where the model is saved
            symbol (str): Stock symbol
            horizon (int): Prediction horizon

        Returns:
            Prophet model: Loaded model
        """
        # Set up prediction horizon for model name
        horizon_str = f"_{horizon}min" if horizon else ""

        # Load model
        model_path = os.path.join(path, f"{symbol}_prophet{horizon_str}.joblib")

        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model not found at {model_path}")

        # Load model components
        model_data = joblib.load(model_path)

        self.model = model_data['model']
        self.last_date = model_data.get('last_date')
        self.frequency = model_data.get('frequency', 'min')
        self.prediction_horizon = model_data.get('prediction_horizon', 1)

        return self.model

def prepare_prophet_data(df, target_col='Close'):
    """
    Prepare data for Prophet model

    Args:
        df (pd.DataFrame): DataFrame with stock data
        target_col (str): Column to use as target

    Returns:
        pd.DataFrame: DataFrame ready for Prophet
    """
    # Create a copy to avoid modifying the original
    prophet_df = df.copy()

    # Rename columns for Prophet
    prophet_df['ds'] = prophet_df['Date']
    prophet_df['y'] = prophet_df[target_col]

    # Keep only necessary columns
    keep_cols = ['ds', 'y']

    # Add regressors if available
    for col in ['Open', 'High', 'Low', 'Volume']:
        if col in prophet_df.columns:
            keep_cols.append(col)

    return prophet_df[keep_cols]
