"""
Model performance tracking for the AI Stocks Bot application.

This module provides tools for tracking and analyzing model prediction performance
to improve accuracy and enable adaptive model selection.
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class ModelPerformanceTracker:
    """Track and analyze model prediction performance"""

    def __init__(self, performance_dir: str = "data/model_performance"):
        """
        Initialize the model performance tracker.

        Args:
            performance_dir: Directory to store performance data
        """
        self.performance_dir = performance_dir

        # Create directory if it doesn't exist
        os.makedirs(performance_dir, exist_ok=True)

    def _get_performance_path(self, symbol: str, model_type: str) -> str:
        """Get the file path for performance data"""
        return os.path.join(self.performance_dir, f"{symbol}_{model_type}_performance.json")

    def track_prediction(self, symbol: str, model_type: str, horizon: int,
                         predicted_value: float, actual_value: float,
                         prediction_time: datetime, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Track a prediction and its actual outcome.

        Args:
            symbol: Stock symbol
            model_type: Type of model
            horizon: Prediction horizon in minutes
            predicted_value: Predicted price
            actual_value: Actual price
            prediction_time: When the prediction was made
            metadata: Additional metadata

        Returns:
            Performance record
        """
        # Calculate error metrics
        absolute_error = abs(predicted_value - actual_value)
        percentage_error = absolute_error / actual_value * 100 if actual_value != 0 else float('inf')
        direction_correct = (predicted_value > actual_value and actual_value > 0) or \
                           (predicted_value < actual_value and actual_value < 0)

        # Create performance record
        record = {
            "symbol": symbol,
            "model_type": model_type,
            "horizon": horizon,
            "prediction_time": prediction_time.isoformat(),
            "predicted_value": predicted_value,
            "actual_value": actual_value,
            "absolute_error": absolute_error,
            "percentage_error": percentage_error,
            "direction_correct": direction_correct
        }

        if metadata:
            record["metadata"] = metadata

        # Load existing performance data
        performance_path = self._get_performance_path(symbol, model_type)
        performance_data = self._load_performance_data(performance_path)

        # Add new record
        if str(horizon) not in performance_data:
            performance_data[str(horizon)] = []

        performance_data[str(horizon)].append(record)

        # Save updated performance data
        self._save_performance_data(performance_path, performance_data)

        logger.debug(f"Tracked prediction for {symbol} using {model_type} with {horizon}min horizon")

        return record

    def _load_performance_data(self, path: str) -> Dict[str, List[Dict[str, Any]]]:
        """Load performance data from a file"""
        if os.path.exists(path):
            try:
                with open(path, "r") as f:
                    data = json.load(f)

                # Convert integer boolean values back to booleans
                for horizon, records in data.items():
                    for record in records:
                        # Convert direction_correct from int to bool if needed
                        if "direction_correct" in record and isinstance(record["direction_correct"], int):
                            record["direction_correct"] = bool(record["direction_correct"])

                return data
            except Exception as e:
                logger.warning(f"Failed to load performance data from {path}: {str(e)}")

        return {}

    def _save_performance_data(self, path: str, data: Dict[str, List[Dict[str, Any]]]) -> None:
        """Save performance data to a file"""
        try:
            # Create a custom JSON encoder to handle special types
            class CustomJSONEncoder(json.JSONEncoder):
                def default(self, obj):
                    # Handle boolean values
                    if isinstance(obj, bool):
                        return int(obj)
                    # Handle numpy types
                    if hasattr(obj, 'item'):
                        return obj.item()
                    # Handle datetime objects
                    if isinstance(obj, datetime):
                        return obj.isoformat()
                    # Let the base class handle other types
                    return super().default(obj)

            with open(path, "w") as f:
                json.dump(data, f, indent=2, cls=CustomJSONEncoder)
        except Exception as e:
            logger.warning(f"Failed to save performance data to {path}: {str(e)}")

    def get_model_performance(self, symbol: str, model_type: str, horizon: Optional[int] = None) -> Dict[str, Any]:
        """
        Get performance metrics for a model.

        Args:
            symbol: Stock symbol
            model_type: Type of model
            horizon: Prediction horizon in minutes (None for all horizons)

        Returns:
            Performance metrics
        """
        # Load performance data
        performance_path = self._get_performance_path(symbol, model_type)
        performance_data = self._load_performance_data(performance_path)

        if not performance_data:
            return {"error": "No performance data available"}

        # Filter by horizon if specified
        if horizon is not None:
            if str(horizon) not in performance_data:
                return {"error": f"No performance data for horizon {horizon}"}

            horizons = [str(horizon)]
        else:
            horizons = list(performance_data.keys())

        # Calculate metrics for each horizon
        metrics = {}

        for h in horizons:
            records = performance_data[h]

            if not records:
                metrics[h] = {"error": "No records"}
                continue

            # Extract metrics
            absolute_errors = [r["absolute_error"] for r in records]
            percentage_errors = [r["percentage_error"] for r in records if r["percentage_error"] != float('inf')]
            direction_correct = sum(1 for r in records if r["direction_correct"])

            # Calculate statistics
            metrics[h] = {
                "count": len(records),
                "mae": np.mean(absolute_errors),
                "mape": np.mean(percentage_errors) if percentage_errors else None,
                "rmse": np.sqrt(np.mean([e**2 for e in absolute_errors])),
                "direction_accuracy": direction_correct / len(records) if records else 0,
                "min_error": min(absolute_errors),
                "max_error": max(absolute_errors),
                "last_prediction_time": records[-1]["prediction_time"]
            }

        return metrics

    def compare_models(self, symbol: str, model_types: List[str], horizon: int) -> Dict[str, Dict[str, Any]]:
        """
        Compare performance of multiple models.

        Args:
            symbol: Stock symbol
            model_types: List of model types to compare
            horizon: Prediction horizon in minutes

        Returns:
            Comparison of model performance
        """
        comparison = {}

        for model_type in model_types:
            metrics = self.get_model_performance(symbol, model_type, horizon)

            if "error" not in metrics:
                if str(horizon) in metrics:
                    comparison[model_type] = metrics[str(horizon)]
                else:
                    comparison[model_type] = {"error": f"No data for horizon {horizon}"}
            else:
                comparison[model_type] = metrics

        return comparison

    def get_best_model(self, symbol: str, model_types: List[str], horizon: int,
                      metric: str = "mae") -> Tuple[Optional[str], Optional[float]]:
        """
        Get the best performing model for a specific horizon.

        Args:
            symbol: Stock symbol
            model_types: List of model types to compare
            horizon: Prediction horizon in minutes
            metric: Metric to use for comparison (mae, mape, rmse, direction_accuracy)

        Returns:
            Tuple of (best_model_type, metric_value)
        """
        comparison = self.compare_models(symbol, model_types, horizon)

        best_model = None
        best_value = None

        for model_type, metrics in comparison.items():
            if "error" in metrics:
                continue

            if metric not in metrics:
                continue

            value = metrics[metric]

            # Skip None values
            if value is None:
                continue

            # For error metrics, lower is better
            if metric in ["mae", "mape", "rmse"]:
                if best_value is None or value < best_value:
                    best_model = model_type
                    best_value = value
            # For accuracy metrics, higher is better
            else:
                if best_value is None or value > best_value:
                    best_model = model_type
                    best_value = value

        return best_model, best_value

# Create a global model performance tracker
model_performance_tracker = ModelPerformanceTracker()

def track_prediction(symbol: str, model_type: str, horizon: int,
                    predicted_value: float, actual_value: float,
                    prediction_time: Optional[datetime] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Track a prediction and its actual outcome"""
    if prediction_time is None:
        prediction_time = datetime.now()

    return model_performance_tracker.track_prediction(
        symbol, model_type, horizon, predicted_value, actual_value, prediction_time, metadata
    )

def get_model_performance(symbol: str, model_type: str, horizon: Optional[int] = None) -> Dict[str, Any]:
    """Get performance metrics for a model"""
    return model_performance_tracker.get_model_performance(symbol, model_type, horizon)

def compare_models(symbol: str, model_types: List[str], horizon: int) -> Dict[str, Dict[str, Any]]:
    """Compare performance of multiple models"""
    return model_performance_tracker.compare_models(symbol, model_types, horizon)

def get_best_model(symbol: str, model_types: List[str], horizon: int,
                  metric: str = "mae") -> Tuple[Optional[str], Optional[float]]:
    """Get the best performing model for a specific horizon"""
    return model_performance_tracker.get_best_model(symbol, model_types, horizon, metric)

def get_prediction_history(symbol: str, model_type: Optional[str] = None,
                          horizon: Optional[int] = None, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Get prediction history for a symbol.

    Args:
        symbol: Stock symbol
        model_type: Type of model (None for all models)
        horizon: Prediction horizon in minutes (None for all horizons)
        limit: Maximum number of records to return

    Returns:
        List of prediction records
    """
    # Load performance data for all model types
    all_records = []

    # Get all model types if not specified
    if model_type is None:
        model_types = ["rf", "gb", "lstm", "ensemble", "auto"]
    else:
        model_types = [model_type]

    # Load records for each model type
    for mt in model_types:
        performance_path = model_performance_tracker._get_performance_path(symbol, mt)
        performance_data = model_performance_tracker._load_performance_data(performance_path)

        if not performance_data:
            continue

        # Filter by horizon if specified
        if horizon is not None:
            if str(horizon) not in performance_data:
                continue

            horizons = [str(horizon)]
        else:
            horizons = list(performance_data.keys())

        # Collect records from each horizon
        for h in horizons:
            records = performance_data[h]

            # Add model type and horizon to each record
            for record in records:
                record["model_type"] = mt
                record["horizon"] = int(h)

                # Convert datetime string to datetime object for sorting
                if "prediction_time" in record and isinstance(record["prediction_time"], str):
                    try:
                        record["prediction_time"] = datetime.fromisoformat(record["prediction_time"])
                    except ValueError:
                        pass

            all_records.extend(records)

    # Sort by prediction time (most recent first)
    all_records.sort(key=lambda x: x.get("prediction_time", datetime.min), reverse=True)

    # Limit the number of records
    return all_records[:limit]
