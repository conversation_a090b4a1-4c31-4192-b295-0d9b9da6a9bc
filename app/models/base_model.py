import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple

class BaseModel(ABC):
    """
    Abstract base class for all prediction models.
    
    This class defines the interface that all prediction models must implement.
    """
    
    def __init__(self, target_column='Close'):
        """
        Initialize the base model.
        
        Args:
            target_column (str): The column to predict (default: 'Close')
        """
        self.target_column = target_column
        self.model_name = "BaseModel"
    
    @abstractmethod
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Preprocess the data for the model.
        
        Args:
            data (pd.DataFrame): The input data
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Processed features and target
        """
        pass
    
    @abstractmethod
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train the model.
        
        Args:
            data (pd.DataFrame): The training data
            **kwargs: Additional training parameters
            
        Returns:
            Dict[str, Any]: Training metrics
        """
        pass
    
    @abstractmethod
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions using the model.
        
        Args:
            data (pd.DataFrame): The input data
            
        Returns:
            np.ndarray: Predicted values
        """
        pass
    
    @abstractmethod
    def save(self, path: str) -> None:
        """
        Save the model to disk.
        
        Args:
            path (str): The path to save the model
        """
        pass
    
    @abstractmethod
    def load(self, path: str) -> None:
        """
        Load the model from disk.
        
        Args:
            path (str): The path to load the model from
        """
        pass
