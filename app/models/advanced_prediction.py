"""
Advanced Prediction Models for TradingView Predictions

This module provides enhanced prediction capabilities including:
1. Multiple model comparison
2. Confidence intervals and risk assessment
3. Advanced technical indicators
4. Model performance metrics
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import json
import os

# Import prediction functions
from app.models.predict import predict_future_prices, predict_from_live_data

# Try to import optional dependencies
try:
    from app.models.adaptive import select_best_model, get_ensemble_weights
except ImportError:
    # Create dummy functions if not available
    def select_best_model(*args, **kwargs):
        return "ensemble"

    def get_ensemble_weights(*args, **kwargs):
        return {"lstm": 0.4, "rf": 0.3, "gb": 0.3}

# Define a simple performance tracking decorator if not available
try:
    from app.utils.performance import track_performance
except ImportError:
    def track_performance(name):
        def decorator(func):
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            return wrapper
        return decorator

# Try to import data processing utilities
try:
    from app.utils.data_processing import load_scaler
except ImportError:
    def load_scaler(*args, **kwargs):
        return None

# Configure logging
logger = logging.getLogger(__name__)

@track_performance("advanced_prediction")
def generate_multi_model_predictions(
    historical_data: pd.DataFrame,
    live_data: Optional[pd.DataFrame],
    symbol: str,
    horizons: List[int],
    models: List[str] = ["lstm", "bilstm", "rf", "gb", "ensemble"],
    include_confidence: bool = True,
    confidence_level: float = 0.95,
    models_path: str = 'saved_models'
) -> Dict[str, Dict[int, Dict[str, float]]]:
    # Apply NumPy fix to handle BitGenerator issues
    try:
        # Import and apply the NumPy MT19937 fix
        from app.utils.numpy_fix_mt19937 import fix_numpy_mt19937
        fix_numpy_mt19937()
        logger.info("Applied NumPy MT19937 fix before multi-model predictions")
    except Exception as fix_error:
        logger.warning(f"Error applying NumPy fix: {str(fix_error)}")
    """
    Generate predictions using multiple models for comparison.

    Args:
        historical_data: DataFrame with historical stock data
        live_data: DataFrame with live stock data (optional)
        symbol: Stock symbol
        horizons: List of prediction horizons in minutes
        models: List of models to use for predictions
        include_confidence: Whether to include confidence intervals
        confidence_level: Confidence level for intervals (0.95 = 95%)
        models_path: Path where models are saved

    Returns:
        Dictionary with predictions for each model and horizon
        Format: {model_name: {horizon: {'prediction': value, 'lower': value, 'upper': value}}}
    """
    # Dictionary to store all predictions
    all_predictions = {}

    # Generate predictions for each model
    for model_type in models:
        try:
            logger.info(f"Generating predictions with {model_type} model")

            # Convert model type to lowercase for compatibility
            model_type = model_type.lower()

            # Map UI model names to internal model types
            model_map = {
                "lstm": "lstm",
                "bilstm": "bilstm",
                "randomforest": "rf",
                "gradientboosting": "gb",
                "ensemble": "ensemble"
            }

            # Get the internal model type
            internal_model = model_map.get(model_type, model_type)

            # Make predictions
            if live_data is not None and not live_data.empty:
                predictions = predict_from_live_data(
                    live_data=live_data,
                    historical_data=historical_data,
                    symbol=symbol,
                    horizons=horizons,
                    model_type=internal_model,
                    models_path=models_path
                )
            else:
                predictions = predict_future_prices(
                    historical_data,
                    symbol,
                    horizons=horizons,
                    model_type=internal_model,
                    models_path=models_path
                )

            # Add confidence intervals if requested
            if include_confidence:
                predictions_with_confidence = add_confidence_intervals(
                    predictions,
                    historical_data,
                    symbol,
                    model_type,
                    confidence_level
                )
                all_predictions[model_type] = predictions_with_confidence
            else:
                # Format predictions to match the structure
                formatted_predictions = {}
                for horizon, pred in predictions.items():
                    formatted_predictions[horizon] = {'prediction': pred}
                all_predictions[model_type] = formatted_predictions

        except Exception as e:
            logger.error(f"Error generating predictions with {model_type} model: {str(e)}")
            # Continue with other models

    # Add consensus model if we have multiple models
    if len(all_predictions) > 1:
        try:
            consensus_predictions = generate_consensus_predictions(all_predictions, horizons)
            all_predictions['consensus'] = consensus_predictions
        except Exception as e:
            logger.error(f"Error generating consensus predictions: {str(e)}")

    return all_predictions

def add_confidence_intervals(
    predictions: Dict[int, float],
    historical_data: pd.DataFrame,
    symbol: str,
    model_type: str,
    confidence_level: float = 0.95
) -> Dict[int, Dict[str, float]]:
    """
    Add confidence intervals to predictions based on model volatility.

    Args:
        predictions: Dictionary with predictions for each horizon
        historical_data: DataFrame with historical stock data
        symbol: Stock symbol
        model_type: Type of model used for predictions
        confidence_level: Confidence level for intervals (0.95 = 95%)

    Returns:
        Dictionary with predictions and confidence intervals
        Format: {horizon: {'prediction': value, 'lower': value, 'upper': value}}
    """
    # Dictionary to store predictions with confidence intervals
    predictions_with_confidence = {}

    # Get the current price
    current_price = historical_data['Close'].iloc[-1]

    # Calculate historical volatility (standard deviation of returns)
    returns = historical_data['Close'].pct_change().dropna()
    volatility = returns.std()

    # Z-score for the confidence level (e.g., 1.96 for 95%)
    from scipy.stats import norm
    z_score = norm.ppf(0.5 + confidence_level / 2)

    # Add confidence intervals for each horizon
    for horizon, prediction in predictions.items():
        # Scale volatility by the square root of time (in days)
        # Convert minutes to days
        days = horizon / (24 * 60)
        scaled_volatility = volatility * np.sqrt(days)

        # Calculate confidence interval
        interval = prediction * scaled_volatility * z_score

        # Store prediction with confidence interval
        predictions_with_confidence[horizon] = {
            'prediction': prediction,
            'lower': prediction - interval,
            'upper': prediction + interval,
            'confidence': confidence_level
        }

    return predictions_with_confidence

def generate_consensus_predictions(
    all_predictions: Dict[str, Dict[int, Dict[str, float]]],
    horizons: List[int]
) -> Dict[int, Dict[str, float]]:
    """
    Generate consensus predictions by combining multiple models.

    Args:
        all_predictions: Dictionary with predictions for each model and horizon
        horizons: List of prediction horizons

    Returns:
        Dictionary with consensus predictions for each horizon
    """
    # Dictionary to store consensus predictions
    consensus_predictions = {}

    # Generate consensus for each horizon
    for horizon in horizons:
        # Collect predictions for this horizon from all models
        horizon_predictions = []
        for model, predictions in all_predictions.items():
            if horizon in predictions:
                horizon_predictions.append(predictions[horizon]['prediction'])

        # Calculate consensus statistics
        if horizon_predictions:
            mean_prediction = np.mean(horizon_predictions)
            std_prediction = np.std(horizon_predictions)
            min_prediction = np.min(horizon_predictions)
            max_prediction = np.max(horizon_predictions)

            # Store consensus prediction with confidence interval
            consensus_predictions[horizon] = {
                'prediction': mean_prediction,
                'lower': mean_prediction - std_prediction,
                'upper': mean_prediction + std_prediction,
                'min': min_prediction,
                'max': max_prediction,
                'std': std_prediction
            }

    return consensus_predictions

def get_model_performance_metrics(
    symbol: str,
    model_type: str,
    horizons: List[int]
) -> Dict[int, Dict[str, float]]:
    """
    Get performance metrics for a specific model and horizons.

    Args:
        symbol: Stock symbol
        model_type: Type of model
        horizons: List of prediction horizons

    Returns:
        Dictionary with performance metrics for each horizon
    """
    # Dictionary to store performance metrics
    performance_metrics = {}

    # Get performance metrics for each horizon
    for horizon in horizons:
        try:
            from app.models.performance import get_model_performance

            # Get performance data
            performance = get_model_performance(symbol, model_type, horizon)

            if str(horizon) in performance:
                horizon_perf = performance[str(horizon)]

                # Extract relevant metrics
                metrics = {
                    'mae': horizon_perf.get('mae'),
                    'mape': horizon_perf.get('mape'),
                    'rmse': horizon_perf.get('rmse'),
                    'direction_accuracy': horizon_perf.get('direction_accuracy'),
                    'count': horizon_perf.get('count', 0)
                }

                performance_metrics[horizon] = metrics
        except Exception as e:
            logger.error(f"Error getting performance metrics for {model_type} model with {horizon}min horizon: {str(e)}")

    return performance_metrics
