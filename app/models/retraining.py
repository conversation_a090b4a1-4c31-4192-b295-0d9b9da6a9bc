"""
Automated model retraining for the AI Stocks Bot application.

This module provides tools for automatically retraining models
based on performance metrics.
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union

from app.models.performance import get_model_performance, compare_models
from app.utils.performance import track_performance

logger = logging.getLogger(__name__)

class ModelRetrainer:
    """Automatically retrain models based on performance metrics"""
    
    def __init__(self, 
                retraining_threshold: float = 0.1,  # 10% worse than baseline
                min_predictions: int = 30,          # Minimum predictions before retraining
                retraining_interval: timedelta = timedelta(days=7),  # Minimum time between retraining
                models_path: str = 'saved_models'):
        """
        Initialize the model retrainer.
        
        Args:
            retraining_threshold: Threshold for retraining (fraction worse than baseline)
            min_predictions: Minimum number of predictions before retraining
            retraining_interval: Minimum time between retraining
            models_path: Path where models are saved
        """
        self.retraining_threshold = retraining_threshold
        self.min_predictions = min_predictions
        self.retraining_interval = retraining_interval
        self.models_path = models_path
        
        # Dictionary to track last retraining time
        self.last_retraining = {}
    
    def check_retraining_needed(self, symbol: str, model_type: str, 
                               horizon: int, metric: str = "mae") -> Tuple[bool, Optional[float]]:
        """
        Check if a model needs retraining based on performance metrics.
        
        Args:
            symbol: Stock symbol
            model_type: Type of model
            horizon: Prediction horizon in minutes
            metric: Metric to use for evaluation
            
        Returns:
            Tuple of (retraining_needed, performance_degradation)
        """
        # Check if enough time has passed since last retraining
        model_key = f"{symbol}_{model_type}_{horizon}"
        if model_key in self.last_retraining:
            time_since_retraining = datetime.now() - self.last_retraining[model_key]
            if time_since_retraining < self.retraining_interval:
                logger.info(f"Not enough time since last retraining for {model_key}: {time_since_retraining}")
                return False, None
        
        # Get performance metrics
        performance = get_model_performance(symbol, model_type, horizon)
        
        # Check if we have enough predictions
        if str(horizon) in performance:
            horizon_perf = performance[str(horizon)]
            if horizon_perf.get("count", 0) < self.min_predictions:
                logger.info(f"Not enough predictions for {model_key}: {horizon_perf.get('count', 0)}/{self.min_predictions}")
                return False, None
            
            # Check if performance is degrading
            if metric in horizon_perf and horizon_perf[metric] is not None:
                # Get baseline performance (first 10 predictions)
                baseline = self._get_baseline_performance(symbol, model_type, horizon, metric)
                
                if baseline is not None:
                    current = horizon_perf[metric]
                    
                    # For error metrics, higher is worse
                    if metric in ["mae", "mape", "rmse"]:
                        degradation = (current - baseline) / baseline
                        
                        if degradation > self.retraining_threshold:
                            logger.info(f"Performance degradation detected for {model_key}: {degradation:.2%}")
                            return True, degradation
                    # For accuracy metrics, lower is worse
                    else:
                        degradation = (baseline - current) / baseline
                        
                        if degradation > self.retraining_threshold:
                            logger.info(f"Performance degradation detected for {model_key}: {degradation:.2%}")
                            return True, degradation
        
        logger.info(f"No retraining needed for {model_key}")
        return False, None
    
    def _get_baseline_performance(self, symbol: str, model_type: str, 
                                 horizon: int, metric: str) -> Optional[float]:
        """
        Get baseline performance for a model.
        
        Args:
            symbol: Stock symbol
            model_type: Type of model
            horizon: Prediction horizon in minutes
            metric: Metric to use for evaluation
            
        Returns:
            Baseline performance value
        """
        try:
            # Get prediction history
            from app.models.performance import get_prediction_history
            history = get_prediction_history(symbol, model_type, horizon, limit=1000)
            
            # Sort by prediction time (oldest first)
            history.sort(key=lambda x: x.get("prediction_time", datetime.max))
            
            # Get the first 10 predictions (or fewer if not available)
            baseline_count = min(10, len(history))
            if baseline_count == 0:
                return None
            
            baseline_records = history[:baseline_count]
            
            # Calculate baseline metric
            if metric == "mae":
                return np.mean([r.get("absolute_error", 0) for r in baseline_records])
            elif metric == "mape":
                return np.mean([r.get("percentage_error", 0) for r in baseline_records])
            elif metric == "rmse":
                return np.sqrt(np.mean([r.get("absolute_error", 0)**2 for r in baseline_records]))
            elif metric == "direction_accuracy":
                return np.mean([1 if r.get("direction_correct", False) else 0 for r in baseline_records])
            else:
                return None
        except Exception as e:
            logger.error(f"Error getting baseline performance: {str(e)}")
            return None
    
    @track_performance("model_retraining")
    def retrain_model(self, symbol: str, model_type: str, horizon: int) -> bool:
        """
        Retrain a model.
        
        Args:
            symbol: Stock symbol
            model_type: Type of model
            horizon: Prediction horizon in minutes
            
        Returns:
            True if retraining was successful
        """
        try:
            logger.info(f"Retraining model for {symbol} with {model_type} model and {horizon}min horizon")
            
            # Import the training module
            from models.train import train_model
            
            # Load stock data
            from app.utils.state_manager import load_stock_data
            stock_data = load_stock_data(symbol)
            
            if stock_data is None or len(stock_data) == 0:
                logger.error(f"No data found for symbol {symbol}")
                return False
            
            # Retrain the model
            success = train_model(stock_data, symbol, model_type, self.models_path)
            
            if success:
                # Update last retraining time
                model_key = f"{symbol}_{model_type}_{horizon}"
                self.last_retraining[model_key] = datetime.now()
                
                logger.info(f"Successfully retrained model for {symbol} with {model_type} model")
                return True
            else:
                logger.error(f"Failed to retrain model for {symbol} with {model_type} model")
                return False
        except Exception as e:
            logger.error(f"Error retraining model: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def check_and_retrain_all_models(self, symbols: Optional[List[str]] = None,
                                    model_types: Optional[List[str]] = None,
                                    horizons: Optional[List[int]] = None) -> Dict[str, bool]:
        """
        Check and retrain all models that need retraining.
        
        Args:
            symbols: List of stock symbols (None for all symbols)
            model_types: List of model types (None for all model types)
            horizons: List of prediction horizons (None for all horizons)
            
        Returns:
            Dictionary of retraining results
        """
        # Get all symbols if not specified
        if symbols is None:
            from app.utils.state_manager import get_available_stock_files
            symbols = get_available_stock_files()
        
        # Get all model types if not specified
        if model_types is None:
            model_types = ["rf", "gb", "lstm"]  # Don't retrain ensemble
        
        # Get all horizons if not specified
        if horizons is None:
            horizons = [1, 5, 15, 30, 60]
        
        # Dictionary to store results
        results = {}
        
        # Check and retrain each model
        for symbol in symbols:
            for model_type in model_types:
                for horizon in horizons:
                    model_key = f"{symbol}_{model_type}_{horizon}"
                    
                    # Check if retraining is needed
                    retraining_needed, degradation = self.check_retraining_needed(
                        symbol, model_type, horizon
                    )
                    
                    if retraining_needed:
                        # Retrain the model
                        success = self.retrain_model(symbol, model_type, horizon)
                        results[model_key] = success
                    else:
                        results[model_key] = False
        
        return results

# Create a global model retrainer
model_retrainer = ModelRetrainer()

def check_retraining_needed(symbol: str, model_type: str, 
                           horizon: int, metric: str = "mae") -> Tuple[bool, Optional[float]]:
    """Check if a model needs retraining"""
    return model_retrainer.check_retraining_needed(symbol, model_type, horizon, metric)

def retrain_model(symbol: str, model_type: str, horizon: int) -> bool:
    """Retrain a model"""
    return model_retrainer.retrain_model(symbol, model_type, horizon)

def check_and_retrain_all_models(symbols: Optional[List[str]] = None,
                                model_types: Optional[List[str]] = None,
                                horizons: Optional[List[int]] = None) -> Dict[str, bool]:
    """Check and retrain all models that need retraining"""
    return model_retrainer.check_and_retrain_all_models(symbols, model_types, horizons)
