"""
Adaptive model selection for the AI Stocks Bot application.

This module provides tools for adaptively selecting the best model
based on recent performance metrics.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from app.models.performance import get_best_model, get_model_performance

logger = logging.getLogger(__name__)

class AdaptiveModelSelector:
    """Adaptively select the best model based on recent performance"""
    
    def __init__(self, default_model: str = "ensemble", 
                 available_models: List[str] = ["rf", "gb", "lstm", "ensemble"],
                 performance_window: timedelta = timedelta(days=7),
                 min_predictions: int = 10):
        """
        Initialize the adaptive model selector.
        
        Args:
            default_model: Default model to use if no performance data is available
            available_models: List of available model types
            performance_window: Time window for considering performance data
            min_predictions: Minimum number of predictions required for a model to be considered
        """
        self.default_model = default_model
        self.available_models = available_models
        self.performance_window = performance_window
        self.min_predictions = min_predictions
        
        # Cache of best models
        self.best_model_cache = {}
        self.cache_expiry = timedelta(hours=1)
    
    def select_model(self, symbol: str, horizon: int, metric: str = "mae") -> str:
        """
        Select the best model for a specific symbol and horizon.
        
        Args:
            symbol: Stock symbol
            horizon: Prediction horizon in minutes
            metric: Metric to use for selection (mae, mape, rmse, direction_accuracy)
            
        Returns:
            Selected model type
        """
        # Check cache
        cache_key = f"{symbol}_{horizon}_{metric}"
        if cache_key in self.best_model_cache:
            model, timestamp = self.best_model_cache[cache_key]
            if datetime.now() - timestamp < self.cache_expiry:
                logger.debug(f"Using cached best model for {symbol} with {horizon}min horizon: {model}")
                return model
        
        # Get best model based on performance
        best_model, best_value = get_best_model(symbol, self.available_models, horizon, metric)
        
        if best_model is None:
            logger.info(f"No performance data for {symbol} with {horizon}min horizon, using default model: {self.default_model}")
            return self.default_model
        
        # Check if the model has enough predictions
        performance = get_model_performance(symbol, best_model, horizon)
        
        if str(horizon) in performance:
            horizon_perf = performance[str(horizon)]
            if horizon_perf.get("count", 0) < self.min_predictions:
                logger.info(f"Not enough predictions for {symbol} with {best_model} model and {horizon}min horizon, using default model: {self.default_model}")
                return self.default_model
        
        # Cache the result
        self.best_model_cache[cache_key] = (best_model, datetime.now())
        
        logger.info(f"Selected best model for {symbol} with {horizon}min horizon: {best_model} ({metric}={best_value})")
        
        return best_model
    
    def get_model_weights(self, symbol: str, horizon: int, metric: str = "mae") -> Dict[str, float]:
        """
        Get weights for ensemble models based on performance.
        
        Args:
            symbol: Stock symbol
            horizon: Prediction horizon in minutes
            metric: Metric to use for weighting (mae, mape, rmse, direction_accuracy)
            
        Returns:
            Dictionary of model weights
        """
        # Get performance for all models
        model_metrics = {}
        valid_models = []
        
        for model_type in self.available_models:
            if model_type == "ensemble":
                continue
            
            performance = get_model_performance(symbol, model_type, horizon)
            
            if str(horizon) in performance:
                horizon_perf = performance[str(horizon)]
                
                if horizon_perf.get("count", 0) >= self.min_predictions:
                    if metric in horizon_perf and horizon_perf[metric] is not None:
                        model_metrics[model_type] = horizon_perf[metric]
                        valid_models.append(model_type)
        
        # If no valid models, use equal weights
        if not valid_models:
            logger.info(f"No valid models for {symbol} with {horizon}min horizon, using equal weights")
            models = [m for m in self.available_models if m != "ensemble"]
            return {model: 1.0 / len(models) for model in models}
        
        # Calculate weights based on performance
        weights = {}
        
        if metric in ["mae", "mape", "rmse"]:
            # For error metrics, lower is better
            # Use inverse of error as weight
            total_inverse_error = sum(1.0 / model_metrics[model] for model in valid_models)
            
            for model in valid_models:
                weights[model] = (1.0 / model_metrics[model]) / total_inverse_error
        else:
            # For accuracy metrics, higher is better
            total_accuracy = sum(model_metrics[model] for model in valid_models)
            
            for model in valid_models:
                weights[model] = model_metrics[model] / total_accuracy
        
        logger.info(f"Calculated model weights for {symbol} with {horizon}min horizon: {weights}")
        
        return weights

# Create a global adaptive model selector
model_selector = AdaptiveModelSelector()

def select_best_model(symbol: str, horizon: int, metric: str = "mae") -> str:
    """Select the best model for a specific symbol and horizon"""
    return model_selector.select_model(symbol, horizon, metric)

def get_ensemble_weights(symbol: str, horizon: int, metric: str = "mae") -> Dict[str, float]:
    """Get weights for ensemble models based on performance"""
    return model_selector.get_model_weights(symbol, horizon, metric)
