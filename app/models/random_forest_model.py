import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import logging
import joblib
import os
from typing import Dict, Any, Tuple

from app.models.base_model import BaseModel

logger = logging.getLogger(__name__)

class RandomForestModel(BaseModel):
    """
    Random Forest model for time series prediction.
    """
    
    def __init__(self, target_column='Close', **kwargs):
        """
        Initialize the Random Forest model.
        
        Args:
            target_column (str): The column to predict (default: 'Close')
            **kwargs: Additional model parameters
        """
        super().__init__(target_column=target_column)
        self.model_name = "RandomForest"
        
        # Model parameters
        self.n_estimators = kwargs.get('n_estimators', 100)
        self.max_depth = kwargs.get('max_depth', 10)
        self.min_samples_split = kwargs.get('min_samples_split', 2)
        self.min_samples_leaf = kwargs.get('min_samples_leaf', 1)
        
        # Scaler
        self.feature_scaler = StandardScaler()
        
        # Model
        self.model = RandomForestRegressor(
            n_estimators=self.n_estimators,
            max_depth=self.max_depth,
            min_samples_split=self.min_samples_split,
            min_samples_leaf=self.min_samples_leaf,
            random_state=42
        )
        
        # Feature importance
        self.feature_importance = None
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Preprocess the data for the Random Forest model.
        
        Args:
            data (pd.DataFrame): The input data
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Processed features and target
        """
        # Make a copy to avoid modifying the original data
        df = data.copy()
        
        # Extract target
        target = df[[self.target_column]]
        
        # Create features
        # Use all numeric columns except the target as features
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        if self.target_column in numeric_cols:
            numeric_cols.remove(self.target_column)
        
        features = df[numeric_cols]
        
        # Add some basic technical indicators
        if 'Close' in features.columns:
            # Moving averages
            features['MA_5'] = df['Close'].rolling(window=5).mean()
            features['MA_10'] = df['Close'].rolling(window=10).mean()
            
            # Price momentum
            features['Return_1d'] = df['Close'].pct_change(1)
            features['Return_5d'] = df['Close'].pct_change(5)
        
        # Fill NaN values
        features = features.fillna(method='bfill').fillna(method='ffill')
        
        return features, target
    
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train the Random Forest model.
        
        Args:
            data (pd.DataFrame): The training data
            **kwargs: Additional training parameters
            
        Returns:
            Dict[str, Any]: Training metrics
        """
        logger.info("Training Random Forest model")
        
        # Override default parameters if provided
        self.n_estimators = kwargs.get('n_estimators', self.n_estimators)
        self.max_depth = kwargs.get('max_depth', self.max_depth)
        self.min_samples_split = kwargs.get('min_samples_split', self.min_samples_split)
        self.min_samples_leaf = kwargs.get('min_samples_leaf', self.min_samples_leaf)
        
        # Update model with new parameters
        self.model = RandomForestRegressor(
            n_estimators=self.n_estimators,
            max_depth=self.max_depth,
            min_samples_split=self.min_samples_split,
            min_samples_leaf=self.min_samples_leaf,
            random_state=42
        )
        
        # Preprocess data
        features, target = self.preprocess_data(data)
        
        # Scale features
        X = self.feature_scaler.fit_transform(features)
        y = target.values.ravel()
        
        # Train model
        self.model.fit(X, y)
        
        # Store feature importance
        self.feature_importance = pd.DataFrame({
            'Feature': features.columns,
            'Importance': self.model.feature_importances_
        }).sort_values('Importance', ascending=False)
        
        # Return training metrics
        return {
            'feature_importance': self.feature_importance
        }
    
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions using the Random Forest model.
        
        Args:
            data (pd.DataFrame): The input data
            
        Returns:
            np.ndarray: Predicted values
        """
        # Preprocess data
        features, _ = self.preprocess_data(data)
        
        # Scale features
        X = self.feature_scaler.transform(features)
        
        # Generate predictions
        predictions = self.model.predict(X)
        
        return predictions
    
    def save(self, path: str) -> None:
        """
        Save the Random Forest model to disk.
        
        Args:
            path (str): The path to save the model
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save model and scaler
        model_data = {
            'model': self.model,
            'feature_scaler': self.feature_scaler,
            'feature_importance': self.feature_importance,
            'params': {
                'n_estimators': self.n_estimators,
                'max_depth': self.max_depth,
                'min_samples_split': self.min_samples_split,
                'min_samples_leaf': self.min_samples_leaf,
                'target_column': self.target_column
            }
        }
        
        joblib.dump(model_data, path)
        logger.info(f"Random Forest model saved to {path}")
    
    def load(self, path: str) -> None:
        """
        Load the Random Forest model from disk.
        
        Args:
            path (str): The path to load the model from
        """
        # Load model and scaler
        model_data = joblib.load(path)
        
        self.model = model_data['model']
        self.feature_scaler = model_data['feature_scaler']
        self.feature_importance = model_data.get('feature_importance', None)
        
        # Load parameters
        params = model_data.get('params', {})
        self.n_estimators = params.get('n_estimators', self.n_estimators)
        self.max_depth = params.get('max_depth', self.max_depth)
        self.min_samples_split = params.get('min_samples_split', self.min_samples_split)
        self.min_samples_leaf = params.get('min_samples_leaf', self.min_samples_leaf)
        self.target_column = params.get('target_column', self.target_column)
        
        logger.info(f"Random Forest model loaded from {path}")
