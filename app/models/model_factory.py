import logging
import os
import glob
from typing import Dict, Any, Optional, List, Union

from app.models.lstm_model import LSTMModel
from app.models.random_forest_model import RandomForestModel
from app.models.gradient_boosting_model import GradientBoostingModel
from app.models.svr_model import SVRModel
from app.models.linear_regression_model import LinearRegressionModel
from app.models.ensemble_model import EnsembleModel
from app.models.hybrid_model import HybridModel
from app.models.prophet_model import ProphetModel
from app.models.base_model import BaseModel

logger = logging.getLogger(__name__)

class ModelFactory:
    """
    Factory class for creating prediction models.
    """

    @staticmethod
    def get_model(model_type: str, target_column: str = 'Close', **kwargs) -> BaseModel:
        """
        Get a model instance based on the model type.

        Args:
            model_type (str): The type of model to create
            target_column (str): The column to predict
            **kwargs: Additional model parameters

        Returns:
            BaseModel: The model instance

        Raises:
            ValueError: If the model type is not supported
        """
        model_type = model_type.lower()

        if model_type == 'lstm':
            return LSTMModel(target_column=target_column, **kwargs)
        elif model_type == 'randomforest' or model_type == 'rf':
            return RandomForestModel(target_column=target_column, **kwargs)
        elif model_type == 'gradientboosting' or model_type == 'gb':
            return GradientBoostingModel(target_column=target_column, **kwargs)
        elif model_type == 'svr':
            return SVRModel(target_column=target_column, **kwargs)
        elif model_type == 'linearregression' or model_type == 'lr':
            return LinearRegressionModel(target_column=target_column, **kwargs)
        elif model_type == 'ensemble':
            return EnsembleModel(target_column=target_column, **kwargs)
        elif model_type.startswith('ensemble_'):
            # Extract the ensemble method from the model type
            ensemble_method = model_type.replace('ensemble_', '')
            # Create kwargs with the ensemble method
            ensemble_kwargs = {'ensemble_method': ensemble_method}
            # Add any additional kwargs
            ensemble_kwargs.update(kwargs)
            return EnsembleModel(target_column=target_column, **ensemble_kwargs)
        elif model_type == 'hybrid':
            return HybridModel(target_column=target_column, **kwargs)
        elif model_type == 'prophet':
            return ProphetModel(target_column=target_column, **kwargs)
        else:
            logger.error(f"Unsupported model type: {model_type}")
            raise ValueError(f"Unsupported model type: {model_type}")

    @staticmethod
    def get_available_models() -> Dict[str, str]:
        """
        Get a dictionary of available models.

        Returns:
            Dict[str, str]: Dictionary mapping model keys to display names
        """
        return {
            'lstm': 'LSTM Neural Network',
            'rf': 'Random Forest',
            'gb': 'Gradient Boosting',
            'svr': 'Support Vector Regression',
            'lr': 'Linear Regression',
            'prophet': 'Prophet Time Series',
            'ensemble': 'Basic Ensemble Model',
            'hybrid': 'Hybrid Model',
            'ensemble_simple': 'Ensemble (Simple Average)',
            'ensemble_weighted': 'Ensemble (Weighted Average)',
            'ensemble_performance': 'Ensemble (Performance Weighted)',
            'ensemble_time': 'Ensemble (Time Weighted)',
            'ensemble_stacking': 'Ensemble (Stacking)'
        }

    @staticmethod
    def get_model_description(model_type: str) -> str:
        """
        Get a description of the model.

        Args:
            model_type (str): The type of model

        Returns:
            str: Description of the model
        """
        model_type = model_type.lower()

        descriptions = {
            'lstm': 'Long Short-Term Memory neural network for sequence prediction',
            'rf': 'Random Forest regression for robust predictions',
            'gb': 'Gradient Boosting regression for high accuracy',
            'svr': 'Support Vector Regression for handling non-linear relationships',
            'lr': 'Linear Regression for simple trend prediction',
            'prophet': 'Facebook Prophet model for time series forecasting with seasonality',
            'ensemble': 'Basic ensemble model combining multiple models with weighted averaging',
            'hybrid': 'Hybrid model combining traditional ML with deep learning',
            'ensemble_simple': 'Ensemble with equal weights for all models (simple average)',
            'ensemble_weighted': 'Ensemble with weights based on model performance (MSE)',
            'ensemble_performance': 'Ensemble with weights based on multiple performance metrics',
            'ensemble_time': 'Ensemble with more weight to recent performance (adapts to market changes)',
            'ensemble_stacking': 'Advanced ensemble using a meta-model to combine predictions'
        }

        return descriptions.get(model_type, 'No description available')

    @staticmethod
    def get_model_parameters(model_type: str) -> Dict[str, Any]:
        """
        Get default parameters for a model.

        Args:
            model_type (str): The type of model

        Returns:
            Dict[str, Any]: Default parameters for the model
        """
        model_type = model_type.lower()

        if model_type == 'lstm':
            return {
                'sequence_length': 10,
                'units': 50,
                'dropout': 0.2,
                'epochs': 50,
                'batch_size': 32
            }
        elif model_type == 'rf' or model_type == 'randomforest':
            return {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 2,
                'min_samples_leaf': 1
            }
        elif model_type == 'gb' or model_type == 'gradientboosting':
            return {
                'n_estimators': 100,
                'learning_rate': 0.1,
                'max_depth': 3,
                'min_samples_split': 2,
                'min_samples_leaf': 1
            }
        elif model_type == 'svr':
            return {
                'kernel': 'rbf',
                'C': 1.0,
                'epsilon': 0.1,
                'gamma': 'scale'
            }
        elif model_type == 'lr' or model_type == 'linearregression':
            return {
                'fit_intercept': True,
                'normalize': False
            }
        elif model_type == 'ensemble':
            return {
                'selected_models': ['LSTM', 'RandomForest', 'GradientBoosting', 'SVR', 'LinearRegression'],
                'confidence_level': 0.95
            }
        elif model_type == 'ensemble_simple':
            return {
                'selected_models': ['LSTM', 'RandomForest', 'GradientBoosting', 'SVR', 'LinearRegression'],
                'confidence_level': 0.95,
                'ensemble_method': 'simple_average'
            }
        elif model_type == 'ensemble_weighted':
            return {
                'selected_models': ['LSTM', 'RandomForest', 'GradientBoosting', 'SVR', 'LinearRegression'],
                'confidence_level': 0.95,
                'ensemble_method': 'weighted_average'
            }
        elif model_type == 'ensemble_performance':
            return {
                'selected_models': ['LSTM', 'RandomForest', 'GradientBoosting', 'SVR', 'LinearRegression'],
                'confidence_level': 0.95,
                'ensemble_method': 'performance_weighted'
            }
        elif model_type == 'ensemble_time':
            return {
                'selected_models': ['LSTM', 'RandomForest', 'GradientBoosting', 'SVR', 'LinearRegression'],
                'confidence_level': 0.95,
                'ensemble_method': 'time_weighted',
                'time_decay_factor': 0.9
            }
        elif model_type == 'ensemble_stacking':
            return {
                'selected_models': ['LSTM', 'RandomForest', 'GradientBoosting', 'SVR', 'LinearRegression'],
                'confidence_level': 0.95,
                'ensemble_method': 'stacking',
                'meta_model': 'ridge'
            }
        elif model_type == 'hybrid':
            return {
                'sequence_length': 10,
                'lstm_units': 50,
                'dense_units': 25,
                'dropout_rate': 0.2,
                'learning_rate': 0.001,
                'epochs': 50,
                'batch_size': 32
            }
        elif model_type == 'prophet':
            return {
                'changepoint_prior_scale': 0.05,
                'seasonality_mode': 'additive',
                'daily_seasonality': True,
                'weekly_seasonality': True,
                'yearly_seasonality': True
            }
        else:
            return {}

    @staticmethod
    def get_trained_models(symbol: str, models_path: str = 'saved_models') -> Dict[str, List[int]]:
        """
        Get a dictionary of trained models for a specific symbol

        Args:
            symbol (str): Stock symbol
            models_path (str): Path where models are saved

        Returns:
            Dict[str, List[int]]: Dictionary mapping model types to lists of trained horizons (in minutes)
        """
        trained_models = {}

        # Check if the models directory exists
        if not os.path.exists(models_path):
            logger.warning(f"Models directory {models_path} does not exist")
            return trained_models

        # Get all model files for this symbol
        all_model_files = glob.glob(os.path.join(models_path, f'{symbol}_*.*'))

        # Process each file to extract model type and horizon
        for file_path in all_model_files:
            file_name = os.path.basename(file_path)

            # Skip scaler files
            if 'scaler' in file_name.lower():
                continue

            # Try to extract model type and horizon
            try:
                # Extract model type
                model_type = None
                for mt in ModelFactory.get_available_models().keys():
                    if f"_{mt}_" in file_name.lower() or f"_{mt}" in file_name.lower():
                        model_type = mt
                        break

                if not model_type:
                    continue

                # Extract horizon
                horizon = None
                if 'min.' in file_name:
                    # Extract the number before 'min.'
                    parts = file_name.split('min.')[0].split('_')
                    for part in reversed(parts):
                        if part.isdigit():
                            horizon = int(part)
                            break

                if horizon:
                    if model_type not in trained_models:
                        trained_models[model_type] = []

                    if horizon not in trained_models[model_type]:
                        trained_models[model_type].append(horizon)

            except Exception as e:
                logger.error(f"Error processing model file {file_name}: {str(e)}")
                continue

        # Sort horizons for each model type
        for model_type in trained_models:
            trained_models[model_type].sort()

        return trained_models

    @staticmethod
    def get_horizon_options(unit: str = 'minutes') -> List[int]:
        """
        Get a list of recommended horizons for the specified unit

        Args:
            unit (str): Unit of time ('minutes', 'days', or 'weeks')

        Returns:
            List[int]: List of recommended horizons
        """
        if unit == 'minutes':
            return [5, 15, 30, 60, 120, 240, 480]
        elif unit == 'days':
            return [1, 2, 3, 5, 7]
        elif unit == 'weeks':
            return [1, 2, 4, 8, 12]
        else:
            return []
