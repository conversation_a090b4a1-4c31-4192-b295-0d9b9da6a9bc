import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
import logging
import joblib
import os
from typing import Dict, Any, Tuple

from app.models.base_model import BaseModel

logger = logging.getLogger(__name__)

class LinearRegressionModel(BaseModel):
    """
    Linear Regression model for time series prediction.
    """
    
    def __init__(self, target_column='Close', **kwargs):
        """
        Initialize the Linear Regression model.
        
        Args:
            target_column (str): The column to predict (default: 'Close')
            **kwargs: Additional model parameters
        """
        super().__init__(target_column=target_column)
        self.model_name = "LinearRegression"
        
        # Model parameters
        self.fit_intercept = kwargs.get('fit_intercept', True)
        self.normalize = kwargs.get('normalize', False)
        
        # Scaler
        self.feature_scaler = StandardScaler()
        
        # Model
        self.model = LinearRegression(
            fit_intercept=self.fit_intercept
        )
        
        # Coefficients
        self.coefficients = None
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Preprocess the data for the Linear Regression model.
        
        Args:
            data (pd.DataFrame): The input data
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Processed features and target
        """
        # Make a copy to avoid modifying the original data
        df = data.copy()
        
        # Extract target
        target = df[[self.target_column]]
        
        # Create features
        # Use all numeric columns except the target as features
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        if self.target_column in numeric_cols:
            numeric_cols.remove(self.target_column)
        
        features = df[numeric_cols]
        
        # Add some basic technical indicators
        if 'Close' in features.columns:
            # Moving averages
            features['MA_5'] = df['Close'].rolling(window=5).mean()
            features['MA_10'] = df['Close'].rolling(window=10).mean()
            
            # Price momentum
            features['Return_1d'] = df['Close'].pct_change(1)
            features['Return_5d'] = df['Close'].pct_change(5)
        
        # Fill NaN values
        features = features.fillna(method='bfill').fillna(method='ffill')
        
        return features, target
    
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train the Linear Regression model.
        
        Args:
            data (pd.DataFrame): The training data
            **kwargs: Additional training parameters
            
        Returns:
            Dict[str, Any]: Training metrics
        """
        logger.info("Training Linear Regression model")
        
        # Override default parameters if provided
        self.fit_intercept = kwargs.get('fit_intercept', self.fit_intercept)
        
        # Update model with new parameters
        self.model = LinearRegression(
            fit_intercept=self.fit_intercept
        )
        
        # Preprocess data
        features, target = self.preprocess_data(data)
        
        # Scale features
        X = self.feature_scaler.fit_transform(features)
        y = target.values.ravel()
        
        # Train model
        self.model.fit(X, y)
        
        # Store coefficients
        self.coefficients = pd.DataFrame({
            'Feature': features.columns,
            'Coefficient': self.model.coef_
        }).sort_values('Coefficient', ascending=False)
        
        # Return training metrics
        return {
            'coefficients': self.coefficients,
            'intercept': self.model.intercept_
        }
    
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions using the Linear Regression model.
        
        Args:
            data (pd.DataFrame): The input data
            
        Returns:
            np.ndarray: Predicted values
        """
        # Preprocess data
        features, _ = self.preprocess_data(data)
        
        # Scale features
        X = self.feature_scaler.transform(features)
        
        # Generate predictions
        predictions = self.model.predict(X)
        
        return predictions
    
    def save(self, path: str) -> None:
        """
        Save the Linear Regression model to disk.
        
        Args:
            path (str): The path to save the model
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save model and scaler
        model_data = {
            'model': self.model,
            'feature_scaler': self.feature_scaler,
            'coefficients': self.coefficients,
            'params': {
                'fit_intercept': self.fit_intercept,
                'target_column': self.target_column
            }
        }
        
        joblib.dump(model_data, path)
        logger.info(f"Linear Regression model saved to {path}")
    
    def load(self, path: str) -> None:
        """
        Load the Linear Regression model from disk.
        
        Args:
            path (str): The path to load the model from
        """
        # Load model and scaler
        model_data = joblib.load(path)
        
        self.model = model_data['model']
        self.feature_scaler = model_data['feature_scaler']
        self.coefficients = model_data.get('coefficients', None)
        
        # Load parameters
        params = model_data.get('params', {})
        self.fit_intercept = params.get('fit_intercept', self.fit_intercept)
        self.target_column = params.get('target_column', self.target_column)
        
        logger.info(f"Linear Regression model loaded from {path}")
