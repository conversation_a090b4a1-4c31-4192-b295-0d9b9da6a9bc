# Add the project root directory to the Python path if not already added
import os
import sys
import logging
import warnings
from typing import List, Dict, Any, Tuple, Optional, Union, Callable
from datetime import datetime, timedelta

# Ensure the project root is in the path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Now import the required packages
try:
    import numpy as np
    import pandas as pd
    import matplotlib.pyplot as plt
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    from sklearn.linear_model import Ridge, Lasso
    from sklearn.ensemble import StackingRegressor
    import joblib
    
    # Import model classes
    from app.models.lstm_model import LSTMModel
    from app.models.random_forest_model import RandomForestModel
    from app.models.gradient_boosting_model import GradientBoostingModel
    from app.models.svr_model import SVRModel
    from app.models.linear_regression_model import LinearRegressionModel
except ImportError as e:
    logging.error(f"Error importing required packages: {str(e)}")
    raise

# Apply NumPy fix to handle BitGenerator issues
try:
    # Import and apply the NumPy MT19937 fix
    from app.utils.numpy_fix_mt19937 import fix_numpy_mt19937
    fix_numpy_mt19937()
except Exception as fix_error:
    logging.warning(f"Error applying NumPy MT19937 fix: {str(fix_error)}")

# Also apply the new BitGenerator fix
try:
    from app.utils.numpy_bitgenerator_fix import fix_numpy_bitgenerator
    fix_numpy_bitgenerator()
except Exception as fix_error:
    logging.warning(f"Error applying NumPy BitGenerator fix: {str(fix_error)}")

# Try to import seaborn, but make it optional
try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Seaborn not available: {str(e)}. Some visualization features will be limited.")
    SEABORN_AVAILABLE = False

from app.models.lstm_model import LSTMModel
from app.models.random_forest_model import RandomForestModel
from app.models.gradient_boosting_model import GradientBoostingModel
from app.models.svr_model import SVRModel
from app.models.linear_regression_model import LinearRegressionModel
from app.models.base_model import BaseModel

logger = logging.getLogger(__name__)

class EnsembleModel(BaseModel):
    """
    Advanced Ensemble model that combines predictions from multiple models.

    This model supports multiple ensemble methods:
    1. Simple averaging - Equal weight to all models
    2. Weighted averaging - Weights based on model performance
    3. Performance-based weighting - Weights dynamically adjusted based on recent performance
    4. Time-based weighting - More weight to recent performance
    5. Stacking - Using a meta-model to combine predictions

    It also provides confidence intervals and uncertainty estimates for predictions.
    """

    # Define ensemble methods
    ENSEMBLE_METHODS = {
        'simple_average': 'Simple Average',
        'weighted_average': 'Weighted Average',
        'performance_weighted': 'Performance-Based Weighting',
        'time_weighted': 'Time-Based Weighting',
        'stacking': 'Stacking (Meta-Model)'
    }

    def __init__(self, target_column='Close', **kwargs):
        """
        Initialize the ensemble model.

        Args:
            target_column (str): The column to predict (default: 'Close')
            **kwargs: Additional arguments for the base models
                - ensemble_method (str): Method to combine predictions
                - selected_models (list): List of models to include
                - confidence_level (float): Confidence level for intervals
                - time_decay_factor (float): Factor for time-based weighting
                - meta_model (str): Type of meta-model for stacking
                - performance_window (int): Window size for performance calculation
        """
        super().__init__(target_column=target_column)
        self.model_name = "Ensemble"
        self.models = {}
        self.weights = {}
        self.default_models = {
            'LSTM': LSTMModel(target_column=target_column),
            'RandomForest': RandomForestModel(target_column=target_column),
            'GradientBoosting': GradientBoostingModel(target_column=target_column),
            'SVR': SVRModel(target_column=target_column),
            'LinearRegression': LinearRegressionModel(target_column=target_column)
        }

        # Model selection and configuration
        self.selected_models = kwargs.get('selected_models', list(self.default_models.keys()))
        self.confidence_level = kwargs.get('confidence_level', 0.95)

        # Ensemble method configuration
        self.ensemble_method = kwargs.get('ensemble_method', 'weighted_average')
        self.time_decay_factor = kwargs.get('time_decay_factor', 0.9)
        self.meta_model_type = kwargs.get('meta_model', 'ridge')
        self.meta_model = None
        self.performance_window = kwargs.get('performance_window', 30)  # Days

        # Performance tracking
        self.model_performances = {}
        self.historical_performances = {}
        self.last_update_time = None
        self.prediction_history = []

    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Preprocess the data for all models.

        Args:
            data (pd.DataFrame): The input data

        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Processed features and target
        """
        # Each model will handle its own preprocessing
        return data, data[[self.target_column]]

    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train all selected models and calculate optimal weights using the selected ensemble method.

        Args:
            data (pd.DataFrame): The training data
            **kwargs: Additional training parameters
                - val_split (float): Validation split ratio
                - test_split (float): Test split ratio
                - update_weights (bool): Whether to update weights based on new data

        Returns:
            Dict[str, Any]: Training metrics
        """
        # Initialize return values
        model_metrics = {}
        val_metrics = {}
        val_predictions = {}
        
        try:
            # Log the start of training
            logger.info(f"Training ensemble model with method: {self.ensemble_method}")
            logger.info(f"Selected models: {self.selected_models}")

            # Apply NumPy fixes to ensure stability
            self._apply_numpy_fixes()
            
            # Initialize models if not already done
            self._initialize_models()
            
            # Check if we have any models to train
            if not self.models:
                logger.error("No models available for training")
                return {'error': 'No models available for training'}

            # Get validation split
            val_split = kwargs.get('val_split', 0.2)
            test_split = kwargs.get('test_split', 0.1)

            # Ensure we have enough data
            if len(data) < 10:  # Arbitrary minimum
                logger.error(f"Not enough data for training: {len(data)} rows")
                return {'error': 'Not enough data for training'}

            # Split data into train, validation, and test sets
            train_size = int(len(data) * (1 - val_split - test_split))
            val_size = int(len(data) * val_split)

            train_data = data.iloc[:train_size]
            val_data = data.iloc[train_size:train_size + val_size]
            test_data = data.iloc[train_size + val_size:]

            # Train each model
            for model_name, model in list(self.models.items()):  # Use list to allow dict modification during iteration
                try:
                    logger.info(f"Training model: {model_name}")
                    metrics = model.train(train_data)
                    model_metrics[model_name] = metrics
                    logger.info(f"Model {model_name} trained successfully")
                except Exception as e:
                    logger.error(f"Error training model {model_name}: {str(e)}")
                    # Remove the model from the ensemble
                    self.models.pop(model_name, None)
                    continue
            
            # Check if we have any successfully trained models
            if not model_metrics:
                logger.error("No models were successfully trained")
                return {'error': 'No models were successfully trained'}
                
            # Evaluate models on validation data
            try:
                X_val, y_val = self.preprocess_data(val_data)
            except Exception as e:
                logger.error(f"Error preprocessing validation data: {str(e)}")
                # Use a fallback approach
                y_val = val_data[self.target_column].values
                X_val = val_data.drop(columns=[self.target_column])
            
            # Evaluate each model
            for model_name, model in list(self.models.items()):  # Use list to allow dict modification
                try:
                    # Get predictions for validation data
                    val_pred = model.predict(val_data)
                    val_predictions[model_name] = val_pred
                    
                    # Calculate metrics
                    mse = mean_squared_error(y_val, val_pred)
                    mae = mean_absolute_error(y_val, val_pred)
                    
                    # R2 can sometimes fail if predictions are constant
                    try:
                        r2 = r2_score(y_val, val_pred)
                    except:
                        r2 = 0.0
                    
                    val_metrics[model_name] = {
                        'MSE': mse,
                        'MAE': mae,
                        'R2': r2
                    }
                    
                    # Store performance metrics for historical tracking
                    timestamp = datetime.now()
                    performance = {
                        'mse': mse,
                        'mae': mae,
                        'rmse': np.sqrt(mse),
                        'r2': r2,
                        'timestamp': timestamp
                    }

                    # Add to historical performances
                    if not hasattr(self, 'historical_performances'):
                        self.historical_performances = {}
                    
                    if model_name not in self.historical_performances:
                        self.historical_performances[model_name] = []
                    
                    self.historical_performances[model_name].append(performance)
                    
                    # Store in model_performances for weight calculation
                    if not hasattr(self, 'model_performances'):
                        self.model_performances = {}
                    
                    self.model_performances[model_name] = {
                        'mse': mse,
                        'mae': mae,
                        'rmse': np.sqrt(mse),
                        'r2': r2,
                        'last_updated': timestamp
                    }

                    # Keep only the most recent performances if we have a window defined
                    if hasattr(self, 'performance_window') and self.performance_window > 0:
                        window_start = timestamp - timedelta(days=self.performance_window)
                        self.historical_performances[model_name] = [
                            perf for perf in self.historical_performances[model_name]
                            if perf['timestamp'] >= window_start
                        ]

                    logger.info(f"Model {model_name} validation metrics: MSE={mse:.4f}, MAE={mae:.4f}, R2={r2:.4f}")
            
                except Exception as e:
                    logger.error(f"Error evaluating model {model_name}: {str(e)}")
                    # Remove the model from the ensemble
                    self.models.pop(model_name, None)
                    continue
            
            # Check if we have any models left after evaluation
            if not val_metrics:
                logger.error("No models passed validation")
                return {'error': 'No models passed validation'}

            # Calculate weights based on the selected ensemble method
            try:
                if self.ensemble_method == 'simple_average':
                    self._calculate_simple_average_weights()
                elif self.ensemble_method == 'weighted_average':
                    self._calculate_weighted_average_weights()
                elif self.ensemble_method == 'performance_weighted':
                    self._calculate_performance_weighted_weights()
                elif self.ensemble_method == 'time_weighted':
                    self._calculate_time_weighted_weights()
                elif self.ensemble_method == 'stacking':
                    self._train_stacking_model(val_data, val_predictions)
                else:
                    # Default to simple average if method is not recognized
                    logger.warning(f"Unrecognized ensemble method: {self.ensemble_method}, using simple average")
                    self._calculate_simple_average_weights()
            except Exception as e:
                logger.error(f"Error calculating weights: {str(e)}")
                # Fall back to simple average
                self._calculate_simple_average_weights()

            logger.info(f"Ensemble model trained successfully with {len(self.models)} base models")
            logger.info(f"Weights: {self.weights}")

            # Return metrics
            return {
                'model_metrics': model_metrics,
                'validation_metrics': val_metrics,
                'model_performances': self.model_performances if hasattr(self, 'model_performances') else {},
                'weights': self.weights,
                'ensemble_method': self.ensemble_method if hasattr(self, 'ensemble_method') else 'simple_average'
            }
        except Exception as e:
            logger.error(f"Unexpected error in ensemble training: {str(e)}")
            return {'error': f"Unexpected error in ensemble training: {str(e)}"}

    def _apply_numpy_fixes(self) -> None:
        """Apply NumPy fixes to ensure stability."""
        try:
            from app.utils.numpy_fix_mt19937 import fix_numpy_mt19937
            fix_numpy_mt19937()
            logger.info("Applied NumPy MT19937 fix before ensemble training")
        except Exception as fix_error:
            logger.warning(f"Error applying NumPy MT19937 fix: {str(fix_error)}")

        try:
            from app.utils.numpy_bitgenerator_fix import fix_numpy_bitgenerator
            fix_numpy_bitgenerator()
            logger.info("Applied NumPy BitGenerator fix before ensemble training")
        except Exception as fix_error:
            logger.warning(f"Error applying NumPy BitGenerator fix: {str(fix_error)}")
            
    def _initialize_models(self) -> None:
        """Initialize models if not already done."""
        for model_name in self.selected_models:
            if model_name not in self.models:
                if model_name in self.default_models:
                    try:
                        # Create a fresh instance of the model
                        if model_name == 'LSTM':
                            self.models[model_name] = LSTMModel(target_column=self.target_column)
                        elif model_name == 'RandomForest':
                            self.models[model_name] = RandomForestModel(target_column=self.target_column)
                        elif model_name == 'GradientBoosting':
                            self.models[model_name] = GradientBoostingModel(target_column=self.target_column)
                        elif model_name == 'SVR':
                            self.models[model_name] = SVRModel(target_column=self.target_column)
                        elif model_name == 'LinearRegression':
                            self.models[model_name] = LinearRegressionModel(target_column=self.target_column)
                        else:
                            # Use the default model as fallback
                            self.models[model_name] = self.default_models[model_name]
                        
                        logger.info(f"Initialized model: {model_name}")
                    except Exception as init_error:
                        logger.error(f"Error initializing model {model_name}: {str(init_error)}")
                        # Skip this model
                        continue
                else:
                    logger.warning(f"Model {model_name} not found in default models, skipping")
    
    def _calculate_simple_average_weights(self) -> None:
        """Calculate equal weights for all models."""
        n_models = len(self.models)
        if n_models > 0:
            weight = 1.0 / n_models
            self.weights = {name: weight for name in self.models.keys()}

    def _calculate_weighted_average_weights(self) -> None:
        """Calculate weights based on inverse MSE."""
        if self.models:
            inverse_mse = {name: 1.0 / (perf['mse'] + 1e-10)
                          for name, perf in self.model_performances.items()}
            total_inverse_mse = sum(inverse_mse.values())
            self.weights = {name: inv_mse / total_inverse_mse
                           for name, inv_mse in inverse_mse.items()}

    def _calculate_performance_weighted_weights(self) -> None:
        """
        Calculate weights based on multiple performance metrics.
        Uses a combination of MSE, MAE, R², and directional accuracy.
        """
        if self.models:
            # Normalize each metric to [0, 1] range
            metrics = {}
            for name, perf in self.model_performances.items():
                # For MSE and MAE, lower is better, so use inverse
                inv_mse = 1.0 / (perf['mse'] + 1e-10)
                inv_mae = 1.0 / (perf['mae'] + 1e-10)

                # For R² and directional accuracy, higher is better
                r2 = max(0, perf['r2'])  # Ensure non-negative
                dir_acc = perf['directional_accuracy'] / 100.0  # Scale to [0, 1]

                # Combine metrics with weights
                # You can adjust these weights based on what's most important
                combined_score = (0.3 * inv_mse + 0.2 * inv_mae +
                                 0.2 * r2 + 0.3 * dir_acc)

                metrics[name] = combined_score

            # Normalize to sum to 1
            total_score = sum(metrics.values())
            if total_score > 0:
                self.weights = {name: score / total_score
                               for name, score in metrics.items()}
            else:
                # Fallback to equal weights
                self._calculate_simple_average_weights()

    def _calculate_time_weighted_weights(self) -> None:
        """
        Calculate weights based on recent performance.
        More recent performance has higher weight.
        """
        if not self.models or not self.historical_performances:
            # Fallback to performance-weighted if no history
            self._calculate_performance_weighted_weights()
            return

        weights = {}
        for name in self.models.keys():
            if name not in self.historical_performances:
                continue

            # Get historical performances sorted by time
            history = sorted(self.historical_performances[name],
                            key=lambda x: x['timestamp'])

            if not history:
                continue

            # Calculate time-decayed weights
            total_weight = 0
            weighted_score = 0

            for i, perf in enumerate(history):
                # More recent performances get higher weight
                time_weight = self.time_decay_factor ** (len(history) - i - 1)
                total_weight += time_weight

                # Use inverse MSE as the performance metric
                inv_mse = 1.0 / (perf['mse'] + 1e-10)
                weighted_score += time_weight * inv_mse

            if total_weight > 0:
                weights[name] = weighted_score / total_weight

        # Normalize to sum to 1
        total = sum(weights.values())
        if total > 0:
            self.weights = {name: w / total for name, w in weights.items()}
        else:
            # Fallback to performance-weighted
            self._calculate_performance_weighted_weights()

    def _train_stacking_model(self, val_data: pd.DataFrame,
                             model_predictions: Dict[str, np.ndarray]) -> None:
        """
        Train a meta-model that combines predictions from base models.

        Args:
            val_data: Validation data used to train the meta-model
            model_predictions: Dictionary of predictions from each base model
        """
        if not model_predictions:
            logger.error("No model predictions available for stacking")
            self._calculate_weighted_average_weights()
            return

        try:
            # Prepare features (predictions from base models)
            X_meta = np.column_stack([model_predictions[name]
                                     for name in model_predictions.keys()])

            # Target values
            y_meta = val_data[self.target_column].values

            # Create meta-model
            if self.meta_model_type == 'ridge':
                self.meta_model = Ridge(alpha=1.0)
            elif self.meta_model_type == 'lasso':
                self.meta_model = Lasso(alpha=0.1)
            else:
                logger.warning(f"Unknown meta-model type '{self.meta_model_type}'. Using Ridge.")
                self.meta_model = Ridge(alpha=1.0)

            # Train meta-model
            self.meta_model.fit(X_meta, y_meta)

            # Get coefficients as weights
            model_names = list(model_predictions.keys())
            coeffs = self.meta_model.coef_

            # Ensure non-negative weights and normalize
            coeffs = np.maximum(coeffs, 0)  # Ensure non-negative
            total = np.sum(coeffs)

            if total > 0:
                self.weights = {name: float(coef / total)
                               for name, coef in zip(model_names, coeffs)}
            else:
                # Fallback to weighted average
                self._calculate_weighted_average_weights()

            logger.info(f"Stacking meta-model trained with coefficients: {self.weights}")
        except Exception as e:
            logger.error(f"Error training stacking model: {str(e)}")
            # Fallback to weighted average
            self._calculate_weighted_average_weights()

    def predict(self, data: pd.DataFrame, **kwargs) -> np.ndarray:
        """
        Generate ensemble predictions using the selected method.

        Args:
            data (pd.DataFrame): The input data
            **kwargs: Additional parameters
                - ensemble_method (str): Override the ensemble method
                - return_all (bool): Return all model predictions

        Returns:
            np.ndarray: Predicted values
        """
        ensemble_method = kwargs.get('ensemble_method', self.ensemble_method)
        return_all = kwargs.get('return_all', False)

        if not self.models:
            logger.error("No models available for prediction. Please train the model first.")
            return np.full(len(data), np.nan)

        # Get predictions from each model
        all_predictions = {}
        for name, model in self.models.items():
            try:
                # Check if the model is a dictionary (which might happen if a model failed to load)
                if isinstance(model, dict):
                    logger.error(f"Model {name} is not properly initialized")
                    continue

                # Get predictions from the model
                predictions = model.predict(data)

                # Store predictions if they're valid
                if predictions is not None and not np.isnan(predictions).all():
                    all_predictions[name] = predictions
                else:
                    logger.error(f"Model {name} returned invalid predictions")
            except Exception as e:
                logger.error(f"Error getting predictions from {name} model: {str(e)}")

        # Check if we have any valid predictions
        if not all_predictions:
            logger.error("No valid predictions from any model")
            return np.full(len(data), np.nan)

        # Generate ensemble predictions based on the method
        if ensemble_method == 'stacking' and self.meta_model is not None:
            # Use meta-model for stacking
            try:
                X_meta = np.column_stack([all_predictions[name]
                                         for name in all_predictions.keys()])
                ensemble_predictions = self.meta_model.predict(X_meta)
            except Exception as e:
                logger.error(f"Error using stacking meta-model: {str(e)}")
                # Fall back to weighted average
                ensemble_predictions = self._weighted_average_predict(all_predictions)
        else:
            # Use weighted average for all other methods
            ensemble_predictions = self._weighted_average_predict(all_predictions)

        # Store prediction for performance tracking
        self._record_prediction(data, all_predictions, ensemble_predictions)

        # Return all predictions if requested
        if return_all:
            return ensemble_predictions, all_predictions

        return ensemble_predictions

    def _weighted_average_predict(self, all_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Combine predictions using weighted average.

        Args:
            all_predictions: Dictionary of predictions from each model

        Returns:
            Combined predictions
        """
        # Get data length from first prediction
        data_len = len(next(iter(all_predictions.values())))

        # Combine predictions using weights
        weighted_predictions = np.zeros(data_len)
        total_weight = 0

        for name, predictions in all_predictions.items():
            weight = self.weights.get(name, 0)
            weighted_predictions += predictions * weight
            total_weight += weight

        # Normalize by total weight if it's not zero
        if total_weight > 0:
            weighted_predictions /= total_weight

        return weighted_predictions

    def _record_prediction(self, data: pd.DataFrame,
                          model_predictions: Dict[str, np.ndarray],
                          ensemble_prediction: np.ndarray) -> None:
        """
        Record prediction for later performance evaluation.

        Args:
            data: Input data
            model_predictions: Predictions from each model
            ensemble_prediction: Final ensemble prediction
        """
        # Only record if we have actual values to compare against
        if self.target_column in data.columns:
            try:
                actual = data[self.target_column].values

                # Create prediction record
                record = {
                    'timestamp': datetime.now(),
                    'ensemble_prediction': ensemble_prediction,
                    'model_predictions': model_predictions,
                    'actual': actual if len(actual) > 0 else None
                }

                # Add to prediction history
                self.prediction_history.append(record)

                # Keep only recent history (last 100 predictions)
                if len(self.prediction_history) > 100:
                    self.prediction_history = self.prediction_history[-100:]
            except Exception as e:
                logger.error(f"Error recording prediction: {str(e)}")

    def predict_with_confidence(self, data: pd.DataFrame, **kwargs) -> Dict[str, np.ndarray]:
        """
        Generate predictions with confidence intervals and uncertainty estimates.

        Args:
            data (pd.DataFrame): The input data
            **kwargs: Additional parameters
                - ensemble_method (str): Override the ensemble method
                - confidence_level (float): Confidence level (0-1)
                - include_model_predictions (bool): Include individual model predictions

        Returns:
            Dict[str, np.ndarray]: Predictions and confidence intervals
        """
        # Get parameters
        ensemble_method = kwargs.get('ensemble_method', self.ensemble_method)
        confidence_level = kwargs.get('confidence_level', self.confidence_level)
        include_model_predictions = kwargs.get('include_model_predictions', True)

        # Calculate z-score based on confidence level
        # 90% -> 1.645, 95% -> 1.96, 99% -> 2.576
        if confidence_level >= 0.99:
            z_score = 2.576
        elif confidence_level >= 0.95:
            z_score = 1.96
        elif confidence_level >= 0.90:
            z_score = 1.645
        else:
            z_score = 1.96  # Default to 95%

        if not self.models:
            logger.error("No models available for prediction. Please train the model first.")
            return {
                'predictions': np.full(len(data), np.nan),
                'lower_bound': np.full(len(data), np.nan),
                'upper_bound': np.full(len(data), np.nan),
                'std_dev': np.full(len(data), np.nan),
                'uncertainty': np.full(len(data), np.nan),
                'model_predictions': {} if include_model_predictions else None
            }

        # Get predictions from each model
        all_predictions = {}
        for name, model in self.models.items():
            try:
                # Check if the model is a dictionary (which might happen if a model failed to load)
                if isinstance(model, dict):
                    logger.error(f"Model {name} is not properly initialized")
                    continue

                # Get predictions from the model
                predictions = model.predict(data)

                # Store predictions if they're valid
                if predictions is not None and not np.isnan(predictions).all():
                    all_predictions[name] = predictions
                else:
                    logger.error(f"Model {name} returned invalid predictions")
            except Exception as e:
                logger.error(f"Error getting predictions from {name} model: {str(e)}")

        # Check if we have any valid predictions
        if not all_predictions:
            logger.error("No valid predictions from any model")
            return {
                'predictions': np.full(len(data), np.nan),
                'lower_bound': np.full(len(data), np.nan),
                'upper_bound': np.full(len(data), np.nan),
                'std_dev': np.full(len(data), np.nan),
                'uncertainty': np.full(len(data), np.nan),
                'model_predictions': {} if include_model_predictions else None
            }

        # Generate ensemble predictions based on the method
        if ensemble_method == 'stacking' and self.meta_model is not None:
            # Use meta-model for stacking
            try:
                X_meta = np.column_stack([all_predictions[name]
                                         for name in all_predictions.keys()])
                ensemble_predictions = self.meta_model.predict(X_meta)
            except Exception as e:
                logger.error(f"Error using stacking meta-model: {str(e)}")
                # Fall back to weighted average
                ensemble_predictions = self._weighted_average_predict(all_predictions)
        else:
            # Use weighted average for all other methods
            ensemble_predictions = self._weighted_average_predict(all_predictions)

        # Calculate standard deviation of predictions (model disagreement)
        model_std_dev = np.zeros(len(data))
        model_count = len(all_predictions)

        if model_count > 1:
            # Calculate the standard deviation across models
            all_preds_array = np.array(list(all_predictions.values()))
            model_std_dev = np.std(all_preds_array, axis=0)
        else:
            # For a single model, use historical error as uncertainty
            model_name = list(all_predictions.keys())[0]
            if model_name in self.model_performances:
                # Use RMSE as a proxy for uncertainty
                rmse = self.model_performances[model_name].get('rmse', 0.05)
                model_std_dev = np.full(len(data), rmse)

        # Calculate uncertainty score (0-1) - normalized standard deviation
        # Higher values mean more uncertainty
        if np.max(model_std_dev) > 0:
            uncertainty = model_std_dev / np.max(model_std_dev)
        else:
            uncertainty = np.zeros(len(data))

        # Calculate confidence intervals
        lower_bound = ensemble_predictions - z_score * model_std_dev
        upper_bound = ensemble_predictions + z_score * model_std_dev

        # Calculate prediction intervals based on historical performance
        # This accounts for both model disagreement and historical accuracy
        historical_rmse = 0.0
        if self.model_performances:
            # Get weighted average of RMSE values
            total_weight = 0.0
            weighted_rmse = 0.0
            for name, weight in self.weights.items():
                if name in self.model_performances:
                    rmse = self.model_performances[name].get('rmse', 0.0)
                    weighted_rmse += weight * rmse
                    total_weight += weight

            if total_weight > 0:
                historical_rmse = weighted_rmse / total_weight

        # Combine model disagreement with historical error
        combined_std = np.sqrt(model_std_dev**2 + historical_rmse**2)

        # Adjust bounds to include historical error
        pred_lower_bound = ensemble_predictions - z_score * combined_std
        pred_upper_bound = ensemble_predictions + z_score * combined_std

        # Store prediction for performance tracking
        self._record_prediction(data, all_predictions, ensemble_predictions)

        result = {
            'predictions': ensemble_predictions,
            'lower_bound': lower_bound,  # Based on model disagreement
            'upper_bound': upper_bound,  # Based on model disagreement
            'pred_lower_bound': pred_lower_bound,  # Includes historical error
            'pred_upper_bound': pred_upper_bound,  # Includes historical error
            'std_dev': model_std_dev,
            'uncertainty': uncertainty,
            'ensemble_method': ensemble_method,
            'confidence_level': confidence_level
        }

        # Include individual model predictions if requested
        if include_model_predictions:
            result['model_predictions'] = all_predictions

        return result

    def save(self, path: str) -> None:
        """
        Save the ensemble model to disk.

        Args:
            path (str): The path to save the model
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # Save each base model
        for name, model in self.models.items():
            model_path = os.path.join(os.path.dirname(path), f"{name}_model.pkl")
            model.save(model_path)

        # Save meta-model if it exists
        if self.meta_model is not None:
            meta_model_path = os.path.join(os.path.dirname(path), "meta_model.pkl")
            try:
                joblib.dump(self.meta_model, meta_model_path)
                logger.info(f"Meta-model saved to {meta_model_path}")
            except Exception as e:
                logger.error(f"Error saving meta-model: {str(e)}")

        # Save ensemble metadata
        ensemble_data = {
            'weights': self.weights,
            'selected_models': self.selected_models,
            'model_performances': self.model_performances,
            'historical_performances': self.historical_performances,
            'target_column': self.target_column,
            'ensemble_method': self.ensemble_method,
            'confidence_level': self.confidence_level,
            'time_decay_factor': self.time_decay_factor,
            'meta_model_type': self.meta_model_type,
            'performance_window': self.performance_window,
            'last_update_time': self.last_update_time,
            'version': '2.0'  # Version tracking for compatibility
        }

        joblib.dump(ensemble_data, path)
        logger.info(f"Enhanced Ensemble model saved to {path}")

    def load(self, path: str) -> None:
        """
        Load the ensemble model from disk.

        Args:
            path (str): The path to load the model from
        """
        try:
            # Load ensemble metadata
            ensemble_data = joblib.load(path)

            # Check version for compatibility
            version = ensemble_data.get('version', '1.0')
            logger.info(f"Loading ensemble model version {version}")

            # Load basic properties
            self.weights = ensemble_data['weights']
            self.selected_models = ensemble_data['selected_models']
            self.model_performances = ensemble_data.get('model_performances', {})
            self.target_column = ensemble_data.get('target_column', self.target_column)

            # Load enhanced properties (with defaults for backward compatibility)
            self.ensemble_method = ensemble_data.get('ensemble_method', 'weighted_average')
            self.confidence_level = ensemble_data.get('confidence_level', 0.95)
            self.time_decay_factor = ensemble_data.get('time_decay_factor', 0.9)
            self.meta_model_type = ensemble_data.get('meta_model_type', 'ridge')
            self.performance_window = ensemble_data.get('performance_window', 30)
            self.historical_performances = ensemble_data.get('historical_performances', {})
            self.last_update_time = ensemble_data.get('last_update_time', None)

            # Load each base model
            self.models = {}
            for model_name in self.selected_models:
                if model_name in self.default_models:
                    model = self.default_models[model_name]
                    model_path = os.path.join(os.path.dirname(path), f"{model_name}_model.pkl")
                    try:
                        model.load(model_path)
                        self.models[model_name] = model
                    except Exception as e:
                        logger.error(f"Error loading {model_name} model: {str(e)}")

            # Load meta-model if it exists and we're using stacking
            if self.ensemble_method == 'stacking':
                meta_model_path = os.path.join(os.path.dirname(path), "meta_model.pkl")
                if os.path.exists(meta_model_path):
                    try:
                        self.meta_model = joblib.load(meta_model_path)
                        logger.info(f"Meta-model loaded from {meta_model_path}")
                    except Exception as e:
                        logger.error(f"Error loading meta-model: {str(e)}")
                        self.meta_model = None
                else:
                    logger.warning(f"Meta-model file not found at {meta_model_path}")
                    self.meta_model = None

            logger.info(f"Enhanced Ensemble model loaded from {path} with {len(self.models)} base models")

            # Log the ensemble method and weights
            logger.info(f"Ensemble method: {self.ensemble_method}")
            logger.info(f"Model weights: {self.weights}")

        except Exception as e:
            logger.error(f"Error loading ensemble model: {str(e)}")
            raise

    def get_model_comparison(self, data: pd.DataFrame, **kwargs) -> Tuple[pd.DataFrame, Optional[pd.DataFrame]]:
        """
        Generate a comprehensive comparison table of all model predictions.

        Args:
            data (pd.DataFrame): The input data
            **kwargs: Additional parameters
                - ensemble_methods (list): List of ensemble methods to compare
                - include_uncertainty (bool): Include uncertainty estimates

        Returns:
            Tuple[pd.DataFrame, Optional[pd.DataFrame]]:
                - Comparison table with predictions from all models
                - Performance metrics table (if actual values are available)
        """
        ensemble_methods = kwargs.get('ensemble_methods', ['simple_average', 'weighted_average', 'performance_weighted'])
        include_uncertainty = kwargs.get('include_uncertainty', True)

        if not self.models:
            logger.error("No models available for comparison. Please train the model first.")
            return pd.DataFrame(), None

        # Get predictions from each model
        all_predictions = {}
        for name, model in self.models.items():
            try:
                # Check if the model is a dictionary (which might happen if a model failed to load)
                if isinstance(model, dict):
                    logger.error(f"Model {name} is not properly initialized")
                    continue

                # Get predictions from the model
                predictions = model.predict(data)

                # Store predictions if they're valid
                if predictions is not None and not np.isnan(predictions).all():
                    all_predictions[name] = predictions
                else:
                    logger.error(f"Model {name} returned invalid predictions")
            except Exception as e:
                logger.error(f"Error getting predictions from {name} model: {str(e)}")

        # Check if we have any valid predictions
        if not all_predictions:
            logger.error("No valid predictions from any model")
            return pd.DataFrame(), None

        # Calculate ensemble predictions for each method
        for method in ensemble_methods:
            if method in self.ENSEMBLE_METHODS:
                # Temporarily set the ensemble method
                original_method = self.ensemble_method
                self.ensemble_method = method

                # Get predictions with this method
                try:
                    if method == 'stacking' and self.meta_model is None:
                        # Skip stacking if no meta-model is available
                        continue

                    ensemble_pred = self.predict(data)
                    if not np.isnan(ensemble_pred).all():
                        method_name = self.ENSEMBLE_METHODS[method]
                        all_predictions[f'Ensemble ({method_name})'] = ensemble_pred

                        # Add uncertainty estimates if requested
                        if include_uncertainty:
                            confidence_results = self.predict_with_confidence(data)
                            all_predictions[f'Ensemble ({method_name}) Lower'] = confidence_results['lower_bound']
                            all_predictions[f'Ensemble ({method_name}) Upper'] = confidence_results['upper_bound']
                except Exception as e:
                    logger.error(f"Error calculating ensemble predictions with method {method}: {str(e)}")

                # Restore original method
                self.ensemble_method = original_method

        # Create comparison DataFrame
        comparison_df = pd.DataFrame(all_predictions)

        # Add actual values if available
        if self.target_column in data.columns:
            comparison_df['Actual'] = data[self.target_column].values

            # Calculate errors
            for name in all_predictions.keys():
                # Skip confidence bounds for error calculation
                if 'Lower' in name or 'Upper' in name:
                    continue

                comparison_df[f'{name}_Error'] = comparison_df['Actual'] - comparison_df[name]
                comparison_df[f'{name}_AbsError'] = np.abs(comparison_df[f'{name}_Error'])

                # Calculate percent error
                comparison_df[f'{name}_PctError'] = (comparison_df[f'{name}_AbsError'] /
                                                    comparison_df['Actual']) * 100

            # Calculate performance metrics
            metrics = {}
            for name in all_predictions.keys():
                # Skip confidence bounds for metrics calculation
                if 'Lower' in name or 'Upper' in name:
                    continue

                try:
                    # Calculate basic metrics
                    mse = mean_squared_error(comparison_df['Actual'], comparison_df[name])
                    rmse = np.sqrt(mse)
                    mae = mean_absolute_error(comparison_df['Actual'], comparison_df[name])

                    # Calculate R-squared
                    r2 = r2_score(comparison_df['Actual'], comparison_df[name])

                    # Calculate MAPE (Mean Absolute Percentage Error)
                    mape = np.mean((comparison_df[f'{name}_AbsError'] / comparison_df['Actual']) * 100)

                    # Calculate directional accuracy
                    actual_diff = np.diff(np.append([comparison_df['Actual'].iloc[0]], comparison_df['Actual']))
                    pred_diff = np.diff(np.append([comparison_df[name].iloc[0]], comparison_df[name]))
                    directional_accuracy = np.mean(np.sign(actual_diff) == np.sign(pred_diff)) * 100

                    metrics[name] = {
                        'RMSE': rmse,
                        'MAE': mae,
                        'MAPE (%)': mape,
                        'R²': r2,
                        'Dir. Acc. (%)': directional_accuracy
                    }
                except Exception as e:
                    logger.error(f"Error calculating metrics for {name} model: {str(e)}")
                    metrics[name] = {
                        'RMSE': np.nan,
                        'MAE': np.nan,
                        'MAPE (%)': np.nan,
                        'R²': np.nan,
                        'Dir. Acc. (%)': np.nan
                    }

            metrics_df = pd.DataFrame(metrics).T

            # Sort metrics by RMSE (ascending)
            metrics_df = metrics_df.sort_values('RMSE')

            return comparison_df, metrics_df

        return comparison_df, None

    def plot_model_comparison(self, data: pd.DataFrame, **kwargs):
        """
        Plot a comprehensive comparison of predictions from all models and ensemble methods.

        Args:
            data (pd.DataFrame): The input data
            **kwargs: Additional parameters
                - figsize (tuple): Figure size
                - ensemble_methods (list): List of ensemble methods to compare
                - include_uncertainty (bool): Include uncertainty bands
                - plot_type (str): Type of plot ('predictions', 'errors', 'metrics', 'all')
                - highlight_ensemble (bool): Highlight ensemble predictions

        Returns:
            tuple: (matplotlib.figure.Figure, pd.DataFrame) - The comparison plot and metrics
        """
        # Get parameters
        figsize = kwargs.get('figsize', (12, 10))
        ensemble_methods = kwargs.get('ensemble_methods',
                                     ['simple_average', 'weighted_average', 'performance_weighted'])
        include_uncertainty = kwargs.get('include_uncertainty', True)
        plot_type = kwargs.get('plot_type', 'all')
        highlight_ensemble = kwargs.get('highlight_ensemble', True)

        try:
            # Get comparison data with all ensemble methods
            comparison_df, metrics_df = self.get_model_comparison(
                data,
                ensemble_methods=ensemble_methods,
                include_uncertainty=include_uncertainty
            )

            # Check if we have valid data to plot
            if comparison_df.empty:
                logger.error("No valid data for plotting model comparison")
                fig, ax = plt.subplots(figsize=figsize)
                ax.text(0.5, 0.5, "No valid prediction data available",
                         horizontalalignment='center', verticalalignment='center',
                         transform=ax.transAxes, fontsize=14)
                return fig, None

            # Determine number of subplots based on plot_type
            if plot_type == 'all':
                fig, axes = plt.subplots(3, 1, figsize=figsize)
                plot_predictions = True
                plot_errors = True
                plot_metrics = True
            elif plot_type == 'predictions':
                fig, axes = plt.subplots(1, 1, figsize=figsize)
                axes = [axes]
                plot_predictions = True
                plot_errors = False
                plot_metrics = False
            elif plot_type == 'errors':
                fig, axes = plt.subplots(1, 1, figsize=figsize)
                axes = [axes]
                plot_predictions = False
                plot_errors = True
                plot_metrics = False
            elif plot_type == 'metrics':
                fig, axes = plt.subplots(1, 1, figsize=figsize)
                axes = [axes]
                plot_predictions = False
                plot_errors = False
                plot_metrics = True
            else:
                # Default to predictions and errors
                fig, axes = plt.subplots(2, 1, figsize=figsize)
                plot_predictions = True
                plot_errors = True
                plot_metrics = False

            # Set Seaborn style if available
            if SEABORN_AVAILABLE:
                sns.set_style("whitegrid")

            # Plot predictions
            ax_index = 0
            if plot_predictions:
                ax = axes[ax_index]
                ax_index += 1

                # First plot actual values if available
                if 'Actual' in comparison_df.columns:
                    ax.plot(comparison_df.index, comparison_df['Actual'],
                           label='Actual', linewidth=2, color='black')

                # Plot base model predictions
                base_models = [col for col in comparison_df.columns
                              if col not in ['Actual'] and
                              'Ensemble' not in col and
                              not col.endswith('_Error') and
                              not col.endswith('_AbsError') and
                              not col.endswith('_PctError') and
                              not col.endswith('Lower') and
                              not col.endswith('Upper')]

                for column in base_models:
                    ax.plot(comparison_df.index, comparison_df[column],
                           label=column, alpha=0.7, linewidth=1)

                # Plot ensemble predictions with thicker lines
                ensemble_cols = [col for col in comparison_df.columns
                               if 'Ensemble' in col and
                               not col.endswith('Lower') and
                               not col.endswith('Upper')]

                for column in ensemble_cols:
                    if highlight_ensemble:
                        ax.plot(comparison_df.index, comparison_df[column],
                               label=column, linewidth=2)

                        # Add uncertainty bands if available
                        if include_uncertainty:
                            lower_col = f"{column} Lower"
                            upper_col = f"{column} Upper"
                            if lower_col in comparison_df.columns and upper_col in comparison_df.columns:
                                ax.fill_between(comparison_df.index,
                                              comparison_df[lower_col],
                                              comparison_df[upper_col],
                                              alpha=0.2)
                    else:
                        ax.plot(comparison_df.index, comparison_df[column],
                               label=column, linewidth=1.5)

                ax.set_title('Model Predictions Comparison')
                ax.set_xlabel('Time')
                ax.set_ylabel('Price')
                ax.legend(loc='best')
                ax.grid(True)

            # Plot errors
            if plot_errors and 'Actual' in comparison_df.columns:
                ax = axes[ax_index]
                ax_index += 1

                # Get error columns (skip percent errors)
                error_columns = [col for col in comparison_df.columns
                                if col.endswith('_Error') and
                                not col.endswith('_AbsError') and
                                not col.endswith('_PctError')]

                if error_columns:
                    # Plot base model errors
                    base_errors = [col for col in error_columns if 'Ensemble' not in col]
                    for column in base_errors:
                        model_name = column.replace('_Error', '')
                        ax.plot(comparison_df.index, comparison_df[column],
                               label=f'{model_name}', alpha=0.7, linewidth=1)

                    # Plot ensemble errors with thicker lines
                    ensemble_errors = [col for col in error_columns if 'Ensemble' in col]
                    for column in ensemble_errors:
                        model_name = column.replace('_Error', '')
                        if highlight_ensemble:
                            ax.plot(comparison_df.index, comparison_df[column],
                                   label=f'{model_name}', linewidth=2)
                        else:
                            ax.plot(comparison_df.index, comparison_df[column],
                                   label=f'{model_name}', linewidth=1.5)

                    ax.set_title('Prediction Errors')
                    ax.set_xlabel('Time')
                    ax.set_ylabel('Error')
                    ax.legend(loc='best')
                    ax.grid(True)

                    # Add zero line
                    ax.axhline(y=0, color='r', linestyle='-', alpha=0.3)
                else:
                    ax.text(0.5, 0.5, "No error data available",
                             horizontalalignment='center', verticalalignment='center',
                             transform=ax.transAxes, fontsize=12)

            # Plot performance metrics
            if plot_metrics and metrics_df is not None and not metrics_df.empty:
                ax = axes[ax_index]

                # Create a bar chart of RMSE values
                if SEABORN_AVAILABLE:
                    sns.barplot(x=metrics_df.index, y='RMSE', data=metrics_df, ax=ax)
                else:
                    metrics_df['RMSE'].plot(kind='bar', ax=ax)

                ax.set_title('Model Performance Comparison (RMSE)')
                ax.set_xlabel('Model')
                ax.set_ylabel('RMSE (lower is better)')
                ax.tick_params(axis='x', rotation=45)

                # Add value labels on top of bars
                for i, v in enumerate(metrics_df['RMSE']):
                    ax.text(i, v + 0.1, f"{v:.3f}", ha='center')

            plt.tight_layout()
            return fig, metrics_df
        except Exception as e:
            logger.error(f"Error plotting model comparison: {str(e)}")
            fig, ax = plt.subplots(figsize=figsize)
            ax.text(0.5, 0.5, f"Error plotting model comparison: {str(e)}",
                     horizontalalignment='center', verticalalignment='center',
                     transform=ax.transAxes, fontsize=14)
            return fig, None
