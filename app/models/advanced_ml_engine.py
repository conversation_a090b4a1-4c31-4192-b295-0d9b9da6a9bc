"""
Advanced Machine Learning Engine for Stock Prediction
Enhanced with Transformer models, Ensemble learning, and Real-time retraining
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import joblib
import os
from pathlib import Path

# ML Libraries
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, VotingRegressor
    from sklearn.linear_model import LinearRegression, Ridge, Lasso
    from sklearn.svm import SVR
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    import xgboost as xgb
    import lightgbm as lgb
except ImportError as e:
    logging.warning(f"Some ML libraries not available: {e}")

# Deep Learning Libraries
try:
    import torch
    import torch.nn as nn
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("PyTorch not available - some features will be limited")

# Configure logging
logger = logging.getLogger(__name__)

class AdvancedMLEngine:
    """Advanced Machine Learning Engine with multiple models and ensemble learning"""
    
    def __init__(self, model_dir: str = "models/ml_cache"):
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        # Model registry
        self.models = {}
        self.scalers = {}
        self.performance_metrics = {}
        
        # Ensemble configuration
        self.ensemble_weights = {}
        self.confidence_thresholds = {
            'high': 0.8,
            'medium': 0.6,
            'low': 0.4
        }
        
        # Feature engineering parameters
        self.feature_windows = [5, 10, 20, 50]
        self.technical_indicators = [
            'sma', 'ema', 'rsi', 'macd', 'bollinger', 'atr', 'stochastic'
        ]
        
        logger.info("Advanced ML Engine initialized")
    
    def create_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create advanced features for ML models"""
        try:
            features_df = df.copy()
            
            # Price-based features
            features_df['returns'] = features_df['Close'].pct_change()
            features_df['log_returns'] = np.log(features_df['Close'] / features_df['Close'].shift(1))
            features_df['volatility'] = features_df['returns'].rolling(window=20).std()
            
            # Technical indicators with multiple timeframes
            for window in self.feature_windows:
                # Moving averages
                features_df[f'sma_{window}'] = features_df['Close'].rolling(window=window).mean()
                features_df[f'ema_{window}'] = features_df['Close'].ewm(span=window).mean()
                
                # Price ratios
                features_df[f'price_sma_ratio_{window}'] = features_df['Close'] / features_df[f'sma_{window}']
                features_df[f'price_ema_ratio_{window}'] = features_df['Close'] / features_df[f'ema_{window}']
                
                # Momentum indicators
                features_df[f'momentum_{window}'] = features_df['Close'] / features_df['Close'].shift(window)
                features_df[f'roc_{window}'] = features_df['Close'].pct_change(window)
            
            # Advanced technical indicators
            features_df = self._add_advanced_indicators(features_df)
            
            # Market microstructure features
            features_df['high_low_ratio'] = features_df['High'] / features_df['Low']
            features_df['open_close_ratio'] = features_df['Open'] / features_df['Close']
            features_df['volume_price_trend'] = features_df['Volume'] * features_df['returns']
            
            # Time-based features
            features_df['day_of_week'] = pd.to_datetime(features_df['Date']).dt.dayofweek
            features_df['month'] = pd.to_datetime(features_df['Date']).dt.month
            features_df['quarter'] = pd.to_datetime(features_df['Date']).dt.quarter
            
            # Lag features
            for lag in [1, 2, 3, 5, 10]:
                features_df[f'close_lag_{lag}'] = features_df['Close'].shift(lag)
                features_df[f'volume_lag_{lag}'] = features_df['Volume'].shift(lag)
                features_df[f'returns_lag_{lag}'] = features_df['returns'].shift(lag)
            
            # Rolling statistics
            for window in [5, 10, 20]:
                features_df[f'close_mean_{window}'] = features_df['Close'].rolling(window=window).mean()
                features_df[f'close_std_{window}'] = features_df['Close'].rolling(window=window).std()
                features_df[f'volume_mean_{window}'] = features_df['Volume'].rolling(window=window).mean()
                features_df[f'returns_mean_{window}'] = features_df['returns'].rolling(window=window).mean()
            
            # Remove infinite and NaN values
            features_df = features_df.replace([np.inf, -np.inf], np.nan)
            features_df = features_df.fillna(method='ffill').fillna(method='bfill')
            
            logger.info(f"Created {len(features_df.columns)} features for ML models")
            return features_df
            
        except Exception as e:
            logger.error(f"Error creating advanced features: {str(e)}")
            return df
    
    def _add_advanced_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add advanced technical indicators"""
        try:
            # RSI with multiple periods
            for period in [14, 21, 30]:
                delta = df['Close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
            
            # MACD with different parameters
            for fast, slow, signal in [(12, 26, 9), (8, 21, 5)]:
                ema_fast = df['Close'].ewm(span=fast).mean()
                ema_slow = df['Close'].ewm(span=slow).mean()
                macd = ema_fast - ema_slow
                macd_signal = macd.ewm(span=signal).mean()
                df[f'macd_{fast}_{slow}'] = macd
                df[f'macd_signal_{fast}_{slow}'] = macd_signal
                df[f'macd_histogram_{fast}_{slow}'] = macd - macd_signal
            
            # Bollinger Bands
            for window in [20, 50]:
                sma = df['Close'].rolling(window=window).mean()
                std = df['Close'].rolling(window=window).std()
                df[f'bb_upper_{window}'] = sma + (std * 2)
                df[f'bb_lower_{window}'] = sma - (std * 2)
                df[f'bb_width_{window}'] = df[f'bb_upper_{window}'] - df[f'bb_lower_{window}']
                df[f'bb_position_{window}'] = (df['Close'] - df[f'bb_lower_{window}']) / df[f'bb_width_{window}']
            
            # Stochastic Oscillator
            for k_period, d_period in [(14, 3), (21, 5)]:
                low_min = df['Low'].rolling(window=k_period).min()
                high_max = df['High'].rolling(window=k_period).max()
                k_percent = 100 * ((df['Close'] - low_min) / (high_max - low_min))
                df[f'stoch_k_{k_period}'] = k_percent
                df[f'stoch_d_{k_period}'] = k_percent.rolling(window=d_period).mean()
            
            # Average True Range (ATR)
            for period in [14, 21]:
                high_low = df['High'] - df['Low']
                high_close = np.abs(df['High'] - df['Close'].shift())
                low_close = np.abs(df['Low'] - df['Close'].shift())
                true_range = np.maximum(high_low, np.maximum(high_close, low_close))
                df[f'atr_{period}'] = true_range.rolling(window=period).mean()
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding advanced indicators: {str(e)}")
            return df
    
    def prepare_training_data(self, df: pd.DataFrame, target_horizons: List[int] = [1, 5, 10, 20]) -> Dict:
        """Prepare training data for multiple prediction horizons"""
        try:
            # Create features
            features_df = self.create_advanced_features(df)
            
            # Select feature columns (exclude non-feature columns)
            exclude_cols = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
            feature_cols = [col for col in features_df.columns if col not in exclude_cols]
            
            training_data = {}
            
            for horizon in target_horizons:
                # Create target variable (future price)
                target = features_df['Close'].shift(-horizon)
                
                # Align features and targets
                X = features_df[feature_cols].iloc[:-horizon]
                y = target.iloc[:-horizon]
                
                # Remove NaN values
                mask = ~(X.isnull().any(axis=1) | y.isnull())
                X_clean = X[mask]
                y_clean = y[mask]
                
                if len(X_clean) > 50:  # Minimum data requirement
                    training_data[horizon] = {
                        'X': X_clean,
                        'y': y_clean,
                        'feature_names': feature_cols
                    }
                    logger.info(f"Prepared {len(X_clean)} samples for {horizon}-day horizon")
                else:
                    logger.warning(f"Insufficient data for {horizon}-day horizon")
            
            return training_data

        except Exception as e:
            logger.error(f"Error preparing training data: {str(e)}")
            return {}

    def create_ensemble_models(self) -> Dict:
        """Create ensemble of different ML models"""
        try:
            models = {
                # Tree-based models
                'random_forest': RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42,
                    n_jobs=-1
                ),
                'gradient_boosting': GradientBoostingRegressor(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42
                ),
                'xgboost': xgb.XGBRegressor(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42,
                    n_jobs=-1
                ),
                'lightgbm': lgb.LGBMRegressor(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42,
                    n_jobs=-1,
                    verbose=-1
                ),

                # Linear models
                'linear_regression': LinearRegression(),
                'ridge': Ridge(alpha=1.0),
                'lasso': Lasso(alpha=0.1),

                # Support Vector Machine
                'svr': SVR(kernel='rbf', C=1.0, gamma='scale')
            }

            logger.info(f"Created {len(models)} base models for ensemble")
            return models

        except Exception as e:
            logger.error(f"Error creating ensemble models: {str(e)}")
            return {}

    def train_ensemble_models(self, training_data: Dict, symbol: str) -> Dict:
        """Train ensemble models for all horizons"""
        try:
            results = {}
            base_models = self.create_ensemble_models()

            for horizon, data in training_data.items():
                logger.info(f"Training models for {horizon}-day horizon")

                X, y = data['X'], data['y']
                feature_names = data['feature_names']

                # Split data for time series (last 20% for validation)
                split_idx = int(len(X) * 0.8)
                X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
                y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]

                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_val_scaled = scaler.transform(X_val)

                # Train individual models
                trained_models = {}
                model_scores = {}

                for model_name, model in base_models.items():
                    try:
                        # Train model
                        model.fit(X_train_scaled, y_train)

                        # Validate model
                        y_pred = model.predict(X_val_scaled)
                        score = r2_score(y_val, y_pred)
                        mse = mean_squared_error(y_val, y_pred)
                        mae = mean_absolute_error(y_val, y_pred)

                        trained_models[model_name] = model
                        model_scores[model_name] = {
                            'r2_score': score,
                            'mse': mse,
                            'mae': mae,
                            'rmse': np.sqrt(mse)
                        }

                        logger.info(f"{model_name} - R²: {score:.4f}, RMSE: {np.sqrt(mse):.4f}")

                    except Exception as e:
                        logger.warning(f"Failed to train {model_name}: {str(e)}")

                # Create weighted ensemble
                ensemble_weights = self._calculate_ensemble_weights(model_scores)

                # Save models and metadata
                model_data = {
                    'models': trained_models,
                    'scaler': scaler,
                    'scores': model_scores,
                    'weights': ensemble_weights,
                    'feature_names': feature_names,
                    'training_date': datetime.now(),
                    'data_points': len(X_train)
                }

                # Save to disk
                model_path = self.model_dir / f"{symbol}_horizon_{horizon}.joblib"
                joblib.dump(model_data, model_path)

                results[horizon] = model_data
                logger.info(f"Saved models for {horizon}-day horizon to {model_path}")

            return results

        except Exception as e:
            logger.error(f"Error training ensemble models: {str(e)}")
            return {}

    def _calculate_ensemble_weights(self, model_scores: Dict) -> Dict:
        """Calculate weights for ensemble based on model performance"""
        try:
            weights = {}

            # Use R² scores for weighting (higher is better)
            r2_scores = {name: scores['r2_score'] for name, scores in model_scores.items()}

            # Convert negative R² to small positive values
            min_score = min(r2_scores.values())
            if min_score < 0:
                adjusted_scores = {name: score - min_score + 0.01 for name, score in r2_scores.items()}
            else:
                adjusted_scores = r2_scores

            # Calculate softmax weights
            total_score = sum(adjusted_scores.values())
            if total_score > 0:
                weights = {name: score / total_score for name, score in adjusted_scores.items()}
            else:
                # Equal weights if all models perform poorly
                num_models = len(model_scores)
                weights = {name: 1.0 / num_models for name in model_scores.keys()}

            logger.info(f"Ensemble weights: {weights}")
            return weights

        except Exception as e:
            logger.error(f"Error calculating ensemble weights: {str(e)}")
            return {}

    def predict_with_ensemble(self, df: pd.DataFrame, symbol: str, horizons: List[int] = [1, 5, 10, 20]) -> Dict:
        """Make predictions using ensemble models"""
        try:
            predictions = {}

            # Create features for prediction
            features_df = self.create_advanced_features(df)

            for horizon in horizons:
                model_path = self.model_dir / f"{symbol}_horizon_{horizon}.joblib"

                if not model_path.exists():
                    logger.warning(f"No trained model found for {symbol} horizon {horizon}")
                    continue

                # Load model data
                model_data = joblib.load(model_path)
                models = model_data['models']
                scaler = model_data['scaler']
                weights = model_data['weights']
                feature_names = model_data['feature_names']

                # Prepare features for prediction
                latest_features = features_df[feature_names].iloc[-1:].fillna(method='ffill')
                latest_features_scaled = scaler.transform(latest_features)

                # Get predictions from all models
                model_predictions = {}
                for model_name, model in models.items():
                    try:
                        pred = model.predict(latest_features_scaled)[0]
                        model_predictions[model_name] = pred
                    except Exception as e:
                        logger.warning(f"Prediction failed for {model_name}: {str(e)}")

                # Calculate ensemble prediction
                if model_predictions:
                    ensemble_pred = sum(pred * weights.get(name, 0) for name, pred in model_predictions.items())

                    # Calculate prediction confidence
                    pred_std = np.std(list(model_predictions.values()))
                    confidence = self._calculate_prediction_confidence(pred_std, model_data['scores'])

                    predictions[horizon] = {
                        'ensemble_prediction': ensemble_pred,
                        'individual_predictions': model_predictions,
                        'confidence': confidence,
                        'prediction_std': pred_std,
                        'model_count': len(model_predictions),
                        'horizon_days': horizon
                    }

                    logger.info(f"Ensemble prediction for {horizon} days: {ensemble_pred:.2f} (confidence: {confidence:.2f})")

            return predictions

        except Exception as e:
            logger.error(f"Error making ensemble predictions: {str(e)}")
            return {}

    def _calculate_prediction_confidence(self, prediction_std: float, model_scores: Dict) -> float:
        """Calculate confidence score for predictions"""
        try:
            # Base confidence from model agreement (lower std = higher confidence)
            agreement_confidence = 1.0 / (1.0 + prediction_std)

            # Average model performance
            avg_r2 = np.mean([scores['r2_score'] for scores in model_scores.values()])
            performance_confidence = max(0, avg_r2)

            # Combined confidence
            confidence = (agreement_confidence * 0.6) + (performance_confidence * 0.4)

            return min(max(confidence, 0.0), 1.0)  # Clamp between 0 and 1

        except Exception as e:
            logger.error(f"Error calculating prediction confidence: {str(e)}")
            return 0.5

    def retrain_models_if_needed(self, df: pd.DataFrame, symbol: str, force_retrain: bool = False) -> bool:
        """Check if models need retraining and retrain if necessary"""
        try:
            retrain_needed = force_retrain

            # Check each horizon model
            for horizon in [1, 5, 10, 20]:
                model_path = self.model_dir / f"{symbol}_horizon_{horizon}.joblib"

                if not model_path.exists():
                    retrain_needed = True
                    break

                # Load model metadata
                model_data = joblib.load(model_path)
                training_date = model_data.get('training_date', datetime.min)

                # Check if model is too old (retrain weekly)
                days_since_training = (datetime.now() - training_date).days
                if days_since_training > 7:
                    logger.info(f"Model for {symbol} horizon {horizon} is {days_since_training} days old - retraining needed")
                    retrain_needed = True
                    break

                # Check model performance degradation
                if self._check_model_performance_degradation(df, model_data, horizon):
                    logger.info(f"Performance degradation detected for {symbol} horizon {horizon}")
                    retrain_needed = True
                    break

            if retrain_needed:
                logger.info(f"Retraining models for {symbol}")
                training_data = self.prepare_training_data(df)
                if training_data:
                    self.train_ensemble_models(training_data, symbol)
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking/retraining models: {str(e)}")
            return False

    def _check_model_performance_degradation(self, df: pd.DataFrame, model_data: Dict, horizon: int) -> bool:
        """Check if model performance has degraded significantly"""
        try:
            # Get recent data for validation
            recent_data = df.tail(50)  # Last 50 days
            if len(recent_data) < horizon + 10:
                return False

            # Prepare validation data
            features_df = self.create_advanced_features(recent_data)
            feature_names = model_data['feature_names']

            # Get actual vs predicted for recent period
            X_recent = features_df[feature_names].iloc[:-horizon]
            y_actual = recent_data['Close'].iloc[horizon:]

            if len(X_recent) < 10:  # Need minimum data
                return False

            # Make predictions
            scaler = model_data['scaler']
            models = model_data['models']
            weights = model_data['weights']

            X_scaled = scaler.transform(X_recent.fillna(method='ffill'))

            # Ensemble prediction
            ensemble_preds = []
            for i in range(len(X_scaled)):
                model_preds = {}
                for name, model in models.items():
                    try:
                        pred = model.predict(X_scaled[i:i+1])[0]
                        model_preds[name] = pred
                    except:
                        continue

                if model_preds:
                    ensemble_pred = sum(pred * weights.get(name, 0) for name, pred in model_preds.items())
                    ensemble_preds.append(ensemble_pred)

            if len(ensemble_preds) < 5:
                return False

            # Calculate recent performance
            recent_mse = mean_squared_error(y_actual[:len(ensemble_preds)], ensemble_preds)
            recent_r2 = r2_score(y_actual[:len(ensemble_preds)], ensemble_preds)

            # Compare with training performance
            training_scores = model_data['scores']
            avg_training_r2 = np.mean([scores['r2_score'] for scores in training_scores.values()])

            # Performance degradation threshold
            performance_drop = avg_training_r2 - recent_r2
            if performance_drop > 0.2:  # 20% drop in R²
                logger.warning(f"Performance degradation detected: R² dropped by {performance_drop:.3f}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking performance degradation: {str(e)}")
            return False

    def get_model_performance_report(self, symbol: str) -> Dict:
        """Generate comprehensive model performance report"""
        try:
            report = {
                'symbol': symbol,
                'report_date': datetime.now(),
                'horizons': {}
            }

            for horizon in [1, 5, 10, 20]:
                model_path = self.model_dir / f"{symbol}_horizon_{horizon}.joblib"

                if model_path.exists():
                    model_data = joblib.load(model_path)

                    horizon_report = {
                        'training_date': model_data.get('training_date'),
                        'data_points': model_data.get('data_points'),
                        'model_scores': model_data.get('scores', {}),
                        'ensemble_weights': model_data.get('weights', {}),
                        'feature_count': len(model_data.get('feature_names', [])),
                        'model_age_days': (datetime.now() - model_data.get('training_date', datetime.now())).days
                    }

                    # Calculate average performance metrics
                    scores = model_data.get('scores', {})
                    if scores:
                        avg_r2 = np.mean([s['r2_score'] for s in scores.values()])
                        avg_rmse = np.mean([s['rmse'] for s in scores.values()])
                        best_model = max(scores.keys(), key=lambda k: scores[k]['r2_score'])

                        horizon_report.update({
                            'average_r2': avg_r2,
                            'average_rmse': avg_rmse,
                            'best_model': best_model,
                            'best_model_r2': scores[best_model]['r2_score']
                        })

                    report['horizons'][horizon] = horizon_report

            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {str(e)}")
            return {}

    def get_feature_importance(self, symbol: str, horizon: int = 5) -> Dict:
        """Get feature importance from trained models"""
        try:
            model_path = self.model_dir / f"{symbol}_horizon_{horizon}.joblib"

            if not model_path.exists():
                return {}

            model_data = joblib.load(model_path)
            models = model_data['models']
            feature_names = model_data['feature_names']

            # Get feature importance from tree-based models
            importance_data = {}

            for model_name, model in models.items():
                if hasattr(model, 'feature_importances_'):
                    importance_data[model_name] = dict(zip(feature_names, model.feature_importances_))
                elif hasattr(model, 'coef_'):
                    # For linear models, use absolute coefficients
                    importance_data[model_name] = dict(zip(feature_names, np.abs(model.coef_)))

            # Calculate average importance across models
            if importance_data:
                avg_importance = {}
                for feature in feature_names:
                    importances = [data.get(feature, 0) for data in importance_data.values()]
                    avg_importance[feature] = np.mean(importances)

                # Sort by importance
                sorted_importance = dict(sorted(avg_importance.items(), key=lambda x: x[1], reverse=True))

                return {
                    'average_importance': sorted_importance,
                    'model_importance': importance_data,
                    'top_10_features': list(sorted_importance.keys())[:10]
                }

            return {}

        except Exception as e:
            logger.error(f"Error getting feature importance: {str(e)}")
            return {}
