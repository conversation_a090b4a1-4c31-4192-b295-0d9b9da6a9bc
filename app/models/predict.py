"""
Enhanced prediction module for the AI Stocks Bot application.

This module provides functions for making predictions using various models,
with adaptive model selection based on performance metrics.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

# Configure logging
logger = logging.getLogger(__name__)

# Import performance tracking
from app.utils.performance import track_performance
from app.utils.caching import cached
from app.models.adaptive import select_best_model, get_ensemble_weights
from app.models.performance import track_prediction

# Import the original prediction function
from models.predict import predict_future_prices as original_predict_future_prices
from models.predict import prepare_prediction_data

@track_performance("enhanced_prediction")
def predict_future_prices(df: pd.DataFrame, symbol: str,
                         horizons: List[int] = [1, 5, 15, 30, 60],
                         model_type: str = 'auto',
                         sequence_length: int = 60,
                         models_path: str = 'saved_models',
                         use_adaptive: bool = True,
                         use_caching: bool = True) -> Dict[int, float]:
    """
    Enhanced prediction function with adaptive model selection and performance tracking.

    Args:
        df: DataFrame with stock data
        symbol: Stock symbol
        horizons: List of prediction horizons in minutes
        model_type: Type of model ('auto', 'ensemble', 'lstm', 'rf', 'gb', etc.)
        sequence_length: Number of time steps to look back
        models_path: Path where models are saved
        use_adaptive: Whether to use adaptive model selection
        use_caching: Whether to use caching for predictions

    Returns:
        Dictionary with predictions for each horizon
    """
    # Start timing
    start_time = datetime.now()

    # Dictionary to store predictions
    predictions = {}

    # Check if DataFrame is empty
    if df is None or len(df) == 0:
        logger.warning(f"Empty DataFrame provided for {symbol}. Using fallback predictions.")
        # Return fallback predictions
        return {horizon: 0.0 for horizon in horizons}

    # Check if DataFrame has required columns
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        logger.warning(f"Missing columns in DataFrame for {symbol}: {missing_columns}. Using fallback predictions.")
        # Return fallback predictions
        return {horizon: 0.0 for horizon in horizons}

    # Check if DataFrame has enough rows
    if len(df) < sequence_length:
        logger.warning(f"Not enough data for {symbol}. Need at least {sequence_length} rows, but got {len(df)}. Using fallback predictions.")
        # Return fallback predictions
        return {horizon: 0.0 for horizon in horizons}

    # If model_type is 'auto', use adaptive model selection
    if model_type.lower() == 'auto' and use_adaptive:
        logger.info(f"Using adaptive model selection for {symbol}")

        # Make predictions for each horizon with the best model
        for horizon in horizons:
            # Select the best model for this horizon
            best_model = select_best_model(symbol, horizon)
            logger.info(f"Selected best model for {symbol} with {horizon}min horizon: {best_model}")

            # Make prediction with the best model
            horizon_predictions = _predict_with_model(df, symbol, [horizon], best_model,
                                                     sequence_length, models_path, use_caching)

            # Add to predictions
            if horizon in horizon_predictions:
                predictions[horizon] = horizon_predictions[horizon]

    # If model_type is 'ensemble' and use_adaptive is True, use adaptive ensemble
    elif model_type.lower() == 'ensemble' and use_adaptive:
        logger.info(f"Using adaptive ensemble for {symbol}")

        # Make predictions for each horizon
        for horizon in horizons:
            # Get model weights for this horizon
            weights = get_ensemble_weights(symbol, horizon)
            logger.info(f"Ensemble weights for {symbol} with {horizon}min horizon: {weights}")

            # Make predictions with each model
            ensemble_predictions = {}

            for model_type, weight in weights.items():
                # Skip if weight is 0
                if weight <= 0:
                    continue

                # Make prediction with this model
                model_predictions = _predict_with_model(df, symbol, [horizon], model_type,
                                                      sequence_length, models_path, use_caching)

                # Add to ensemble predictions
                if horizon in model_predictions:
                    ensemble_predictions[model_type] = model_predictions[horizon]

            # Calculate weighted average
            if ensemble_predictions:
                weighted_sum = 0
                total_weight = 0

                for model_type, prediction in ensemble_predictions.items():
                    weighted_sum += prediction * weights[model_type]
                    total_weight += weights[model_type]

                # Add to predictions
                if total_weight > 0:
                    predictions[horizon] = weighted_sum / total_weight
                    logger.info(f"Adaptive ensemble prediction for {horizon}min horizon: {predictions[horizon]}")
            else:
                # Fallback to original ensemble if no models have predictions
                logger.warning(f"No models have predictions for {horizon}min horizon, using original ensemble")
                horizon_predictions = _predict_with_model(df, symbol, [horizon], 'ensemble',
                                                        sequence_length, models_path, use_caching)

                if horizon in horizon_predictions:
                    predictions[horizon] = horizon_predictions[horizon]

    # Otherwise, use the specified model type
    else:
        logger.info(f"Using specified model type: {model_type}")
        predictions = _predict_with_model(df, symbol, horizons, model_type,
                                         sequence_length, models_path, use_caching)

    # Track performance
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    logger.info(f"Prediction completed in {duration:.2f} seconds")

    return predictions

@cached()
def _predict_with_model(df: pd.DataFrame, symbol: str, horizons: List[int],
                       model_type: str, sequence_length: int,
                       models_path: str, use_caching: bool) -> Dict[int, float]:
    """
    Make predictions with a specific model.

    Args:
        df: DataFrame with stock data
        symbol: Stock symbol
        horizons: List of prediction horizons in minutes
        model_type: Type of model
        sequence_length: Number of time steps to look back
        models_path: Path where models are saved
        use_caching: Whether to use caching

    Returns:
        Dictionary with predictions for each horizon
    """
    # If caching is disabled, use the original function directly
    if not use_caching:
        return original_predict_future_prices(df, symbol, horizons, sequence_length, model_type, models_path)

    # Use the original function with caching
    return original_predict_future_prices(df, symbol, horizons, sequence_length, model_type, models_path)

def track_prediction_accuracy(symbol: str, model_type: str, horizon: int,
                             predicted_value: float, actual_value: float,
                             prediction_time: Optional[datetime] = None) -> None:
    """
    Track the accuracy of a prediction.

    Args:
        symbol: Stock symbol
        model_type: Type of model
        horizon: Prediction horizon in minutes
        predicted_value: Predicted price
        actual_value: Actual price
        prediction_time: When the prediction was made
    """
    # Track the prediction
    track_prediction(symbol, model_type, horizon, predicted_value, actual_value, prediction_time)

    # Log the accuracy
    absolute_error = abs(predicted_value - actual_value)
    percentage_error = absolute_error / actual_value * 100 if actual_value != 0 else float('inf')

    logger.info(f"Prediction accuracy for {symbol} using {model_type} with {horizon}min horizon:")
    logger.info(f"  Predicted: {predicted_value:.4f}, Actual: {actual_value:.4f}")
    logger.info(f"  Absolute Error: {absolute_error:.4f}, Percentage Error: {percentage_error:.2f}%")
