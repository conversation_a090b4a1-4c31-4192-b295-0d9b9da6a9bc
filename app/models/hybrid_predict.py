"""
Hybrid prediction module that combines multiple forecasting techniques.

This module implements a hybrid approach that combines traditional ML models,
deep learning models, and statistical time series models to generate more
accurate predictions.
"""
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Union, Tuple, Any
from datetime import datetime, timedelta
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

# Configure logging
logger = logging.getLogger(__name__)

# Import prediction modules
from models.predict import predict_future_prices as original_predict
from app.models.predict import predict_future_prices as enhanced_predict
from app.utils.performance import track_performance

# Import advanced time series models
try:
    from app.models.advanced_time_series import (
        ARIMAForecaster, AutoARIMAForecaster, ProphetForecaster,
        forecast_with_prophet, STATSMODELS_AVAILABLE, PROPHET_AVAILABLE, AUTO_ARIMA_AVAILABLE
    )
    ADVANCED_TS_AVAILABLE = True
except ImportError:
    logger.warning("Advanced time series models not available.")
    ADVANCED_TS_AVAILABLE = False

# Import trend analysis
try:
    from app.utils.trend_analysis import (
        detect_trend, calculate_momentum, identify_support_resistance,
        TREND_ANALYSIS_AVAILABLE
    )
except ImportError:
    logger.warning("Trend analysis module not available.")
    TREND_ANALYSIS_AVAILABLE = False

    # Define fallback functions
    def detect_trend(df, window=20):
        """Fallback trend detection"""
        return "neutral", 0.0

    def calculate_momentum(df, window=14):
        """Fallback momentum calculation"""
        return 0.0

    def identify_support_resistance(df, window=20):
        """Fallback support/resistance identification"""
        last_price = df['Close'].iloc[-1]
        return last_price * 0.98, last_price * 1.02  # Simple 2% bands


@track_performance("hybrid_prediction")
def hybrid_predict_future_prices(
    df: pd.DataFrame,
    symbol: str,
    horizons: List[int] = [1, 5, 15, 30, 60],
    sequence_length: int = 60,
    models_path: str = 'saved_models',
    use_statistical: bool = True,
    use_ml: bool = True,
    use_deep_learning: bool = True,
    use_trend_analysis: bool = True,
    blend_method: str = 'weighted'
) -> Dict[int, float]:
    """
    Generate predictions using a hybrid approach that combines multiple techniques.

    Args:
        df (pd.DataFrame): DataFrame with stock data
        symbol (str): Stock symbol
        horizons (List[int]): List of prediction horizons in minutes
        sequence_length (int): Number of time steps to look back
        models_path (str): Path where models are saved
        use_statistical (bool): Whether to use statistical models
        use_ml (bool): Whether to use machine learning models
        use_deep_learning (bool): Whether to use deep learning models
        use_trend_analysis (bool): Whether to use trend analysis
        blend_method (str): Method to blend predictions ('simple', 'weighted', 'adaptive')

    Returns:
        Dict[int, float]: Dictionary with predictions for each horizon
    """
    # Dictionary to store predictions
    predictions = {}
    ml_predictions = {}
    dl_predictions = {}
    stat_predictions = {}
    trend_adjusted_predictions = {}

    try:
        # 1. Get predictions from ML models if enabled
        if use_ml:
            try:
                # Use RandomForest and GradientBoosting
                ml_predictions = enhanced_predict(
                    df, symbol, horizons,
                    model_type='ensemble',  # Use ensemble of ML models
                    sequence_length=sequence_length,
                    models_path=models_path,
                    use_adaptive=True
                )
                logger.info(f"ML predictions generated for {symbol}")
            except Exception as e:
                logger.error(f"Error generating ML predictions: {str(e)}")

        # 2. Get predictions from deep learning models if enabled
        if use_deep_learning:
            try:
                # Use LSTM and BiLSTM
                dl_models = ['lstm', 'bilstm']
                dl_predictions_list = []

                for model in dl_models:
                    try:
                        model_preds = enhanced_predict(
                            df, symbol, horizons,
                            model_type=model,
                            sequence_length=sequence_length,
                            models_path=models_path
                        )
                        dl_predictions_list.append(model_preds)
                    except Exception as e:
                        logger.error(f"Error with {model} model: {str(e)}")

                # Average the DL predictions
                if dl_predictions_list:
                    dl_predictions = {}
                    for horizon in horizons:
                        values = [preds.get(horizon, 0) for preds in dl_predictions_list if horizon in preds]
                        if values:
                            dl_predictions[horizon] = sum(values) / len(values)

                    logger.info(f"DL predictions generated for {symbol}")
            except Exception as e:
                logger.error(f"Error generating DL predictions: {str(e)}")

        # 3. Get predictions from statistical models if enabled
        if use_statistical and ADVANCED_TS_AVAILABLE:
            try:
                stat_predictions = {}

                # Use Prophet for each horizon
                if PROPHET_AVAILABLE:
                    for horizon in horizons:
                        try:
                            pred = forecast_with_prophet(df, horizon, target_column='Close')
                            stat_predictions[horizon] = pred
                        except Exception as e:
                            logger.error(f"Error with Prophet for horizon {horizon}: {str(e)}")

                logger.info(f"Statistical predictions generated for {symbol}")
            except Exception as e:
                logger.error(f"Error generating statistical predictions: {str(e)}")

        # 4. Apply trend analysis adjustments if enabled
        if use_trend_analysis:
            try:
                # Detect current trend
                trend_direction, trend_strength = detect_trend(df)

                # Calculate momentum
                momentum = calculate_momentum(df)

                # Identify support and resistance levels
                support, resistance = identify_support_resistance(df)

                # Get the last price
                last_price = df['Close'].iloc[-1]

                # Adjust predictions based on trend analysis
                for horizon in horizons:
                    # Base prediction (average of available predictions)
                    available_preds = []
                    if horizon in ml_predictions:
                        available_preds.append(ml_predictions[horizon])
                    if horizon in dl_predictions:
                        available_preds.append(dl_predictions[horizon])
                    if horizon in stat_predictions:
                        available_preds.append(stat_predictions[horizon])

                    if available_preds:
                        base_pred = sum(available_preds) / len(available_preds)

                        # Convert horizon to a fraction of a day for scaling
                        horizon_days = horizon / (24 * 60)  # Convert minutes to days

                        # Apply trend adjustment
                        if trend_direction == "up":
                            # Upward trend - apply positive adjustment scaled by horizon and strength
                            adjustment = trend_strength * momentum * horizon_days
                            adjusted_pred = base_pred * (1 + adjustment)
                        elif trend_direction == "down":
                            # Downward trend - apply negative adjustment scaled by horizon and strength
                            adjustment = trend_strength * momentum * horizon_days
                            adjusted_pred = base_pred * (1 - adjustment)
                        else:
                            # Neutral trend - small random adjustment
                            adjustment = (np.random.random() * 0.01 - 0.005) * horizon_days
                            adjusted_pred = base_pred * (1 + adjustment)

                        # Respect support and resistance levels
                        if adjusted_pred > resistance:
                            # If prediction is above resistance, pull it back down
                            adjusted_pred = resistance - (adjusted_pred - resistance) * 0.5
                        elif adjusted_pred < support:
                            # If prediction is below support, pull it back up
                            adjusted_pred = support + (support - adjusted_pred) * 0.5

                        trend_adjusted_predictions[horizon] = adjusted_pred

                logger.info(f"Trend-adjusted predictions generated for {symbol}")
            except Exception as e:
                logger.error(f"Error applying trend analysis: {str(e)}")

        # 5. Blend predictions using the specified method
        for horizon in horizons:
            available_preds = {}
            if horizon in ml_predictions:
                available_preds['ml'] = ml_predictions[horizon]
            if horizon in dl_predictions:
                available_preds['dl'] = dl_predictions[horizon]
            if horizon in stat_predictions:
                available_preds['stat'] = stat_predictions[horizon]
            if horizon in trend_adjusted_predictions:
                available_preds['trend'] = trend_adjusted_predictions[horizon]

            # Get the last price for sanity checking
            last_price = df['Close'].iloc[-1]

            if available_preds:
                if blend_method == 'simple':
                    # Simple average
                    raw_prediction = sum(available_preds.values()) / len(available_preds)

                elif blend_method == 'weighted':
                    # Weighted average with predefined weights
                    weights = {
                        'ml': 0.3,
                        'dl': 0.3,
                        'stat': 0.2,
                        'trend': 0.2
                    }

                    weighted_sum = 0
                    total_weight = 0

                    for source, pred in available_preds.items():
                        weight = weights.get(source, 0)
                        weighted_sum += pred * weight
                        total_weight += weight

                    if total_weight > 0:
                        raw_prediction = weighted_sum / total_weight
                    else:
                        raw_prediction = sum(available_preds.values()) / len(available_preds)

                else:
                    # Default to simple average
                    raw_prediction = sum(available_preds.values()) / len(available_preds)

                # Apply sanity check to ensure prediction is within reasonable bounds
                # Limit predictions to a maximum of 20% change from last price
                max_change_ratio = 0.20  # 20% maximum change

                # Calculate upper and lower bounds
                upper_bound = last_price * (1 + max_change_ratio)
                lower_bound = last_price * (1 - max_change_ratio)

                # Ensure prediction is within bounds
                if raw_prediction > upper_bound:
                    logger.warning(f"Prediction for {symbol} at horizon {horizon} was capped: {raw_prediction:.2f} -> {upper_bound:.2f}")
                    predictions[horizon] = upper_bound
                elif raw_prediction < lower_bound:
                    logger.warning(f"Prediction for {symbol} at horizon {horizon} was floored: {raw_prediction:.2f} -> {lower_bound:.2f}")
                    predictions[horizon] = lower_bound
                else:
                    predictions[horizon] = raw_prediction
            else:
                # No predictions available, use the original prediction function
                try:
                    original_preds = original_predict(
                        df, symbol, [horizon],
                        sequence_length=sequence_length,
                        model_type='lstm',
                        models_path=models_path
                    )

                    if horizon in original_preds:
                        predictions[horizon] = original_preds[horizon]
                    else:
                        # Last resort: use the last known price
                        predictions[horizon] = df['Close'].iloc[-1]
                except Exception as e:
                    logger.error(f"Error with original prediction: {str(e)}")
                    # Use the last known price
                    predictions[horizon] = df['Close'].iloc[-1]
    except Exception as e:
        logger.error(f"Global error in hybrid prediction: {str(e)}")
        # Fallback to last price for all horizons
        for horizon in horizons:
            try:
                predictions[horizon] = df['Close'].iloc[-1]
            except:
                predictions[horizon] = 0.0

    return predictions
