"""
Advanced time series forecasting techniques for the AI Stocks Bot.

This module implements sophisticated time series forecasting methods
that can be used alongside the existing models to improve prediction accuracy.
"""
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Union, Tuple, Any
from datetime import datetime, timedelta
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")

# Configure logging
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.statespace.sarimax import SARIMAX
    STATSMODELS_AVAILABLE = True
except ImportError:
    logger.warning("statsmodels not available. ARIMA and SARIMA models will not be available.")
    STATSMODELS_AVAILABLE = False

try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    logger.warning("Prophet not available. Prophet model will not be available.")
    PROPHET_AVAILABLE = False

try:
    from pmdarima import auto_arima
    AUTO_ARIMA_AVAILABLE = True
except (ImportError, ValueError) as e:
    logger.warning(f"pmdarima not available due to {type(e).__name__}: {str(e)}. Auto ARIMA will not be available.")
    AUTO_ARIMA_AVAILABLE = False
    auto_arima = None


class ARIMAForecaster:
    """
    ARIMA (AutoRegressive Integrated Moving Average) forecasting model.
    """
    def __init__(self, p=1, d=1, q=0):
        """
        Initialize the ARIMA model.
        
        Args:
            p (int): Order of the autoregressive model (AR)
            d (int): Degree of differencing (I)
            q (int): Order of the moving average model (MA)
        """
        if not STATSMODELS_AVAILABLE:
            raise ImportError("statsmodels is required for ARIMA forecasting")
        
        self.p = p
        self.d = d
        self.q = q
        self.model = None
        self.fitted_model = None
    
    def fit(self, series: pd.Series):
        """
        Fit the ARIMA model to the time series data.
        
        Args:
            series (pd.Series): Time series data to fit
        """
        try:
            self.model = ARIMA(series, order=(self.p, self.d, self.q))
            self.fitted_model = self.model.fit()
            return True
        except Exception as e:
            logger.error(f"Error fitting ARIMA model: {str(e)}")
            return False
    
    def predict(self, steps: int) -> np.ndarray:
        """
        Generate forecasts for future time steps.
        
        Args:
            steps (int): Number of steps to forecast
            
        Returns:
            np.ndarray: Forecasted values
        """
        if self.fitted_model is None:
            raise ValueError("Model must be fitted before making predictions")
        
        try:
            forecast = self.fitted_model.forecast(steps=steps)
            return forecast
        except Exception as e:
            logger.error(f"Error forecasting with ARIMA model: {str(e)}")
            return np.array([np.nan] * steps)


class AutoARIMAForecaster:
    """
    Auto ARIMA forecasting model that automatically selects the best parameters.
    """
    def __init__(self, seasonal=False, m=None):
        """
        Initialize the Auto ARIMA model.
        
        Args:
            seasonal (bool): Whether to include seasonal components
            m (int): The seasonal period (if seasonal=True)
        """
        if not AUTO_ARIMA_AVAILABLE:
            raise ImportError("pmdarima is required for Auto ARIMA forecasting")
        
        self.seasonal = seasonal
        self.m = m
        self.model = None
    
    def fit(self, series: pd.Series):
        """
        Fit the Auto ARIMA model to the time series data.
        
        Args:
            series (pd.Series): Time series data to fit
        """
        try:
            # Use auto_arima to find the best parameters
            self.model = auto_arima(
                series,
                seasonal=self.seasonal,
                m=self.m,
                suppress_warnings=True,
                error_action="ignore",
                stepwise=True
            )
            return True
        except Exception as e:
            logger.error(f"Error fitting Auto ARIMA model: {str(e)}")
            return False
    
    def predict(self, steps: int) -> np.ndarray:
        """
        Generate forecasts for future time steps.
        
        Args:
            steps (int): Number of steps to forecast
            
        Returns:
            np.ndarray: Forecasted values
        """
        if self.model is None:
            raise ValueError("Model must be fitted before making predictions")
        
        try:
            forecast, conf_int = self.model.predict(n_periods=steps, return_conf_int=True)
            return forecast
        except Exception as e:
            logger.error(f"Error forecasting with Auto ARIMA model: {str(e)}")
            return np.array([np.nan] * steps)


class ProphetForecaster:
    """
    Facebook Prophet forecasting model.
    """
    def __init__(self, changepoint_prior_scale=0.05, seasonality_mode='multiplicative'):
        """
        Initialize the Prophet model.
        
        Args:
            changepoint_prior_scale (float): Flexibility of the trend
            seasonality_mode (str): 'additive' or 'multiplicative'
        """
        if not PROPHET_AVAILABLE:
            raise ImportError("Prophet is required for Prophet forecasting")
        
        self.model = Prophet(
            changepoint_prior_scale=changepoint_prior_scale,
            seasonality_mode=seasonality_mode
        )
    
    def fit(self, df: pd.DataFrame):
        """
        Fit the Prophet model to the time series data.
        
        Args:
            df (pd.DataFrame): DataFrame with 'ds' (dates) and 'y' (values) columns
        """
        try:
            self.model.fit(df)
            return True
        except Exception as e:
            logger.error(f"Error fitting Prophet model: {str(e)}")
            return False
    
    def predict(self, future_df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate forecasts for future dates.
        
        Args:
            future_df (pd.DataFrame): DataFrame with 'ds' column for future dates
            
        Returns:
            pd.DataFrame: Forecasted values
        """
        if self.model is None:
            raise ValueError("Model must be fitted before making predictions")
        
        try:
            forecast = self.model.predict(future_df)
            return forecast
        except Exception as e:
            logger.error(f"Error forecasting with Prophet model: {str(e)}")
            return pd.DataFrame()


def prepare_data_for_prophet(df: pd.DataFrame, target_column: str = 'Close') -> pd.DataFrame:
    """
    Prepare data for Prophet model.
    
    Args:
        df (pd.DataFrame): Input DataFrame with 'Date' and target column
        target_column (str): Column to predict
        
    Returns:
        pd.DataFrame: DataFrame with 'ds' and 'y' columns for Prophet
    """
    prophet_df = pd.DataFrame()
    prophet_df['ds'] = df['Date']
    prophet_df['y'] = df[target_column]
    return prophet_df


def forecast_with_prophet(df: pd.DataFrame, horizon: int, target_column: str = 'Close') -> float:
    """
    Generate forecast using Prophet model.
    
    Args:
        df (pd.DataFrame): Input DataFrame with 'Date' and target column
        horizon (int): Forecast horizon in minutes
        target_column (str): Column to predict
        
    Returns:
        float: Forecasted value
    """
    if not PROPHET_AVAILABLE:
        logger.warning("Prophet not available. Using fallback prediction.")
        return df[target_column].iloc[-1]  # Return last value as fallback
    
    try:
        # Prepare data for Prophet
        prophet_df = prepare_data_for_prophet(df, target_column)
        
        # Create and fit model
        model = ProphetForecaster()
        success = model.fit(prophet_df)
        
        if not success:
            logger.warning("Failed to fit Prophet model. Using fallback prediction.")
            return df[target_column].iloc[-1]
        
        # Create future dataframe
        last_date = df['Date'].iloc[-1]
        future_date = last_date + timedelta(minutes=horizon)
        future_df = pd.DataFrame({'ds': [future_date]})
        
        # Make prediction
        forecast = model.predict(future_df)
        
        # Return forecasted value
        return forecast['yhat'].iloc[0]
    
    except Exception as e:
        logger.error(f"Error forecasting with Prophet: {str(e)}")
        return df[target_column].iloc[-1]  # Return last value as fallback
