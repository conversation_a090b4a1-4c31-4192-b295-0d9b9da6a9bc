import numpy as np
import pandas as pd
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
import logging
import joblib
import os
from typing import Dict, Any, Tuple

from app.models.base_model import BaseModel

logger = logging.getLogger(__name__)

class SVRModel(BaseModel):
    """
    Support Vector Regression model for time series prediction.
    """
    
    def __init__(self, target_column='Close', **kwargs):
        """
        Initialize the SVR model.
        
        Args:
            target_column (str): The column to predict (default: 'Close')
            **kwargs: Additional model parameters
        """
        super().__init__(target_column=target_column)
        self.model_name = "SVR"
        
        # Model parameters
        self.kernel = kwargs.get('kernel', 'rbf')
        self.C = kwargs.get('C', 1.0)
        self.epsilon = kwargs.get('epsilon', 0.1)
        self.gamma = kwargs.get('gamma', 'scale')
        
        # Scalers
        self.feature_scaler = StandardScaler()
        self.target_scaler = StandardScaler()
        
        # Model
        self.model = SVR(
            kernel=self.kernel,
            C=self.C,
            epsilon=self.epsilon,
            gamma=self.gamma
        )
    
    def preprocess_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Preprocess the data for the SVR model.
        
        Args:
            data (pd.DataFrame): The input data
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: Processed features and target
        """
        # Make a copy to avoid modifying the original data
        df = data.copy()
        
        # Extract target
        target = df[[self.target_column]]
        
        # Create features
        # Use a subset of features to avoid dimensionality issues with SVR
        price_cols = ['Open', 'High', 'Low', 'Close']
        features = df[price_cols]
        
        # Add some basic technical indicators
        if 'Close' in features.columns:
            # Moving averages
            features['MA_5'] = df['Close'].rolling(window=5).mean()
            features['MA_10'] = df['Close'].rolling(window=10).mean()
            
            # Price momentum
            features['Return_1d'] = df['Close'].pct_change(1)
        
        # Fill NaN values
        features = features.fillna(method='bfill').fillna(method='ffill')
        
        return features, target
    
    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        Train the SVR model.
        
        Args:
            data (pd.DataFrame): The training data
            **kwargs: Additional training parameters
            
        Returns:
            Dict[str, Any]: Training metrics
        """
        logger.info("Training SVR model")
        
        # Override default parameters if provided
        self.kernel = kwargs.get('kernel', self.kernel)
        self.C = kwargs.get('C', self.C)
        self.epsilon = kwargs.get('epsilon', self.epsilon)
        self.gamma = kwargs.get('gamma', self.gamma)
        
        # Update model with new parameters
        self.model = SVR(
            kernel=self.kernel,
            C=self.C,
            epsilon=self.epsilon,
            gamma=self.gamma
        )
        
        # Preprocess data
        features, target = self.preprocess_data(data)
        
        # Scale features and target
        X = self.feature_scaler.fit_transform(features)
        y = self.target_scaler.fit_transform(target).ravel()
        
        # Train model
        self.model.fit(X, y)
        
        # Return training metrics
        return {
            'params': {
                'kernel': self.kernel,
                'C': self.C,
                'epsilon': self.epsilon,
                'gamma': self.gamma
            }
        }
    
    def predict(self, data: pd.DataFrame) -> np.ndarray:
        """
        Generate predictions using the SVR model.
        
        Args:
            data (pd.DataFrame): The input data
            
        Returns:
            np.ndarray: Predicted values
        """
        # Preprocess data
        features, _ = self.preprocess_data(data)
        
        # Scale features
        X = self.feature_scaler.transform(features)
        
        # Generate predictions
        scaled_predictions = self.model.predict(X)
        
        # Inverse transform predictions
        predictions = self.target_scaler.inverse_transform(scaled_predictions.reshape(-1, 1)).flatten()
        
        return predictions
    
    def save(self, path: str) -> None:
        """
        Save the SVR model to disk.
        
        Args:
            path (str): The path to save the model
        """
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Save model and scalers
        model_data = {
            'model': self.model,
            'feature_scaler': self.feature_scaler,
            'target_scaler': self.target_scaler,
            'params': {
                'kernel': self.kernel,
                'C': self.C,
                'epsilon': self.epsilon,
                'gamma': self.gamma,
                'target_column': self.target_column
            }
        }
        
        joblib.dump(model_data, path)
        logger.info(f"SVR model saved to {path}")
    
    def load(self, path: str) -> None:
        """
        Load the SVR model from disk.
        
        Args:
            path (str): The path to load the model from
        """
        # Load model and scalers
        model_data = joblib.load(path)
        
        self.model = model_data['model']
        self.feature_scaler = model_data['feature_scaler']
        self.target_scaler = model_data['target_scaler']
        
        # Load parameters
        params = model_data.get('params', {})
        self.kernel = params.get('kernel', self.kernel)
        self.C = params.get('C', self.C)
        self.epsilon = params.get('epsilon', self.epsilon)
        self.gamma = params.get('gamma', self.gamma)
        self.target_column = params.get('target_column', self.target_column)
        
        logger.info(f"SVR model loaded from {path}")
