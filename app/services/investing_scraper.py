"""
Investing.com scraper for Egyptian market news.
"""

import requests
from bs4 import BeautifulSoup
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import time
import random

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InvestingScraper:
    """
    Scraper for Investing.com Egyptian market news.
    """
    
    def __init__(self):
        """
        Initialize the scraper with default headers.
        """
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.8,image/webp,image/apng,*/*;q=0.8',
            'Connection': 'keep-alive',
            'Referer': 'https://www.investing.com/'
        }
        self.base_url = "https://www.investing.com"
    
    def get_egypt_market_news(self, page_size: int = 10) -> List[Dict[str, Any]]:
        """
        Get news from the Egypt market page.
        
        Args:
            page_size (int): Number of news items to return
            
        Returns:
            List[Dict[str, Any]]: List of news articles
        """
        try:
            url = f"{self.base_url}/markets/egypt"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code != 200:
                logger.error(f"Failed to fetch Egypt market news: {response.status_code}")
                return []
            
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = []
            
            # Find news articles on the page
            articles = soup.select('.articleItem')
            
            for article in articles[:page_size]:
                try:
                    # Extract article details
                    title_elem = article.select_one('.title a')
                    if not title_elem:
                        continue
                        
                    title = title_elem.text.strip()
                    article_url = self.base_url + title_elem['href'] if title_elem.has_attr('href') else None
                    
                    # Extract date if available
                    date_elem = article.select_one('.date')
                    published_at = date_elem.text.strip() if date_elem else "Unknown date"
                    
                    # Extract description if available
                    desc_elem = article.select_one('.articleContent')
                    description = desc_elem.text.strip() if desc_elem else ""
                    
                    # Create article object
                    article_obj = {
                        "title": title,
                        "description": description,
                        "url": article_url,
                        "publishedAt": published_at,
                        "source": {
                            "id": "investing-com",
                            "name": "Investing.com"
                        }
                    }
                    
                    news_items.append(article_obj)
                    
                except Exception as e:
                    logger.error(f"Error parsing article: {str(e)}")
                    continue
            
            return news_items
            
        except Exception as e:
            logger.error(f"Error in get_egypt_market_news: {str(e)}")
            return []
    
    def search_stock_news(self, symbol: str, page_size: int = 10) -> List[Dict[str, Any]]:
        """
        Search for news about a specific Egyptian stock.
        
        Args:
            symbol (str): Stock symbol or name to search for
            page_size (int): Number of news items to return
            
        Returns:
            List[Dict[str, Any]]: List of news articles
        """
        try:
            # Map common Egyptian stock symbols to their names on investing.com
            stock_mapping = {
                "COMI": "Commercial International Bank Egypt",
                "HRHO": "EFG Hermes",
                "TMGH": "Talaat Moustafa Group",
                "SWDY": "Elsewedy Electric",
                "EAST": "Eastern Company"
            }
            
            # Get the search term based on the symbol
            search_term = stock_mapping.get(symbol.upper(), symbol)
            
            # URL encode the search term
            search_term = requests.utils.quote(search_term)
            
            # Search URL
            url = f"{self.base_url}/search/?q={search_term}&tab=news"
            
            response = requests.get(url, headers=self.headers)
            
            if response.status_code != 200:
                logger.error(f"Failed to search for {symbol} news: {response.status_code}")
                return []
            
            soup = BeautifulSoup(response.text, 'html.parser')
            news_items = []
            
            # Find news articles in search results
            articles = soup.select('.js-article-item')
            
            for article in articles[:page_size]:
                try:
                    # Extract article details
                    title_elem = article.select_one('.title a')
                    if not title_elem:
                        continue
                        
                    title = title_elem.text.strip()
                    article_url = self.base_url + title_elem['href'] if title_elem.has_attr('href') else None
                    
                    # Extract date if available
                    date_elem = article.select_one('.date')
                    published_at = date_elem.text.strip() if date_elem else "Unknown date"
                    
                    # Extract description if available
                    desc_elem = article.select_one('.textDiv')
                    description = desc_elem.text.strip() if desc_elem else ""
                    
                    # Create article object
                    article_obj = {
                        "title": title,
                        "description": description,
                        "url": article_url,
                        "publishedAt": published_at,
                        "source": {
                            "id": "investing-com",
                            "name": "Investing.com"
                        }
                    }
                    
                    news_items.append(article_obj)
                    
                except Exception as e:
                    logger.error(f"Error parsing article: {str(e)}")
                    continue
            
            return news_items
            
        except Exception as e:
            logger.error(f"Error in search_stock_news: {str(e)}")
            return []
