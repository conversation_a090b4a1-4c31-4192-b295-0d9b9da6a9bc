"""
News Service

This module provides functionality to fetch news articles from NewsAPI and other sources.
"""

import os
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from app.services.investing_scraper import InvestingScraper

# Configure logging
logger = logging.getLogger(__name__)

class NewsService:
    """
    Service for fetching news articles from NewsAPI.
    """

    # NewsAPI base URL
    BASE_URL = "https://newsapi.org/v2"

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the NewsService.

        Args:
            api_key (Optional[str]): NewsAPI API key. If None, will try to get from environment variable.
        """
        self.api_key = api_key or os.environ.get("NEWS_API_KEY", "598f92f09cc44eff927f1563199bc52b")
        self.investing_scraper = InvestingScraper()

    def get_stock_news(self, symbol: str, days: int = 7, language: str = "en",
                       sort_by: str = "publishedAt", page_size: int = 10,
                       market: str = "egypt", specific_sites: bool = True) -> List[Dict[str, Any]]:
        """
        Get news articles for a specific stock symbol.

        Args:
            symbol (str): Stock symbol to search for
            days (int): Number of days to look back
            language (str): Language of news articles
            sort_by (str): Sorting method (relevancy, popularity, publishedAt)
            page_size (int): Number of articles to return
            market (str): Stock market to focus on (egypt, global)
            specific_sites (bool): Whether to limit search to specific financial sites

        Returns:
            List[Dict[str, Any]]: List of news articles
        """
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Format dates for API
            from_date = start_date.strftime("%Y-%m-%d")
            to_date = end_date.strftime("%Y-%m-%d")

            # Define domains with good Egyptian market coverage
            egyptian_market_domains = [
                "investing.com",
                "reuters.com",
                "bloomberg.com",
                "zawya.com",
                "dailynewsegypt.com",
                "enterprise.press",
                "mubasher.info",
                "egypttoday.com",
                "ahram.org.eg",
                "madamasr.com"
            ]

            # Build query based on market
            if market.lower() == "egypt":
                # For Egyptian market, use advanced search syntax
                # Try different variations of the stock symbol and Egyptian market terms
                if symbol == "COMI":
                    # Special handling for Commercial International Bank (CIB)
                    query = f'("Commercial International Bank" OR CIB OR COMI) AND (Egypt OR Egyptian OR "Cairo Stock" OR EGX)'
                elif symbol == "HRHO":
                    # Special handling for Hermes
                    query = f'("EFG Hermes" OR HRHO OR Hermes) AND (Egypt OR Egyptian OR "Cairo Stock" OR EGX)'
                elif symbol == "TMGH":
                    # Special handling for Talaat Moustafa Group
                    query = f'("Talaat Moustafa" OR TMG OR TMGH) AND (Egypt OR Egyptian OR "Cairo Stock" OR EGX)'
                elif symbol == "SWDY":
                    # Special handling for Elsewedy Electric
                    query = f'("Elsewedy Electric" OR SWDY OR Elsewedy) AND (Egypt OR Egyptian OR "Cairo Stock" OR EGX)'
                elif symbol == "EAST":
                    # Special handling for Eastern Company
                    query = f'("Eastern Company" OR EAST OR "Eastern Tobacco") AND (Egypt OR Egyptian OR "Cairo Stock" OR EGX)'
                else:
                    # Generic handling for other Egyptian stocks
                    query = f'"{symbol}" AND (Egypt OR Egyptian OR "Cairo Stock" OR EGX)'
            else:
                # For global market
                query = symbol

            # Prepare query parameters
            params = {
                "apiKey": self.api_key,
                "q": query,
                "language": language,
                "sortBy": sort_by,
                "pageSize": page_size,
                "from": from_date,
                "to": to_date
            }

            # Add domains parameter if specific sites are requested
            if specific_sites and market.lower() == "egypt":
                params["domains"] = ",".join(egyptian_market_domains)

            # Make API request
            response = requests.get(f"{self.BASE_URL}/everything", params=params)

            # Check if request was successful
            if response.status_code == 200:
                data = response.json()
                articles = data.get("articles", [])
                logger.info(f"Retrieved {len(articles)} news articles for {symbol} in {market} market from NewsAPI")

                # If we're looking for Egyptian market news and got no results, try the investing.com scraper
                if market.lower() == "egypt" and len(articles) == 0:
                    logger.info(f"No results from NewsAPI, trying investing.com scraper for {symbol}")
                    investing_articles = self.investing_scraper.search_stock_news(symbol, page_size)
                    if investing_articles:
                        logger.info(f"Retrieved {len(investing_articles)} news articles for {symbol} from investing.com")
                        return investing_articles

                return articles
            else:
                logger.error(f"Error fetching news: {response.status_code} - {response.text}")

                # If NewsAPI request failed and we're looking for Egyptian market news, try the investing.com scraper
                if market.lower() == "egypt":
                    logger.info(f"NewsAPI request failed, trying investing.com scraper for {symbol}")
                    investing_articles = self.investing_scraper.search_stock_news(symbol, page_size)
                    if investing_articles:
                        logger.info(f"Retrieved {len(investing_articles)} news articles for {symbol} from investing.com")
                        return investing_articles

                return []

        except Exception as e:
            logger.error(f"Error in get_stock_news: {str(e)}")
            return []

    def get_market_news(self, category: str = "business", country: str = "eg",
                        page_size: int = 10, market: str = "egypt",
                        specific_sites: bool = True) -> List[Dict[str, Any]]:
        """
        Get top headlines for a specific category and country.

        Args:
            category (str): News category (business, technology, etc.)
            country (str): Country code (eg, us, gb, etc.)
            page_size (int): Number of articles to return
            market (str): Stock market to focus on (egypt, global)
            specific_sites (bool): Whether to limit search to specific financial sites

        Returns:
            List[Dict[str, Any]]: List of news articles
        """
        try:
            # Define domains with good Egyptian market coverage
            egyptian_market_domains = [
                "investing.com",
                "reuters.com",
                "bloomberg.com",
                "zawya.com",
                "dailynewsegypt.com",
                "enterprise.press",
                "mubasher.info",
                "egypttoday.com",
                "ahram.org.eg",
                "madamasr.com"
            ]

            # For Egyptian market, we need to use a different approach since NewsAPI
            # doesn't have great coverage for Egypt in top-headlines
            if market.lower() == "egypt" or country.lower() == "eg":
                # Use everything endpoint with Egypt-specific query
                # Calculate date range for a wider search
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)  # Look back 30 days for Egyptian market news

                # Format dates for API
                from_date = start_date.strftime("%Y-%m-%d")
                to_date = end_date.strftime("%Y-%m-%d")

                # Advanced query for Egyptian market news
                egyptian_market_query = '("Egyptian Stock Exchange" OR "Cairo Stock Exchange" OR EGX OR "Egyptian market" OR "Egypt stocks" OR "Egypt market" OR "Egypt economy") AND (finance OR economy OR stocks OR trading OR investment)'

                params = {
                    "apiKey": self.api_key,
                    "q": egyptian_market_query,
                    "sortBy": "publishedAt",
                    "pageSize": page_size,
                    "language": "en",
                    "from": from_date,
                    "to": to_date
                }

                # Add domains parameter if specific sites are requested
                if specific_sites:
                    params["domains"] = ",".join(egyptian_market_domains)

                # Make API request
                response = requests.get(f"{self.BASE_URL}/everything", params=params)
            else:
                # Use top-headlines for other countries
                params = {
                    "apiKey": self.api_key,
                    "category": category,
                    "country": country,
                    "pageSize": page_size
                }

                # Make API request
                response = requests.get(f"{self.BASE_URL}/top-headlines", params=params)

            # Check if request was successful
            if response.status_code == 200:
                data = response.json()
                articles = data.get("articles", [])
                logger.info(f"Retrieved {len(articles)} market news articles for {market} market from NewsAPI")

                # If we're looking for Egyptian market news and got no results, try the investing.com scraper
                if (market.lower() == "egypt" or country.lower() == "eg") and len(articles) == 0:
                    logger.info("No results from NewsAPI, trying investing.com scraper for Egyptian market news")
                    investing_articles = self.investing_scraper.get_egypt_market_news(page_size)
                    if investing_articles:
                        logger.info(f"Retrieved {len(investing_articles)} market news articles from investing.com")
                        return investing_articles

                return articles
            else:
                logger.error(f"Error fetching market news: {response.status_code} - {response.text}")

                # If NewsAPI request failed and we're looking for Egyptian market news, try the investing.com scraper
                if market.lower() == "egypt" or country.lower() == "eg":
                    logger.info("NewsAPI request failed, trying investing.com scraper for Egyptian market news")
                    investing_articles = self.investing_scraper.get_egypt_market_news(page_size)
                    if investing_articles:
                        logger.info(f"Retrieved {len(investing_articles)} market news articles from investing.com")
                        return investing_articles

                return []

        except Exception as e:
            logger.error(f"Error in get_market_news: {str(e)}")
            return []

    def search_financial_news(self, query: str, days: int = 30,
                             language: str = "en", page_size: int = 10,
                             market: str = "egypt", specific_sites: bool = True) -> List[Dict[str, Any]]:
        """
        Search for financial news articles.

        Args:
            query (str): Search query
            days (int): Number of days to look back
            language (str): Language of news articles
            page_size (int): Number of articles to return
            market (str): Stock market to focus on (egypt, global)
            specific_sites (bool): Whether to limit search to specific financial sites

        Returns:
            List[Dict[str, Any]]: List of news articles
        """
        try:
            # Define domains with good Egyptian market coverage
            egyptian_market_domains = [
                "investing.com",
                "reuters.com",
                "bloomberg.com",
                "zawya.com",
                "dailynewsegypt.com",
                "enterprise.press",
                "mubasher.info",
                "egypttoday.com",
                "ahram.org.eg",
                "madamasr.com"
            ]

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Format dates for API
            from_date = start_date.strftime("%Y-%m-%d")
            to_date = end_date.strftime("%Y-%m-%d")

            # Build query based on market
            if market.lower() == "egypt":
                # For Egyptian market, use advanced search syntax
                # Increase search period for Egyptian market
                start_date = end_date - timedelta(days=max(days, 60))  # At least 60 days for Egyptian market
                from_date = start_date.strftime("%Y-%m-%d")

                # More specific financial and Egyptian terms
                financial_terms = '(finance OR financial OR economy OR economic OR "stock market" OR earnings OR report OR investment OR trading)'
                egyptian_terms = '(Egypt OR Egyptian OR EGX OR "Cairo Stock Exchange" OR "Egyptian Stock Exchange")'

                # Handle specific report types
                if "annual report" in query.lower() or "10-k" in query.lower():
                    search_query = f'({query} OR "annual report" OR "yearly results") AND {egyptian_terms}'
                elif "quarterly report" in query.lower() or "10-q" in query.lower():
                    search_query = f'({query} OR "quarterly report" OR "quarterly results") AND {egyptian_terms}'
                elif "earnings call" in query.lower() or "transcript" in query.lower():
                    search_query = f'({query} OR "earnings call" OR "earnings transcript" OR "financial results") AND {egyptian_terms}'
                else:
                    # Generic financial news search
                    search_query = f'{query} AND {financial_terms} AND {egyptian_terms}'
            else:
                # For global market
                search_query = f"{query} AND (finance OR financial OR economy OR economic OR market OR stock OR earnings OR report)"

            # Prepare query parameters
            params = {
                "apiKey": self.api_key,
                "q": search_query,
                "language": language,
                "sortBy": "relevancy",
                "pageSize": page_size,
                "from": from_date,
                "to": to_date
            }

            # Add domains parameter if specific sites are requested
            if specific_sites and market.lower() == "egypt":
                params["domains"] = ",".join(egyptian_market_domains)

            # Make API request
            response = requests.get(f"{self.BASE_URL}/everything", params=params)

            # Check if request was successful
            if response.status_code == 200:
                data = response.json()
                articles = data.get("articles", [])
                logger.info(f"Retrieved {len(articles)} financial news articles for query: {query} in {market} market")
                return articles
            else:
                logger.error(f"Error searching financial news: {response.status_code} - {response.text}")
                return []

        except Exception as e:
            logger.error(f"Error in search_financial_news: {str(e)}")
            return []
