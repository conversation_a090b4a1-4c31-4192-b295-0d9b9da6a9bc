import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Optional, Dict
import os
import sys
import logging
import configparser

# Import performance metrics tracking
try:
    from app.components.performance_metrics import track_prediction
except ImportError:
    # Define a dummy function if the module is not available
    def track_prediction(symbol, predictions, horizon_unit, model_type):
        pass

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from models.predict import predict_future_prices, predict_from_live_data
from app.models.model_factory import ModelFactory
from scrapers.price_scraper import PriceScraper
from app.utils.config_helpers import clean_config_value
from app.utils.data_processing import is_model_trained

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def safe_get_base_price(current_live_data, historical_data):
    """
    Safely get the base price from live data or historical data

    Args:
        current_live_data: Live data DataFrame (can be None)
        historical_data: Historical data DataFrame

    Returns:
        float: Base price for predictions
    """
    try:
        if current_live_data is not None and not current_live_data.empty and 'Close' in current_live_data.columns:
            return float(current_live_data['Close'].iloc[-1])
        elif historical_data is not None and not historical_data.empty and 'Close' in historical_data.columns:
            return float(historical_data['Close'].iloc[-1])
        else:
            raise ValueError("No valid Close price found in data")
    except Exception as e:
        logger.error(f"Error getting base price: {str(e)}")
        # Return a default price if all else fails
        return 100.0

def check_api_server_status():
    """Check if TradingView API server is running"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_price_from_api_server(symbol: str) -> Optional[Dict]:
    """Fetch price data from TradingView API server"""
    try:
        import requests
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                # Extract price from the API response
                raw_price = stock_data[0].get('price', 0)

                # Convert from piasters to EGP (divide by 1000)
                # API returns prices in piasters (81500 = 81.50 EGP)
                price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                return {
                    'symbol': symbol,
                    'price': price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'api_data': stock_data[0]  # Store full API data for advanced analysis
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching from API server: {str(e)}")
        return None

def safe_get_live_data_advanced(symbol: str) -> Optional[pd.DataFrame]:
    """Safely fetch live data with comprehensive error handling and API server integration"""
    try:
        from scrapers.price_scraper import PriceScraper
        from datetime import datetime

        with st.spinner("Fetching latest price data..."):
            # Check if API server is available
            api_status = check_api_server_status()

            if api_status:
                st.info("🔥 Using TradingView API server for enhanced data")
                price_data = fetch_price_from_api_server(symbol)
                source_used = "TradingView API"
            else:
                st.info("📡 Using direct TradingView scraping")
                scraper = PriceScraper(source="tradingview")
                try:
                    price_data = scraper.get_price(symbol)
                    source_used = "TradingView Direct"
                finally:
                    scraper.close_driver()

            if price_data and isinstance(price_data, dict):
                # Convert scraper data format to prediction-compatible format
                current_time = datetime.now()
                price = price_data.get('price', 0)

                # Create DataFrame with required columns for predictions
                converted_data = {
                    'Date': current_time,
                    'Open': price,
                    'High': price * 1.001,  # Simulate small high variation
                    'Low': price * 0.999,   # Simulate small low variation
                    'Close': price,         # Main price for predictions
                    'Volume': 1000000,      # Default volume
                    'symbol': price_data.get('symbol', symbol),
                    'currency': price_data.get('currency', 'EGP'),
                    'timestamp': price_data.get('timestamp', current_time.isoformat()),
                    'source': source_used,
                    'real_time': price_data.get('real_time', False)
                }

                df = pd.DataFrame([converted_data])

                # Ensure Date is datetime
                df['Date'] = pd.to_datetime(df['Date'])

                # Check if this is sample data
                is_sample = price_data.get('source', '').lower() == 'sample data'
                is_real_time = price_data.get('real_time', False)
                is_api = source_used == "TradingView API"

                if is_sample:
                    st.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                elif is_api:
                    st.success(f"✅ Price fetched from TradingView API: {price:.2f} EGP")
                    st.info("🔥 Enhanced data with technical analysis available")
                elif is_real_time:
                    st.success(f"🔴 Real-time price fetched: {price:.2f} EGP")
                else:
                    st.success(f"⏱️ Live price fetched: {price:.2f} EGP (15-min delay)")

                # Store in session state
                if 'live_data' not in st.session_state:
                    st.session_state.live_data = df
                else:
                    st.session_state.live_data = pd.concat([st.session_state.live_data, df], ignore_index=True)

                return df
            else:
                st.warning("⚠️ Could not fetch live price data")
                return None

    except Exception as e:
        st.warning(f"⚠️ Error fetching live data: {str(e)}")
        logger.error(f"Live data fetch error: {str(e)}")
        return None

def advanced_prediction_component(historical_data, live_data, symbol):
    """
    Advanced Streamlit component for displaying predictions with enhanced features

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        symbol (str): Stock symbol
    """
    st.subheader("Advanced Price Predictions")

    # Add a description of the advanced features
    st.markdown("""
    This advanced prediction page includes additional features:
    - Live data fetching for more accurate predictions
    - Ensemble approach to blend multiple models
    - Confidence scores for each prediction
    - Multiple prediction horizons (minutes, days, weeks)
    """)

    # Add a button to go to the Enhanced Ensemble Predictions page
    col1, col2 = st.columns([3, 1])
    with col2:
        if st.button("Try Enhanced Ensemble Methods", help="Go to the new Ensemble Predictions page with advanced ensemble techniques"):
            st.session_state.page = "Ensemble Predictions"
            st.rerun

    # Add explanation about horizon units
    st.info("📊 **Horizon Units**: All models are internally trained using minute-based horizons. When you select days or weeks, the app will look for models trained with the equivalent number of minutes (1 day = 1440 minutes, 1 week = 10080 minutes). Make sure you've trained models with appropriate horizons on the Train Model page.")

    if historical_data is None:
        st.warning("Please upload historical data first")
        return

    if symbol is None or symbol == "":
        st.warning("Please provide a stock symbol")
        return

    # Validate data structure and columns
    required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
    if not all(col in historical_data.columns for col in required_columns):
        missing_cols = [col for col in required_columns if col not in historical_data.columns]
        st.error(f"❌ **Data Error**: Missing required columns: {', '.join(missing_cols)}")
        st.markdown("**Your data must include these columns:**")
        for col in required_columns:
            status = "✅" if col in historical_data.columns else "❌"
            st.markdown(f"- {status} {col}")
        st.markdown("**Please re-upload your data with all required columns.**")
        return

    # Check if data has enough records
    if len(historical_data) < 60:
        st.error(f"❌ **Data Error**: Insufficient data. Found {len(historical_data)} records, need at least 60.")
        st.markdown("**Please upload more historical data for accurate predictions.**")
        return

    # Validate Close column data
    if historical_data['Close'].isnull().any():
        st.error("❌ **Data Error**: 'Close' column contains missing values.")
        st.markdown("**Please clean your data and ensure all Close prices are valid.**")
        return

    if (historical_data['Close'] <= 0).any():
        st.error("❌ **Data Error**: 'Close' column contains invalid prices (zero or negative).")
        st.markdown("**Please ensure all Close prices are positive values.**")
        return

    # Create two columns for the main controls
    col1, col2 = st.columns([1, 1])

    with col1:
        # Model type selection
        available_models = ModelFactory.get_available_models()
        model_type = st.selectbox(
            "Select primary model",
            options=list(available_models.keys()),
            index=0,
            format_func=lambda x: available_models[x],
            key="adv_model_type"
        )

        # Show model description
        st.info(ModelFactory.get_model_description(model_type))

    with col2:
        # Prediction timeframe selection
        horizon_options = [
            "Short-term (minutes)",
            "Medium-term (days)",
            "Long-term (weeks)"
        ]
        horizon_type = st.radio("Select prediction timeframe", horizon_options, key="adv_horizon_type")

        # Define horizons based on selected timeframe
        if horizon_type == "Short-term (minutes)":
            horizons_options = [
                [5, 15, 30, 60],  # Default - practical short-term options
                [4, 15, 30, 60],  # Include 4-minute option
                [5, 15, 30, 60, 120],  # Extended short-term
                [30, 60, 120, 240, 480]  # Longer horizons
            ]

            horizon_sets = [
                "Default (5, 15, 30, 60)",
                "Include 4-min (4, 15, 30, 60)",
                "Extended (5, 15, 30, 60, 120)",
                "Longer (30, 60, 120, 240, 480)"
            ]

            selected_horizon_set = st.selectbox(
                "Select prediction horizons (minutes)",
                options=horizon_sets,
                index=0,
                key="adv_horizon_set"
            )

            # Get the index of the selected horizon set
            horizon_index = horizon_sets.index(selected_horizon_set)

            # Get the selected horizons
            horizons = horizons_options[horizon_index]
            horizon_unit = "minutes"

            # For short-term, we'll use the existing single-point prediction
            num_points = 1

        elif horizon_type == "Medium-term (days)":
            horizons = [1, 2, 3, 5, 7]
            horizon_unit = "days"
            # For medium-term, we'll predict multiple points (one per day)
            num_points = st.slider("Number of prediction points", min_value=1, max_value=7, value=5, key="adv_num_points_days")
        else:  # Long-term
            horizons = [1, 2, 4, 8, 12]
            horizon_unit = "weeks"
            # For long-term, we'll predict multiple points (one per week)
            num_points = st.slider("Number of prediction points", min_value=1, max_value=12, value=4, key="adv_num_points_weeks")

    # Allow user to select specific horizons
    selected_horizons = st.multiselect(
        f"Select specific prediction horizons ({horizon_unit})",
        options=horizons,
        default=horizons,
        key="adv_selected_horizons"
    )

    if not selected_horizons:
        st.warning("Please select at least one prediction horizon")
        return

    # Store the prediction parameters in session state if possible
    try:
        st.session_state.horizon_unit = horizon_unit
        st.session_state.num_prediction_points = num_points
    except AttributeError:
        # If we can't store in session state, just continue
        pass

    # Check if models are trained for the selected horizons
    # Check if any models are trained
    models_trained = []
    models_not_trained = []

    # Convert horizons to minutes for checking trained models
    for horizon in selected_horizons:
        # Convert horizon to minutes for model training check
        if horizon_unit == "days":
            model_horizon = horizon * 24 * 60  # days to minutes
        elif horizon_unit == "weeks":
            model_horizon = horizon * 7 * 24 * 60  # weeks to minutes
        else:  # minutes
            model_horizon = horizon

        # Check if the model is trained - always use "minutes" as the horizon_unit
        # since all models are stored with minute-based horizons
        if is_model_trained(symbol, model_horizon, model_type.lower(), 'saved_models', "minutes"):
            models_trained.append(horizon)
        else:
            models_not_trained.append(horizon)

    if not models_trained:
        st.warning("No trained models found for the selected horizons. Please train models first.")
        st.info("Go to the 'Train Model' page to train models for the selected horizons.")

        # Add a button to go to the Train Model page
        if st.button("Go to Train Model page"):
            st.session_state.page = "Train Model"
        return
    elif models_not_trained:
        st.warning(f"Models not trained for horizons: {', '.join(map(str, models_not_trained))}")
        st.info("Only predictions for trained models will be generated.")

    # Enhanced features section
    st.subheader("Enhanced Features")

    # Option to use AI predictions
    use_ai_predictions = st.checkbox("Use AI-enhanced predictions", value=False,
                                   help="Use AI service to enhance predictions (if enabled in AI Services)",
                                   key="adv_use_ai")

    # Option to fetch fresh live data
    fetch_live_data = st.checkbox("Fetch fresh live data before prediction", value=True,
                                help="Fetch the latest price data from TradingView before making predictions",
                                key="adv_fetch_live")

    # Option to use ensemble approach (blend multiple models)
    use_ensemble = st.checkbox("Use ensemble approach", value=False,
                             help="Blend predictions from multiple models for potentially better accuracy",
                             key="adv_use_ensemble")

    # If ensemble is selected, let user choose additional models
    if use_ensemble:
        # Get available models excluding the currently selected one
        other_models = [m for m in list(available_models.keys()) if m != model_type]

        # Let user select additional models
        ensemble_models = st.multiselect(
            "Select additional models for ensemble",
            options=other_models,
            default=other_models[:2] if len(other_models) >= 2 else other_models,
            format_func=lambda x: available_models[x],
            key="adv_ensemble_models"
        )

        # Ensemble weights
        st.write("Adjust model weights:")
        primary_weight = st.slider(f"Weight for {available_models[model_type]}",
                                 min_value=0.1, max_value=1.0, value=0.6, step=0.1,
                                 key="adv_primary_weight")

        # Calculate remaining weight
        remaining_weight = 1.0 - primary_weight

        # If there are additional models, let user distribute the remaining weight
        if ensemble_models:
            st.info(f"Remaining weight ({remaining_weight:.1f}) will be distributed among selected models")
        else:
            st.warning("No additional models selected. Ensemble will use only the primary model.")

    # Make predictions
    if st.button("Generate Advanced Predictions", key="adv_generate_btn"):
        try:
            with st.spinner("Generating predictions..."):
                # Use only horizons with trained models
                trained_horizons = [h for h in selected_horizons if h in models_trained]

                # Convert horizons to minutes for prediction functions
                model_horizons = []
                horizon_conversion = {}  # Map original horizons to converted horizons

                for h in trained_horizons:
                    if horizon_unit == "days":
                        converted_horizon = h * 24 * 60  # days to minutes
                    elif horizon_unit == "weeks":
                        converted_horizon = h * 7 * 24 * 60  # weeks to minutes
                    else:  # minutes
                        converted_horizon = h

                    model_horizons.append(converted_horizon)
                    horizon_conversion[h] = converted_horizon

                # Fetch fresh live data if requested
                current_live_data = live_data
                if fetch_live_data:
                    # Use the modern API server approach (same as main predictions page)
                    current_live_data = safe_get_live_data_advanced(symbol)

                # Check if we should use AI predictions
                if use_ai_predictions and not use_ensemble:
                    # Import AI prediction module
                    from models.ai_predict import predict_with_ai_service

                    # Check if AI services are enabled
                    config_path = os.path.join("config", "ai_services.conf")
                    if os.path.exists(config_path):
                        config = configparser.ConfigParser()
                        config.read(config_path)

                        ai_enabled = config.getboolean("ai_services", "enabled", fallback=False)
                        provider = config.get("ai_services", "default_provider", fallback="none")

                        if ai_enabled and provider != "none":
                            st.info(f"Using AI service ({provider}) for predictions instead of {available_models[model_type]}")

                            # Create AI config
                            ai_config = {
                                "provider": clean_config_value(provider),
                                "config": config
                            }

                            # Make AI predictions
                            with st.spinner("Generating AI predictions..."):
                                ai_predictions = predict_with_ai_service(
                                    historical_data,
                                    symbol,
                                    model_horizons,
                                    ai_config,
                                    current_live_data  # Use the freshly fetched live data
                                )

                                # Use AI predictions
                                predictions = ai_predictions
                                st.success("AI predictions generated successfully")
                        else:
                            st.warning("AI services are not enabled. Using selected models instead.")
                            # Fall back to traditional models
                            use_ai_predictions = False
                    else:
                        st.warning("AI services configuration not found. Using selected models instead.")
                        # Fall back to traditional models
                        use_ai_predictions = False
                elif use_ai_predictions and use_ensemble:
                    st.warning("Cannot use both AI predictions and ensemble approach simultaneously. Using ensemble approach.")
                    use_ai_predictions = False

                # If not using AI predictions or if AI predictions failed, use traditional models
                if not use_ai_predictions:
                    # Check if using ensemble approach
                    if use_ensemble and ensemble_models:
                        try:
                            st.info(f"Using ensemble approach with {len(ensemble_models) + 1} models")

                            # Dictionary to store predictions from each model
                            all_model_predictions = {}
                            successful_models = []

                            # Get predictions from primary model
                            try:
                                if current_live_data is not None and not current_live_data.empty:
                                    primary_predictions = predict_from_live_data(
                                        current_live_data, historical_data, symbol,
                                        horizons=model_horizons,
                                        model_type=model_type.lower(),
                                        models_path='saved_models'
                                    )
                                    base_price = safe_get_base_price(current_live_data, historical_data)
                                    st.info(f"Using live price data: ${base_price:.2f}")
                                else:
                                    primary_predictions = predict_future_prices(
                                        historical_data, symbol,
                                        horizons=model_horizons,
                                        model_type=model_type.lower(),
                                        models_path='saved_models'
                                    )
                                    base_price = safe_get_base_price(None, historical_data)
                                    st.info(f"Using historical price data: ${base_price:.2f}")

                                # Store primary model predictions
                                all_model_predictions[model_type] = primary_predictions
                                successful_models.append(model_type)
                            except Exception as e:
                                st.error(f"Error getting predictions from primary model ({available_models[model_type]}): {str(e)}")
                                # If primary model fails, we'll need to use a different model as the base

                            # Get predictions from additional models
                            for additional_model in ensemble_models:
                                try:
                                    st.write(f"Getting predictions from {available_models[additional_model]}...")

                                    if current_live_data is not None and not current_live_data.empty:
                                        model_predictions = predict_from_live_data(
                                            current_live_data, historical_data, symbol,
                                            horizons=model_horizons,
                                            model_type=additional_model.lower(),
                                            models_path='saved_models'
                                        )
                                    else:
                                        model_predictions = predict_future_prices(
                                            historical_data, symbol,
                                            horizons=model_horizons,
                                            model_type=additional_model.lower(),
                                            models_path='saved_models'
                                        )

                                    # Store this model's predictions
                                    all_model_predictions[additional_model] = model_predictions
                                    successful_models.append(additional_model)
                                except Exception as e:
                                    st.warning(f"Error getting predictions from {available_models[additional_model]}: {str(e)}")
                                    # Continue with other models

                            # Check if we have any successful models
                            if not successful_models:
                                st.error("All models failed to generate predictions. Falling back to simple prediction.")
                                # Fall back to a simple prediction based on historical data
                                predictions = {}
                                for horizon in model_horizons:
                                    # Simple percentage increase based on horizon
                                    if horizon_unit == "minutes":
                                        increase_factor = 1 + (horizon / 10000)  # Very small increase for minutes
                                    elif horizon_unit == "days":
                                        increase_factor = 1 + (horizon / 100)    # 1% increase per day
                                    else:  # weeks
                                        increase_factor = 1 + (horizon / 33)     # 3% increase per week

                                    # Use the last known price as the base
                                    base_price = safe_get_base_price(current_live_data, historical_data)

                                    predictions[horizon] = base_price * increase_factor
                            else:
                                # Calculate ensemble predictions
                                predictions = {}

                                # If primary model failed, use the first successful model as the base
                                if model_type not in successful_models:
                                    model_type = successful_models[0]
                                    primary_predictions = all_model_predictions[model_type]
                                    st.info(f"Using {available_models[model_type]} as the primary model (original primary model failed)")

                                # Remove the primary model from the ensemble models list if it's there
                                ensemble_models = [m for m in successful_models if m != model_type]

                                if not ensemble_models:
                                    # If we only have one successful model, just use its predictions
                                    predictions = all_model_predictions[model_type]
                                    st.info(f"Only {available_models[model_type]} generated successful predictions. Using its predictions directly.")
                                else:
                                    # Calculate weight for each additional model
                                    additional_weight = remaining_weight / len(ensemble_models) if ensemble_models else 0

                                    # Blend predictions for each horizon
                                    for horizon in model_horizons:
                                        if horizon in primary_predictions:
                                            # Start with weighted primary prediction
                                            ensemble_prediction = primary_weight * primary_predictions[horizon]

                                            # Add weighted predictions from additional models
                                            for additional_model in ensemble_models:
                                                if additional_model in all_model_predictions and horizon in all_model_predictions[additional_model]:
                                                    ensemble_prediction += additional_weight * all_model_predictions[additional_model][horizon]

                                            # Store the ensemble prediction
                                            predictions[horizon] = ensemble_prediction

                                st.success(f"Ensemble predictions generated successfully using {len(successful_models)} models")
                        except Exception as e:
                            st.error(f"Error in ensemble prediction: {str(e)}")
                            st.warning("Falling back to single model prediction")
                            # Fall back to single model prediction
                            use_ensemble = False

                    # If not using ensemble approach or if ensemble approach failed
                    if not use_ensemble:
                        try:
                            if current_live_data is not None and not current_live_data.empty:
                                # Make predictions using live data
                                predictions = predict_from_live_data(
                                    current_live_data, historical_data, symbol,
                                    horizons=model_horizons,
                                    model_type=model_type.lower(),
                                    models_path='saved_models'
                                )
                                base_price = safe_get_base_price(current_live_data, historical_data)
                                st.info(f"Using live price data: ${base_price:.2f}")
                            else:
                                # Make predictions using only historical data
                                predictions = predict_future_prices(
                                    historical_data, symbol,
                                    horizons=model_horizons,
                                    model_type=model_type.lower(),
                                    models_path='saved_models'
                                )
                                base_price = safe_get_base_price(None, historical_data)
                                st.info(f"Using historical price data: ${base_price:.2f}")
                        except Exception as e:
                            st.error(f"Error generating predictions with {available_models[model_type]}: {str(e)}")
                            st.warning("Using simple trend-based predictions instead")

                            # Fall back to a simple prediction based on historical data
                            predictions = {}
                            for horizon in model_horizons:
                                # Simple percentage increase based on horizon
                                if horizon_unit == "minutes":
                                    increase_factor = 1 + (horizon / 10000)  # Very small increase for minutes
                                elif horizon_unit == "days":
                                    increase_factor = 1 + (horizon / 100)    # 1% increase per day
                                else:  # weeks
                                    increase_factor = 1 + (horizon / 33)     # 3% increase per week

                                # Use the last known price as the base
                                base_price = safe_get_base_price(current_live_data, historical_data)

                                predictions[horizon] = base_price * increase_factor

                # Map the predictions back to the original horizons
                horizon_predictions = {}
                for h in trained_horizons:
                    converted_h = horizon_conversion[h]
                    if converted_h in predictions:
                        horizon_predictions[h] = predictions[converted_h]
                    else:
                        # If we don't have a prediction for this horizon, use a fallback
                        logger.warning(f"No prediction found for horizon {h} ({horizon_unit})")

                        # Find the closest horizon we have a prediction for
                        available_horizons = list(predictions.keys())
                        if available_horizons:
                            closest_horizon = min(available_horizons, key=lambda x: abs(x - converted_h))
                            logger.info(f"Using prediction from horizon {closest_horizon} minutes for {h} {horizon_unit}")

                            # Get the base price
                            base_price = safe_get_base_price(current_live_data, historical_data)

                            # Adjust the prediction based on the difference in horizons
                            horizon_ratio = converted_h / closest_horizon if closest_horizon != 0 else 1
                            adjusted_prediction = predictions[closest_horizon] * (1 + (horizon_ratio - 1) * 0.01)
                            horizon_predictions[h] = adjusted_prediction
                        else:
                            # If we have no predictions at all, use a simple percentage increase
                            base_price = safe_get_base_price(current_live_data, historical_data)

                            # Simple percentage increase based on horizon
                            if horizon_unit == "minutes":
                                increase_factor = 1 + (h / 10000)  # Very small increase for minutes
                            elif horizon_unit == "days":
                                increase_factor = 1 + (h / 100)    # 1% increase per day
                            else:  # weeks
                                increase_factor = 1 + (h / 33)     # 3% increase per week

                            horizon_predictions[h] = base_price * increase_factor

                # Replace predictions with mapped predictions
                predictions = horizon_predictions

                # Make sure all selected horizons are in the predictions
                for h in selected_horizons:
                    if h not in predictions:
                        # If we don't have a prediction for this horizon, use a fallback
                        logger.warning(f"No prediction found for selected horizon {h} ({horizon_unit})")

                        # Get the base price
                        base_price = safe_get_base_price(current_live_data, historical_data)

                        # Simple percentage increase based on horizon
                        if horizon_unit == "minutes":
                            increase_factor = 1 + (h / 10000)  # Very small increase for minutes
                        elif horizon_unit == "days":
                            increase_factor = 1 + (h / 100)    # 1% increase per day
                        else:  # weeks
                            increase_factor = 1 + (h / 33)     # 3% increase per week

                        predictions[h] = base_price * increase_factor

                # Display predictions
                st.success("Predictions generated successfully")

                # Save predictions to session state for reporting
                st.session_state.predictions = predictions

                # Track predictions for performance metrics
                try:
                    # Determine which model type to track
                    if use_ai_predictions:
                        tracking_model = f"ai_{provider}"
                    elif use_ensemble:
                        tracking_model = f"ensemble_{model_type}"
                    else:
                        tracking_model = model_type

                    # Track the predictions
                    track_prediction(symbol, predictions, horizon_unit, tracking_model)
                    st.info("Predictions tracked for performance metrics")
                except Exception as e:
                    logger.error(f"Error tracking predictions: {str(e)}")
                    # Continue even if tracking fails

                # Create a table of predictions
                pred_data = []
                current_time = datetime.now()

                # Get the horizon unit and number of points from session state
                horizon_unit = st.session_state.horizon_unit
                num_points = st.session_state.num_prediction_points

                # Generate multiple prediction points for each horizon
                all_predictions = {}

                for horizon in selected_horizons:
                    # Skip horizons that don't have predictions
                    if horizon not in predictions:
                        continue

                    # Initialize list to store multiple predictions for this horizon
                    horizon_predictions = []

                    # Convert horizon to minutes for the model (which was trained on minute-based data)
                    if horizon_unit == "days":
                        model_horizon = horizon * 24 * 60  # days to minutes
                    elif horizon_unit == "weeks":
                        model_horizon = horizon * 7 * 24 * 60  # weeks to minutes
                    else:  # minutes
                        model_horizon = horizon

                    # Get base price - use freshly fetched live data if available
                    base_price = safe_get_base_price(current_live_data, historical_data)

                    # For short-term predictions (minutes), just use the existing single-point prediction
                    if horizon_unit == "minutes":
                        pred_time = current_time + timedelta(minutes=horizon)
                        # Calculate confidence score (higher for shorter horizons)
                        confidence = max(0, min(100, 100 - (horizon * 0.5)))  # 0.5% decrease per minute

                        # Get the predicted price
                        if horizon in predictions:
                            predicted_price = predictions[horizon]
                        else:
                            # If we don't have a prediction for this horizon, use a simple trend-based estimate
                            st.warning(f"No model prediction available for {horizon} {horizon_unit}. Using trend-based estimate.")
                            # Find the closest horizon we have a prediction for
                            available_horizons = list(predictions.keys())
                            if available_horizons:
                                closest_horizon = min(available_horizons, key=lambda x: abs(x - horizon))
                                # Adjust the prediction based on the difference in horizons
                                horizon_ratio = horizon / closest_horizon if closest_horizon != 0 else 1
                                predicted_price = predictions[closest_horizon] * (1 + (horizon_ratio - 1) * 0.01)
                            else:
                                # If we have no predictions at all, use a simple percentage increase
                                predicted_price = base_price * (1 + (horizon / 100))

                        pred_data.append({
                            f'Horizon ({horizon_unit})': horizon,
                            'Predicted Time': pred_time.strftime('%Y-%m-%d %H:%M:%S'),
                            'Predicted Price': round(predicted_price, 2),
                            'Confidence': f"{confidence:.1f}%"
                        })
                        all_predictions[horizon] = {pred_time: predicted_price}
                    else:
                        # For medium and long-term predictions, generate multiple points
                        horizon_pred_dict = {}

                        # Get the predicted price for this horizon
                        if horizon in predictions:
                            predicted_price = predictions[horizon]
                            # Use the model's prediction as a trend indicator
                            trend_factor = predicted_price / base_price
                        else:
                            # If we don't have a prediction for this horizon, use a simple trend-based estimate
                            st.warning(f"No model prediction available for {horizon} {horizon_unit}. Using trend-based estimate.")
                            # Find the closest horizon we have a prediction for
                            available_horizons = list(predictions.keys())
                            if available_horizons:
                                closest_horizon = min(available_horizons, key=lambda x: abs(x - horizon))
                                # Adjust the prediction based on the difference in horizons
                                horizon_ratio = horizon / closest_horizon if closest_horizon != 0 else 1
                                predicted_price = predictions[closest_horizon] * (1 + (horizon_ratio - 1) * 0.05)
                                trend_factor = predicted_price / base_price
                            else:
                                # If we have no predictions at all, use a simple percentage increase
                                if horizon_unit == "days":
                                    trend_factor = 1 + (horizon * 0.01)  # 1% increase per day
                                else:  # weeks
                                    trend_factor = 1 + (horizon * 0.03)  # 3% increase per week

                        # Generate predictions for each point
                        for i in range(1, num_points + 1):
                            if horizon_unit == "days":
                                point_time = current_time + timedelta(days=i * horizon)
                                # Adjust trend based on the point number
                                point_trend = 1 + (trend_factor - 1) * (i / num_points)
                                point_price = base_price * point_trend
                            else:  # weeks
                                point_time = current_time + timedelta(weeks=i * horizon)
                                # Adjust trend based on the point number
                                point_trend = 1 + (trend_factor - 1) * (i / num_points)
                                point_price = base_price * point_trend

                            # Calculate confidence score (decreases with longer horizons and further points)
                            if horizon_unit == "days":
                                # 5% decrease per day, additional 2% decrease per point
                                confidence = max(0, min(100, 100 - (horizon * 5) - (i * 2)))
                            else:  # weeks
                                # 10% decrease per week, additional 5% decrease per point
                                confidence = max(0, min(100, 100 - (horizon * 10) - (i * 5)))

                            # Add to predictions data
                            pred_data.append({
                                f'Horizon ({horizon_unit})': horizon,
                                'Point': i,
                                'Predicted Time': point_time.strftime('%Y-%m-%d %H:%M:%S'),
                                'Predicted Price': round(point_price, 2),
                                'Confidence': f"{confidence:.1f}%"
                            })

                            # Store for plotting
                            horizon_pred_dict[point_time] = point_price

                        all_predictions[horizon] = horizon_pred_dict

                # Create and display the predictions dataframe
                pred_df = pd.DataFrame(pred_data)
                st.dataframe(pred_df)

                # Plot predictions using the freshly fetched live data
                plot_predictions(historical_data, current_live_data, all_predictions, symbol, horizon_unit)

        except Exception as e:
            st.error(f"Error generating predictions: {str(e)}")
            logger.error(f"Error generating predictions: {str(e)}")

            # Provide helpful debugging information
            if "Close" in str(e):
                st.error("❌ **Data Issue**: The 'Close' column is missing from the data.")
                st.markdown("**Possible solutions:**")
                st.markdown("1. 🔄 **Reload the page** and try again")
                st.markdown("2. 📤 **Re-upload your data** to ensure it has the required columns")
                st.markdown("3. 📊 **Check data format** - ensure your data has Date, Open, High, Low, Close, Volume columns")

                # Show data structure for debugging
                if hasattr(st.session_state, 'historical_data') and st.session_state.historical_data is not None:
                    st.markdown("**Current data columns:**")
                    st.write(list(st.session_state.historical_data.columns))
                else:
                    st.error("No historical data found in session state")
            else:
                st.markdown("**Possible solutions:**")
                st.markdown("1. 🔄 **Reload the page** and try again")
                st.markdown("2. 🤖 **Try a different model** if available")
                st.markdown("3. ⏱️ **Try different time horizons**")

def plot_predictions(historical_data, live_data, predictions, symbol, horizon_unit='minutes'):
    """
    Plot historical data and predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        predictions (dict): Dictionary with predictions for each horizon
        symbol (str): Stock symbol
        horizon_unit (str): Unit of time for horizons (minutes, days, weeks)
    """
    st.subheader("Price Forecast")

    # Create figure
    fig = go.Figure()

    # Add historical data
    fig.add_trace(go.Scatter(
        x=historical_data['Date'],
        y=historical_data['Close'],
        mode='lines',
        name='Historical',
        line=dict(color='blue')
    ))

    # Add live data if available
    if live_data is not None and not live_data.empty:
        fig.add_trace(go.Scatter(
            x=live_data['Date'],
            y=live_data['Close'],
            mode='markers',
            name='Live',
            marker=dict(color='green', size=10)
        ))

    # Add predictions
    colors = ['red', 'orange', 'purple', 'brown', 'pink']

    for i, (horizon, pred_dict) in enumerate(predictions.items()):
        # Get color for this horizon (cycle through colors if needed)
        color = colors[i % len(colors)]

        # Extract times and prices from the prediction dictionary
        pred_times = list(pred_dict.keys())
        pred_prices = list(pred_dict.values())

        # For single-point predictions (minutes)
        if len(pred_times) == 1:
            fig.add_trace(go.Scatter(
                x=pred_times,
                y=pred_prices,
                mode='markers',
                name=f'{horizon} {horizon_unit}',
                marker=dict(color=color, size=10, symbol='star')
            ))
        else:
            # For multi-point predictions (days, weeks)
            fig.add_trace(go.Scatter(
                x=pred_times,
                y=pred_prices,
                mode='lines+markers',
                name=f'{horizon} {horizon_unit}',
                line=dict(color=color, dash='dot'),
                marker=dict(color=color, size=8, symbol='circle')
            ))

    # Update layout
    fig.update_layout(
        title=f'{symbol} Price Forecast',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig, use_container_width=True)
