"""
Custom Indicators and Intermarket Analysis Module

This module provides:
- Custom indicator creation with AI-powered optimization
- Intermarket analysis (currency, commodity impacts)
- Options flow analysis simulation
- Advanced technical indicators
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class CustomIndicators:
    """
    Custom Technical Indicators with AI-powered optimization
    """

    def __init__(self):
        self.custom_indicators = {}
        self.optimization_history = {}

    @staticmethod
    def create_adaptive_moving_average(data: pd.Series, period: int = 20, alpha: float = 0.1) -> pd.Series:
        """
        Create an adaptive moving average that adjusts to market volatility

        Args:
            data (pd.Series): Price data
            period (int): Base period for calculation
            alpha (float): Adaptation factor

        Returns:
            pd.Series: Adaptive moving average
        """
        try:
            # Calculate volatility
            volatility = data.rolling(period).std()

            # Adaptive factor based on volatility
            adaptive_factor = alpha * (volatility / volatility.rolling(period * 2).mean())
            adaptive_factor = adaptive_factor.fillna(alpha)

            # Calculate adaptive EMA
            ama = pd.Series(index=data.index, dtype=float)
            ama.iloc[0] = data.iloc[0]

            for i in range(1, len(data)):
                ama.iloc[i] = ama.iloc[i-1] + adaptive_factor.iloc[i] * (data.iloc[i] - ama.iloc[i-1])

            return ama

        except Exception as e:
            logger.error(f"Error creating adaptive moving average: {str(e)}")
            return data.rolling(period).mean()

    @staticmethod
    def create_ai_momentum_indicator(data: pd.DataFrame, lookback: int = 14) -> Dict[str, pd.Series]:
        """
        Create AI-powered momentum indicator combining multiple momentum measures

        Args:
            data (pd.DataFrame): OHLCV data
            lookback (int): Lookback period

        Returns:
            Dict[str, pd.Series]: AI momentum components
        """
        try:
            close = data['Close']
            high = data['High']
            low = data['Low']
            volume = data.get('Volume', pd.Series([1] * len(data)))

            # Traditional momentum
            momentum = close.pct_change(lookback)

            # Rate of Change
            roc = (close - close.shift(lookback)) / close.shift(lookback) * 100

            # Relative Strength Index
            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=lookback).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=lookback).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            # Volume-weighted momentum
            volume_momentum = (close.pct_change() * volume).rolling(lookback).sum() / volume.rolling(lookback).sum()

            # Price efficiency
            price_change = abs(close - close.shift(lookback))
            price_path = abs(close.diff()).rolling(lookback).sum()
            efficiency = price_change / price_path

            # Combine indicators using AI weighting
            weights = CustomIndicators._calculate_ai_weights([momentum, roc/100, (rsi-50)/50, volume_momentum, efficiency])

            ai_momentum = (
                weights[0] * momentum +
                weights[1] * (roc/100) +
                weights[2] * ((rsi-50)/50) +
                weights[3] * volume_momentum +
                weights[4] * efficiency
            )

            return {
                'AI_Momentum': ai_momentum,
                'Traditional_Momentum': momentum,
                'ROC': roc,
                'RSI': rsi,
                'Volume_Momentum': volume_momentum,
                'Price_Efficiency': efficiency,
                'Weights': pd.Series(weights, index=['Momentum', 'ROC', 'RSI', 'Volume', 'Efficiency'])
            }

        except Exception as e:
            logger.error(f"Error creating AI momentum indicator: {str(e)}")
            return {'AI_Momentum': close.pct_change(lookback)}

    @staticmethod
    def _calculate_ai_weights(indicators: List[pd.Series]) -> List[float]:
        """Calculate AI-optimized weights for combining indicators"""
        try:
            # Simple correlation-based weighting
            weights = []

            for i, indicator in enumerate(indicators):
                if indicator.isna().all():
                    weights.append(0.0)
                    continue

                # Calculate indicator quality metrics
                volatility = indicator.std()
                trend_strength = abs(indicator.rolling(20).mean().iloc[-1]) if len(indicator) >= 20 else 0

                # Weight based on trend strength and inverse volatility
                weight = trend_strength / (volatility + 1e-6) if volatility > 0 else 0
                weights.append(weight)

            # Normalize weights
            total_weight = sum(weights)
            if total_weight > 0:
                weights = [w / total_weight for w in weights]
            else:
                weights = [1.0 / len(indicators)] * len(indicators)

            return weights

        except Exception as e:
            logger.error(f"Error calculating AI weights: {str(e)}")
            return [1.0 / len(indicators)] * len(indicators)

    @staticmethod
    def create_volatility_bands(data: pd.Series, period: int = 20, std_dev: float = 2.0) -> Dict[str, pd.Series]:
        """
        Create adaptive volatility bands

        Args:
            data (pd.Series): Price data
            period (int): Period for calculation
            std_dev (float): Standard deviation multiplier

        Returns:
            Dict[str, pd.Series]: Volatility bands
        """
        try:
            # Calculate base moving average
            ma = data.rolling(period).mean()

            # Calculate adaptive standard deviation
            rolling_std = data.rolling(period).std()

            # Create bands
            upper_band = ma + (std_dev * rolling_std)
            lower_band = ma - (std_dev * rolling_std)

            # Calculate band width and position
            band_width = (upper_band - lower_band) / ma * 100
            band_position = (data - lower_band) / (upper_band - lower_band) * 100

            return {
                'Middle_Band': ma,
                'Upper_Band': upper_band,
                'Lower_Band': lower_band,
                'Band_Width': band_width,
                'Band_Position': band_position
            }

        except Exception as e:
            logger.error(f"Error creating volatility bands: {str(e)}")
            return {'Middle_Band': data.rolling(period).mean()}

class IntermarketAnalysis:
    """
    Intermarket Analysis for currency and commodity impacts
    """

    def __init__(self):
        self.correlation_cache = {}
        self.impact_factors = {}

    @staticmethod
    def simulate_currency_impact(stock_data: pd.DataFrame, currency_strength: float = 0.0) -> Dict[str, Any]:
        """
        Simulate USD impact on Egyptian stocks based on real market relationships

        Args:
            stock_data (pd.DataFrame): Stock price data
            currency_strength (float): USD strength indicator (-1 to 1)

        Returns:
            Dict[str, Any]: Currency impact analysis with EGX-specific correlations
        """
        try:
            close_prices = stock_data['Close']

            # Real USD-EGX correlation: Strong USD typically hurts Egyptian stocks
            # Based on historical analysis: -0.45 average correlation
            base_correlation = -0.45

            # Sector-specific USD correlations for Egyptian market
            sector_correlations = {
                "Banks": -0.65,           # High USD exposure (foreign funding, debt)
                "Real Estate": -0.35,     # Moderate impact (foreign investment)
                "Telecom": -0.25,         # Lower impact (more domestic revenue)
                "Energy": -0.15,          # Least impacted (commodity-linked, USD revenues)
                "Consumer Goods": -0.55,  # High import dependency
                "Industrial": -0.40,      # Moderate impact (mixed exposure)
                "Tourism": -0.70,         # Highest impact (foreign currency dependent)
                "Utilities": -0.20        # Low impact (regulated, domestic)
            }

            # Calculate currency-adjusted prices with realistic scaling
            currency_factor = 1 + (currency_strength * base_correlation * 0.08)  # 8% max impact
            adjusted_prices = close_prices * currency_factor

            # Calculate impact metrics
            price_impact = (adjusted_prices.iloc[-1] - close_prices.iloc[-1]) / close_prices.iloc[-1] * 100
            volatility_impact = adjusted_prices.std() / close_prices.std() - 1

            # Generate sector-specific impacts
            sector_impacts = {}
            for sector, correlation in sector_correlations.items():
                sector_impact = currency_strength * correlation * 8  # 8% max sector impact
                sector_impacts[sector] = {
                    "impact_percent": sector_impact,
                    "correlation": correlation,
                    "risk_level": "High" if abs(correlation) > 0.5 else "Medium" if abs(correlation) > 0.3 else "Low"
                }

            # Enhanced recommendation based on real EGX dynamics
            if abs(currency_strength) < 0.15:
                recommendation = "Minimal USD impact - Neutral outlook for EGX"
                risk_level = "Low"
            elif currency_strength > 0.6:
                recommendation = "Strong USD creates significant headwinds - Avoid Banks/Tourism, favor Energy/Telecom"
                risk_level = "High"
            elif currency_strength > 0.3:
                recommendation = "Moderate USD strength pressure - Monitor import-heavy sectors, defensive positioning"
                risk_level = "Medium"
            elif currency_strength < -0.6:
                recommendation = "Weak USD provides strong tailwinds - Bullish for all EGX sectors, especially Banks/Tourism"
                risk_level = "Opportunity"
            elif currency_strength < -0.3:
                recommendation = "USD weakness supports EGX - Positive for foreign-exposed sectors"
                risk_level = "Low-Medium"
            else:
                recommendation = "Balanced USD environment - Sector rotation opportunities in EGX"
                risk_level = "Low"

            return {
                'currency_correlation': base_correlation,
                'price_impact_percent': price_impact,
                'volatility_impact': volatility_impact,
                'adjusted_current_price': adjusted_prices.iloc[-1],
                'currency_factor': currency_factor,
                'sector_impacts': sector_impacts,
                'risk_level': risk_level,
                'market_context': "EGX has negative correlation with USD due to capital flows, debt servicing, and import costs",
                'key_factors': [
                    "Foreign investment flows (portfolio and FDI)",
                    "Egypt's USD-denominated debt burden",
                    "Import costs for Egyptian companies",
                    "Tourism revenue (USD-denominated)",
                    "Suez Canal revenues (USD-denominated)"
                ],
                'recommendation': recommendation
            }

        except Exception as e:
            logger.error(f"Error simulating currency impact: {str(e)}")
            return {'error': str(e)}

    @staticmethod
    def simulate_commodity_impact(stock_data: pd.DataFrame, commodity_prices: Dict[str, float] = None) -> Dict[str, Any]:
        """
        Simulate commodity impact on Egyptian stocks based on real market relationships

        Args:
            stock_data (pd.DataFrame): Stock price data
            commodity_prices (Dict[str, float]): Commodity price changes (%)

        Returns:
            Dict[str, Any]: Commodity impact analysis with EGX-specific correlations
        """
        try:
            if commodity_prices is None:
                commodity_prices = {
                    'oil': 0.0,      # Oil price change %
                    'gold': 0.0,     # Gold price change %
                    'copper': 0.0    # Copper price change %
                }

            close_prices = stock_data['Close']

            # Real commodity correlations for Egyptian market based on economic structure
            correlations = {
                'oil': -0.15,    # Negative: Egypt is net oil importer, higher oil hurts most sectors
                'gold': 0.25,    # Positive: Safe haven, inflation hedge, Egypt has gold mining
                'copper': 0.35   # Positive: Industrial demand indicator, construction boom
            }

            # Sector-specific commodity impacts for EGX
            sector_commodity_impacts = {
                'oil': {
                    "Energy": 0.60,           # Direct positive correlation for oil/gas companies
                    "Airlines": -0.80,        # High negative correlation (fuel costs)
                    "Transportation": -0.65,  # High negative correlation (fuel costs)
                    "Manufacturing": -0.30,   # Moderate negative (energy costs)
                    "Consumer Goods": -0.25,  # Moderate negative (transportation costs)
                    "Tourism": -0.20,         # Slight negative (transportation costs)
                    "Banks": -0.10,           # Slight negative (economic impact)
                    "Real Estate": -0.05,     # Minimal impact
                    "Telecom": -0.05          # Minimal impact
                },
                'gold': {
                    "Mining": 0.85,           # Direct positive correlation
                    "Banks": 0.30,            # Positive (safe haven flows)
                    "Real Estate": 0.20,      # Positive (inflation hedge)
                    "Consumer Goods": 0.15,   # Slight positive (inflation hedge)
                    "Energy": 0.10,           # Slight positive
                    "Tourism": 0.05,          # Minimal positive
                    "Telecom": 0.05,          # Minimal positive
                    "Transportation": 0.00,   # Neutral
                    "Airlines": 0.00          # Neutral
                },
                'copper': {
                    "Construction": 0.70,     # High positive (direct material)
                    "Industrial": 0.60,       # High positive (manufacturing input)
                    "Real Estate": 0.45,      # Moderate positive (construction demand)
                    "Energy": 0.30,           # Moderate positive (infrastructure)
                    "Utilities": 0.25,        # Moderate positive (electrical infrastructure)
                    "Banks": 0.20,            # Positive (economic growth indicator)
                    "Consumer Goods": 0.10,   # Slight positive (economic growth)
                    "Tourism": 0.05,          # Minimal positive
                    "Telecom": 0.05           # Minimal positive
                }
            }

            # Calculate weighted commodity impact
            total_impact = 0
            impact_breakdown = {}

            for commodity, price_change in commodity_prices.items():
                if commodity in correlations:
                    correlation = correlations[commodity]
                    impact = price_change * correlation * 0.01  # Convert to decimal
                    total_impact += impact

                    # Calculate sector-specific impacts
                    sector_impacts = {}
                    if commodity in sector_commodity_impacts:
                        for sector, sector_corr in sector_commodity_impacts[commodity].items():
                            sector_impact = price_change * sector_corr * 0.01 * 100  # Convert to percentage
                            sector_impacts[sector] = {
                                "impact_percent": sector_impact,
                                "correlation": sector_corr
                            }

                    impact_breakdown[commodity] = {
                        'price_change': price_change,
                        'overall_correlation': correlation,
                        'overall_impact': impact * 100,  # Convert back to percentage
                        'sector_impacts': sector_impacts,
                        'market_context': IntermarketAnalysis._get_commodity_context(commodity)
                    }

            # Apply impact to stock prices
            commodity_factor = 1 + total_impact
            adjusted_prices = close_prices * commodity_factor

            # Calculate metrics
            price_impact = total_impact * 100

            # Enhanced recommendation
            recommendation = IntermarketAnalysis._get_enhanced_commodity_recommendation(
                commodity_prices, impact_breakdown, price_impact
            )

            return {
                'total_price_impact_percent': price_impact,
                'commodity_factor': commodity_factor,
                'adjusted_current_price': adjusted_prices.iloc[-1],
                'impact_breakdown': impact_breakdown,
                'market_summary': IntermarketAnalysis._get_commodity_market_summary(commodity_prices),
                'recommendation': recommendation
            }

        except Exception as e:
            logger.error(f"Error simulating commodity impact: {str(e)}")
            return {'error': str(e)}

    @staticmethod
    def _get_currency_recommendation(price_impact: float) -> str:
        """Get recommendation based on currency impact"""
        if price_impact > 2:
            return "Strong positive currency impact - Consider buying"
        elif price_impact > 0.5:
            return "Moderate positive currency impact - Cautiously bullish"
        elif price_impact < -2:
            return "Strong negative currency impact - Consider selling"
        elif price_impact < -0.5:
            return "Moderate negative currency impact - Cautiously bearish"
        else:
            return "Minimal currency impact - Neutral"

    @staticmethod
    def _get_commodity_recommendation(price_impact: float) -> str:
        """Get recommendation based on commodity impact"""
        if price_impact > 1.5:
            return "Positive commodity tailwinds - Bullish outlook"
        elif price_impact > 0.3:
            return "Mild commodity support - Slightly positive"
        elif price_impact < -1.5:
            return "Commodity headwinds - Bearish pressure"
        elif price_impact < -0.3:
            return "Mild commodity pressure - Slightly negative"
        else:
            return "Neutral commodity environment"

    @staticmethod
    def _get_commodity_context(commodity: str) -> str:
        """Get market context for specific commodities"""
        contexts = {
            'oil': "Egypt is a net oil importer. Higher oil prices increase costs for most sectors except Energy companies (EGAS, EGPC). Airlines and transportation are most negatively affected.",
            'gold': "Egypt has significant gold mining operations. Gold serves as safe haven and inflation hedge. Rising gold prices benefit mining companies and attract safe-haven flows to EGX.",
            'copper': "Copper is key industrial input for Egypt's construction boom and infrastructure projects. Rising copper indicates strong global growth, benefiting Egyptian industrial and construction sectors."
        }
        return contexts.get(commodity, "No specific context available")

    @staticmethod
    def _get_enhanced_commodity_recommendation(commodity_prices: Dict[str, float], impact_breakdown: Dict, total_impact: float) -> str:
        """Generate enhanced recommendation based on multiple commodity impacts"""
        recommendations = []

        for commodity, price_change in commodity_prices.items():
            if abs(price_change) > 2:  # Significant price change
                if commodity == 'oil':
                    if price_change > 0:
                        recommendations.append("Rising oil prices: Favor Energy sector (EGAS), avoid Airlines/Transportation")
                    else:
                        recommendations.append("Falling oil prices: Positive for Airlines/Transportation, negative for Energy")
                elif commodity == 'gold':
                    if price_change > 0:
                        recommendations.append("Rising gold: Bullish for Mining sector and safe-haven flows to EGX")
                    else:
                        recommendations.append("Falling gold: Risk-on environment, favor growth sectors over Mining")
                elif commodity == 'copper':
                    if price_change > 0:
                        recommendations.append("Rising copper: Strong for Construction/Industrial, indicates economic growth")
                    else:
                        recommendations.append("Falling copper: Weakness in Construction/Industrial, economic concerns")

        if not recommendations:
            return "Minimal commodity impacts - Neutral environment for EGX sectors"

        return " | ".join(recommendations)

    @staticmethod
    def _get_commodity_market_summary(commodity_prices: Dict[str, float]) -> str:
        """Generate market summary based on commodity movements"""
        oil_change = commodity_prices.get('oil', 0)
        gold_change = commodity_prices.get('gold', 0)
        copper_change = commodity_prices.get('copper', 0)

        summary_parts = []

        if abs(oil_change) > 1:
            direction = "rising" if oil_change > 0 else "falling"
            summary_parts.append(f"Oil {direction} ({oil_change:+.1f}%)")

        if abs(gold_change) > 1:
            direction = "rising" if gold_change > 0 else "falling"
            summary_parts.append(f"Gold {direction} ({gold_change:+.1f}%)")

        if abs(copper_change) > 1:
            direction = "rising" if copper_change > 0 else "falling"
            summary_parts.append(f"Copper {direction} ({copper_change:+.1f}%)")

        if not summary_parts:
            return "Stable commodity environment with minimal price movements"

        return "Active commodity environment: " + ", ".join(summary_parts)

class OptionsFlowAnalysis:
    """
    Options Flow Analysis (Simulated for Egyptian market)
    """

    @staticmethod
    def simulate_options_flow(stock_data: pd.DataFrame, volume_data: pd.Series = None) -> Dict[str, Any]:
        """
        Simulate options flow analysis based on volume and price patterns

        Args:
            stock_data (pd.DataFrame): Stock price data
            volume_data (pd.Series): Volume data

        Returns:
            Dict[str, Any]: Simulated options flow analysis
        """
        try:
            close_prices = stock_data['Close']
            volume = volume_data if volume_data is not None else stock_data.get('Volume', pd.Series([1] * len(stock_data)))

            # Simulate unusual activity detection
            avg_volume = volume.rolling(20).mean()
            volume_spike = volume / avg_volume

            # Simulate call/put ratio based on price momentum and volume
            price_momentum = close_prices.pct_change(5)

            # Higher volume + positive momentum = more call activity
            call_activity = np.where(
                (volume_spike > 1.5) & (price_momentum > 0.02),
                volume_spike * price_momentum * 100,
                0
            )

            # Higher volume + negative momentum = more put activity
            put_activity = np.where(
                (volume_spike > 1.5) & (price_momentum < -0.02),
                volume_spike * abs(price_momentum) * 100,
                0
            )

            # Calculate metrics
            total_call_activity = np.sum(call_activity[-5:])  # Last 5 days
            total_put_activity = np.sum(put_activity[-5:])    # Last 5 days

            call_put_ratio = total_call_activity / (total_put_activity + 1e-6)

            # Unusual activity score
            unusual_activity_score = max(volume_spike.iloc[-5:])

            return {
                'call_put_ratio': call_put_ratio,
                'total_call_activity': total_call_activity,
                'total_put_activity': total_put_activity,
                'unusual_activity_score': unusual_activity_score,
                'sentiment': OptionsFlowAnalysis._get_options_sentiment(call_put_ratio),
                'activity_level': OptionsFlowAnalysis._get_activity_level(unusual_activity_score),
                'recommendation': OptionsFlowAnalysis._get_options_recommendation(call_put_ratio, unusual_activity_score)
            }

        except Exception as e:
            logger.error(f"Error simulating options flow: {str(e)}")
            return {'error': str(e)}

    @staticmethod
    def _get_options_sentiment(call_put_ratio: float) -> str:
        """Determine sentiment based on call/put ratio"""
        if call_put_ratio > 2.0:
            return "Very Bullish"
        elif call_put_ratio > 1.2:
            return "Bullish"
        elif call_put_ratio > 0.8:
            return "Neutral"
        elif call_put_ratio > 0.5:
            return "Bearish"
        else:
            return "Very Bearish"

    @staticmethod
    def _get_activity_level(score: float) -> str:
        """Determine activity level"""
        if score > 3.0:
            return "Extremely High"
        elif score > 2.0:
            return "High"
        elif score > 1.5:
            return "Moderate"
        else:
            return "Normal"

    @staticmethod
    def _get_options_recommendation(call_put_ratio: float, activity_score: float) -> str:
        """Get recommendation based on options flow"""
        if call_put_ratio > 1.5 and activity_score > 2.0:
            return "Strong bullish options flow - Consider long positions"
        elif call_put_ratio < 0.7 and activity_score > 2.0:
            return "Strong bearish options flow - Consider short positions"
        elif activity_score > 2.5:
            return "High unusual activity - Monitor closely for breakout"
        else:
            return "Normal options activity - No strong directional signal"
