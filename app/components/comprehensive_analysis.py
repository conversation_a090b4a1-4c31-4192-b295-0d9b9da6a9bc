"""
Comprehensive SMC Analysis Tool
Integrated from smc_integration/3_🎯_Comprehensive_Analysis.py
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
import sys
from datetime import datetime, timedelta
import json
from typing import Dict, List, Optional, Tuple

# Import SMC components
from app.components.smc_indicators import (
    detect_order_blocks, 
    detect_fvg, 
    detect_liquidity_zones,
    detect_market_structure,
    FairValueGap,
    OrderBlock,
    LiquidityZone
)
from app.components.smc_analysis_engine import SMCAnalysisEngine, SMCSignal

def calculate_comprehensive_analysis(df: pd.DataFrame, symbol: str, current_price: Optional[float] = None) -> Dict:
    """Calculate comprehensive SMC analysis"""
    
    # Use provided current price or fall back to last close
    if current_price is not None:
        price = float(current_price)
    else:
        price = float(df['close'].iloc[-1])
        
    prev_price = float(df['close'].iloc[-2])
    price_change = price - prev_price
    price_change_pct = (price_change / prev_price) * 100
    
    # Use SMC Analysis Engine
    smc_engine = SMCAnalysisEngine()
    smc_results = smc_engine.analyze_stock(df, symbol)
    
    # Extract components
    order_blocks = smc_results.get('order_blocks', [])
    fvgs = smc_results.get('fvgs', [])
    liquidity_zones = smc_results.get('liquidity_zones', [])
    market_structure = smc_results.get('market_structure', {})
    
    # Calculate key levels
    recent_high = df['high'].tail(20).max()
    recent_low = df['low'].tail(20).min()
    support_levels = []
    resistance_levels = []
      # Extract support/resistance from order blocks
    for ob in order_blocks:
        ob_type = getattr(ob, 'ob_type', getattr(ob, 'block_type', 'unknown'))
        if ob_type == "bullish" and ob.high < price:
            support_levels.append(ob.high)
        elif ob_type == "bearish" and ob.low > price:
            resistance_levels.append(ob.low)
    
    # Extract from FVGs
    for fvg in fvgs:
        if fvg.gap_type == "bullish" and fvg.high < price:
            support_levels.append(fvg.high)
        elif fvg.gap_type == "bearish" and fvg.low > price:
            resistance_levels.append(fvg.low)
    
    # Calculate confluence score
    confluence_factors = {
        'order_blocks': len([ob for ob in order_blocks if abs((ob.high + ob.low)/2 - price) / price < 0.05]),
        'fvgs': len([fvg for fvg in fvgs if abs((fvg.high + fvg.low)/2 - price) / price < 0.05]),
        'liquidity_zones': len([lz for lz in liquidity_zones if abs((lz.high + lz.low)/2 - price) / price < 0.05]),
        'market_structure': 1 if market_structure.get('trend') in ['bullish', 'bearish'] else 0
    }
    
    total_confluence = sum(confluence_factors.values())
    confluence_strength = min(total_confluence / 8, 1.0)  # Normalize to 0-1
    
    return {
        'symbol': symbol,
        'current_price': price,
        'price_change': price_change,
        'price_change_pct': price_change_pct,
        'market_structure': market_structure,
        'order_blocks': order_blocks,
        'fvgs': fvgs,
        'liquidity_zones': liquidity_zones,
        'support_levels': sorted(support_levels, reverse=True)[:5],
        'resistance_levels': sorted(resistance_levels)[:5],
        'recent_high': recent_high,
        'recent_low': recent_low,
        'confluence_factors': confluence_factors,
        'confluence_strength': confluence_strength,
        'analysis_time': datetime.now()
    }

def generate_trading_signals(analysis: Dict) -> Dict:
    """Generate comprehensive trading signals and recommendations"""
    
    current_price = analysis['current_price']
    trend = analysis['market_structure'].get('trend', 'neutral')
    confluence = analysis['confluence_strength']
    
    # Determine primary signal
    if confluence >= 0.6:
        if trend == 'bullish':
            primary_signal = 'strong_bullish'
            signal_strength = 'STRONG BUY'
            signal_color = 'success'
        elif trend == 'bearish':
            primary_signal = 'strong_bearish'
            signal_strength = 'STRONG SELL'
            signal_color = 'error'
        else:
            primary_signal = 'wait'
            signal_strength = 'WAIT FOR BREAKOUT'
            signal_color = 'warning'
    elif confluence >= 0.3:
        if trend == 'bullish':
            primary_signal = 'weak_bullish'
            signal_strength = 'WEAK BUY'
            signal_color = 'info'
        elif trend == 'bearish':
            primary_signal = 'weak_bearish'
            signal_strength = 'WEAK SELL'
            signal_color = 'info'
        else:
            primary_signal = 'neutral'
            signal_strength = 'NEUTRAL'
            signal_color = 'info'
    else:
        primary_signal = 'wait'
        signal_strength = 'WAIT FOR SETUP'
        signal_color = 'warning'
    
    # Risk management calculations
    support_levels = analysis['support_levels']
    resistance_levels = analysis['resistance_levels']
      # Calculate stop loss
    if support_levels:
        stop_loss = min(support_levels[0] * 0.98, current_price * 0.98)
    else:
        stop_loss = current_price * 0.95  # 5% stop loss fallback
    
    risk_per_share = current_price - stop_loss
    
    # Calculate profit targets
    if resistance_levels:
        valid_resistance = [r for r in resistance_levels if r > current_price]
        
        if valid_resistance:
            take_profit_1 = max(valid_resistance[0] * 0.98, current_price * 1.02)
            if len(valid_resistance) > 1:
                take_profit_2 = max(valid_resistance[1] * 0.98, current_price * 1.05)
            else:
                take_profit_2 = max(valid_resistance[0] * 1.02, current_price * 1.05)
        else:
            take_profit_1 = current_price * 1.03
            take_profit_2 = current_price * 1.06
    else:
        take_profit_1 = current_price * 1.03
        take_profit_2 = current_price * 1.06
    
    # Ensure targets are always above entry price
    take_profit_1 = max(take_profit_1, current_price * 1.01)
    take_profit_2 = max(take_profit_2, take_profit_1 * 1.02)
    
    reward_1 = take_profit_1 - current_price
    reward_2 = take_profit_2 - current_price
    
    rr_ratio_1 = reward_1 / risk_per_share if risk_per_share > 0 else 0
    rr_ratio_2 = reward_2 / risk_per_share if risk_per_share > 0 else 0
    
    return {
        'primary_signal': primary_signal,
        'signal_strength': signal_strength,
        'signal_color': signal_color,
        'entry_price': current_price,
        'stop_loss': stop_loss,
        'take_profit_1': take_profit_1,
        'take_profit_2': take_profit_2,
        'risk_per_share': risk_per_share,
        'reward_1': reward_1,
        'reward_2': reward_2,
        'rr_ratio_1': rr_ratio_1,
        'rr_ratio_2': rr_ratio_2
    }

def calculate_position_sizing(account_size: float, risk_pct: float, risk_per_share: float, current_price: float) -> Dict:
    """Calculate position sizing based on risk management"""
    
    risk_amount = account_size * (risk_pct / 100)
    shares = int(risk_amount / risk_per_share) if risk_per_share > 0 else 0
    position_value = shares * current_price
    actual_risk = shares * risk_per_share
    actual_risk_pct = (actual_risk / account_size) * 100 if account_size > 0 else 0
    
    return {
        'risk_amount': risk_amount,
        'shares': shares,
        'position_value': position_value,
        'actual_risk': actual_risk,
        'actual_risk_pct': actual_risk_pct
    }

def display_comprehensive_analysis(df: pd.DataFrame, symbol: str, current_price: Optional[float] = None):
    """Display comprehensive SMC analysis with professional styling"""
      # Custom CSS for professional look with dark theme compatibility
    st.markdown("""
    <style>
        .main-header {
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
            padding: 2rem;
            border-radius: 10px;
            color: white;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .analysis-section {
            background: rgba(50, 50, 50, 0.3);
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
            color: inherit;
        }
        
        .signal-box {
            padding: 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
            font-weight: bold;
            text-align: center;
        }
        
        .action-item {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 0.8rem;
            margin: 0.5rem 0;
            color: #42a5f5;
        }
        
        .risk-warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            color: #ffca28;
        }
        
        .pro-tip {
            background: rgba(23, 162, 184, 0.2);
            border: 1px solid #17a2b8;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            color: #26c6da;
        }
    </style>
    """, unsafe_allow_html=True)
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🎯 Comprehensive Technical Analysis</h1>
        <p>Professional SMC Trading Advisor for EGX</p>
        <p style="font-size: 0.9em; opacity: 0.9;">Advanced Analysis • Trading Signals • Risk Management • Action Plans</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Account settings sidebar
    with st.sidebar:
        st.header("💰 Account Settings")
        account_size = st.number_input("Account Size (EGP)", 
                                     min_value=1000, value=100000, step=1000,
                                     help="Your total trading account size")
        
        max_risk = st.slider("Max Risk per Trade (%)", 0.5, 5.0, 2.0, 0.1,
                           help="Maximum percentage of account to risk per trade")
      # Run comprehensive analysis
    with st.spinner(f"🔍 Analyzing {symbol} comprehensively..."):
        analysis = calculate_comprehensive_analysis(df, symbol, current_price)
        signals = generate_trading_signals(analysis)
        
        # Calculate position sizing
        pos_1pct = calculate_position_sizing(account_size, 1.0, signals['risk_per_share'], signals['entry_price'])
        pos_2pct = calculate_position_sizing(account_size, 2.0, signals['risk_per_share'], signals['entry_price'])
    
    # Current Status Overview
    st.markdown("## 📊 Current Market Status")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "💰 Current Price",
            f"{analysis['current_price']:.2f} EGP",
            delta=f"{analysis['price_change']:+.2f} ({analysis['price_change_pct']:+.1f}%)"
        )
    
    with col2:
        trend = analysis['market_structure'].get('trend', 'neutral')
        trend_emoji = "📈" if trend == "bullish" else "📉" if trend == "bearish" else "➡️"
        st.metric("📊 Market Trend", f"{trend_emoji} {trend.upper()}")
    
    with col3:
        st.metric("⚡ Confluence Score", f"{analysis['confluence_strength']:.0%}")
    
    with col4:
        st.metric("📅 Analysis Date", datetime.now().strftime("%Y-%m-%d"))
    
    # Primary Trading Signal
    st.markdown("## 🎯 Primary Trading Signal")
    
    signal_colors = {
        'success': '#28a745',
        'error': '#dc3545', 
        'warning': '#ffc107',
        'info': '#17a2b8'
    }
    
    signal_color = signal_colors.get(signals['signal_color'], '#6c757d')
    
    st.markdown(f"""
    <div class="signal-box" style="background: {signal_color}; color: white;">
        <h2>{signals['signal_strength']}</h2>
        <p>Primary Signal: {signals['primary_signal'].replace('_', ' ').title()}</p>
    </div>
    """, unsafe_allow_html=True)
    
    # SMC Confluence Analysis
    st.markdown("""
    <div class="analysis-section">
        <h3>⚡ Smart Money Concepts (SMC) Confluence</h3>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        ob_count = analysis['confluence_factors']['order_blocks']
        st.metric("🔲 Order Blocks", ob_count)
        if ob_count > 0:
            st.success("✅ OB Confluence")
        else:
            st.info("➖ No OB Confluence")
    
    with col2:
        fvg_count = analysis['confluence_factors']['fvgs']
        st.metric("⚡ Fair Value Gaps", fvg_count)
        if fvg_count > 0:
            st.success("✅ FVG Confluence")
        else:
            st.info("➖ No FVG Confluence")
    
    with col3:
        liq_count = analysis['confluence_factors']['liquidity_zones']
        st.metric("💧 Liquidity Zones", liq_count)
        if liq_count > 0:
            st.success("✅ LIQ Confluence")
        else:
            st.info("➖ No LIQ Confluence")
    
    with col4:
        ms_score = analysis['confluence_factors']['market_structure']
        st.metric("🔄 Market Structure", ms_score)
        if ms_score > 0:
            st.success("✅ MS Confluence")
        else:
            st.info("➖ No MS Confluence")
    
    # Trading Scenarios
    st.markdown("""
    <div class="analysis-section">
        <h3>🎯 Trading Scenario & Action Plan</h3>
    </div>
    """, unsafe_allow_html=True)
    
    if signals['primary_signal'] in ['strong_bullish', 'weak_bullish']:
        st.markdown("### 🟢 BULLISH SCENARIO")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown(f"""
            <div class="action-item">
                <h4>📍 Entry Strategy</h4>
                <ul>
                    <li><strong>Entry Price:</strong> {signals['entry_price']:.2f} EGP</li>
                    <li><strong>Entry Type:</strong> Market/Limit Order</li>
                    <li><strong>Confirmation:</strong> Volume increase, bullish reversal</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown(f"""
            <div class="action-item">
                <h4>⛔ Risk Management</h4>
                <ul>
                    <li><strong>Stop Loss:</strong> {signals['stop_loss']:.2f} EGP</li>
                    <li><strong>Risk per Share:</strong> {signals['risk_per_share']:.2f} EGP</li>
                    <li><strong>Max Risk:</strong> {max_risk}% of account</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div class="action-item">
                <h4>🎯 Profit Targets</h4>
                <ul>
                    <li><strong>Target 1:</strong> {signals['take_profit_1']:.2f} EGP (R:R = 1:{signals['rr_ratio_1']:.1f})</li>
                    <li><strong>Target 2:</strong> {signals['take_profit_2']:.2f} EGP (R:R = 1:{signals['rr_ratio_2']:.1f})</li>
                    <li><strong>Partial Close:</strong> 50% at Target 1</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown(f"""
            <div class="action-item">
                <h4>📊 Position Sizing</h4>
                <ul>
                    <li><strong>Conservative (1%):</strong> {pos_1pct['shares']:.0f} shares</li>
                    <li><strong>Moderate (2%):</strong> {pos_2pct['shares']:.0f} shares</li>
                    <li><strong>Position Value:</strong> {pos_2pct['position_value']:.0f} EGP</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)
    
    elif signals['primary_signal'] in ['strong_bearish', 'weak_bearish']:
        st.markdown("### 🔴 BEARISH SCENARIO")
        st.info("🚫 **Note:** Short selling is typically not available for retail traders on EGX. Consider waiting for bullish setups.")
    
    else:
        st.markdown("### ⏳ WAIT FOR BETTER SETUP")
        
        st.markdown(f"""
        <div class="action-item">
            <h4>⏰ Current Recommendation: WAIT</h4>
            <p><strong>Why wait?</strong></p>
            <ul>
                <li>Low confluence score ({analysis['confluence_strength']:.0%})</li>
                <li>Unclear market structure</li>
                <li>Poor risk-reward ratio</li>
                <li>Missing confirmation signals</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    # Risk Warnings
    if signals['rr_ratio_1'] < 1.5:
        st.markdown("""
        <div class="risk-warning">
            <h4>⚠️ Risk Warning</h4>
            <p>Current risk-reward ratio is below 1.5:1. Consider waiting for better setup with higher reward potential.</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Pro Tips
    st.markdown("""
    <div class="pro-tip">
        <h4>💡 Professional Trading Tips</h4>
        <ul>
            <li><strong>Patience Pays:</strong> Wait for high-probability setups with clear confluence</li>
            <li><strong>Risk First:</strong> Never risk more than 1-2% of your account per trade</li>
            <li><strong>Follow Process:</strong> Stick to your SMC analysis and ignore emotions</li>
            <li><strong>Volume Matters:</strong> Always confirm price action with volume</li>
            <li><strong>Multiple Timeframes:</strong> Use higher TF for bias, lower TF for entries</li>
            <li><strong>Journal Everything:</strong> Keep detailed records of all trades and analysis</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)
    
    return analysis, signals
