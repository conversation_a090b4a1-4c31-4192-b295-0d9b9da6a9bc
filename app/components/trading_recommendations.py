"""
Trading Recommendations Component
Generates buy/sell/hold recommendations based on AI predictions and current price
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class TradingRecommendation:
    """Represents a trading recommendation"""
    symbol: str
    current_price: float
    predicted_price: float
    time_horizon: str
    recommendation: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float
    price_change_pct: float
    reasoning: str
    risk_level: str
    target_price: float
    stop_loss: float
    take_profit: float
    recommendation_date: datetime
    model_used: str
    # Enhanced fields
    prediction_interval: Tuple[float, float] = None  # (lower_bound, upper_bound)
    model_agreement: float = 0.0  # How much models agree (0-1)
    market_regime: str = "normal"  # bull, bear, sideways, volatile
    volatility_adjusted_threshold: float = 0.05  # Dynamic threshold
    position_size_pct: float = 0.0  # Recommended position size
    expected_return: float = 0.0  # Risk-adjusted expected return

@dataclass
class RecommendationSettings:
    """Settings for generating recommendations"""
    buy_threshold: float = 0.05  # 5% upside for buy signal
    sell_threshold: float = -0.05  # 5% downside for sell signal
    min_confidence: float = 0.6  # Minimum confidence for recommendations
    risk_tolerance: str = 'medium'  # 'low', 'medium', 'high'
    stop_loss_pct: float = 0.03  # 3% stop loss
    take_profit_multiplier: float = 2.0  # Risk-reward ratio

class TradingRecommendationEngine:
    """Engine for generating trading recommendations"""

    def __init__(self, settings: RecommendationSettings = None):
        self.settings = settings or RecommendationSettings()
        self.market_regimes = {
            'bull': {'buy_multiplier': 0.8, 'sell_multiplier': 1.2},
            'bear': {'buy_multiplier': 1.2, 'sell_multiplier': 0.8},
            'volatile': {'buy_multiplier': 1.5, 'sell_multiplier': 1.5},
            'sideways': {'buy_multiplier': 1.0, 'sell_multiplier': 1.0}
        }
        
    def generate_recommendation(self, current_price: float, predicted_price: float,
                              confidence: float, time_horizon: str, symbol: str,
                              model_used: str = "AI Model") -> TradingRecommendation:
        """
        Generate a trading recommendation based on current and predicted prices

        Args:
            current_price: Current market price
            predicted_price: AI-predicted future price
            confidence: Model confidence (0-1)
            time_horizon: Prediction time horizon
            symbol: Stock symbol
            model_used: Name of the model that generated the prediction

        Returns:
            TradingRecommendation object
        """

        # Ensure no None values
        current_price = current_price or 0.0
        predicted_price = predicted_price or current_price
        confidence = confidence or 0.5
        time_horizon = time_horizon or "1D"
        symbol = symbol or "UNKNOWN"
        model_used = model_used or "AI Model"
        
        # Calculate price change percentage
        price_change_pct = (predicted_price - current_price) / current_price
        
        # Determine recommendation based on thresholds
        recommendation, reasoning = self._determine_recommendation(
            price_change_pct, confidence
        )
        
        # Calculate risk level
        risk_level = self._calculate_risk_level(price_change_pct, confidence)
        
        # Calculate target prices
        target_price, stop_loss, take_profit = self._calculate_targets(
            current_price, predicted_price, recommendation
        )
        
        return TradingRecommendation(
            symbol=symbol,
            current_price=current_price,
            predicted_price=predicted_price,
            time_horizon=time_horizon,
            recommendation=recommendation,
            confidence=confidence,
            price_change_pct=price_change_pct,
            reasoning=reasoning,
            risk_level=risk_level,
            target_price=target_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            recommendation_date=datetime.now(),
            model_used=model_used
        )
    
    def _determine_recommendation(self, price_change_pct: float, 
                                confidence: float) -> Tuple[str, str]:
        """Determine buy/sell/hold recommendation with reasoning"""
        
        # Check confidence threshold
        if confidence < self.settings.min_confidence:
            return "HOLD", f"Low confidence ({confidence:.1%}) - insufficient signal strength"
        
        # Strong buy signal
        if price_change_pct >= self.settings.buy_threshold * 2:
            return "BUY", f"Strong upside potential ({price_change_pct:+.1%}) with high confidence"
        
        # Buy signal
        elif price_change_pct >= self.settings.buy_threshold:
            return "BUY", f"Positive price movement expected ({price_change_pct:+.1%})"
        
        # Strong sell signal
        elif price_change_pct <= self.settings.sell_threshold * 2:
            return "SELL", f"Strong downside risk ({price_change_pct:+.1%}) - consider exit"
        
        # Sell signal
        elif price_change_pct <= self.settings.sell_threshold:
            return "SELL", f"Negative price movement expected ({price_change_pct:+.1%})"
        
        # Hold signal
        else:
            return "HOLD", f"Price movement within neutral range ({price_change_pct:+.1%})"
    
    def _calculate_risk_level(self, price_change_pct: float, confidence: float) -> str:
        """Calculate risk level based on price change and confidence"""
        
        volatility = abs(price_change_pct)
        
        # High risk conditions
        if volatility > 0.1 or confidence < 0.5:
            return "high"
        
        # Low risk conditions
        elif volatility < 0.03 and confidence > 0.8:
            return "low"
        
        # Medium risk (default)
        else:
            return "medium"
    
    def _calculate_targets(self, current_price: float, predicted_price: float,
                          recommendation: str) -> Tuple[float, float, float]:
        """Calculate target price, stop loss, and take profit levels"""
        
        target_price = predicted_price
        
        if recommendation == "BUY":
            # Stop loss below current price
            stop_loss = current_price * (1 - self.settings.stop_loss_pct)
            
            # Take profit based on risk-reward ratio
            risk_amount = current_price - stop_loss
            take_profit = current_price + (risk_amount * self.settings.take_profit_multiplier)
            
        elif recommendation == "SELL":
            # Stop loss above current price
            stop_loss = current_price * (1 + self.settings.stop_loss_pct)
            
            # Take profit below current price
            risk_amount = stop_loss - current_price
            take_profit = current_price - (risk_amount * self.settings.take_profit_multiplier)
            
        else:  # HOLD
            stop_loss = current_price * (1 - self.settings.stop_loss_pct)
            take_profit = current_price * (1 + self.settings.stop_loss_pct)
        
        return target_price, stop_loss, take_profit
    
    def generate_batch_recommendations(self, predictions_data: List[Dict]) -> List[TradingRecommendation]:
        """Generate recommendations for multiple predictions"""
        
        recommendations = []
        
        for pred_data in predictions_data:
            try:
                recommendation = self.generate_recommendation(
                    current_price=pred_data['current_price'],
                    predicted_price=pred_data['predicted_price'],
                    confidence=pred_data['confidence'],
                    time_horizon=pred_data['time_horizon'],
                    symbol=pred_data['symbol'],
                    model_used=pred_data.get('model_used', 'AI Model')
                )
                recommendations.append(recommendation)
                
            except Exception as e:
                logger.error(f"Error generating recommendation for {pred_data.get('symbol', 'unknown')}: {str(e)}")
                continue
        
        return recommendations
    
    def update_settings(self, **kwargs):
        """Update recommendation settings"""
        for key, value in kwargs.items():
            if hasattr(self.settings, key):
                setattr(self.settings, key, value)
    
    def get_recommendation_summary(self, recommendations: List[TradingRecommendation]) -> Dict:
        """Generate summary statistics for a list of recommendations"""
        
        if not recommendations:
            return {}
        
        buy_count = sum(1 for r in recommendations if r.recommendation == "BUY")
        sell_count = sum(1 for r in recommendations if r.recommendation == "SELL")
        hold_count = sum(1 for r in recommendations if r.recommendation == "HOLD")
        
        avg_confidence = np.mean([r.confidence for r in recommendations])
        avg_price_change = np.mean([r.price_change_pct for r in recommendations])
        
        high_risk_count = sum(1 for r in recommendations if r.risk_level == "high")
        medium_risk_count = sum(1 for r in recommendations if r.risk_level == "medium")
        low_risk_count = sum(1 for r in recommendations if r.risk_level == "low")
        
        return {
            'total_recommendations': len(recommendations),
            'buy_signals': buy_count,
            'sell_signals': sell_count,
            'hold_signals': hold_count,
            'avg_confidence': avg_confidence,
            'avg_expected_change': avg_price_change,
            'risk_distribution': {
                'high': high_risk_count,
                'medium': medium_risk_count,
                'low': low_risk_count
            }
        }

    def generate_enhanced_recommendation(self, historical_data: pd.DataFrame,
                                       predictions: List[Dict], symbol: str) -> TradingRecommendation:
        """
        Generate enhanced recommendation with multiple models and market regime detection

        Args:
            historical_data: Historical price data for market analysis
            predictions: List of predictions from different models
            symbol: Stock symbol

        Returns:
            Enhanced TradingRecommendation with advanced features
        """

        if not predictions:
            raise ValueError("No predictions provided")

        current_price = historical_data['close'].iloc[-1]

        # 1. Ensemble prediction with confidence intervals
        ensemble_pred, confidence_interval, model_agreement = self._calculate_ensemble_prediction(predictions)

        # 2. Market regime detection
        market_regime = self._detect_market_regime(historical_data)

        # 3. Dynamic threshold adjustment
        volatility = historical_data['close'].pct_change().std()
        adjusted_threshold = self._calculate_dynamic_threshold(volatility, market_regime)

        # 4. Risk-adjusted position sizing
        position_size = self._calculate_position_size(current_price, ensemble_pred, volatility, model_agreement)

        # 5. Generate recommendation with enhanced logic
        price_change_pct = (ensemble_pred - current_price) / current_price
        recommendation, reasoning = self._determine_enhanced_recommendation(
            price_change_pct, model_agreement, market_regime, adjusted_threshold
        )

        # 6. Calculate enhanced risk metrics
        risk_level = self._calculate_enhanced_risk_level(
            price_change_pct, model_agreement, volatility, market_regime
        )

        # 7. Advanced target calculation
        target_price, stop_loss, take_profit = self._calculate_enhanced_targets(
            current_price, ensemble_pred, recommendation, volatility, market_regime
        )

        # 8. Expected return calculation
        expected_return = self._calculate_expected_return(
            current_price, ensemble_pred, confidence_interval, position_size
        )

        return TradingRecommendation(
            symbol=symbol,
            current_price=current_price,
            predicted_price=ensemble_pred,
            time_horizon="ensemble",
            recommendation=recommendation,
            confidence=model_agreement,
            price_change_pct=price_change_pct,
            reasoning=reasoning,
            risk_level=risk_level,
            target_price=target_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            recommendation_date=datetime.now(),
            model_used="Enhanced Ensemble",
            prediction_interval=confidence_interval,
            model_agreement=model_agreement,
            market_regime=market_regime,
            volatility_adjusted_threshold=adjusted_threshold,
            position_size_pct=position_size,
            expected_return=expected_return
        )

    def _calculate_ensemble_prediction(self, predictions: List[Dict]) -> Tuple[float, Tuple[float, float], float]:
        """Calculate ensemble prediction with confidence intervals"""

        prices = [p['predicted_price'] for p in predictions]
        confidences = [p['confidence'] for p in predictions]

        # Weighted average by confidence
        weights = np.array(confidences) / sum(confidences)
        ensemble_price = np.average(prices, weights=weights)

        # Confidence interval (using standard deviation)
        price_std = np.std(prices)
        lower_bound = ensemble_price - 1.96 * price_std  # 95% confidence
        upper_bound = ensemble_price + 1.96 * price_std

        # Model agreement (inverse of coefficient of variation)
        cv = price_std / ensemble_price if ensemble_price > 0 else 1.0
        model_agreement = max(0.0, 1.0 - cv)

        return ensemble_price, (lower_bound, upper_bound), model_agreement

    def _detect_market_regime(self, data: pd.DataFrame) -> str:
        """
        Detect current market regime using multiple technical indicators
        Returns: 'bull', 'bear', 'volatile', or 'sideways'
        """
        if len(data) < 20:
            return 'normal'  # Default if not enough data
            
        # Calculate key metrics
        returns = data['close'].pct_change().dropna()
        volatility = returns.rolling(window=20).std().iloc[-1]
        sma_20 = data['close'].rolling(window=20).mean()
        sma_50 = data['close'].rolling(window=50).mean()
        
        # Current trend
        current_price = data['close'].iloc[-1]
        trend_20d = (current_price - sma_20.iloc[-1]) / sma_20.iloc[-1]
        trend_50d = (current_price - sma_50.iloc[-1]) / sma_50.iloc[-1]
        
        # Volatility threshold (annualized)
        high_vol_threshold = 0.02  # 2% daily = ~32% annualized
        
        # Regime classification logic
        if volatility > high_vol_threshold:
            return 'volatile'
        elif trend_20d > 0.05 and trend_50d > 0.03:
            return 'bull'
        elif trend_20d < -0.05 and trend_50d < -0.03:
            return 'bear'
        else:
            return 'sideways'

    def _calculate_dynamic_threshold(self, volatility: float, market_regime: str) -> float:
        """Calculate dynamic threshold based on market conditions"""

        base_threshold = self.settings.buy_threshold
        regime_multiplier = self.market_regimes.get(market_regime, {}).get('buy_multiplier', 1.0)

        # Adjust for volatility
        volatility_multiplier = 1.0 + (volatility / 0.02)  # Scale with volatility

        return base_threshold * regime_multiplier * volatility_multiplier

    def _calculate_position_size(self, current_price: float, predicted_price: float,
                               volatility: float, model_agreement: float) -> float:
        """Calculate optimal position size using Kelly Criterion-inspired approach"""

        # Expected return
        expected_return = (predicted_price - current_price) / current_price

        # Risk estimate (volatility adjusted by model agreement)
        risk_estimate = volatility * (2.0 - model_agreement)  # Higher disagreement = higher risk

        # Kelly fraction (simplified)
        if risk_estimate > 0:
            kelly_fraction = max(0.0, expected_return / (risk_estimate ** 2))
        else:
            kelly_fraction = 0.0

        # Cap position size based on risk tolerance
        max_position = {'low': 0.05, 'medium': 0.10, 'high': 0.20}
        max_size = max_position.get(self.settings.risk_tolerance, 0.10)

        return min(kelly_fraction, max_size)

    def _determine_enhanced_recommendation(self, price_change_pct: float, model_agreement: float,
                                         market_regime: str, adjusted_threshold: float) -> Tuple[str, str]:
        """Enhanced recommendation logic with market regime consideration"""

        # Require higher model agreement for stronger signals
        min_agreement = 0.7 if abs(price_change_pct) > adjusted_threshold * 2 else 0.5

        if model_agreement < min_agreement:
            return "HOLD", f"Low model agreement ({model_agreement:.1%}) - conflicting signals"

        # Market regime adjustments
        regime_factor = self.market_regimes.get(market_regime, {}).get('buy_multiplier', 1.0)
        effective_threshold = adjusted_threshold * regime_factor

        if price_change_pct >= effective_threshold:
            strength = "Strong" if price_change_pct >= effective_threshold * 2 else "Moderate"
            return "BUY", f"{strength} buy signal in {market_regime} market ({price_change_pct:+.1%})"
        elif price_change_pct <= -effective_threshold:
            strength = "Strong" if price_change_pct <= -effective_threshold * 2 else "Moderate"
            return "SELL", f"{strength} sell signal in {market_regime} market ({price_change_pct:+.1%})"
        else:
            return "HOLD", f"Neutral signal in {market_regime} market ({price_change_pct:+.1%})"

    def generate_enhanced_recommendations(self, df: pd.DataFrame, predictions_data: List[Dict],
                                       symbol: str, use_kelly: bool = False,
                                       market_regime_detection: bool = True) -> List[TradingRecommendation]:
        """
        Generate enhanced recommendations with advanced features

        Args:
            df: Historical price data for market analysis
            predictions_data: List of prediction data from different models
            symbol: Stock symbol
            use_kelly: Whether to use Kelly criterion for position sizing
            market_regime_detection: Whether to use market regime detection

        Returns:
            List of enhanced TradingRecommendation objects
        """
        try:
            recommendations = []
            current_price = df['close'].iloc[-1]
            returns = df['close'].pct_change().dropna()
            volatility = returns.std()

            # Market regime analysis
            trend = (df['close'].iloc[-1] - df['close'].iloc[-20]) / df['close'].iloc[-20]
            if volatility > 0.03:
                market_regime = 'volatile'
            elif trend > 0.05:
                market_regime = 'bull'
            elif trend < -0.05:
                market_regime = 'bear'
            else:
                market_regime = 'sideways'

            for pred_data in predictions_data:
                # Base calculations with None safety
                predicted_price = pred_data.get('predicted_price') or current_price
                price_change = predicted_price - current_price
                price_change_pct = price_change / current_price if current_price != 0 else 0.0

                # Calculate base confidence using model agreement if available
                if 'model_agreement' in pred_data and pred_data['model_agreement'] is not None:
                    base_confidence = pred_data['model_agreement']
                else:
                    base_confidence = pred_data.get('confidence') or 0.5

                # Adjust confidence based on market conditions
                volatility_factor = max(0.7, 1 - (volatility * 3))  # Reduce confidence in high volatility
                regime_factor = {
                    'volatile': 0.8,
                    'sideways': 0.9,
                    'bull': 0.95,
                    'bear': 0.95
                }.get(market_regime, 0.9)

                # Calculate adjusted confidence (ensure it's realistic)
                confidence = min(0.95, base_confidence * volatility_factor * regime_factor)
                confidence = max(0.55, confidence)  # Set minimum confidence

                # Generate recommendation
                recommendation, reasoning = self._determine_recommendation(
                    price_change_pct, confidence
                )

                # Risk level based on multiple factors
                risk_level = self._calculate_enhanced_risk_level(
                    price_change_pct, confidence, volatility, market_regime
                )

                # Calculate targets
                target_price, stop_loss, take_profit = self._calculate_targets(
                    current_price, predicted_price, recommendation
                )

                # Calculate position size if requested
                position_size = None
                if use_kelly and recommendation != "HOLD":
                    win_rate = confidence
                    win_amount = abs(take_profit - current_price)
                    loss_amount = abs(current_price - stop_loss)
                    kelly_fraction = self._calculate_kelly_fraction(win_rate, win_amount, loss_amount)
                    position_size = min(kelly_fraction, 0.25)  # Cap at 25%

                # Create recommendation with safe defaults
                rec = TradingRecommendation(
                    symbol=symbol or "UNKNOWN",
                    current_price=current_price or 0.0,
                    predicted_price=predicted_price or current_price or 0.0,
                    time_horizon=pred_data.get('time_horizon') or '1D',
                    recommendation=recommendation or "HOLD",
                    confidence=confidence or 0.5,
                    price_change_pct=price_change_pct or 0.0,
                    reasoning=reasoning or "No reasoning available",
                    risk_level=risk_level or "medium",
                    target_price=target_price or current_price or 0.0,
                    stop_loss=stop_loss or current_price or 0.0,
                    take_profit=take_profit or current_price or 0.0,
                    recommendation_date=datetime.now(),
                    model_used=pred_data.get('model_used') or 'AI Model',
                    model_agreement=pred_data.get('model_agreement') or confidence or 0.5,
                    market_regime=market_regime or 'normal',
                    volatility_adjusted_threshold=pred_data.get('volatility_adjusted_threshold') or 0.05,
                    position_size_pct=position_size or 0.0
                )

                recommendations.append(rec)

            return recommendations

        except Exception as e:
            logger.error(f"Error generating enhanced recommendations: {str(e)}")
            return []

    def _calculate_enhanced_risk_level(self, price_change_pct: float, confidence: float,
                                     volatility: float, market_regime: str) -> str:
        """Calculate risk level based on multiple factors"""
        
        # Base risk from price change
        price_risk = abs(price_change_pct)
        
        # Adjust for market conditions
        regime_risk_factor = {
            'volatile': 1.3,
            'bull': 0.9,
            'bear': 1.1,
            'sideways': 0.8
        }.get(market_regime, 1.0)
        
        # Combined risk score
        risk_score = (price_risk * 2 + volatility * 3) * regime_risk_factor * (1 - confidence)
        
        # Determine risk level
        if risk_score < 0.05:
            return 'low'
        elif risk_score < 0.12:
            return 'medium'
        else:
            return 'high'

    def _calculate_kelly_fraction(self, win_rate: float, win_amount: float, loss_amount: float) -> float:
        """Calculate optimal position size using Kelly Criterion"""
        if loss_amount <= 0 or win_amount <= 0:
            return 0
        
        b = win_amount / loss_amount
        return max(0, min((b * win_rate - (1 - win_rate)) / b, 1.0))
