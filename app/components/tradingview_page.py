"""
TradingView Chart Page Component - A dedicated component for displaying TradingView charts
"""
import streamlit as st
import streamlit.components.v1 as components
import logging
import pandas as pd
from app.utils.common import load_stock_data, get_available_stocks

# Configure logging
logger = logging.getLogger(__name__)

def tradingview_page_component():
    """
    Display a dedicated TradingView chart page component
    """
    st.title("TradingView Advanced Chart")
    st.write("This dedicated page provides a full-featured TradingView chart for EGX stocks.")

    # Get available stocks
    stock_symbols = get_available_stocks()

    # Create horizontal controls at the top
    st.subheader("Chart Controls")

    # Create a 4-column layout for the controls
    col1, col2, col3, col4, col5 = st.columns([1, 1, 1, 1, 1])

    with col1:
        # Stock selection
        selected_symbol = st.selectbox(
            "Select Stock Symbol",
            options=stock_symbols,
            index=stock_symbols.index("COMI") if "COMI" in stock_symbols else 0,
            key="tradingview_stock_select"
        )

    with col2:
        # Chart theme
        theme = st.selectbox(
            "Chart Theme",
            options=["light", "dark"],
            index=1,
            key="tradingview_theme"
        )

    with col3:
        # Chart interval
        interval = st.selectbox(
            "Time Interval",
            options=["1", "5", "15", "30", "60", "D", "W", "M"],
            index=5,
            format_func=lambda x: {
                "1": "1 Minute",
                "5": "5 Minutes",
                "15": "15 Minutes",
                "30": "30 Minutes",
                "60": "1 Hour",
                "D": "1 Day",
                "W": "1 Week",
                "M": "1 Month"
            }.get(x, x),
            key="tradingview_interval"
        )

    with col4:
        # Chart style
        chart_style = st.selectbox(
            "Chart Style",
            options=["1", "2", "3", "4", "5", "6", "7", "8", "9"],
            index=0,
            format_func=lambda x: {
                "1": "Bars",
                "2": "Candles",
                "3": "Hollow Candles",
                "4": "Heikin Ashi",
                "5": "Line",
                "6": "Area",
                "7": "Renko",
                "8": "Kagi",
                "9": "Point & Figure"
            }.get(x, x),
            key="tradingview_style"
        )

    with col5:
        # Add a button to refresh the chart
        st.write("&nbsp;", unsafe_allow_html=True)  # Add some spacing
        if st.button("Refresh Chart", type="primary", key="tradingview_refresh", use_container_width=True):
            st.rerun()

    # Main content area - TradingView Chart
    st.subheader(f"TradingView Chart for {selected_symbol}")

    # Create the TradingView widget HTML
    tradingview_html = f"""
        <!-- TradingView Widget BEGIN -->
        <div id="tradingview_widget_container"></div>
        <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
        <script type="text/javascript">
          new TradingView.widget({{
            "container_id": "tradingview_widget_container",
            "width": "100%",
            "height": 700,
            "symbol": "EGX:{selected_symbol}",
            "interval": "{interval}",
            "timezone": "Etc/UTC",
            "theme": "{theme}",
            "style": "{chart_style}",
            "locale": "en",
            "toolbar_bg": "#f1f3f6",
            "enable_publishing": false,
            "hide_side_toolbar": false,
            "allow_symbol_change": true,
            "studies": [],
            "details": true,
            "hotlist": false,
            "calendar": false,
            "news": ["headlines"]
          }});
        </script>
        <!-- TradingView Widget END -->
        """

    # Render the TradingView widget
    components.html(tradingview_html, height=750)

    # Add a note about troubleshooting
    st.caption("If the chart appears black, try clicking the refresh button or switching to a different tab and back.")

    # Add a link to open in TradingView
    st.markdown(f"[Open in TradingView](https://www.tradingview.com/chart/?symbol=EGX:{selected_symbol})")

    # Add some stock information
    try:
        stock_data = load_stock_data(selected_symbol)
        if stock_data is not None and not stock_data.empty:
            st.subheader("Recent Stock Data")
            st.dataframe(stock_data.tail(5))
    except Exception as e:
        logger.error(f"Error loading stock data: {str(e)}")
        st.error("Could not load recent stock data.")
