"""
Portfolio management component for the AI Stocks Bot app
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import os
import logging
import json
from typing import Dict, List, Optional, Union, Tuple, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Portfolio:
    """
    Portfolio class for managing stock holdings
    """
    
    def __init__(self, initial_capital: float = 10000.0):
        """
        Initialize the portfolio
        
        Args:
            initial_capital (float): Initial capital
        """
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.holdings = {}  # Symbol -> {shares, avg_price}
        self.transactions = []  # List of transactions
    
    def buy(self, symbol: str, shares: float, price: float, date: datetime = None) -> bool:
        """
        Buy shares of a stock
        
        Args:
            symbol (str): Stock symbol
            shares (float): Number of shares to buy
            price (float): Price per share
            date (datetime, optional): Transaction date. Defaults to current datetime.
            
        Returns:
            bool: True if successful, False otherwise
        """
        if date is None:
            date = datetime.now()
        
        total_cost = shares * price
        
        if total_cost > self.cash:
            logger.warning(f"Insufficient funds to buy {shares} shares of {symbol} at ${price:.2f}")
            return False
        
        # Update cash
        self.cash -= total_cost
        
        # Update holdings
        if symbol in self.holdings:
            # Calculate new average price
            current_shares = self.holdings[symbol]['shares']
            current_avg_price = self.holdings[symbol]['avg_price']
            
            new_total_shares = current_shares + shares
            new_avg_price = (current_shares * current_avg_price + shares * price) / new_total_shares
            
            self.holdings[symbol] = {
                'shares': new_total_shares,
                'avg_price': new_avg_price
            }
        else:
            self.holdings[symbol] = {
                'shares': shares,
                'avg_price': price
            }
        
        # Record transaction
        self.transactions.append({
            'date': date,
            'type': 'buy',
            'symbol': symbol,
            'shares': shares,
            'price': price,
            'total': total_cost
        })
        
        logger.info(f"Bought {shares} shares of {symbol} at ${price:.2f}")
        return True
    
    def sell(self, symbol: str, shares: float, price: float, date: datetime = None) -> bool:
        """
        Sell shares of a stock
        
        Args:
            symbol (str): Stock symbol
            shares (float): Number of shares to sell
            price (float): Price per share
            date (datetime, optional): Transaction date. Defaults to current datetime.
            
        Returns:
            bool: True if successful, False otherwise
        """
        if date is None:
            date = datetime.now()
        
        if symbol not in self.holdings:
            logger.warning(f"No shares of {symbol} in portfolio")
            return False
        
        if shares > self.holdings[symbol]['shares']:
            logger.warning(f"Insufficient shares of {symbol} to sell")
            return False
        
        # Calculate proceeds
        proceeds = shares * price
        
        # Update cash
        self.cash += proceeds
        
        # Update holdings
        remaining_shares = self.holdings[symbol]['shares'] - shares
        
        if remaining_shares > 0:
            self.holdings[symbol]['shares'] = remaining_shares
        else:
            del self.holdings[symbol]
        
        # Record transaction
        self.transactions.append({
            'date': date,
            'type': 'sell',
            'symbol': symbol,
            'shares': shares,
            'price': price,
            'total': proceeds
        })
        
        logger.info(f"Sold {shares} shares of {symbol} at ${price:.2f}")
        return True
    
    def get_portfolio_value(self, prices: Dict[str, float] = None) -> float:
        """
        Get the current portfolio value
        
        Args:
            prices (Dict[str, float], optional): Dictionary of current prices for each symbol.
                If not provided, uses the average purchase price.
                
        Returns:
            float: Total portfolio value (cash + holdings)
        """
        holdings_value = 0.0
        
        for symbol, holding in self.holdings.items():
            if prices is not None and symbol in prices:
                price = prices[symbol]
            else:
                price = holding['avg_price']
            
            holdings_value += holding['shares'] * price
        
        return self.cash + holdings_value
    
    def get_portfolio_summary(self, prices: Dict[str, float] = None) -> pd.DataFrame:
        """
        Get a summary of the portfolio
        
        Args:
            prices (Dict[str, float], optional): Dictionary of current prices for each symbol.
                If not provided, uses the average purchase price.
                
        Returns:
            pd.DataFrame: Portfolio summary
        """
        if not self.holdings:
            return pd.DataFrame(columns=['Symbol', 'Shares', 'Avg Price', 'Current Price', 'Market Value', 'Gain/Loss', 'Gain/Loss %'])
        
        data = []
        
        for symbol, holding in self.holdings.items():
            if prices is not None and symbol in prices:
                current_price = prices[symbol]
            else:
                current_price = holding['avg_price']
            
            shares = holding['shares']
            avg_price = holding['avg_price']
            market_value = shares * current_price
            gain_loss = market_value - (shares * avg_price)
            gain_loss_pct = (gain_loss / (shares * avg_price)) * 100
            
            data.append({
                'Symbol': symbol,
                'Shares': shares,
                'Avg Price': avg_price,
                'Current Price': current_price,
                'Market Value': market_value,
                'Gain/Loss': gain_loss,
                'Gain/Loss %': gain_loss_pct
            })
        
        return pd.DataFrame(data)
    
    def get_transaction_history(self) -> pd.DataFrame:
        """
        Get the transaction history
        
        Returns:
            pd.DataFrame: Transaction history
        """
        if not self.transactions:
            return pd.DataFrame(columns=['Date', 'Type', 'Symbol', 'Shares', 'Price', 'Total'])
        
        return pd.DataFrame(self.transactions)
    
    def get_performance_history(self, prices_history: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Get the portfolio performance history
        
        Args:
            prices_history (Dict[str, pd.DataFrame]): Dictionary of price history for each symbol
                Each DataFrame should have a 'Date' column and a 'Close' column
                
        Returns:
            pd.DataFrame: Portfolio performance history
        """
        if not self.transactions:
            return pd.DataFrame(columns=['Date', 'Portfolio Value'])
        
        # Get the earliest and latest dates
        earliest_date = min(t['date'] for t in self.transactions)
        latest_date = max(
            max(t['date'] for t in self.transactions),
            max(df['Date'].max() for df in prices_history.values() if not df.empty)
        )
        
        # Create a date range
        date_range = pd.date_range(earliest_date, latest_date)
        
        # Initialize portfolio history
        portfolio_history = pd.DataFrame({
            'Date': date_range,
            'Cash': self.initial_capital,
            'Holdings Value': 0.0,
            'Portfolio Value': self.initial_capital
        })
        
        # Simulate portfolio value over time
        holdings = {}
        cash = self.initial_capital
        
        for i, date in enumerate(date_range):
            # Apply transactions for this date
            for transaction in self.transactions:
                if transaction['date'].date() <= date.date() and (i == 0 or transaction['date'].date() > date_range[i-1].date()):
                    if transaction['type'] == 'buy':
                        symbol = transaction['symbol']
                        shares = transaction['shares']
                        price = transaction['price']
                        
                        if symbol in holdings:
                            holdings[symbol] += shares
                        else:
                            holdings[symbol] = shares
                        
                        cash -= shares * price
                    
                    elif transaction['type'] == 'sell':
                        symbol = transaction['symbol']
                        shares = transaction['shares']
                        price = transaction['price']
                        
                        if symbol in holdings:
                            holdings[symbol] = max(0, holdings[symbol] - shares)
                            if holdings[symbol] == 0:
                                del holdings[symbol]
                        
                        cash += shares * price
            
            # Calculate holdings value for this date
            holdings_value = 0.0
            
            for symbol, shares in holdings.items():
                if symbol in prices_history:
                    # Find the closest price date
                    price_df = prices_history[symbol]
                    price_df['Date'] = pd.to_datetime(price_df['Date'])
                    
                    # Get price on or before the current date
                    price_on_date = price_df[price_df['Date'] <= date]
                    
                    if not price_on_date.empty:
                        price = price_on_date.iloc[-1]['Close']
                        holdings_value += shares * price
            
            # Update portfolio history
            portfolio_history.loc[i, 'Cash'] = cash
            portfolio_history.loc[i, 'Holdings Value'] = holdings_value
            portfolio_history.loc[i, 'Portfolio Value'] = cash + holdings_value
        
        return portfolio_history
    
    def save(self, file_path: str) -> bool:
        """
        Save the portfolio to a file
        
        Args:
            file_path (str): Path to save the portfolio
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert datetime objects to strings
            transactions = []
            for t in self.transactions:
                t_copy = t.copy()
                t_copy['date'] = t_copy['date'].strftime('%Y-%m-%d %H:%M:%S')
                transactions.append(t_copy)
            
            portfolio_data = {
                'initial_capital': self.initial_capital,
                'cash': self.cash,
                'holdings': self.holdings,
                'transactions': transactions
            }
            
            with open(file_path, 'w') as f:
                json.dump(portfolio_data, f, indent=4)
            
            logger.info(f"Portfolio saved to {file_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error saving portfolio: {str(e)}")
            return False
    
    @classmethod
    def load(cls, file_path: str) -> 'Portfolio':
        """
        Load a portfolio from a file
        
        Args:
            file_path (str): Path to the portfolio file
            
        Returns:
            Portfolio: Loaded portfolio
        """
        try:
            with open(file_path, 'r') as f:
                portfolio_data = json.load(f)
            
            portfolio = cls(portfolio_data['initial_capital'])
            portfolio.cash = portfolio_data['cash']
            portfolio.holdings = portfolio_data['holdings']
            
            # Convert string dates back to datetime objects
            transactions = []
            for t in portfolio_data['transactions']:
                t_copy = t.copy()
                t_copy['date'] = datetime.strptime(t_copy['date'], '%Y-%m-%d %H:%M:%S')
                transactions.append(t_copy)
            
            portfolio.transactions = transactions
            
            logger.info(f"Portfolio loaded from {file_path}")
            return portfolio
        
        except Exception as e:
            logger.error(f"Error loading portfolio: {str(e)}")
            return cls()


def portfolio_component():
    """
    Streamlit component for portfolio management
    """
    st.header("Portfolio Management")
    
    # Initialize portfolio in session state if not already there
    if 'portfolio' not in st.session_state:
        st.session_state.portfolio = Portfolio()
    
    # Create tabs for different portfolio views
    tab1, tab2, tab3, tab4 = st.tabs(["Summary", "Holdings", "Transactions", "Performance"])
    
    with tab1:
        st.subheader("Portfolio Summary")
        
        # Display portfolio value
        portfolio = st.session_state.portfolio
        
        # Get current prices (in a real app, you would fetch these from an API)
        current_prices = {}
        for symbol in portfolio.holdings.keys():
            if 'historical_data' in st.session_state and st.session_state.historical_data is not None:
                if st.session_state.symbol == symbol:
                    current_prices[symbol] = st.session_state.historical_data['Close'].iloc[-1]
        
        portfolio_value = portfolio.get_portfolio_value(current_prices)
        initial_capital = portfolio.initial_capital
        gain_loss = portfolio_value - initial_capital
        gain_loss_pct = (gain_loss / initial_capital) * 100 if initial_capital > 0 else 0
        
        # Create columns for metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Portfolio Value", f"${portfolio_value:.2f}", f"{gain_loss_pct:.2f}%")
        
        with col2:
            st.metric("Cash", f"${portfolio.cash:.2f}")
        
        with col3:
            holdings_value = portfolio_value - portfolio.cash
            st.metric("Holdings Value", f"${holdings_value:.2f}")
        
        # Display asset allocation
        if portfolio.holdings:
            st.subheader("Asset Allocation")
            
            # Calculate allocation percentages
            allocation_data = []
            
            for symbol, holding in portfolio.holdings.items():
                if symbol in current_prices:
                    price = current_prices[symbol]
                else:
                    price = holding['avg_price']
                
                value = holding['shares'] * price
                percentage = (value / holdings_value) * 100 if holdings_value > 0 else 0
                
                allocation_data.append({
                    'Symbol': symbol,
                    'Value': value,
                    'Percentage': percentage
                })
            
            allocation_df = pd.DataFrame(allocation_data)
            
            # Create a pie chart
            if not allocation_df.empty:
                fig = px.pie(
                    allocation_df,
                    values='Value',
                    names='Symbol',
                    title='Asset Allocation',
                    hover_data=['Percentage'],
                    labels={'Value': 'Value ($)', 'Percentage': 'Allocation (%)'}
                )
                
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No holdings in portfolio")
    
    with tab2:
        st.subheader("Portfolio Holdings")
        
        # Display holdings
        portfolio = st.session_state.portfolio
        
        # Get current prices (in a real app, you would fetch these from an API)
        current_prices = {}
        for symbol in portfolio.holdings.keys():
            if 'historical_data' in st.session_state and st.session_state.historical_data is not None:
                if st.session_state.symbol == symbol:
                    current_prices[symbol] = st.session_state.historical_data['Close'].iloc[-1]
        
        holdings_df = portfolio.get_portfolio_summary(current_prices)
        
        if not holdings_df.empty:
            # Format the dataframe for display
            formatted_df = holdings_df.copy()
            formatted_df['Shares'] = formatted_df['Shares'].map('{:.2f}'.format)
            formatted_df['Avg Price'] = formatted_df['Avg Price'].map('${:.2f}'.format)
            formatted_df['Current Price'] = formatted_df['Current Price'].map('${:.2f}'.format)
            formatted_df['Market Value'] = formatted_df['Market Value'].map('${:.2f}'.format)
            formatted_df['Gain/Loss'] = formatted_df['Gain/Loss'].map('${:.2f}'.format)
            formatted_df['Gain/Loss %'] = formatted_df['Gain/Loss %'].map('{:.2f}%'.format)
            
            st.dataframe(formatted_df, use_container_width=True)
            
            # Add buy/sell functionality
            st.subheader("Trade Stocks")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Buy Stock")
                
                # Get available stocks
                available_stocks = []
                if 'historical_data' in st.session_state and st.session_state.historical_data is not None:
                    available_stocks.append(st.session_state.symbol)
                
                buy_symbol = st.selectbox("Select Stock to Buy", options=available_stocks, key="buy_symbol")
                
                if buy_symbol:
                    # Get current price
                    if buy_symbol in current_prices:
                        buy_price = current_prices[buy_symbol]
                    else:
                        buy_price = 0.0
                    
                    st.write(f"Current Price: ${buy_price:.2f}")
                    
                    # Input fields
                    buy_shares = st.number_input("Shares to Buy", min_value=0.01, value=1.0, step=0.01, key="buy_shares")
                    buy_total = buy_shares * buy_price
                    
                    st.write(f"Total Cost: ${buy_total:.2f}")
                    
                    # Buy button
                    if st.button("Buy"):
                        if portfolio.buy(buy_symbol, buy_shares, buy_price):
                            st.success(f"Bought {buy_shares} shares of {buy_symbol} at ${buy_price:.2f}")
                        else:
                            st.error("Insufficient funds")
            
            with col2:
                st.subheader("Sell Stock")
                
                # Get stocks in portfolio
                portfolio_stocks = list(portfolio.holdings.keys())
                
                if portfolio_stocks:
                    sell_symbol = st.selectbox("Select Stock to Sell", options=portfolio_stocks, key="sell_symbol")
                    
                    if sell_symbol:
                        # Get current price and shares owned
                        if sell_symbol in current_prices:
                            sell_price = current_prices[sell_symbol]
                        else:
                            sell_price = portfolio.holdings[sell_symbol]['avg_price']
                        
                        shares_owned = portfolio.holdings[sell_symbol]['shares']
                        
                        st.write(f"Current Price: ${sell_price:.2f}")
                        st.write(f"Shares Owned: {shares_owned:.2f}")
                        
                        # Input fields
                        sell_shares = st.number_input("Shares to Sell", min_value=0.01, max_value=shares_owned, value=min(1.0, shares_owned), step=0.01, key="sell_shares")
                        sell_total = sell_shares * sell_price
                        
                        st.write(f"Total Proceeds: ${sell_total:.2f}")
                        
                        # Sell button
                        if st.button("Sell"):
                            if portfolio.sell(sell_symbol, sell_shares, sell_price):
                                st.success(f"Sold {sell_shares} shares of {sell_symbol} at ${sell_price:.2f}")
                            else:
                                st.error("Error selling shares")
                else:
                    st.info("No stocks in portfolio to sell")
        else:
            st.info("No holdings in portfolio")
            
            # Add buy functionality
            st.subheader("Buy Stock")
            
            # Get available stocks
            available_stocks = []
            if 'historical_data' in st.session_state and st.session_state.historical_data is not None:
                available_stocks.append(st.session_state.symbol)
            
            buy_symbol = st.selectbox("Select Stock to Buy", options=available_stocks)
            
            if buy_symbol:
                # Get current price
                if 'historical_data' in st.session_state and st.session_state.historical_data is not None:
                    buy_price = st.session_state.historical_data['Close'].iloc[-1]
                else:
                    buy_price = 0.0
                
                st.write(f"Current Price: ${buy_price:.2f}")
                
                # Input fields
                buy_shares = st.number_input("Shares to Buy", min_value=0.01, value=1.0, step=0.01)
                buy_total = buy_shares * buy_price
                
                st.write(f"Total Cost: ${buy_total:.2f}")
                
                # Buy button
                if st.button("Buy"):
                    if portfolio.buy(buy_symbol, buy_shares, buy_price):
                        st.success(f"Bought {buy_shares} shares of {buy_symbol} at ${buy_price:.2f}")
                    else:
                        st.error("Insufficient funds")
    
    with tab3:
        st.subheader("Transaction History")
        
        # Display transactions
        portfolio = st.session_state.portfolio
        transactions_df = portfolio.get_transaction_history()
        
        if not transactions_df.empty:
            # Format the dataframe for display
            formatted_df = transactions_df.copy()
            formatted_df['Date'] = formatted_df['Date'].dt.strftime('%Y-%m-%d %H:%M:%S')
            formatted_df['Type'] = formatted_df['Type'].str.capitalize()
            formatted_df['Shares'] = formatted_df['Shares'].map('{:.2f}'.format)
            formatted_df['Price'] = formatted_df['Price'].map('${:.2f}'.format)
            formatted_df['Total'] = formatted_df['Total'].map('${:.2f}'.format)
            
            st.dataframe(formatted_df, use_container_width=True)
        else:
            st.info("No transactions in portfolio")
    
    with tab4:
        st.subheader("Portfolio Performance")
        
        # Display performance
        portfolio = st.session_state.portfolio
        
        if not portfolio.transactions:
            st.info("No transactions in portfolio")
            return
        
        # Get price history for holdings
        prices_history = {}
        
        for symbol in set([t['symbol'] for t in portfolio.transactions]):
            if 'historical_data' in st.session_state and st.session_state.historical_data is not None:
                if st.session_state.symbol == symbol:
                    prices_history[symbol] = st.session_state.historical_data[['Date', 'Close']]
        
        if not prices_history:
            st.warning("No price history available for portfolio holdings")
            return
        
        # Calculate performance history
        performance_df = portfolio.get_performance_history(prices_history)
        
        # Create performance chart
        fig = go.Figure()
        
        # Add portfolio value line
        fig.add_trace(go.Scatter(
            x=performance_df['Date'],
            y=performance_df['Portfolio Value'],
            mode='lines',
            name='Portfolio Value',
            line=dict(color='green')
        ))
        
        # Add initial capital line
        fig.add_trace(go.Scatter(
            x=performance_df['Date'],
            y=[portfolio.initial_capital] * len(performance_df),
            mode='lines',
            name='Initial Capital',
            line=dict(color='red', dash='dash')
        ))
        
        # Update layout
        fig.update_layout(
            title='Portfolio Performance',
            xaxis_title='Date',
            yaxis_title='Value ($)',
            hovermode='x unified',
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Calculate performance metrics
        initial_value = performance_df['Portfolio Value'].iloc[0]
        final_value = performance_df['Portfolio Value'].iloc[-1]
        
        total_return = (final_value / initial_value - 1) * 100
        
        # Calculate drawdown
        performance_df['Peak'] = performance_df['Portfolio Value'].cummax()
        performance_df['Drawdown'] = (performance_df['Portfolio Value'] - performance_df['Peak']) / performance_df['Peak'] * 100
        max_drawdown = performance_df['Drawdown'].min()
        
        # Display metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Return", f"{total_return:.2f}%")
        
        with col2:
            st.metric("Maximum Drawdown", f"{max_drawdown:.2f}%")
        
        with col3:
            # Calculate annualized return
            days = (performance_df['Date'].iloc[-1] - performance_df['Date'].iloc[0]).days
            if days > 0:
                years = days / 365.0
                annualized_return = ((1 + total_return / 100) ** (1 / years) - 1) * 100
                st.metric("Annualized Return", f"{annualized_return:.2f}%")
            else:
                st.metric("Annualized Return", "N/A")
    
    # Add save/load functionality
    st.sidebar.subheader("Portfolio Actions")
    
    # Save portfolio
    if st.sidebar.button("Save Portfolio"):
        portfolio_dir = "portfolios"
        os.makedirs(portfolio_dir, exist_ok=True)
        
        file_path = os.path.join(portfolio_dir, "portfolio.json")
        
        if st.session_state.portfolio.save(file_path):
            st.sidebar.success(f"Portfolio saved to {file_path}")
        else:
            st.sidebar.error("Error saving portfolio")
    
    # Load portfolio
    if st.sidebar.button("Load Portfolio"):
        portfolio_dir = "portfolios"
        file_path = os.path.join(portfolio_dir, "portfolio.json")
        
        if os.path.exists(file_path):
            portfolio = Portfolio.load(file_path)
            st.session_state.portfolio = portfolio
            st.sidebar.success("Portfolio loaded successfully")
        else:
            st.sidebar.error("No saved portfolio found")
    
    # Reset portfolio
    if st.sidebar.button("Reset Portfolio"):
        st.session_state.portfolio = Portfolio()
        st.sidebar.success("Portfolio reset to initial state")
