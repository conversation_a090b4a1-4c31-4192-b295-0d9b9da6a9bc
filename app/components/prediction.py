import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Optional, Dict
import os
import sys
import logging

# Import performance metrics tracking
try:
    from app.components.performance_metrics import track_prediction
except ImportError:
    # Define a dummy function if the module is not available
    def track_prediction(symbol, predictions, horizon_unit, model_type):
        pass

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import logging
import logging
logger = logging.getLogger(__name__)

def check_api_server_status():
    """Check if TradingView API server is running"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_price_from_api_server(symbol: str) -> Optional[Dict]:
    """Fetch price data from TradingView API server"""
    try:
        import requests
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                # Extract price from the API response
                raw_price = stock_data[0].get('price', 0)

                # Convert from piasters to EGP (divide by 1000)
                # API returns prices in piasters (81500 = 81.50 EGP)
                price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                return {
                    'symbol': symbol,
                    'price': price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'api_data': stock_data[0]  # Store full API data for advanced analysis
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching from API server: {str(e)}")
        return None

def safe_get_live_data_prediction(symbol: str) -> Optional[pd.DataFrame]:
    """Safely fetch live data with comprehensive error handling and API server integration"""
    try:
        from scrapers.price_scraper import PriceScraper
        from datetime import datetime

        with st.spinner("Fetching latest price data..."):
            # Check if API server is available
            api_status = check_api_server_status()

            if api_status:
                st.info("🔥 Using TradingView API server for enhanced data")
                price_data = fetch_price_from_api_server(symbol)
                source_used = "TradingView API"
            else:
                st.info("📡 Using direct TradingView scraping")
                scraper = PriceScraper(source="tradingview")
                try:
                    price_data = scraper.get_price(symbol)
                    source_used = "TradingView Direct"
                finally:
                    scraper.close_driver()

            if price_data and isinstance(price_data, dict):
                # Convert scraper data format to prediction-compatible format
                current_time = datetime.now()
                price = price_data.get('price', 0)

                # Create DataFrame with required columns for predictions
                converted_data = {
                    'Date': current_time,
                    'Open': price,
                    'High': price * 1.001,  # Simulate small high variation
                    'Low': price * 0.999,   # Simulate small low variation
                    'Close': price,         # Main price for predictions
                    'Volume': 1000000,      # Default volume
                    'symbol': price_data.get('symbol', symbol),
                    'currency': price_data.get('currency', 'EGP'),
                    'timestamp': price_data.get('timestamp', current_time.isoformat()),
                    'source': source_used,
                    'real_time': price_data.get('real_time', False)
                }

                df = pd.DataFrame([converted_data])

                # Ensure Date is datetime
                df['Date'] = pd.to_datetime(df['Date'])

                # Check if this is sample data
                is_sample = price_data.get('source', '').lower() == 'sample data'
                is_real_time = price_data.get('real_time', False)
                is_api = source_used == "TradingView API"

                if is_sample:
                    st.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                elif is_api:
                    st.success(f"✅ Price fetched from TradingView API: {price:.2f} EGP")
                    st.info("🔥 Enhanced data with technical analysis available")
                elif is_real_time:
                    st.success(f"🔴 Real-time price fetched: {price:.2f} EGP")
                else:
                    st.success(f"⏱️ Live price fetched: {price:.2f} EGP (15-min delay)")

                # Store in session state
                if 'live_data' not in st.session_state:
                    st.session_state.live_data = df
                else:
                    st.session_state.live_data = pd.concat([st.session_state.live_data, df], ignore_index=True)

                return df
            else:
                st.warning("⚠️ Could not fetch live price data")
                return None

    except Exception as e:
        st.warning(f"⚠️ Error fetching live data: {str(e)}")
        logger.error(f"Live data fetch error: {str(e)}")
        return None

# Import both original and enhanced prediction modules
from models.predict import predict_from_live_data
# Use the enhanced prediction module when available
try:
    from app.models.predict import predict_future_prices
    from app.models.performance import get_model_performance, compare_models
    ENHANCED_PREDICTIONS_AVAILABLE = True
    logger.info("Using enhanced prediction module with adaptive model selection")
except ImportError:
    from models.predict import predict_future_prices
    ENHANCED_PREDICTIONS_AVAILABLE = False
    logger.info("Enhanced prediction module not available, using original module")
from app.models.model_factory import ModelFactory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def prediction_component(historical_data, live_data, symbol):
    """
    Streamlit component for displaying predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        symbol (str): Stock symbol
    """
    st.subheader("Price Predictions")

    if historical_data is None:
        st.warning("Please upload historical data first")
        return

    if symbol is None or symbol == "":
        st.warning("Please provide a stock symbol")
        return

    # Model type selection
    available_models = ModelFactory.get_available_models()
    model_type = st.selectbox(
        "Select model type",
        options=list(available_models.keys()),
        index=0,
        format_func=lambda x: available_models[x]
    )

    # Show model description
    st.info(ModelFactory.get_model_description(model_type))

    # Prediction timeframe selection
    horizon_options = [
        "Short-term (minutes)",
        "Medium-term (days)",
        "Long-term (weeks)"
    ]
    horizon_type = st.radio("Select prediction timeframe", horizon_options)

    if horizon_type == "Short-term (minutes)":
        horizons = [5, 15, 30, 60]
        horizon_unit = "minutes"
        # For short-term, we'll use the existing single-point prediction
        num_points = 1
    elif horizon_type == "Medium-term (days)":
        horizons = [1, 2, 3, 5, 7]
        horizon_unit = "days"
        # For medium-term, we'll predict multiple points (one per day)
        num_points = st.slider("Number of prediction points", min_value=1, max_value=7, value=5)
    else:  # Long-term
        horizons = [1, 2, 4, 8, 12]
        horizon_unit = "weeks"
        # For long-term, we'll predict multiple points (one per week)
        num_points = st.slider("Number of prediction points", min_value=1, max_value=12, value=4)

    selected_horizons = st.multiselect(
        f"Select prediction horizons ({horizon_unit})",
        options=horizons,
        default=[horizons[0]]
    )

    if not selected_horizons:
        st.warning("Please select at least one prediction horizon")
        return

    # Store the prediction parameters in session state if possible
    try:
        st.session_state.horizon_unit = horizon_unit
        st.session_state.num_prediction_points = num_points
    except AttributeError:
        # If we can't store in session state, just continue
        pass

    # Check if models are trained for the selected horizons
    from app.utils.data_processing import is_model_trained

    # Check if any models are trained
    models_trained = []
    models_not_trained = []
    for horizon in selected_horizons:
        if is_model_trained(symbol, horizon, model_type.lower(), 'saved_models', horizon_unit):
            models_trained.append(horizon)
        else:
            models_not_trained.append(horizon)

    if not models_trained:
        st.warning("No trained models found for the selected horizons. Please train models first.")
        st.info("Go to the 'Train Model' page to train models for the selected horizons.")

        # Add a button to go to the Train Model page
        if st.button("Go to Train Model page"):
            st.session_state.page = "Train Model"
        return
    elif models_not_trained:
        st.warning(f"Models not trained for horizons: {', '.join(map(str, models_not_trained))}")
        st.info("Only predictions for trained models will be generated.")

    # Option to use AI predictions
    use_ai_predictions = st.checkbox("Use AI-enhanced predictions", value=False,
                                    help="Use AI service to enhance predictions (if enabled in AI Services)")

    # Option to fetch fresh live data
    fetch_live_data = st.checkbox("Fetch fresh live data before prediction", value=True,
                                 help="Fetch the latest price data from TradingView before making predictions")

    # Option to use adaptive model selection (if available)
    use_adaptive = False
    if ENHANCED_PREDICTIONS_AVAILABLE:
        use_adaptive = st.checkbox("Use adaptive model selection", value=True,
                                  help="Automatically select the best model based on past performance")

        if use_adaptive:
            # Show model performance metrics if available
            with st.expander("Model Performance Metrics"):
                try:
                    # Get performance metrics for all models
                    model_types = ["rf", "gb", "lstm", "ensemble"]

                    # Convert horizon to minutes for the model
                    if horizon_unit == "days":
                        model_horizon = selected_horizons[0] * 24 * 60  # days to minutes
                    elif horizon_unit == "weeks":
                        model_horizon = selected_horizons[0] * 7 * 24 * 60  # weeks to minutes
                    else:  # minutes
                        model_horizon = selected_horizons[0]

                    # Compare models
                    comparison = compare_models(symbol, model_types, model_horizon)

                    # Create a DataFrame for display
                    metrics_data = []
                    for model_name, metrics in comparison.items():
                        if "error" not in metrics:
                            metrics_data.append({
                                "Model": model_name,
                                "MAE": f"{metrics.get('mae', 'N/A'):.4f}" if metrics.get('mae') is not None else "N/A",
                                "RMSE": f"{metrics.get('rmse', 'N/A'):.4f}" if metrics.get('rmse') is not None else "N/A",
                                "Direction Accuracy": f"{metrics.get('direction_accuracy', 'N/A'):.2%}" if metrics.get('direction_accuracy') is not None else "N/A",
                                "Count": metrics.get('count', 'N/A')
                            })

                    if metrics_data:
                        # Create a more visual representation with a bar chart
                        metrics_df = pd.DataFrame(metrics_data)

                        # Display the metrics table
                        st.subheader("Model Performance Comparison")
                        st.dataframe(metrics_df)

                        # Create a bar chart for MAE comparison
                        try:
                            # Extract MAE values for plotting
                            chart_data = []
                            for model_name, metrics in comparison.items():
                                if "error" not in metrics and "mae" in metrics and metrics["mae"] is not None:
                                    chart_data.append({
                                        "Model": model_name,
                                        "MAE": metrics["mae"]
                                    })

                            if chart_data:
                                chart_df = pd.DataFrame(chart_data)
                                st.subheader("Mean Absolute Error by Model")
                                st.bar_chart(chart_df.set_index("Model"))
                        except Exception as chart_error:
                            st.warning(f"Could not create performance chart: {str(chart_error)}")

                        # Highlight the best model
                        best_model, best_value = None, None
                        for model_name, metrics in comparison.items():
                            if "error" not in metrics and "mae" in metrics and metrics["mae"] is not None:
                                if best_value is None or metrics["mae"] < best_value:
                                    best_model = model_name
                                    best_value = metrics["mae"]

                        if best_model:
                            st.success(f"Best model based on MAE: {best_model} (MAE: {best_value:.4f})")

                            # Add option to always use the best model
                            st.session_state.best_model = best_model
                            always_use_best = st.checkbox("Always use the best performing model", value=True,
                                                         help="Automatically select the best model for each prediction")
                            if always_use_best:
                                st.info(f"The system will automatically use the best model ({best_model}) for predictions")
                    else:
                        st.info("No performance metrics available yet. Metrics will be collected as predictions are made and verified.")

                        # Add information about how metrics are collected
                        st.markdown("""
                        ### How Performance Metrics are Collected

                        1. When you make a prediction, the system records the predicted value
                        2. When actual price data becomes available, the system calculates the error
                        3. Over time, the system learns which models perform best for each stock and horizon
                        4. The adaptive model selection uses this data to choose the best model

                        Make predictions regularly to improve the system's accuracy!
                        """)
                except Exception as e:
                    st.warning(f"Could not load performance metrics: {str(e)}")

            # Add option to view prediction history
            with st.expander("Prediction History"):
                try:
                    # Get prediction history for this symbol
                    from app.models.performance import get_prediction_history
                    history = get_prediction_history(symbol, limit=10)

                    if history and len(history) > 0:
                        # Create a DataFrame for display
                        history_data = []
                        for record in history:
                            history_data.append({
                                "Date": record.get("prediction_time", "Unknown"),
                                "Model": record.get("model_type", "Unknown"),
                                "Horizon": record.get("horizon", "Unknown"),
                                "Predicted": f"${record.get('predicted_value', 0):.2f}",
                                "Actual": f"${record.get('actual_value', 0):.2f}",
                                "Error": f"${record.get('absolute_error', 0):.2f}",
                                "Error %": f"{record.get('percentage_error', 0):.2f}%"
                            })

                        history_df = pd.DataFrame(history_data)
                        st.dataframe(history_df)
                    else:
                        st.info("No prediction history available yet.")
                except Exception as e:
                    st.warning(f"Could not load prediction history: {str(e)}")

    # Option to use ensemble approach (blend multiple models)
    use_ensemble = st.checkbox("Use ensemble approach", value=False,
                              help="Blend predictions from multiple models for potentially better accuracy")

    # If ensemble is selected, let user choose additional models
    ensemble_models = []
    if use_ensemble:
        # Get available models excluding the currently selected one
        other_models = [m for m in list(available_models.keys()) if m != model_type]

        # Let user select additional models
        ensemble_models = st.multiselect(
            "Select additional models for ensemble",
            options=other_models,
            default=other_models[:2] if len(other_models) >= 2 else other_models,
            format_func=lambda x: available_models[x]
        )

        # Ensemble weights
        st.write("Adjust model weights:")
        primary_weight = st.slider(f"Weight for {available_models[model_type]}",
                                  min_value=0.1, max_value=1.0, value=0.6, step=0.1)

        # Calculate remaining weight
        remaining_weight = 1.0 - primary_weight

        # If there are additional models, let user distribute the remaining weight
        if ensemble_models:
            st.info(f"Remaining weight ({remaining_weight:.1f}) will be distributed among selected models")
        else:
            st.warning("No additional models selected. Ensemble will use only the primary model.")

        # If adaptive ensemble is available, offer that option
        if ENHANCED_PREDICTIONS_AVAILABLE:
            use_adaptive_ensemble = st.checkbox("Use adaptive ensemble weights", value=True,
                                              help="Automatically adjust ensemble weights based on past performance")
            if use_adaptive_ensemble:
                st.info("Adaptive ensemble will override manual weights with weights based on model performance")

    # Make predictions
    if st.button("Generate Predictions"):
        try:
            with st.spinner("Generating predictions..."):
                # Use only horizons with trained models
                trained_horizons = [h for h in selected_horizons if h in models_trained]

                # Convert horizons to minutes for prediction functions
                model_horizons = []
                for h in trained_horizons:
                    if horizon_unit == "days":
                        model_horizons.append(h * 24 * 60)  # days to minutes
                    elif horizon_unit == "weeks":
                        model_horizons.append(h * 7 * 24 * 60)  # weeks to minutes
                    else:  # minutes
                        model_horizons.append(h)

                # Fetch fresh live data if requested
                current_live_data = live_data
                if fetch_live_data:
                    # Use the modern API server approach (same as Advanced Analysis)
                    current_live_data = safe_get_live_data_prediction(symbol)

                # Check if we should use AI predictions
                if use_ai_predictions:
                    # Import AI prediction module
                    from models.ai_predict import predict_with_ai_service
                    import configparser
                    from app.utils.config_helpers import clean_config_value

                    # Check if AI services are enabled
                    config_path = os.path.join("config", "ai_services.conf")
                    if os.path.exists(config_path):
                        config = configparser.ConfigParser()
                        config.read(config_path)

                        ai_enabled = config.getboolean("ai_services", "enabled", fallback=False)
                        provider = config.get("ai_services", "default_provider", fallback="none")

                        if ai_enabled and provider != "none":
                            st.info(f"Using AI-enhanced predictions with provider: {provider}")

                            # Create AI config
                            ai_config = {
                                "provider": clean_config_value(provider),
                                "config": config
                            }

                            # Make AI predictions
                            with st.spinner("Generating AI predictions..."):
                                ai_predictions = predict_with_ai_service(
                                    historical_data,
                                    symbol,
                                    model_horizons,
                                    ai_config,
                                    current_live_data  # Use the freshly fetched live data
                                )

                                # Use AI predictions
                                predictions = ai_predictions
                                st.success("AI predictions generated successfully")
                        else:
                            st.warning("AI services are not enabled. Using traditional models instead.")
                            # Fall back to traditional models
                            use_ai_predictions = False
                    else:
                        st.warning("AI services configuration not found. Using traditional models instead.")
                        # Fall back to traditional models
                        use_ai_predictions = False

                # If not using AI predictions or if AI predictions failed, use traditional models
                if not use_ai_predictions:
                    # Determine which data to use
                    if current_live_data is not None and not current_live_data.empty:
                        prediction_data = current_live_data
                        st.info(f"Using live price data: ${current_live_data['Close'].iloc[-1]:.2f}")
                    else:
                        prediction_data = historical_data
                        st.info(f"Using historical price data: ${historical_data['Close'].iloc[-1]:.2f}")

                    # Check if using adaptive model selection
                    if use_adaptive and ENHANCED_PREDICTIONS_AVAILABLE:
                        st.info("Using adaptive model selection based on past performance")

                        # Use the enhanced prediction module with adaptive selection
                        predictions = predict_future_prices(
                            prediction_data, symbol,
                            horizons=model_horizons,
                            model_type='auto',  # Use adaptive selection
                            sequence_length=60,
                            models_path='saved_models',
                            use_adaptive=True,
                            use_caching=True
                        )

                        st.success("Adaptive model predictions generated successfully")

                    # Check if using ensemble approach
                    elif use_ensemble and ensemble_models:
                        # Check if using adaptive ensemble weights
                        use_adaptive_ensemble = False
                        if ENHANCED_PREDICTIONS_AVAILABLE:
                            use_adaptive_ensemble = st.session_state.get('use_adaptive_ensemble', False)

                        if use_adaptive_ensemble and ENHANCED_PREDICTIONS_AVAILABLE:
                            st.info("Using adaptive ensemble with performance-based weights")

                            # Use the enhanced prediction module with adaptive ensemble
                            predictions = predict_future_prices(
                                prediction_data, symbol,
                                horizons=model_horizons,
                                model_type='ensemble',  # Use ensemble
                                sequence_length=60,
                                models_path='saved_models',
                                use_adaptive=True,
                                use_caching=True
                            )

                            st.success("Adaptive ensemble predictions generated successfully")
                        else:
                            st.info(f"Using manual ensemble approach with {len(ensemble_models) + 1} models")

                            # Dictionary to store predictions from each model
                            all_model_predictions = {}

                            # Get predictions from primary model
                            primary_predictions = predict_future_prices(
                                prediction_data, symbol,
                                horizons=model_horizons,
                                model_type=model_type.lower(),
                                models_path='saved_models'
                            )

                            # Store primary model predictions
                            all_model_predictions[model_type] = primary_predictions

                            # Get predictions from additional models
                            for additional_model in ensemble_models:
                                st.write(f"Getting predictions from {available_models[additional_model]}...")

                                model_predictions = predict_future_prices(
                                    prediction_data, symbol,
                                    horizons=model_horizons,
                                    model_type=additional_model.lower(),
                                    models_path='saved_models'
                                )

                                # Store this model's predictions
                                all_model_predictions[additional_model] = model_predictions

                            # Calculate ensemble predictions
                            predictions = {}

                            # Calculate weight for each additional model
                            additional_weight = remaining_weight / len(ensemble_models) if ensemble_models else 0

                            # Blend predictions for each horizon
                            for horizon in model_horizons:
                                # Start with weighted primary prediction
                                ensemble_prediction = primary_weight * primary_predictions[horizon]

                                # Add weighted predictions from additional models
                                for additional_model in ensemble_models:
                                    if horizon in all_model_predictions[additional_model]:
                                        ensemble_prediction += additional_weight * all_model_predictions[additional_model][horizon]

                                # Store the ensemble prediction
                                predictions[horizon] = ensemble_prediction

                            st.success("Manual ensemble predictions generated successfully")
                    else:
                        # Use single model (no ensemble, no adaptive)
                        predictions = predict_future_prices(
                            prediction_data, symbol,
                            horizons=model_horizons,
                            model_type=model_type.lower(),
                            models_path='saved_models'
                        )

                        st.success("Predictions generated successfully")

                # Map the predictions back to the original horizons
                horizon_predictions = {}
                for i, h in enumerate(trained_horizons):
                    horizon_predictions[h] = predictions[model_horizons[i]]

                # Replace predictions with mapped predictions
                predictions = horizon_predictions

                # Display predictions
                st.success("Predictions generated successfully")

                # Save predictions to session state for reporting
                st.session_state.predictions = predictions

                # Track predictions for performance metrics
                try:
                    # Import tracking function if available
                    try:
                        from app.models.performance import track_prediction
                        tracking_available = True
                    except ImportError:
                        tracking_available = False

                    if tracking_available:
                        # Determine which model type to track
                        if use_ai_predictions:
                            tracking_model = f"ai_{provider}"
                        elif use_ensemble:
                            tracking_model = f"ensemble_{model_type}"
                        else:
                            tracking_model = model_type

                        # Track the predictions for each horizon
                        for horizon, prediction in predictions.items():
                            # Convert to minutes for tracking
                            if horizon_unit == "days":
                                tracking_horizon = horizon * 24 * 60  # days to minutes
                            elif horizon_unit == "weeks":
                                tracking_horizon = horizon * 7 * 24 * 60  # weeks to minutes
                            else:  # minutes
                                tracking_horizon = horizon

                            # Track the prediction (actual value will be updated later)
                            track_prediction(
                                symbol,
                                tracking_model,
                                tracking_horizon,
                                prediction,
                                0.0,  # Actual value not known yet
                                datetime.now()
                            )

                        st.info("Predictions tracked for performance metrics")
                    else:
                        logger.info("Performance tracking not available")
                except Exception as e:
                    logger.error(f"Error tracking predictions: {str(e)}")
                    # Continue even if tracking fails

                # Create a table of predictions
                pred_data = []
                current_time = datetime.now()

                # Get the horizon unit and number of points from session state
                horizon_unit = st.session_state.horizon_unit
                num_points = st.session_state.num_prediction_points

                # Generate multiple prediction points for each horizon
                all_predictions = {}

                for horizon in selected_horizons:
                    # Initialize list to store multiple predictions for this horizon
                    horizon_predictions = []

                    # Convert horizon to minutes for the model (which was trained on minute-based data)
                    if horizon_unit == "days":
                        model_horizon = horizon * 24 * 60  # days to minutes
                    elif horizon_unit == "weeks":
                        model_horizon = horizon * 7 * 24 * 60  # weeks to minutes
                    else:  # minutes
                        model_horizon = horizon

                    # For short-term predictions (minutes), just use the existing single-point prediction
                    if horizon_unit == "minutes":
                        pred_time = current_time + timedelta(minutes=horizon)
                        # Calculate confidence score (higher for shorter horizons)
                        confidence = max(0, min(100, 100 - (horizon * 0.5)))  # 0.5% decrease per minute

                        pred_data.append({
                            f'Horizon ({horizon_unit})': horizon,
                            'Predicted Time': pred_time.strftime('%Y-%m-%d %H:%M:%S'),
                            'Predicted Price': round(predictions[horizon], 2),
                            'Confidence': f"{confidence:.1f}%"
                        })
                        all_predictions[horizon] = {pred_time: predictions[horizon]}
                    else:
                        # For medium and long-term predictions, generate multiple points
                        horizon_pred_dict = {}

                        # Base price is the last known price - use freshly fetched live data if available
                        if current_live_data is not None and not current_live_data.empty:
                            base_price = current_live_data['Close'].iloc[-1]
                        else:
                            base_price = historical_data['Close'].iloc[-1]

                        # Use the model's prediction as a trend indicator
                        trend_factor = predictions[horizon] / base_price

                        # Generate predictions for each point
                        for i in range(1, num_points + 1):
                            if horizon_unit == "days":
                                point_time = current_time + timedelta(days=i * horizon)
                                # Adjust trend based on the point number
                                point_trend = 1 + (trend_factor - 1) * (i / num_points)
                                point_price = base_price * point_trend
                            else:  # weeks
                                point_time = current_time + timedelta(weeks=i * horizon)
                                # Adjust trend based on the point number
                                point_trend = 1 + (trend_factor - 1) * (i / num_points)
                                point_price = base_price * point_trend

                            # Calculate confidence score (decreases with longer horizons and further points)
                            if horizon_unit == "days":
                                # 5% decrease per day, additional 2% decrease per point
                                confidence = max(0, min(100, 100 - (horizon * 5) - (i * 2)))
                            else:  # weeks
                                # 10% decrease per week, additional 5% decrease per point
                                confidence = max(0, min(100, 100 - (horizon * 10) - (i * 5)))

                            # Add to predictions data
                            pred_data.append({
                                f'Horizon ({horizon_unit})': horizon,
                                'Point': i,
                                'Predicted Time': point_time.strftime('%Y-%m-%d %H:%M:%S'),
                                'Predicted Price': round(point_price, 2),
                                'Confidence': f"{confidence:.1f}%"
                            })

                            # Store for plotting
                            horizon_pred_dict[point_time] = point_price

                        all_predictions[horizon] = horizon_pred_dict

                # Create and display the predictions dataframe
                pred_df = pd.DataFrame(pred_data)
                st.dataframe(pred_df)

                # Plot predictions using the freshly fetched live data
                plot_predictions(historical_data, current_live_data, all_predictions, symbol, horizon_unit)

        except Exception as e:
            st.error(f"Error generating predictions: {str(e)}")
            logger.error(f"Error generating predictions: {str(e)}")

def plot_predictions(historical_data, live_data, predictions, symbol, horizon_unit='minutes'):
    """
    Plot historical data and predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        predictions (dict): Dictionary with predictions for each horizon
        symbol (str): Stock symbol
        horizon_unit (str): Unit of time for horizons (minutes, days, weeks)
    """
    st.subheader("Price Forecast")

    # Create figure
    fig = go.Figure()

    # Add historical data
    fig.add_trace(go.Scatter(
        x=historical_data['Date'],
        y=historical_data['Close'],
        mode='lines',
        name='Historical',
        line=dict(color='blue')
    ))

    # Add live data if available
    if live_data is not None and not live_data.empty:
        fig.add_trace(go.Scatter(
            x=live_data['Date'],
            y=live_data['Close'],
            mode='markers',
            name='Live',
            marker=dict(color='green', size=10)
        ))

    # Add predictions
    colors = ['red', 'orange', 'purple', 'brown', 'pink']

    for i, (horizon, pred_dict) in enumerate(predictions.items()):
        # Get color for this horizon (cycle through colors if needed)
        color = colors[i % len(colors)]

        # Extract times and prices from the prediction dictionary
        pred_times = list(pred_dict.keys())
        pred_prices = list(pred_dict.values())

        # For single-point predictions (minutes)
        if len(pred_times) == 1:
            fig.add_trace(go.Scatter(
                x=pred_times,
                y=pred_prices,
                mode='markers',
                name=f'{horizon} {horizon_unit}',
                marker=dict(color=color, size=10, symbol='star')
            ))
        else:
            # For multi-point predictions (days, weeks)
            fig.add_trace(go.Scatter(
                x=pred_times,
                y=pred_prices,
                mode='lines+markers',
                name=f'{horizon} {horizon_unit}',
                line=dict(color=color, dash='dot'),
                marker=dict(color=color, size=8, symbol='circle')
            ))

    # Update layout
    fig.update_layout(
        title=f'{symbol} Price Forecast',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig, use_container_width=True)

    # Add trend line between last actual price and predictions
    if live_data is not None and not live_data.empty:
        last_price = live_data['Close'].iloc[-1]
        last_time = live_data['Date'].iloc[-1]
    else:
        last_price = historical_data['Close'].iloc[-1]
        last_time = historical_data['Date'].iloc[-1]

    # Create trend lines
    fig2 = go.Figure()

    # Add last actual price point
    fig2.add_trace(go.Scatter(
        x=[last_time],
        y=[last_price],
        mode='markers',
        name='Last Actual',
        marker=dict(color='blue', size=10)
    ))

    # Add prediction lines
    for i, (horizon, pred_dict) in enumerate(predictions.items()):
        # Get color for this horizon
        color = colors[i % len(colors)]

        # Extract times and prices
        pred_times = list(pred_dict.keys())
        pred_prices = list(pred_dict.values())

        # For single-point predictions
        if len(pred_times) == 1:
            fig2.add_trace(go.Scatter(
                x=[last_time, pred_times[0]],
                y=[last_price, pred_prices[0]],
                mode='lines+markers',
                name=f'{horizon} {horizon_unit}',
                line=dict(color=color, dash='dot'),
                marker=dict(size=[0, 10], symbol=['circle', 'star'])
            ))
        else:
            # For multi-point predictions, connect from last actual to first prediction
            # and then show the trend line for all predictions
            all_x = [last_time] + pred_times
            all_y = [last_price] + pred_prices

            fig2.add_trace(go.Scatter(
                x=all_x,
                y=all_y,
                mode='lines+markers',
                name=f'{horizon} {horizon_unit}',
                line=dict(color=color, dash='dot'),
                marker=dict(color=color, size=8)
            ))

    # Update layout
    fig2.update_layout(
        title=f'{symbol} Prediction Trends',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Show plot
    st.plotly_chart(fig2, use_container_width=True)
