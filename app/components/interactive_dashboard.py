"""
Interactive dashboard component for the AI Stocks Bot application.
Provides a comprehensive view of stock data with interactive charts and metrics.
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import os
import sys
import logging

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import application configuration
from app.config import (
    STOCK_DATA_DIR, MODELS_DIR, TECHNICAL_INDICATORS,
    THEME_COLOR, SECONDARY_COLOR, DANGER_COLOR, SUCCESS_COLOR
)

# Import utility functions
from app.utils.common import (
    load_stock_data, get_available_stocks,
    format_prediction_result, get_model_display_name
)
from app.utils.error_handling import handle_errors, log_execution_time
from app.utils.feature_engineering import add_technical_indicators

# Configure logging
logger = logging.getLogger(__name__)

@log_execution_time
def interactive_dashboard_component():
    """
    Interactive dashboard component that provides a comprehensive view of stock data.
    """
    st.title("Interactive Stock Dashboard")

    # Sidebar for controls
    with st.sidebar:
        st.header("Dashboard Controls")

        # Stock selection
        available_stocks = get_available_stocks(STOCK_DATA_DIR)
        if not available_stocks:
            st.error("No stock data found. Please upload stock data first.")
            return

        selected_stock = st.selectbox(
            "Select Stock",
            options=available_stocks,
            index=0 if available_stocks else None,
            key="dashboard_stock_select"
        )

        # Time period selection
        time_periods = {
            "1 Week": 7,
            "1 Month": 30,
            "3 Months": 90,
            "6 Months": 180,
            "1 Year": 365,
            "All Data": 0
        }

        selected_period = st.selectbox(
            "Select Time Period",
            options=list(time_periods.keys()),
            index=2,  # Default to 3 months
            key="dashboard_period_select"
        )

        # Technical indicator selection
        st.subheader("Technical Indicators")
        selected_indicators = st.multiselect(
            "Select Indicators",
            options=TECHNICAL_INDICATORS,
            default=["SMA20", "SMA50", "RSI"],
            key="dashboard_indicators_select"
        )

        # Chart type selection
        chart_types = ["Candlestick", "OHLC", "Line", "Area"]
        selected_chart_type = st.selectbox(
            "Chart Type",
            options=chart_types,
            index=0,
            key="dashboard_chart_type"
        )

        # Auto-refresh option
        auto_refresh = st.checkbox(
            "Auto-refresh (30s)",
            value=False,
            key="dashboard_auto_refresh"
        )

        # Refresh button
        if st.button("Refresh Data", key="dashboard_refresh"):
            st.rerun

    # Main content area
    if not selected_stock:
        st.warning("Please select a stock from the sidebar.")
        return

    # Load stock data
    stock_data = load_stock_data(selected_stock, STOCK_DATA_DIR)
    if stock_data is None or stock_data.empty:
        st.error(f"No data found for {selected_stock}.")
        return

    # Filter data based on selected time period
    if time_periods[selected_period] > 0:
        end_date = stock_data['Date'].max()
        start_date = end_date - timedelta(days=time_periods[selected_period])
        filtered_data = stock_data[stock_data['Date'] >= start_date].copy()
    else:
        filtered_data = stock_data.copy()

    # Add technical indicators
    if selected_indicators:
        filtered_data = add_technical_indicators(filtered_data, selected_indicators)

    # Display key metrics
    display_key_metrics(filtered_data, selected_stock)

    # Display interactive chart
    display_interactive_chart(
        filtered_data,
        selected_stock,
        selected_chart_type,
        selected_indicators
    )

    # Display additional analysis
    with st.expander("Price Statistics", expanded=True):
        display_price_statistics(filtered_data)

    with st.expander("Volume Analysis", expanded=True):
        display_volume_analysis(filtered_data)

    with st.expander("Technical Analysis", expanded=True):
        display_technical_analysis(filtered_data, selected_indicators)

    # Set up auto-refresh if enabled
    if auto_refresh:
        st.markdown(
            """
            <script>
                setTimeout(function(){
                    window.location.reload();
                }, 30000);
            </script>
            """,
            unsafe_allow_html=True
        )

def display_key_metrics(data, symbol):
    """
    Display key metrics for the selected stock.

    Args:
        data (pd.DataFrame): Stock data
        symbol (str): Stock symbol
    """
    st.subheader(f"Key Metrics for {symbol}")

    # Get latest data
    latest = data.iloc[-1]
    previous = data.iloc[-2] if len(data) > 1 else latest

    # Calculate metrics
    price_change = latest['Close'] - previous['Close']
    price_change_pct = (price_change / previous['Close']) * 100 if previous['Close'] > 0 else 0

    # Create metrics row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Current Price",
            f"{latest['Close']:.2f}",
            f"{price_change:.2f} ({price_change_pct:.2f}%)",
            delta_color="normal"
        )

    with col2:
        # Calculate 52-week high and low
        if len(data) >= 252:  # Approximately 1 year of trading days
            year_data = data.iloc[-252:]
            high_52w = year_data['High'].max()
            low_52w = year_data['Low'].min()

            # Calculate percentage from 52-week high
            pct_from_high = ((latest['Close'] - high_52w) / high_52w) * 100

            st.metric(
                "52-Week Range",
                f"{low_52w:.2f} - {high_52w:.2f}",
                f"{pct_from_high:.2f}% from high",
                delta_color="inverse"  # Lower is better (closer to high)
            )
        else:
            # Use available data
            high = data['High'].max()
            low = data['Low'].min()

            # Calculate percentage from high
            pct_from_high = ((latest['Close'] - high) / high) * 100

            st.metric(
                "Price Range",
                f"{low:.2f} - {high:.2f}",
                f"{pct_from_high:.2f}% from high",
                delta_color="inverse"  # Lower is better (closer to high)
            )

    with col3:
        # Calculate average volume
        avg_volume = data['Volume'].mean()
        volume_change = latest['Volume'] - avg_volume
        volume_change_pct = (volume_change / avg_volume) * 100 if avg_volume > 0 else 0

        st.metric(
            "Volume",
            f"{int(latest['Volume']):,}",
            f"{volume_change_pct:.2f}% vs avg",
            delta_color="normal"
        )

    with col4:
        # Calculate volatility (standard deviation of returns)
        if len(data) > 1:
            returns = data['Close'].pct_change().dropna()
            volatility = returns.std() * 100

            # Compare to previous period
            if len(returns) > 20:
                current_vol = returns.iloc[-20:].std() * 100
                previous_vol = returns.iloc[-40:-20].std() * 100
                vol_change = current_vol - previous_vol

                st.metric(
                    "Volatility (20d)",
                    f"{current_vol:.2f}%",
                    f"{vol_change:.2f}%",
                    delta_color="inverse"  # Lower volatility is generally better
                )
            else:
                st.metric(
                    "Volatility",
                    f"{volatility:.2f}%",
                    None
                )
        else:
            st.metric(
                "Volatility",
                "N/A",
                None
            )

def display_interactive_chart(data, symbol, chart_type, indicators):
    """
    Display an interactive chart for the selected stock.

    Args:
        data (pd.DataFrame): Stock data
        symbol (str): Stock symbol
        chart_type (str): Type of chart to display
        indicators (list): Technical indicators to display
    """
    st.subheader(f"{symbol} Price Chart")

    # Create figure with secondary y-axis for volume
    fig = make_subplots(
        rows=2,
        cols=1,
        shared_xaxes=True,
        vertical_spacing=0.03,
        row_heights=[0.7, 0.3],
        subplot_titles=(f"{symbol} Price", "Volume")
    )

    # Add price data based on chart type
    if chart_type == "Candlestick":
        fig.add_trace(
            go.Candlestick(
                x=data['Date'],
                open=data['Open'],
                high=data['High'],
                low=data['Low'],
                close=data['Close'],
                name="Price",
                showlegend=False
            ),
            row=1, col=1
        )
    elif chart_type == "OHLC":
        fig.add_trace(
            go.Ohlc(
                x=data['Date'],
                open=data['Open'],
                high=data['High'],
                low=data['Low'],
                close=data['Close'],
                name="Price",
                showlegend=False
            ),
            row=1, col=1
        )
    elif chart_type == "Line":
        fig.add_trace(
            go.Scatter(
                x=data['Date'],
                y=data['Close'],
                mode='lines',
                name="Close Price",
                line=dict(color=THEME_COLOR, width=2)
            ),
            row=1, col=1
        )
    elif chart_type == "Area":
        fig.add_trace(
            go.Scatter(
                x=data['Date'],
                y=data['Close'],
                mode='lines',
                fill='tozeroy',
                name="Close Price",
                line=dict(color=THEME_COLOR, width=2),
                fillcolor=f"rgba({int(THEME_COLOR[1:3], 16)}, {int(THEME_COLOR[3:5], 16)}, {int(THEME_COLOR[5:7], 16)}, 0.3)"
            ),
            row=1, col=1
        )

    # Add volume chart
    fig.add_trace(
        go.Bar(
            x=data['Date'],
            y=data['Volume'],
            name="Volume",
            marker=dict(color=SECONDARY_COLOR)
        ),
        row=2, col=1
    )

    # Add technical indicators
    for indicator in indicators:
        if indicator in data.columns:
            fig.add_trace(
                go.Scatter(
                    x=data['Date'],
                    y=data[indicator],
                    mode='lines',
                    name=indicator,
                    line=dict(width=1)
                ),
                row=1, col=1
            )

    # Update layout
    fig.update_layout(
        title=f"{symbol} Stock Price and Volume",
        xaxis_title="Date",
        yaxis_title="Price",
        height=600,
        hovermode="x unified",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        xaxis_rangeslider_visible=False
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="Price", row=1, col=1)
    fig.update_yaxes(title_text="Volume", row=2, col=1)

    # Display the chart
    st.plotly_chart(fig, use_container_width=True)

def display_price_statistics(data):
    """
    Display price statistics for the selected stock.

    Args:
        data (pd.DataFrame): Stock data
    """
    # Calculate statistics
    stats = {
        "Open": data['Open'].describe(),
        "High": data['High'].describe(),
        "Low": data['Low'].describe(),
        "Close": data['Close'].describe()
    }

    # Create DataFrame for display
    stats_df = pd.DataFrame(stats)

    # Round to 2 decimal places
    stats_df = stats_df.round(2)

    # Display statistics
    st.dataframe(stats_df, use_container_width=True)

    # Create a box plot
    fig = go.Figure()

    for col in ['Open', 'High', 'Low', 'Close']:
        fig.add_trace(
            go.Box(
                y=data[col],
                name=col,
                boxpoints='outliers'
            )
        )

    fig.update_layout(
        title="Price Distribution",
        yaxis_title="Price",
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)

def display_volume_analysis(data):
    """
    Display volume analysis for the selected stock.

    Args:
        data (pd.DataFrame): Stock data
    """
    # Calculate volume statistics
    volume_stats = data['Volume'].describe()

    # Create a column layout
    col1, col2 = st.columns(2)

    with col1:
        # Display volume statistics
        st.subheader("Volume Statistics")
        st.write(f"Average Volume: {int(volume_stats['mean']):,}")
        st.write(f"Maximum Volume: {int(volume_stats['max']):,}")
        st.write(f"Minimum Volume: {int(volume_stats['min']):,}")
        st.write(f"Standard Deviation: {int(volume_stats['std']):,}")

    with col2:
        # Calculate correlation between volume and price
        correlations = {
            "Volume vs. Close": data[['Volume', 'Close']].corr().iloc[0, 1],
            "Volume vs. Price Change": data[['Volume']].corrwith(data['Close'].pct_change()).iloc[0],
            "Volume vs. Absolute Price Change": data[['Volume']].corrwith(data['Close'].pct_change().abs()).iloc[0]
        }

        # Display correlations
        st.subheader("Volume Correlations")
        for name, value in correlations.items():
            st.write(f"{name}: {value:.4f}")

    # Create a volume histogram
    fig = px.histogram(
        data,
        x="Volume",
        nbins=30,
        title="Volume Distribution"
    )

    fig.update_layout(
        xaxis_title="Volume",
        yaxis_title="Frequency",
        height=300
    )

    st.plotly_chart(fig, use_container_width=True)

    # Create a scatter plot of volume vs. price
    # Check if statsmodels is available for trendline
    try:
        import statsmodels.api as sm
        has_statsmodels = True
    except ImportError:
        has_statsmodels = False
        st.warning("The statsmodels package is not installed. Trendline will not be displayed. Install with 'pip install statsmodels'.")

    # Create scatter plot with or without trendline
    if has_statsmodels:
        fig = px.scatter(
            data,
            x="Volume",
            y="Close",
            title="Volume vs. Price",
            trendline="ols"
        )
    else:
        fig = px.scatter(
            data,
            x="Volume",
            y="Close",
            title="Volume vs. Price"
        )

    fig.update_layout(
        xaxis_title="Volume",
        yaxis_title="Close Price",
        height=300
    )

    st.plotly_chart(fig, use_container_width=True)

def display_technical_analysis(data, indicators):
    """
    Display technical analysis for the selected stock.

    Args:
        data (pd.DataFrame): Stock data
        indicators (list): Technical indicators to display
    """
    if not indicators or all(indicator not in data.columns for indicator in indicators):
        st.info("No technical indicators selected or available.")
        return

    # Filter to only include selected indicators that are in the data
    available_indicators = [ind for ind in indicators if ind in data.columns]

    if not available_indicators:
        st.info("Selected indicators are not available in the data.")
        return

    # Create a subplot for each indicator
    fig = make_subplots(
        rows=len(available_indicators),
        cols=1,
        shared_xaxes=True,
        vertical_spacing=0.03,
        subplot_titles=available_indicators
    )

    # Add each indicator to the subplot
    for i, indicator in enumerate(available_indicators):
        fig.add_trace(
            go.Scatter(
                x=data['Date'],
                y=data[indicator],
                mode='lines',
                name=indicator,
                line=dict(color=THEME_COLOR)
            ),
            row=i+1, col=1
        )

        # Add close price as reference if it's not an oscillator
        if not any(osc in indicator for osc in ['RSI', 'MACD', 'CCI', 'ROC']):
            fig.add_trace(
                go.Scatter(
                    x=data['Date'],
                    y=data['Close'],
                    mode='lines',
                    name='Close',
                    line=dict(color='rgba(0,0,0,0.3)', width=1)
                ),
                row=i+1, col=1
            )

    # Update layout
    fig.update_layout(
        height=200 * len(available_indicators),
        showlegend=False,
        hovermode="x unified"
    )

    st.plotly_chart(fig, use_container_width=True)

    # Display indicator correlations
    if len(available_indicators) > 1:
        st.subheader("Indicator Correlations")

        # Calculate correlations
        corr_data = data[['Close'] + available_indicators].corr()

        # Create heatmap
        fig = px.imshow(
            corr_data,
            text_auto='.2f',
            color_continuous_scale='RdBu_r',
            zmin=-1, zmax=1,
            title="Correlation Matrix"
        )

        fig.update_layout(height=400)

        st.plotly_chart(fig, use_container_width=True)
