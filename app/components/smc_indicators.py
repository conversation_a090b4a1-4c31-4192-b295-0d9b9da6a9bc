"""
Smart Money Concepts (SMC) Indicators
Core SMC analysis functions for professional trading
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

# Configure logger
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@dataclass
class OrderBlock:
    """Represents an order block zone"""
    high: float
    low: float
    timestamp: int
    block_type: str  # 'bullish' or 'bearish'
    strength: float  # 0-1 strength score
    tested: bool = False
    broken: bool = False
    volume: float = 0.0

@dataclass
class FairValueGap:
    """Represents a Fair Value Gap"""
    high: float
    low: float
    timestamp: int
    gap_type: str  # 'bullish' or 'bearish'
    strength: float  # 0-1 strength score
    filled: bool = False
    partially_filled: bool = False
    volume: float = 0.0
    displacement: float = 0.0  # Size of the displacement that created the FVG

class LiquidityType(Enum):
    """Types of liquidity zones"""
    BUY_SIDE = "buy_side"  # Above resistance, stops from shorts
    SELL_SIDE = "sell_side"  # Below support, stops from longs
    INTERNAL = "internal"  # Within range, both sides

@dataclass
class LiquidityZone:
    """Represents a liquidity zone"""
    high: float
    low: float
    center: float
    timestamp: int
    zone_type: LiquidityType
    strength: float  # 0-1 strength score
    volume_profile: float
    touches: int = 0
    swept: bool = False
    active: bool = True
    confluence_factors: List[str] = None

    def __post_init__(self):
        if self.confluence_factors is None:
            self.confluence_factors = []

def detect_order_blocks(df: pd.DataFrame, lookback: int = 20, min_strength: float = 0.3) -> List[OrderBlock]:
    """
    Detect order blocks in price data
    
    Args:
        df: OHLCV DataFrame
        lookback: Number of periods to look back for swing points
        min_strength: Minimum strength threshold for valid order blocks
    
    Returns:
        List of OrderBlock objects
    """
    if len(df) < lookback * 2:
        return []
    
    order_blocks = []
    
    # Find swing highs and lows
    swing_highs = find_swing_points(df, 'high', lookback)
    swing_lows = find_swing_points(df, 'low', lookback)
    
    # Detect bullish order blocks (from swing lows)
    for idx, low_price in swing_lows.items():
        if idx < lookback or idx >= len(df) - 5:
            continue
        
        swing_candle = df.iloc[idx]
        
        body_size = abs(swing_candle['close'] - swing_candle['open'])
        lower_wick = swing_candle['open'] - swing_candle['low'] if swing_candle['close'] > swing_candle['open'] else swing_candle['close'] - swing_candle['low']
        
        if lower_wick > body_size * 0.5:
            ob_high = max(swing_candle['open'], swing_candle['close'])
            ob_low = swing_candle['low']
            
            volume_strength = min(swing_candle.get('volume', 0) / df['volume'].rolling(20).mean().iloc[idx], 2.0) / 2.0
            wick_strength = min(lower_wick / body_size, 3.0) / 3.0
            strength = (volume_strength + wick_strength) / 2
            
            if strength >= min_strength:
                order_blocks.append(OrderBlock(
                    high=ob_high,
                    low=ob_low,
                    timestamp=idx,
                    block_type='bullish',
                    strength=strength,
                    volume=swing_candle.get('volume', 0)
                ))
    
    # Detect bearish order blocks (from swing highs)
    for idx, high_price in swing_highs.items():
        if idx < lookback or idx >= len(df) - 5:
            continue
        
        swing_candle = df.iloc[idx]
        
        body_size = abs(swing_candle['close'] - swing_candle['open'])
        upper_wick = swing_candle['high'] - swing_candle['open'] if swing_candle['close'] < swing_candle['open'] else swing_candle['high'] - swing_candle['close']
        
        if upper_wick > body_size * 0.5:
            ob_high = swing_candle['high']
            ob_low = min(swing_candle['open'], swing_candle['close'])
            
            volume_strength = min(swing_candle.get('volume', 0) / df['volume'].rolling(20).mean().iloc[idx], 2.0) / 2.0
            wick_strength = min(upper_wick / body_size, 3.0) / 3.0
            strength = (volume_strength + wick_strength) / 2
            
            if strength >= min_strength:
                order_blocks.append(OrderBlock(
                    high=ob_high,
                    low=ob_low,
                    timestamp=idx,
                    block_type='bearish',
                    strength=strength,
                    volume=swing_candle.get('volume', 0)
                ))
    
    order_blocks = update_order_block_status(df, order_blocks)
    order_blocks.sort(key=lambda x: x.timestamp, reverse=True)
    return order_blocks[:10]

def detect_fvg(df: pd.DataFrame, min_gap_size: float = 0.001) -> List[FairValueGap]:
    """Detect Fair Value Gaps"""
    if len(df) < 3:
        return []

    fvg_zones = []

    for i in range(2, len(df)):
        candle1 = df.iloc[i-2]  # First candle
        candle2 = df.iloc[i-1]  # Middle candle (displacement candle)
        candle3 = df.iloc[i]    # Third candle

        try:
            c1_high = float(candle1['high'])
            c1_low = float(candle1['low'])
            c2_open = float(candle2['open'])
            c2_close = float(candle2['close'])
            c2_volume = float(candle2.get('volume', 0))
            c3_high = float(candle3['high'])
            c3_low = float(candle3['low'])
        except (TypeError, ValueError):
            continue

        # Bullish FVG: Gap between candle1 high and candle3 low
        if c3_low > c1_high:
            gap_size = c3_low - c1_high
            gap_size_pct = gap_size / c1_high

            if gap_size_pct >= min_gap_size:
                displacement = abs(c2_close - c2_open) / c2_open if c2_open != 0 else 0

                avg_volume = float(df['volume'].rolling(20).mean().iloc[i-1])
                volume_strength = min(c2_volume / avg_volume if avg_volume > 0 else 0, 2.0) / 2.0
                gap_strength = min(gap_size_pct * 100, 5.0) / 5.0
                displacement_strength = min(displacement * 10, 1.0)

                strength = (volume_strength + gap_strength + displacement_strength) / 3

                fvg_zones.append(FairValueGap(
                    high=c3_low,
                    low=c1_high,
                    timestamp=i-1,
                    gap_type='bullish',
                    strength=strength,
                    volume=c2_volume,
                    displacement=displacement
                ))

        # Bearish FVG: Gap between candle1 low and candle3 high
        elif c3_high < c1_low:
            gap_size = c1_low - c3_high
            gap_size_pct = gap_size / c1_low

            if gap_size_pct >= min_gap_size:
                displacement = abs(c2_close - c2_open) / c2_open if c2_open != 0 else 0

                avg_volume = float(df['volume'].rolling(20).mean().iloc[i-1])
                volume_strength = min(c2_volume / avg_volume if avg_volume > 0 else 0, 2.0) / 2.0
                gap_strength = min(gap_size_pct * 100, 5.0) / 5.0
                displacement_strength = min(displacement * 10, 1.0)

                strength = (volume_strength + gap_strength + displacement_strength) / 3

                fvg_zones.append(FairValueGap(
                    high=c1_low,
                    low=c3_high,
                    timestamp=i-1,
                    gap_type='bearish',
                    strength=strength,
                    volume=c2_volume,
                    displacement=displacement
                ))

    # Update FVG status
    fvg_zones = update_fvg_status(df, fvg_zones)

    # Sort by timestamp and return most recent
    fvg_zones.sort(key=lambda x: x.timestamp, reverse=True)
    return fvg_zones[:15]

def detect_liquidity_zones(df: pd.DataFrame, lookback: int = 50, min_strength: float = 0.4) -> List[LiquidityZone]:
    """
    Detect liquidity zones using multiple SMC concepts
    
    Args:
        df: OHLCV DataFrame
        lookback: Periods to analyze for zone detection
        min_strength: Minimum strength threshold
    
    Returns:
        List of LiquidityZone objects
    """
    if len(df) < lookback * 2:
        return []
    
    zones = []
    
    # Find swing highs and lows (potential liquidity areas)
    swing_highs = find_swing_points(df, 'high', lookback)
    swing_lows = find_swing_points(df, 'low', lookback)
    
    # Buy-side liquidity (above swing highs)
    for idx, high_price in swing_highs.items():
        if idx < lookback:
            continue
            
        # Calculate strength based on how many times this level was tested
        test_count = 0
        for i in range(idx + 1, min(idx + 50, len(df))):
            if abs(df.iloc[i]['high'] - high_price) / high_price < 0.005:  # Within 0.5%
                test_count += 1
        
        strength = min(test_count / 3, 1.0)  # Normalize to 0-1
        
        if strength > 0.2:  # Minimum strength threshold
            zones.append(LiquidityZone(
                high=high_price * 1.002,  # Small buffer above
                low=high_price * 0.998,   # Small buffer below
                center=(high_price * 1.002 + high_price * 0.998) / 2,
                timestamp=idx,
                zone_type=LiquidityType.BUY_SIDE,
                strength=strength,
                volume_profile=df.iloc[idx].get('volume', 0)
            ))
    
    # Sell-side liquidity (below swing lows)
    for idx, low_price in swing_lows.items():
        if idx < lookback:
            continue
            
        test_count = 0
        for i in range(idx + 1, min(idx + 50, len(df))):
            if abs(df.iloc[i]['low'] - low_price) / low_price < 0.005:
                test_count += 1
        
        strength = min(test_count / 3, 1.0)
        
        if strength > 0.2:
            zones.append(LiquidityZone(
                high=low_price * 1.002,
                low=low_price * 0.998,
                center=(low_price * 1.002 + low_price * 0.998) / 2,
                timestamp=idx,
                zone_type=LiquidityType.SELL_SIDE,
                strength=strength,
                volume_profile=df.iloc[idx].get('volume', 0)
            ))
    
    # Internal liquidity zones (within recent range)
    recent_high = df['high'].rolling(lookback).max().iloc[-1]
    recent_low = df['low'].rolling(lookback).min().iloc[-1]
    if recent_high > recent_low:
        zones.append(LiquidityZone(
            high=recent_high,
            low=recent_low,
            center=(recent_high + recent_low) / 2,
            timestamp=len(df) - 1,
            zone_type=LiquidityType.INTERNAL,
            strength=1.0,
            volume_profile=df.iloc[-1].get('volume', 0)
        ))
    
    # Sort by timestamp and return most recent
    zones.sort(key=lambda x: x.timestamp, reverse=True)
    return zones[:20]  # Return top 20 zones

def find_swing_points(df: pd.DataFrame, price_type: str, lookback: int) -> Dict[int, float]:
    """Find swing highs or lows"""
    swing_points = {}
    
    for i in range(lookback, len(df) - lookback):
        if price_type == 'high':
            current = df.iloc[i]['high']
            left_max = df.iloc[i-lookback:i]['high'].max()
            right_max = df.iloc[i+1:i+lookback+1]['high'].max()
            
            if current > left_max and current > right_max:
                swing_points[i] = current
                
        elif price_type == 'low':
            current = df.iloc[i]['low']
            left_min = df.iloc[i-lookback:i]['low'].min()
            right_min = df.iloc[i+1:i+lookback+1]['low'].min()
            
            if current < left_min and current < right_min:
                swing_points[i] = current
    
    # Debugging swing points
    logger.debug(f"Swing Points ({price_type}): {swing_points}")
    
    return swing_points

def update_order_block_status(df: pd.DataFrame, order_blocks: List[OrderBlock]) -> List[OrderBlock]:
    """Update whether order blocks have been tested or broken"""
    for ob in order_blocks:
        start_idx = ob.timestamp + 1
        
        if start_idx >= len(df):
            continue
            
        subsequent_data = df.iloc[start_idx:]
        
        for idx, candle in subsequent_data.iterrows():
            if ob.block_type == 'bullish':
                if candle['low'] <= ob.high and candle['high'] >= ob.low:
                    ob.tested = True
                if candle['close'] < ob.low:
                    ob.broken = True
                    break
            elif ob.block_type == 'bearish':
                if candle['high'] >= ob.low and candle['low'] <= ob.high:
                    ob.tested = True
                if candle['close'] > ob.high:
                    ob.broken = True
                    break
    
    return order_blocks

def update_fvg_status(df: pd.DataFrame, fvg_zones: List[FairValueGap]) -> List[FairValueGap]:
    """Update whether FVGs have been filled or partially filled"""
    for fvg in fvg_zones:
        start_idx = fvg.timestamp + 1
        
        if start_idx >= len(df):
            continue
            
        subsequent_data = df.iloc[start_idx:]
        
        for idx, candle in subsequent_data.iterrows():
            if fvg.gap_type == 'bullish':
                if candle['low'] <= fvg.high and candle['high'] >= fvg.low:
                    fvg.partially_filled = True
                if candle['low'] <= fvg.low:
                    fvg.filled = True
                    break
            elif fvg.gap_type == 'bearish':
                if candle['high'] >= fvg.low and candle['low'] <= fvg.high:
                    fvg.partially_filled = True
                if candle['high'] >= fvg.high:
                    fvg.filled = True
                    break
    
    return fvg_zones

def get_active_order_blocks(df: pd.DataFrame, current_price: float, max_distance_pct: float = 5.0) -> List[OrderBlock]:
    """Get order blocks that are still active and near current price"""
    all_blocks = detect_order_blocks(df)
    active_blocks = []
    for ob in all_blocks:
        if ob.broken:
            continue
        ob_center = (ob.high + ob.low) / 2
        distance_pct = abs(current_price - ob_center) / current_price * 100
        if distance_pct <= max_distance_pct:
            active_blocks.append(ob)
    return active_blocks

def analyze_order_block_confluence(order_blocks: List[OrderBlock], current_price: float) -> Dict:
    """Analyze confluence of multiple order blocks"""
    if not order_blocks:
        return {
            "confluence": 0,
            "strength": 0,
            "direction": "none",
            "bullish_blocks": 0,
            "bearish_blocks": 0,
            "total_blocks": 0
        }
    
    bullish_blocks = [ob for ob in order_blocks if ob.block_type == 'bullish' and not ob.broken]
    bearish_blocks = [ob for ob in order_blocks if ob.block_type == 'bearish' and not ob.broken]
    
    confluence = 0
    strength = 0
    
    if len(bullish_blocks) > 0:
        confluence += 1
        strength += sum(ob.strength for ob in bullish_blocks) / len(bullish_blocks)
    
    if len(bearish_blocks) > 0:
        confluence += 1
        strength += sum(ob.strength for ob in bearish_blocks) / len(bearish_blocks)
    
    direction = "both"
    if len(bullish_blocks) > 0 and len(bearish_blocks) == 0:
        direction = "bullish"
    elif len(bullish_blocks) == 0 and len(bearish_blocks) > 0:
        direction = "bearish"
    
    strength /= confluence if confluence > 0 else 1
    
    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "bullish_blocks": len(bullish_blocks),
        "bearish_blocks": len(bearish_blocks),
        "total_blocks": len(order_blocks)
    }

def get_active_fvgs(df: pd.DataFrame, current_price: float, max_distance_pct: float = 3.0) -> List[FairValueGap]:
    """Get FVGs that are still active and near current price"""
    all_fvgs = detect_fvg(df)
    active_fvgs = []
    for fvg in all_fvgs:
        if fvg.filled:
            continue
        fvg_center = (fvg.high + fvg.low) / 2
        distance_pct = abs(current_price - fvg_center) / current_price * 100
        if distance_pct <= max_distance_pct:
            active_fvgs.append(fvg)
    return active_fvgs

def analyze_fvg_confluence(fvgs: List[FairValueGap], current_price: float) -> Dict:
    """Analyze confluence of multiple FVGs"""
    if not fvgs:
        return {
            "confluence": 0,
            "strength": 0,
            "direction": "none",
            "bullish_fvgs": 0,
            "bearish_fvgs": 0,
            "total_fvgs": 0
        }
    
    bullish_fvgs = [fvg for fvg in fvgs if fvg.gap_type == 'bullish' and not fvg.filled]
    bearish_fvgs = [fvg for fvg in fvgs if fvg.gap_type == 'bearish' and not fvg.filled]
    
    confluence = 0
    strength = 0
    
    if len(bullish_fvgs) > 0:
        confluence += 1
        strength += sum(fvg.strength for fvg in bullish_fvgs) / len(bullish_fvgs)
    
    if len(bearish_fvgs) > 0:
        confluence += 1
        strength += sum(fvg.strength for fvg in bearish_fvgs) / len(bearish_fvgs)
    
    direction = "both"
    if len(bullish_fvgs) > 0 and len(bearish_fvgs) == 0:
        direction = "bullish"
    elif len(bullish_fvgs) == 0 and len(bearish_fvgs) > 0:
        direction = "bearish"
    
    strength /= confluence if confluence > 0 else 1
    
    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "bullish_fvgs": len(bullish_fvgs),
        "bearish_fvgs": len(bearish_fvgs),
        "total_fvgs": len(fvgs)
    }

def get_active_liquidity_zones(df: pd.DataFrame, current_price: float, max_distance_pct: float = 10.0) -> List[LiquidityZone]:
    """Get active liquidity zones near current price"""
    all_zones = detect_liquidity_zones(df)
    active_zones = []
    for zone in all_zones:
        if not zone.active:
            continue
        distance_pct = abs(current_price - zone.center) / current_price * 100
        if distance_pct <= max_distance_pct:
            active_zones.append(zone)
    return active_zones

def analyze_liquidity_confluence(zones: List[LiquidityZone], current_price: float) -> Dict:
    """Analyze confluence of liquidity zones"""
    if not zones:
        return {
            "confluence": 0,
            "strength": 0,
            "direction": "none",
            "buy_side_zones": 0,
            "sell_side_zones": 0,
            "total_zones": 0
        }
    
    buy_side_zones = [zone for zone in zones if zone.zone_type == LiquidityType.BUY_SIDE and zone.active]
    sell_side_zones = [zone for zone in zones if zone.zone_type == LiquidityType.SELL_SIDE and zone.active]
    
    confluence = 0
    strength = 0
    
    if len(buy_side_zones) > 0:
        confluence += 1
        strength += sum(zone.strength for zone in buy_side_zones) / len(buy_side_zones)
    
    if len(sell_side_zones) > 0:
        confluence += 1
        strength += sum(zone.strength for zone in sell_side_zones) / len(sell_side_zones)
    
    direction = "both"
    if len(buy_side_zones) > 0 and len(sell_side_zones) == 0:
        direction = "bullish"
    elif len(buy_side_zones) == 0 and len(sell_side_zones) > 0:
        direction = "bearish"
    
    strength /= confluence if confluence > 0 else 1
    
    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "buy_side_zones": len(buy_side_zones),
        "sell_side_zones": len(sell_side_zones),
        "total_zones": len(zones),
        "nearest_zone": min(zones, key=lambda z: abs(current_price - z.center)) if zones else None
    }

def detect_market_structure(df: pd.DataFrame) -> dict:
    """
    Detect market structure based on swing highs and lows
    
    Args:
        df: OHLCV DataFrame
    
    Returns:
        dict: Market structure analysis results
    """
    structure = {
        "trend": "consolidation",
        "event": None,
        "highs": [],
        "lows": [],
    }

    df["swing_high"] = df["high"][(df["high"] > df["high"].shift(1)) & (df["high"] > df["high"].shift(-1))]
    df["swing_low"] = df["low"][(df["low"] < df["low"].shift(1)) & (df["low"] < df["low"].shift(-1))]

    highs = df["swing_high"].dropna()
    lows = df["swing_low"].dropna()

    if len(highs) > 1 and highs.iloc[-1] > highs.iloc[-2]:
        structure["event"] = "BOS"
        structure["trend"] = "bullish"
    elif len(lows) > 1 and lows.iloc[-1] < lows.iloc[-2]:
        structure["event"] = "CHoCH"
        structure["trend"] = "bearish"
    else:
        structure["trend"] = "consolidation"

    structure["highs"] = highs
    structure["lows"] = lows

    return structure

def calculate_premium_discount_zones(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Calculate Premium/Discount Zones"""
    zones = []

    for i in range(len(df)):
        try:
            high = float(df.iloc[i]['high'])
            low = float(df.iloc[i]['low'])
            equilibrium = (high + low) / 2

            zones.append({
                'high': high,
                'low': low,
                'equilibrium': equilibrium,
                'premium_range': high - equilibrium,
                'discount_range': equilibrium - low,
                'total_range': high - low,
                'premium_pct': ((high - equilibrium) / (high - low)) * 100,
                'discount_pct': ((equilibrium - low) / (high - low)) * 100,
            })
        except (TypeError, ValueError):
            continue

    return zones

def resample_ohlcv(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """Resample OHLCV data to the specified timeframe"""
    if timeframe not in ['1H', '4H', '1D', '1W']:
        raise ValueError(f"Unsupported timeframe: {timeframe}")

    resample_rule = {
        '1H': 'H',
        '4H': '4H',
        '1D': 'D',
        '1W': 'W'
    }[timeframe]

    resampled_df = df.resample(resample_rule).agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }).dropna()

    return resampled_df

def analyze_multiple_timeframes(df: pd.DataFrame, timeframes: List[str]) -> Dict[str, Any]:
    """Analyze multiple timeframes for SMC features"""
    results = {}

    for timeframe in timeframes:
        resampled_df = resample_ohlcv(df, timeframe)
        results[timeframe] = {
            'order_blocks': detect_order_blocks(resampled_df),
            'fvg_zones': detect_fvg(resampled_df),
            'liquidity_zones': detect_liquidity_zones(resampled_df),
        }

    return results
