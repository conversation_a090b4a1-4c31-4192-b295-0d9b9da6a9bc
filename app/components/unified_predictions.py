import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Optional, Dict
import os
import sys
import logging
import configparser

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from models.predict import predict_future_prices, predict_from_live_data
from app.models.model_factory import ModelFactory
from scrapers.price_scraper import PriceScraper
from app.utils.config_helpers import clean_config_value
from app.utils.data_processing import is_model_trained
from app.components.prediction import prediction_component
from app.components.advanced_prediction import advanced_prediction_component

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_api_server_status():
    """Check if TradingView API server is running"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_price_from_api_server(symbol: str) -> Optional[Dict]:
    """Fetch price data from TradingView API server"""
    try:
        import requests
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                # Extract price from the API response
                raw_price = stock_data[0].get('price', 0)

                # Convert from piasters to EGP (divide by 1000)
                # API returns prices in piasters (81500 = 81.50 EGP)
                price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                return {
                    'symbol': symbol,
                    'price': price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'api_data': stock_data[0]  # Store full API data for advanced analysis
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching from API server: {str(e)}")
        return None

def safe_get_live_data_unified(symbol: str) -> Optional[pd.DataFrame]:
    """Safely fetch live data with comprehensive error handling and API server integration"""
    try:
        from scrapers.price_scraper import PriceScraper
        from datetime import datetime

        with st.spinner("Fetching latest price data..."):
            # Check if API server is available
            api_status = check_api_server_status()

            if api_status:
                st.info("🔥 Using TradingView API server for enhanced data")
                price_data = fetch_price_from_api_server(symbol)
                source_used = "TradingView API"
            else:
                st.info("📡 Using direct TradingView scraping")
                scraper = PriceScraper(source="tradingview")
                try:
                    price_data = scraper.get_price(symbol)
                    source_used = "TradingView Direct"
                finally:
                    scraper.close_driver()

            if price_data and isinstance(price_data, dict):
                # Convert scraper data format to prediction-compatible format
                current_time = datetime.now()
                price = price_data.get('price', 0)

                # Create DataFrame with required columns for predictions
                converted_data = {
                    'Date': current_time,
                    'Open': price,
                    'High': price * 1.001,  # Simulate small high variation
                    'Low': price * 0.999,   # Simulate small low variation
                    'Close': price,         # Main price for predictions
                    'Volume': 1000000,      # Default volume
                    'symbol': price_data.get('symbol', symbol),
                    'currency': price_data.get('currency', 'EGP'),
                    'timestamp': price_data.get('timestamp', current_time.isoformat()),
                    'source': source_used,
                    'real_time': price_data.get('real_time', False)
                }

                df = pd.DataFrame([converted_data])

                # Ensure Date is datetime
                df['Date'] = pd.to_datetime(df['Date'])

                # Check if this is sample data
                is_sample = price_data.get('source', '').lower() == 'sample data'
                is_real_time = price_data.get('real_time', False)
                is_api = source_used == "TradingView API"

                if is_sample:
                    st.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                elif is_api:
                    st.success(f"✅ Price fetched from TradingView API: {price:.2f} EGP")
                    st.info("🔥 Enhanced data with technical analysis available")
                elif is_real_time:
                    st.success(f"🔴 Real-time price fetched: {price:.2f} EGP")
                else:
                    st.success(f"⏱️ Live price fetched: {price:.2f} EGP (15-min delay)")

                # Store in session state
                if 'live_data' not in st.session_state:
                    st.session_state.live_data = df
                else:
                    st.session_state.live_data = pd.concat([st.session_state.live_data, df], ignore_index=True)

                return df
            else:
                st.warning("⚠️ Could not fetch live price data")
                return None

    except Exception as e:
        st.warning(f"⚠️ Error fetching live data: {str(e)}")
        logger.error(f"Live data fetch error: {str(e)}")
        return None

def unified_predictions_component(historical_data, live_data, symbol):
    """
    Unified Streamlit component that combines basic and advanced prediction features

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        symbol (str): Stock symbol
    """
    st.title("Unified Predictions")

    # Add description
    st.markdown("""
    This page combines all prediction capabilities in one place. Choose the prediction type that best suits your needs.
    """)

    # Add explanation about horizon units
    with st.expander("📊 Understanding Prediction Horizons", expanded=False):
        st.markdown("""
        ### How Prediction Horizons Work

        All models are internally trained using minute-based horizons. When you select days or weeks, they are converted to minutes:

        - **1 day = 1440 minutes** (24 hours × 60 minutes)
        - **1 week = 10080 minutes** (7 days × 24 hours × 60 minutes)

        ### Recommended Horizons

        - **Short-term (minutes)**: 5, 15, 30, 60 minutes
        - **Medium-term (days)**: 1, 2, 3, 5, 7 days
        - **Long-term (weeks)**: 1, 2, 4, 8, 12 weeks

        ### Training Models

        Before making predictions, you need to train models for the specific horizons you want to predict.
        You can do this on the Train Model page or by clicking the "Train More Models" button at the bottom of this page.
        """)

    if historical_data is None:
        st.warning("Please upload historical data first")
        return

    if symbol is None or symbol == "":
        st.warning("Please provide a stock symbol")
        return

    # Create tabs for different prediction types
    tabs = st.tabs(["Basic Predictions", "Advanced Predictions", "Quick Predictions"])

    # Basic Predictions tab
    with tabs[0]:
        st.header("Basic Predictions")
        # Use a unique key prefix for all widgets in this tab
        with st.container():
            # Add a key prefix to avoid conflicts
            key_prefix = "basic_"
            # Run the component with the key prefix
            try:
                # Create a wrapper function that adds the key prefix to all st widgets
                def st_with_key_prefix(func):
                    def wrapper(*args, **kwargs):
                        if 'key' in kwargs:
                            kwargs['key'] = f"{key_prefix}{kwargs['key']}"
                        return func(*args, **kwargs)
                    return wrapper

                # Save original streamlit functions
                original_button = st.button
                original_checkbox = st.checkbox
                original_selectbox = st.selectbox
                original_multiselect = st.multiselect
                original_slider = st.slider
                original_radio = st.radio

                # Replace with wrapped versions
                st.button = st_with_key_prefix(st.button)
                st.checkbox = st_with_key_prefix(st.checkbox)
                st.selectbox = st_with_key_prefix(st.selectbox)
                st.multiselect = st_with_key_prefix(st.multiselect)
                st.slider = st_with_key_prefix(st.slider)
                st.radio = st_with_key_prefix(st.radio)

                # Run the component
                prediction_component(historical_data, live_data, symbol)

                # Restore original functions
                st.button = original_button
                st.checkbox = original_checkbox
                st.selectbox = original_selectbox
                st.multiselect = original_multiselect
                st.slider = original_slider
                st.radio = original_radio
            except Exception as e:
                st.error(f"Error in Basic Predictions tab: {str(e)}")
                import traceback
                st.code(traceback.format_exc())

    # Advanced Predictions tab
    with tabs[1]:
        st.header("Advanced Predictions")
        # Use a unique key prefix for all widgets in this tab
        with st.container():
            # Add a key prefix to avoid conflicts
            key_prefix = "advanced_"
            # Run the component with the key prefix
            try:
                # Create a wrapper function that adds the key prefix to all st widgets
                def st_with_key_prefix(func):
                    def wrapper(*args, **kwargs):
                        if 'key' in kwargs:
                            kwargs['key'] = f"{key_prefix}{kwargs['key']}"
                        return func(*args, **kwargs)
                    return wrapper

                # Save original streamlit functions
                original_button = st.button
                original_checkbox = st.checkbox
                original_selectbox = st.selectbox
                original_multiselect = st.multiselect
                original_slider = st.slider
                original_radio = st.radio

                # Replace with wrapped versions
                st.button = st_with_key_prefix(st.button)
                st.checkbox = st_with_key_prefix(st.checkbox)
                st.selectbox = st_with_key_prefix(st.selectbox)
                st.multiselect = st_with_key_prefix(st.multiselect)
                st.slider = st_with_key_prefix(st.slider)
                st.radio = st_with_key_prefix(st.radio)

                # Run the component
                advanced_prediction_component(historical_data, live_data, symbol)

                # Restore original functions
                st.button = original_button
                st.checkbox = original_checkbox
                st.selectbox = original_selectbox
                st.multiselect = original_multiselect
                st.slider = original_slider
                st.radio = original_radio
            except Exception as e:
                st.error(f"Error in Advanced Predictions tab: {str(e)}")
                import traceback
                st.code(traceback.format_exc())

    # Quick Predictions tab - a simplified version for quick results
    with tabs[2]:
        st.header("Quick Predictions")
        quick_predictions(historical_data, live_data, symbol)

    # Add a section at the bottom to show trained model information
    st.markdown("---")
    st.subheader("Available Trained Models")

    # Get trained models using the ModelFactory
    trained_models = ModelFactory.get_trained_models(symbol)

    if not trained_models:
        st.warning(f"No trained models found for {symbol}. Please go to the Train Model page to train models.")

        # Add a button to go to the Train Model page
        if st.button("Go to Train Model page", key="goto_train_from_unified"):
            st.session_state.page = "Train Model"
    else:
        # Display trained models in a more user-friendly format
        st.write(f"The following models are trained for {symbol}:")

        for model_type, horizons in trained_models.items():
            # Get user-friendly model name
            model_name = ModelFactory.get_available_models().get(model_type, model_type)

            # Convert minutes to appropriate units for display
            minute_horizons = []
            day_horizons = []
            week_horizons = []

            for h in horizons:
                if h < 24*60:  # Less than a day
                    minute_horizons.append(h)
                elif h < 7*24*60:  # Less than a week
                    day_horizons.append(h // (24*60))
                else:  # A week or more
                    week_horizons.append(h // (7*24*60))

            # Create expandable sections for each model type
            with st.expander(f"{model_name} ({len(horizons)} horizons)"):
                if minute_horizons:
                    st.write(f"**Minutes:** {', '.join(map(str, minute_horizons))}")
                if day_horizons:
                    st.write(f"**Days:** {', '.join(map(str, day_horizons))}")
                if week_horizons:
                    st.write(f"**Weeks:** {', '.join(map(str, week_horizons))}")

        # Add a note about training more models
        st.info("You can train more models with different horizons on the Train Model page.")

        # Add a button to go to the Train Model page
        if st.button("Train More Models", key="train_more_from_unified"):
            st.session_state.page = "Train Model"

def quick_predictions(historical_data, live_data, symbol):
    """
    Simplified prediction component for quick results

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        symbol (str): Stock symbol
    """
    st.info("Quick predictions use pre-configured settings to generate forecasts with minimal input.")

    # Pre-defined horizons
    horizons = [15, 30, 60]  # minutes

    # Model selection (simplified)
    model_options = ["LSTM", "Random Forest", "Ensemble"]
    selected_model = st.selectbox("Select model type", options=model_options, key="quick_model_select")

    # Map user-friendly names to actual model types
    model_map = {
        "LSTM": "lstm",
        "Random Forest": "rf",
        "Ensemble": "ensemble"
    }

    model_type = model_map[selected_model]

    # Option to fetch fresh live data
    fetch_live_data = st.checkbox("Fetch fresh live data", value=True, key="quick_fetch_live")

    # Generate predictions button
    if st.button("Generate Quick Predictions", key="quick_generate_btn"):
        with st.spinner("Generating predictions..."):
            try:
                # Fetch fresh live data if requested
                current_live_data = live_data
                if fetch_live_data:
                    # Use the modern API server approach (same as Advanced Analysis)
                    current_live_data = safe_get_live_data_unified(symbol)

                # Check if models are trained
                models_trained = []
                for horizon in horizons:
                    if is_model_trained(symbol, horizon, model_type, 'saved_models', 'minutes'):
                        models_trained.append(horizon)

                if not models_trained:
                    st.warning("No trained models found for the selected horizons. Please train models first.")

                    # Add a button to go to the Train Model page
                    if st.button("Go to Train Model page", key="quick_goto_train"):
                        st.session_state.page = "Train Model"
                    return

                # Use only trained horizons
                trained_horizons = models_trained

                # Make predictions
                if current_live_data is not None and not current_live_data.empty:
                    predictions = predict_from_live_data(
                        current_live_data, historical_data, symbol,
                        horizons=trained_horizons,
                        model_type=model_type,
                        models_path='saved_models'
                    )
                    st.info(f"Using live price data: {current_live_data['Close'].iloc[-1]:.2f} EGP")
                else:
                    predictions = predict_future_prices(
                        historical_data, symbol,
                        horizons=trained_horizons,
                        model_type=model_type,
                        models_path='saved_models'
                    )
                    st.info(f"Using historical price data: {historical_data['Close'].iloc[-1]:.2f} EGP")

                # Display predictions
                if predictions:
                    st.success("Predictions generated successfully")

                    # Create a table of predictions
                    pred_data = []
                    current_time = datetime.now()

                    for horizon in trained_horizons:
                        pred_time = current_time + timedelta(minutes=horizon)
                        pred_data.append({
                            'Horizon (minutes)': horizon,
                            'Predicted Time': pred_time.strftime('%Y-%m-%d %H:%M:%S'),
                            'Predicted Price': round(predictions[horizon], 2)
                        })

                    # Create and display the predictions dataframe
                    pred_df = pd.DataFrame(pred_data)
                    st.dataframe(pred_df)

                    # Plot predictions
                    plot_quick_predictions(historical_data, current_live_data, predictions, symbol)
                else:
                    st.error("Failed to generate predictions")

            except Exception as e:
                st.error(f"Error generating predictions: {str(e)}")
                logger.error(f"Error generating predictions: {str(e)}")

def plot_quick_predictions(historical_data, live_data, predictions, symbol):
    """
    Plot historical data and predictions

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        predictions (dict): Dictionary with predictions for each horizon
        symbol (str): Stock symbol
    """
    # Create figure
    fig = go.Figure()

    # Add historical data (last 30 points for clarity)
    recent_data = historical_data.tail(30)
    fig.add_trace(go.Scatter(
        x=recent_data['Date'],
        y=recent_data['Close'],
        mode='lines',
        name='Historical',
        line=dict(color='blue')
    ))

    # Add live data if available
    if live_data is not None and not live_data.empty:
        fig.add_trace(go.Scatter(
            x=live_data['Date'],
            y=live_data['Close'],
            mode='markers',
            name='Live',
            marker=dict(color='green', size=10)
        ))

    # Add predictions
    current_time = datetime.now()
    colors = ['red', 'orange', 'purple']

    for i, (horizon, price) in enumerate(predictions.items()):
        # Get color for this horizon
        color = colors[i % len(colors)]

        # Calculate prediction time
        pred_time = current_time + timedelta(minutes=horizon)

        # Add prediction point
        fig.add_trace(go.Scatter(
            x=[pred_time],
            y=[price],
            mode='markers',
            name=f'{horizon} minutes',
            marker=dict(color=color, size=10, symbol='star')
        ))

    # Update layout
    fig.update_layout(
        title=f'{symbol} Price Forecast',
        xaxis_title='Date',
        yaxis_title='Price',
        hovermode='x unified'
    )

    # Show plot
    st.plotly_chart(fig, use_container_width=True)
