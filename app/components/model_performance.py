"""
Model performance visualization component for the AI Stocks Bot.

This component provides visualizations and analysis of model performance metrics.
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Union, Tuple, Any

# Import performance tracking functions
from app.utils.performance import (
    get_model_performance, get_best_model, update_model_performance
)

# Configure logging
logger = logging.getLogger(__name__)


def model_performance_component(symbol: str):
    """
    Display model performance metrics and visualizations.

    Args:
        symbol (str): Stock symbol
    """
    st.header("Model Performance Analysis")

    # Get performance data
    performance_data = get_model_performance(symbol)

    if performance_data.empty:
        st.warning(f"No performance data available for {symbol}. Make predictions to generate performance metrics.")
        return

    # Create tabs for different views
    tab1, tab2, tab3 = st.tabs(["Overview", "Detailed Metrics", "Model Comparison"])

    with tab1:
        st.subheader("Performance Overview")

        # Get unique models and horizons
        models = performance_data['model'].unique()
        horizons = performance_data['horizon'].unique()

        # Create columns for filters
        col1, col2 = st.columns(2)

        with col1:
            selected_model = st.selectbox(
                "Select Model",
                options=["All"] + list(models),
                index=0
            )

        with col2:
            selected_horizon = st.selectbox(
                "Select Horizon (minutes)",
                options=["All"] + list(horizons),
                index=0
            )

        # Filter data based on selection
        filtered_data = performance_data.copy()

        if selected_model != "All":
            filtered_data = filtered_data[filtered_data['model'] == selected_model]

        if selected_horizon != "All":
            filtered_data = filtered_data[filtered_data['horizon'] == selected_horizon]

        if filtered_data.empty:
            st.warning("No data available for the selected filters.")
            return

        # Display summary metrics
        st.subheader("Summary Metrics")

        # Calculate average metrics with outlier removal
        # Remove extreme outliers (values more than 3 standard deviations from mean)
        mse_data = filtered_data['mse']
        mae_data = filtered_data['mae']
        mape_data = filtered_data['mape']
        hit_ratio_data = filtered_data['hit_ratio']

        # Function to remove outliers
        def remove_outliers(series):
            if len(series) <= 5:  # Not enough data for reliable outlier detection
                return series
            mean = series.mean()
            std = series.std()
            return series[abs(series - mean) <= 3 * std]

        # Apply outlier removal
        mse_clean = remove_outliers(mse_data)
        mae_clean = remove_outliers(mae_data)
        mape_clean = remove_outliers(mape_data)

        # Calculate metrics with cleaned data
        avg_metrics = {
            "MSE": mse_clean.mean(),
            "MAE": mae_clean.mean(),
            "MAPE (%)": mape_clean.mean(),
            "Hit Ratio (%)": hit_ratio_data.mean() * 100  # Hit ratio is already bounded [0,1]
        }

        # Create columns for metrics
        metric_cols = st.columns(4)

        for i, (metric, value) in enumerate(avg_metrics.items()):
            with metric_cols[i]:
                st.metric(
                    label=metric,
                    value=f"{value:.2f}"
                )

        # Plot error distribution
        st.subheader("Error Distribution")

        # Filter out extreme errors for better visualization
        error_data = filtered_data.copy()

        # Calculate error statistics
        error_mean = error_data['error'].mean()
        error_std = error_data['error'].std()

        # Filter to +/- 5 standard deviations for visualization
        if len(error_data) > 10:  # Only apply if we have enough data
            error_data = error_data[
                (error_data['error'] >= error_mean - 5 * error_std) &
                (error_data['error'] <= error_mean + 5 * error_std)
            ]

            if len(error_data) < len(filtered_data):
                st.info(f"Filtered {len(filtered_data) - len(error_data)} extreme outliers for better visualization")

        fig = px.histogram(
            error_data,
            x="error",
            nbins=50,
            title="Prediction Error Distribution",
            labels={"error": "Error (Actual - Predicted)"},
            color_discrete_sequence=["#3366CC"]
        )

        fig.update_layout(
            xaxis_title="Error",
            yaxis_title="Count",
            showlegend=False
        )

        st.plotly_chart(fig, use_container_width=True)

        # Plot error over time
        st.subheader("Prediction Error Over Time")

        # Convert timestamp to datetime if it's not already
        if 'timestamp' in filtered_data.columns and not pd.api.types.is_datetime64_any_dtype(filtered_data['timestamp']):
            filtered_data['timestamp'] = pd.to_datetime(filtered_data['timestamp'])

        # Sort by timestamp
        time_sorted_data = filtered_data.sort_values('timestamp')

        fig = px.line(
            time_sorted_data,
            x="timestamp",
            y="error",
            title="Prediction Error Over Time",
            labels={"error": "Error (Actual - Predicted)", "timestamp": "Time"},
            color_discrete_sequence=["#3366CC"]
        )

        # Add a horizontal line at y=0
        fig.add_shape(
            type="line",
            x0=time_sorted_data['timestamp'].min(),
            y0=0,
            x1=time_sorted_data['timestamp'].max(),
            y1=0,
            line=dict(color="red", width=2, dash="dash")
        )

        fig.update_layout(
            xaxis_title="Time",
            yaxis_title="Error",
            showlegend=False
        )

        st.plotly_chart(fig, use_container_width=True)

    with tab2:
        st.subheader("Detailed Performance Metrics")

        # Group by model and horizon
        if not performance_data.empty:
            # Create a pivot table of metrics
            metrics_pivot = pd.pivot_table(
                performance_data,
                values=['mse', 'mae', 'mape', 'hit_ratio'],
                index=['model'],
                columns=['horizon'],
                aggfunc='mean'
            )

            # Format the pivot table
            formatted_pivot = pd.DataFrame()

            for metric in ['mse', 'mae', 'mape', 'hit_ratio']:
                if (metric, performance_data['horizon'].iloc[0]) in metrics_pivot.columns:
                    metric_df = metrics_pivot[metric].copy()

                    # Format column names
                    metric_df.columns = [f"{col}m" for col in metric_df.columns]

                    # Add metric name to index
                    metric_df.index = [f"{idx} - {metric.upper()}" for idx in metric_df.index]

                    # Append to formatted pivot
                    formatted_pivot = pd.concat([formatted_pivot, metric_df])

            # Display the pivot table
            st.dataframe(formatted_pivot.style.format("{:.4f}"), use_container_width=True)

            # Allow downloading the data
            csv = performance_data.to_csv(index=False)
            st.download_button(
                label="Download Performance Data",
                data=csv,
                file_name=f"{symbol}_performance_data.csv",
                mime="text/csv"
            )
        else:
            st.warning("No performance data available.")

    with tab3:
        st.subheader("Model Comparison")

        # Select metrics to compare
        selected_metrics = st.multiselect(
            "Select Metrics to Compare",
            options=["MSE", "MAE", "MAPE", "Hit Ratio"],
            default=["MSE", "Hit Ratio"]
        )

        if not selected_metrics:
            st.warning("Please select at least one metric to compare.")
            return

        # Map selected metrics to dataframe columns
        metric_map = {
            "MSE": "mse",
            "MAE": "mae",
            "MAPE": "mape",
            "Hit Ratio": "hit_ratio"
        }

        # Group by model and calculate average metrics
        model_metrics = performance_data.groupby('model')[
            [metric_map[m] for m in selected_metrics]
        ].mean().reset_index()

        # Create bar charts for each metric
        for metric in selected_metrics:
            df_col = metric_map[metric]

            fig = px.bar(
                model_metrics,
                x="model",
                y=df_col,
                title=f"{metric} by Model",
                labels={"model": "Model", df_col: metric},
                color="model",
                color_discrete_sequence=px.colors.qualitative.Plotly
            )

            fig.update_layout(
                xaxis_title="Model",
                yaxis_title=metric,
                showlegend=False
            )

            st.plotly_chart(fig, use_container_width=True)

        # Display best model for each horizon
        st.subheader("Best Model by Horizon")

        best_models = {}
        for horizon in sorted(performance_data['horizon'].unique()):
            best_model = get_best_model(symbol, horizon, metric="mse")
            best_models[horizon] = best_model

        # Create a dataframe for display
        best_model_df = pd.DataFrame({
            "Horizon (minutes)": list(best_models.keys()),
            "Best Model": list(best_models.values())
        })

        st.dataframe(best_model_df, use_container_width=True)


def prediction_accuracy_component(symbol: str, historical_predictions: pd.DataFrame = None):
    """
    Display prediction accuracy metrics and visualizations.

    Args:
        symbol (str): Stock symbol
        historical_predictions (pd.DataFrame): Historical prediction data
    """
    st.header("Prediction Accuracy Analysis")

    if historical_predictions is None or historical_predictions.empty:
        st.warning(f"No historical prediction data available for {symbol}.")
        return

    # Ensure we have the required columns
    required_cols = ['Date', 'Actual', 'Predicted']
    if not all(col in historical_predictions.columns for col in required_cols):
        st.warning("Historical prediction data is missing required columns (Date, Actual, Predicted).")
        return

    # Calculate error metrics
    historical_predictions['Error'] = historical_predictions['Actual'] - historical_predictions['Predicted']
    historical_predictions['Abs_Error'] = abs(historical_predictions['Error'])
    historical_predictions['Pct_Error'] = (historical_predictions['Abs_Error'] / historical_predictions['Actual']) * 100

    # Display summary metrics
    st.subheader("Summary Metrics")

    mse = (historical_predictions['Error'] ** 2).mean()
    mae = historical_predictions['Abs_Error'].mean()
    mape = historical_predictions['Pct_Error'].mean()

    # Create columns for metrics
    metric_cols = st.columns(3)

    with metric_cols[0]:
        st.metric(
            label="Mean Squared Error",
            value=f"{mse:.4f}"
        )

    with metric_cols[1]:
        st.metric(
            label="Mean Absolute Error",
            value=f"{mae:.4f}"
        )

    with metric_cols[2]:
        st.metric(
            label="Mean Absolute Percentage Error",
            value=f"{mape:.2f}%"
        )

    # Plot actual vs predicted
    st.subheader("Actual vs Predicted Values")

    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=historical_predictions['Date'],
        y=historical_predictions['Actual'],
        mode='lines',
        name='Actual',
        line=dict(color='blue')
    ))

    fig.add_trace(go.Scatter(
        x=historical_predictions['Date'],
        y=historical_predictions['Predicted'],
        mode='lines',
        name='Predicted',
        line=dict(color='red')
    ))

    fig.update_layout(
        title="Actual vs Predicted Values",
        xaxis_title="Date",
        yaxis_title="Price",
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
    )

    st.plotly_chart(fig, use_container_width=True)

    # Plot error distribution
    st.subheader("Error Distribution")

    fig = px.histogram(
        historical_predictions,
        x="Error",
        nbins=30,
        title="Prediction Error Distribution",
        labels={"Error": "Error (Actual - Predicted)"},
        color_discrete_sequence=["#3366CC"]
    )

    fig.update_layout(
        xaxis_title="Error",
        yaxis_title="Count",
        showlegend=False
    )

    st.plotly_chart(fig, use_container_width=True)
