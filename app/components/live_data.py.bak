import streamlit as st
import pandas as pd
from datetime import datetime
import time
import os
import sys
import logging

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from scrapers.price_scraper import PriceScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def live_data_component(symbol):
    """
    Streamlit component for displaying live data

    Args:
        symbol (str): Stock symbol

    Returns:
        pd.DataFrame: DataFrame with live data
    """
    st.subheader("Live Price Data")

    if not symbol:
        st.warning("Please upload a CSV file and provide a stock symbol first")
        return None

    # Scraper source selection
    source = st.selectbox(
        "Select data source",
        options=["TradingView", "Mubasher"],
        index=0
    )

    # Initialize scraper
    scraper = PriceScraper(source=source.lower())

    # Get live data
    if st.button("Fetch Live Price"):
        try:
            with st.spinner("Fetching live price..."):
                price_data = scraper.get_price(symbol)

                if price_data:
                    # Create DataFrame
                    live_df = pd.DataFrame([price_data])

                    # Display live data
                    st.success(f"Live price fetched at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    st.dataframe(live_df)

                    # Store in session state
                    if 'live_data' not in st.session_state:
                        st.session_state.live_data = live_df
                    else:
                        st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)

                    # Return live data
                    return live_df
                else:
                    st.error(f"Failed to fetch live price for {symbol}")
                    return None

        except Exception as e:
            st.error(f"Error fetching live price: {str(e)}")
            logger.error(f"Error fetching live price: {str(e)}")
            return None

    # Auto-refresh option
    auto_refresh = st.checkbox("Enable auto-refresh")

    if auto_refresh:
        refresh_interval = st.slider("Refresh interval (seconds)", min_value=30, max_value=300, value=60, step=30)

        # Create a placeholder for live data
        live_data_placeholder = st.empty()

        # Auto-refresh logic
        try:
            with st.spinner("Fetching live price..."):
                price_data = scraper.get_price(symbol)

                if price_data:
                    # Create DataFrame
                    live_df = pd.DataFrame([price_data])

                    # Display live data
                    live_data_placeholder.success(f"Live price fetched at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    live_data_placeholder.dataframe(live_df)

                    # Store in session state
                    if 'live_data' not in st.session_state:
                        st.session_state.live_data = live_df
                    else:
                        st.session_state.live_data = pd.concat([st.session_state.live_data, live_df], ignore_index=True)

                    # Return live data
                    return live_df
                else:
                    live_data_placeholder.error(f"Failed to fetch live price for {symbol}")
                    return None

        except Exception as e:
            st.error(f"Error fetching live price: {str(e)}")
            logger.error(f"Error fetching live price: {str(e)}")
            return None

    # Return existing live data if available
    if 'live_data' in st.session_state:
        st.dataframe(st.session_state.live_data)
        return st.session_state.live_data

    return None
