"""
Status indicator component for the AI Stocks Bot app.
This component provides a modern way to display progress and status for long-running operations.
"""
import streamlit as st
import time
from typing import Callable, Optional, Any, List, Dict
import logging
from contextlib import contextmanager

# Configure logging
logger = logging.getLogger(__name__)

@contextmanager
def status_indicator(message: str, expanded: bool = True, state: str = "running"):
    """
    Context manager for displaying a status indicator during long-running operations.
    
    Args:
        message (str): The message to display in the status indicator
        expanded (bool): Whether the status should be expanded by default
        state (str): Initial state of the status indicator ("running", "complete", "error")
    
    Yields:
        st.status: The status object that can be updated
    """
    try:
        # Create the status indicator
        with st.status(message, expanded=expanded, state=state) as status:
            # Yield the status object so it can be updated
            yield status
            # Set to complete when the context exits normally
            status.update(state="complete")
    except Exception as e:
        # If an exception occurs, set the status to error
        if 'status' in locals():
            status.update(state="error", expanded=True)
            with status:
                st.error(f"Error: {str(e)}")
        # Re-raise the exception
        raise

def timed_operation(func: Callable, *args, message: str = "Operation in progress...", **kwargs) -> Any:
    """
    Execute a function while displaying a status indicator with timing information.
    
    Args:
        func (Callable): The function to execute
        message (str): The message to display in the status indicator
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function
    
    Returns:
        Any: The result of the function
    """
    start_time = time.time()
    
    with status_indicator(message) as status:
        # Execute the function
        result = func(*args, **kwargs)
        
        # Calculate elapsed time
        elapsed_time = time.time() - start_time
        
        # Update status with timing information
        status.update(label=f"{message} (Completed in {elapsed_time:.2f}s)")
        
        return result

def multi_step_operation(steps: List[Dict[str, Any]], title: str = "Multi-step operation in progress...") -> List[Any]:
    """
    Execute a series of steps while displaying progress in a status indicator.
    
    Args:
        steps (List[Dict]): List of step dictionaries, each containing:
            - 'name': Step name
            - 'func': Function to execute
            - 'args': (Optional) Arguments to pass to the function
            - 'kwargs': (Optional) Keyword arguments to pass to the function
        title (str): The title to display in the status indicator
    
    Returns:
        List[Any]: List of results from each step
    """
    results = []
    start_time = time.time()
    
    with status_indicator(title) as status:
        for i, step in enumerate(steps):
            step_name = step['name']
            func = step['func']
            args = step.get('args', [])
            kwargs = step.get('kwargs', {})
            
            # Update status to show current step
            status.update(label=f"{title} - Step {i+1}/{len(steps)}: {step_name}")
            
            # Add a progress message within the status
            with status:
                step_start = time.time()
                st.write(f"Running: {step_name}...")
            
            # Execute the function
            try:
                result = func(*args, **kwargs)
                results.append(result)
                
                # Calculate step elapsed time
                step_elapsed = time.time() - step_start
                
                # Update status with success message
                with status:
                    st.success(f"✅ {step_name} completed in {step_elapsed:.2f}s")
            
            except Exception as e:
                # Handle error in this step
                with status:
                    st.error(f"❌ {step_name} failed: {str(e)}")
                logger.error(f"Error in step '{step_name}': {str(e)}")
                # Add None to results to maintain index alignment
                results.append(None)
        
        # Calculate total elapsed time
        total_elapsed = time.time() - start_time
        
        # Update final status
        status.update(label=f"{title} (Completed in {total_elapsed:.2f}s)")
        
        with status:
            st.write(f"All steps completed in {total_elapsed:.2f}s")
        
        return results
