"""
LLM Insights Component

This module provides a Streamlit component for integrating LLM-powered insights
into the stock prediction application.
"""

import streamlit as st
import pandas as pd
import numpy as np
import logging
import asyncio
import time
import threading
from typing import Dict, List, Any, Optional, Union
import os
import sys
import json
from datetime import datetime, timedelta

# Import LLM services
from app.services.llm_service import LLMService, LLAMA_CPP_AVAILABLE
from app.services.llm_functions import (
    explain_prediction,
    analyze_news_sentiment,
    generate_investment_thesis,
    summarize_financial_report,
    create_market_insights
)

# Import news service
from app.services.news_service import NewsService

# Import utility functions
from app.utils.common import load_stock_data, get_available_stocks
from app.utils.error_handling import handle_errors, log_execution_time
from app.utils.memory_management import monitor_memory_usage

# Configure logging
logger = logging.getLogger(__name__)

# Check if running in asyncio environment
def is_asyncio_loop_running():
    """Check if asyncio loop is running."""
    try:
        return asyncio.get_event_loop().is_running()
    except RuntimeError:
        return False

# Helper function to run async functions
def run_async(coro):
    """Run an async coroutine and return the result."""
    if is_asyncio_loop_running():
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    else:
        # Use the current event loop
        return asyncio.run(coro)

@log_execution_time
@monitor_memory_usage
@handle_errors()
def llm_insights_component():
    """
    Streamlit component for LLM-powered insights.
    """
    st.title("AI Insights & Analysis")

    # Check if LLM is available
    if not LLAMA_CPP_AVAILABLE:
        st.error("LLM functionality is not available. Please install llama-cpp-python.")
        st.info("You can install it with: pip install llama-cpp-python")
        return

    # Initialize LLM service
    llm_service = LLMService()

    # Check if model file exists
    if not os.path.exists(llm_service.model_path):
        st.error(f"Model file not found: {llm_service.model_path}")
        st.info("Please make sure the model file is in the correct location.")

        # Allow user to specify model path
        custom_model_path = st.text_input(
            "Custom Model Path",
            placeholder="Enter the full path to the .gguf model file"
        )

        if custom_model_path and os.path.exists(custom_model_path):
            llm_service.model_path = custom_model_path
            st.success(f"Using custom model path: {custom_model_path}")
        else:
            return

    # Sidebar for LLM settings
    with st.sidebar:
        st.header("LLM Settings")

        n_threads = st.slider("CPU Threads", min_value=1, max_value=16, value=4)
        n_gpu_layers = st.slider("GPU Layers", min_value=0, max_value=32, value=0)

        # Update LLM service settings
        llm_service.n_threads = n_threads
        llm_service.n_gpu_layers = n_gpu_layers

        # Model loading button
        if st.button("Load Model"):
            with st.spinner("Loading model..."):
                if llm_service.load_model():
                    st.success("Model loaded successfully!")
                else:
                    st.error("Failed to load model. Check logs for details.")

    # Main content
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "Prediction Explanations",
        "News Analysis",
        "Investment Thesis",
        "Financial Reports",
        "Market Insights"
    ])

    # Get available stock symbols
    symbols = get_available_stocks()

    # Tab 1: Prediction Explanations
    with tab1:
        st.header("Explain Predictions")
        st.write("Get natural language explanations of model predictions.")

        # Select stock
        symbol = st.selectbox("Select Stock", symbols, key="explain_symbol")

        # Load stock data
        if symbol:
            stock_data = load_stock_data(symbol)

            if stock_data is not None:
                # Get current price
                current_price = stock_data["Close"].iloc[-1]

                # Input fields
                col1, col2 = st.columns(2)

                with col1:
                    predicted_price = st.number_input(
                        "Predicted Price",
                        min_value=0.0,
                        value=float(current_price * 1.05),  # Default to 5% increase
                        step=0.01
                    )

                    horizon = st.number_input(
                        "Prediction Horizon",
                        min_value=1,
                        value=7,
                        step=1
                    )

                    horizon_unit = st.selectbox(
                        "Horizon Unit",
                        ["days", "weeks", "months"],
                        index=0
                    )

                    # Add a button to fetch model prediction
                    if st.button("Fetch Model Prediction", key="fetch_model_pred_btn"):
                        with st.spinner("Generating prediction from models..."):
                            try:
                                # Import the prediction function
                                from models.predict import predict_future_prices

                                # Convert horizon to minutes based on the unit
                                if horizon_unit == "days":
                                    model_horizon = horizon * 24 * 60  # days to minutes
                                elif horizon_unit == "weeks":
                                    model_horizon = horizon * 7 * 24 * 60  # weeks to minutes
                                elif horizon_unit == "months":
                                    model_horizon = horizon * 30 * 24 * 60  # approximate months to minutes
                                else:
                                    model_horizon = horizon  # already in minutes

                                # Generate predictions using the model
                                predictions = predict_future_prices(
                                    stock_data, symbol,
                                    horizons=[model_horizon],
                                    model_type='auto'
                                )

                                if predictions and model_horizon in predictions:
                                    # Get the predicted price
                                    model_predicted_price = predictions[model_horizon]

                                    # Calculate percent change
                                    percent_change = (model_predicted_price - current_price) / current_price * 100

                                    # Update the predicted price input
                                    st.session_state.predicted_price_input = float(model_predicted_price)

                                    # Show success message with the prediction
                                    st.success(f"Model prediction: EGP {model_predicted_price:.2f} ({percent_change:.2f}% change)")

                                    # Store model info for explanation
                                    st.session_state.model_info = {
                                        "type": "ensemble",
                                        "horizon": model_horizon,
                                        "horizon_unit": "minutes",
                                        "percent_change": percent_change
                                    }
                                else:
                                    st.error(f"No prediction available for {horizon} {horizon_unit} horizon")
                            except Exception as e:
                                st.error(f"Error generating prediction: {str(e)}")
                                import traceback
                                st.error(traceback.format_exc())

                with col2:
                    model_type = st.selectbox(
                        "Model Type",
                        ["LSTM", "Transformer", "Ensemble", "Hybrid", "RandomForest"],
                        index=2
                    )

                    confidence = st.slider(
                        "Confidence Score",
                        min_value=0.0,
                        max_value=1.0,
                        value=0.85,
                        step=0.01
                    )

                # Technical indicators
                with st.expander("Technical Indicators"):
                    # Calculate some basic indicators
                    sma_20 = stock_data["Close"].rolling(window=20).mean().iloc[-1]
                    sma_50 = stock_data["Close"].rolling(window=50).mean().iloc[-1]
                    rsi = 50  # Placeholder, would calculate actual RSI

                    # Allow user to edit
                    col1, col2 = st.columns(2)

                    with col1:
                        sma_20_input = st.number_input("SMA 20", value=float(sma_20), step=0.01)
                        rsi_input = st.number_input("RSI", value=float(rsi), min_value=0.0, max_value=100.0, step=0.1)

                    with col2:
                        sma_50_input = st.number_input("SMA 50", value=float(sma_50), step=0.01)
                        macd_input = st.number_input("MACD", value=0.0, step=0.01)

                # Generate explanation button
                if st.button("Generate Explanation", key="explain_btn"):
                    # Check if model is loaded
                    if not llm_service.is_loaded:
                        with st.spinner("Loading model..."):
                            llm_service.load_model()

                    # Prepare technical indicators
                    tech_indicators = {
                        "SMA 20": sma_20_input,
                        "SMA 50": sma_50_input,
                        "RSI": rsi_input,
                        "MACD": macd_input,
                        "Price vs SMA 20": f"{(current_price / sma_20_input - 1) * 100:.2f}%",
                        "Price vs SMA 50": f"{(current_price / sma_50_input - 1) * 100:.2f}%"
                    }

                    # Generate explanation
                    with st.spinner("Generating explanation..."):
                        explanation = run_async(explain_prediction(
                            symbol=symbol,
                            current_price=current_price,
                            predicted_price=predicted_price,
                            horizon=horizon,
                            horizon_unit=horizon_unit,
                            model_type=model_type,
                            confidence_score=confidence,
                            technical_indicators=tech_indicators
                        ))

                        # Display explanation
                        st.subheader("Prediction Explanation")
                        st.write(explanation)

                        # Add download button for the explanation
                        st.download_button(
                            label="Download Explanation",
                            data=explanation,
                            file_name=f"{symbol}_prediction_explanation.txt",
                            mime="text/plain"
                        )

    # Tab 2: News Analysis
    with tab2:
        st.header("News Sentiment Analysis")
        st.write("Analyze the sentiment of news articles related to stocks.")

        # Select stock
        symbol = st.selectbox("Select Stock", symbols, key="news_symbol")

        # Initialize news service
        news_service = NewsService()

        # Add tabs for different news sources
        news_tabs = st.tabs(["Live News", "Custom News"])

        with news_tabs[0]:
            # Fetch live news
            st.subheader("Live News Articles")

            # News search options
            col1, col2, col3 = st.columns(3)

            with col1:
                days_back = st.slider("Days to look back", min_value=1, max_value=90, value=30)

            with col2:
                article_count = st.slider("Number of articles", min_value=1, max_value=20, value=5)

            with col3:
                market_focus = st.selectbox(
                    "Market Focus",
                    ["Egypt", "Global"],
                    index=0,
                    key="news_market_focus"
                )

            # Additional options
            col1, col2 = st.columns(2)

            with col1:
                specific_sites = st.checkbox(
                    "Limit to financial sites",
                    value=True,
                    help="Limit search to specific financial sites with Egyptian market coverage"
                )

            # Fetch news button
            if st.button("Fetch News Articles", key="fetch_news_btn"):
                with st.spinner("Fetching news articles..."):
                    # Get news articles
                    articles = news_service.get_stock_news(
                        symbol=symbol,
                        days=days_back,
                        page_size=article_count,
                        market=market_focus.lower(),
                        specific_sites=specific_sites
                    )

                    if articles:
                        st.success(f"Found {len(articles)} news articles for {symbol}")

                        # Store articles in session state
                        st.session_state.news_articles = articles

                        # Display articles
                        for i, article in enumerate(articles):
                            with st.expander(f"{i+1}. {article['title']}"):
                                st.write(f"**Source:** {article['source']['name']}")
                                st.write(f"**Published:** {article['publishedAt']}")
                                st.write(f"**Author:** {article.get('author', 'Unknown')}")
                                st.write("---")
                                st.write(article['description'])
                                st.write("---")
                                st.write(article['content'] if article.get('content') else "Full content not available")
                                st.write("---")
                                st.write(f"[Read full article]({article['url']})")

                                # Add analyze button for each article
                                if st.button(f"Analyze Article #{i+1}", key=f"analyze_article_{i}"):
                                    # Check if model is loaded
                                    if not llm_service.is_loaded:
                                        with st.spinner("Loading model..."):
                                            llm_service.load_model()

                                    # Generate analysis
                                    with st.spinner("Analyzing news sentiment..."):
                                        analysis = run_async(analyze_news_sentiment(
                                            symbol=symbol,
                                            news_title=article['title'],
                                            news_content=article['description'] + "\n" + (article.get('content', '')),
                                            news_date=article['publishedAt'],
                                            news_source=article['source']['name']
                                        ))

                                        # Display analysis
                                        st.subheader("Sentiment Analysis")
                                        st.write(analysis)

                                        # Add download button for the analysis
                                        st.download_button(
                                            label="Download Analysis",
                                            data=analysis,
                                            file_name=f"{symbol}_news_analysis_{i+1}.txt",
                                            mime="text/plain",
                                            key=f"download_analysis_{i}"
                                        )
                    else:
                        st.warning(f"No news articles found for {symbol} in the last {days_back} days.")

        with news_tabs[1]:
            # Manual news input
            st.subheader("Custom News Analysis")

            # News input
            news_title = st.text_input("News Title", placeholder="Enter news article title")

            news_content = st.text_area(
                "News Content",
                placeholder="Paste the news article content here",
                height=200
            )

            col1, col2 = st.columns(2)

            with col1:
                news_date = st.date_input("News Date", value=datetime.now())

            with col2:
                news_source = st.text_input("News Source", placeholder="Enter news source")

            # Generate analysis button
            if st.button("Analyze Sentiment", key="news_btn"):
                if not news_title or not news_content:
                    st.warning("Please enter both news title and content.")
                    return

                # Check if model is loaded
                if not llm_service.is_loaded:
                    with st.spinner("Loading model..."):
                        llm_service.load_model()

                # Generate analysis
                with st.spinner("Analyzing news sentiment..."):
                    analysis = run_async(analyze_news_sentiment(
                        symbol=symbol,
                        news_title=news_title,
                        news_content=news_content,
                        news_date=news_date.strftime("%Y-%m-%d"),
                        news_source=news_source
                    ))

                    # Display analysis
                    st.subheader("Sentiment Analysis")
                    st.write(analysis)

                    # Add download button for the analysis
                    st.download_button(
                        label="Download Analysis",
                        data=analysis,
                        file_name=f"{symbol}_news_analysis.txt",
                        mime="text/plain"
                    )

    # Tab 3: Investment Thesis
    with tab3:
        st.header("Investment Thesis Generator")
        st.write("Generate comprehensive investment theses based on prediction data.")

        # Select stock
        symbol = st.selectbox("Select Stock", symbols, key="thesis_symbol")

        # Load stock data
        if symbol:
            stock_data = load_stock_data(symbol)

            if stock_data is not None:
                # Get current price
                current_price = stock_data["Close"].iloc[-1]

                # Input fields
                col1, col2 = st.columns(2)

                with col1:
                    predicted_price = st.number_input(
                        "Predicted Price",
                        min_value=0.0,
                        value=float(current_price * 1.05),  # Default to 5% increase
                        step=0.01,
                        key="thesis_pred_price"
                    )

                    horizon = st.number_input(
                        "Investment Horizon",
                        min_value=1,
                        value=30,
                        step=1,
                        key="thesis_horizon"
                    )

                    horizon_unit = st.selectbox(
                        "Horizon Unit",
                        ["days", "weeks", "months"],
                        index=0,
                        key="thesis_horizon_unit"
                    )

                with col2:
                    model_type = st.selectbox(
                        "Model Type",
                        ["LSTM", "Transformer", "Ensemble", "Hybrid", "RandomForest"],
                        index=2,
                        key="thesis_model_type"
                    )

                    confidence = st.slider(
                        "Confidence Score",
                        min_value=0.0,
                        max_value=1.0,
                        value=0.85,
                        step=0.01,
                        key="thesis_confidence"
                    )

                # Technical analysis
                with st.expander("Technical Analysis"):
                    # Calculate some basic indicators
                    sma_20 = stock_data["Close"].rolling(window=20).mean().iloc[-1]
                    sma_50 = stock_data["Close"].rolling(window=50).mean().iloc[-1]
                    rsi = 50  # Placeholder, would calculate actual RSI

                    # Allow user to edit
                    col1, col2 = st.columns(2)

                    with col1:
                        sma_20_input = st.number_input("SMA 20", value=float(sma_20), step=0.01, key="thesis_sma20")
                        rsi_input = st.number_input("RSI", value=float(rsi), min_value=0.0, max_value=100.0, step=0.1, key="thesis_rsi")

                    with col2:
                        sma_50_input = st.number_input("SMA 50", value=float(sma_50), step=0.01, key="thesis_sma50")
                        macd_input = st.number_input("MACD", value=0.0, step=0.01, key="thesis_macd")

                # Fundamental data
                with st.expander("Fundamental Data"):
                    col1, col2 = st.columns(2)

                    with col1:
                        pe_ratio = st.number_input("P/E Ratio", value=15.0, step=0.1)
                        eps = st.number_input("EPS", value=1.0, step=0.01)

                    with col2:
                        market_cap = st.number_input("Market Cap (M)", value=1000.0, step=1.0)
                        dividend_yield = st.number_input("Dividend Yield (%)", value=2.0, step=0.1)

                # Generate thesis button
                if st.button("Generate Thesis", key="thesis_btn"):
                    # Check if model is loaded
                    if not llm_service.is_loaded:
                        with st.spinner("Loading model..."):
                            llm_service.load_model()

                    # Prepare technical analysis
                    tech_analysis = {
                        "SMA 20": sma_20_input,
                        "SMA 50": sma_50_input,
                        "RSI": rsi_input,
                        "MACD": macd_input,
                        "Price vs SMA 20": f"{(current_price / sma_20_input - 1) * 100:.2f}%",
                        "Price vs SMA 50": f"{(current_price / sma_50_input - 1) * 100:.2f}%"
                    }

                    # Prepare fundamental data
                    fundamental_data = {
                        "P/E Ratio": pe_ratio,
                        "EPS": eps,
                        "Market Cap": f"${market_cap}M",
                        "Dividend Yield": f"{dividend_yield}%"
                    }

                    # Generate thesis
                    with st.spinner("Generating investment thesis..."):
                        thesis = run_async(generate_investment_thesis(
                            symbol=symbol,
                            current_price=current_price,
                            predicted_price=predicted_price,
                            horizon=horizon,
                            horizon_unit=horizon_unit,
                            model_type=model_type,
                            confidence_score=confidence,
                            technical_analysis=tech_analysis,
                            fundamental_data=fundamental_data
                        ))

                        # Display thesis
                        st.subheader("Investment Thesis")
                        st.write(thesis)

                        # Add download button for the thesis
                        st.download_button(
                            label="Download Thesis",
                            data=thesis,
                            file_name=f"{symbol}_investment_thesis.txt",
                            mime="text/plain"
                        )

    # Tabs 4 and 5 would follow a similar pattern
    # For brevity, I'm not implementing them fully here

    with tab4:
        st.header("Financial Report Summarizer")
        st.write("Summarize financial reports and earnings calls with AI")

        # Select stock
        symbol = st.selectbox("Select Stock", symbols, key="report_symbol")

        # Report type selection
        report_type = st.selectbox(
            "Report Type",
            ["Annual Report (10-K)", "Quarterly Report (10-Q)", "Earnings Call Transcript", "Press Release", "Custom Report"],
            key="report_type"
        )

        # Report content
        if report_type == "Custom Report":
            # Manual input for custom report
            report_title = st.text_input("Report Title", placeholder="Enter report title", key="report_title")

            report_content = st.text_area(
                "Report Content",
                placeholder="Paste the report content here",
                height=300,
                key="report_content"
            )

            # Report metadata
            col1, col2 = st.columns(2)

            with col1:
                report_date = st.date_input("Report Date", value=datetime.now(), key="report_date")
                report_quarter = st.selectbox("Quarter", ["Q1", "Q2", "Q3", "Q4", "Annual"], key="report_quarter")

            with col2:
                fiscal_year = st.number_input("Fiscal Year", min_value=2000, max_value=2030, value=datetime.now().year, key="fiscal_year")
        else:
            # Fetch report from NewsAPI
            st.info(f"Searching for {report_type} for {symbol}")

            # Initialize news service
            news_service = NewsService()

            # Search options
            col1, col2, col3 = st.columns(3)

            with col1:
                days_back = st.slider("Days to look back", min_value=30, max_value=365, value=180, key="report_days")

            with col2:
                article_count = st.slider("Number of results", min_value=1, max_value=10, value=3, key="report_count")

            with col3:
                market_focus = st.selectbox(
                    "Market Focus",
                    ["Egypt", "Global"],
                    index=0,
                    key="report_market_focus"
                )

            # Additional options
            col1, col2 = st.columns(2)

            with col1:
                specific_sites = st.checkbox(
                    "Limit to financial sites",
                    value=True,
                    help="Limit search to specific financial sites with Egyptian market coverage",
                    key="report_specific_sites"
                )

            # Search query based on report type
            if report_type == "Annual Report (10-K)":
                search_query = f"{symbol} annual report 10-K"
            elif report_type == "Quarterly Report (10-Q)":
                search_query = f"{symbol} quarterly report 10-Q"
            elif report_type == "Earnings Call Transcript":
                search_query = f"{symbol} earnings call transcript"
            else:  # Press Release
                search_query = f"{symbol} financial press release"

            # Fetch reports button
            if st.button("Search for Reports", key="fetch_reports_btn"):
                with st.spinner(f"Searching for {report_type} for {symbol}..."):
                    # Get financial news
                    articles = news_service.search_financial_news(
                        query=search_query,
                        days=days_back,
                        page_size=article_count,
                        market=market_focus.lower(),
                        specific_sites=specific_sites
                    )

                    if articles:
                        st.success(f"Found {len(articles)} potential reports for {symbol}")

                        # Store articles in session state
                        st.session_state.report_articles = articles

                        # Display articles
                        for i, article in enumerate(articles):
                            with st.expander(f"{i+1}. {article['title']}"):
                                st.write(f"**Source:** {article['source']['name']}")
                                st.write(f"**Published:** {article['publishedAt']}")
                                st.write(f"**Author:** {article.get('author', 'Unknown')}")
                                st.write("---")
                                st.write(article['description'])
                                st.write("---")
                                st.write(article['content'] if article.get('content') else "Full content not available")
                                st.write("---")
                                st.write(f"[Read full article]({article['url']})")

                                # Add summarize button for each article
                                if st.button(f"Summarize Report #{i+1}", key=f"summarize_report_{i}"):
                                    # Check if model is loaded
                                    if not llm_service.is_loaded:
                                        with st.spinner("Loading model..."):
                                            llm_service.load_model()

                                    # Generate summary
                                    with st.spinner("Summarizing financial report..."):
                                        summary = run_async(summarize_financial_report(
                                            symbol=symbol,
                                            report_title=article['title'],
                                            report_content=article['description'] + "\n" + (article.get('content', '')),
                                            report_date=article['publishedAt'],
                                            report_type=report_type
                                        ))

                                        # Display summary
                                        st.subheader("Report Summary")
                                        st.write(summary)

                                        # Add download button for the summary
                                        st.download_button(
                                            label="Download Summary",
                                            data=summary,
                                            file_name=f"{symbol}_{report_type.replace(' ', '_')}_summary.txt",
                                            mime="text/plain",
                                            key=f"download_summary_{i}"
                                        )
                    else:
                        st.warning(f"No reports found for {symbol} in the last {days_back} days.")

        # Only show summarize button for custom reports
        if report_type == "Custom Report":
            # Generate summary button
            if st.button("Generate Summary", key="summary_btn"):
                if not report_title or not report_content:
                    st.warning("Please enter both report title and content.")
                    return

                # Check if model is loaded
                if not llm_service.is_loaded:
                    with st.spinner("Loading model..."):
                        llm_service.load_model()

                # Generate summary
                with st.spinner("Summarizing financial report..."):
                    summary = run_async(summarize_financial_report(
                        symbol=symbol,
                        report_title=report_title,
                        report_content=report_content,
                        report_date=report_date.strftime("%Y-%m-%d"),
                        report_type=f"{report_quarter} {fiscal_year} {report_type}"
                    ))

                    # Display summary
                    st.subheader("Report Summary")
                    st.write(summary)

                    # Add download button for the summary
                    st.download_button(
                        label="Download Summary",
                        data=summary,
                        file_name=f"{symbol}_{report_quarter}_{fiscal_year}_summary.txt",
                        mime="text/plain"
                    )

    with tab5:
        st.header("Market Insights Dashboard")
        st.write("Generate AI-powered market insights and analysis")

        # Initialize news service
        news_service = NewsService()

        # Dashboard options
        insight_type = st.selectbox(
            "Insight Type",
            ["Market Overview", "Sector Analysis", "Stock Comparison", "Economic Indicators", "Custom Analysis"],
            key="insight_type"
        )

        # Market news section
        st.subheader("Market News")

        # News search options
        col1, col2, col3 = st.columns(3)

        with col1:
            news_category = st.selectbox(
                "News Category",
                ["business", "technology", "general", "science", "health"],
                index=0,
                key="news_category"
            )

        with col2:
            news_count = st.slider("Number of articles", min_value=1, max_value=10, value=3, key="market_news_count")

        with col3:
            market_focus = st.selectbox(
                "Market Focus",
                ["Egypt", "Global"],
                index=0,
                key="market_news_focus"
            )

        # Additional options
        col1, col2 = st.columns(2)

        with col1:
            specific_sites = st.checkbox(
                "Limit to financial sites",
                value=True,
                help="Limit search to specific financial sites with Egyptian market coverage",
                key="market_specific_sites"
            )

        # Fetch market news button
        if st.button("Fetch Market News", key="fetch_market_news_btn"):
            with st.spinner("Fetching market news..."):
                # Get market news
                articles = news_service.get_market_news(
                    category=news_category,
                    page_size=news_count,
                    market=market_focus.lower(),
                    country="eg" if market_focus.lower() == "egypt" else "us",
                    specific_sites=specific_sites
                )

                if articles:
                    st.success(f"Found {len(articles)} market news articles")

                    # Store articles in session state
                    st.session_state.market_news = articles

                    # Display articles
                    for i, article in enumerate(articles):
                        with st.expander(f"{i+1}. {article['title']}"):
                            st.write(f"**Source:** {article['source']['name']}")
                            st.write(f"**Published:** {article['publishedAt']}")
                            st.write(f"**Author:** {article.get('author', 'Unknown')}")
                            st.write("---")
                            st.write(article['description'])
                            st.write("---")
                            st.write(article['content'] if article.get('content') else "Full content not available")
                            st.write("---")
                            st.write(f"[Read full article]({article['url']})")
                else:
                    st.warning(f"No market news found for category: {news_category}")

        # Insights generation section
        st.subheader("Generate Market Insights")

        # Stocks to include
        selected_stocks = st.multiselect(
            "Select Stocks to Include",
            symbols,
            default=symbols[:3] if len(symbols) >= 3 else symbols,
            key="insight_stocks"
        )

        # Time period
        time_period = st.selectbox(
            "Time Period",
            ["1 Week", "1 Month", "3 Months", "6 Months", "1 Year"],
            index=1,
            key="insight_period"
        )

        # Additional context
        additional_context = st.text_area(
            "Additional Context (Optional)",
            placeholder="Add any specific questions or context for the analysis",
            height=100,
            key="insight_context"
        )

        # Generate insights button
        if st.button("Generate Market Insights", key="generate_insights_btn"):
            if not selected_stocks:
                st.warning("Please select at least one stock to include in the analysis.")
                return

            # Check if model is loaded
            if not llm_service.is_loaded:
                with st.spinner("Loading model..."):
                    llm_service.load_model()

            # Prepare stock data
            stock_data = {}
            for symbol in selected_stocks:
                data = load_stock_data(symbol)
                if data is not None:
                    # Calculate some basic metrics
                    current_price = data["Close"].iloc[-1]
                    prev_price = data["Close"].iloc[-2] if len(data) > 1 else current_price
                    change_pct = ((current_price - prev_price) / prev_price) * 100

                    # Add to stock data
                    stock_data[symbol] = {
                        "current_price": current_price,
                        "change_percent": change_pct,
                        "volume": data["Volume"].iloc[-1] if "Volume" in data.columns else 0,
                        "high": data["High"].max() if "High" in data.columns else 0,
                        "low": data["Low"].min() if "Low" in data.columns else 0
                    }

            # Generate insights
            with st.spinner("Generating market insights..."):
                insights = run_async(create_market_insights(
                    stocks=stock_data,
                    insight_type=insight_type,
                    time_period=time_period,
                    additional_context=additional_context,
                    market_news=st.session_state.get("market_news", [])
                ))

                # Display insights
                st.subheader("Market Insights")
                st.write(insights)

                # Add download button for the insights
                st.download_button(
                    label="Download Insights",
                    data=insights,
                    file_name=f"market_insights_{insight_type.replace(' ', '_')}.txt",
                    mime="text/plain"
                )
