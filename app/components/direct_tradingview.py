"""
Direct TradingView chart integration for the AI Stocks Bot app.
This module uses the TradingView JavaScript API directly for maximum compatibility.
"""
import streamlit as st
import logging
import uuid
from typing import Optional

# Configure logging
logger = logging.getLogger(__name__)

def direct_tradingview_chart(symbol: str, width: int = 1000, height: int = 600, theme: str = "dark"):
    """
    Embed a TradingView chart using the direct JavaScript API for maximum compatibility

    Args:
        symbol (str): Stock symbol (e.g., "COMI" for Commercial International Bank)
        width (int): Width of the chart in pixels
        height (int): Height of the chart in pixels
        theme (str): Chart theme ("light" or "dark")
    """
    # Generate a unique container ID to avoid conflicts
    container_id = f"tradingview_chart_{uuid.uuid4().hex[:8]}"

    # Clean up the symbol
    clean_symbol = symbol.replace(" ", "")
    if ":" in clean_symbol:
        # Extract just the symbol part if it has a prefix
        clean_symbol = clean_symbol.split(":")[1]

    # Format for TradingView - use the EGX:COMI format for Egyptian stocks
    formatted_symbol = f"EGX:{clean_symbol}"

    # Debug info
    print(f"Direct TV Chart: Original symbol: {symbol}, Formatted for TradingView: {formatted_symbol}")

    # Create the TradingView widget HTML using the mini chart widget
    # This is the most reliable widget for EGX stocks
    html_content = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container">
        <div class="tradingview-widget-container__widget"></div>
        <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js" async>
        {{
            "symbol": "{clean_symbol}",
            "width": {width},
            "height": {height},
            "locale": "en",
            "dateRange": "12M",
            "colorTheme": "{theme}",
            "trendLineColor": "rgba(41, 98, 255, 1)",
            "underLineColor": "rgba(41, 98, 255, 0.3)",
            "underLineBottomColor": "rgba(41, 98, 255, 0)",
            "isTransparent": false,
            "autosize": false,
            "largeChartUrl": ""
        }}
        </script>
    </div>
    <!-- TradingView Widget END -->
    """

    # Display the TradingView widget
    st.components.v1.html(html_content, width=width, height=height)

    # Add a note about troubleshooting
    st.caption("If the chart appears black, try switching to a different tab and back, or refresh the page.")

    return formatted_symbol
