"""
Multi-model prediction display component for TradingView predictions.
This module provides functions to display predictions from multiple models.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timed<PERSON><PERSON>

def display_multi_model_predictions(historical_data, live_data, all_predictions, symbol, show_confidence=True, performance_metrics=None):
    """
    Display predictions from multiple models for comparison

    Args:
        historical_data: Historical price data
        live_data: Live price data
        all_predictions: Dictionary of predictions for each model
        symbol: Stock symbol
        show_confidence: Whether to show confidence intervals
        performance_metrics: Dictionary of performance metrics for each model
    """
    if not all_predictions:
        st.warning("No predictions were generated.")
        return

    # Get the current price
    current_price = historical_data['Close'].iloc[-1] if historical_data is not None else None
    if live_data is not None and not live_data.empty:
        current_price = live_data['Close'].iloc[-1]

    # Create tabs for different views
    tab1, tab2 = st.tabs(["Comparison Table", "Model Charts"])

    with tab1:
        # Create a comparison table
        st.subheader("Model Comparison")

        # Create a DataFrame to hold all predictions
        comparison_data = []

        # Get all horizons from all models
        all_horizons = set()
        for model_name, model_predictions in all_predictions.items():
            if isinstance(model_predictions, dict):
                for horizon in model_predictions.keys():
                    all_horizons.add(horizon)

        # Sort horizons
        all_horizons = sorted(list(all_horizons))

        # Build comparison data
        for horizon in all_horizons:
            row_data = {"Horizon": f"{horizon} min"}

            for model_name, model_predictions in all_predictions.items():
                # Ensure model_predictions is a dictionary
                if not isinstance(model_predictions, dict):
                    continue

                # Safely check if horizon exists in predictions
                if isinstance(horizon, (int, float)) and horizon in model_predictions:
                    pred_value = model_predictions[horizon]
                    if isinstance(pred_value, dict):
                        # Handle case where prediction includes confidence intervals
                        pred_value = pred_value.get('prediction', 0)

                    # Ensure pred_value is a number
                    if isinstance(pred_value, (int, float)) and not np.isnan(pred_value):
                        # Calculate percent change
                        if current_price and current_price > 0:
                            pct_change = (pred_value - current_price) / current_price * 100
                            row_data[f"{model_name}"] = f"{pred_value:.2f} ({pct_change:+.2f}%)"
                        else:
                            row_data[f"{model_name}"] = f"{pred_value:.2f}"

            comparison_data.append(row_data)

        # Create DataFrame and display
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            st.dataframe(comparison_df, use_container_width=True)
        else:
            st.warning("No prediction data available for comparison.")

        # Display performance metrics if available
        if performance_metrics:
            st.subheader("Model Performance Metrics")

            # Create performance metrics table
            metrics_data = []

            for model_name, metrics in performance_metrics.items():
                row_data = {"Model": model_name}

                # Add metrics
                if "rmse" in metrics:
                    row_data["RMSE"] = f"{metrics['rmse']:.4f}"
                if "mae" in metrics:
                    row_data["MAE"] = f"{metrics['mae']:.4f}"
                if "direction_accuracy" in metrics:
                    row_data["Direction Accuracy"] = f"{metrics['direction_accuracy']:.2%}"

                metrics_data.append(row_data)

            # Create DataFrame and display
            if metrics_data:
                metrics_df = pd.DataFrame(metrics_data)
                st.dataframe(metrics_df, use_container_width=True)

    with tab2:
        # Create a combined chart for all models
        st.subheader("Prediction Comparison Chart")

        # Create a figure
        fig = go.Figure()

        # Add historical data
        if historical_data is not None and not historical_data.empty:
            # Get the last 30 days of data for display
            display_data = historical_data.tail(30).copy()

            # Add historical price line
            fig.add_trace(go.Scatter(
                x=display_data.index,
                y=display_data['Close'],
                mode='lines',
                name='Historical Price',
                line=dict(color='gray', width=2)
            ))

        # Get current time and price
        current_time = datetime.now()

        # Add predictions for each model with different colors
        colors = px.colors.qualitative.Plotly  # Get a color palette

        for i, (model_name, model_predictions) in enumerate(all_predictions.items()):
            color = colors[i % len(colors)]  # Cycle through colors

            # Ensure model_predictions is a dictionary
            if not isinstance(model_predictions, dict):
                continue

            for horizon, pred_value in model_predictions.items():
                # Ensure horizon is a number
                if not isinstance(horizon, (int, float)):
                    continue

                # Handle case where prediction includes confidence intervals
                if isinstance(pred_value, dict):
                    pred_value = pred_value.get('prediction', 0)

                # Ensure pred_value is a valid number
                if not isinstance(pred_value, (int, float)) or np.isnan(pred_value):
                    continue

                # Calculate prediction time
                pred_time = current_time + timedelta(minutes=horizon)

                # Format horizon label
                if horizon < 60:
                    horizon_label = f'{horizon} min'
                else:
                    hours = horizon / 60
                    horizon_label = f'{hours:.1f} hr' if hours % 1 > 0 else f'{int(hours)} hr'

                # Add prediction line
                fig.add_trace(go.Scatter(
                    x=[current_time, pred_time],
                    y=[current_price, pred_value],
                    mode='lines+markers',
                    name=f'{model_name} - {horizon_label}',
                    line=dict(color=color, dash='dot', width=2),
                    marker=dict(size=8)
                ))

        # Update layout
        fig.update_layout(
            title=f'Prediction Comparison for {symbol}',
            xaxis_title='Time',
            yaxis_title='Price',
            legend_title='Models',
            height=500,
            hovermode='x unified'
        )

        # Display the figure
        st.plotly_chart(fig, use_container_width=True)
