"""
Enhanced TradingView chart integration with advanced features for the AI Stocks Bot app.

This module provides advanced TradingView chart components with support for:
- Custom drawing tools
- Advanced technical indicators
- Chart templates and saving functionality
"""
import streamlit as st
import logging
import uuid
import json
import os
import pandas as pd
import plotly.graph_objects as go
from typing import List, Dict, Optional, Any, Union
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Constants
CHART_TEMPLATES_DIR = "chart_templates"
DEFAULT_INDICATORS = [
    "MASimple@tv-basicstudies",
    "MAExp@tv-basicstudies",
    "RSI@tv-basicstudies",
    "MACD@tv-basicstudies",
    "BB@tv-basicstudies",
    "Volume@tv-basicstudies"
]

# Ensure templates directory exists
if not os.path.exists(CHART_TEMPLATES_DIR):
    os.makedirs(CHART_TEMPLATES_DIR)

def enhanced_tradingview_chart(
    symbol: str,
    width: int = 1200,
    height: int = 700,
    theme: str = "dark",
    interval: str = "D",
    style: str = "2",  # Default to candlestick
    studies: Optional[List[str]] = None,
    enable_drawing_tools: bool = True,
    enable_custom_indicators: bool = True,
    enable_templates: bool = True,
    template_name: Optional[str] = None,
    data: Optional[pd.DataFrame] = None
):
    """
    Enhanced TradingView chart with advanced features

    Args:
        symbol (str): Stock symbol (e.g., "EGX:COMI")
        width (int): Width of the chart in pixels
        height (int): Height of the chart in pixels
        theme (str): Chart theme ("light" or "dark")
        interval (str): Chart interval (e.g., "D" for daily, "W" for weekly)
        style (str): Chart style ("1" for bars, "2" for candles, "3" for line, "4" for area)
        studies (List[str]): List of studies to add to the chart
        enable_drawing_tools (bool): Enable advanced drawing tools
        enable_custom_indicators (bool): Enable custom indicators
        enable_templates (bool): Enable chart templates
        template_name (str): Name of template to load (if enable_templates is True)
    """
    # Format symbol for TradingView Enhanced Chart
    # For enhanced charts, we need to try different exchange prefixes

    # First, clean up the symbol
    clean_symbol = symbol.replace(" ", "")
    if ":" in clean_symbol:
        # Extract just the symbol part if it has a prefix
        clean_symbol = clean_symbol.split(":")[1]

    # Try using the symbol directly without any prefix
    # This works for the mini chart, so it might work for other charts too
    formatted_symbol = clean_symbol

    # Debug info
    print(f"Enhanced TV: Original symbol: {symbol}, Formatted for TradingView: {formatted_symbol}")

    # Generate a unique container ID
    container_id = f"tradingview_chart_{uuid.uuid4().hex[:8]}"

    # Load template if specified
    config = {}
    if enable_templates and template_name:
        config = load_chart_template(template_name)

        # Apply template settings if available
        if config:
            if "interval" in config:
                interval = config.get("interval", interval)
            if "style" in config:
                style = config.get("style", style)
            if "studies" in config:
                studies = config.get("studies", studies)

    # Use default studies if none provided
    if not studies:
        studies = DEFAULT_INDICATORS

    # Create UI for template management if enabled
    if enable_templates:
        template_col1, template_col2, template_col3 = st.columns([2, 2, 1])

        with template_col1:
            # Template selection
            templates = get_available_templates()
            selected_template = st.selectbox(
                "Chart Template",
                options=["Default"] + templates,
                index=0,
                key=f"template_select_{container_id}"
            )

            if selected_template != "Default" and selected_template != template_name:
                # Reload with the selected template
                return enhanced_tradingview_chart(
                    symbol=symbol,
                    width=width,
                    height=height,
                    theme=theme,
                    enable_drawing_tools=enable_drawing_tools,
                    enable_custom_indicators=enable_custom_indicators,
                    enable_templates=enable_templates,
                    template_name=selected_template
                )

        with template_col2:
            # Template saving
            new_template_name = st.text_input(
                "Save as New Template",
                key=f"template_name_{container_id}"
            )

        with template_col3:
            # Save button
            if st.button("Save Template", key=f"save_template_{container_id}"):
                if new_template_name:
                    # Create template config
                    template_config = {
                        "interval": interval,
                        "style": style,
                        "studies": studies,
                        "symbol": formatted_symbol,
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    # Save template
                    save_chart_template(new_template_name, template_config)
                    st.success(f"Template '{new_template_name}' saved successfully!")
                else:
                    st.warning("Please enter a template name")

    # Create UI for custom indicators if enabled
    if enable_custom_indicators:
        st.subheader("Custom Indicators")

        # Predefined indicator groups
        indicator_groups = {
            "Trend": ["MASimple@tv-basicstudies", "MAExp@tv-basicstudies", "VWAP@tv-basicstudies", "ParabolicSAR@tv-basicstudies"],
            "Momentum": ["RSI@tv-basicstudies", "MACD@tv-basicstudies", "Stochastic@tv-basicstudies", "CCI@tv-basicstudies"],
            "Volatility": ["BB@tv-basicstudies", "ATR@tv-basicstudies", "StochasticRSI@tv-basicstudies"],
            "Volume": ["Volume@tv-basicstudies", "VWMA@tv-basicstudies", "OBV@tv-basicstudies"],
            "Advanced": ["IchimokuCloud@tv-basicstudies", "ZigZag@tv-basicstudies", "PivotPointsStandard@tv-basicstudies"]
        }

        # Use columns instead of tabs to avoid nesting issues
        col1, col2, col3 = st.columns(3)

        # Track selected indicators
        selected_indicators = []

        # Distribute indicator groups across columns
        columns = [col1, col2, col3]
        categories = list(indicator_groups.keys())

        for i, category in enumerate(categories):
            col_index = i % len(columns)
            with columns[col_index]:
                st.write(f"**{category}**")
                for indicator in indicator_groups[category]:
                    # Extract readable name from indicator ID
                    readable_name = indicator.split('@')[0]

                    # Check if this indicator is in the studies list
                    is_selected = indicator in studies

                    # Create checkbox for each indicator
                    if st.checkbox(readable_name, value=is_selected, key=f"{indicator}_{container_id}_{col_index}"):
                        selected_indicators.append(indicator)

        # Update studies list with selected indicators
        if selected_indicators:
            studies = selected_indicators

    # Map style numbers to names for the external widget
    style_map = {
        "1": "BARS",
        "2": "CANDLES",
        "3": "LINE",
        "4": "AREA"
    }
    chart_style = style_map.get(style, "CANDLES")

    # Convert studies to a format suitable for the external widget
    studies_json = "[]"
    if studies and len(studies) > 0:
        studies_json = str(studies).replace("'", '"')

    # Create the TradingView widget HTML using the external embedding approach
    # This is more reliable for EGX stocks
    html_content = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container" style="height:{height}px;width:{width}px">
      <div class="tradingview-widget-container__widget" style="height:100%;width:100%"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js" async>
      {{
        "width": {width},
        "height": {height},
        "symbol": "EGX:{formatted_symbol}",
        "interval": "{interval}",
        "timezone": "exchange",
        "theme": "{theme}",
        "style": "{style}",
        "locale": "en",
        "enable_publishing": false,
        "withdateranges": true,
        "hide_side_toolbar": false,
        "allow_symbol_change": true,
        "studies": {studies_json},
        "backgroundColor": "rgba(0, 0, 0, 1)",
        "gridColor": "rgba(42, 46, 57, 0)",
        "hide_top_toolbar": false,
        "hide_legend": false,
        "save_image": false
      }}
      </script>
    </div>
    <!-- TradingView Widget END -->
    """

    # Display the TradingView widget with a fallback
    try:
        st.components.v1.html(html_content, width=width, height=height)

        # Add a small note about troubleshooting
        st.caption("If the chart appears black, try switching to a different tab and back, or refresh the page.")
    except Exception as e:
        st.error(f"Error displaying TradingView chart: {str(e)}")

        # Fallback to a basic plotly chart if we have data
        if isinstance(data, pd.DataFrame) and not data.empty and 'Date' in data.columns and 'Close' in data.columns:
            st.warning("Using fallback chart. For full features, please refresh the page.")

            import plotly.graph_objects as go
            fig = go.Figure()

            # Add candlestick chart if we have OHLC data
            if all(col in data.columns for col in ['Open', 'High', 'Low', 'Close']):
                fig.add_trace(go.Candlestick(
                    x=data['Date'],
                    open=data['Open'],
                    high=data['High'],
                    low=data['Low'],
                    close=data['Close'],
                    name="Price"
                ))
            else:
                # Otherwise just show close price
                fig.add_trace(go.Scatter(
                    x=data['Date'],
                    y=data['Close'],
                    mode='lines',
                    name=symbol
                ))

            fig.update_layout(
                title=f"{symbol} Price Chart",
                xaxis_title="Date",
                yaxis_title="Price",
                height=height,
                width=width
            )

            st.plotly_chart(fig, use_container_width=True)

    return formatted_symbol

def get_available_templates() -> List[str]:
    """Get list of available chart templates"""
    if not os.path.exists(CHART_TEMPLATES_DIR):
        return []

    templates = []
    for filename in os.listdir(CHART_TEMPLATES_DIR):
        if filename.endswith('.json'):
            templates.append(filename[:-5])  # Remove .json extension

    return templates

def save_chart_template(name: str, config: Dict[str, Any]) -> bool:
    """Save a chart template configuration"""
    try:
        filepath = os.path.join(CHART_TEMPLATES_DIR, f"{name}.json")
        with open(filepath, 'w') as f:
            json.dump(config, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving chart template: {str(e)}")
        return False

def load_chart_template(name: str) -> Dict[str, Any]:
    """Load a chart template configuration"""
    try:
        filepath = os.path.join(CHART_TEMPLATES_DIR, f"{name}.json")
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error loading chart template: {str(e)}")

    return {}
