import streamlit as st
import pandas as pd
import os
import sys
import logging

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app.utils.data_processing import load_csv_data
from app.utils.feature_engineering import prepare_features

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def upload_csv_component():
    """
    Streamlit component for uploading CSV files

    Returns:
        tuple: (df, symbol) where df is the DataFrame and symbol is the stock symbol
    """
    st.subheader("Upload Historical Data")

    uploaded_file = st.file_uploader("Upload CSV file with historical stock data", type=["csv"])

    if uploaded_file is not None:
        try:
            # Read CSV file
            df = pd.read_csv(uploaded_file)

            # Check if required columns exist
            required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                st.error(f"Missing required columns: {', '.join(missing_columns)}")
                return None, None

            # Convert Date column to datetime
            df['Date'] = pd.to_datetime(df['Date'])

            # Sort by date
            df = df.sort_values('Date')

            # Get stock symbol from filename or ask user
            symbol = os.path.splitext(uploaded_file.name)[0]
            symbol_input = st.text_input("Stock Symbol", value=symbol)

            # Display raw data
            st.subheader("Raw Data")
            st.dataframe(df.head())

            # Display data info
            st.subheader("Data Information")
            st.write(f"Total rows: {len(df)}")
            st.write(f"Date range: {df['Date'].min()} to {df['Date'].max()}")

            # Add feature engineering
            if st.checkbox("Apply Feature Engineering"):
                df_features = prepare_features(df)
                st.subheader("Data with Technical Indicators")
                st.dataframe(df_features.head())

                # Return the feature-engineered DataFrame
                return df_features, symbol_input

            # Return the raw DataFrame
            return df, symbol_input

        except Exception as e:
            st.error(f"Error processing CSV file: {str(e)}")
            logger.error(f"Error processing CSV file: {str(e)}")
            return None, None

    return None, None

def save_uploaded_data(df, symbol, data_dir='data'):
    """
    Save uploaded data to disk

    Args:
        df (pd.DataFrame): DataFrame to save
        symbol (str): Stock symbol
        data_dir (str): Directory to save the data

    Returns:
        str: Path to the saved file
    """
    if df is None or symbol is None:
        return None

    try:
        # Create directory if it doesn't exist
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # Save DataFrame to CSV
        file_path = os.path.join(data_dir, f"{symbol}.csv")
        df.to_csv(file_path, index=False)

        return file_path

    except Exception as e:
        logger.error(f"Error saving data: {str(e)}")
        return None
