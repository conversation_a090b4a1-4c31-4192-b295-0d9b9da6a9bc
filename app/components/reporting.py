"""
Reporting component for the AI Stocks Bot app
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import os
import logging
import json
from typing import Dict, List, Optional, Union, Tuple, Any
import matplotlib.pyplot as plt
import io
import base64
import tempfile
import warnings

# Try to import FPDF, but make it optional
FPDF_AVAILABLE = False
try:
    from fpdf import FPDF
    # Test if FPDF is working correctly
    test_pdf = FPDF()
    test_pdf.add_page()
    test_pdf.set_font('Arial', 'B', 16)
    test_pdf.cell(40, 10, 'Test')
    FPDF_AVAILABLE = True
    del test_pdf  # Clean up
except (ImportError, Exception) as e:
    warnings.warn(f"FPDF not available or not working correctly: {str(e)}. PDF report generation will not be functional.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_pdf_report(symbol: str, data: pd.DataFrame, predictions: Dict = None, strategy_results: Dict = None) -> str:
    """
    Create a PDF report for a stock

    Args:
        symbol (str): Stock symbol
        data (pd.DataFrame): Historical price data
        predictions (Dict, optional): Prediction results
        strategy_results (Dict, optional): Trading strategy results

    Returns:
        str: Path to the generated PDF file
    """
    # Check if FPDF is available
    if not FPDF_AVAILABLE:
        st.error("FPDF library is not available. Please install it with 'pip install fpdf' to generate PDF reports.")
        return ""

    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
            temp_path = tmp.name

        # Create PDF with error handling
        try:
            pdf = FPDF()
            pdf.add_page()
        except Exception as e:
            logger.error(f"Error initializing FPDF: {str(e)}")
            st.error(f"Error initializing PDF generator: {str(e)}")
            return ""

        # Add title
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, f'Stock Analysis Report: {symbol}', 0, 1, 'C')
        pdf.ln(10)

        # Add date
        pdf.set_font('Arial', '', 10)
        pdf.cell(0, 10, f'Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 1, 'R')
        pdf.ln(5)

        # Add price chart
        if not data.empty:
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Price History', 0, 1, 'L')

            # Create price chart
            plt.figure(figsize=(10, 6))
            plt.plot(data['Date'], data['Close'])
            plt.title(f'{symbol} Price History')
            plt.xlabel('Date')
            plt.ylabel('Price')
            plt.grid(True)

            # Save chart to a temporary buffer
            buf = io.BytesIO()
            plt.savefig(buf, format='png')
            buf.seek(0)

            # Save the buffer to a temporary file first
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as img_tmp:
                img_tmp_path = img_tmp.name
                img_tmp.write(buf.getvalue())

            # Add chart to PDF using the temporary file
            pdf.image(img_tmp_path, x=10, y=None, w=190)

            # Clean up
            plt.close()
            os.unlink(img_tmp_path)  # Delete the temporary file

            pdf.ln(5)

            # Add price statistics
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Price Statistics', 0, 1, 'L')

            pdf.set_font('Arial', '', 10)

            # Calculate statistics
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2]
            price_change = current_price - prev_price
            price_change_pct = (price_change / prev_price) * 100

            high_52w = data['High'].tail(252).max()
            low_52w = data['Low'].tail(252).min()

            avg_volume = data['Volume'].mean()

            # Add statistics to PDF
            pdf.cell(60, 10, f'Current Price: ${current_price:.2f}', 0, 0)
            pdf.cell(60, 10, f'Change: ${price_change:.2f} ({price_change_pct:.2f}%)', 0, 1)

            pdf.cell(60, 10, f'52-Week High: ${high_52w:.2f}', 0, 0)
            pdf.cell(60, 10, f'52-Week Low: ${low_52w:.2f}', 0, 1)

            pdf.cell(60, 10, f'Average Volume: {avg_volume:,.0f}', 0, 1)

            pdf.ln(5)

        # Add predictions if available
        if predictions is not None and predictions:
            pdf.add_page()
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Price Predictions', 0, 1, 'L')

            pdf.set_font('Arial', '', 10)

            # Create a table for predictions
            col_width = 40
            row_height = 10

            # Table header
            pdf.cell(col_width, row_height, 'Horizon', 1, 0, 'C')
            pdf.cell(col_width, row_height, 'Predicted Price', 1, 0, 'C')
            pdf.cell(col_width, row_height, 'Change (%)', 1, 1, 'C')

            # Table rows
            current_price = data['Close'].iloc[-1] if not data.empty else 0

            for horizon, price in predictions.items():
                change_pct = ((price / current_price) - 1) * 100 if current_price > 0 else 0

                pdf.cell(col_width, row_height, str(horizon), 1, 0, 'C')
                pdf.cell(col_width, row_height, f'${price:.2f}', 1, 0, 'C')
                pdf.cell(col_width, row_height, f'{change_pct:.2f}%', 1, 1, 'C')

            pdf.ln(5)

            # Create prediction chart
            if not data.empty:
                plt.figure(figsize=(10, 6))

                # Plot historical prices
                plt.plot(data['Date'].tail(30), data['Close'].tail(30), label='Historical')

                # Plot predictions
                current_date = data['Date'].iloc[-1]
                for horizon, price in predictions.items():
                    pred_date = current_date + pd.Timedelta(days=horizon)
                    plt.scatter([pred_date], [price], color='red', s=50)

                plt.title(f'{symbol} Price Predictions')
                plt.xlabel('Date')
                plt.ylabel('Price')
                plt.grid(True)
                plt.legend()

                # Save chart to a temporary buffer
                buf = io.BytesIO()
                plt.savefig(buf, format='png')
                buf.seek(0)

                # Save the buffer to a temporary file first
                with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as img_tmp:
                    img_tmp_path = img_tmp.name
                    img_tmp.write(buf.getvalue())

                # Add chart to PDF using the temporary file
                pdf.image(img_tmp_path, x=10, y=None, w=190)

                # Clean up
                plt.close()
                os.unlink(img_tmp_path)  # Delete the temporary file

        # Add strategy results if available
        if strategy_results is not None and strategy_results:
            pdf.add_page()
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Trading Strategy Results', 0, 1, 'L')

            pdf.set_font('Arial', '', 10)

            # Add strategy metrics
            if 'metrics' in strategy_results:
                metrics = strategy_results['metrics']

                pdf.cell(0, 10, f"Strategy: {strategy_results.get('strategy_name', 'Unknown')}", 0, 1)
                pdf.ln(5)

                # Create a table for metrics
                col_width = 60
                row_height = 10

                # Table header
                pdf.cell(col_width, row_height, 'Metric', 1, 0, 'C')
                pdf.cell(col_width, row_height, 'Value', 1, 1, 'C')

                # Table rows
                for metric, value in metrics.items():
                    # Format the metric name
                    metric_name = ' '.join(word.capitalize() for word in metric.split('_'))

                    # Format the value
                    if isinstance(value, float):
                        if 'return' in metric or 'rate' in metric or 'drawdown' in metric:
                            formatted_value = f'{value:.2f}%'
                        else:
                            formatted_value = f'{value:.2f}'
                    else:
                        formatted_value = str(value)

                    pdf.cell(col_width, row_height, metric_name, 1, 0)
                    pdf.cell(col_width, row_height, formatted_value, 1, 1)

                pdf.ln(5)

            # Add strategy chart if available
            if 'chart' in strategy_results:
                # In a real implementation, you would save the chart to a buffer and add it to the PDF
                pass

        # Save PDF with error handling
        try:
            pdf.output(temp_path)

            # Verify the PDF was created successfully
            if os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                logger.info(f"PDF report created successfully at {temp_path}")
                return temp_path
            else:
                logger.error("PDF file was not created or is empty")
                return ""
        except Exception as e:
            logger.error(f"Error saving PDF: {str(e)}")
            # If the file was created but is corrupt, delete it
            if os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                except Exception:
                    pass
            return ""
    except Exception as e:
        st.error(f"Error generating PDF report: {str(e)}")
        return ""

def get_download_link(file_path: str, link_text: str) -> str:
    """
    Generate a download link for a file

    Args:
        file_path (str): Path to the file
        link_text (str): Text to display for the link

    Returns:
        str: HTML link
    """
    try:
        if not file_path or not os.path.exists(file_path):
            return "Error: File not found"

        with open(file_path, 'rb') as f:
            data = f.read()

        b64 = base64.b64encode(data).decode()
        href = f'<a href="data:application/pdf;base64,{b64}" download="{os.path.basename(file_path)}">{link_text}</a>'

        return href
    except Exception as e:
        return f"Error generating download link: {str(e)}"

def reporting_component():
    """
    Streamlit component for generating reports
    """
    st.header("Reporting")

    # Check if we have data loaded
    if 'historical_data' not in st.session_state or st.session_state.historical_data is None:
        st.warning("No data loaded. Please load a stock first.")
        return

    # Get data from session state
    historical_data = st.session_state.historical_data
    symbol = st.session_state.symbol

    # Create tabs for different report types
    tab1, tab2, tab3 = st.tabs(["Stock Analysis", "Prediction Report", "Strategy Report"])

    with tab1:
        st.subheader("Stock Analysis Report")

        # Report options
        st.write("Select options for the stock analysis report:")

        include_price_history = st.checkbox("Include Price History", value=True)
        include_volume_analysis = st.checkbox("Include Volume Analysis", value=True)
        include_technical_indicators = st.checkbox("Include Technical Indicators", value=True)

        # Generate report button
        if st.button("Generate Stock Analysis Report"):
            with st.spinner("Generating report..."):
                try:
                    if not FPDF_AVAILABLE:
                        st.error("FPDF library is not available. Please install it with 'pip install fpdf' to generate PDF reports.")
                    else:
                        # Create report
                        report_path = create_pdf_report(symbol, historical_data)

                        if report_path:
                            # Display download link
                            st.success("Report generated successfully!")
                            st.markdown(get_download_link(report_path, "Download Stock Analysis Report"), unsafe_allow_html=True)
                        else:
                            st.error("Failed to generate report.")

                    # Preview report
                    st.subheader("Report Preview")

                    # Display price history
                    if include_price_history:
                        st.write("### Price History")

                        fig = px.line(
                            historical_data,
                            x='Date',
                            y='Close',
                            title=f"{symbol} Price History"
                        )

                        st.plotly_chart(fig, use_container_width=True)

                        # Price statistics
                        st.write("### Price Statistics")

                        col1, col2, col3 = st.columns(3)

                        with col1:
                            current_price = historical_data['Close'].iloc[-1]
                            prev_price = historical_data['Close'].iloc[-2]
                            price_change = current_price - prev_price
                            price_change_pct = (price_change / prev_price) * 100

                            st.metric("Current Price", f"${current_price:.2f}", f"{price_change_pct:.2f}%")

                        with col2:
                            high_52w = historical_data['High'].tail(252).max()
                            low_52w = historical_data['Low'].tail(252).min()

                            st.metric("52-Week High", f"${high_52w:.2f}")
                            st.metric("52-Week Low", f"${low_52w:.2f}")

                        with col3:
                            avg_volume = historical_data['Volume'].mean()

                            st.metric("Average Volume", f"{avg_volume:,.0f}")

                    # Display volume analysis
                    if include_volume_analysis:
                        st.write("### Volume Analysis")

                        fig = go.Figure()

                        # Add volume bars
                        fig.add_trace(go.Bar(
                            x=historical_data['Date'],
                            y=historical_data['Volume'],
                            name="Volume"
                        ))

                        # Add price line on secondary y-axis
                        fig.add_trace(go.Scatter(
                            x=historical_data['Date'],
                            y=historical_data['Close'],
                            name="Close Price",
                            yaxis="y2"
                        ))

                        # Update layout with secondary y-axis
                        fig.update_layout(
                            title=f"{symbol} Volume Analysis",
                            xaxis_title="Date",
                            yaxis_title="Volume",
                            yaxis2=dict(
                                title="Price",
                                overlaying="y",
                                side="right"
                            )
                        )

                        st.plotly_chart(fig, use_container_width=True)

                    # Display technical indicators
                    if include_technical_indicators and 'df_with_indicators' in st.session_state:
                        st.write("### Technical Indicators")

                        df_with_indicators = st.session_state.df_with_indicators

                        # RSI Chart
                        if 'RSI_14' in df_with_indicators.columns:
                            fig = go.Figure()

                            # Add price subplot
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Close'][-90:],
                                name="Price",
                                yaxis="y1"
                            ))

                            # Add RSI subplot
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['RSI_14'][-90:],
                                name="RSI (14)",
                                yaxis="y2"
                            ))

                            # Add overbought/oversold lines
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=[70] * len(df_with_indicators[-90:]),
                                name="Overbought (70)",
                                line=dict(color='red', width=1, dash='dash'),
                                yaxis="y2"
                            ))

                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=[30] * len(df_with_indicators[-90:]),
                                name="Oversold (30)",
                                line=dict(color='green', width=1, dash='dash'),
                                yaxis="y2"
                            ))

                            # Update layout with secondary y-axis
                            fig.update_layout(
                                title="Relative Strength Index (RSI)",
                                xaxis_title="Date",
                                yaxis=dict(
                                    title="Price",
                                    domain=[0.6, 1]
                                ),
                                yaxis2=dict(
                                    title="RSI",
                                    domain=[0, 0.5],
                                    range=[0, 100]
                                ),
                                height=600
                            )

                            st.plotly_chart(fig, use_container_width=True)

                except Exception as e:
                    st.error(f"Error generating report: {str(e)}")

    with tab2:
        st.subheader("Prediction Report")

        # Check if we have predictions
        if 'predictions' not in st.session_state:
            st.info("No predictions available. You can generate predictions below or use the Predictions page.")

            # Add a simple prediction generator
            st.subheader("Quick Prediction Generator")

            # Model type selection
            from app.models.model_factory import ModelFactory
            available_models = ModelFactory.get_available_models()

            model_type = st.selectbox(
                "Select model type",
                options=list(available_models.keys()),
                index=0,
                format_func=lambda x: available_models[x],
                key="report_model_type"
            )

            # Show model description
            st.info(ModelFactory.get_model_description(model_type))

            # Prediction horizons
            st.subheader("Prediction Horizons")

            # Default horizons
            default_horizons = [5, 30, 60]

            # Let user select horizons
            selected_horizons = st.multiselect(
                "Select prediction horizons (minutes)",
                options=[5, 15, 30, 60, 120, 240],
                default=default_horizons,
                key="report_horizons"
            )

            if not selected_horizons:
                st.warning("Please select at least one prediction horizon")
            else:
                # Generate predictions button
                if st.button("Generate Predictions", key="report_generate_predictions"):
                    with st.spinner("Generating predictions..."):
                        try:
                            # Import prediction function
                            from models.predict import predict_future_prices

                            # Make predictions using only historical data
                            predictions = predict_future_prices(
                                historical_data, symbol,
                                horizons=selected_horizons,
                                model_type=model_type.lower(),
                                models_path='saved_models'
                            )

                            # Save predictions to session state for reporting
                            st.session_state.predictions = predictions

                            st.success("Predictions generated successfully! Refresh this section to see the report.")

                            # Add a button to refresh the page
                            if st.button("Refresh Report", key="refresh_prediction_report"):
                                st.rerun

                        except Exception as e:
                            st.error(f"Error generating predictions: {str(e)}")
        else:
            predictions = st.session_state.predictions

            # Report options
            st.write("Select options for the prediction report:")

            include_multiple_models = st.checkbox("Include Multiple Models", value=True)
            include_confidence_intervals = st.checkbox("Include Confidence Intervals", value=True)

            # Generate report button
            if st.button("Generate Prediction Report"):
                with st.spinner("Generating report..."):
                    try:
                        if not FPDF_AVAILABLE:
                            st.error("FPDF library is not available. Please install it with 'pip install fpdf' to generate PDF reports.")
                        else:
                            # Create report
                            report_path = create_pdf_report(symbol, historical_data, predictions=predictions)

                            if report_path:
                                # Display download link
                                st.success("Report generated successfully!")
                                st.markdown(get_download_link(report_path, "Download Prediction Report"), unsafe_allow_html=True)
                            else:
                                st.error("Failed to generate report.")

                        # Preview report
                        st.subheader("Report Preview")

                        # Display predictions
                        st.write("### Price Predictions")

                        # Create a table of predictions
                        pred_data = []
                        current_time = datetime.now()

                        for horizon, price in predictions.items():
                            pred_time = current_time + timedelta(days=horizon)

                            pred_data.append({
                                'Horizon': f'{horizon} days',
                                'Predicted Time': pred_time.strftime('%Y-%m-%d'),
                                'Predicted Price': f'${price:.2f}'
                            })

                        pred_df = pd.DataFrame(pred_data)
                        st.dataframe(pred_df)

                        # Create prediction chart
                        fig = go.Figure()

                        # Add historical data
                        fig.add_trace(go.Scatter(
                            x=historical_data['Date'][-30:],
                            y=historical_data['Close'][-30:],
                            mode='lines',
                            name='Historical',
                            line=dict(color='blue')
                        ))

                        # Add predictions
                        current_date = historical_data['Date'].iloc[-1]

                        for horizon, price in predictions.items():
                            pred_date = current_date + pd.Timedelta(days=horizon)

                            fig.add_trace(go.Scatter(
                                x=[pred_date],
                                y=[price],
                                mode='markers',
                                name=f'{horizon} days',
                                marker=dict(color='red', size=10, symbol='star')
                            ))

                        # Update layout
                        fig.update_layout(
                            title=f'{symbol} Price Predictions',
                            xaxis_title='Date',
                            yaxis_title='Price',
                            hovermode='x unified'
                        )

                        st.plotly_chart(fig, use_container_width=True)

                        # Add confidence intervals if selected
                        if include_confidence_intervals:
                            st.write("### Prediction Confidence Intervals")

                            # In a real implementation, you would calculate confidence intervals
                            # Here we'll just simulate them

                            fig = go.Figure()

                            # Add historical data
                            fig.add_trace(go.Scatter(
                                x=historical_data['Date'][-30:],
                                y=historical_data['Close'][-30:],
                                mode='lines',
                                name='Historical',
                                line=dict(color='blue')
                            ))

                            # Add predictions with confidence intervals
                            current_date = historical_data['Date'].iloc[-1]

                            for horizon, price in predictions.items():
                                pred_date = current_date + pd.Timedelta(days=horizon)

                                # Simulate confidence intervals (±5% and ±10%)
                                lower_10 = price * 0.9
                                lower_5 = price * 0.95
                                upper_5 = price * 1.05
                                upper_10 = price * 1.1

                                # Add confidence interval
                                fig.add_trace(go.Scatter(
                                    x=[pred_date, pred_date],
                                    y=[lower_10, upper_10],
                                    mode='lines',
                                    name=f'{horizon} days (90% CI)',
                                    line=dict(color='rgba(255,0,0,0.3)', width=10)
                                ))

                                fig.add_trace(go.Scatter(
                                    x=[pred_date, pred_date],
                                    y=[lower_5, upper_5],
                                    mode='lines',
                                    name=f'{horizon} days (95% CI)',
                                    line=dict(color='rgba(255,0,0,0.5)', width=5)
                                ))

                                # Add prediction point
                                fig.add_trace(go.Scatter(
                                    x=[pred_date],
                                    y=[price],
                                    mode='markers',
                                    name=f'{horizon} days',
                                    marker=dict(color='red', size=10, symbol='star')
                                ))

                            # Update layout
                            fig.update_layout(
                                title=f'{symbol} Price Predictions with Confidence Intervals',
                                xaxis_title='Date',
                                yaxis_title='Price',
                                hovermode='x unified'
                            )

                            st.plotly_chart(fig, use_container_width=True)

                        # Add multiple models if selected
                        if include_multiple_models:
                            st.write("### Multiple Model Predictions")

                            # In a real implementation, you would have predictions from multiple models
                            # Here we'll just simulate them

                            models = ['LSTM', 'RandomForest', 'GradientBoosting', 'Ensemble']
                            model_predictions = {}

                            for model in models:
                                model_predictions[model] = {}

                                for horizon, price in predictions.items():
                                    # Simulate different model predictions (±5%)
                                    model_predictions[model][horizon] = price * np.random.uniform(0.95, 1.05)

                            # Create a table of model predictions
                            model_pred_data = []

                            for horizon in predictions.keys():
                                row = {'Horizon': f'{horizon} days'}

                                for model in models:
                                    row[model] = f'${model_predictions[model][horizon]:.2f}'

                                model_pred_data.append(row)

                            model_pred_df = pd.DataFrame(model_pred_data)
                            st.dataframe(model_pred_df)

                            # Create model comparison chart
                            fig = go.Figure()

                            # Add historical data
                            fig.add_trace(go.Scatter(
                                x=historical_data['Date'][-30:],
                                y=historical_data['Close'][-30:],
                                mode='lines',
                                name='Historical',
                                line=dict(color='blue')
                            ))

                            # Add model predictions
                            colors = ['red', 'green', 'purple', 'orange']

                            for i, model in enumerate(models):
                                model_x = []
                                model_y = []

                                for horizon in sorted(predictions.keys()):
                                    pred_date = current_date + pd.Timedelta(days=horizon)
                                    model_x.append(pred_date)
                                    model_y.append(model_predictions[model][horizon])

                                fig.add_trace(go.Scatter(
                                    x=model_x,
                                    y=model_y,
                                    mode='lines+markers',
                                    name=model,
                                    line=dict(color=colors[i % len(colors)])
                                ))

                            # Update layout
                            fig.update_layout(
                                title=f'{symbol} Multiple Model Predictions',
                                xaxis_title='Date',
                                yaxis_title='Price',
                                hovermode='x unified'
                            )

                            st.plotly_chart(fig, use_container_width=True)

                    except Exception as e:
                        st.error(f"Error generating report: {str(e)}")

    with tab3:
        st.subheader("Strategy Report")

        # Check if we have strategy results
        if 'strategy_results' not in st.session_state:
            st.info("No strategy results available. You can run a simple trading strategy below or use the Trading Strategies tab in the Dashboard.")

            # Add a simple strategy runner
            st.subheader("Quick Strategy Runner")

            # Strategy selection
            from app.utils.trading_strategies import StrategyFactory
            available_strategies = StrategyFactory.get_available_strategies()

            strategy_type = st.selectbox(
                "Select a trading strategy",
                options=list(available_strategies.keys()),
                format_func=lambda x: available_strategies[x],
                key="report_strategy_type"
            )

            # Show strategy description
            st.info(StrategyFactory.get_strategy_description(strategy_type))

            # Strategy parameters
            st.subheader("Strategy Parameters")
            default_params = StrategyFactory.get_strategy_parameters(strategy_type)

            params = {}
            col1, col2 = st.columns(2)

            if strategy_type == 'ma_crossover':
                with col1:
                    params['fast_period'] = st.slider("Fast Period", 1, 50, default_params['fast_period'], key="report_fast_period")
                with col2:
                    params['slow_period'] = st.slider("Slow Period", 10, 200, default_params['slow_period'], key="report_slow_period")

            elif strategy_type == 'rsi':
                with col1:
                    params['period'] = st.slider("RSI Period", 1, 30, default_params['period'], key="report_rsi_period")
                with col2:
                    params['overbought'] = st.slider("Overbought Level", 50, 90, default_params['overbought'], key="report_overbought")
                    params['oversold'] = st.slider("Oversold Level", 10, 50, default_params['oversold'], key="report_oversold")

            elif strategy_type == 'macd':
                with col1:
                    params['fast_period'] = st.slider("Fast Period", 1, 50, default_params['fast_period'], key="report_macd_fast")
                    params['slow_period'] = st.slider("Slow Period", 10, 100, default_params['slow_period'], key="report_macd_slow")
                with col2:
                    params['signal_period'] = st.slider("Signal Period", 1, 50, default_params['signal_period'], key="report_signal_period")

            elif strategy_type == 'bollinger_bands':
                with col1:
                    params['period'] = st.slider("Period", 5, 50, default_params['period'], key="report_bb_period")
                with col2:
                    params['std_dev'] = st.slider("Standard Deviations", 1.0, 3.0, default_params['std_dev'], 0.1, key="report_std_dev")

            # Backtest parameters
            st.subheader("Backtest Parameters")

            col1, col2, col3 = st.columns(3)

            with col1:
                initial_capital = st.number_input("Initial Capital", min_value=1000.0, value=10000.0, step=1000.0, key="report_initial_capital")

            with col2:
                position_size = st.slider("Position Size (%)", 10, 100, 100, 10, key="report_position_size") / 100.0

            with col3:
                commission = st.number_input("Commission per Trade", min_value=0.0, value=0.0, step=1.0, key="report_commission")

            # Run backtest button
            if st.button("Run Strategy Backtest", key="report_run_backtest"):
                with st.spinner("Running backtest..."):
                    try:
                        # Check if we have technical indicators data
                        if 'df_with_indicators' not in st.session_state:
                            # Calculate technical indicators
                            from app.utils.technical_indicators import TechnicalIndicators
                            df_with_indicators = TechnicalIndicators.add_all_indicators(historical_data)
                            st.session_state.df_with_indicators = df_with_indicators
                        else:
                            df_with_indicators = st.session_state.df_with_indicators

                        # Create strategy instance
                        strategy = StrategyFactory.get_strategy(strategy_type, **params)

                        # Run backtest
                        results = strategy.backtest(
                            df_with_indicators,
                            initial_capital=initial_capital,
                            position_size=position_size,
                            commission=commission
                        )

                        # Save strategy results to session state for reporting
                        st.session_state.strategy_results = {
                            'strategy_name': strategy.name,
                            'metrics': results['metrics'],
                            'trades': results['trades'],
                            'chart_data': results['data']
                        }

                        st.success("Strategy backtest completed successfully! Refresh this section to see the report.")

                        # Add a button to refresh the page
                        if st.button("Refresh Report", key="refresh_strategy_report"):
                            st.rerun

                    except Exception as e:
                        st.error(f"Error running backtest: {str(e)}")
        else:
            strategy_results = st.session_state.strategy_results

            # Report options
            st.write("Select options for the strategy report:")

            include_trade_log = st.checkbox("Include Trade Log", value=True)
            include_performance_metrics = st.checkbox("Include Performance Metrics", value=True)

            # Generate report button
            if st.button("Generate Strategy Report"):
                with st.spinner("Generating report..."):
                    try:
                        if not FPDF_AVAILABLE:
                            st.error("FPDF library is not available. Please install it with 'pip install fpdf' to generate PDF reports.")
                        else:
                            # Create report
                            report_path = create_pdf_report(symbol, historical_data, strategy_results=strategy_results)

                            if report_path:
                                # Display download link
                                st.success("Report generated successfully!")
                                st.markdown(get_download_link(report_path, "Download Strategy Report"), unsafe_allow_html=True)
                            else:
                                st.error("Failed to generate report.")

                        # Preview report
                        st.subheader("Report Preview")

                        # Display strategy results
                        st.write(f"### {strategy_results.get('strategy_name', 'Trading Strategy')} Results")

                        # Display performance metrics
                        if include_performance_metrics and 'metrics' in strategy_results:
                            st.write("### Performance Metrics")

                            metrics = strategy_results['metrics']

                            col1, col2 = st.columns(2)

                            with col1:
                                st.metric("Strategy Return", f"{metrics.get('strategy_return', 0):.2f}%",
                                         f"{metrics.get('strategy_return', 0) - metrics.get('market_return', 0):.2f}%")
                                st.metric("Total Trades", f"{metrics.get('total_trades', 0)}")
                                st.metric("Win Rate", f"{metrics.get('win_rate', 0):.2f}%")

                            with col2:
                                st.metric("Buy & Hold Return", f"{metrics.get('market_return', 0):.2f}%")
                                st.metric("Winning Trades", f"{metrics.get('winning_trades', 0)}")
                                st.metric("Max Drawdown", f"{metrics.get('strategy_max_drawdown', 0):.2f}%")

                        # Display trade log
                        if include_trade_log and 'trades' in strategy_results:
                            st.write("### Trade Log")

                            trades_df = strategy_results['trades']

                            if not trades_df.empty:
                                st.dataframe(trades_df)
                            else:
                                st.info("No trades were executed during the backtest period.")

                        # Display strategy chart
                        if 'chart_data' in strategy_results:
                            st.write("### Strategy Performance")

                            chart_data = strategy_results['chart_data']

                            fig = go.Figure()

                            # Add portfolio value
                            fig.add_trace(go.Scatter(
                                x=chart_data['Date'],
                                y=chart_data['Portfolio_Value'],
                                mode='lines',
                                name='Strategy',
                                line=dict(color='green')
                            ))

                            # Add market value
                            fig.add_trace(go.Scatter(
                                x=chart_data['Date'],
                                y=chart_data['Market_Value'],
                                mode='lines',
                                name='Buy & Hold',
                                line=dict(color='blue')
                            ))

                            # Update layout
                            fig.update_layout(
                                title=f'{strategy_results.get("strategy_name", "Trading Strategy")} Performance',
                                xaxis_title='Date',
                                yaxis_title='Value',
                                hovermode='x unified'
                            )

                            st.plotly_chart(fig, use_container_width=True)

                    except Exception as e:
                        st.error(f"Error generating report: {str(e)}")

    # Add export options
    st.sidebar.subheader("Export Options")

    export_format = st.sidebar.selectbox(
        "Export Format",
        options=["PDF", "Excel", "CSV"],
        index=0
    )

    if st.sidebar.button("Export All Reports"):
        with st.spinner("Exporting reports..."):
            try:
                if not FPDF_AVAILABLE:
                    st.sidebar.error("FPDF library is not available. Please install it with 'pip install fpdf' to generate PDF reports.")
                else:
                    # Create reports
                    stock_report_path = create_pdf_report(symbol, historical_data)

                    predictions = st.session_state.get('predictions', {})
                    prediction_report_path = create_pdf_report(symbol, historical_data, predictions=predictions)

                    strategy_results = st.session_state.get('strategy_results', {})
                    strategy_report_path = create_pdf_report(symbol, historical_data, strategy_results=strategy_results)

                    if stock_report_path and prediction_report_path and strategy_report_path:
                        # Display download links
                        st.sidebar.success("Reports exported successfully!")

                        st.sidebar.markdown(get_download_link(stock_report_path, "Download Stock Analysis Report"), unsafe_allow_html=True)
                        st.sidebar.markdown(get_download_link(prediction_report_path, "Download Prediction Report"), unsafe_allow_html=True)
                        st.sidebar.markdown(get_download_link(strategy_report_path, "Download Strategy Report"), unsafe_allow_html=True)
                    else:
                        st.sidebar.error("Failed to generate one or more reports.")

            except Exception as e:
                st.sidebar.error(f"Error exporting reports: {str(e)}")
