"""
SMC Analysis Engine
Professional Smart Money Concepts analysis engine for the AI Stocks Bot
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass

from .smc_indicators import (
    detect_order_blocks, 
    detect_fvg, 
    detect_liquidity_zones,
    detect_market_structure,
    get_egx_parameters,
    get_stock_specific_parameters,
    analyze_order_block_confluence,
    analyze_fvg_confluence,
    analyze_liquidity_confluence
)

logger = logging.getLogger(__name__)

@dataclass 
class SMCSignal:
    """Represents an SMC trading signal"""
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0-1 confidence score
    entry_price: float
    stop_loss: float
    take_profit_1: float
    take_profit_2: float = None
    risk_reward: float = 0.0
    timeframe_alignment: Dict[str, str] = None
    supporting_factors: List[str] = None
    risk_level: str = "MEDIUM"  # LOW, MEDIUM, HIGH

    def __post_init__(self):
        if self.timeframe_alignment is None:
            self.timeframe_alignment = {}
        if self.supporting_factors is None:
            self.supporting_factors = []

class SMCAnalysisEngine:
    """
    Smart Money Concepts Analysis Engine for AI Stocks Bot
    Provides institutional-grade market structure analysis
    """
    
    def __init__(self):
        self.egx_params = get_egx_parameters()
        self.analysis_cache = {}
    
    def analyze_stock(self, df: pd.DataFrame, symbol: str) -> Dict:
        """
        Perform comprehensive SMC analysis on stock data
        
        Args:
            df: OHLCV DataFrame
            symbol: Stock symbol
            
        Returns:
            Dict containing complete SMC analysis
        """
        try:
            # Get stock-specific parameters
            params = get_stock_specific_parameters(symbol)
            
            # Basic price metrics
            current_price = float(df['close'].iloc[-1])
            prev_price = float(df['close'].iloc[-2])
            price_change = current_price - prev_price
            price_change_pct = (price_change / prev_price) * 100
            
            # Market Structure Analysis
            market_structure = detect_market_structure(df)
            
            # SMC Analysis
            order_blocks = detect_order_blocks(
                df, 
                lookback=params.get('order_blocks', {}).get('lookback', 20),
                min_strength=params.get('order_blocks', {}).get('min_strength', 0.15)
            )
            
            fvgs = detect_fvg(
                df,
                min_gap_size=params.get('fvg', {}).get('min_gap_size', 0.002)
            )
            
            liquidity_zones = detect_liquidity_zones(
                df,
                lookback=params.get('liquidity_zones', {}).get('lookback', 30),
                min_strength=0.4
            )
            
            # Calculate confluence
            confluence_factors = self._calculate_confluence_factors(
                order_blocks, fvgs, liquidity_zones, current_price
            )
            
            confluence_strength = min(sum(confluence_factors.values()) / 8, 1.0)
            
            # Support/Resistance levels
            support_levels, resistance_levels = self._extract_key_levels(
                order_blocks, fvgs, current_price
            )
            
            # Enhanced analysis
            premium_discount = self._calculate_premium_discount_zones(df, current_price)
            bos_events = self._detect_break_of_structure(df, market_structure)
            
            # Generate trading signal
            trading_signal = self._generate_trading_signal(
                current_price, market_structure, order_blocks, fvgs, 
                liquidity_zones, confluence_strength
            )
            
            # Generate summary
            analysis_summary = self._generate_summary(
                current_price, price_change_pct, market_structure, 
                order_blocks, fvgs, liquidity_zones, confluence_strength, trading_signal
            )
            
            return {
                'symbol': symbol,
                'current_price': current_price,
                'price_change': price_change,
                'price_change_pct': price_change_pct,
                'market_structure': market_structure,
                'order_blocks': order_blocks,
                'fvgs': fvgs,
                'liquidity_zones': liquidity_zones,
                'confluence_factors': confluence_factors,
                'confluence_strength': confluence_strength,
                'support_levels': support_levels[:5],  # Top 5
                'resistance_levels': resistance_levels[:5],  # Top 5
                'premium_discount_zones': premium_discount,
                'bos_events': bos_events,                'trading_signal': trading_signal,
                'summary': analysis_summary,  # Change from analysis_summary to summary
                'analysis_time': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"SMC analysis failed for {symbol}: {e}")
            return {'error': f'SMC analysis failed: {e}'}
    
    def _calculate_confluence_factors(self, order_blocks, fvgs, liquidity_zones, current_price):
        """Calculate SMC confluence factors"""
        return {
            'order_blocks': len([ob for ob in order_blocks if 
                               abs((ob.high + ob.low)/2 - current_price) / current_price < 0.08]),
            'fvgs': len([fvg for fvg in fvgs if 
                        abs((fvg.high + fvg.low)/2 - current_price) / current_price < 0.05]),
            'liquidity_zones': len([lz for lz in liquidity_zones if 
                                  abs(lz.center - current_price) / current_price < 0.10]),
            'structure_strength': 1 if len(order_blocks) > 0 or len(fvgs) > 0 else 0
        }
    
    def _extract_key_levels(self, order_blocks, fvgs, current_price):
        """Extract support and resistance levels"""
        support_levels = []
        resistance_levels = []
        
        # From order blocks
        for ob in order_blocks:
            if ob.block_type == "bullish" and ob.high < current_price:
                support_levels.append(ob.high)
            elif ob.block_type == "bearish" and ob.low > current_price:
                resistance_levels.append(ob.low)
        
        # From FVGs
        for fvg in fvgs:
            if fvg.gap_type == "bullish" and fvg.high < current_price:
                support_levels.append(fvg.high)
            elif fvg.gap_type == "bearish" and fvg.low > current_price:
                resistance_levels.append(fvg.low)
        
        return sorted(support_levels, reverse=True), sorted(resistance_levels)
    
    def _calculate_premium_discount_zones(self, df, current_price):
        """Calculate premium/discount zones based on recent range"""
        try:
            # Use last 50 periods to establish range
            recent_data = df.tail(50)
            range_high = recent_data['high'].max()
            range_low = recent_data['low'].min()
            range_mid = (range_high + range_low) / 2
            
            # Define zones
            premium_threshold = range_mid + (range_high - range_mid) * 0.5
            discount_threshold = range_mid - (range_mid - range_low) * 0.5
            
            if current_price > premium_threshold:
                current_zone = 'premium'
            elif current_price < discount_threshold:
                current_zone = 'discount'
            else:
                current_zone = 'equilibrium'
            
            return {
                'range_high': range_high,
                'range_low': range_low,
                'range_mid': range_mid,
                'premium_threshold': premium_threshold,
                'discount_threshold': discount_threshold,
                'current_zone': current_zone
            }
        except Exception as e:
            logger.error(f"Error calculating premium/discount zones: {e}")
            return {}
    
    def _detect_break_of_structure(self, df, market_structure):
        """Detect Break of Structure events"""
        bos_events = []
        
        try:
            highs = market_structure.get('highs', pd.Series())
            lows = market_structure.get('lows', pd.Series())
            
            # Simple BOS detection
            if len(highs) > 1:
                recent_high = highs.iloc[-1]
                prev_high = highs.iloc[-2]
                
                if recent_high > prev_high:
                    bos_events.append({
                        'type': 'bullish_bos',
                        'price': recent_high,
                        'previous_level': prev_high,
                        'strength': 0.7,
                        'timestamp': len(df) - 1
                    })
            
            if len(lows) > 1:
                recent_low = lows.iloc[-1] 
                prev_low = lows.iloc[-2]
                
                if recent_low < prev_low:
                    bos_events.append({
                        'type': 'bearish_bos',
                        'price': recent_low,
                        'previous_level': prev_low,
                        'strength': 0.7,
                        'timestamp': len(df) - 1
                    })
                    
        except Exception as e:
            logger.error(f"Error detecting BOS: {e}")
        
        return bos_events
    
    def _generate_trading_signal(self, current_price, market_structure, order_blocks, fvgs, liquidity_zones, confluence):
        """Generate trading signal based on SMC analysis"""
        try:
            # Analyze confluences
            ob_confluence = analyze_order_block_confluence(order_blocks, current_price)
            fvg_confluence = analyze_fvg_confluence(fvgs, current_price)
            lz_confluence = analyze_liquidity_confluence(liquidity_zones, current_price)
            
            # Determine overall direction
            bullish_factors = 0
            bearish_factors = 0
            
            # Market structure
            if market_structure.get('trend') == 'bullish':
                bullish_factors += 1
            elif market_structure.get('trend') == 'bearish':
                bearish_factors += 1
            
            # Order blocks
            if ob_confluence.get('direction') == 'bullish':
                bullish_factors += 1
            elif ob_confluence.get('direction') == 'bearish':
                bearish_factors += 1
            
            # FVGs
            if fvg_confluence.get('direction') == 'bullish':
                bullish_factors += 1
            elif fvg_confluence.get('direction') == 'bearish':
                bearish_factors += 1
            
            # Liquidity zones
            if lz_confluence.get('direction') == 'bullish':
                bullish_factors += 1
            elif lz_confluence.get('direction') == 'bearish':
                bearish_factors += 1
            
            # Determine signal
            if bullish_factors > bearish_factors + 1:
                signal_type = 'BUY'
                confidence = confluence * 0.7 + (bullish_factors / 4) * 0.3
            elif bearish_factors > bullish_factors + 1:
                signal_type = 'SELL'
                confidence = confluence * 0.7 + (bearish_factors / 4) * 0.3
            else:
                signal_type = 'HOLD'
                confidence = 0.5
            
            # Calculate entry, stop loss, and take profit
            if signal_type == 'BUY':
                entry_price = current_price
                stop_loss = current_price * 0.97  # 3% stop loss
                take_profit_1 = current_price * 1.06  # 6% take profit
                take_profit_2 = current_price * 1.12  # 12% extended target
            elif signal_type == 'SELL':
                entry_price = current_price
                stop_loss = current_price * 1.03  # 3% stop loss
                take_profit_1 = current_price * 0.94  # 6% take profit
                take_profit_2 = current_price * 0.88  # 12% extended target
            else:
                entry_price = current_price
                stop_loss = current_price
                take_profit_1 = current_price
                take_profit_2 = current_price
            
            # Calculate risk-reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit_1 - entry_price)
            risk_reward = reward / risk if risk > 0 else 0
            
            # Supporting factors
            supporting_factors = []
            if ob_confluence.get('confluence'):
                supporting_factors.append(f"Order Block Confluence ({ob_confluence.get('total_blocks', 0)} blocks)")
            if fvg_confluence.get('confluence'):
                supporting_factors.append(f"FVG Confluence ({fvg_confluence.get('total_fvgs', 0)} gaps)")
            if lz_confluence.get('confluence'):
                supporting_factors.append(f"Liquidity Zone Confluence ({lz_confluence.get('total_zones', 0)} zones)")
            
            return SMCSignal(
                signal_type=signal_type,
                confidence=min(confidence, 0.95),
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit_1=take_profit_1,
                take_profit_2=take_profit_2,
                risk_reward=risk_reward,
                supporting_factors=supporting_factors,
                risk_level="HIGH" if confidence < 0.6 else "MEDIUM" if confidence < 0.8 else "LOW"
            )
            
        except Exception as e:
            logger.error(f"Error generating trading signal: {e}")
            return SMCSignal(
                signal_type='HOLD',
                confidence=0.5,
                entry_price=current_price,
                stop_loss=current_price,
                take_profit_1=current_price,
                risk_level="HIGH"
            )
    
    def _generate_summary(self, current_price, price_change_pct, market_structure, 
                         order_blocks, fvgs, liquidity_zones, confluence_strength, trading_signal):
        """Generate a comprehensive analysis summary"""
        summary = {}
        
        try:
            # Market sentiment
            trend = market_structure.get('trend', 'neutral')
            if trend == 'bullish':
                summary['market_sentiment'] = f"Bullish trend with {price_change_pct:+.2f}% recent change"
            elif trend == 'bearish':
                summary['market_sentiment'] = f"Bearish trend with {price_change_pct:+.2f}% recent change"
            else:
                summary['market_sentiment'] = f"Neutral/sideways with {price_change_pct:+.2f}% recent change"
            
            # Structure analysis
            total_structures = len(order_blocks) + len(fvgs) + len(liquidity_zones)
            summary['structure_count'] = f"{total_structures} key structures identified"
            summary['order_blocks'] = f"{len(order_blocks)} order blocks detected"
            summary['fair_value_gaps'] = f"{len(fvgs)} FVGs identified"
            summary['liquidity_zones'] = f"{len(liquidity_zones)} liquidity zones found"
            
            # Confluence assessment
            if confluence_strength >= 0.7:
                summary['confluence_assessment'] = f"High confluence ({confluence_strength:.1%}) - Strong setup"
            elif confluence_strength >= 0.4:
                summary['confluence_assessment'] = f"Moderate confluence ({confluence_strength:.1%}) - Decent setup"
            else:
                summary['confluence_assessment'] = f"Low confluence ({confluence_strength:.1%}) - Weak setup"
              # Trading recommendation
            signal_strength = trading_signal.confidence if trading_signal else 0.5
            signal_type = trading_signal.signal_type if trading_signal else 'HOLD'
            
            if signal_type == 'BUY' and signal_strength >= 0.6:
                summary['trading_recommendation'] = f"BUY signal with {signal_strength:.1%} confidence"
            elif signal_type == 'SELL' and signal_strength >= 0.6:
                summary['trading_recommendation'] = f"SELL signal with {signal_strength:.1%} confidence"
            elif signal_strength >= 0.4:
                summary['trading_recommendation'] = f"WATCH - {signal_type} bias with {signal_strength:.1%} confidence"
            else:
                summary['trading_recommendation'] = "NEUTRAL - No clear signal"
            
            # Risk assessment - use both confluence and signal confidence
            if confluence_strength >= 0.6 and signal_strength >= 0.6:
                summary['risk_level'] = "LOW - High probability setup"
            elif confluence_strength >= 0.4 and signal_strength >= 0.4:
                summary['risk_level'] = "MODERATE - Acceptable risk-reward"
            else:
                summary['risk_level'] = "HIGH - Low probability setup"
            
            # Key levels summary
            nearby_obs = [ob for ob in order_blocks if abs((ob.high + ob.low)/2 - current_price) / current_price < 0.05]
            nearby_fvgs = [fvg for fvg in fvgs if abs((fvg.high + fvg.low)/2 - current_price) / current_price < 0.05]
            
            if nearby_obs or nearby_fvgs:
                summary['key_levels'] = f"Price near {len(nearby_obs)} order blocks and {len(nearby_fvgs)} FVGs"
            else:
                summary['key_levels'] = "No major structures near current price"
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            summary['error'] = "Summary generation failed"
        
        return summary
