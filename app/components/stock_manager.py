import streamlit as st
import os
import pandas as pd
import shutil
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def stock_manager_component(data_dir='data/stocks'):
    """
    Streamlit component for managing stock files

    Args:
        data_dir (str): Directory containing stock files
    """
    st.subheader("Stock File Manager")

    # Create directory if it doesn't exist
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    # Get list of stock files
    stock_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

    if not stock_files:
        st.warning("No stock files found.")
        return

    # Display stock files
    st.write(f"Found {len(stock_files)} stock files:")

    # Create a table to display stock information
    stock_info = []

    for file in stock_files:
        file_path = os.path.join(data_dir, file)
        symbol = os.path.splitext(file)[0]

        # Get file size
        size_kb = os.path.getsize(file_path) / 1024

        # Get date range
        try:
            df = pd.read_csv(file_path)
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')

            # Sort by date
            df = df.sort_values('Date')

            min_date = df['Date'].min().strftime('%Y-%m-%d')
            max_date = df['Date'].max().strftime('%Y-%m-%d')

            # Check if the data includes 2025
            has_2025 = (df['Date'].dt.year == 2025).any()
            date_range_str = f"{min_date} to {max_date}"
            if has_2025:
                date_range_str = f"✓ {date_range_str}"  # Add a checkmark for files with 2025 data

            num_rows = len(df)
        except Exception as e:
            logger.error(f"Error reading file {file}: {str(e)}")
            date_range_str = "Error"
            num_rows = 0

        stock_info.append({
            "Symbol": symbol,
            "Filename": file,
            "Size (KB)": f"{size_kb:.1f}",
            "Rows": num_rows,
            "Date Range": date_range_str
        })

    # Display stock information
    st.dataframe(pd.DataFrame(stock_info))

    # Stock management options
    st.markdown("---")
    st.subheader("Manage Stock Files")

    # Select a stock file to manage
    selected_file = st.selectbox("Select a stock file to manage", stock_files)

    if selected_file:
        selected_path = os.path.join(data_dir, selected_file)
        selected_symbol = os.path.splitext(selected_file)[0]

        # Management options
        option = st.radio("Choose an action", ["View", "Rename", "Delete", "Backup"])

        if option == "View":
            try:
                df = pd.read_csv(selected_path)

                # Convert Date column to datetime
                df['Date'] = pd.to_datetime(df['Date'], errors='coerce')

                # Sort by date
                df = df.sort_values('Date')

                st.write(f"Preview of {selected_file}:")
                st.write(f"Date range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")

                # Sort by date in descending order to show the most recent data first
                df_display = df.sort_values('Date', ascending=False).head(10)
                st.dataframe(df_display)

                # Option to view more data
                if st.checkbox("View more data"):
                    num_rows = st.slider("Number of rows to display", min_value=10, max_value=min(100, len(df)), value=20)
                    # Sort by date in descending order to show the most recent data first
                    df_display_more = df.sort_values('Date', ascending=False).head(num_rows)
                    st.dataframe(df_display_more)
            except Exception as e:
                st.error(f"Error reading file: {str(e)}")

        elif option == "Rename":
            new_symbol = st.text_input("New symbol name", value=selected_symbol)

            if new_symbol and new_symbol != selected_symbol:
                if st.button(f"Rename {selected_symbol} to {new_symbol}"):
                    try:
                        new_path = os.path.join(data_dir, f"{new_symbol}.csv")

                        # Check if new file already exists
                        if os.path.exists(new_path):
                            st.error(f"A file with the name {new_symbol}.csv already exists.")
                        else:
                            # Rename file
                            os.rename(selected_path, new_path)
                            st.success(f"Renamed {selected_symbol} to {new_symbol}")

                            # Update session state if the renamed file was the selected stock
                            if 'symbol' in st.session_state and st.session_state.symbol == selected_symbol:
                                st.session_state.symbol = new_symbol
                                st.info(f"Updated selected stock to {new_symbol}")

                            # Refresh the page
                            st.rerun
                    except Exception as e:
                        st.error(f"Error renaming file: {str(e)}")

        elif option == "Delete":
            st.warning(f"Are you sure you want to delete {selected_file}? This action cannot be undone.")

            if st.button(f"Yes, delete {selected_symbol}"):
                try:
                    os.remove(selected_path)
                    st.success(f"Deleted {selected_file}")

                    # Clear session state if the deleted file was the selected stock
                    if 'symbol' in st.session_state and st.session_state.symbol == selected_symbol:
                        st.session_state.historical_data = None
                        st.session_state.symbol = None
                        st.session_state.live_data = None
                        st.info("Cleared selected stock from session")

                    # Refresh the page
                    st.rerun
                except Exception as e:
                    st.error(f"Error deleting file: {str(e)}")

        elif option == "Backup":
            backup_dir = os.path.join(data_dir, "backup")

            # Create backup directory if it doesn't exist
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            if st.button(f"Create backup of {selected_symbol}"):
                try:
                    backup_path = os.path.join(backup_dir, f"{selected_symbol}_backup.csv")
                    shutil.copy2(selected_path, backup_path)
                    st.success(f"Created backup at {backup_path}")
                except Exception as e:
                    st.error(f"Error creating backup: {str(e)}")

    # Option to clear all session data
    st.markdown("---")
    st.subheader("Session Management")

    if st.button("Clear all session data"):
        # Clear session state
        for key in list(st.session_state.keys()):
            del st.session_state[key]

        st.success("Cleared all session data")
        st.info("Refreshing page...")
        st.rerun
