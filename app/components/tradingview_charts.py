"""
Enhanced TradingView chart integration using the professional TradingView Charting Library
"""
import streamlit as st
import logging
import uuid
import json
import os
import pandas as pd
from typing import List, Optional, Dict, Any

# Configure logging
logger = logging.getLogger(__name__)

# TradingView Professional Library Configuration
TRADINGVIEW_LIBRARY_PATH = "static/tradingview/"
TRADINGVIEW_DATAFEED_URL = "http://127.0.0.1:8000/api/tradingview/"

def google_charts_professional_component(symbol: str, width: int = 1000, height: int = 600,
                                        theme: str = "dark", interval: str = "1D",
                                        container_id: str = None,
                                        studies: List[str] = None):
    """
    Professional stock chart using Google Charts with candlestick visualization

    This provides a clean, fast, and reliable charting experience with:
    - Interactive candlestick charts
    - Zoom and pan functionality
    - Professional styling
    - Real stock data visualization
    - No server dependencies
    - Fast loading

    Args:
        symbol (str): Stock symbol (e.g., "COMI" for Commercial International Bank)
        width (int): Chart width in pixels
        height (int): Chart height in pixels
        theme (str): Chart theme ("dark" or "light")
        interval (str): Chart interval (currently supports daily data)
        container_id (str): HTML container ID (auto-generated if None)
        studies (List[str]): Technical studies to add (future enhancement)
    """

    # Generate unique container ID if not provided
    if container_id is None:
        container_id = f"google_chart_{uuid.uuid4().hex[:8]}"

    # Check if symbol is provided
    if not symbol:
        st.error("❌ No stock symbol provided")
        st.info("💡 Please select a stock first from the Stock Management page")
        return None

    # Clean and format symbol
    clean_symbol = symbol.replace(" ", "").replace("EGX:", "").replace("EGX-", "")

    try:
        # Load stock data for the chart - try main data directory first, then stocks subfolder
        from app.utils.common import load_stock_data

        # First try the main data directory (data/COMI.csv)
        stock_data = load_stock_data(clean_symbol, base_path='data')

        # If not found, try the stocks subfolder (data/stocks/COMI.csv)
        if stock_data is None or stock_data.empty:
            stock_data = load_stock_data(clean_symbol, base_path='data/stocks')

        if stock_data is None or stock_data.empty:
            st.error(f"❌ No data available for symbol: {clean_symbol}")
            st.info("💡 Please ensure the stock data is available in either:")
            st.info("   • `data/{clean_symbol}.csv` (main data directory)")
            st.info("   • `data/stocks/{clean_symbol}.csv` (stocks subfolder)")
            return None

        # Prepare data for Google Charts (last 100 data points for performance)
        chart_data = stock_data.tail(100).copy()
        chart_data = chart_data.reset_index()

        # Convert data to Google Charts format with validation
        chart_data_json = []
        for _, row in chart_data.iterrows():
            try:
                # Validate that all required columns exist and have valid values
                date_str = row['Date'].strftime('%Y-%m-%d') if hasattr(row['Date'], 'strftime') else str(row['Date'])
                low_val = float(row['Low']) if pd.notna(row['Low']) else 0.0
                open_val = float(row['Open']) if pd.notna(row['Open']) else 0.0
                close_val = float(row['Close']) if pd.notna(row['Close']) else 0.0
                high_val = float(row['High']) if pd.notna(row['High']) else 0.0

                # Ensure OHLC values are valid (High >= Low, etc.)
                if high_val < low_val:
                    high_val, low_val = low_val, high_val
                if open_val < low_val:
                    open_val = low_val
                if open_val > high_val:
                    open_val = high_val
                if close_val < low_val:
                    close_val = low_val
                if close_val > high_val:
                    close_val = high_val

                chart_data_json.append([
                    date_str,
                    low_val,
                    open_val,
                    close_val,
                    high_val
                ])
            except Exception as e:
                logger.warning(f"Skipping invalid data row: {e}")
                continue

        # Ensure we have enough valid data
        if len(chart_data_json) < 5:
            st.error(f"❌ Insufficient valid data points: {len(chart_data_json)}. Need at least 5 data points.")
            st.info("💡 Please check that the stock data contains valid OHLC values.")
            return None

        logger.info(f"Prepared {len(chart_data_json)} valid data points for chart")

        # Theme configuration
        bg_color = '#1e1e1e' if theme == 'dark' else '#ffffff'
        text_color = '#ffffff' if theme == 'dark' else '#333333'
        grid_color = '#333333' if theme == 'dark' else '#e1e1e1'

        # Calculate price statistics
        latest_price = float(chart_data.iloc[-1]['Close'])
        prev_price = float(chart_data.iloc[-2]['Close']) if len(chart_data) > 1 else latest_price
        price_change = latest_price - prev_price
        price_change_pct = (price_change / prev_price * 100) if prev_price != 0 else 0

        # Display metrics before chart
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("📊 Symbol", clean_symbol)
        with col2:
            st.metric("💰 Latest Price", f"{latest_price:.2f} EGP")
        with col3:
            st.metric("📈 Change", f"{price_change:+.2f} EGP", f"{price_change_pct:+.1f}%")
        with col4:
            st.metric("📅 Data Points", len(chart_data))

        # Create the Enhanced Google Charts HTML using Web Components
        google_charts_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Enhanced Professional Chart</title>

            <!-- Google Charts Library -->
            <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

            <style>
                * {{
                    box-sizing: border-box;
                    margin: 0;
                    padding: 0;
                }}

                body {{
                    margin: 0;
                    padding: 15px;
                    background-color: {bg_color};
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                    color: {text_color};
                }}

                .chart-container {{
                    width: 100%;
                    max-width: {width}px;
                    margin: 0 auto;
                    background: {bg_color};
                    border-radius: 12px;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, {'0.3' if theme == 'dark' else '0.1'});
                    overflow: hidden;
                }}

                .chart-header {{
                    background: linear-gradient(135deg, {'#2d3748 0%, #4a5568 100%' if theme == 'dark' else '#f7fafc 0%, #edf2f7 100%'});
                    padding: 20px;
                    text-align: center;
                    border-bottom: 1px solid {grid_color};
                }}

                .chart-title {{
                    font-size: 24px;
                    font-weight: 700;
                    color: {text_color};
                    margin-bottom: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                }}

                .chart-subtitle {{
                    font-size: 14px;
                    color: {'#a0aec0' if theme == 'dark' else '#718096'};
                    font-weight: 500;
                }}

                .chart-controls {{
                    display: flex;
                    justify-content: center;
                    gap: 15px;
                    padding: 15px 20px;
                    background: {'#2d3748' if theme == 'dark' else '#f8f9fa'};
                    border-bottom: 1px solid {grid_color};
                    flex-wrap: wrap;
                }}

                .control-group {{
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }}

                .control-label {{
                    font-size: 12px;
                    font-weight: 600;
                    color: {'#e2e8f0' if theme == 'dark' else '#4a5568'};
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }}

                .control-button {{
                    padding: 8px 16px;
                    border: 1px solid {grid_color};
                    background: {'#4a5568' if theme == 'dark' else '#ffffff'};
                    color: {text_color};
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }}

                .control-button:hover {{
                    background: {'#718096' if theme == 'dark' else '#f7fafc'};
                    transform: translateY(-1px);
                }}

                .control-button.active {{
                    background: #3182ce;
                    color: white;
                    border-color: #3182ce;
                }}

                #{container_id} {{
                    width: 100%;
                    height: {height}px;
                    background-color: {bg_color};
                    position: relative;
                }}

                .chart-stats {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                    gap: 15px;
                    padding: 20px;
                    background: {'#1a202c' if theme == 'dark' else '#ffffff'};
                    border-top: 1px solid {grid_color};
                }}

                .stat-item {{
                    text-align: center;
                    padding: 12px;
                    background: {'#2d3748' if theme == 'dark' else '#f8f9fa'};
                    border-radius: 8px;
                    border: 1px solid {grid_color};
                }}

                .stat-label {{
                    font-size: 11px;
                    color: {'#a0aec0' if theme == 'dark' else '#718096'};
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 4px;
                }}

                .stat-value {{
                    font-size: 16px;
                    font-weight: 700;
                    color: {text_color};
                }}

                .stat-change {{
                    font-size: 12px;
                    font-weight: 600;
                    margin-top: 2px;
                }}

                .positive {{ color: #48bb78; }}
                .negative {{ color: #f56565; }}

                .loading {{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: {height}px;
                    color: {text_color};
                    font-size: 16px;
                    gap: 15px;
                }}

                .loading-spinner {{
                    width: 40px;
                    height: 40px;
                    border: 3px solid {grid_color};
                    border-top: 3px solid #3182ce;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }}

                @keyframes spin {{
                    0% {{ transform: rotate(0deg); }}
                    100% {{ transform: rotate(360deg); }}
                }}

                .chart-footer {{
                    padding: 15px 20px;
                    background: {'#1a202c' if theme == 'dark' else '#f8f9fa'};
                    text-align: center;
                    font-size: 12px;
                    color: {'#a0aec0' if theme == 'dark' else '#718096'};
                    border-top: 1px solid {grid_color};
                }}

                google-chart {{
                    width: 100%;
                    height: 100%;
                }}
            </style>
        </head>
        <body>
            <div class="chart-container">
                <div class="chart-header">
                    <div class="chart-title">
                        📊 {clean_symbol}
                        <span style="font-size: 16px; font-weight: 500;">Professional Stock Chart</span>
                    </div>
                    <div class="chart-subtitle">
                        Egyptian Exchange • Real-time Data • Interactive Analysis
                    </div>
                </div>

                <div class="chart-controls">
                    <div class="control-group">
                        <span class="control-label">Period:</span>
                        <button class="control-button active" onclick="changePeriod('1M')">1M</button>
                        <button class="control-button" onclick="changePeriod('3M')">3M</button>
                        <button class="control-button" onclick="changePeriod('6M')">6M</button>
                        <button class="control-button" onclick="changePeriod('1Y')">1Y</button>
                    </div>
                    <div class="control-group">
                        <span class="control-label">Style:</span>
                        <button class="control-button active" onclick="changeStyle('candlestick')">Candles</button>
                        <button class="control-button" onclick="changeStyle('line')">Line</button>
                        <button class="control-button" onclick="changeStyle('area')">Area</button>
                    </div>
                    <div class="control-group">
                        <span class="control-label">Indicators:</span>
                        <button class="control-button" onclick="toggleMA()">MA</button>
                        <button class="control-button" onclick="toggleVolume()">Volume</button>
                    </div>
                </div>

                <div id="{container_id}">
                    <div class="loading">
                        <div class="loading-spinner"></div>
                        <div>Loading Enhanced Professional Chart...</div>
                        <div style="font-size: 12px; opacity: 0.7;">Powered by Google Web Components</div>
                    </div>
                </div>

                <div class="chart-stats">
                    <div class="stat-item">
                        <div class="stat-label">Latest Price</div>
                        <div class="stat-value">{latest_price:.2f} EGP</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Change</div>
                        <div class="stat-value">{price_change:+.2f} EGP</div>
                        <div class="stat-change {'positive' if price_change >= 0 else 'negative'}">{price_change_pct:+.1f}%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Data Points</div>
                        <div class="stat-value">{len(chart_data)}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Period</div>
                        <div class="stat-value">Daily</div>
                    </div>
                </div>

                <div class="chart-footer">
                    🚀 Enhanced Professional Charts • Interactive & Responsive • Real Egyptian Exchange Data
                </div>
            </div>

            <script type="text/javascript">
                // Enhanced chart data and configuration
                let chartData = {json.dumps(chart_data_json)};
                let currentStyle = 'candlestick';
                let showMA = false;
                let showVolume = false;
                let currentPeriod = '1M';
                let chart = null;

                // Load Google Charts
                google.charts.load('current', {{'packages':['corechart']}});
                google.charts.setOnLoadCallback(initializeChart);

                // Initialize the enhanced chart
                function initializeChart() {{
                    try {{
                        const container = document.getElementById('{container_id}');

                        // Validate chart data
                        if (!chartData || chartData.length === 0) {{
                            throw new Error('No chart data available');
                        }}

                        // Validate data structure
                        for (let i = 0; i < Math.min(chartData.length, 5); i++) {{
                            if (!chartData[i] || chartData[i].length !== 5) {{
                                console.error(`Data row ${{i}}:`, chartData[i]);
                                throw new Error(`Invalid data row ${{i}}: expected 5 columns, got ${{chartData[i] ? chartData[i].length : 0}}`);
                            }}
                        }}

                        console.log('Chart data validation passed. Data points:', chartData.length);
                        console.log('Sample data rows:', chartData.slice(0, 3));

                        // Prepare data for Google Chart
                        const chartDataArray = [
                            ['Date', 'Low', 'Open', 'Close', 'High']
                        ].concat(chartData);

                        const data = google.visualization.arrayToDataTable(chartDataArray);

                        // Enhanced chart options
                        const options = {{
                            backgroundColor: '{bg_color}',
                            chartArea: {{
                                left: 60,
                                top: 20,
                                width: '85%',
                                height: '80%',
                                backgroundColor: '{bg_color}'
                            }},
                            candlestick: {{
                                fallingColor: {{
                                    strokeWidth: 1,
                                    stroke: '#f56565',
                                    fill: '#f56565'
                                }},
                                risingColor: {{
                                    strokeWidth: 1,
                                    stroke: '#48bb78',
                                    fill: '#48bb78'
                                }}
                            }},
                            hAxis: {{
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 10
                                }},
                                gridlines: {{
                                    color: '{grid_color}',
                                    count: 6
                                }},
                                minorGridlines: {{
                                    color: 'transparent'
                                }}
                            }},
                            vAxis: {{
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 10
                                }},
                                gridlines: {{
                                    color: '{grid_color}',
                                    count: 5
                                }},
                                minorGridlines: {{
                                    color: 'transparent'
                                }},
                                format: '#.##'
                            }},
                            legend: {{
                                position: 'none'
                            }},
                            explorer: {{
                                actions: ['dragToZoom', 'rightClickToReset'],
                                axis: 'horizontal',
                                keepInBounds: true,
                                maxZoomIn: 8.0,
                                maxZoomOut: 2.0
                            }},
                            crosshair: {{
                                trigger: 'both',
                                orientation: 'both',
                                color: '{text_color}',
                                opacity: 0.8
                            }},
                            focusTarget: 'category',
                            tooltip: {{
                                isHtml: true,
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 11
                                }}
                            }},
                            animation: {{
                                startup: true,
                                duration: 1000,
                                easing: 'out'
                            }}
                        }};

                        // Create and draw the chart
                        chart = new google.visualization.CandlestickChart(container);
                        chart.draw(data, options);

                        console.log('Enhanced Google Chart initialized successfully');

                    }} catch (error) {{
                        console.error('Error initializing chart:', error);
                        document.getElementById('{container_id}').innerHTML =
                            '<div style="color: #ff6b6b; text-align: center; padding: 50px; font-size: 16px;">❌ Failed to load chart<br><small>Error: ' + error.message + '</small></div>';
                    }}
                }}

                // Control functions
                function changePeriod(period) {{
                    currentPeriod = period;
                    updateActiveButton('.control-group:first-child .control-button', period);

                    // Filter data based on period and redraw chart
                    let filteredData = [...chartData];
                    const now = new Date();

                    switch(period) {{
                        case '1M':
                            const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                            filteredData = chartData.filter(row => new Date(row[0]) >= oneMonthAgo);
                            break;
                        case '3M':
                            const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
                            filteredData = chartData.filter(row => new Date(row[0]) >= threeMonthsAgo);
                            break;
                        case '6M':
                            const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
                            filteredData = chartData.filter(row => new Date(row[0]) >= sixMonthsAgo);
                            break;
                        case '1Y':
                            const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                            filteredData = chartData.filter(row => new Date(row[0]) >= oneYearAgo);
                            break;
                    }}

                    // Redraw chart with filtered data
                    redrawChart(filteredData);
                    console.log('Period changed to:', period, 'Data points:', filteredData.length);
                }}

                function changeStyle(style) {{
                    currentStyle = style;
                    updateActiveButton('.control-group:nth-child(2) .control-button', style);

                    // Redraw chart with new style
                    redrawChart(getFilteredData(), style);
                    console.log('Chart style changed to:', style);
                }}

                function toggleMA() {{
                    showMA = !showMA;
                    const button = document.querySelector('.control-group:last-child .control-button:first-child');
                    button.classList.toggle('active');

                    // Redraw chart with/without moving average
                    redrawChart(getFilteredData());
                    console.log('Moving Average toggled:', showMA);
                }}

                function toggleVolume() {{
                    showVolume = !showVolume;
                    const button = document.querySelector('.control-group:last-child .control-button:last-child');
                    button.classList.toggle('active');

                    // Redraw chart with/without volume
                    redrawChart(getFilteredData());
                    console.log('Volume toggled:', showVolume);
                }}

                // Helper functions
                function getFilteredData() {{
                    let filteredData = [...chartData];
                    const now = new Date();

                    switch(currentPeriod) {{
                        case '1M':
                            const oneMonthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                            filteredData = chartData.filter(row => new Date(row[0]) >= oneMonthAgo);
                            break;
                        case '3M':
                            const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
                            filteredData = chartData.filter(row => new Date(row[0]) >= threeMonthsAgo);
                            break;
                        case '6M':
                            const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
                            filteredData = chartData.filter(row => new Date(row[0]) >= sixMonthsAgo);
                            break;
                        case '1Y':
                            const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                            filteredData = chartData.filter(row => new Date(row[0]) >= oneYearAgo);
                            break;
                    }}

                    return filteredData;
                }}

                function calculateMovingAverage(data, period = 20) {{
                    const ma = [];
                    for (let i = 0; i < data.length; i++) {{
                        if (i < period - 1) {{
                            ma.push(null);
                        }} else {{
                            let sum = 0;
                            for (let j = i - period + 1; j <= i; j++) {{
                                sum += parseFloat(data[j][3]); // Close price
                            }}
                            ma.push(sum / period);
                        }}
                    }}
                    return ma;
                }}

                function redrawChart(data, style = currentStyle) {{
                    try {{
                        const container = document.getElementById('{container_id}');

                        // Prepare data for Google Chart
                        let chartDataArray;

                        // Normalize style parameter
                        if (style === 'Candles' || style === 'candlestick' || !style) {{
                            style = 'candlestick';
                        }} else if (style === 'Line') {{
                            style = 'line';
                        }} else if (style === 'Area') {{
                            style = 'area';
                        }}

                        if (style === 'candlestick') {{
                            // Create header row
                            let header = ['Date', 'Low', 'Open', 'Close', 'High'];
                            if (showMA) {{
                                header.push('MA(20)');
                            }}

                            // Create data rows
                            chartDataArray = [header];

                            // Calculate moving average if needed
                            let ma = null;
                            if (showMA) {{
                                ma = calculateMovingAverage(data);
                            }}

                            // Add data rows
                            for (let i = 0; i < data.length; i++) {{
                                let row = [
                                    data[i][0], // Date
                                    data[i][1], // Low
                                    data[i][2], // Open
                                    data[i][3], // Close
                                    data[i][4]  // High
                                ];

                                if (showMA && ma) {{
                                    row.push(ma[i]);
                                }}

                                chartDataArray.push(row);
                            }}

                        }} else if (style === 'line') {{
                            // Create header row
                            let header = ['Date', 'Close'];
                            if (showMA) {{
                                header.push('MA(20)');
                            }}

                            // Create data rows
                            chartDataArray = [header];

                            // Calculate moving average if needed
                            let ma = null;
                            if (showMA) {{
                                ma = calculateMovingAverage(data);
                            }}

                            // Add data rows
                            for (let i = 0; i < data.length; i++) {{
                                let row = [
                                    data[i][0], // Date
                                    parseFloat(data[i][3]) // Close
                                ];

                                if (showMA && ma) {{
                                    row.push(ma[i]);
                                }}

                                chartDataArray.push(row);
                            }}

                        }} else if (style === 'area') {{
                            // Area chart doesn't support MA overlay in this implementation
                            chartDataArray = [['Date', 'Close']];

                            for (let i = 0; i < data.length; i++) {{
                                chartDataArray.push([
                                    data[i][0], // Date
                                    parseFloat(data[i][3]) // Close
                                ]);
                            }}
                        }}

                        const chartData = google.visualization.arrayToDataTable(chartDataArray);

                        // Chart options based on style
                        let options = {{
                            backgroundColor: '{bg_color}',
                            chartArea: {{
                                left: 60,
                                top: 20,
                                width: '85%',
                                height: '80%',
                                backgroundColor: '{bg_color}'
                            }},
                            hAxis: {{
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 10
                                }},
                                gridlines: {{
                                    color: '{grid_color}',
                                    count: 6
                                }}
                            }},
                            vAxis: {{
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 10
                                }},
                                gridlines: {{
                                    color: '{grid_color}',
                                    count: 5
                                }},
                                format: '#.##'
                            }},
                            legend: {{
                                position: showMA ? 'top' : 'none',
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 11
                                }}
                            }},
                            explorer: {{
                                actions: ['dragToZoom', 'rightClickToReset'],
                                axis: 'horizontal',
                                keepInBounds: true
                            }},
                            animation: {{
                                startup: false,
                                duration: 500,
                                easing: 'out'
                            }}
                        }};

                        // Style-specific options
                        if (style === 'candlestick') {{
                            options.candlestick = {{
                                fallingColor: {{
                                    strokeWidth: 1,
                                    stroke: '#f56565',
                                    fill: '#f56565'
                                }},
                                risingColor: {{
                                    strokeWidth: 1,
                                    stroke: '#48bb78',
                                    fill: '#48bb78'
                                }}
                            }};
                            chart = new google.visualization.CandlestickChart(container);
                        }} else if (style === 'line') {{
                            options.series = {{
                                0: {{color: '#3182ce', lineWidth: 2}},
                                1: {{color: '#ed8936', lineWidth: 1, lineDashStyle: [5, 5]}}
                            }};
                            chart = new google.visualization.LineChart(container);
                        }} else if (style === 'area') {{
                            options.series = {{
                                0: {{
                                    color: '#3182ce',
                                    areaOpacity: 0.3
                                }}
                            }};
                            chart = new google.visualization.AreaChart(container);
                        }}

                        chart.draw(chartData, options);

                    }} catch (error) {{
                        console.error('Error redrawing chart:', error);
                    }}
                }}

                function updateActiveButton(selector, value) {{
                    const buttons = document.querySelectorAll(selector);
                    buttons.forEach(btn => {{
                        btn.classList.remove('active');
                        if (btn.textContent.toLowerCase().includes(value.toLowerCase()) ||
                            btn.getAttribute('onclick').includes(value)) {{
                            btn.classList.add('active');
                        }}
                    }});
                }}

                // Handle window resize
                window.addEventListener('resize', function() {{
                    if (chart) {{
                        chart.draw(chart.getDataTable(), chart.getOptions());
                    }}
                }});
            </script>
        </body>
        </html>
        """

        # Render the Enhanced Google Charts component
        st.components.v1.html(google_charts_html, height=height + 300)

        # Add enhanced chart info
        st.caption("🚀 **Enhanced Professional Charts** - Powered by Google Web Components with advanced interactivity, multiple chart styles, and technical indicators")

        return {
            "symbol": clean_symbol,
            "container_id": container_id,
            "theme": theme,
            "data_points": len(chart_data)
        }

    except Exception as e:
        st.error(f"❌ Error creating Google Charts component: {str(e)}")
        st.info("💡 Please check that the stock data is available and properly formatted.")
        return None

def tradingview_chart_component(symbol: str, width: int = 1000, height: int = 600,
                               theme: str = "dark", interval: str = "D",
                               style: str = "1",
                               studies: Optional[List[str]] = None):
    """
    Embed a TradingView chart widget in Streamlit using the external embedding approach

    Args:
        symbol (str): Stock symbol (e.g., "EGX:COMI" for Commercial International Bank)
        width (int): Width of the chart in pixels
        height (int): Height of the chart in pixels
        theme (str): Chart theme ("light" or "dark")
        interval (str): Chart interval (e.g., "D" for daily, "W" for weekly, "M" for monthly)
        style (str): Chart style ("1" for bars, "2" for candles, "3" for line, "4" for area)
        studies (List[str]): List of studies to add to the chart
    """
    # Try different symbol formats for TradingView
    # For EGX stocks, we need to try different prefixes

    # First, clean up the symbol
    clean_symbol = symbol.replace(" ", "")
    if ":" in clean_symbol:
        # Extract just the symbol part if it has a prefix
        clean_symbol = clean_symbol.split(":")[1]

    # Try different formats for EGX stocks
    # Format 1: No prefix (just the symbol)
    formatted_symbol = clean_symbol

    # Debug info
    print(f"Regular Chart: Original symbol: {symbol}, Formatted for TradingView: {formatted_symbol}")

    # Map style numbers to names for the external widget
    style_map = {
        "1": "BARS",
        "2": "CANDLES",
        "3": "LINE",
        "4": "AREA"
    }
    chart_style = style_map.get(style, "CANDLES")

    # Create the TradingView widget HTML using the direct JavaScript API
    # This is more reliable for EGX stocks
    html_content = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container" style="height:{height}px;width:{width}px">
      <div id="tradingview_chart_main" style="height:100%;width:100%"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
      <script type="text/javascript">
      new TradingView.widget(
      {{
        "width": {width},
        "height": {height},
        "symbol": "EGX:{formatted_symbol}",
        "interval": "D",
        "timezone": "exchange",
        "theme": "{theme}",
        "style": "1",
        "locale": "en",
        "toolbar_bg": "#f1f3f6",
        "enable_publishing": false,
        "hide_top_toolbar": false,
        "hide_side_toolbar": false,
        "allow_symbol_change": true,
        "container_id": "tradingview_chart_main"
      }}
      );
      </script>
    </div>
    <!-- TradingView Widget END -->
    """

    # Display the TradingView widget
    st.components.v1.html(html_content, width=width, height=height)

    # Add a note about troubleshooting
    st.caption("If the chart appears black, try switching to a different tab and back, or refresh the page.")

    return formatted_symbol

def tradingview_advanced_chart_component(symbol: str, width: int = 1000, height: int = 600):
    """
    Embed a TradingView Advanced Chart widget in Streamlit using the external embedding approach

    Args:
        symbol (str): Stock symbol (e.g., "EGX:COMI" for Commercial International Bank)
        width (int): Width of the chart in pixels
        height (int): Height of the chart in pixels
    """
    # Try different symbol formats for TradingView
    # For EGX stocks, we need to try different prefixes

    # First, clean up the symbol
    clean_symbol = symbol.replace(" ", "")
    if ":" in clean_symbol:
        # Extract just the symbol part if it has a prefix
        clean_symbol = clean_symbol.split(":")[1]

    # Try different formats for EGX stocks
    # Format 1: No prefix (just the symbol)
    formatted_symbol = clean_symbol

    # Debug info
    print(f"Advanced Chart: Original symbol: {symbol}, Formatted for TradingView: {formatted_symbol}")

    # Create the TradingView Advanced Chart widget HTML using the external embedding approach
    # Use the standard chart widget with proper EGX prefix
    html_content = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container" style="height:{height}px;width:{width}px">
      <div id="tradingview_chart" style="height:100%;width:100%"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
      <script type="text/javascript">
      new TradingView.widget(
      {{
        "width": {width},
        "height": {height},
        "symbol": "EGX-{formatted_symbol}",
        "interval": "D",
        "timezone": "exchange",
        "theme": "dark",
        "style": "1",
        "locale": "en",
        "toolbar_bg": "#f1f3f6",
        "enable_publishing": false,
        "hide_top_toolbar": false,
        "hide_side_toolbar": false,
        "allow_symbol_change": true,
        "container_id": "tradingview_chart"
      }}
      );
      </script>
    </div>
    <!-- TradingView Widget END -->
    """

    # Display the TradingView widget
    st.components.v1.html(html_content, width=width, height=height)

    # Add a note about troubleshooting
    st.caption("If the chart appears black, try switching to a different tab and back, or refresh the page.")

    return formatted_symbol

def tradingview_mini_chart_component(symbol: str, width: int = 350, height: int = 220,
                                    theme: str = "dark"):
    """
    Embed a TradingView Mini Chart widget in Streamlit using the external embedding approach

    Args:
        symbol (str): Stock symbol (e.g., "EGX:COMI" for Commercial International Bank)
        width (int): Width of the chart in pixels
        height (int): Height of the chart in pixels
        theme (str): Chart theme ("light" or "dark")
    """
    # First, clean up the symbol
    clean_symbol = symbol.replace(" ", "")
    if ":" in clean_symbol:
        # Extract just the symbol part if it has a prefix
        clean_symbol = clean_symbol.split(":")[1]

    # Instead of using the mini-symbol-overview widget, let's use the single-ticker widget
    # which has better compatibility with international stocks
    html_content = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container" style="height:{height}px;width:{width}px">
      <div class="tradingview-widget-container__widget" style="height:100%;width:100%"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-single-quote.js" async>
      {{
        "symbol": "EGX:{clean_symbol}",
        "width": {width},
        "height": {height},
        "locale": "en",
        "colorTheme": "{theme}",
        "isTransparent": false
      }}
      </script>
    </div>
    <!-- TradingView Widget END -->
    """

    # Display the TradingView widget
    st.components.v1.html(html_content, width=width, height=height)

    # Return the formatted symbol (EGX:SYMBOL format)
    return f"EGX:{clean_symbol}"
