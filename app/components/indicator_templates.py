"""
Indicator templates and presets component for the AI Stocks Bot app.

This module provides functionality for creating, managing, and applying
technical indicator templates and presets for different trading strategies.
"""
import streamlit as st
import pandas as pd
import numpy as np
import json
import os
import logging
from typing import Dict, List, Optional, Union, Tuple, Any
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Constants
INDICATOR_TEMPLATES_DIR = "indicator_templates"
STRATEGY_PRESETS_DIR = "strategy_presets"

# Ensure directories exist
for directory in [INDICATOR_TEMPLATES_DIR, STRATEGY_PRESETS_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)

# Define available indicators with their TradingView IDs and parameters
AVAILABLE_INDICATORS = {
    "Moving Average": {
        "id": "MASimple@tv-basicstudies",
        "parameters": {
            "length": {"type": "int", "default": 20, "min": 1, "max": 500},
            "source": {"type": "select", "default": "close", "options": ["open", "high", "low", "close", "hl2", "hlc3", "ohlc4"]}
        }
    },
    "Exponential Moving Average": {
        "id": "MAExp@tv-basicstudies",
        "parameters": {
            "length": {"type": "int", "default": 20, "min": 1, "max": 500},
            "source": {"type": "select", "default": "close", "options": ["open", "high", "low", "close", "hl2", "hlc3", "ohlc4"]}
        }
    },
    "RSI": {
        "id": "RSI@tv-basicstudies",
        "parameters": {
            "length": {"type": "int", "default": 14, "min": 1, "max": 100},
            "source": {"type": "select", "default": "close", "options": ["open", "high", "low", "close", "hl2", "hlc3", "ohlc4"]}
        }
    },
    "MACD": {
        "id": "MACD@tv-basicstudies",
        "parameters": {
            "fast_length": {"type": "int", "default": 12, "min": 1, "max": 200},
            "slow_length": {"type": "int", "default": 26, "min": 1, "max": 200},
            "signal_length": {"type": "int", "default": 9, "min": 1, "max": 50}
        }
    },
    "Bollinger Bands": {
        "id": "BB@tv-basicstudies",
        "parameters": {
            "length": {"type": "int", "default": 20, "min": 1, "max": 500},
            "source": {"type": "select", "default": "close", "options": ["open", "high", "low", "close", "hl2", "hlc3", "ohlc4"]},
            "mult": {"type": "float", "default": 2.0, "min": 0.1, "max": 10.0, "step": 0.1}
        }
    },
    "Stochastic": {
        "id": "Stochastic@tv-basicstudies",
        "parameters": {
            "k_length": {"type": "int", "default": 14, "min": 1, "max": 100},
            "d_length": {"type": "int", "default": 3, "min": 1, "max": 50},
            "smooth": {"type": "int", "default": 3, "min": 1, "max": 50}
        }
    },
    "Volume": {
        "id": "Volume@tv-basicstudies",
        "parameters": {}
    },
    "On-Balance Volume": {
        "id": "OBV@tv-basicstudies",
        "parameters": {}
    },
    "Average True Range": {
        "id": "ATR@tv-basicstudies",
        "parameters": {
            "length": {"type": "int", "default": 14, "min": 1, "max": 100}
        }
    },
    "Ichimoku Cloud": {
        "id": "IchimokuCloud@tv-basicstudies",
        "parameters": {
            "conversion_line_length": {"type": "int", "default": 9, "min": 1, "max": 100},
            "base_line_length": {"type": "int", "default": 26, "min": 1, "max": 200},
            "lagging_span_length": {"type": "int", "default": 52, "min": 1, "max": 300},
            "displacement": {"type": "int", "default": 26, "min": 1, "max": 200}
        }
    },
    "Pivot Points Standard": {
        "id": "PivotPointsStandard@tv-basicstudies",
        "parameters": {}
    }
}

# Define strategy presets with their indicator configurations
DEFAULT_STRATEGY_PRESETS = {
    "Trend Following": {
        "description": "Identify and follow established trends",
        "indicators": [
            {"name": "Moving Average", "parameters": {"length": 50}},
            {"name": "Moving Average", "parameters": {"length": 200}},
            {"name": "MACD", "parameters": {}}
        ]
    },
    "Mean Reversion": {
        "description": "Identify overbought/oversold conditions for potential reversals",
        "indicators": [
            {"name": "RSI", "parameters": {}},
            {"name": "Bollinger Bands", "parameters": {}}
        ]
    },
    "Breakout": {
        "description": "Identify potential breakouts from consolidation patterns",
        "indicators": [
            {"name": "Bollinger Bands", "parameters": {"length": 20, "mult": 2.0}},
            {"name": "Volume", "parameters": {}}
        ]
    },
    "Support/Resistance": {
        "description": "Identify key support and resistance levels",
        "indicators": [
            {"name": "Pivot Points Standard", "parameters": {}},
            {"name": "Moving Average", "parameters": {"length": 200}}
        ]
    },
    "Volatility Based": {
        "description": "Adapt to changing market volatility",
        "indicators": [
            {"name": "Average True Range", "parameters": {}},
            {"name": "Bollinger Bands", "parameters": {"length": 20, "mult": 2.5}}
        ]
    }
}

def indicator_templates_component():
    """
    Component for managing indicator templates and strategy presets
    """
    st.subheader("Indicator Templates & Strategy Presets")

    # Create tabs for different functionalities
    template_tab, preset_tab, custom_tab = st.tabs([
        "Indicator Templates", "Strategy Presets", "Custom Indicators"
    ])

    with template_tab:
        st.write("Create and manage indicator templates")

        # Template management UI
        col1, col2 = st.columns(2)

        with col1:
            # Template selection
            templates = get_indicator_templates()
            selected_template = st.selectbox(
                "Select Template",
                options=["None"] + templates,
                index=0,
                key="indicator_template_select"
            )

            if selected_template != "None":
                template_data = load_indicator_template(selected_template)
                if template_data:
                    st.write(f"Description: {template_data.get('description', 'No description')}")

                    # Display indicators in template
                    st.write("Indicators:")
                    for indicator in template_data.get('indicators', []):
                        indicator_name = indicator.get('name', 'Unknown')
                        params = indicator.get('parameters', {})
                        param_str = ", ".join([f"{k}: {v}" for k, v in params.items()])
                        st.write(f"- {indicator_name} ({param_str})")

                    # Apply template button
                    if st.button("Apply Template", key="apply_template_btn"):
                        # Store in session state for use in charts
                        st.session_state.selected_indicator_template = template_data
                        st.success(f"Template '{selected_template}' applied")

        with col2:
            # Template creation
            st.write("Create New Template")
            new_template_name = st.text_input("Template Name", key="new_template_name")
            new_template_desc = st.text_area("Description", key="new_template_desc")

            # Indicator selection for new template
            selected_indicators = []

            # Add indicators to template
            with st.expander("Add Indicators"):
                for i in range(5):  # Allow up to 5 indicators
                    col_ind1, col_ind2 = st.columns([1, 1])

                    with col_ind1:
                        indicator_name = st.selectbox(
                            f"Indicator {i+1}",
                            options=["None"] + list(AVAILABLE_INDICATORS.keys()),
                            index=0,
                            key=f"indicator_select_{i}"
                        )

                    if indicator_name != "None":
                        with col_ind2:
                            # Show parameter inputs based on indicator type
                            indicator_info = AVAILABLE_INDICATORS.get(indicator_name, {})
                            parameters = {}

                            for param_name, param_info in indicator_info.get('parameters', {}).items():
                                if param_info['type'] == 'int':
                                    parameters[param_name] = st.number_input(
                                        param_name,
                                        min_value=param_info.get('min', 1),
                                        max_value=param_info.get('max', 1000),
                                        value=param_info.get('default', 14),
                                        step=1,
                                        key=f"param_{i}_{param_name}"
                                    )
                                elif param_info['type'] == 'float':
                                    parameters[param_name] = st.number_input(
                                        param_name,
                                        min_value=param_info.get('min', 0.1),
                                        max_value=param_info.get('max', 100.0),
                                        value=param_info.get('default', 1.0),
                                        step=param_info.get('step', 0.1),
                                        key=f"param_{i}_{param_name}"
                                    )
                                elif param_info['type'] == 'select':
                                    parameters[param_name] = st.selectbox(
                                        param_name,
                                        options=param_info.get('options', []),
                                        index=param_info.get('options', []).index(param_info.get('default', '')),
                                        key=f"param_{i}_{param_name}"
                                    )

                        # Add to selected indicators
                        selected_indicators.append({
                            "name": indicator_name,
                            "id": indicator_info.get('id', ''),
                            "parameters": parameters
                        })

            # Save template button
            if st.button("Save Template", key="save_template_btn"):
                if new_template_name and selected_indicators:
                    template_data = {
                        "name": new_template_name,
                        "description": new_template_desc,
                        "indicators": selected_indicators,
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    save_indicator_template(new_template_name, template_data)
                    st.success(f"Template '{new_template_name}' saved successfully")
                else:
                    st.warning("Please enter a template name and add at least one indicator")

    with preset_tab:
        st.write("Use predefined strategy presets")

        # Initialize strategy presets if not already done
        initialize_strategy_presets()

        # Strategy preset selection
        presets = get_strategy_presets()
        selected_preset = st.selectbox(
            "Select Strategy Preset",
            options=presets,
            index=0,
            key="strategy_preset_select"
        )

        if selected_preset:
            preset_data = load_strategy_preset(selected_preset)
            if preset_data:
                st.write(f"Description: {preset_data.get('description', 'No description')}")

                # Display indicators in preset
                st.write("Indicators:")
                for indicator in preset_data.get('indicators', []):
                    indicator_name = indicator.get('name', 'Unknown')
                    params = indicator.get('parameters', {})
                    param_str = ", ".join([f"{k}: {v}" for k, v in params.items()])
                    st.write(f"- {indicator_name} ({param_str})")

                # Apply preset button
                if st.button("Apply Preset", key="apply_preset_btn"):
                    # Convert preset to TradingView studies format
                    studies = []
                    for indicator in preset_data.get('indicators', []):
                        indicator_name = indicator.get('name', '')
                        indicator_info = AVAILABLE_INDICATORS.get(indicator_name, {})
                        if 'id' in indicator_info:
                            studies.append(indicator_info['id'])

                    # Store in session state for use in charts
                    st.session_state.selected_studies = studies
                    st.success(f"Strategy preset '{selected_preset}' applied")

    with custom_tab:
        st.write("Create custom indicator combinations")

        # Custom indicator UI
        st.write("Select indicators to display on chart")

        # Group indicators by category
        indicator_categories = {
            "Trend": ["Moving Average", "Exponential Moving Average", "MACD"],
            "Momentum": ["RSI", "Stochastic"],
            "Volatility": ["Bollinger Bands", "Average True Range"],
            "Volume": ["Volume", "On-Balance Volume"],
            "Advanced": ["Ichimoku Cloud", "Pivot Points Standard"]
        }

        # Use columns instead of tabs for indicator categories to avoid nesting issues
        col1, col2 = st.columns(2)

        # Track selected custom indicators
        if "custom_indicators" not in st.session_state:
            st.session_state.custom_indicators = []

        # Split indicator categories between columns
        categories = list(indicator_categories.keys())
        half = len(categories) // 2

        # First column
        with col1:
            for category in categories[:half]:
                st.write(f"**{category}**")
                for indicator_name in indicator_categories[category]:
                    indicator_info = AVAILABLE_INDICATORS.get(indicator_name, {})

                    # Check if this indicator is already selected
                    is_selected = any(ind.get('name') == indicator_name for ind in st.session_state.custom_indicators)

                    # Create checkbox for each indicator
                    if st.checkbox(indicator_name, value=is_selected, key=f"custom_{indicator_name}_col1"):
                        # If not already in list, add it
                        if not is_selected:
                            st.session_state.custom_indicators.append({
                                "name": indicator_name,
                                "id": indicator_info.get('id', ''),
                                "parameters": {}
                            })
                    elif is_selected:
                        # Remove from list if unchecked
                        st.session_state.custom_indicators = [
                            ind for ind in st.session_state.custom_indicators
                            if ind.get('name') != indicator_name
                        ]

        # Second column
        with col2:
            for category in categories[half:]:
                st.write(f"**{category}**")
                for indicator_name in indicator_categories[category]:
                    indicator_info = AVAILABLE_INDICATORS.get(indicator_name, {})

                    # Check if this indicator is already selected
                    is_selected = any(ind.get('name') == indicator_name for ind in st.session_state.custom_indicators)

                    # Create checkbox for each indicator
                    if st.checkbox(indicator_name, value=is_selected, key=f"custom_{indicator_name}_col2"):
                        # If not already in list, add it
                        if not is_selected:
                            st.session_state.custom_indicators.append({
                                "name": indicator_name,
                                "id": indicator_info.get('id', ''),
                                "parameters": {}
                            })
                    elif is_selected:
                        # Remove from list if unchecked
                        st.session_state.custom_indicators = [
                            ind for ind in st.session_state.custom_indicators
                            if ind.get('name') != indicator_name
                        ]

        # Display selected indicators
        if st.session_state.custom_indicators:
            st.subheader("Selected Indicators")
            for indicator in st.session_state.custom_indicators:
                st.write(f"- {indicator.get('name', 'Unknown')}")

            # Apply custom indicators button
            if st.button("Apply Custom Indicators", key="apply_custom_btn"):
                # Convert to TradingView studies format
                studies = [ind.get('id', '') for ind in st.session_state.custom_indicators if 'id' in ind]

                # Store in session state for use in charts
                st.session_state.selected_studies = studies
                st.success("Custom indicators applied")
        else:
            st.info("No indicators selected")

def get_indicator_templates() -> List[str]:
    """Get list of available indicator templates"""
    templates = []

    try:
        if os.path.exists(INDICATOR_TEMPLATES_DIR):
            for filename in os.listdir(INDICATOR_TEMPLATES_DIR):
                if filename.endswith('.json'):
                    templates.append(filename[:-5])  # Remove .json extension
    except Exception as e:
        logger.error(f"Error loading indicator templates: {str(e)}")

    return templates

def save_indicator_template(name: str, data: Dict[str, Any]) -> bool:
    """Save an indicator template"""
    try:
        if not os.path.exists(INDICATOR_TEMPLATES_DIR):
            os.makedirs(INDICATOR_TEMPLATES_DIR)

        filepath = os.path.join(INDICATOR_TEMPLATES_DIR, f"{name}.json")
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving indicator template: {str(e)}")
        return False

def load_indicator_template(name: str) -> Dict[str, Any]:
    """Load an indicator template"""
    try:
        filepath = os.path.join(INDICATOR_TEMPLATES_DIR, f"{name}.json")
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error loading indicator template: {str(e)}")

    return {}

def get_strategy_presets() -> List[str]:
    """Get list of available strategy presets"""
    presets = []

    try:
        if os.path.exists(STRATEGY_PRESETS_DIR):
            for filename in os.listdir(STRATEGY_PRESETS_DIR):
                if filename.endswith('.json'):
                    presets.append(filename[:-5])  # Remove .json extension
    except Exception as e:
        logger.error(f"Error loading strategy presets: {str(e)}")

    return presets

def initialize_strategy_presets():
    """Initialize default strategy presets if they don't exist"""
    try:
        if not os.path.exists(STRATEGY_PRESETS_DIR):
            os.makedirs(STRATEGY_PRESETS_DIR)

        # Check if presets already exist
        existing_presets = get_strategy_presets()

        # Create default presets if they don't exist
        for preset_name, preset_data in DEFAULT_STRATEGY_PRESETS.items():
            if preset_name not in existing_presets:
                preset_data['name'] = preset_name
                preset_data['created_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                save_strategy_preset(preset_name, preset_data)
    except Exception as e:
        logger.error(f"Error initializing strategy presets: {str(e)}")

def save_strategy_preset(name: str, data: Dict[str, Any]) -> bool:
    """Save a strategy preset"""
    try:
        if not os.path.exists(STRATEGY_PRESETS_DIR):
            os.makedirs(STRATEGY_PRESETS_DIR)

        filepath = os.path.join(STRATEGY_PRESETS_DIR, f"{name}.json")
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving strategy preset: {str(e)}")
        return False

def load_strategy_preset(name: str) -> Dict[str, Any]:
    """Load a strategy preset"""
    try:
        filepath = os.path.join(STRATEGY_PRESETS_DIR, f"{name}.json")
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error loading strategy preset: {str(e)}")

    return {}
