"""
Chart patterns and drawing tools component for the AI Stocks Bot app.

This module provides functionality for identifying and visualizing chart patterns,
as well as tools for drawing and annotating charts.
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from typing import Dict, List, Optional, Union, Tuple, Any
import logging
import json
import os
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Constants
PATTERNS_DIR = "chart_patterns"
DRAWING_TOOLS_DIR = "drawing_tools"

# Ensure directories exist
for directory in [PATTERNS_DIR, DRAWING_TOOLS_DIR]:
    if not os.path.exists(directory):
        os.makedirs(directory)

class ChartPattern:
    """Class representing a chart pattern"""

    def __init__(self, name: str, description: str, points: List[Dict[str, Any]]):
        self.name = name
        self.description = description
        self.points = points

    def to_dict(self) -> Dict[str, Any]:
        """Convert pattern to dictionary for serialization"""
        return {
            "name": self.name,
            "description": self.description,
            "points": self.points,
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChartPattern':
        """Create pattern from dictionary"""
        return cls(
            name=data.get("name", "Unnamed Pattern"),
            description=data.get("description", ""),
            points=data.get("points", [])
        )

def chart_patterns_component(symbol: str, data: pd.DataFrame, mode: str = "all"):
    """
    Component for identifying and managing chart patterns

    Args:
        symbol (str): Stock symbol
        data (pd.DataFrame): Historical price data
        mode (str): Component mode - "all", "patterns_only", or "drawing_only"
    """
    # Skip subheader and tabs if in specific mode
    if mode == "all":
        st.subheader("Chart Patterns & Drawing Tools")

        # Create tabs for different functionalities
        pattern_tab, drawing_tab, template_tab = st.tabs([
            "Pattern Recognition", "Drawing Tools", "Pattern Templates"
        ])
    elif mode == "patterns_only":
        # Only show pattern recognition content
        pattern_tab = st
        drawing_tab = None
        template_tab = None
    elif mode == "drawing_only":
        # Only show drawing tools content
        pattern_tab = None
        drawing_tab = st
        template_tab = None
    else:
        # Default to all tabs
        st.subheader("Chart Patterns & Drawing Tools")

        # Create tabs for different functionalities
        pattern_tab, drawing_tab, template_tab = st.tabs([
            "Pattern Recognition", "Drawing Tools", "Pattern Templates"
        ])

    if pattern_tab:
        pattern_tab.write("Identify common chart patterns automatically")

        # Pattern recognition settings
        col1, col2 = pattern_tab.columns(2)

        with col1:
            pattern_types = st.multiselect(
                "Pattern Types",
                options=[
                    "Head and Shoulders", "Inverse Head and Shoulders",
                    "Double Top", "Double Bottom",
                    "Triple Top", "Triple Bottom",
                    "Ascending Triangle", "Descending Triangle", "Symmetrical Triangle",
                    "Cup and Handle", "Rounding Bottom",
                    "Wedge", "Flag", "Pennant"
                ],
                default=["Head and Shoulders", "Double Top", "Double Bottom"]
            )

        with col2:
            lookback_period = st.slider(
                "Lookback Period (days)",
                min_value=30,
                max_value=365,
                value=180,
                step=30
            )

        # Run pattern detection
        detect_btn = pattern_tab.button("Detect Patterns", key="detect_patterns_btn")
        if detect_btn:
            with pattern_tab.spinner("Detecting patterns..."):
                # Filter data based on lookback period
                if len(data) > lookback_period:
                    analysis_data = data.tail(lookback_period).copy()
                else:
                    analysis_data = data.copy()

                # Detect patterns
                detected_patterns = detect_chart_patterns(analysis_data, pattern_types)

                if detected_patterns:
                    pattern_tab.success(f"Detected {len(detected_patterns)} patterns")

                    # Display detected patterns
                    for i, pattern in enumerate(detected_patterns):
                        pattern_container = pattern_tab.container()
                        pattern_container.subheader(f"{pattern['name']} ({pattern['confidence']:.1f}% confidence)")
                        pattern_container.write(pattern['description'])

                        # Create a chart with the pattern highlighted
                        fig = go.Figure()

                        # Add candlestick chart
                        fig.add_trace(go.Candlestick(
                            x=analysis_data['Date'],
                            open=analysis_data['Open'],
                            high=analysis_data['High'],
                            low=analysis_data['Low'],
                            close=analysis_data['Close'],
                            name="Price"
                        ))

                        # Highlight pattern region
                        if 'start_idx' in pattern and 'end_idx' in pattern:
                            start_date = analysis_data['Date'].iloc[pattern['start_idx']]
                            end_date = analysis_data['Date'].iloc[pattern['end_idx']]

                            # Add shape to highlight the pattern
                            fig.add_shape(
                                type="rect",
                                x0=start_date,
                                y0=analysis_data['Low'].iloc[pattern['start_idx']:pattern['end_idx']+1].min() * 0.99,
                                x1=end_date,
                                y1=analysis_data['High'].iloc[pattern['start_idx']:pattern['end_idx']+1].max() * 1.01,
                                line=dict(color="rgba(255, 0, 0, 0.5)", width=2),
                                fillcolor="rgba(255, 0, 0, 0.1)"
                            )

                            # Add pattern name as annotation
                            fig.add_annotation(
                                x=start_date,
                                y=analysis_data['High'].iloc[pattern['start_idx']:pattern['end_idx']+1].max() * 1.03,
                                text=pattern['name'],
                                showarrow=True,
                                arrowhead=1
                            )

                        # Update layout
                        fig.update_layout(
                            title=f"{pattern['name']} Pattern",
                            xaxis_title="Date",
                            yaxis_title="Price",
                            xaxis_rangeslider_visible=False
                        )

                        pattern_container.plotly_chart(fig, use_container_width=True)

                        # Option to save pattern
                        if pattern_container.button("Save Pattern", key=f"save_pattern_{i}"):
                            pattern_obj = ChartPattern(
                                name=pattern['name'],
                                description=pattern['description'],
                                points=[
                                    {"x": str(analysis_data['Date'].iloc[pattern['start_idx']]), "y": float(analysis_data['Close'].iloc[pattern['start_idx']])},
                                    {"x": str(analysis_data['Date'].iloc[pattern['end_idx']]), "y": float(analysis_data['Close'].iloc[pattern['end_idx']])}
                                ]
                            )
                            save_pattern(symbol, pattern_obj)
                            pattern_container.success(f"Pattern saved for {symbol}")
                else:
                    pattern_tab.info("No patterns detected in the selected time period")

    if drawing_tab:
        drawing_tab.write("Create and manage custom drawing tools")

        # Drawing tools UI
        drawing_tools = [
            "Trend Line", "Horizontal Line", "Vertical Line",
            "Fibonacci Retracement", "Fibonacci Extension",
            "Andrews' Pitchfork", "Gann Fan",
            "Rectangle", "Ellipse", "Triangle",
            "Text Annotation", "Arrow"
        ]

        selected_tool = drawing_tab.selectbox(
            "Select Drawing Tool",
            options=drawing_tools
        )

        # Display instructions for the selected tool
        tool_instructions = get_drawing_tool_instructions(selected_tool)
        drawing_tab.info(tool_instructions)

        # Note about TradingView integration
        drawing_tab.markdown("""
        **Note:** For the full drawing experience, use the TradingView chart which provides
        interactive drawing tools directly on the chart. This section provides guidance on
        how to use those tools effectively.
        """)

        # Display saved drawings
        saved_drawings = get_saved_drawings(symbol)
        if saved_drawings:
            drawing_tab.subheader("Saved Drawings")
            for i, drawing in enumerate(saved_drawings):
                with drawing_tab.expander(f"{drawing['tool']} - {drawing['name']}"):
                    drawing_tab.write(drawing['description'])
                    drawing_tab.write(f"Created: {drawing['created_at']}")

    if template_tab:
        template_tab.write("Create and apply pattern templates")

        # Pattern templates UI
        col1, col2 = template_tab.columns(2)

        with col1:
            # Template selection
            templates = get_pattern_templates()
            selected_template = st.selectbox(
                "Select Template",
                options=["None"] + templates,
                index=0
            )

            if selected_template != "None":
                template_data = load_pattern_template(selected_template)
                if template_data:
                    st.write(f"Description: {template_data.get('description', 'No description')}")
                    st.write(f"Pattern Type: {template_data.get('pattern_type', 'Unknown')}")

        with col2:
            # Template creation
            new_template_name = st.text_input("New Template Name")
            new_template_desc = st.text_area("Description")
            new_template_type = st.selectbox(
                "Pattern Type",
                options=[
                    "Reversal", "Continuation", "Bilateral",
                    "Support/Resistance", "Trend", "Volatility"
                ]
            )

            if st.button("Create Template", key="create_template_btn"):
                if new_template_name:
                    template_data = {
                        "name": new_template_name,
                        "description": new_template_desc,
                        "pattern_type": new_template_type,
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    save_pattern_template(new_template_name, template_data)
                    st.success(f"Template '{new_template_name}' created successfully")
                else:
                    st.warning("Please enter a template name")

def detect_chart_patterns(data: pd.DataFrame, pattern_types: List[str]) -> List[Dict[str, Any]]:
    """
    Detect chart patterns in the given data

    Args:
        data (pd.DataFrame): Historical price data
        pattern_types (List[str]): Types of patterns to detect

    Returns:
        List[Dict[str, Any]]: List of detected patterns
    """
    # This is a placeholder for actual pattern detection logic
    # In a real implementation, this would use technical analysis algorithms

    detected_patterns = []

    # Simple example pattern detection
    if "Double Top" in pattern_types:
        # Find local maxima
        for i in range(10, len(data) - 10):
            if (data['High'].iloc[i] > data['High'].iloc[i-1:i].max() and
                data['High'].iloc[i] > data['High'].iloc[i+1:i+10].max()):

                # Look for another peak of similar height
                for j in range(i + 5, len(data) - 5):
                    if (abs(data['High'].iloc[j] - data['High'].iloc[i]) / data['High'].iloc[i] < 0.03 and
                        data['High'].iloc[j] > data['High'].iloc[j-5:j].max() and
                        data['High'].iloc[j] > data['High'].iloc[j+1:j+5].max()):

                        # Found potential double top
                        detected_patterns.append({
                            "name": "Double Top",
                            "description": "A double top is a reversal pattern that forms after an extended move up. The pattern is confirmed when the price falls below the neckline support level.",
                            "confidence": 85.0,
                            "start_idx": i - 5,
                            "end_idx": j + 5
                        })
                        break

    # Add more pattern detection logic here

    return detected_patterns

def get_drawing_tool_instructions(tool_name: str) -> str:
    """Get instructions for using a specific drawing tool"""
    instructions = {
        "Trend Line": "Connect two significant price points to identify the direction of a trend. Look for touches or bounces off the line to confirm its significance.",
        "Horizontal Line": "Draw at significant support or resistance levels. These can be previous highs/lows or psychological price levels.",
        "Vertical Line": "Mark important dates or events that might impact price action.",
        "Fibonacci Retracement": "Draw from a significant low to a significant high (or vice versa) to identify potential retracement levels.",
        "Fibonacci Extension": "Use three points: start of a move, end of a move, and the retracement level to project potential extension targets.",
        "Andrews' Pitchfork": "Select three points: a significant pivot high/low followed by two reaction highs/lows to create a channel of support and resistance.",
        "Gann Fan": "Place at significant market tops or bottoms to project potential support/resistance levels based on Gann angles.",
        "Rectangle": "Draw around price consolidation areas to identify potential breakout levels.",
        "Ellipse": "Encircle price patterns or consolidation areas.",
        "Triangle": "Connect three significant points to highlight triangle patterns (ascending, descending, or symmetrical).",
        "Text Annotation": "Add notes or observations directly on the chart.",
        "Arrow": "Highlight specific price movements or potential entry/exit points."
    }

    return instructions.get(tool_name, "No instructions available for this tool.")

def save_pattern(symbol: str, pattern: ChartPattern) -> bool:
    """Save a detected pattern"""
    try:
        # Create symbol directory if it doesn't exist
        symbol_dir = os.path.join(PATTERNS_DIR, symbol)
        if not os.path.exists(symbol_dir):
            os.makedirs(symbol_dir)

        # Generate filename
        filename = f"{pattern.name.replace(' ', '_').lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = os.path.join(symbol_dir, filename)

        # Save pattern data
        with open(filepath, 'w') as f:
            json.dump(pattern.to_dict(), f, indent=2)

        return True
    except Exception as e:
        logger.error(f"Error saving pattern: {str(e)}")
        return False

def get_saved_drawings(symbol: str) -> List[Dict[str, Any]]:
    """Get saved drawings for a symbol"""
    drawings = []

    try:
        symbol_dir = os.path.join(DRAWING_TOOLS_DIR, symbol)
        if os.path.exists(symbol_dir):
            for filename in os.listdir(symbol_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(symbol_dir, filename)
                    with open(filepath, 'r') as f:
                        drawing_data = json.load(f)
                        drawings.append(drawing_data)
    except Exception as e:
        logger.error(f"Error loading drawings: {str(e)}")

    return drawings

def get_pattern_templates() -> List[str]:
    """Get list of available pattern templates"""
    templates = []

    try:
        if os.path.exists(PATTERNS_DIR):
            for filename in os.listdir(PATTERNS_DIR):
                if filename.endswith('.json') and not os.path.isdir(os.path.join(PATTERNS_DIR, filename)):
                    templates.append(filename[:-5])  # Remove .json extension
    except Exception as e:
        logger.error(f"Error loading pattern templates: {str(e)}")

    return templates

def save_pattern_template(name: str, data: Dict[str, Any]) -> bool:
    """Save a pattern template"""
    try:
        filepath = os.path.join(PATTERNS_DIR, f"{name}.json")
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving pattern template: {str(e)}")
        return False

def load_pattern_template(name: str) -> Dict[str, Any]:
    """Load a pattern template"""
    try:
        filepath = os.path.join(PATTERNS_DIR, f"{name}.json")
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Error loading pattern template: {str(e)}")

    return {}
