"""
Performance Metrics Dashboard Component

This module provides components for tracking and visualizing prediction performance.
It includes:
1. Prediction Accuracy Tracker - Track and display how accurate past predictions were
2. Model Performance Comparison - Visual comparison of different models' performance
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import os
import json
import logging
from typing import Dict, List, Tuple, Optional, Union

# Set up logging
logger = logging.getLogger(__name__)

# Constants
METRICS_DIR = "data/metrics"
PREDICTIONS_FILE = os.path.join(METRICS_DIR, "predictions.csv")
PERFORMANCE_FILE = os.path.join(METRICS_DIR, "model_performance.csv")

# Ensure metrics directory exists
os.makedirs(METRICS_DIR, exist_ok=True)

def get_timedelta(horizon: int, horizon_unit: str) -> timedelta:
    """
    Convert horizon and unit to timedelta

    Args:
        horizon: Number of time units
        horizon_unit: Unit of time (minutes, days, weeks)

    Returns:
        timedelta object
    """
    if horizon_unit == "minutes":
        return timedelta(minutes=horizon)
    elif horizon_unit == "days":
        return timedelta(days=horizon)
    elif horizon_unit == "weeks":
        return timedelta(weeks=horizon)
    else:
        raise ValueError(f"Unknown horizon unit: {horizon_unit}")

def save_prediction_record(prediction_record: Dict) -> None:
    """
    Save prediction record to CSV file

    Args:
        prediction_record: Dictionary containing prediction details
    """
    # Create DataFrame from record
    record_df = pd.DataFrame([prediction_record])

    # Check if file exists
    if os.path.exists(PREDICTIONS_FILE):
        # Append to existing file
        predictions_df = pd.read_csv(PREDICTIONS_FILE)
        predictions_df = pd.concat([predictions_df, record_df], ignore_index=True)
    else:
        # Create new file
        predictions_df = record_df

    # Save to CSV
    predictions_df.to_csv(PREDICTIONS_FILE, index=False)
    logger.info(f"Saved prediction record for {prediction_record['symbol']} with horizon {prediction_record['horizon']} {prediction_record['horizon_unit']}")

def track_prediction(symbol: str, predictions: Dict, horizon_unit: str, model_type: str) -> None:
    """
    Store predictions for later verification

    Args:
        symbol: Stock symbol
        predictions: Dictionary of predictions for different horizons
        horizon_unit: Unit of time for horizons (minutes, days, weeks)
        model_type: Type of model used for prediction
    """
    # Store predictions with timestamp
    current_time = datetime.now()

    # Create a record for each prediction
    for horizon, price in predictions.items():
        prediction_record = {
            'symbol': symbol,
            'horizon': horizon,
            'horizon_unit': horizon_unit,
            'predicted_price': price,
            'prediction_time': current_time,
            'target_time': current_time + get_timedelta(horizon, horizon_unit),
            'actual_price': None,  # To be filled later
            'verified': False,
            'model_type': model_type
        }

        # Save to CSV
        save_prediction_record(prediction_record)

def verify_predictions() -> int:
    """
    Check for predictions that can be verified with actual prices

    Returns:
        Number of predictions verified
    """
    if not os.path.exists(PREDICTIONS_FILE):
        return 0

    # Load predictions
    predictions_df = pd.read_csv(PREDICTIONS_FILE)

    # Convert datetime columns
    predictions_df['prediction_time'] = pd.to_datetime(predictions_df['prediction_time'])
    predictions_df['target_time'] = pd.to_datetime(predictions_df['target_time'])

    # Filter unverified predictions where target time has passed
    unverified = predictions_df[(predictions_df['verified'] == False) &
                               (predictions_df['target_time'] <= datetime.now())]

    if unverified.empty:
        return 0

    # Get actual prices for each prediction
    verified_count = 0

    for idx, row in unverified.iterrows():
        try:
            # Get actual price at target time
            actual_price = get_actual_price(row['symbol'], row['target_time'])

            if actual_price is not None:
                # Update prediction record
                predictions_df.loc[idx, 'actual_price'] = actual_price
                predictions_df.loc[idx, 'verified'] = True
                verified_count += 1
        except Exception as e:
            logger.error(f"Error verifying prediction: {str(e)}")

    # Save updated predictions
    predictions_df.to_csv(PREDICTIONS_FILE, index=False)

    # Update model performance metrics if we verified any predictions
    if verified_count > 0:
        update_model_performance()

    return verified_count

def get_actual_price(symbol: str, target_time: datetime) -> Optional[float]:
    """
    Get actual price for a symbol at a specific time

    Args:
        symbol: Stock symbol
        target_time: Target time for price

    Returns:
        Actual price or None if not available
    """
    # Load historical data
    try:
        data_file = f"data/stocks/{symbol}.csv"
        if not os.path.exists(data_file):
            logger.warning(f"No data file found for {symbol}")
            return None

        historical_data = pd.read_csv(data_file)
        historical_data['Date'] = pd.to_datetime(historical_data['Date'])

        # Check if target_time is in the future compared to our latest data
        latest_date = historical_data['Date'].max()
        if target_time > latest_date:
            logger.info(f"Target time {target_time} is in the future compared to latest data {latest_date}")
            return None

        # Find closest data point
        closest_idx = (historical_data['Date'] - target_time).abs().idxmin()
        closest_row = historical_data.iloc[closest_idx]

        # Check if the closest point is within 1 day of target
        time_diff_seconds = abs((closest_row['Date'] - target_time).total_seconds())
        if time_diff_seconds <= 86400:  # 24 hours
            logger.info(f"Found price {closest_row['Close']} for {symbol} at {closest_row['Date']} (within {time_diff_seconds/3600:.1f} hours of target)")
            return closest_row['Close']
        else:
            logger.warning(f"Closest data point for {symbol} is too far from target: {time_diff_seconds/3600:.1f} hours")

        return None
    except Exception as e:
        logger.error(f"Error getting actual price: {str(e)}")
        return None

def calculate_performance_metrics(verified_predictions: pd.DataFrame) -> Dict:
    """
    Calculate performance metrics for verified predictions

    Args:
        verified_predictions: DataFrame of verified predictions

    Returns:
        Dictionary of performance metrics
    """
    if verified_predictions.empty:
        return {
            'rmse': 0,
            'mae': 0,
            'mape': 0,
            'directional_accuracy': 0,
            'count': 0
        }

    # Calculate errors
    verified_predictions['error'] = verified_predictions['actual_price'] - verified_predictions['predicted_price']
    verified_predictions['abs_error'] = abs(verified_predictions['error'])
    verified_predictions['pct_error'] = verified_predictions['abs_error'] / verified_predictions['actual_price'] * 100

    # Calculate directional accuracy
    verified_predictions['prev_price'] = verified_predictions['actual_price'].shift(1)
    verified_predictions['actual_direction'] = np.sign(verified_predictions['actual_price'] - verified_predictions['prev_price'])
    verified_predictions['predicted_direction'] = np.sign(verified_predictions['predicted_price'] - verified_predictions['prev_price'])
    verified_predictions['correct_direction'] = verified_predictions['actual_direction'] == verified_predictions['predicted_direction']

    # Calculate metrics
    metrics = {
        'rmse': np.sqrt(np.mean(verified_predictions['error'] ** 2)),
        'mae': np.mean(verified_predictions['abs_error']),
        'mape': np.mean(verified_predictions['pct_error']),
        'directional_accuracy': np.mean(verified_predictions['correct_direction']) * 100,
        'count': len(verified_predictions)
    }

    return metrics

def update_model_performance() -> None:
    """
    Update performance metrics for all models
    """
    if not os.path.exists(PREDICTIONS_FILE):
        return

    # Load verified predictions
    predictions_df = pd.read_csv(PREDICTIONS_FILE)
    verified = predictions_df[predictions_df['verified'] == True]

    if verified.empty:
        return

    # Group by model type and symbol
    performance_data = []

    for (model_type, symbol), group in verified.groupby(['model_type', 'symbol']):
        # Calculate metrics
        metrics = calculate_performance_metrics(group)

        # Add to performance data
        performance_data.append({
            'model_type': model_type,
            'symbol': symbol,
            'rmse': metrics['rmse'],
            'mae': metrics['mae'],
            'mape': metrics['mape'],
            'directional_accuracy': metrics['directional_accuracy'],
            'count': metrics['count'],
            'last_updated': datetime.now()
        })

    # Save performance data
    performance_df = pd.DataFrame(performance_data)
    performance_df.to_csv(PERFORMANCE_FILE, index=False)
    logger.info(f"Updated performance metrics for {len(performance_data)} model-symbol combinations")

def plot_prediction_accuracy(symbol: str, timeframe: str = '1month') -> go.Figure:
    """
    Plot prediction accuracy over time

    Args:
        symbol: Stock symbol
        timeframe: Time period for visualization (1week, 1month, 3months, etc.)

    Returns:
        Plotly figure
    """
    if not os.path.exists(PREDICTIONS_FILE):
        return go.Figure()

    # Load verified predictions
    predictions_df = pd.read_csv(PREDICTIONS_FILE)
    verified = predictions_df[(predictions_df['verified'] == True) &
                             (predictions_df['symbol'] == symbol)]

    if verified.empty:
        return go.Figure()

    # Convert datetime columns
    verified['prediction_time'] = pd.to_datetime(verified['prediction_time'])
    verified['target_time'] = pd.to_datetime(verified['target_time'])

    # Filter by timeframe
    if timeframe == '1week':
        cutoff = datetime.now() - timedelta(days=7)
    elif timeframe == '1month':
        cutoff = datetime.now() - timedelta(days=30)
    elif timeframe == '3months':
        cutoff = datetime.now() - timedelta(days=90)
    elif timeframe == '6months':
        cutoff = datetime.now() - timedelta(days=180)
    elif timeframe == '1year':
        cutoff = datetime.now() - timedelta(days=365)
    else:
        cutoff = datetime.now() - timedelta(days=30)  # Default to 1 month

    verified = verified[verified['prediction_time'] >= cutoff]

    if verified.empty:
        return go.Figure()

    # Calculate errors
    verified['error'] = verified['actual_price'] - verified['predicted_price']
    verified['abs_error'] = abs(verified['error'])
    verified['pct_error'] = verified['abs_error'] / verified['actual_price'] * 100

    # Group by date and model type
    verified['date'] = verified['prediction_time'].dt.date
    daily_errors = verified.groupby(['date', 'model_type']).agg({
        'pct_error': 'mean',
        'abs_error': 'mean',
        'error': 'mean'
    }).reset_index()

    # Create figure
    fig = go.Figure()

    # Add trace for each model type
    for model_type, group in daily_errors.groupby('model_type'):
        fig.add_trace(go.Scatter(
            x=group['date'],
            y=group['pct_error'],
            mode='lines+markers',
            name=f'{model_type} (MAPE %)',
            hovertemplate='Date: %{x}<br>MAPE: %{y:.2f}%'
        ))

    # Update layout
    fig.update_layout(
        title=f'Prediction Accuracy for {symbol} ({timeframe})',
        xaxis_title='Date',
        yaxis_title='Mean Absolute Percentage Error (%)',
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    return fig

def plot_model_comparison(symbol: str, metric: str = 'directional_accuracy') -> go.Figure:
    """
    Plot comparison of model performance

    Args:
        symbol: Stock symbol
        metric: Performance metric to compare (rmse, mae, mape, directional_accuracy)

    Returns:
        Plotly figure
    """
    if not os.path.exists(PERFORMANCE_FILE):
        return go.Figure()

    # Load performance data
    performance_df = pd.read_csv(PERFORMANCE_FILE)
    symbol_perf = performance_df[performance_df['symbol'] == symbol]

    if symbol_perf.empty:
        return go.Figure()

    # Create figure
    if metric == 'directional_accuracy':
        # Higher is better for directional accuracy
        fig = px.bar(
            symbol_perf,
            x='model_type',
            y='directional_accuracy',
            color='model_type',
            title=f'Model Comparison for {symbol} - Directional Accuracy',
            labels={'directional_accuracy': 'Directional Accuracy (%)', 'model_type': 'Model Type'},
            text_auto='.1f'
        )
    else:
        # Lower is better for error metrics
        fig = px.bar(
            symbol_perf,
            x='model_type',
            y=metric,
            color='model_type',
            title=f'Model Comparison for {symbol} - {metric.upper()}',
            labels={metric: metric.upper(), 'model_type': 'Model Type'},
            text_auto='.3f'
        )

    # Update layout
    fig.update_layout(
        xaxis_title='Model Type',
        yaxis_title=metric.upper(),
        showlegend=False
    )

    return fig

def performance_metrics_dashboard(symbol: str):
    """
    Main component for performance metrics dashboard

    Args:
        symbol: Stock symbol
    """
    st.title("Performance Metrics Dashboard")

    # Verify any pending predictions
    verified_count = verify_predictions()
    if verified_count > 0:
        st.success(f"Verified {verified_count} predictions with actual outcomes")

    # Show status of unverified predictions
    if os.path.exists(PREDICTIONS_FILE):
        predictions_df = pd.read_csv(PREDICTIONS_FILE)
        unverified = predictions_df[(predictions_df['verified'] == False) &
                                   (predictions_df['symbol'] == symbol)]

        if not unverified.empty:
            # Convert datetime columns
            unverified['prediction_time'] = pd.to_datetime(unverified['prediction_time'])
            unverified['target_time'] = pd.to_datetime(unverified['target_time'])

            # Count predictions by status
            pending = unverified[unverified['target_time'] > datetime.now()]
            ready = unverified[unverified['target_time'] <= datetime.now()]

            # Show counts
            col1, col2 = st.columns(2)
            with col1:
                st.info(f"**{len(pending)}** predictions pending verification (target time not yet reached)")
            with col2:
                if len(ready) > 0:
                    st.warning(f"**{len(ready)}** predictions ready for verification but missing actual price data")

            # Show explanation if needed
            if len(ready) > 0:
                st.info("To verify predictions, you need to update your historical data to include the target times. Use the 'Live Data' page to fetch the latest prices.")

                # Show the earliest and latest target times that need data
                if not ready.empty:
                    earliest = ready['target_time'].min()
                    latest = ready['target_time'].max()
                    st.write(f"Need price data from {earliest.strftime('%Y-%m-%d %H:%M')} to {latest.strftime('%Y-%m-%d %H:%M')}")

    # Create tabs for different sections
    tabs = st.tabs(["Prediction Accuracy", "Model Comparison", "Detailed Metrics"])

    with tabs[0]:
        st.subheader("Prediction Accuracy Tracker")

        # Timeframe selection
        timeframe = st.selectbox(
            "Select timeframe",
            options=["1week", "1month", "3months", "6months", "1year"],
            index=1,
            key="accuracy_timeframe"
        )

        # Plot accuracy over time
        fig = plot_prediction_accuracy(symbol, timeframe)
        if fig.data:
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info(f"No verified predictions found for {symbol} in the selected timeframe")

    with tabs[1]:
        st.subheader("Model Performance Comparison")

        # Metric selection
        metric = st.selectbox(
            "Select performance metric",
            options=["directional_accuracy", "rmse", "mae", "mape"],
            index=0,
            format_func=lambda x: {
                "directional_accuracy": "Directional Accuracy (%)",
                "rmse": "Root Mean Square Error (RMSE)",
                "mae": "Mean Absolute Error (MAE)",
                "mape": "Mean Absolute Percentage Error (MAPE)"
            }[x],
            key="comparison_metric"
        )

        # Plot model comparison
        fig = plot_model_comparison(symbol, metric)
        if fig.data:
            st.plotly_chart(fig, use_container_width=True)

            # Add explanation
            if metric == "directional_accuracy":
                st.info("Directional Accuracy: Higher is better. Shows how often the model correctly predicts the price direction.")
            else:
                st.info(f"{metric.upper()}: Lower is better. Measures the prediction error magnitude.")
        else:
            st.info(f"No performance data found for {symbol}")

    with tabs[2]:
        st.subheader("Detailed Metrics")

        # Load and display detailed metrics
        if os.path.exists(PREDICTIONS_FILE):
            predictions_df = pd.read_csv(PREDICTIONS_FILE)
            verified = predictions_df[(predictions_df['verified'] == True) &
                                     (predictions_df['symbol'] == symbol)]

            if not verified.empty:
                # Convert datetime columns
                verified['prediction_time'] = pd.to_datetime(verified['prediction_time'])
                verified['target_time'] = pd.to_datetime(verified['target_time'])

                # Calculate additional metrics
                verified['error'] = verified['actual_price'] - verified['predicted_price']
                verified['abs_error'] = abs(verified['error'])
                verified['pct_error'] = verified['abs_error'] / verified['actual_price'] * 100

                # Display metrics table
                st.dataframe(
                    verified[[
                        'model_type', 'horizon', 'horizon_unit', 'prediction_time',
                        'target_time', 'predicted_price', 'actual_price',
                        'error', 'abs_error', 'pct_error'
                    ]].sort_values('prediction_time', ascending=False),
                    use_container_width=True
                )
            else:
                st.info(f"No verified predictions found for {symbol}")
        else:
            st.info("No prediction data available yet")
