"""
EGX-Optimized SMC Parameters
Configured specifically for Egyptian Exchange market conditions
"""

def get_egx_parameters():
    """
    Get EGX-optimized parameters for SMC analysis
    
    Returns:
        dict: Configuration parameters optimized for Egyptian Exchange
    """
    return {
        # Order Blocks Configuration
        'order_blocks': {
            'lookback_periods': 20,
            'min_strength': 0.3,
            'wick_to_body_ratio': 0.5,
            'volume_multiplier': 1.5
        },
        
        # Fair Value Gaps Configuration
        'fvg': {
            'min_gap_size_pct': 0.001,  # 0.1% minimum gap
            'max_gap_age_bars': 50,
            'strength_threshold': 0.4
        },
        
        # Liquidity Zones Configuration
        'liquidity_zones': {
            'lookback_periods': 20,
            'test_tolerance_pct': 0.5,  # 0.5% tolerance for level testing
            'min_tests': 2,
            'strength_threshold': 0.2
        },
        
        # Displacement Configuration
        'displacement': {
            'min_displacement_pct': 1.0,  # 1% minimum displacement
            'volume_threshold': 1.2,  # 20% above average volume
            'lookback_periods': 10
        },
        
        # Market Structure Configuration
        'market_structure': {
            'swing_lookback': 15,
            'trend_confirmation_bars': 5,
            'structure_break_threshold': 0.5
        },
        
        # Distance Filters (for active zones)
        'distance_filters': {
            'order_blocks_pct': 5.0,      # 5% max distance from current price
            'fvg_pct': 3.0,               # 3% max distance for FVGs
            'liquidity_zones_pct': 4.0,   # 4% max distance for liquidity zones
            'displacement_pct': 2.0       # 2% max distance for recent displacements
        },
        
        # Confluence Scoring
        'confluence': {
            'weights': {
                'order_blocks': 0.3,
                'fvg': 0.25,
                'liquidity_zones': 0.2,
                'displacement': 0.15,
                'market_structure': 0.1
            },
            'min_factors': 2,  # Minimum factors for valid confluence
            'strong_confluence_threshold': 0.7
        },
        
        # EGX-Specific Settings
        'egx_specific': {
            'trading_hours': {
                'start': '10:00',
                'end': '14:30',
                'timezone': 'Africa/Cairo'
            },
            'currency': 'EGP',
            'tick_size': 0.01,
            'min_volume': 10000,
            'major_stocks': [
                'COMI', 'FWRY', 'PHDC', 'EFID', 
                'UBEE', 'GGRN', 'OBRI', 'UTOP'
            ]
        },
        
        # Risk Management
        'risk_management': {
            'max_risk_per_trade_pct': 2.0,  # 2% max risk per trade
            'risk_reward_ratio': 2.0,       # Minimum 1:2 risk/reward
            'stop_loss_atr_multiplier': 1.5,
            'take_profit_atr_multiplier': 3.0
        },
        
        # Signal Validation
        'signal_validation': {
            'min_confluence_score': 0.5,
            'require_volume_confirmation': True,
            'require_structure_alignment': True,
            'max_signal_age_bars': 10
        }
    }

def get_stock_specific_parameters(symbol: str):
    """
    Get stock-specific parameters for major EGX stocks
    
    Args:
        symbol: Stock symbol (e.g., 'COMI', 'FWRY')
        
    Returns:
        dict: Stock-specific parameter adjustments
    """
    
    # Base parameters
    base_params = get_egx_parameters()
    
    # Stock-specific adjustments
    stock_adjustments = {
        'COMI': {
            'volatility_multiplier': 1.0,  # Standard volatility
            'volume_threshold': 1.2,
            'min_gap_size_pct': 0.001
        },
        'FWRY': {
            'volatility_multiplier': 1.3,  # Higher volatility
            'volume_threshold': 1.5,
            'min_gap_size_pct': 0.0015
        },
        'PHDC': {
            'volatility_multiplier': 1.1,
            'volume_threshold': 1.1,
            'min_gap_size_pct': 0.0012
        },
        'EFID': {
            'volatility_multiplier': 1.2,
            'volume_threshold': 1.3,
            'min_gap_size_pct': 0.0013
        },
        'UBEE': {
            'volatility_multiplier': 0.9,  # Lower volatility
            'volume_threshold': 1.0,
            'min_gap_size_pct': 0.0008
        },
        'GGRN': {
            'volatility_multiplier': 1.4,  # Higher volatility
            'volume_threshold': 1.6,
            'min_gap_size_pct': 0.0016
        },
        'OBRI': {
            'volatility_multiplier': 1.1,
            'volume_threshold': 1.2,
            'min_gap_size_pct': 0.0011
        },
        'UTOP': {
            'volatility_multiplier': 1.2,
            'volume_threshold': 1.4,
            'min_gap_size_pct': 0.0014
        }
    }
    
    # Apply stock-specific adjustments
    if symbol in stock_adjustments:
        adjustments = stock_adjustments[symbol]
        
        # Adjust FVG parameters
        base_params['fvg']['min_gap_size_pct'] = adjustments['min_gap_size_pct']
        
        # Adjust displacement parameters
        base_params['displacement']['volume_threshold'] = adjustments['volume_threshold']
        
        # Adjust order block parameters based on volatility
        vol_mult = adjustments['volatility_multiplier']
        base_params['order_blocks']['min_strength'] *= (1 / vol_mult)  # Lower threshold for higher volatility
        
        # Adjust distance filters based on volatility
        for key in base_params['distance_filters']:
            base_params['distance_filters'][key] *= vol_mult
    
    return base_params

def get_timeframe_parameters(timeframe: str):
    """
    Get timeframe-specific parameter adjustments
    
    Args:
        timeframe: Timeframe string (e.g., '1D', '4H', '1H')
        
    Returns:
        dict: Timeframe-specific adjustments
    """
    
    timeframe_adjustments = {
        '1D': {
            'lookback_multiplier': 1.0,
            'strength_threshold_multiplier': 1.0,
            'distance_multiplier': 1.0
        },
        '4H': {
            'lookback_multiplier': 1.2,
            'strength_threshold_multiplier': 0.9,
            'distance_multiplier': 0.8
        },
        '1H': {
            'lookback_multiplier': 1.5,
            'strength_threshold_multiplier': 0.8,
            'distance_multiplier': 0.6
        },
        '30M': {
            'lookback_multiplier': 2.0,
            'strength_threshold_multiplier': 0.7,
            'distance_multiplier': 0.5
        },
        '15M': {
            'lookback_multiplier': 2.5,
            'strength_threshold_multiplier': 0.6,
            'distance_multiplier': 0.4
        }
    }
    
    return timeframe_adjustments.get(timeframe, timeframe_adjustments['1D'])

def calculate_position_size(account_balance: float, risk_pct: float, stop_loss_distance: float, current_price: float) -> dict:
    """
    Calculate position size based on EGX risk management rules
    
    Args:
        account_balance: Total account balance in EGP
        risk_pct: Risk percentage (e.g., 2.0 for 2%)
        stop_loss_distance: Distance to stop loss in EGP
        current_price: Current stock price in EGP
        
    Returns:
        dict: Position sizing information
    """
    
    # Maximum risk amount
    max_risk_amount = account_balance * (risk_pct / 100)
    
    # Calculate position size
    if stop_loss_distance > 0:
        position_size = max_risk_amount / stop_loss_distance
        position_value = position_size * current_price
        
        # EGX minimum lot size (usually 1 share, but some stocks have minimum lots)
        min_lot_size = 1
        position_size = max(min_lot_size, round(position_size))
        
        return {
            'position_size': position_size,
            'position_value': position_value,
            'risk_amount': position_size * stop_loss_distance,
            'risk_percentage': (position_size * stop_loss_distance) / account_balance * 100,
            'max_risk_amount': max_risk_amount
        }
    else:
        return {
            'position_size': 0,
            'position_value': 0,
            'risk_amount': 0,
            'risk_percentage': 0,
            'max_risk_amount': max_risk_amount,
            'error': 'Invalid stop loss distance'
        }
