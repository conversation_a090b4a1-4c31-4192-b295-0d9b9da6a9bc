"""
Dashboard component for the AI Stocks Bot app
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import os
import logging
from typing import Dict, List, Optional, Union, Tuple, Any

# Import our new modules
from app.utils.technical_indicators import TechnicalIndicators
from app.utils.trading_strategies import StrategyFactory, TradingStrategy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def dashboard_component():
    """
    Streamlit component for displaying a dashboard overview
    """
    st.header("Interactive Dashboard")

    # Check if we have data loaded
    if 'historical_data' not in st.session_state or st.session_state.historical_data is None:
        st.warning("No data loaded. Please load a stock first.")
        return

    # Get data from session state
    historical_data = st.session_state.historical_data
    symbol = st.session_state.symbol

    # Add technical indicators to the data
    with st.spinner("Calculating technical indicators..."):
        try:
            df_with_indicators = TechnicalIndicators.add_all_indicators(historical_data)
            st.session_state.df_with_indicators = df_with_indicators
        except Exception as e:
            st.error(f"Error calculating indicators: {str(e)}")
            df_with_indicators = historical_data.copy()

    # Create tabs for different dashboard sections
    tab1, tab2, tab3, tab4 = st.tabs(["Overview", "Technical Analysis", "Trading Strategies", "Model Performance"])

    with tab1:
        # Create a layout with columns
        col1, col2 = st.columns([2, 1])

        with col1:
            st.subheader(f"{symbol} Overview")

            # Add time period selector
            time_periods = {
                "1W": 7,
                "1M": 30,
                "3M": 90,
                "6M": 180,
                "1Y": 365,
                "All": len(historical_data)
            }

            selected_period = st.select_slider(
                "Select Time Period",
                options=list(time_periods.keys()),
                value="3M"
            )

            # Filter data based on selected period
            days_to_show = time_periods[selected_period]
            display_data = historical_data.iloc[-min(days_to_show, len(historical_data)):].copy()

            # Create a price chart with enhanced interactivity
            fig = go.Figure()

            # Add price line
            fig.add_trace(go.Scatter(
                x=display_data['Date'],
                y=display_data['Close'],
                mode='lines',
                name='Close Price',
                line=dict(color='blue', width=2)
            ))

            # Add volume as a bar chart at the bottom with low opacity
            if 'Volume' in display_data.columns:
                # Create a secondary y-axis for volume
                fig.add_trace(go.Bar(
                    x=display_data['Date'],
                    y=display_data['Volume'],
                    name='Volume',
                    marker=dict(color='rgba(0,0,255,0.2)'),
                    yaxis='y2',
                    opacity=0.3
                ))

            # Add range selector and other interactive features
            fig.update_layout(
                title=f"{symbol} Price History ({selected_period})",
                xaxis=dict(
                    rangeselector=dict(
                        buttons=list([
                            dict(count=7, label="1w", step="day", stepmode="backward"),
                            dict(count=1, label="1m", step="month", stepmode="backward"),
                            dict(count=3, label="3m", step="month", stepmode="backward"),
                            dict(count=6, label="6m", step="month", stepmode="backward"),
                            dict(count=1, label="1y", step="year", stepmode="backward"),
                            dict(step="all")
                        ])
                    ),
                    rangeslider=dict(visible=True),
                    type="date"
                ),
                yaxis=dict(
                    title="Price",
                    side="left",
                    showgrid=True
                ),
                yaxis2=dict(
                    title="Volume",
                    overlaying="y",
                    side="right",
                    showgrid=False,
                    visible=False  # Hide the axis labels but keep the data
                ),
                hovermode="x unified",  # Show all data points at the same x-value
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                margin=dict(l=0, r=0, t=40, b=0),  # Tighter margins
                dragmode="zoom",  # Enable zoom by default
                selectdirection="h"  # Horizontal selection
            )

            # Add buttons for zoom and other tools
            fig.update_layout(
                updatemenus=[
                    dict(
                        type="buttons",
                        direction="left",
                        buttons=[
                            dict(label="Reset",
                                 method="relayout",
                                 args=["xaxis.range", None]),
                        ],
                        pad={"r": 10, "t": 10},
                        showactive=False,
                        x=0.11,
                        xanchor="left",
                        y=1.1,
                        yanchor="top"
                    ),
                ]
            )

            st.plotly_chart(fig, use_container_width=True, config={
                'scrollZoom': True,  # Enable scroll to zoom
                'displayModeBar': True,  # Always display the mode bar
                'modeBarButtonsToAdd': ['drawline', 'drawopenpath', 'eraseshape']  # Add drawing tools
            })

    with col2:
        st.subheader("Key Metrics")

        # Create a card-like container for metrics
        metrics_container = st.container()

        with metrics_container:
            # Add a border and background to make it look like a card
            st.markdown("""
            <style>
            div[data-testid="stMetricValue"] > div {font-size: 20px;}
            div[data-testid="stMetricDelta"] > div {font-size: 14px;}
            </style>
            """, unsafe_allow_html=True)

            # Calculate key metrics
            current_price = historical_data['Close'].iloc[-1]
            previous_price = historical_data['Close'].iloc[-2]
            price_change = current_price - previous_price
            price_change_pct = (price_change / previous_price) * 100

            # Display current price prominently
            st.metric("Current Price", f"{current_price:.2f}", f"{price_change_pct:.2f}%")

            # Add a divider
            st.markdown("<hr style='margin: 5px 0px; border: none; height: 1px; background-color: #e0e0e0;'>", unsafe_allow_html=True)

            # Create two columns for metrics
            col_a, col_b = st.columns(2)

            # Period changes
            with col_a:
                # 7-day metrics
                days_7 = min(7, len(historical_data))
                price_7d_ago = historical_data['Close'].iloc[-days_7]
                change_7d = current_price - price_7d_ago
                change_7d_pct = (change_7d / price_7d_ago) * 100
                st.metric("7-Day", f"{change_7d:.2f}", f"{change_7d_pct:.2f}%")

                # 30-day metrics
                days_30 = min(30, len(historical_data))
                price_30d_ago = historical_data['Close'].iloc[-days_30]
                change_30d = current_price - price_30d_ago
                change_30d_pct = (change_30d / price_30d_ago) * 100
                st.metric("30-Day", f"{change_30d:.2f}", f"{change_30d_pct:.2f}%")

            with col_b:
                # 90-day metrics
                days_90 = min(90, len(historical_data))
                if days_90 > 30:  # Only show if we have enough data
                    price_90d_ago = historical_data['Close'].iloc[-days_90]
                    change_90d = current_price - price_90d_ago
                    change_90d_pct = (change_90d / price_90d_ago) * 100
                    st.metric("90-Day", f"{change_90d:.2f}", f"{change_90d_pct:.2f}%")

                # Year-to-date or 180-day metrics
                days_180 = min(180, len(historical_data))
                if days_180 > 90:  # Only show if we have enough data
                    price_180d_ago = historical_data['Close'].iloc[-days_180]
                    change_180d = current_price - price_180d_ago
                    change_180d_pct = (change_180d / price_180d_ago) * 100
                    st.metric("180-Day", f"{change_180d:.2f}", f"{change_180d_pct:.2f}%")

            # Add a divider
            st.markdown("<hr style='margin: 5px 0px; border: none; height: 1px; background-color: #e0e0e0;'>", unsafe_allow_html=True)

            # Technical metrics
            col_c, col_d = st.columns(2)

            with col_c:
                # Volume metrics
                if 'Volume' in historical_data.columns:
                    avg_volume = historical_data['Volume'].mean()
                    last_volume = historical_data['Volume'].iloc[-1]
                    volume_change = (last_volume / avg_volume - 1) * 100
                    st.metric("Volume", f"{last_volume:,.0f}", f"{volume_change:.2f}%")

                # Volatility (standard deviation of returns)
                returns = historical_data['Close'].pct_change().dropna()
                volatility = returns.std() * 100
                st.metric("Volatility", f"{volatility:.2f}%")

            with col_d:
                # Calculate high/low range
                if 'High' in historical_data.columns and 'Low' in historical_data.columns:
                    # Daily range
                    daily_high = historical_data['High'].iloc[-1]
                    daily_low = historical_data['Low'].iloc[-1]
                    daily_range_pct = ((daily_high - daily_low) / daily_low) * 100
                    st.metric("Day Range", f"{daily_low:.2f}-{daily_high:.2f}", f"{daily_range_pct:.2f}%")

                # Calculate 52-week high/low
                days_year = min(252, len(historical_data))  # Approx trading days in a year
                if days_year > 30:  # Only show if we have enough data
                    year_high = historical_data['Close'].iloc[-days_year:].max()
                    year_low = historical_data['Close'].iloc[-days_year:].min()
                    from_low_pct = ((current_price - year_low) / year_low) * 100
                    st.metric("52W Range", f"{year_low:.2f}-{year_high:.2f}", f"{from_low_pct:.2f}% from low")

            # Add a divider
            st.markdown("<hr style='margin: 5px 0px; border: none; height: 1px; background-color: #e0e0e0;'>", unsafe_allow_html=True)

            # Add a small summary section
            st.markdown(f"**Last updated:** {historical_data['Date'].iloc[-1].strftime('%Y-%m-%d')}")

    with tab2:
        st.subheader("Technical Analysis")

        # Add time period selector for technical analysis
        ta_time_periods = {
            "1W": 7,
            "2W": 14,
            "1M": 30,
            "3M": 90,
            "6M": 180,
            "1Y": 365,
        }

        ta_selected_period = st.select_slider(
            "Select Analysis Period",
            options=list(ta_time_periods.keys()),
            value="1M"
        )

        # Filter data based on selected period
        ta_days_to_show = ta_time_periods[ta_selected_period]
        ta_display_data = historical_data.iloc[-min(ta_days_to_show, len(historical_data)):].copy()

        # Create tabs for different technical analysis views
        ta_tab1, ta_tab2, ta_tab3, ta_tab4 = st.tabs(["Price Trends", "Volume Analysis", "Moving Averages", "Indicators"])

        with ta_tab1:
            # Price trends chart
            fig = go.Figure()

            # Add candlestick chart
            fig.add_trace(go.Candlestick(
                x=ta_display_data['Date'],
                open=ta_display_data['Open'],
                high=ta_display_data['High'],
                low=ta_display_data['Low'],
                close=ta_display_data['Close'],
                name="OHLC"
            ))

            # Calculate and add a trend line (simple moving average)
            ta_display_data['SMA10'] = ta_display_data['Close'].rolling(window=10).mean()
            fig.add_trace(go.Scatter(
                x=ta_display_data['Date'],
                y=ta_display_data['SMA10'],
                name="10-Day MA",
                line=dict(color='rgba(255, 165, 0, 0.7)', width=2)
            ))

            # Update layout with enhanced interactivity
            fig.update_layout(
                title=f"{symbol} Price Trends ({ta_selected_period})",
                xaxis_title="Date",
                yaxis_title="Price",
                xaxis_rangeslider_visible=False,  # Hide the range slider for cleaner look
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(l=0, r=0, t=40, b=0),  # Tighter margins
                hovermode="x unified",  # Show all data points at the same x-value
                dragmode="zoom"  # Enable zoom by default
            )

            st.plotly_chart(fig, use_container_width=True, config={
                'scrollZoom': True,  # Enable scroll to zoom
                'displayModeBar': True,  # Always display the mode bar
                'modeBarButtonsToAdd': ['drawline', 'drawopenpath', 'eraseshape']  # Add drawing tools
            })

        with ta_tab2:
            # Volume analysis chart
            fig = go.Figure()

            # Add volume bars
            fig.add_trace(go.Bar(
                x=ta_display_data['Date'],
                y=ta_display_data['Volume'],
                name="Volume",
                marker=dict(color='rgba(0, 0, 255, 0.5)')
            ))

            # Calculate and add volume moving average
            ta_display_data['Volume_MA10'] = ta_display_data['Volume'].rolling(window=10).mean()
            fig.add_trace(go.Scatter(
                x=ta_display_data['Date'],
                y=ta_display_data['Volume_MA10'],
                name="10-Day Volume MA",
                line=dict(color='rgba(255, 0, 0, 0.7)', width=2)
            ))

            # Add price line on secondary y-axis
            fig.add_trace(go.Scatter(
                x=ta_display_data['Date'],
                y=ta_display_data['Close'],
                name="Close Price",
                yaxis="y2",
                line=dict(color='rgba(0, 128, 0, 0.7)', width=2)
            ))

            # Update layout with secondary y-axis and enhanced interactivity
            fig.update_layout(
                title=f"{symbol} Volume Analysis ({ta_selected_period})",
                xaxis_title="Date",
                yaxis_title="Volume",
                yaxis2=dict(
                    title="Price",
                    overlaying="y",
                    side="right"
                ),
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(l=0, r=0, t=40, b=0),  # Tighter margins
                hovermode="x unified",  # Show all data points at the same x-value
                dragmode="zoom"  # Enable zoom by default
            )

            st.plotly_chart(fig, use_container_width=True, config={
                'scrollZoom': True,  # Enable scroll to zoom
                'displayModeBar': True  # Always display the mode bar
            })

        with ta_tab3:
            # Let user select which moving averages to display
            ma_periods = [5, 10, 20, 50, 100, 200]
            selected_mas = st.multiselect(
                "Select Moving Averages",
                options=ma_periods,
                default=[5, 20, 50],
                format_func=lambda x: f"{x}-Day MA"
            )

            # Calculate moving averages
            ta_data_ma = ta_display_data.copy()

            # Calculate all selected MAs
            for period in selected_mas:
                ta_data_ma[f'MA{period}'] = ta_data_ma['Close'].rolling(window=period).mean()

            # Moving averages chart
            fig = go.Figure()

            # Add price line
            fig.add_trace(go.Scatter(
                x=ta_data_ma['Date'],
                y=ta_data_ma['Close'],
                name="Close Price",
                line=dict(color='black', width=2)
            ))

            # Add moving averages with different colors
            colors = ['red', 'blue', 'green', 'purple', 'orange', 'brown']
            for i, period in enumerate(selected_mas):
                color = colors[i % len(colors)]
                fig.add_trace(go.Scatter(
                    x=ta_data_ma['Date'],
                    y=ta_data_ma[f'MA{period}'],
                    name=f"{period}-Day MA",
                    line=dict(color=color, width=2)
                ))

            # Update layout with enhanced interactivity
            fig.update_layout(
                title=f"{symbol} Moving Averages ({ta_selected_period})",
                xaxis_title="Date",
                yaxis_title="Price",
                legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
                margin=dict(l=0, r=0, t=40, b=0),  # Tighter margins
                hovermode="x unified",  # Show all data points at the same x-value
                dragmode="zoom"  # Enable zoom by default
            )

            st.plotly_chart(fig, use_container_width=True, config={
                'scrollZoom': True,  # Enable scroll to zoom
                'displayModeBar': True  # Always display the mode bar
            })

            # Add MA crossover analysis if at least 2 MAs are selected
            if len(selected_mas) >= 2:
                st.subheader("Moving Average Crossover Analysis")

                # Sort periods for analysis
                analysis_periods = sorted(selected_mas)

                # Create columns for displaying crossover information
                crossover_cols = st.columns(len(analysis_periods)-1)

                # Analyze each pair of consecutive MAs
                for i in range(len(analysis_periods)-1):
                    fast_period = analysis_periods[i]
                    slow_period = analysis_periods[i+1]

                    # Get the last values
                    fast_ma = ta_data_ma[f'MA{fast_period}'].iloc[-1]
                    slow_ma = ta_data_ma[f'MA{slow_period}'].iloc[-1]

                    # Determine if there's a crossover
                    if fast_ma > slow_ma:
                        signal = "Bullish"
                        color = "green"
                    else:
                        signal = "Bearish"
                        color = "red"

                    # Calculate the difference
                    diff_pct = ((fast_ma - slow_ma) / slow_ma) * 100

                    # Display in the appropriate column
                    with crossover_cols[i]:
                        st.markdown(f"<h5 style='text-align: center;'>{fast_period} vs {slow_period} Day</h5>", unsafe_allow_html=True)
                        st.markdown(f"<p style='text-align: center; color: {color}; font-weight: bold;'>{signal}</p>", unsafe_allow_html=True)
                        st.markdown(f"<p style='text-align: center;'>Difference: {diff_pct:.2f}%</p>", unsafe_allow_html=True)

        # Add a new tab for technical indicators
        with ta_tab4:
            st.subheader("Technical Indicators")

            # Let user select indicators to display
            indicator_options = [
                "RSI", "MACD", "Bollinger Bands", "Stochastic Oscillator",
                "Moving Averages", "Volume Indicators"
            ]

            selected_indicators = st.multiselect(
                "Select indicators to display",
                options=indicator_options,
                default=["RSI", "MACD"]
            )

            if not selected_indicators:
                st.info("Please select at least one indicator to display")
            else:
                # Create a figure for each selected indicator
                for indicator in selected_indicators:
                    if indicator == "RSI":
                        # RSI Chart
                        if f'RSI_14' in df_with_indicators.columns:
                            fig = go.Figure()

                            # Add price subplot
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Close'][-90:],
                                name="Price",
                                yaxis="y1"
                            ))

                            # Add RSI subplot
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['RSI_14'][-90:],
                                name="RSI (14)",
                                yaxis="y2"
                            ))

                            # Add overbought/oversold lines
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=[70] * len(df_with_indicators[-90:]),
                                name="Overbought (70)",
                                line=dict(color='red', width=1, dash='dash'),
                                yaxis="y2"
                            ))

                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=[30] * len(df_with_indicators[-90:]),
                                name="Oversold (30)",
                                line=dict(color='green', width=1, dash='dash'),
                                yaxis="y2"
                            ))

                            # Update layout with secondary y-axis
                            fig.update_layout(
                                title="Relative Strength Index (RSI)",
                                xaxis_title="Date",
                                yaxis=dict(
                                    title="Price",
                                    domain=[0.6, 1]
                                ),
                                yaxis2=dict(
                                    title="RSI",
                                    domain=[0, 0.5],
                                    range=[0, 100]
                                ),
                                height=600
                            )

                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.warning("RSI indicator not available in the data")

                    elif indicator == "MACD":
                        # MACD Chart
                        if 'MACD_Line' in df_with_indicators.columns and 'MACD_Signal' in df_with_indicators.columns:
                            fig = go.Figure()

                            # Add price subplot
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Close'][-90:],
                                name="Price",
                                yaxis="y1"
                            ))

                            # Add MACD line
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['MACD_Line'][-90:],
                                name="MACD Line",
                                yaxis="y2"
                            ))

                            # Add MACD signal line
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['MACD_Signal'][-90:],
                                name="Signal Line",
                                yaxis="y2"
                            ))

                            # Add MACD histogram
                            fig.add_trace(go.Bar(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['MACD_Histogram'][-90:],
                                name="MACD Histogram",
                                marker_color=np.where(df_with_indicators['MACD_Histogram'][-90:] >= 0, 'green', 'red'),
                                yaxis="y2"
                            ))

                            # Update layout with secondary y-axis
                            fig.update_layout(
                                title="Moving Average Convergence Divergence (MACD)",
                                xaxis_title="Date",
                                yaxis=dict(
                                    title="Price",
                                    domain=[0.6, 1]
                                ),
                                yaxis2=dict(
                                    title="MACD",
                                    domain=[0, 0.5]
                                ),
                                height=600
                            )

                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.warning("MACD indicator not available in the data")

                    elif indicator == "Bollinger Bands":
                        # Bollinger Bands Chart
                        if 'BB_Upper_20' in df_with_indicators.columns and 'BB_Lower_20' in df_with_indicators.columns:
                            fig = go.Figure()

                            # Add price line
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Close'][-90:],
                                name="Close Price",
                                line=dict(color='blue')
                            ))

                            # Add Bollinger Bands
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['BB_Upper_20'][-90:],
                                name="Upper Band",
                                line=dict(color='red', width=1)
                            ))

                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['BB_Middle_20'][-90:],
                                name="Middle Band",
                                line=dict(color='orange', width=1)
                            ))

                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['BB_Lower_20'][-90:],
                                name="Lower Band",
                                line=dict(color='green', width=1)
                            ))

                            # Update layout
                            fig.update_layout(
                                title="Bollinger Bands (20, 2)",
                                xaxis_title="Date",
                                yaxis_title="Price"
                            )

                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.warning("Bollinger Bands indicator not available in the data")

                    elif indicator == "Stochastic Oscillator":
                        # Stochastic Oscillator Chart
                        if 'Stoch_%K' in df_with_indicators.columns and 'Stoch_%D' in df_with_indicators.columns:
                            fig = go.Figure()

                            # Add price subplot
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Close'][-90:],
                                name="Price",
                                yaxis="y1"
                            ))

                            # Add Stochastic lines
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Stoch_%K'][-90:],
                                name="%K",
                                yaxis="y2"
                            ))

                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Stoch_%D'][-90:],
                                name="%D",
                                yaxis="y2"
                            ))

                            # Add overbought/oversold lines
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=[80] * len(df_with_indicators[-90:]),
                                name="Overbought (80)",
                                line=dict(color='red', width=1, dash='dash'),
                                yaxis="y2"
                            ))

                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=[20] * len(df_with_indicators[-90:]),
                                name="Oversold (20)",
                                line=dict(color='green', width=1, dash='dash'),
                                yaxis="y2"
                            ))

                            # Update layout with secondary y-axis
                            fig.update_layout(
                                title="Stochastic Oscillator",
                                xaxis_title="Date",
                                yaxis=dict(
                                    title="Price",
                                    domain=[0.6, 1]
                                ),
                                yaxis2=dict(
                                    title="Stochastic",
                                    domain=[0, 0.5],
                                    range=[0, 100]
                                ),
                                height=600
                            )

                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.warning("Stochastic Oscillator indicator not available in the data")

                    elif indicator == "Moving Averages":
                        # Moving Averages Chart
                        fig = go.Figure()

                        # Add price line
                        fig.add_trace(go.Scatter(
                            x=df_with_indicators['Date'][-90:],
                            y=df_with_indicators['Close'][-90:],
                            name="Close Price",
                            line=dict(color='blue')
                        ))

                        # Add moving averages if available
                        ma_columns = [col for col in df_with_indicators.columns if col.startswith('SMA_') or col.startswith('EMA_')]

                        if ma_columns:
                            # Select a subset of MAs to avoid cluttering the chart
                            selected_mas = []
                            for period in [5, 10, 20, 50, 200]:
                                sma_col = f'SMA_{period}'
                                ema_col = f'EMA_{period}'

                                if sma_col in ma_columns:
                                    selected_mas.append(sma_col)
                                if ema_col in ma_columns:
                                    selected_mas.append(ema_col)

                            # Add selected MAs to chart
                            for ma in selected_mas:
                                fig.add_trace(go.Scatter(
                                    x=df_with_indicators['Date'][-90:],
                                    y=df_with_indicators[ma][-90:],
                                    name=ma
                                ))

                            # Update layout
                            fig.update_layout(
                                title="Moving Averages",
                                xaxis_title="Date",
                                yaxis_title="Price"
                            )

                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.warning("Moving Average indicators not available in the data")

                    elif indicator == "Volume Indicators":
                        # Volume Indicators Chart
                        if 'Volume' in df_with_indicators.columns:
                            fig = go.Figure()

                            # Add price subplot
                            fig.add_trace(go.Scatter(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Close'][-90:],
                                name="Price",
                                yaxis="y1"
                            ))

                            # Add volume bars
                            fig.add_trace(go.Bar(
                                x=df_with_indicators['Date'][-90:],
                                y=df_with_indicators['Volume'][-90:],
                                name="Volume",
                                marker_color='rgba(0,0,255,0.3)',
                                yaxis="y2"
                            ))

                            # Add OBV if available
                            if 'OBV' in df_with_indicators.columns:
                                # Normalize OBV to fit on the same scale as volume
                                obv = df_with_indicators['OBV'][-90:]
                                obv_norm = (obv - obv.min()) / (obv.max() - obv.min()) * df_with_indicators['Volume'][-90:].max()

                                fig.add_trace(go.Scatter(
                                    x=df_with_indicators['Date'][-90:],
                                    y=obv_norm,
                                    name="On-Balance Volume (normalized)",
                                    yaxis="y2"
                                ))

                            # Update layout with secondary y-axis
                            fig.update_layout(
                                title="Volume Analysis",
                                xaxis_title="Date",
                                yaxis=dict(
                                    title="Price",
                                    domain=[0.6, 1]
                                ),
                                yaxis2=dict(
                                    title="Volume",
                                    domain=[0, 0.5]
                                ),
                                height=600
                            )

                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.warning("Volume data not available")

    with tab3:
        st.subheader("Trading Strategies Backtesting")

        # Let user select a trading strategy
        available_strategies = StrategyFactory.get_available_strategies()

        strategy_type = st.selectbox(
            "Select a trading strategy",
            options=list(available_strategies.keys()),
            format_func=lambda x: available_strategies[x]
        )

        # Show strategy description
        st.info(StrategyFactory.get_strategy_description(strategy_type))

        # Get default parameters for the selected strategy
        default_params = StrategyFactory.get_strategy_parameters(strategy_type)

        # Create parameter inputs based on strategy type
        st.subheader("Strategy Parameters")

        params = {}
        col1, col2 = st.columns(2)

        if strategy_type == 'ma_crossover':
            with col1:
                params['fast_period'] = st.slider("Fast Period", 1, 50, default_params['fast_period'])
            with col2:
                params['slow_period'] = st.slider("Slow Period", 10, 200, default_params['slow_period'])

        elif strategy_type == 'rsi':
            with col1:
                params['period'] = st.slider("RSI Period", 1, 30, default_params['period'])
            with col2:
                params['overbought'] = st.slider("Overbought Level", 50, 90, default_params['overbought'])
                params['oversold'] = st.slider("Oversold Level", 10, 50, default_params['oversold'])

        elif strategy_type == 'macd':
            with col1:
                params['fast_period'] = st.slider("Fast Period", 1, 50, default_params['fast_period'])
                params['slow_period'] = st.slider("Slow Period", 10, 100, default_params['slow_period'])
            with col2:
                params['signal_period'] = st.slider("Signal Period", 1, 50, default_params['signal_period'])

        elif strategy_type == 'bollinger_bands':
            with col1:
                params['period'] = st.slider("Period", 5, 50, default_params['period'])
            with col2:
                params['std_dev'] = st.slider("Standard Deviations", 1.0, 3.0, default_params['std_dev'], 0.1)

        # Backtest parameters
        st.subheader("Backtest Parameters")

        col1, col2, col3 = st.columns(3)

        with col1:
            initial_capital = st.number_input("Initial Capital", min_value=1000.0, value=10000.0, step=1000.0)

        with col2:
            position_size = st.slider("Position Size (%)", 10, 100, 100, 10) / 100.0

        with col3:
            commission = st.number_input("Commission per Trade", min_value=0.0, value=0.0, step=1.0)

        # Run backtest button
        if st.button("Run Backtest"):
            with st.spinner("Running backtest..."):
                try:
                    # Create strategy instance
                    strategy = StrategyFactory.get_strategy(strategy_type, **params)

                    # Run backtest
                    results = strategy.backtest(
                        df_with_indicators,
                        initial_capital=initial_capital,
                        position_size=position_size,
                        commission=commission
                    )

                    # Display results
                    st.success("Backtest completed successfully!")

                    # Save strategy results to session state for reporting
                    st.session_state.strategy_results = {
                        'strategy_name': strategy.name,
                        'metrics': results['metrics'],
                        'trades': results['trades'],
                        'chart_data': results['data']
                    }

                    # Display metrics
                    metrics = results['metrics']

                    # Create metrics display
                    col1, col2 = st.columns(2)

                    with col1:
                        st.metric("Strategy Return", f"{metrics['strategy_return']:.2f}%",
                                 f"{metrics['strategy_return'] - metrics['market_return']:.2f}%")
                        st.metric("Total Trades", f"{metrics['total_trades']}")
                        st.metric("Win Rate", f"{metrics['win_rate']:.2f}%")

                    with col2:
                        st.metric("Buy & Hold Return", f"{metrics['market_return']:.2f}%")
                        st.metric("Winning Trades", f"{metrics['winning_trades']}")
                        st.metric("Max Drawdown", f"{metrics['strategy_max_drawdown']:.2f}%")

                    # Plot backtest results
                    fig = strategy.plot_backtest_results(results)
                    st.plotly_chart(fig, use_container_width=True)

                    # Display trades table if there are any trades
                    if len(results['trades']) > 0:
                        st.subheader("Trade Log")
                        st.dataframe(results['trades'])
                    else:
                        st.info("No trades were executed during the backtest period.")

                except Exception as e:
                    st.error(f"Error running backtest: {str(e)}")

    with tab4:
        st.subheader("Model Training Status")

    # Check if models are trained
    models_path = 'saved_models'
    if not os.path.exists(models_path):
        st.warning("No models have been trained yet.")
    else:
        # Get list of model files (both .pkl scalers and .joblib models)
        model_files = [f for f in os.listdir(models_path) if (f.endswith('.pkl') or f.endswith('.joblib')) and symbol in f]

        if not model_files:
            st.warning(f"No trained models found for {symbol}.")
        else:
            # Extract horizons from filenames
            horizons = set()  # Use a set to avoid duplicates

            for file in model_files:
                # Handle different file formats
                if file.endswith('.pkl'):  # Scaler files like COMI_30_scaler.pkl
                    parts = file.split('_')
                    if len(parts) >= 2 and 'scaler' in parts[-1]:
                        try:
                            horizon = int(parts[-2])
                            horizons.add(horizon)
                        except ValueError:
                            pass
                elif file.endswith('.joblib'):  # Model files like COMI_rf_30min.joblib or COMI_ensemble_30min.joblib
                    if 'min.joblib' in file:
                        try:
                            # Extract the number before 'min.joblib'
                            horizon_str = file.split('min.joblib')[0].split('_')[-1]
                            horizon = int(horizon_str)
                            horizons.add(horizon)
                        except (ValueError, IndexError):
                            pass

            # Convert set to sorted list
            horizons = sorted(list(horizons))

            if horizons:
                st.success(f"Found {len(horizons)} trained models for {symbol}.")

                # Count model types for each horizon
                model_counts = {}
                model_types = set()

                for horizon in horizons:
                    model_counts[horizon] = {}

                    # Count models for this horizon
                    for file in model_files:
                        if f"_{horizon}min.joblib" in file or f"_{horizon}_scaler.pkl" in file:
                            # Extract model type
                            if '_rf_' in file or file.startswith(f"{symbol}_rf_"):
                                model_type = 'Random Forest'
                            elif '_gb_' in file or file.startswith(f"{symbol}_gb_"):
                                model_type = 'Gradient Boosting'
                            elif '_lr_' in file or file.startswith(f"{symbol}_lr_"):
                                model_type = 'Linear Regression'
                            elif '_svr_' in file or file.startswith(f"{symbol}_svr_"):
                                model_type = 'SVR'
                            elif '_xgb_' in file or file.startswith(f"{symbol}_xgb_"):
                                model_type = 'XGBoost'
                            elif 'hybrid' in file:
                                model_type = 'Hybrid'
                            elif 'ensemble' in file and not ('base' in file or 'weights' in file):
                                model_type = 'Ensemble'
                            elif 'enhanced_ensemble' in file:
                                model_type = 'Enhanced Ensemble'
                            elif 'scaler' in file:
                                model_type = 'Scaler'
                            else:
                                continue  # Skip files we can't categorize

                            model_types.add(model_type)
                            model_counts[horizon][model_type] = model_counts[horizon].get(model_type, 0) + 1

                # Create a more detailed DataFrame
                data = []
                for horizon in sorted(horizons):
                    row = {'Horizon': horizon}
                    for model_type in sorted(model_types):
                        row[model_type] = '✓' if model_type in model_counts[horizon] else ''
                    data.append(row)

                # Create DataFrame
                horizon_df = pd.DataFrame(data)

                # Display as a styled table
                st.dataframe(horizon_df, use_container_width=True)

                # Add model performance visualization if we have enough data
                if len(horizons) >= 2:
                    st.subheader("Model Performance by Horizon")

                    # Get performance metrics (this is a placeholder - in a real app, you'd load actual metrics)
                    metrics = get_model_performance_metrics(symbol)

                    # Create performance visualization
                    performance_fig = go.Figure()

                    # Add MSE trace
                    performance_fig.add_trace(go.Bar(
                        x=metrics['horizons'],
                        y=metrics['mse'],
                        name='MSE',
                        marker_color='indianred'
                    ))

                    # Add R² trace on secondary y-axis
                    performance_fig.add_trace(go.Scatter(
                        x=metrics['horizons'],
                        y=metrics['r2'],
                        name='R²',
                        marker_color='royalblue',
                        yaxis='y2'
                    ))

                    # Update layout
                    performance_fig.update_layout(
                        title=f"Model Performance Metrics for {symbol}",
                        xaxis_title="Prediction Horizon",
                        yaxis_title="Mean Squared Error",
                        yaxis2=dict(
                            title="R² Score",
                            overlaying="y",
                            side="right",
                            range=[0, 1]
                        ),
                        legend=dict(
                            orientation="h",
                            yanchor="bottom",
                            y=1.02,
                            xanchor="right",
                            x=1
                        )
                    )

                    # Display the figure
                    st.plotly_chart(performance_fig, use_container_width=True)

                    # Add a note about the metrics
                    st.info("Note: These are sample performance metrics. In a production app, these would be calculated from actual model evaluation results.")
            else:
                st.warning(f"Could not determine horizons for trained models for {symbol}.")

def get_model_performance_metrics(symbol):
    """
    Get performance metrics for trained models

    Args:
        symbol (str): Stock symbol

    Returns:
        dict: Dictionary with performance metrics
    """
    # Get available horizons
    models_path = 'saved_models'
    horizons = []

    if os.path.exists(models_path):
        # Get list of model files
        model_files = [f for f in os.listdir(models_path) if (f.endswith('.pkl') or f.endswith('.joblib')) and symbol in f]

        # Extract horizons
        for file in model_files:
            # Handle different file formats
            if file.endswith('.pkl'):  # Scaler files
                parts = file.split('_')
                if len(parts) >= 2 and 'scaler' in parts[-1]:
                    try:
                        horizon = int(parts[-2])
                        if horizon not in horizons:
                            horizons.append(horizon)
                    except ValueError:
                        pass
            elif file.endswith('.joblib'):  # Model files
                if 'min.joblib' in file:
                    try:
                        # Extract the number before 'min.joblib'
                        horizon_str = file.split('min.joblib')[0].split('_')[-1]
                        horizon = int(horizon_str)
                        if horizon not in horizons:
                            horizons.append(horizon)
                    except (ValueError, IndexError):
                        pass

    # Sort horizons
    horizons = sorted(horizons)

    # If no horizons found, use some defaults
    if not horizons:
        horizons = [1, 5, 30, 60]

    # Generate random metrics for each horizon
    # In a real implementation, you would load actual metrics from saved files
    num_horizons = len(horizons)

    # Generate metrics that look somewhat realistic
    # MSE typically increases with horizon length
    mse_base = np.random.uniform(0.001, 0.01)
    mse = [mse_base * (1 + h/10) for h in horizons]

    # MAE follows a similar pattern
    mae_base = np.random.uniform(0.01, 0.1)
    mae = [mae_base * (1 + h/10) for h in horizons]

    # R² typically decreases with horizon length
    r2_base = np.random.uniform(0.8, 0.95)
    r2 = [max(0.5, r2_base * (1 - h/100)) for h in horizons]

    return {
        'mse': mse,
        'mae': mae,
        'r2': r2,
        'horizons': horizons
    }
