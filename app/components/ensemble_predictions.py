import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
from typing import Optional, Dict
import os
import sys
import logging

# Import performance metrics tracking
try:
    from app.components.performance_metrics import track_prediction
except ImportError:
    # Define a dummy function if the module is not available
    def track_prediction(symbol, predictions, horizon_unit, model_type):
        pass

# Add the project root directory to the Python path if not already added
if os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')) not in sys.path:
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from models.predict import predict_future_prices, predict_from_live_data
from app.models.model_factory import ModelFactory
# Import EnsembleModel class for static methods only
from app.models.ensemble_model import EnsembleModel
from scrapers.price_scraper import PriceScraper
from app.utils.data_processing import is_model_trained

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_api_server_status():
    """Check if TradingView API server is running"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        return response.status_code == 200
    except:
        return False

def fetch_price_from_api_server(symbol: str) -> Optional[Dict]:
    """Fetch price data from TradingView API server"""
    try:
        import requests
        # Format symbol for EGX
        egx_symbol = f"EGX-{symbol}"

        payload = {
            "pairs": [egx_symbol],
            "intervals": ["1D"]
        }

        response = requests.post(
            "http://127.0.0.1:8000/api/scrape_pairs",
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            stock_data = data.get(egx_symbol, [])

            if stock_data:
                # Extract price from the API response
                raw_price = stock_data[0].get('price', 0)

                # Convert from piasters to EGP (divide by 1000)
                # API returns prices in piasters (81500 = 81.50 EGP)
                price = raw_price / 1000.0 if raw_price > 1000 else raw_price

                return {
                    'symbol': symbol,
                    'price': price,
                    'currency': 'EGP',
                    'timestamp': datetime.now().isoformat(),
                    'source': 'TradingView API',
                    'real_time': True,
                    'api_data': stock_data[0]  # Store full API data for advanced analysis
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching from API server: {str(e)}")
        return None

def safe_get_live_data_ensemble(symbol: str) -> Optional[pd.DataFrame]:
    """Safely fetch live data with comprehensive error handling and API server integration"""
    try:
        from scrapers.price_scraper import PriceScraper
        from datetime import datetime

        with st.spinner("Fetching latest price data..."):
            # Check if API server is available
            api_status = check_api_server_status()

            if api_status:
                st.info("🔥 Using TradingView API server for enhanced data")
                price_data = fetch_price_from_api_server(symbol)
                source_used = "TradingView API"
            else:
                st.info("📡 Using direct TradingView scraping")
                scraper = PriceScraper(source="tradingview")
                try:
                    price_data = scraper.get_price(symbol)
                    source_used = "TradingView Direct"
                finally:
                    scraper.close_driver()

            if price_data and isinstance(price_data, dict):
                # Convert scraper data format to prediction-compatible format
                current_time = datetime.now()
                price = price_data.get('price', 0)

                # Create DataFrame with required columns for predictions
                converted_data = {
                    'Date': current_time,
                    'Open': price,
                    'High': price * 1.001,  # Simulate small high variation
                    'Low': price * 0.999,   # Simulate small low variation
                    'Close': price,         # Main price for predictions
                    'Volume': 1000000,      # Default volume
                    'symbol': price_data.get('symbol', symbol),
                    'currency': price_data.get('currency', 'EGP'),
                    'timestamp': price_data.get('timestamp', current_time.isoformat()),
                    'source': source_used,
                    'real_time': price_data.get('real_time', False)
                }

                df = pd.DataFrame([converted_data])

                # Ensure Date is datetime
                df['Date'] = pd.to_datetime(df['Date'])

                # Check if this is sample data
                is_sample = price_data.get('source', '').lower() == 'sample data'
                is_real_time = price_data.get('real_time', False)
                is_api = source_used == "TradingView API"

                if is_sample:
                    st.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                elif is_api:
                    st.success(f"✅ Price fetched from TradingView API: {price:.2f} EGP")
                    st.info("🔥 Enhanced data with technical analysis available")
                elif is_real_time:
                    st.success(f"🔴 Real-time price fetched: {price:.2f} EGP")
                else:
                    st.success(f"⏱️ Live price fetched: {price:.2f} EGP (15-min delay)")

                # Store in session state
                if 'live_data' not in st.session_state:
                    st.session_state.live_data = df
                else:
                    st.session_state.live_data = pd.concat([st.session_state.live_data, df], ignore_index=True)

                return df
            else:
                st.warning("⚠️ Could not fetch live price data")
                return None

    except Exception as e:
        st.warning(f"⚠️ Error fetching live data: {str(e)}")
        logger.error(f"Live data fetch error: {str(e)}")
        return None

def ensemble_predictions_component(historical_data, live_data, symbol):
    """
    Enhanced Ensemble Predictions component for Streamlit

    Args:
        historical_data (pd.DataFrame): DataFrame with historical data
        live_data (pd.DataFrame): DataFrame with live data
        symbol (str): Stock symbol
    """
    # Apply comprehensive NumPy compatibility fix
    try:
        # Import and apply the comprehensive NumPy compatibility fix
        from app.utils.numpy_compatibility import apply_comprehensive_numpy_fix
        apply_comprehensive_numpy_fix()
        logger.info("Applied comprehensive NumPy compatibility fix before ensemble predictions")
    except Exception as fix_error:
        logger.warning(f"Error applying comprehensive NumPy compatibility fix: {str(fix_error)}")

        # Try the original fixes as fallback
        try:
            # Import and apply the NumPy MT19937 fix
            from app.utils.numpy_fix_mt19937 import fix_numpy_mt19937
            fix_numpy_mt19937()
            logger.info("Applied NumPy MT19937 fix before ensemble predictions")
        except Exception as mt_error:
            logger.warning(f"Error applying NumPy MT19937 fix: {str(mt_error)}")

        try:
            from app.utils.numpy_bitgenerator_fix import fix_numpy_bitgenerator
            fix_numpy_bitgenerator()
            logger.info("Applied NumPy BitGenerator fix before ensemble predictions")
        except Exception as bg_error:
            logger.warning(f"Error applying NumPy BitGenerator fix: {str(bg_error)}")

    # Force garbage collection to clean up any memory issues
    try:
        import gc
        gc.collect()
        logger.info("Forced garbage collection before ensemble predictions")
    except Exception as gc_error:
        logger.warning(f"Error during garbage collection: {str(gc_error)}")

    st.title("Enhanced Ensemble Predictions")

    st.markdown("""
    This page uses advanced ensemble methods to combine multiple prediction models for more robust forecasts.

    ### Features:
    - Multiple ensemble techniques (simple average, weighted average, performance-based, time-based, stacking)
    - Confidence intervals and uncertainty estimates
    - Model performance comparison
    - Visualization of ensemble predictions vs. individual models
    """)

    if historical_data is None:
        st.warning("Please upload historical data first")
        return

    if symbol is None or symbol == "":
        st.warning("Please provide a stock symbol")
        return

    # Create tabs for different sections
    tabs = st.tabs(["Ensemble Configuration", "Predictions", "Model Comparison", "Performance Analysis"])

    with tabs[0]:
        # Ensemble Configuration Tab
        st.subheader("Ensemble Configuration")

        # Model selection
        available_models = ModelFactory.get_available_models()

        # Let user select models to include in ensemble
        selected_models = st.multiselect(
            "Select models to include in ensemble",
            options=list(available_models.keys()),
            default=list(available_models.keys())[:3] if len(available_models) >= 3 else list(available_models.keys()),
            format_func=lambda x: available_models[x],
            key="ensemble_models"
        )

        if not selected_models:
            st.warning("Please select at least one model")
            return

        # Ensemble method selection
        ensemble_methods = EnsembleModel.ENSEMBLE_METHODS
        ensemble_method = st.selectbox(
            "Select ensemble method",
            options=list(ensemble_methods.keys()),
            format_func=lambda x: ensemble_methods[x],
            index=1,  # Default to weighted average
            key="ensemble_method"
        )

        # Show description based on selected method
        if ensemble_method == "simple_average":
            st.info("**Simple Average**: Equal weight to all models. Good when you don't know which model performs best.")
        elif ensemble_method == "weighted_average":
            st.info("**Weighted Average**: Weights based on model performance (MSE). Better models get higher weights.")
        elif ensemble_method == "performance_weighted":
            st.info("**Performance-Based Weighting**: Weights based on multiple metrics (MSE, MAE, R², directional accuracy). More sophisticated than simple weighted average.")
        elif ensemble_method == "time_weighted":
            st.info("**Time-Based Weighting**: More weight to recent performance. Adapts to changing market conditions.")
        elif ensemble_method == "stacking":
            st.info("**Stacking**: Uses a meta-model to combine predictions. Most sophisticated but requires validation data.")

        # Advanced options
        with st.expander("Advanced Options"):
            # Confidence level
            confidence_level = st.slider(
                "Confidence Level",
                min_value=0.8,
                max_value=0.99,
                value=0.95,
                step=0.01,
                format="%.2f",
                help="Higher values create wider confidence intervals"
            )

            # Time decay factor (for time-weighted method)
            if ensemble_method == "time_weighted":
                time_decay_factor = st.slider(
                    "Time Decay Factor",
                    min_value=0.5,
                    max_value=0.99,
                    value=0.9,
                    step=0.01,
                    format="%.2f",
                    help="How quickly older performance data loses importance (higher = slower decay)"
                )

            # Meta-model type (for stacking method)
            if ensemble_method == "stacking":
                meta_model_type = st.selectbox(
                    "Meta-Model Type",
                    options=["ridge", "lasso"],
                    index=0,
                    help="Type of model to use for combining predictions"
                )

        # Prediction horizon configuration
        st.subheader("Prediction Horizons")

        # Prediction timeframe selection
        horizon_options = [
            "Short-term (minutes)",
            "Medium-term (days)",
            "Long-term (weeks)"
        ]
        horizon_type = st.radio("Select prediction timeframe", horizon_options, key="ensemble_horizon_type")

        # Define horizons based on selected timeframe
        if horizon_type == "Short-term (minutes)":
            horizons = [5, 15, 30, 60]
            horizon_unit = "minutes"
        elif horizon_type == "Medium-term (days)":
            horizons = [1, 2, 3, 5, 7]
            horizon_unit = "days"
        else:  # Long-term
            horizons = [1, 2, 4, 8]
            horizon_unit = "weeks"

        # Allow user to select specific horizons
        selected_horizons = st.multiselect(
            f"Select specific prediction horizons ({horizon_unit})",
            options=horizons,
            default=horizons[:2],
            key="ensemble_selected_horizons"
        )

        if not selected_horizons:
            st.warning("Please select at least one prediction horizon")
            return

        # Option to fetch fresh live data
        fetch_live_data = st.checkbox(
            "Fetch fresh live data before prediction",
            value=True,
            help="Fetch the latest price data before making predictions",
            key="ensemble_fetch_live"
        )

    with tabs[1]:
        # Predictions Tab
        st.subheader("Generate Ensemble Predictions")

        if st.button("Generate Predictions", key="ensemble_generate_btn"):
            try:
                with st.spinner("Generating ensemble predictions..."):
                    # Convert horizons to minutes for prediction functions
                    model_horizons = []
                    horizon_conversion = {}  # Map original horizons to converted horizons

                    for h in selected_horizons:
                        if horizon_unit == "days":
                            converted_horizon = h * 24 * 60  # days to minutes
                        elif horizon_unit == "weeks":
                            converted_horizon = h * 7 * 24 * 60  # weeks to minutes
                        else:  # minutes
                            converted_horizon = h

                        model_horizons.append(converted_horizon)
                        horizon_conversion[h] = converted_horizon

                    # Fetch fresh live data if requested
                    current_live_data = live_data
                    if fetch_live_data:
                        # Use the modern API server approach (same as Advanced Analysis)
                        current_live_data = safe_get_live_data_ensemble(symbol)

                    # Try to use the full EnsembleModel implementation
                    try:
                        # Import EnsembleModel at the beginning of the function to avoid reference errors
                        # This is already imported at the top of the file, so we'll use that reference

                        # Apply NumPy fix again right before creating the ensemble model
                        try:
                            from app.utils.numpy_compatibility import apply_comprehensive_numpy_fix
                            apply_comprehensive_numpy_fix()
                        except Exception as e:
                            logger.warning(f"Error applying NumPy fix: {str(e)}")

                        # Force garbage collection
                        try:
                            import gc
                            gc.collect()
                        except Exception as e:
                            logger.warning(f"Error during garbage collection: {str(e)}")

                        # Create the ensemble model - use a try/except block for each step
                        try:
                            # First check if the class is properly imported
                            # Import EnsembleModel directly from the module to avoid reference errors
                            from app.models.ensemble_model import EnsembleModel as EnsembleModelClass

                            # Create the model instance
                            ensemble_model = EnsembleModelClass(
                                target_column='Close',
                                ensemble_method=ensemble_method,
                                selected_models=selected_models,
                                confidence_level=0.95
                            )
                            st.info(f"Using full ensemble model with {len(selected_models)} base models and {ensemble_method} method")
                        except Exception as model_error:
                            logger.error(f"Error creating EnsembleModel instance: {str(model_error)}")
                            st.warning(f"Could not create ensemble model instance: {str(model_error)}")
                            st.info(f"Using simplified multi-model approach with {len(selected_models)} base models")
                    except Exception as ensemble_error:
                        # Fall back to simplified approach if ensemble model creation fails
                        logger.error(f"Error in ensemble model section: {str(ensemble_error)}")
                        st.warning(f"Could not create ensemble model: {str(ensemble_error)}")
                        st.info(f"Using simplified multi-model approach with {len(selected_models)} base models")

                    # Get predictions from each model for each horizon
                    all_predictions = {}
                    model_predictions = {}

                    for horizon in model_horizons:
                        model_predictions[horizon] = {}

                        for model_name in selected_models:
                            try:
                                # Make predictions using the model
                                if current_live_data is not None and not current_live_data.empty:
                                    # Make predictions using live data
                                    pred = predict_from_live_data(
                                        current_live_data, historical_data, symbol,
                                        horizons=[horizon],
                                        model_type=model_name.lower(),
                                        models_path='saved_models'
                                    )
                                else:
                                    # Make predictions using only historical data
                                    pred = predict_future_prices(
                                        historical_data, symbol,
                                        horizons=[horizon],
                                        model_type=model_name.lower(),
                                        models_path='saved_models'
                                    )

                                # Store prediction
                                if horizon in pred:
                                    model_predictions[horizon][model_name] = pred[horizon]
                            except Exception as e:
                                st.warning(f"Error getting predictions from {model_name} for horizon {horizon}: {str(e)}")

                    # Create ensemble predictions
                    ensemble_predictions = {}
                    confidence_intervals = {}

                    # Current time for prediction timestamps
                    current_time = datetime.now()

                    # Process each horizon
                    for horizon in model_horizons:
                        # Skip if no predictions for this horizon
                        if not model_predictions[horizon]:
                            continue

                        # Create a small dataset for ensemble prediction
                        pred_df = pd.DataFrame({
                            'Close': [historical_data['Close'].iloc[-1]]
                        })

                        # Add model predictions as features
                        for model_name, pred in model_predictions[horizon].items():
                            pred_df[f'{model_name}_pred'] = [pred]

                        # Always use simple average to avoid BitGenerator issues
                        weights = {}

                        # Equal weights for all models
                        weight = 1.0 / len(model_predictions[horizon])
                        weights = {model: weight for model in model_predictions[horizon].keys()}

                        # Calculate weighted average
                        ensemble_pred = 0
                        for model_name, pred in model_predictions[horizon].items():
                            ensemble_pred += weights[model_name] * pred

                        # Calculate standard deviation for confidence interval
                        preds_array = np.array(list(model_predictions[horizon].values()))
                        std_dev = np.std(preds_array)

                        # Calculate confidence intervals (95%)
                        z_score = 1.96
                        lower_bound = ensemble_pred - z_score * std_dev
                        upper_bound = ensemble_pred + z_score * std_dev

                        # Store ensemble prediction
                        ensemble_predictions[horizon] = ensemble_pred

                        # Store confidence interval
                        confidence_intervals[horizon] = {
                            'lower': lower_bound,
                            'upper': upper_bound,
                            'std_dev': std_dev
                        }

                    # Display predictions
                    if ensemble_predictions:
                        st.success("Ensemble predictions generated successfully!")

                        # Create a DataFrame for display
                        pred_data = []

                        for h_minutes in model_horizons:
                            # Skip if no prediction for this horizon
                            if h_minutes not in ensemble_predictions:
                                continue

                            # Convert back to original horizon unit
                            for h, converted_h in horizon_conversion.items():
                                if converted_h == h_minutes:
                                    original_h = h
                                    break
                            else:
                                original_h = h_minutes / (24 * 60) if horizon_unit == "days" else h_minutes / (7 * 24 * 60)

                            # Calculate prediction time
                            if horizon_unit == "minutes":
                                pred_time = current_time + timedelta(minutes=original_h)
                            elif horizon_unit == "days":
                                pred_time = current_time + timedelta(days=original_h)
                            else:  # weeks
                                pred_time = current_time + timedelta(weeks=original_h)

                            # Add to predictions data
                            pred_data.append({
                                'Horizon': f'{original_h} {horizon_unit}',
                                'Predicted Time': pred_time.strftime('%Y-%m-%d %H:%M:%S'),
                                'Predicted Price': round(ensemble_predictions[h_minutes], 2),
                                'Lower Bound': round(confidence_intervals[h_minutes]['lower'], 2),
                                'Upper Bound': round(confidence_intervals[h_minutes]['upper'], 2),
                                'Uncertainty': round(confidence_intervals[h_minutes]['std_dev'], 2)
                            })

                        # Create and display the predictions dataframe
                        pred_df = pd.DataFrame(pred_data)
                        st.dataframe(pred_df)

                        # Track predictions for performance metrics
                        track_predictions = {}
                        for h_minutes in model_horizons:
                            if h_minutes in ensemble_predictions:
                                track_predictions[h_minutes] = ensemble_predictions[h_minutes]

                        # Record predictions for later verification
                        track_prediction(symbol, track_predictions, "minutes", f"ensemble_{ensemble_method}")

                        # Plot predictions
                        st.subheader("Prediction Visualization")

                        # Create a plotly figure
                        fig = go.Figure()

                        # Add historical data
                        fig.add_trace(go.Scatter(
                            x=historical_data['Date'].iloc[-30:],  # Last 30 days
                            y=historical_data['Close'].iloc[-30:],
                            mode='lines',
                            name='Historical',
                            line=dict(color='blue')
                        ))

                        # Add live data if available
                        if current_live_data is not None and not current_live_data.empty:
                            fig.add_trace(go.Scatter(
                                x=current_live_data['Date'],
                                y=current_live_data['Close'],
                                mode='markers',
                                name='Live',
                                marker=dict(color='green', size=10)
                            ))

                        # Add predictions
                        colors = ['red', 'orange', 'purple', 'brown', 'pink']

                        for i, row in enumerate(pred_data):
                            # Get color for this horizon
                            color = colors[i % len(colors)]

                            # Add prediction point
                            fig.add_trace(go.Scatter(
                                x=[pd.to_datetime(row['Predicted Time'])],
                                y=[row['Predicted Price']],
                                mode='markers',
                                name=row['Horizon'],
                                marker=dict(color=color, size=10)
                            ))

                            # Add confidence interval
                            fig.add_trace(go.Scatter(
                                x=[pd.to_datetime(row['Predicted Time']), pd.to_datetime(row['Predicted Time'])],
                                y=[row['Lower Bound'], row['Upper Bound']],
                                mode='lines',
                                line=dict(color=color, width=1),
                                showlegend=False
                            ))

                        # Update layout
                        fig.update_layout(
                            title=f'{symbol} Multi-Model Predictions (Simple Average)',
                            xaxis_title='Date',
                            yaxis_title='Price',
                            hovermode='x unified',
                            legend=dict(
                                orientation="h",
                                yanchor="bottom",
                                y=1.02,
                                xanchor="right",
                                x=1
                            )
                        )

                        # Show plot
                        st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.warning("No predictions could be generated. Please check if models are trained for the selected horizons.")
            except Exception as e:
                st.error(f"Error generating ensemble predictions: {str(e)}")
                st.info("This might be due to missing trained models for the selected horizons. Please train models first.")

    with tabs[2]:
        # Model Comparison Tab
        st.subheader("Model Comparison")

        # Select horizon for comparison
        if horizon_unit == "minutes":
            default_horizon = 30
        elif horizon_unit == "days":
            default_horizon = 1
        else:  # weeks
            default_horizon = 1

        comparison_horizon = st.selectbox(
            f"Select horizon for model comparison ({horizon_unit})",
            options=horizons,
            index=horizons.index(default_horizon) if default_horizon in horizons else 0,
            key="comparison_horizon"
        )

        # Convert to minutes
        if horizon_unit == "days":
            comparison_horizon_minutes = comparison_horizon * 24 * 60
        elif horizon_unit == "weeks":
            comparison_horizon_minutes = comparison_horizon * 7 * 24 * 60
        else:
            comparison_horizon_minutes = comparison_horizon

        # Button to generate comparison
        if st.button("Compare Models", key="compare_models_btn"):
            try:
                with st.spinner("Generating model comparison..."):
                    # Get predictions from each model
                    model_predictions = {}

                    for model_name in selected_models:
                        try:
                            # Make predictions using the model
                            if live_data is not None and not live_data.empty:
                                # Make predictions using live data
                                pred = predict_from_live_data(
                                    live_data, historical_data, symbol,
                                    horizons=[comparison_horizon_minutes],
                                    model_type=model_name.lower(),
                                    models_path='saved_models'
                                )
                            else:
                                # Make predictions using only historical data
                                pred = predict_future_prices(
                                    historical_data, symbol,
                                    horizons=[comparison_horizon_minutes],
                                    model_type=model_name.lower(),
                                    models_path='saved_models'
                                )

                            # Store prediction
                            if comparison_horizon_minutes in pred:
                                model_predictions[model_name] = pred[comparison_horizon_minutes]
                        except Exception as e:
                            st.warning(f"Error getting predictions from {model_name}: {str(e)}")

                    # Check if we have any predictions
                    if not model_predictions:
                        st.warning("No predictions could be generated. Please check if models are trained for the selected horizon.")
                        return

                    # Create ensemble predictions for different methods
                    ensemble_predictions = {}

                    # Simple average
                    if len(model_predictions) > 0:
                        ensemble_predictions['Simple Average'] = sum(model_predictions.values()) / len(model_predictions)

                    # Weighted average (using placeholder weights)
                    if len(model_predictions) > 0:
                        # Just use equal weights for now
                        weights = {model: 1.0/len(model_predictions) for model in model_predictions.keys()}
                        ensemble_predictions['Weighted Average'] = sum(pred * weights[model] for model, pred in model_predictions.items())

                    # Performance-weighted (using placeholder weights)
                    if len(model_predictions) > 0:
                        # Just use equal weights for now
                        weights = {model: 1.0/len(model_predictions) for model in model_predictions.keys()}
                        ensemble_predictions['Performance Weighted'] = sum(pred * weights[model] for model, pred in model_predictions.items())

                    # Create a DataFrame for display
                    comparison_data = []

                    # Add individual model predictions
                    for model_name, pred in model_predictions.items():
                        comparison_data.append({
                            'Model': model_name,
                            'Predicted Price': round(pred, 2),
                            'Type': 'Base Model'
                        })

                    # Add ensemble predictions
                    for method_name, pred in ensemble_predictions.items():
                        comparison_data.append({
                            'Model': method_name,
                            'Predicted Price': round(pred, 2),
                            'Type': 'Ensemble'
                        })

                    # Create DataFrame
                    comparison_df = pd.DataFrame(comparison_data)

                    # Display comparison
                    st.subheader(f"Model Comparison for {comparison_horizon} {horizon_unit} Horizon")
                    st.dataframe(comparison_df)

                    # Create bar chart
                    fig = px.bar(
                        comparison_df,
                        x='Model',
                        y='Predicted Price',
                        color='Type',
                        title=f"Model Predictions Comparison for {comparison_horizon} {horizon_unit}",
                        labels={'Predicted Price': 'Predicted Price', 'Model': 'Model'},
                        text='Predicted Price'
                    )

                    # Update layout
                    fig.update_layout(
                        xaxis_title='Model',
                        yaxis_title='Predicted Price',
                        legend_title='Model Type',
                        xaxis={'categoryorder':'total descending'}
                    )

                    # Show plot
                    st.plotly_chart(fig, use_container_width=True)

                    # Add explanation
                    st.markdown("""
                    ### Ensemble Methods Explanation

                    - **Simple Average**: Equal weight to all models
                    - **Weighted Average**: Weights based on model performance (MSE)
                    - **Performance Weighted**: Weights based on multiple metrics (MSE, MAE, R², directional accuracy)

                    The full implementation of the enhanced ensemble model includes more sophisticated methods like time-based weighting and stacking, which require more historical performance data to work effectively.
                    """)
            except Exception as e:
                st.error(f"Error generating model comparison: {str(e)}")

    with tabs[3]:
        # Performance Analysis Tab
        st.subheader("Performance Analysis")

        st.info("This tab will show historical performance of ensemble methods once you have made predictions and they have been verified.")

        # Add explanation about the performance metrics
        st.markdown("""
        ### How Performance Tracking Works

        1. When you make predictions using the Ensemble Predictions page, they are automatically recorded
        2. After the prediction horizon has passed, the system verifies the predictions against actual prices
        3. Performance metrics are calculated and displayed on this page and in the Performance Metrics Dashboard

        To start building performance data:
        1. Make predictions using different ensemble methods
        2. Wait for the prediction horizons to pass
        3. Make sure you have actual price data for the verification times
        4. Return to this page to see the performance analysis

        You can also visit the Performance Metrics Dashboard for a more comprehensive view of all prediction methods.
        """)

        # Add button to go to Performance Metrics Dashboard
        if st.button("Go to Performance Metrics Dashboard"):
            st.session_state.page = "Performance Metrics"
            st.rerun
