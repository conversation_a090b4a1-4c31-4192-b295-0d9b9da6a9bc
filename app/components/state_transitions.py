"""
State transition components for the AI Stocks Bot application.

This module provides UI components for managing state transitions in the application,
such as loading data, selecting models, and displaying results.
"""

import os
import sys
import logging
import time
import types
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Union, List, Tuple, Type
import pandas as pd
import numpy as np
import streamlit as st

# Configure logging
logger = logging.getLogger(__name__)

# Import session state management
from app.utils.session_state import (
    get_session_value,
    set_session_value,
    track_error,
    track_performance
)

# Import state manager
from app.utils.state_manager import (
    load_stock_data,
    load_and_process_data,
    get_available_stock_files,
    save_uploaded_data,
    save_model,
    load_model,
    save_predictions
)

# Import error handling utilities
try:
    from app.utils.error_handling import (
        handle_exception,
        log_exception,
        ErrorHandler
    )
    ERROR_HANDLING_AVAILABLE = True
except ImportError:
    logger.warning("Error handling utilities not available in state_transitions module")
    ERROR_HANDLING_AVAILABLE = False

def data_selection_component() -> Tuple[Optional[pd.DataFrame], Optional[str]]:
    """
    Component for selecting stock data.

    Returns:
        Tuple[Optional[pd.DataFrame], Optional[str]]: Tuple of (DataFrame, symbol)
    """
    st.subheader("Select Stock Data")

    # Get available stock files
    available_stocks = get_available_stock_files()

    if not available_stocks:
        st.warning("No stock data files found. Please upload some data first.")
        st.info("Go to the 'Upload Data' page to upload stock data.")
        return None, None

    # Let user select a stock
    selected_stock = st.selectbox(
        "Select a stock",
        options=available_stocks,
        index=0 if available_stocks else None,
        key="data_selection_stock_select"
    )

    if not selected_stock:
        st.warning("Please select a stock from the dropdown")
        return None, None

    # Load the selected stock data
    with st.spinner(f"Loading data for {selected_stock}..."):
        start_time = time.time()
        df = load_stock_data(selected_stock)
        end_time = time.time()

        # Track performance
        track_performance('data_load', end_time - start_time)

    if df is not None:
        st.success(f"Loaded stock data for {selected_stock}")

        # Display date range
        st.info(f"Date range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")

        # Display data preview
        st.subheader("Data Preview")
        st.dataframe(df.head())

        # Update session state
        set_session_value('symbol', selected_stock)
        set_session_value('historical_data', df)

        return df, selected_stock
    else:
        st.error(f"Failed to load data for {selected_stock}")
        return None, None

def model_selection_component(symbol: Optional[str] = None) -> Tuple[Optional[str], Optional[int]]:
    """
    Component for selecting a model.

    Args:
        symbol (Optional[str]): Stock symbol

    Returns:
        Tuple[Optional[str], Optional[int]]: Tuple of (model_type, horizon)
    """
    st.subheader("Select Model")

    # Get symbol from session state if not provided
    if symbol is None:
        symbol = get_session_value('symbol')

    if not symbol:
        st.warning("Please select a stock first")
        return None, None

    # Model type selection
    model_type = st.selectbox(
        "Select model type",
        options=["Random Forest", "Gradient Boosting", "LSTM", "Ensemble"],
        index=0,
        key="model_selection_type"
    )

    # Convert to internal model type
    model_type_map = {
        "Random Forest": "rf",
        "Gradient Boosting": "gb",
        "LSTM": "lstm",
        "Ensemble": "ensemble"
    }

    internal_model_type = model_type_map.get(model_type, "rf")

    # Horizon selection
    horizon = st.selectbox(
        "Select prediction horizon (minutes)",
        options=[1, 5, 15, 30, 60, 240, 1440],  # 1 min to 1 day
        index=2,  # Default to 15 minutes
        key="model_selection_horizon"
    )

    # Check if model exists
    from app.utils.data_processing import is_model_trained

    model_exists = is_model_trained(symbol, horizon, internal_model_type)

    if model_exists:
        st.success(f"Model found for {symbol} with {horizon} minute horizon")
    else:
        st.warning(f"No model found for {symbol} with {horizon} minute horizon. You'll need to train a model first.")

    # Update session state
    set_session_value('model_type', internal_model_type)
    set_session_value('horizon', horizon)

    return internal_model_type, horizon

def prediction_options_component() -> Dict[str, Any]:
    """
    Component for configuring prediction options.

    Returns:
        Dict[str, Any]: Dictionary of prediction options
    """
    st.subheader("Prediction Options")

    # Get current values from session state
    options = get_session_value('prediction_options', {})

    # Number of predictions
    num_predictions = st.slider(
        "Number of predictions",
        min_value=1,
        max_value=10,
        value=options.get('num_predictions', 5),
        key="prediction_options_num"
    )

    # Use live data
    use_live_data = st.checkbox(
        "Use live data",
        value=options.get('use_live_data', True),
        key="prediction_options_live"
    )

    # Show confidence intervals
    show_confidence = st.checkbox(
        "Show confidence intervals",
        value=options.get('show_confidence', True),
        key="prediction_options_confidence"
    )

    # Advanced options
    show_advanced = st.checkbox(
        "Show advanced options",
        value=options.get('show_advanced', False),
        key="prediction_options_advanced_toggle"
    )

    if show_advanced:
        # Confidence level
        confidence_level = st.slider(
            "Confidence level (%)",
            min_value=50,
            max_value=99,
            value=options.get('confidence_level', 95),
            key="prediction_options_confidence_level"
        )

        # Feature importance
        show_feature_importance = st.checkbox(
            "Show feature importance",
            value=options.get('show_feature_importance', True),
            key="prediction_options_feature_importance"
        )

        # Ensemble weights
        if get_session_value('model_type') == 'ensemble':
            st.subheader("Ensemble Weights")

            # Get default weights
            default_weights = options.get('ensemble_weights', {
                'rf': 0.3,
                'gb': 0.3,
                'lstm': 0.4
            })

            # Random Forest weight
            rf_weight = st.slider(
                "Random Forest weight",
                min_value=0.0,
                max_value=1.0,
                value=default_weights.get('rf', 0.3),
                step=0.1,
                key="prediction_options_rf_weight"
            )

            # Gradient Boosting weight
            gb_weight = st.slider(
                "Gradient Boosting weight",
                min_value=0.0,
                max_value=1.0,
                value=default_weights.get('gb', 0.3),
                step=0.1,
                key="prediction_options_gb_weight"
            )

            # LSTM weight
            lstm_weight = st.slider(
                "LSTM weight",
                min_value=0.0,
                max_value=1.0,
                value=default_weights.get('lstm', 0.4),
                step=0.1,
                key="prediction_options_lstm_weight"
            )

            # Normalize weights
            total_weight = rf_weight + gb_weight + lstm_weight
            if total_weight > 0:
                rf_weight /= total_weight
                gb_weight /= total_weight
                lstm_weight /= total_weight

            ensemble_weights = {
                'rf': rf_weight,
                'gb': gb_weight,
                'lstm': lstm_weight
            }
        else:
            ensemble_weights = options.get('ensemble_weights', {})
            confidence_level = options.get('confidence_level', 95)
            show_feature_importance = options.get('show_feature_importance', True)
    else:
        # Use default values
        confidence_level = options.get('confidence_level', 95)
        show_feature_importance = options.get('show_feature_importance', True)
        ensemble_weights = options.get('ensemble_weights', {})

    # Create options dictionary
    prediction_options = {
        'num_predictions': num_predictions,
        'use_live_data': use_live_data,
        'show_confidence': show_confidence,
        'show_advanced': show_advanced,
        'confidence_level': confidence_level,
        'show_feature_importance': show_feature_importance,
        'ensemble_weights': ensemble_weights
    }

    # Update session state
    set_session_value('prediction_options', prediction_options)

    return prediction_options

def live_data_component(symbol: Optional[str] = None) -> Optional[pd.DataFrame]:
    """
    Component for fetching live data.

    Args:
        symbol (Optional[str]): Stock symbol

    Returns:
        Optional[pd.DataFrame]: DataFrame with live data
    """
    st.subheader("Live Price Data")

    # Get symbol from session state if not provided
    if symbol is None:
        symbol = get_session_value('symbol')

    if not symbol:
        st.warning("Please select a stock first")
        return None

    # Display the selected stock
    st.info(f"Selected stock: {symbol}")

    # Scraper source selection
    source = st.selectbox(
        "Select data source",
        options=["TradingView", "Mubasher"],
        index=0,
        key="live_data_source"
    )

    # Get live data
    if st.button("Fetch Live Price", key="live_data_fetch"):
        try:
            with st.spinner("Fetching live price..."):
                # Import scraper
                from scrapers.price_scraper import PriceScraper

                # Initialize scraper with proper resource management
                scraper = PriceScraper(source=source.lower())
                try:
                    start_time = time.time()
                    price_data = scraper.get_price(symbol)
                    end_time = time.time()

                    # Track performance
                    track_performance('live_data_fetch', end_time - start_time)

                    if price_data:
                        # Create DataFrame
                        live_df = pd.DataFrame([price_data])

                        # Check if this is sample data
                        is_sample = 'Note' in price_data and 'sample' in price_data['Note'].lower()

                        if is_sample:
                            st.warning(f"⚠️ Using sample price data for {symbol}. Live data could not be fetched.")
                            st.info("The sample price is based on historical data with a small random variation to simulate live data.")
                        else:
                            st.success(f"Live price fetched at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                        # Display live data
                        st.subheader("Live Price")
                        st.dataframe(live_df)

                        # Update session state
                        set_session_value('live_data', live_df)
                        set_session_value('last_update', datetime.now())

                        return live_df
                    else:
                        st.error(f"Failed to get price for {symbol}")
                        return None
                finally:
                    # Ensure scraper is closed
                    scraper.close_driver()
        except Exception as e:
            if ERROR_HANDLING_AVAILABLE:
                log_exception(e)
            else:
                logger.error(f"Error fetching live price: {str(e)}")

            st.error(f"Error fetching live price: {str(e)}")
            return None

    # Return cached live data if available
    live_data = get_session_value('live_data')
    last_update = get_session_value('last_update')

    if live_data is not None and last_update is not None:
        st.info(f"Using cached live data from {last_update.strftime('%Y-%m-%d %H:%M:%S')}")
        st.dataframe(live_data)
        return live_data

    return None
