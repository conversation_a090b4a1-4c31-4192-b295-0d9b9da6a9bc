# AI Stocks Bot Components

This directory contains the various components used in the AI Stocks Bot application.

## Unified Predictions Component

The `unified_predictions.py` file contains a new component that combines all prediction functionality in one place. This component is designed to streamline the prediction workflow and provide a more intuitive user experience.

### Features

- **Tabbed Interface**: Easily switch between different prediction types (Basic, Advanced, Quick)
- **Model Information**: View all trained models and their horizons
- **Quick Access**: Direct links to train more models if needed

### How to Use

1. Navigate to the "Unified Predictions" page from the sidebar
2. Select the appropriate tab for your prediction needs:
   - **Basic Predictions**: Simple predictions with minimal options
   - **Advanced Predictions**: More options including ensemble models and confidence scores
   - **Quick Predictions**: Fast predictions with pre-configured settings
3. View available trained models at the bottom of the page
4. Train more models directly from the unified interface if needed

### Integration with Existing Components

The unified predictions component integrates with:
- The existing prediction components
- The model factory for model information
- The training workflow

## Other Components

- **advanced_prediction.py**: Advanced prediction features with ensemble models and confidence scores
- **dashboard.py**: Main dashboard for visualizing stock data
- **interactive_dashboard.py**: Enhanced dashboard with interactive charts
- **live_trading.py**: Live trading simulation
- **portfolio.py**: Portfolio management
- **prediction.py**: Basic prediction functionality
- **reporting.py**: Report generation
- **stock_manager.py**: Stock data management

## Future Improvements

- Further consolidation of redundant functionality
- Enhanced model management
- Improved visualization of prediction results
- Better integration between components
