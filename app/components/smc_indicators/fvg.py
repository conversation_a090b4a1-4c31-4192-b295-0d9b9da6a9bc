"""
Enhanced Fair Value Gap (FVG) Detection for SMC Strategy
Identifies imbalances in price action that often get filled by smart money
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class FairValueGap:
    """Represents a Fair Value Gap"""
    high: float
    low: float
    timestamp: int
    gap_type: str  # 'bullish' or 'bearish'
    strength: float  # 0-1 strength score
    filled: bool = False
    partially_filled: bool = False
    volume: float = 0.0
    displacement: float = 0.0  # Size of the displacement that created the FVG

def detect_fvg(df: pd.DataFrame, min_gap_size: float = 0.001) -> List[FairValueGap]:
    """
    Enhanced FVG detection with strength analysis

    Args:
        df: OHLCV DataFrame
        min_gap_size: Minimum gap size as percentage of price

    Returns:
        List of FairValueGap objects
    """
    if len(df) < 3:
        return []

    fvg_zones = []

    for i in range(2, len(df)):
        candle1 = df.iloc[i-2]  # First candle
        candle2 = df.iloc[i-1]  # Middle candle (displacement candle)
        candle3 = df.iloc[i]    # Third candle

        # Extract scalar values from pandas Series
        try:
            c1_high = float(candle1['high'])
            c1_low = float(candle1['low'])
            c2_open = float(candle2['open'])
            c2_close = float(candle2['close'])
            c2_volume = float(candle2.get('volume', 0))
            c3_high = float(candle3['high'])
            c3_low = float(candle3['low'])
        except (TypeError, ValueError):
            # Skip this iteration if conversion fails
            continue

        # Bullish FVG: Gap between candle1 high and candle3 low
        if c3_low > c1_high:
            gap_size = c3_low - c1_high
            gap_size_pct = gap_size / c1_high

            if gap_size_pct >= min_gap_size:
                # Calculate displacement (size of the middle candle)
                displacement = abs(c2_close - c2_open) / c2_open if c2_open != 0 else 0

                # Calculate strength based on gap size, volume, and displacement
                avg_volume = float(df['volume'].rolling(20).mean().iloc[i-1])
                volume_strength = min(c2_volume / avg_volume if avg_volume > 0 else 0, 2.0) / 2.0
                gap_strength = min(gap_size_pct * 100, 5.0) / 5.0  # Normalize to 0-1
                displacement_strength = min(displacement * 10, 1.0)  # Normalize to 0-1

                strength = (volume_strength + gap_strength + displacement_strength) / 3

                fvg_zones.append(FairValueGap(
                    high=c3_low,
                    low=c1_high,
                    timestamp=i-1,  # Use middle candle timestamp
                    gap_type='bullish',
                    strength=strength,
                    volume=c2_volume,
                    displacement=displacement
                ))

        # Bearish FVG: Gap between candle1 low and candle3 high
        elif c3_high < c1_low:
            gap_size = c1_low - c3_high
            gap_size_pct = gap_size / c1_low

            if gap_size_pct >= min_gap_size:
                displacement = abs(c2_close - c2_open) / c2_open if c2_open != 0 else 0

                avg_volume = float(df['volume'].rolling(20).mean().iloc[i-1])
                volume_strength = min(c2_volume / avg_volume if avg_volume > 0 else 0, 2.0) / 2.0
                gap_strength = min(gap_size_pct * 100, 5.0) / 5.0
                displacement_strength = min(displacement * 10, 1.0)

                strength = (volume_strength + gap_strength + displacement_strength) / 3

                fvg_zones.append(FairValueGap(
                    high=c1_low,
                    low=c3_high,
                    timestamp=i-1,
                    gap_type='bearish',
                    strength=strength,
                    volume=c2_volume,
                    displacement=displacement
                ))

    # Update FVG status (filled/partially filled)
    fvg_zones = update_fvg_status(df, fvg_zones)

    # Sort by timestamp and return most recent
    fvg_zones.sort(key=lambda x: x.timestamp, reverse=True)
    return fvg_zones[:15]  # Return top 15 most recent

def update_fvg_status(df: pd.DataFrame, fvg_zones: List[FairValueGap]) -> List[FairValueGap]:
    """Update whether FVGs have been filled or partially filled"""

    for fvg in fvg_zones:
        # Check subsequent price action after FVG formation
        start_idx = fvg.timestamp + 1

        if start_idx >= len(df):
            continue

        subsequent_data = df.iloc[start_idx:]

        for idx, candle in subsequent_data.iterrows():
            if fvg.gap_type == 'bullish':
                # Check if price came back into the FVG zone
                if candle['low'] <= fvg.high and candle['high'] >= fvg.low:
                    fvg.partially_filled = True

                # Check if FVG was completely filled (price went below FVG low)
                if candle['low'] <= fvg.low:
                    fvg.filled = True
                    break

            elif fvg.gap_type == 'bearish':
                # Check if price came back into the FVG zone
                if candle['high'] >= fvg.low and candle['low'] <= fvg.high:
                    fvg.partially_filled = True

                # Check if FVG was completely filled (price went above FVG high)
                if candle['high'] >= fvg.high:
                    fvg.filled = True
                    break

    return fvg_zones

def get_active_fvgs(df: pd.DataFrame, current_price: float, max_distance_pct: float = 3.0) -> List[FairValueGap]:
    """Get FVGs that are still active and near current price"""
    all_fvgs = detect_fvg(df)

    active_fvgs = []
    for fvg in all_fvgs:
        if fvg.filled:
            continue

        # Check if FVG is within reasonable distance from current price
        fvg_center = (fvg.high + fvg.low) / 2
        distance_pct = abs(current_price - fvg_center) / current_price * 100

        if distance_pct <= max_distance_pct:
            active_fvgs.append(fvg)

    return active_fvgs

def analyze_fvg_confluence(fvgs: List[FairValueGap], current_price: float) -> Dict:
    """Analyze confluence of multiple FVGs"""
    if not fvgs:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    bullish_fvgs = [fvg for fvg in fvgs if fvg.gap_type == 'bullish' and not fvg.filled]
    bearish_fvgs = [fvg for fvg in fvgs if fvg.gap_type == 'bearish' and not fvg.filled]

    bullish_strength = sum(fvg.strength for fvg in bullish_fvgs)
    bearish_strength = sum(fvg.strength for fvg in bearish_fvgs)

    total_strength = bullish_strength + bearish_strength

    if total_strength == 0:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    # Determine dominant direction
    if bullish_strength > bearish_strength * 1.3:
        direction = "bullish"
        strength = bullish_strength / total_strength
    elif bearish_strength > bullish_strength * 1.3:
        direction = "bearish"
        strength = bearish_strength / total_strength
    else:
        direction = "neutral"
        strength = 0.5

    confluence = len(fvgs) >= 2 and total_strength > 0.8

    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "bullish_fvgs": len(bullish_fvgs),
        "bearish_fvgs": len(bearish_fvgs),
        "total_fvgs": len(fvgs)
    }

def get_nearest_fvg(fvgs: List[FairValueGap], current_price: float) -> Optional[FairValueGap]:
    """Get the nearest unfilled FVG to current price"""
    if not fvgs:
        return None

    unfilled_fvgs = [fvg for fvg in fvgs if not fvg.filled]
    if not unfilled_fvgs:
        return None

    # Find the FVG with minimum distance to current price
    nearest_fvg = min(unfilled_fvgs, key=lambda fvg: abs(current_price - (fvg.high + fvg.low) / 2))

    return nearest_fvg

# Legacy function for backward compatibility
def detect_fvg_legacy(df: pd.DataFrame) -> list:
    """Legacy function for backward compatibility"""
    fvgs = detect_fvg(df)

    # Convert to old format
    legacy_fvgs = []
    for fvg in fvgs:
        legacy_fvgs.append({
            "start": fvg.low,
            "end": fvg.high,
            "status": "filled" if fvg.filled else "unfilled",
            "index": fvg.timestamp,
            "type": fvg.gap_type,
            "strength": fvg.strength
        })

    return legacy_fvgs
