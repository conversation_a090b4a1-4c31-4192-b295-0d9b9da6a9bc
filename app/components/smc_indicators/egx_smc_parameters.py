#!/usr/bin/env python3
"""
EGX-Optimized SMC Parameters
Adjusted parameters specifically for Egyptian Exchange stocks
"""

# EGX-Optimized Parameters for SMC Analysis
EGX_SMC_PARAMS = {
    
    # Order Blocks - Relaxed for EGX market characteristics
    'order_blocks': {
        'lookback': 10,              # Reduced from 20 (less historical data needed)
        'min_strength': 0.15,        # Reduced from 0.3 (easier to detect)
        'confluence_min_blocks': 1,   # Reduced from 2 (single strong block counts)
        'confluence_min_strength': 0.5  # Reduced from 1.0
    },
    
    # Fair Value Gaps - Adjusted for EGX volatility
    'fvg': {
        'min_gap_size': 0.002,       # Reduced from 0.005 (0.2% vs 0.5%)
        'confluence_min_fvgs': 1,    # Reduced from 2 (single strong FVG counts)
        'confluence_min_strength': 0.4  # Reduced from 0.8
    },
    
    # Liquidity Zones - Optimized for EGX equal highs/lows
    'liquidity_zones': {
        'lookback': 30,              # Reduced from 50
        'equal_tolerance': 0.003,    # Increased from 0.002 (0.3% tolerance)
        'confluence_min_zones': 2,   # Reduced from 3
        'confluence_min_strength': 1.0  # Reduced from 2.0
    },
    
    # Displacements - Adjusted for EGX market moves
    'displacement': {
        'min_displacement_pct': 1.0, # Reduced from 2.0 (1% vs 2%)
        'min_strength': 0.15,        # Reduced from 0.3
        'volume_threshold': 1.2,     # Reduced from 1.5 (20% vs 50% volume increase)
        'confluence_min_displacements': 1,  # Reduced from 2
        'confluence_min_strength': 0.8      # Reduced from 1.5
    },
    
    # Market Structure - EGX specific
    'market_structure': {
        'swing_lookback': 8,         # Reduced from 12
        'min_swing_size': 0.01       # 1% minimum swing size
    },
    
    # Distance filters for active detection
    'distance_filters': {
        'order_blocks_pct': 8.0,     # Increased from 5.0 (8% distance)
        'fvg_pct': 5.0,              # Increased from 3.0 (5% distance)
        'liquidity_zones_pct': 10.0, # Increased from 8.0 (10% distance)
        'displacement_pct': 6.0      # Increased from 4.0 (6% distance)
    },
    
    # Confluence scoring weights
    'confluence_weights': {
        'order_blocks': 0.25,
        'fvg': 0.20,
        'liquidity_zones': 0.25,
        'displacement': 0.15,
        'market_structure': 0.15
    }
}

def get_egx_parameters():
    """Get EGX-optimized SMC parameters"""
    return EGX_SMC_PARAMS

def get_stock_specific_parameters(symbol: str):
    """Get stock-specific parameters (can be customized per stock)"""
    # For now, return default EGX parameters
    # In future, can add stock-specific adjustments
    params = EGX_SMC_PARAMS.copy()
    
    # Example: Adjust for high-volatility stocks
    high_volatility_stocks = ['FWRY', 'PHDC', 'GGRN']
    if symbol in high_volatility_stocks:
        params['fvg']['min_gap_size'] = 0.003  # Increase gap size requirement
        params['displacement']['min_displacement_pct'] = 1.5  # Increase displacement requirement
    
    return params
