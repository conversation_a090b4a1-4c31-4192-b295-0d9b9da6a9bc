"""
Algorithmic Trading Engine
Advanced automated trading strategies with risk management and backtesting
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import json
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time

# Configure logging
logger = logging.getLogger(__name__)

class OrderType(Enum):
    """Order types for trading"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    """Order status"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class StrategyStatus(Enum):
    """Strategy status"""
    ACTIVE = "active"
    PAUSED = "paused"
    STOPPED = "stopped"

@dataclass
class Order:
    """Trading order data structure"""
    id: str
    symbol: str
    side: OrderSide
    type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime = None
    filled_at: Optional[datetime] = None
    filled_price: Optional[float] = None
    strategy_id: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Position:
    """Trading position data structure"""
    symbol: str
    quantity: float
    avg_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float
    created_at: datetime
    updated_at: datetime

@dataclass
class TradingStrategy:
    """Trading strategy configuration"""
    id: str
    name: str
    description: str
    symbols: List[str]
    parameters: Dict
    risk_management: Dict
    status: StrategyStatus = StrategyStatus.ACTIVE
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class AlgoTradingEngine:
    """Advanced Algorithmic Trading Engine"""
    
    def __init__(self, data_dir: str = "trading_data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Trading state
        self.strategies: Dict[str, TradingStrategy] = {}
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.portfolio_value = 100000.0  # Starting capital
        self.available_cash = 100000.0
        
        # Performance tracking
        self.trade_history = []
        self.performance_metrics = {}
        
        # Risk management
        self.max_position_size = 0.1  # 10% of portfolio per position
        self.max_daily_loss = 0.05    # 5% max daily loss
        self.max_drawdown = 0.15      # 15% max drawdown
        
        # Strategy execution
        self.is_running = False
        self.execution_thread = None
        
        logger.info("Algorithmic Trading Engine initialized")
    
    def create_strategy(self, strategy_config: Dict) -> str:
        """Create a new trading strategy"""
        try:
            strategy = TradingStrategy(
                id=f"strategy_{len(self.strategies) + 1}_{int(time.time())}",
                name=strategy_config['name'],
                description=strategy_config.get('description', ''),
                symbols=strategy_config['symbols'],
                parameters=strategy_config.get('parameters', {}),
                risk_management=strategy_config.get('risk_management', {})
            )
            
            self.strategies[strategy.id] = strategy
            self._save_strategy(strategy)
            
            logger.info(f"Created strategy: {strategy.name} ({strategy.id})")
            return strategy.id
            
        except Exception as e:
            logger.error(f"Error creating strategy: {str(e)}")
            return ""
    
    def get_predefined_strategies(self) -> Dict:
        """Get predefined trading strategies"""
        return {
            'momentum_breakout': {
                'name': 'Momentum Breakout',
                'description': 'Buy on upward momentum, sell on reversal',
                'parameters': {
                    'lookback_period': 20,
                    'breakout_threshold': 0.02,
                    'stop_loss_pct': 0.05,
                    'take_profit_pct': 0.10,
                    'rsi_oversold': 30,
                    'rsi_overbought': 70
                },
                'risk_management': {
                    'max_position_size': 0.1,
                    'max_daily_trades': 5,
                    'position_sizing': 'fixed_percentage'
                }
            },
            'mean_reversion': {
                'name': 'Mean Reversion',
                'description': 'Buy oversold, sell overbought conditions',
                'parameters': {
                    'lookback_period': 14,
                    'bollinger_std': 2.0,
                    'rsi_oversold': 25,
                    'rsi_overbought': 75,
                    'stop_loss_pct': 0.03,
                    'take_profit_pct': 0.06
                },
                'risk_management': {
                    'max_position_size': 0.08,
                    'max_daily_trades': 3,
                    'position_sizing': 'volatility_adjusted'
                }
            },
            'trend_following': {
                'name': 'Trend Following',
                'description': 'Follow strong trends with moving average crossovers',
                'parameters': {
                    'fast_ma': 10,
                    'slow_ma': 30,
                    'trend_strength_threshold': 0.015,
                    'stop_loss_pct': 0.08,
                    'trailing_stop_pct': 0.05
                },
                'risk_management': {
                    'max_position_size': 0.12,
                    'max_daily_trades': 2,
                    'position_sizing': 'trend_strength'
                }
            },
            'ml_prediction': {
                'name': 'ML Prediction Based',
                'description': 'Trade based on machine learning predictions',
                'parameters': {
                    'confidence_threshold': 0.7,
                    'prediction_horizon': 5,
                    'stop_loss_pct': 0.06,
                    'take_profit_pct': 0.12,
                    'rebalance_frequency': 'daily'
                },
                'risk_management': {
                    'max_position_size': 0.15,
                    'max_daily_trades': 4,
                    'position_sizing': 'confidence_weighted'
                }
            },
            'pairs_trading': {
                'name': 'Pairs Trading',
                'description': 'Trade correlated stock pairs for arbitrage',
                'parameters': {
                    'lookback_period': 60,
                    'entry_threshold': 2.0,
                    'exit_threshold': 0.5,
                    'stop_loss_threshold': 3.0,
                    'correlation_threshold': 0.8
                },
                'risk_management': {
                    'max_position_size': 0.05,
                    'max_pairs': 3,
                    'position_sizing': 'equal_weight'
                }
            }
        }
    
    def execute_momentum_breakout_strategy(self, symbol: str, data: pd.DataFrame, params: Dict) -> List[Dict]:
        """Execute momentum breakout strategy"""
        try:
            signals = []
            
            # Calculate indicators
            lookback = params.get('lookback_period', 20)
            breakout_threshold = params.get('breakout_threshold', 0.02)
            
            # Moving averages
            data['sma_fast'] = data['Close'].rolling(window=10).mean()
            data['sma_slow'] = data['Close'].rolling(window=lookback).mean()
            
            # RSI
            delta = data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            data['bb_middle'] = data['Close'].rolling(window=lookback).mean()
            data['bb_std'] = data['Close'].rolling(window=lookback).std()
            data['bb_upper'] = data['bb_middle'] + (data['bb_std'] * 2)
            data['bb_lower'] = data['bb_middle'] - (data['bb_std'] * 2)
            
            # Volume analysis
            data['volume_ma'] = data['Volume'].rolling(window=lookback).mean()
            data['volume_ratio'] = data['Volume'] / data['volume_ma']
            
            # Generate signals
            for i in range(lookback, len(data)):
                current_price = data['Close'].iloc[i]
                prev_price = data['Close'].iloc[i-1]
                price_change = (current_price - prev_price) / prev_price
                
                rsi = data['rsi'].iloc[i]
                volume_ratio = data['volume_ratio'].iloc[i]
                
                # Breakout conditions
                above_sma = current_price > data['sma_slow'].iloc[i]
                strong_volume = volume_ratio > 1.5
                momentum = price_change > breakout_threshold
                
                # Buy signal
                if (above_sma and momentum and strong_volume and 
                    rsi < params.get('rsi_overbought', 70)):
                    
                    signals.append({
                        'timestamp': data.index[i],
                        'symbol': symbol,
                        'action': 'BUY',
                        'price': current_price,
                        'confidence': min(0.9, (volume_ratio - 1) * 0.5 + 0.5),
                        'stop_loss': current_price * (1 - params.get('stop_loss_pct', 0.05)),
                        'take_profit': current_price * (1 + params.get('take_profit_pct', 0.10)),
                        'reason': f'Momentum breakout: {price_change:.2%} with volume {volume_ratio:.1f}x'
                    })
                
                # Sell signal (if we have position)
                elif (rsi > params.get('rsi_overbought', 70) or 
                      current_price < data['sma_fast'].iloc[i]):
                    
                    signals.append({
                        'timestamp': data.index[i],
                        'symbol': symbol,
                        'action': 'SELL',
                        'price': current_price,
                        'confidence': 0.7,
                        'reason': f'Exit signal: RSI {rsi:.1f} or below fast MA'
                    })
            
            return signals
            
        except Exception as e:
            logger.error(f"Error executing momentum breakout strategy: {str(e)}")
            return []
    
    def execute_mean_reversion_strategy(self, symbol: str, data: pd.DataFrame, params: Dict) -> List[Dict]:
        """Execute mean reversion strategy"""
        try:
            signals = []
            
            lookback = params.get('lookback_period', 14)
            bb_std = params.get('bollinger_std', 2.0)
            
            # Calculate indicators
            data['sma'] = data['Close'].rolling(window=lookback).mean()
            data['std'] = data['Close'].rolling(window=lookback).std()
            data['bb_upper'] = data['sma'] + (data['std'] * bb_std)
            data['bb_lower'] = data['sma'] - (data['std'] * bb_std)
            
            # RSI
            delta = data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=lookback).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=lookback).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))
            
            # Generate signals
            for i in range(lookback, len(data)):
                current_price = data['Close'].iloc[i]
                rsi = data['rsi'].iloc[i]
                bb_upper = data['bb_upper'].iloc[i]
                bb_lower = data['bb_lower'].iloc[i]
                sma = data['sma'].iloc[i]
                
                # Oversold condition (Buy signal)
                if (current_price <= bb_lower and 
                    rsi <= params.get('rsi_oversold', 25)):
                    
                    signals.append({
                        'timestamp': data.index[i],
                        'symbol': symbol,
                        'action': 'BUY',
                        'price': current_price,
                        'confidence': 0.8,
                        'stop_loss': current_price * (1 - params.get('stop_loss_pct', 0.03)),
                        'take_profit': sma,  # Target mean reversion to SMA
                        'reason': f'Oversold: Price {current_price:.2f} below BB lower {bb_lower:.2f}, RSI {rsi:.1f}'
                    })
                
                # Overbought condition (Sell signal)
                elif (current_price >= bb_upper and 
                      rsi >= params.get('rsi_overbought', 75)):
                    
                    signals.append({
                        'timestamp': data.index[i],
                        'symbol': symbol,
                        'action': 'SELL',
                        'price': current_price,
                        'confidence': 0.8,
                        'stop_loss': current_price * (1 + params.get('stop_loss_pct', 0.03)),
                        'take_profit': sma,  # Target mean reversion to SMA
                        'reason': f'Overbought: Price {current_price:.2f} above BB upper {bb_upper:.2f}, RSI {rsi:.1f}'
                    })
            
            return signals
            
        except Exception as e:
            logger.error(f"Error executing mean reversion strategy: {str(e)}")
            return []
