"""
AI Stocks Bot Application

This package contains the main application code for the AI Stocks Bot.
"""

import os
import sys
import logging
from datetime import datetime

# Application version
APP_VERSION = "1.0.0"
APP_NAME = "AI Stocks Bot for EGX"

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f'app_{datetime.now().strftime("%Y%m%d")}.log')

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

# Create a logger for this module
logger = logging.getLogger(__name__)
logger.info(f"Starting {APP_NAME} v{APP_VERSION}")

# Import memory management utilities
try:
    from app.utils.memory_management import cleanup_memory
    logger.info("Memory management utilities loaded")
except ImportError:
    logger.warning("Memory management utilities not available")

    # Define a dummy function if the real one is not available
    def cleanup_memory(objects_to_clean=None):
        pass

# Import error handling utilities
try:
    from app.utils.error_handling import handle_exception, log_exception
    logger.info("Error handling utilities loaded")
except ImportError:
    logger.warning("Error handling utilities not available")

    # Define dummy functions if the real ones are not available
    def handle_exception(exc, default_return_value=None, raise_exception=False, log_level=logging.ERROR):
        logger.error(f"Error: {str(exc)}")
        if raise_exception:
            raise exc
        return default_return_value

    def log_exception(exc, level=logging.ERROR, include_traceback=True):
        logger.log(level, f"Exception: {str(exc)}")

# Apply numpy fix before importing numpy-dependent libraries
try:
    from app.utils.numpy_fix import fix_numpy_imports
    fix_numpy_imports()
    logger.info("Applied NumPy fix")
except ImportError:
    logger.warning("NumPy fix not available")

# Apply NumPy BitGenerator fix for scikit-learn compatibility
try:
    from app.utils.numpy_bitgenerator_fix import fix_numpy_bitgenerator
    fix_numpy_bitgenerator()
    logger.info("Applied NumPy BitGenerator fix")
except ImportError:
    logger.warning("NumPy BitGenerator fix not available")

# Apply TensorFlow fix for Windows compatibility
try:
    from app.utils.tensorflow_fix import fix_tensorflow_imports
    fix_tensorflow_imports()
    logger.info("Applied TensorFlow fix")
except ImportError:
    logger.warning("TensorFlow fix not available")

# Initialize session state management
def init_session_state():
    """
    Initialize session state variables for the application.
    This should be called at the start of the Streamlit app.
    """
    import streamlit as st

    # Define default session state variables if they don't exist
    if 'historical_data' not in st.session_state:
        st.session_state.historical_data = None

    if 'symbol' not in st.session_state:
        st.session_state.symbol = None

    if 'predictions' not in st.session_state:
        st.session_state.predictions = {}

    if 'models' not in st.session_state:
        st.session_state.models = {}

    if 'live_data' not in st.session_state:
        st.session_state.live_data = None

    if 'last_update' not in st.session_state:
        st.session_state.last_update = None

    if 'config' not in st.session_state:
        st.session_state.config = None

    # Performance tracking
    if 'performance_metrics' not in st.session_state:
        st.session_state.performance_metrics = {
            'load_times': [],
            'prediction_times': [],
            'render_times': []
        }

    logger.info("Session state initialized")

# Clean up resources when the application exits
import atexit

def cleanup_resources():
    """
    Clean up resources when the application exits.
    """
    logger.info("Cleaning up resources...")
    cleanup_memory()
    logger.info("Application shutdown complete")

atexit.register(cleanup_resources)
