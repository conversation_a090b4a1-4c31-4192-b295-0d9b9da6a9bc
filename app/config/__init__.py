"""
Configuration management for the AI Stocks Bot application.

This module provides a centralized way to manage configuration settings
for the application, including loading from config files, environment
variables, and providing defaults.
"""

import os
import sys
import logging
import configparser
from typing import Dict, Any, Optional, Union
import json

# Configure logging
logger = logging.getLogger(__name__)

# Default configuration values
DEFAULT_CONFIG = {
    "app": {
        "name": "AI Stocks Bot for EGX",
        "version": "1.0.0",
        "debug": False,
        "log_level": "INFO",
        "data_dir": "data/stocks",
        "models_dir": "saved_models",
        "logs_dir": "logs"
    },
    "scraper": {
        "source": "tradingview",
        "use_real_time": False,
        "timeout": 30,
        "retry_attempts": 3,
        "retry_delay": 1.0,
        "max_wait": 30.0
    },
    "model": {
        "default_type": "ensemble",
        "sequence_length": 60,
        "prediction_horizons": [5, 15, 30, 60],
        "batch_size": 32,
        "epochs": 50,
        "validation_split": 0.2,
        "use_early_stopping": True
    },
    "ui": {
        "theme": "light",
        "show_advanced_options": False,
        "cache_timeout": 3600,
        "max_display_rows": 100
    }
}

class Config:
    """
    Configuration manager for the application.

    This class handles loading configuration from various sources,
    providing defaults, and accessing configuration values.
    """

    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize the configuration manager.

        Args:
            config_file (str, optional): Path to the configuration file.
                If None, will look for config.ini in the app directory.
        """
        self._config = DEFAULT_CONFIG.copy()
        self._config_file = config_file

        # Load configuration from file if provided
        if config_file:
            self.load_from_file(config_file)
        else:
            # Try to find config.ini in standard locations
            possible_locations = [
                "config.ini",
                "config/config.ini",
                os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.ini"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "config.ini")
            ]

            for location in possible_locations:
                if os.path.exists(location):
                    self.load_from_file(location)
                    break

        # Override with environment variables
        self.load_from_env()

        logger.info(f"Configuration initialized")

    def load_from_file(self, config_file: str) -> bool:
        """
        Load configuration from a file.

        Args:
            config_file (str): Path to the configuration file.

        Returns:
            bool: True if the file was loaded successfully, False otherwise.
        """
        try:
            if not os.path.exists(config_file):
                logger.warning(f"Configuration file not found: {config_file}")
                return False

            parser = configparser.ConfigParser()
            parser.read(config_file)

            # Update configuration from file
            for section in parser.sections():
                if section not in self._config:
                    self._config[section] = {}

                for key, value in parser[section].items():
                    # Try to convert value to appropriate type
                    try:
                        # Try as boolean
                        if value.lower() in ('true', 'yes', '1'):
                            value = True
                        elif value.lower() in ('false', 'no', '0'):
                            value = False
                        # Try as number
                        elif value.isdigit():
                            value = int(value)
                        elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                            value = float(value)
                        # Try as JSON
                        elif value.startswith('{') or value.startswith('['):
                            try:
                                value = json.loads(value)
                            except json.JSONDecodeError:
                                pass
                    except (ValueError, AttributeError):
                        pass

                    self._config[section][key] = value

            logger.info(f"Loaded configuration from {config_file}")
            return True

        except Exception as e:
            logger.error(f"Error loading configuration from {config_file}: {str(e)}")
            return False

    def load_from_env(self) -> None:
        """
        Load configuration from environment variables.

        Environment variables should be in the format:
        AIBOT_SECTION_KEY=value

        For example:
        AIBOT_APP_DEBUG=true
        """
        try:
            prefix = "AIBOT_"

            for key, value in os.environ.items():
                if key.startswith(prefix):
                    # Remove prefix and split into section and key
                    parts = key[len(prefix):].lower().split('_', 1)

                    if len(parts) == 2:
                        section, key = parts

                        # Create section if it doesn't exist
                        if section not in self._config:
                            self._config[section] = {}

                        # Try to convert value to appropriate type
                        try:
                            # Try as boolean
                            if value.lower() in ('true', 'yes', '1'):
                                value = True
                            elif value.lower() in ('false', 'no', '0'):
                                value = False
                            # Try as number
                            elif value.isdigit():
                                value = int(value)
                            elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                                value = float(value)
                            # Try as JSON
                            elif value.startswith('{') or value.startswith('['):
                                try:
                                    value = json.loads(value)
                                except json.JSONDecodeError:
                                    pass
                        except (ValueError, AttributeError):
                            pass

                        self._config[section][key] = value

            logger.debug("Loaded configuration from environment variables")

        except Exception as e:
            logger.error(f"Error loading configuration from environment variables: {str(e)}")

    def get(self, section: str, key: str, default: Any = None) -> Any:
        """
        Get a configuration value.

        Args:
            section (str): Configuration section.
            key (str): Configuration key.
            default (Any, optional): Default value if the key is not found.

        Returns:
            Any: The configuration value, or the default if not found.
        """
        try:
            return self._config.get(section, {}).get(key, default)
        except Exception as e:
            logger.error(f"Error getting configuration value {section}.{key}: {str(e)}")
            return default

    def set(self, section: str, key: str, value: Any) -> None:
        """
        Set a configuration value.

        Args:
            section (str): Configuration section.
            key (str): Configuration key.
            value (Any): Configuration value.
        """
        try:
            if section not in self._config:
                self._config[section] = {}

            self._config[section][key] = value
            logger.debug(f"Set configuration value {section}.{key} = {value}")

        except Exception as e:
            logger.error(f"Error setting configuration value {section}.{key}: {str(e)}")

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get all configuration values in a section.

        Args:
            section (str): Configuration section.

        Returns:
            Dict[str, Any]: Dictionary of configuration values in the section.
        """
        try:
            return self._config.get(section, {}).copy()
        except Exception as e:
            logger.error(f"Error getting configuration section {section}: {str(e)}")
            return {}

    def get_all(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all configuration values.

        Returns:
            Dict[str, Dict[str, Any]]: Dictionary of all configuration values.
        """
        return self._config.copy()

    def save(self, config_file: Optional[str] = None) -> bool:
        """
        Save the configuration to a file.

        Args:
            config_file (str, optional): Path to the configuration file.
                If None, will use the file specified in the constructor.

        Returns:
            bool: True if the file was saved successfully, False otherwise.
        """
        try:
            if config_file is None:
                config_file = self._config_file

            if config_file is None:
                logger.error("No configuration file specified")
                return False

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(config_file)), exist_ok=True)

            parser = configparser.ConfigParser()

            # Convert configuration to strings
            for section, values in self._config.items():
                parser[section] = {}

                for key, value in values.items():
                    if isinstance(value, (dict, list)):
                        value = json.dumps(value)
                    elif isinstance(value, bool):
                        value = str(value).lower()
                    else:
                        value = str(value)

                    parser[section][key] = value

            # Write to file
            with open(config_file, 'w') as f:
                parser.write(f)

            logger.info(f"Saved configuration to {config_file}")
            return True

        except Exception as e:
            logger.error(f"Error saving configuration to {config_file}: {str(e)}")
            return False

# Create a global configuration instance
config = Config()

# Function to get the global configuration instance
def get_config() -> Config:
    """
    Get the global configuration instance.

    Returns:
        Config: The global configuration instance.
    """
    return config

# Define constants for backward compatibility
STOCK_DATA_DIR = 'data/stocks'
MODELS_DIR = 'saved_models'
DEFAULT_PREDICTION_HORIZONS = [5, 15, 30, 60]
DEFAULT_SEQUENCE_LENGTH = 60

# Technical indicators for UI
TECHNICAL_INDICATORS = [
    "SMA5", "SMA20", "SMA50",
    "EMA5", "EMA20", "EMA50",
    "RSI", "MACD", "MACD_Signal", "MACD_Diff",
    "BB_Mid", "BB_High", "BB_Low", "BB_Width",
    "ATR"
]

# Theme colors for UI
THEME_COLOR = "#1f77b4"  # Primary blue
SECONDARY_COLOR = "#ff7f0e"  # Orange
DANGER_COLOR = "#d62728"  # Red
SUCCESS_COLOR = "#2ca02c"  # Green

# Model display names for UI
MODEL_DISPLAY_NAMES = {
    'rf': 'Random Forest',
    'gb': 'Gradient Boosting',
    'xgb': 'XGBoost',
    'lr': 'Linear Regression',
    'svr': 'Support Vector Regression',
    'lstm': 'LSTM Neural Network',
    'bilstm': 'Bidirectional LSTM',
    'transformer': 'Transformer',
    'hybrid': 'ARIMA + ML Hybrid',
    'ensemble': 'Ensemble Model',
    'enhanced_ensemble': 'Enhanced Ensemble',
    'prophet': 'Facebook Prophet'
}
