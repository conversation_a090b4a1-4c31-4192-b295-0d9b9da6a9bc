#!/usr/bin/env python3
"""
Quick check script to see what medium-term models (2h, 4h) are missing
This helps understand the scope before starting training
"""

import os
import sys
from typing import Dict, List

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def get_available_stocks() -> List[str]:
    """Get list of available stock CSV files"""
    stock_dir = "data/stocks"
    stocks = []
    
    if os.path.exists(stock_dir):
        for file in os.listdir(stock_dir):
            if file.endswith('.csv') and not file.startswith('.'):
                stock_symbol = file.replace('.csv', '')
                stocks.append(stock_symbol)
    
    return sorted(stocks)

def check_model_files(symbol: str, horizon: int, model_type: str) -> Dict[str, str]:
    """Check what model files exist for a given combination"""
    # Possible file patterns
    patterns = [
        f"saved_models/{symbol}_{model_type}_{horizon}min.joblib",
        f"saved_models/{symbol}_{model_type}_{horizon}min.pkl", 
        f"saved_models/{symbol}_{model_type}_{horizon}min.keras",
        f"saved_models/{symbol}_{model_type}_{horizon}min.h5",
        f"saved_models/{symbol}_{model_type}_{horizon}min_model.joblib",
        f"saved_models/{symbol}_{model_type}_{horizon}min_model.pkl"
    ]
    
    found_files = []
    for pattern in patterns:
        if os.path.exists(pattern):
            found_files.append(pattern)
    
    return found_files

def analyze_medium_term_coverage():
    """Analyze current medium-term model coverage"""
    print("🔍 MEDIUM-TERM MODEL COVERAGE ANALYSIS")
    print("=" * 60)
    
    stocks = get_available_stocks()
    horizons = [120, 240]  # 2h, 4h
    model_types = ['ensemble', 'rf', 'lstm', 'gb', 'lr', 'xgb', 'svr', 'prophet', 'hybrid']
    
    print(f"📊 Checking coverage for:")
    print(f"   • Stocks: {len(stocks)} ({', '.join(stocks)})")
    print(f"   • Horizons: 120min (2h), 240min (4h)")
    print(f"   • Models: {len(model_types)} types")
    print(f"   • Total combinations: {len(stocks) * len(horizons) * len(model_types)}")
    
    # Coverage analysis
    total_possible = len(stocks) * len(horizons) * len(model_types)
    total_existing = 0
    missing_models = []
    
    print(f"\n📈 DETAILED COVERAGE BY STOCK:")
    print("-" * 60)
    
    for stock in stocks:
        print(f"\n{stock}:")
        stock_existing = 0
        
        for horizon in horizons:
            horizon_name = f"{horizon//60}h" if horizon >= 60 else f"{horizon}min"
            print(f"  {horizon_name} ({horizon}min):")
            
            horizon_existing = 0
            for model_type in model_types:
                files = check_model_files(stock, horizon, model_type)
                
                if files:
                    print(f"    ✅ {model_type.upper()}: {len(files)} file(s)")
                    horizon_existing += 1
                    total_existing += 1
                else:
                    print(f"    ❌ {model_type.upper()}: Missing")
                    missing_models.append(f"{stock}_{model_type}_{horizon}min")
            
            coverage_pct = (horizon_existing / len(model_types)) * 100
            print(f"    📊 {horizon_name} Coverage: {horizon_existing}/{len(model_types)} ({coverage_pct:.1f}%)")
            stock_existing += horizon_existing
        
        stock_coverage_pct = (stock_existing / (len(horizons) * len(model_types))) * 100
        print(f"  📊 {stock} Total Coverage: {stock_existing}/{len(horizons) * len(model_types)} ({stock_coverage_pct:.1f}%)")
    
    # Overall summary
    overall_coverage_pct = (total_existing / total_possible) * 100
    
    print(f"\n" + "=" * 60)
    print(f"📊 OVERALL MEDIUM-TERM MODEL SUMMARY")
    print(f"=" * 60)
    print(f"✅ Existing models: {total_existing}/{total_possible} ({overall_coverage_pct:.1f}%)")
    print(f"❌ Missing models: {len(missing_models)}/{total_possible} ({100-overall_coverage_pct:.1f}%)")
    
    # Coverage by horizon
    print(f"\n📈 COVERAGE BY HORIZON:")
    for horizon in horizons:
        horizon_name = f"{horizon//60}h" if horizon >= 60 else f"{horizon}min"
        horizon_existing = 0
        
        for stock in stocks:
            for model_type in model_types:
                files = check_model_files(stock, horizon, model_type)
                if files:
                    horizon_existing += 1
        
        horizon_total = len(stocks) * len(model_types)
        horizon_coverage_pct = (horizon_existing / horizon_total) * 100
        print(f"  {horizon_name} ({horizon}min): {horizon_existing}/{horizon_total} ({horizon_coverage_pct:.1f}%)")
    
    # Coverage by model type
    print(f"\n🤖 COVERAGE BY MODEL TYPE:")
    for model_type in model_types:
        model_existing = 0
        
        for stock in stocks:
            for horizon in horizons:
                files = check_model_files(stock, horizon, model_type)
                if files:
                    model_existing += 1
        
        model_total = len(stocks) * len(horizons)
        model_coverage_pct = (model_existing / model_total) * 100
        print(f"  {model_type.upper()}: {model_existing}/{model_total} ({model_coverage_pct:.1f}%)")
    
    # Training recommendations
    print(f"\n💡 TRAINING RECOMMENDATIONS:")
    print("-" * 60)
    
    if overall_coverage_pct >= 80:
        print("🎉 EXCELLENT: Most medium-term models already exist!")
        print("   • Consider training only missing high-priority models")
    elif overall_coverage_pct >= 50:
        print("✅ GOOD: Partial coverage exists")
        print("   • Focus on completing missing models for key stocks")
    elif overall_coverage_pct >= 20:
        print("⚠️ PARTIAL: Limited coverage")
        print("   • Systematic training recommended for all combinations")
    else:
        print("❌ POOR: Very few models exist")
        print("   • Full training pipeline needed")
    
    # Estimate training time
    models_to_train = len(missing_models)
    if models_to_train > 0:
        # Rough estimates: RF=2min, GB=3min, LSTM=10min, others=5min average
        avg_time_per_model = 5  # minutes
        estimated_time_minutes = models_to_train * avg_time_per_model
        estimated_time_hours = estimated_time_minutes / 60
        
        print(f"\n⏱️ TRAINING TIME ESTIMATE:")
        print(f"   • Models to train: {models_to_train}")
        print(f"   • Estimated time: ~{estimated_time_minutes} minutes ({estimated_time_hours:.1f} hours)")
        print(f"   • Recommendation: Run training in batches or overnight")
    
    return {
        'total_possible': total_possible,
        'total_existing': total_existing,
        'missing_models': missing_models,
        'coverage_percentage': overall_coverage_pct
    }

def main():
    """Main analysis function"""
    try:
        results = analyze_medium_term_coverage()
        
        print(f"\n🚀 NEXT STEPS:")
        print("-" * 30)
        
        if results['coverage_percentage'] < 100:
            print("1. Run: python train_medium_term_models.py")
            print("2. Monitor training progress in logs/medium_term_training.log")
            print("3. Check results with this script again after training")
        else:
            print("✅ All medium-term models are already trained!")
            print("🎉 Your system is ready for 2h and 4h predictions!")
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
